# Advanced vocabulary service configuration
vocabulary_service:
  cache_size: 50000
  
  scheduled_updates:
    enabled: true
    update_interval_hours: 24
    quiet_hours_start: 2  # 2 AM
    quiet_hours_end: 6    # 6 AM
    batch_size: 100
    max_concurrent_lookups: 4
    
  version_management:
    enabled: true
    retention_days: 365
    auto_backup: true
    backup_location: "/data/vocabulary/backups"
    
  conflict_resolution:
    strategy: "confidence_weighted"
    review_threshold: 0.1  # Require review if scores differ by less than 10%
    preferred_vocabularies:
      - SNOMED
      - RxNorm
      - LOINC
    context_weight: 1.5
    
  external_services:
    - name: "NHS_Terminology_Server"
      type: "database"
      connection:
        host: "${NHS_TERM_HOST}"
        port: 5432
        database: "nhs_terminology"
        username: "${NHS_TERM_USER}"
        password: "${NHS_TERM_PASS}"
      priority: 1
      cache_ttl_hours: 168  # 1 week
      
    - name: "UMLS_API"
      type: "rest_api"
      endpoint: "https://uts-ws.nlm.nih.gov/rest"
      api_key: "${UMLS_API_KEY}"
      priority: 2
      rate_limit: 20  # requests per second
      timeout_seconds: 30
      
    - name: "BNF_Database"
      type: "database"
      connection:
        host: "${BNF_HOST}"
        port: 5432
        database: "bnf"
        username: "${BNF_USER}"
        password: "${BNF_PASS}"
      priority: 3
      
  ml_configuration:
    enabled: true
    model_path: "models/medical_term_classifier_v2.pb"
    confidence_threshold: 0.7
    batch_size: 64
    
  auto_learn:
    enabled: true
    min_occurrences: 3  # Minimum times a term must appear
    confidence_threshold: 0.85
    dictionaries:
      - name: "NHS_Dictionary"
        priority: 1
      - name: "SNOMED_UK"
        priority: 2
      - name: "BNF"
        priority: 3
        
  performance:
    parallel_threads: 4
    batch_timeout_seconds: 60
    max_retries: 3
    circuit_breaker:
      enabled: true
      failure_threshold: 5
      timeout_seconds: 60
      
  monitoring:
    metrics_enabled: true
    log_level: "INFO"
    alert_thresholds:
      cache_hit_rate_min: 0.7
      processing_time_max_ms: 1000
      error_rate_max: 0.05 