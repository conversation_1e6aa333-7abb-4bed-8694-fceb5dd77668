# Microservices configuration for OMOP ETL Pipeline

# Run mode: microservices or orchestrator
run_mode: orchestrator

# API Server Configuration
api:
  host: "0.0.0.0"
  port: 8080
  threads: 4
  base_url: "http://localhost:8080"

# Service Endpoints (for microservices mode)
services:
  extract:
    endpoint: "localhost:50051"
    timeout_seconds: 300
  transform:
    endpoint: "localhost:50052"
    timeout_seconds: 300
  load:
    endpoint: "localhost:50053"
    timeout_seconds: 300

# Orchestrator Configuration
orchestrator:
  max_concurrent_jobs: 10
  batch_size: 1000
  service_timeout_seconds: 300
  enable_circuit_breaker: true
  circuit_breaker_threshold: 5
  enable_retry: true
  max_retries: 3
  retry_delay_seconds: 5

# ETL Pipeline Configuration
etl_settings:
  batch_size: 1000
  parallel_workers: 4
  validation_mode: strict
  error_threshold: 0.01
  enable_checkpointing: true
  checkpoint_interval_records: 10000 