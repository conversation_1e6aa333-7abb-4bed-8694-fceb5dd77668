# CMake Toolchain Files for OMOP ETL Pipeline

This directory contains CMake toolchain files for different build environments, particularly focused on Docker builds.

## Available Toolchain Files

### 1. `docker-toolchain.cmake`
Standard Docker toolchain for x86_64 builds using GCC 13.

**Features:**
- Uses GCC 13 compilers (`/usr/bin/gcc-13`, `/usr/bin/g++-13`)
- C++20 standard support
- Optimized for Ubuntu 22.04 Docker environment
- Disables clang-tidy (not available in container)
- Sets up PostgreSQL and other dependency paths

**Usage:**
```bash
cmake --preset docker-release
cmake --build --preset docker-release
```

### 2. `docker-multiarch-toolchain.cmake`
Multi-architecture Docker toolchain supporting x86_64, aarch64, and armhf.

**Features:**
- Automatic architecture detection from Docker build arguments
- Supports `TARGETARCH` and `TARGETPLATFORM` environment variables
- Architecture-specific optimizations
- Same GCC 13 toolchain with cross-compilation support

**Supported Architectures:**
- `amd64` / `x86_64` - Intel/AMD 64-bit
- `arm64` / `aarch64` - ARM 64-bit
- `arm` / `armhf` / `arm/v7` - ARM 32-bit

**Usage:**
```bash
# For multi-architecture builds
cmake --preset docker-multiarch-release
cmake --build --preset docker-multiarch-release
```

## CMake Presets

The following presets are available in `CMakePresets.json`:

### Standard Docker Presets
- `docker-release` - Release build with tests
- `docker-debug` - Debug build with coverage and tests
- `docker-simple` - Simple release build without tests

### Multi-Architecture Docker Presets
- `docker-multiarch-release` - Multi-arch release build with tests
- `docker-multiarch-debug` - Multi-arch debug build with coverage and tests

## Docker Build Integration

### Standard Docker Build
```dockerfile
# Use the standard docker toolchain
RUN cmake --preset docker-release && \
    cmake --build --preset docker-release --config Release
```

### Multi-Architecture Docker Build
```dockerfile
# Use the multi-architecture toolchain
RUN cmake --preset docker-multiarch-release && \
    cmake --build --preset docker-multiarch-release --config Release
```

## Environment Variables

The multi-architecture toolchain automatically detects the target architecture from these environment variables:

- `TARGETARCH` - Docker buildx architecture (e.g., "amd64", "arm64")
- `TARGETPLATFORM` - Docker buildx platform (e.g., "linux/amd64", "linux/arm64")

## Benefits of Using Toolchain Files

1. **Consistent Build Environment**: Ensures all Docker builds use the same compiler and flags
2. **Cross-Compilation Support**: Enables building for different architectures
3. **Simplified Configuration**: Removes the need for manual compiler path specification
4. **IDE Integration**: Better support for IDEs and development tools
5. **Reproducible Builds**: Consistent builds across different environments

## Troubleshooting

### Compiler Not Found
If you encounter compiler not found errors, ensure that:
1. The Docker image has GCC 13 installed
2. The toolchain file path is correct in CMakePresets.json
3. The build environment matches the toolchain expectations

### Architecture Detection Issues
For multi-architecture builds:
1. Verify that `TARGETARCH` or `TARGETPLATFORM` is set correctly
2. Check that the architecture is supported in the toolchain file
3. Ensure the Docker buildx platform is specified correctly

### Performance Issues
The toolchain includes architecture-specific optimizations:
- `-march=native` for x86_64 and aarch64
- `-march=armv7-a -mfpu=neon` for armhf

These can be modified in the toolchain files if needed. 