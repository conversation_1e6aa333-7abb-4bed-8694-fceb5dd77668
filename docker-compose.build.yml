# Docker Compose Build Environment for OMOP ETL Pipeline
# This file provides different build configurations for all targets

version: '3.8'

services:
  # Debug Build
  omop-build-debug:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-build-debug
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - debug_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=ON
      - ENABLE_SANITIZERS=ON
    command: >
      bash -c "
        echo 'Building Debug configuration...' &&
        cmake --preset docker-debug &&
        cmake --build --preset docker-debug --target all-components &&
        echo 'Debug build completed successfully'
      "
    networks:
      - omop-build-network
    profiles:
      - debug

  # Release Build
  omop-build-release:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
        BUILD_TYPE: release
        USE_MULTIARCH: "true"
    container_name: omop-build-release
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - release_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=OFF
      - BUILD_INTEGRATION_TESTS=OFF
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Building Release configuration...' &&
        cmake --preset docker-multiarch-release &&
        cmake --build --preset docker-multiarch-release --target all-components &&
        echo 'Release build completed successfully'
      "
    networks:
      - omop-build-network
    profiles:
      - release

  # RelWithDebInfo Build
  omop-build-relwithdebinfo:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-build-relwithdebinfo
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - relwithdebinfo_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=RelWithDebInfo
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Building RelWithDebInfo configuration...' &&
        cmake -B build/docker-relwithdebinfo -S . \
          -DCMAKE_BUILD_TYPE=RelWithDebInfo \
          -DBUILD_TESTS=ON \
          -DBUILD_INTEGRATION_TESTS=ON \
          -DBUILD_APPLICATIONS=ON \
          -DENABLE_COVERAGE=OFF \
          -DENABLE_SANITIZERS=OFF &&
        cmake --build build/docker-relwithdebinfo --target all-components &&
        echo 'RelWithDebInfo build completed successfully'
      "
    networks:
      - omop-build-network
    profiles:
      - relwithdebinfo

  # Libraries Only Build
  omop-build-libraries:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-build-libraries
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - libraries_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=OFF
      - BUILD_INTEGRATION_TESTS=OFF
      - BUILD_APPLICATIONS=OFF
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Building libraries only...' &&
        cmake --preset docker-release &&
        cmake --build --preset docker-release --target libraries &&
        echo 'Libraries build completed successfully'
      "
    networks:
      - omop-build-network
    profiles:
      - libraries

  # Applications Only Build
  omop-build-applications:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-build-applications
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - applications_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=OFF
      - BUILD_INTEGRATION_TESTS=OFF
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Building applications...' &&
        cmake --preset docker-release &&
        cmake --build --preset docker-release --target libraries &&
        cmake --build --preset docker-release --target applications &&
        echo 'Applications build completed successfully'
      "
    networks:
      - omop-build-network
    profiles:
      - applications

  # Multi-Architecture Build
  omop-build-multiarch:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
        BUILD_TYPE: release
        USE_MULTIARCH: "true"
    container_name: omop-build-multiarch
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - multiarch_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=OFF
      - BUILD_INTEGRATION_TESTS=OFF
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Building multi-architecture release...' &&
        cmake --preset docker-multiarch-release &&
        cmake --build --preset docker-multiarch-release --target all-components &&
        echo 'Multi-architecture build completed successfully'
      "
    networks:
      - omop-build-network
    profiles:
      - multiarch

  # Static Analysis Build
  omop-build-analysis:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-build-analysis
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - analysis_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=ON
    command: >
      bash -c "
        echo 'Building with static analysis...' &&
        cmake --preset docker-debug &&
        echo 'Running clang-tidy...' &&
        if command -v clang-tidy &> /dev/null; then
          find src -name '*.cpp' -o -name '*.h' | xargs clang-tidy -p build/docker-debug || true
        fi &&
        echo 'Running cppcheck...' &&
        if command -v cppcheck &> /dev/null; then
          cppcheck --enable=all --std=c++20 --suppress=missingInclude src/ || true
        fi &&
        cmake --build --preset docker-debug --target all-components &&
        echo 'Static analysis build completed'
      "
    networks:
      - omop-build-network
    profiles:
      - analysis

  # Documentation Build
  omop-build-docs:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-build-docs
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - docs_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=OFF
      - BUILD_INTEGRATION_TESTS=OFF
      - BUILD_APPLICATIONS=OFF
      - BUILD_DOCS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Installing documentation tools...' &&
        sudo apt-get update &&
        sudo apt-get install -y doxygen graphviz &&
        echo 'Building documentation...' &&
        cmake -B build/docker-docs -S . \
          -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_DOCS=ON \
          -DBUILD_TESTS=OFF \
          -DBUILD_INTEGRATION_TESTS=OFF \
          -DBUILD_APPLICATIONS=OFF &&
        cmake --build build/docker-docs --target docs &&
        echo 'Documentation build completed'
      "
    networks:
      - omop-build-network
    profiles:
      - docs

  # Packaging Build
  omop-build-package:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
        BUILD_TYPE: release
        USE_MULTIARCH: "true"
    container_name: omop-build-package
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - package_build_cache:/workspace/build
      - build_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=OFF
      - BUILD_INTEGRATION_TESTS=OFF
      - BUILD_APPLICATIONS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    command: >
      bash -c "
        echo 'Building for packaging...' &&
        cmake --preset docker-multiarch-release &&
        cmake --build --preset docker-multiarch-release --target all-components &&
        echo 'Creating packages...' &&
        cd build/docker-multiarch-release &&
        cpack -G TGZ &&
        cpack -G DEB &&
        ls -la *.tar.gz *.deb 2>/dev/null || echo 'No packages created' &&
        echo 'Packaging completed'
      "
    networks:
      - omop-build-network
    profiles:
      - package

volumes:
  debug_build_cache:
    driver: local
  release_build_cache:
    driver: local
  relwithdebinfo_build_cache:
    driver: local
  libraries_build_cache:
    driver: local
  applications_build_cache:
    driver: local
  multiarch_build_cache:
    driver: local
  analysis_build_cache:
    driver: local
  docs_build_cache:
    driver: local
  package_build_cache:
    driver: local
  build_conan_cache:
    driver: local

networks:
  omop-build-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16