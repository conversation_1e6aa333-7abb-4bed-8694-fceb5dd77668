# OMOP CDM ETL Pipeline

![C++](https://img.shields.io/badge/C++-20-blue.svg)
![CMake](https://img.shields.io/badge/CMake-3.20+-green.svg)
![License](https://img.shields.io/badge/license-MIT-blue.svg)

A robust, high-performance C++20 ETL (Extract, Transform, Load) pipeline for converting healthcare data to the OMOP CDM (Observational Medical Outcomes Partnership Common Data Model) format. Built with modern C++20, this framework provides comprehensive support for healthcare data transformation with multiple data sources and full OMOP table coverage.

## Features

### Core Capabilities
- 🚀 **High Performance**: Multithreaded pipeline architecture for efficient data processing
- 🛠️ **Extensible**: Plugin-based architecture for custom extractors, transformers, and loaders
- 📊 **Data Validation**: Robust validation system for ensuring data quality
- 🔄 **Incremental Processing**: Support for checkpointing and resumable jobs
- 📝 **Comprehensive Logging**: Detailed logging with configurable levels
- 🔧 **Configurable**: YAML-based configuration for flexible deployment
- 📈 **Monitoring**: Real-time statistics and progress tracking
- 🧪 **Testable**: Designed for unit and integration testing
- 📚 **Well-Documented**: Doxygen documentation and usage examples

### Supported Data Sources
- PostgreSQL
- MySQL
- CSV
- JSON

### Supported OMOP CDM Tables
- Person
- Observation Period
- Visit Occurrence
- Condition Occurrence
- Drug Exposure
- Procedure Occurrence
- Measurement
- Observation
- Death
- Note

## Requirements

### Build Requirements
- C++20 compliant compiler (GCC 10+, Clang 12+, or MSVC 2019+)
- CMake 3.23 or higher
- PostgreSQL development libraries (libpq)
- ODBC development libraries
- yaml-cpp library
- nlohmann_json library
- spdlog library (for logging)
- cpp-httplib (for REST API)
- Google Test (for testing)

### Runtime Requirements
- PostgreSQL 12+ or MySQL 8+ database
- Sufficient memory for batch processing (minimum 4GB recommended)
- Linux, macOS, or Windows operating system

### Docker Requirements (Recommended Development Environment)
- **Memory**: 8GB allocated to Docker (4GB minimum reserved)
- **CPU**: 4 cores allocated (2 cores minimum reserved)
- **Disk**: 10GB free space for build artifacts and dependencies
- **Platform**: Supports Intel, AMD, and ARM architectures

#### Test Resource Usage
The comprehensive test suite requires significant resources during compilation:
- **Memory-intensive tests**: `test_interfaces.cpp` and `test_pipeline.cpp` require substantial memory
- **Recommended build approach**: Single-threaded compilation (`-j 1`) to prevent memory exhaustion
- **Coverage disabled**: Use `-DCODE_COVERAGE=OFF` to reduce memory usage during builds
- **Debug builds preferred**: Use debug configuration for testing to reduce optimization memory overhead

## Installation

### Using the Unified Build System (Recommended)

The OMOP ETL Pipeline now features a **unified build system** that consolidates all Docker builds and development workflows into a single, parameterized approach. This system provides consistent, cross-platform development and deployment with environment-specific configurations.

#### Quick Start with Unified Build System
```bash
# Clone the repository
git clone https://github.com/UCL-Cancer-Data-Engineering/omop-etl.git
cd omop-etl

# 1. Detect your system architecture
./scripts/build.sh detect-arch

# 2. Start development environment (auto-configures for your platform)
./scripts/build.sh dev -e dev

# 3. Build and test all components
./scripts/build.sh build -e dev --tests

# 4. Run specific tests
./scripts/build.sh test -t extract --filter "ConnectionPool*"
```

**📚 For detailed instructions, see:**
- **[Unified Build Documentation](scripts/README.md)** - Comprehensive build system documentation
- **[Template Configuration Guide](scripts/configs/README.md)** - Environment-specific configuration management

#### Development Workflow
```bash
# Start development environment with auto-configuration
./scripts/build.sh dev -e dev

# Enter interactive development shell
./scripts/build.sh shell

# Build specific components
./scripts/build.sh build -t api --tests

# Run tests with filters
./scripts/build.sh test -t extract --filter "ConnectionPool*"

# Format and lint code
./scripts/build.sh format
./scripts/build.sh lint

# Build documentation
./scripts/build.sh docs

# Stop development environment
./scripts/build.sh down
```

#### Production Deployment
```bash
# Generate production configuration
./scripts/build.sh config -e prod --validate

# Build production images
./scripts/build.sh build -e prod --clean

# Deploy production services
./scripts/build.sh up -e prod --profiles "api,postgres"
```

#### Multi-Environment Support
```bash
# Development environment
./scripts/build.sh dev -e dev

# Staging environment  
./scripts/build.sh build -e staging --tests

# Production environment
./scripts/build.sh build -e prod --clean
```

## Quick Start

### Command Line Interface

```bash
# Run ETL job with configuration file using unified build system
./scripts/build.sh shell
# Inside the shell:
omop-etl run --config config/etl/postgres_mappings.yaml

# Or run directly with environment configuration
./scripts/build.sh build -e dev -t cli
docker-compose -f scripts/docker-compose.yml exec omop-etl-cli \
  omop-etl validate --config config/etl/csv_mappings.yaml

# List available transformations
docker-compose -f scripts/docker-compose.yml exec omop-etl-cli \
  omop-etl list-transforms
```

### API Usage

```bash
# Start API server
omop-etl-api --port 8080 --config config/api/config.yaml

# Create new ETL job
curl -X POST http://localhost:8080/api/v1/etl/jobs \
  -H "Content-Type: application/json" \
  -d '{"config": "postgres_mappings.yaml", "source": "production"}'

# Check job status
curl http://localhost:8080/api/v1/etl/jobs/{job_id}
```

## Configuration

### Basic Mapping Configuration

```yaml
# config/etl/postgres_mappings.yaml
source:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: clinical_db
    username: ${DB_USER}
    password: ${DB_PASSWORD}

target:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: omop_cdm
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}

mappings:
  person:
    source_table: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507  # Male
          "F": 8532  # Female
```

### Advanced Transformation Example

```yaml
drug_exposure:
  source_table: medications
  target_table: drug_exposure
  transformations:
    - source_column: medication_id
      target_column: drug_exposure_id
      type: direct

    - source_columns: [drug_name, drug_strength]
      target_column: drug_concept_id
      type: custom_transform
      function: lookup_drug_concept

    - source_columns: [start_date, duration_days]
      target_column: drug_exposure_end_datetime
      type: date_calculation
      calculation: "start_date + duration_days"

  validation_rules:
    - field: drug_concept_id
      type: not_null

    - field: drug_exposure_start_datetime
      type: before
      compare_to: drug_exposure_end_datetime
```

## Directory Structure

```
omop-etl/
├── CMakeLists.txt                  # Root CMake configuration
├── CMakePresets.json              # CMake presets for different build configurations
├── README.md                      # Project documentation
├── LICENSE                        # Project license file
├── .gitignore                     # Git ignore file
├── .clang-tidy                    # Clang-tidy configuration
├── .vscode/                       # VSCode configuration
├── cmake/                         # CMake modules and utilities
├── config/                        # Configuration files
├── src/                           # Source code root
│   ├── CMakeLists.txt            # Source directory CMake configuration
│   ├── app/                      # Application code
│   └── lib/                      # Library code
│       ├── CMakeLists.txt
│       ├── cdm/                  # OHDSI CDM data handling
│       ├── common/               # Common utilities and components
│       ├── core/                 # Core pipeline components
│       ├── extract/              # Data extraction components
│       ├── transform/            # Data transformation logic
│       ├── load/                 # Data loading components
│       └── service/              # Service layer functionality
├── tests/                        # Unit and integration tests
├── docs/                         # Documentation
├── examples/                     # Example configurations and usage
├── scripts/                      # Build and deployment scripts
│   ├── build.sh                  # Unified build system script
│   ├── render_config.py          # Configuration template renderer
│   ├── configs/                  # Environment-specific configurations
│   └── docker-compose.yml        # Docker Compose configuration
├── Dockerfile                    # Unified parameterized Docker configuration
└── .dockerignore                 # Docker ignore file
```

### Directory Descriptions

#### Root Directory
- `CMakeLists.txt`: Main CMake configuration file that sets up the project build system
- `CMakePresets.json`: Predefined CMake configurations for different build scenarios
- `README.md`: Project documentation and getting started guide
- `LICENSE`: Project license information
- `.gitignore`: Specifies files and directories to be ignored by Git
- `.clang-tidy`: Configuration for the Clang-tidy static analysis tool

#### Source Code (`src/`)
The source code is organized into two main directories:

##### Application Code (`src/app/`)
Contains the main application entry points and user-facing components.

##### Library Code (`src/lib/`)
Core library components organized into specialized modules:

- `cdm/`: OHDSI Common Data Model (CDM) related code for handling standardized healthcare data
- `common/`: Shared utilities, logging, configuration, and exception handling
- `core/`: Core pipeline components and interfaces
- `extract/`: Data extraction components for different source formats
- `transform/`: Data transformation logic and mapping rules
- `load/`: Data loading components for target systems
- `service/`: Service layer functionality for ETL operations

#### Configuration (`config/`)
Contains configuration files for different aspects of the ETL process.

#### Documentation (`docs/`)
Project documentation including:
- API documentation
- Design documents
- User guides
- Configuration guides

#### Tests (`tests/`)
Contains unit and integration tests for the project components.

#### Examples (`examples/`)
Example configurations and usage scenarios to help users get started.

#### Scripts (`scripts/`)
Build and deployment scripts for various environments.

#### Docker Files
- `Dockerfile`: Unified parameterized container configuration supporting all build targets and architectures
- `.dockerignore`: Specifies files to exclude from Docker builds

### Build System
The project uses CMake as its build system, with configurations defined in:
- Root `CMakeLists.txt`
- `CMakePresets.json` for different build configurations
- Individual `CMakeLists.txt` files in each source directory

### CMake Toolchain Support
The project includes specialized CMake toolchain files for Docker builds:

#### Available Toolchains
- **`cmake/docker-toolchain.cmake`**: Standard Docker toolchain for x86_64 builds using GCC 13
- **`cmake/docker-multiarch-toolchain.cmake`**: Multi-architecture toolchain supporting x86_64, aarch64, and armhf

#### CMake Presets with Toolchain Support
- **Standard Docker**: `docker-release`, `docker-debug`, `docker-simple`
- **Multi-Architecture**: `docker-multiarch-release`, `docker-multiarch-debug`

#### Usage Examples
```bash
# Standard Docker build
cmake --preset docker-release
cmake --build --preset docker-release

# Multi-architecture build
cmake --preset docker-multiarch-release
cmake --build --preset docker-multiarch-release

# Using the build script
./scripts/docker-build-with-toolchain.sh --multiarch --arch arm64 --type release
```

**📖 See [cmake/README.md](cmake/README.md) for detailed toolchain documentation.**

### Development Environment
- VSCode configuration in `.vscode/`
- Development container support through Docker
- Clang-tidy integration for code quality

## API Documentation

### Endpoints

#### ETL Job Management
- `POST /api/v1/etl/jobs` - Create new ETL job
- `GET /api/v1/etl/jobs` - List all jobs
- `GET /api/v1/etl/jobs/{job_id}` - Get job details
- `DELETE /api/v1/etl/jobs/{job_id}` - Cancel job
- `GET /api/v1/etl/jobs/{job_id}/logs` - Get job logs

#### Configuration Management
- `GET /api/v1/etl/config` - List configurations
- `POST /api/v1/etl/config` - Upload configuration
- `POST /api/v1/etl/config/validate` - Validate configuration

#### System Management
- `GET /api/v1/health` - Health check
- `GET /api/v1/etl/vocabulary` - List available vocabularies
- `GET /api/v1/etl/transforms` - List available transformations

### Authentication

The API supports JWT-based authentication:

```bash
# Obtain token
curl -X POST http://localhost:8080/api/v1/auth/token \
  -d '{"username": "admin", "password": "password"}'

# Use token in requests
curl -H "Authorization: Bearer {token}" \
  http://localhost:8080/api/v1/etl/jobs
```

## Architecture

The pipeline follows a modular, layered architecture:

```
┌─────────────────────────────────────────────────┐
│                  Applications                    │
│         (CLI Application / REST API)             │
├─────────────────────────────────────────────────┤
│                Service Layer                     │
│        (ETL Service / Job Service)               │
├─────────────────────────────────────────────────┤
│                 Core Pipeline                    │
│     (Pipeline / Job Manager / Interfaces)        │
├─────────────────────────────────────────────────┤
│  Extract    │    Transform    │      Load       │
│ Components  │   Components    │   Components    │
├─────────────────────────────────────────────────┤
│              Common Components                   │
│   (Configuration / Logging / Validation)         │
└─────────────────────────────────────────────────┘
```

### Core Components

- **Extractors**: Read data from various sources (CSV, JSON, databases)
- **Transformers**: Convert source data to OMOP CDM format, apply validation rules
- **Loaders**: Write transformed data to target systems
- **Pipeline**: Orchestrates the ETL process with proper error handling and checkpointing

### Supporting Components

- **Job Manager**: Manages ETL job execution and scheduling
- **Configuration**: YAML-based configuration system
- **Logging**: Comprehensive logging framework
- **Utilities**: Common utilities for string manipulation, date handling, etc.
- **Validation**: Data validation framework

## Development

### Recommended Unified Build System Development

For consistent builds across all platforms, use the unified build system:

```bash
# Interactive development workflow
./scripts/build.sh dev -e dev
./scripts/build.sh shell

# Inside the shell:
cmake --preset docker-debug -DCODE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
cd build/docker-debug && ctest --output-on-failure --verbose
```

**📖 See [Unified Build Documentation](scripts/README.md) for complete instructions including:**
- Step-by-step build process for all environments and targets
- Memory optimization techniques
- Troubleshooting common issues
- Cross-platform build instructions
- CI/CD integration examples
- Environment-specific configuration management

### Alternative: Building from Source (Not Recommended)

**Note**: Direct host builds are not recommended due to complex dependency management and platform-specific issues. Use the unified build system instead for consistent results.

If you must build directly on the host system:

```bash
# Ensure all dependencies are installed (see Requirements section)
# This approach requires manual dependency management

# Debug build with tests
cmake -B build -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON
cmake --build build -j 1  # Use single-threaded build to prevent memory issues

# Run specific test suite
./build/tests/unit/core/test_interfaces
./build/tests/unit/core/test_record
./build/tests/unit/core/test_pipeline
```

**Recommended**: Use the unified build system approach above instead of host builds.

### Test Execution Notes

- **Memory-intensive tests**: Some test files require significant memory during compilation
- **Single-threaded builds**: Use `-j 1` to prevent compiler memory exhaustion
- **Individual test execution**: Run tests individually if experiencing timeout issues:
  ```bash
  # Run individual test executables using unified build system
  ./scripts/build.sh test -t core --filter "test_interfaces"

  ./scripts/build.sh test -t core --filter "test_record"

  ./scripts/build.sh test -t core --filter "test_pipeline"

  # Or run all tests with specific filters
  ./scripts/build.sh test -t core --filter "test_interfaces|test_record"
  ```

### Code Style

The project follows the Google C++ Style Guide with the following modifications:
- 4-space indentation
- 120-character line limit
- Doxygen-style documentation comments

### Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Performance Considerations

### Batch Processing
- Default batch size: 10,000 records
- Configurable via `batch_size` parameter
- Automatic memory management for large datasets

### Parallel Processing
- Multi-threaded extraction and transformation
- Thread pool size: CPU cores - 1 (configurable)
- Lock-free data structures for high throughput

### Database Optimization
- Prepared statements for repeated queries
- Connection pooling with configurable pool size
- Bulk insert operations for PostgreSQL and MySQL

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```
   Error: Failed to connect to database
   Solution: Check connection parameters and network accessibility
   ```

2. **Vocabulary Mapping Failures**
   ```
   Error: Unknown vocabulary concept
   Solution: Ensure vocabulary tables are populated in OMOP database
   ```

3. **Memory Issues**
   ```
   Error: Out of memory during batch processing
   Solution: Reduce batch_size in configuration
   ```

### Debug Mode

Enable detailed logging using unified build system:
```bash
./scripts/build.sh shell
# Inside the shell:
omop-etl run --config mappings.yaml --log-level debug
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- OHDSI Community for the OMOP CDM specifications
- Modern C++ community for design patterns and best practices
- All contributors to the project

## Support

- Documentation: [https://omop-etl.github.io](https://omop-etl.github.io)
- Issues: [GitHub Issues](https://github.com/UCL-Cancer-Data-Engineering/omop-etl/issues)
