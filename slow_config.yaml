# Slow configuration for testing long-running jobs
pipeline:
  batch_size: 100
  max_parallel_batches: 1
  error_threshold: 0.01
  stop_on_error: false
  enable_checkpointing: true
  checkpoint_dir: "/tmp/omop-etl/checkpoints"
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "csv"
        file_path: "test_data/csv/patients.csv"
        delimiter: ","
        has_header: true
        processing_delay_ms: 3000
        per_record_delay_ms: 50
    - name: "transform"
      type: "transformer"
      config:
        type: "identity"
        validate_records: true
    - name: "load"
      type: "loader"
      config:
        type: "database"
        connection_string: "postgresql://test_user:test_pass@localhost:5432/test_db"
        batch_size: 100 