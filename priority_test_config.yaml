pipeline:
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "csv"
        file_path: "tests/integration/test_data/csv/patients.csv"
        encoding: "utf-8"
        delimiter: ","
        has_header: true
        batch_size: 10
        processing_delay_ms: 2000  # 2 second delay per batch
    
    - name: "transform"
      type: "transformer"
      config:
        type: "identity"
        validation_rules:
          - field: "patient_id"
            required: true
          - field: "age"
            type: "integer"
            range: [0, 150]
        cleaning_rules:
          - field: "name"
            trim: true
            lowercase: false
    
    - name: "load"
      type: "loader"
      config:
        type: "database"
        target_type: "postgresql"
        host: "localhost"
        port: 5432
        database: "test_db"
        schema: "public"
        table: "test_patients"
        batch_size: 10
        retry_attempts: 3
        retry_delay_ms: 1000

monitoring:
  metrics:
    enabled: true
    collection_interval_ms: 1000
  logging:
    level: "info"
    format: "json" 