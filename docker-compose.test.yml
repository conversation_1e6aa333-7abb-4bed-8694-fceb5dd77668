# Docker Compose Test Environment for OMOP ETL Pipeline
# This file provides environments for unit and integration testing
# NOTE: To avoid permissions issues, set your UID and GID before running:
#   export UID=$(id -u) && export GID=$(id -g)

version: '3.8'

services:
  # Unit Test Runner
  omop-unit-tests:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-unit-tests
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - unit_test_cache:/workspace/build
      - test_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    user: "${UID}:${GID}"
    environment:
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=OFF
      - ENABLE_COVERAGE=ON
      - ENABLE_SANITIZERS=ON
      - GTEST_COLOR=1
      - GTEST_OUTPUT=xml:/workspace/build/test-results/
    command: >
      bash -c "
        echo 'Building unit tests...' &&
        cmake --preset docker-debug &&
        cmake --build --preset docker-debug --target libraries &&
        cmake --build --preset docker-debug --target test_unit_tests &&
        echo 'Running unit tests...' &&
        cd build/docker-debug &&
        ctest -L unit --output-on-failure --verbose
      "
    networks:
      - omop-test-network
    profiles:
      - unit-tests

  # Integration Test Runner
  omop-integration-tests:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-integration-tests
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - integration_test_cache:/workspace/build
      - test_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    user: "${UID}:${GID}"
    environment:
      - POSTGRES_HOST=postgres-test
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db-test
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - ENABLE_COVERAGE=ON
      - GTEST_COLOR=1
      - GTEST_OUTPUT=xml:/workspace/build/test-results/
    depends_on:
      postgres-test:
        condition: service_healthy
      omop-db-test:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Building integration tests...' &&
        cmake --preset docker-debug &&
        cmake --build --preset docker-debug --target libraries &&
        cmake --build --preset docker-debug --target test_integration_tests &&
        echo 'Running integration tests...' &&
        cd build/docker-debug &&
        ctest -L integration --output-on-failure --verbose
      "
    networks:
      - omop-test-network
    profiles:
      - integration-tests

  # All Tests Runner
  omop-all-tests:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-all-tests
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - all_test_cache:/workspace/build
      - test_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    user: "${UID}:${GID}"
    environment:
      - POSTGRES_HOST=postgres-test
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db-test
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - ENABLE_COVERAGE=ON
      - ENABLE_SANITIZERS=ON
      - GTEST_COLOR=1
      - GTEST_OUTPUT=xml:/workspace/build/test-results/
    depends_on:
      postgres-test:
        condition: service_healthy
      omop-db-test:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Building all tests...' &&
        cmake --preset docker-debug &&
        cmake --build --preset docker-debug --target libraries &&
        cmake --build --preset docker-debug --target test_all_tests &&
        echo 'Running all tests...' &&
        cd build/docker-debug &&
        ctest --output-on-failure --verbose
      "
    networks:
      - omop-test-network
    profiles:
      - all-tests

  # Test Database Services
  postgres-test:
    image: postgres:15-alpine
    container_name: omop-postgres-test
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
    volumes:
      - clinical-data:/var/lib/postgresql/data
      - ./scripts/init-source-clinical-postgres.sql:/docker-entrypoint-initdb.d/init.sql
      - ./tests/integration/test_data/sql:/docker-entrypoint-initdb.d/test_data:ro
    ports:
      - "5435:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U clinical_user -d clinical_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-test-network

  omop-db-test:
    image: postgres:15-alpine
    container_name: omop-cdm-test
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=omop_cdm
      - POSTGRES_USER=omop_user
      - POSTGRES_PASSWORD=omop_pass
    volumes:
      - omop_test_data:/var/lib/postgresql/data
      - ./scripts/init-target-omop-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5436:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omop_user -d omop_cdm"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-test-network

  # Performance Test Runner
  omop-performance-tests:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-performance-tests
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - perf_test_cache:/workspace/build
      - test_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - POSTGRES_HOST=postgres-test
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db-test
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - CMAKE_BUILD_TYPE=Release
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - ENABLE_COVERAGE=OFF
      - ENABLE_SANITIZERS=OFF
    depends_on:
      postgres-test:
        condition: service_healthy
      omop-db-test:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Building performance tests...' &&
        cmake --preset docker-release &&
        cmake --build --preset docker-release --target libraries &&
        echo 'Running performance tests...' &&
        cd build/docker-release &&
        ctest -L performance --output-on-failure --verbose
      "
    networks:
      - omop-test-network
    profiles:
      - performance-tests
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 16G
        reservations:
          cpus: '4.0'
          memory: 8G

  # Coverage Report Generator
  omop-coverage:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-coverage
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - coverage_cache:/workspace/build
      - test_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    environment:
      - POSTGRES_HOST=postgres-test
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db-test
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - ENABLE_COVERAGE=ON
      - ENABLE_SANITIZERS=OFF
    depends_on:
      postgres-test:
        condition: service_healthy
      omop-db-test:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Building with coverage...' &&
        cmake --preset docker-debug &&
        cmake --build --preset docker-debug --target libraries &&
        cmake --build --preset docker-debug --target test_all_tests &&
        echo 'Running tests with coverage...' &&
        cd build/docker-debug &&
        ctest --output-on-failure &&
        echo 'Generating coverage report...' &&
        if command -v gcovr &> /dev/null; then
          gcovr -r /workspace --html --html-details -o coverage.html /workspace/build/docker-debug/
          echo 'Coverage report generated: coverage.html'
        else
          echo 'gcovr not available, skipping coverage report'
        fi
      "
    networks:
      - omop-test-network
    profiles:
      - coverage

volumes:
  clinical-data:
    driver: local
  omop_test_data:
    driver: local
  unit_test_cache:
    driver: local
  integration_test_cache:
    driver: local
  all_test_cache:
    driver: local
  perf_test_cache:
    driver: local
  coverage_cache:
    driver: local
  test_conan_cache:
    driver: local

networks:
  omop-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16