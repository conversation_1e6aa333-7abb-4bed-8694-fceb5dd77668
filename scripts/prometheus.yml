global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # OMOP ETL API
  - job_name: 'omop-etl-api'
    static_configs:
      - targets: ['omop-etl-api:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL databases
  - job_name: 'postgres-clinical'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'postgres-omop'
    static_configs:
      - targets: ['omop-db:5432']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['omop-redis:6379']
    scrape_interval: 30s

  # Node exporter (if available)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker containers
  - job_name: 'docker'
    static_configs:
      - targets: ['docker.for.mac.localhost:9323']
    scrape_interval: 30s 