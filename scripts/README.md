# Unified Build System for OMOP ETL Pipeline

This document describes the new unified build system that consolidates all Docker builds and development workflows into a single, parameterized approach.

## Overview

The unified build system replaces the previous collection of individual Dockerfiles and build scripts with:

- **Single Dockerfile** (`Dockerfile`) - Parameterized to build any component
- **Unified Build Script** (`scripts/build.sh`) - Consolidates all build, test, and development functionality
- **Parameterized Docker Compose** - Uses build arguments to customize containers

## Key Benefits

- **Reduced Complexity**: One Dockerfile instead of 12+ individual files
- **Consistent Builds**: Same base environment for all components
- **Parameter-Driven**: Customize builds through environment variables and build args
- **Consolidated Scripts**: Single script handles build, test, dev, docs, and more
- **Multi-Architecture**: Built-in support for AMD64, ARM64, and ARM v7

## Quick Start

### 1. Architecture Detection
```bash
./scripts/build.sh detect-arch
```

### 2. Basic API Build
```bash
./scripts/build.sh build
```

### 3. Development Environment
```bash
./scripts/build.sh dev
./scripts/build.sh shell
```

### 4. Build with Tests
```bash
./scripts/build.sh build -t api --tests
```

### 5. Run Tests
```bash
./scripts/build.sh test -t extract --filter "ConnectionPool*"
```

## Build Targets

| Target | Description | Use Case |
|--------|-------------|----------|
| `api` | REST API service with gRPC support | Production API deployment |
| `cli` | Command-line interface application | Batch processing, automation |
| `common` | Common utilities and configuration | Library development |
| `core` | Core pipeline and job management | Pipeline development |
| `cdm` | OMOP CDM table definitions | Schema development |
| `extract` | Data extraction components | Extraction testing |
| `transform` | Data transformation components | Transformation testing |
| `load` | Data loading and batch processing | Loading testing |
| `service` | Service layer components | Microservice development |
| `all` | Build all targets | Complete system build |

## Commands

### Build Commands
```bash
# Build specific target
./scripts/build.sh build -t cli

# Build with specific type
./scripts/build.sh build -t api -b debug

# Build with tests enabled
./scripts/build.sh build -t extract --tests

# Clean build (removes existing containers/images)
./scripts/build.sh build -t api --clean

# Build for specific platform
./scripts/build.sh build -t api --platform linux/arm64

# Build with all features
./scripts/build.sh build -t api --tests --grpc --rest-api
```

### Test Commands
```bash
# Run all tests for target
./scripts/build.sh test -t core

# Run specific tests
./scripts/build.sh test -t extract --filter "ConnectionPool*"

# Verbose test output
./scripts/build.sh test -t api -v

# Test all components
./scripts/build.sh test -t all
```

### Development Commands
```bash
# Start development environment
./scripts/build.sh dev

# Enter development shell
./scripts/build.sh shell

# Format code
./scripts/build.sh format

# Run static analysis
./scripts/build.sh lint

# Build documentation
./scripts/build.sh docs
```

### Service Management
```bash
# Start default services (API + databases)
./scripts/build.sh up

# Start with specific profiles
./scripts/build.sh up --profiles "api,cli,postgres"

# Stop all services
./scripts/build.sh down
```

### Utility Commands
```bash
# Clean all build artifacts
./scripts/build.sh clean

# Detect system architecture
./scripts/build.sh detect-arch
```

## Configuration

### Environment Variables

Create `scripts/build.env` from `scripts/build.env.example`:

```bash
cp scripts/build.env.example scripts/build.env
```

Key configuration options:

```bash
# Build Configuration
BUILD_TARGET=api                # Default build target
BUILD_TYPE=release              # release or debug
ENABLE_TESTS=false              # Enable test building
ENABLE_GRPC=true                # Enable gRPC support
ENABLE_REST_API=true            # Enable REST API support

# Platform Configuration
DOCKER_PLATFORM=linux/amd64     # Target platform
```

### Docker Compose Profiles

The system supports multiple Docker Compose profiles:

- `default` - API service and databases
- `dev` - Development environment
- `cli` - CLI application
- `service` - Service library testing
- `test` - Test-enabled builds

## Architecture Support

The system automatically detects and supports multiple architectures:

- **AMD64/x86_64** - Intel/AMD processors
- **ARM64/aarch64** - Apple Silicon, AWS Graviton
- **ARM v7** - Raspberry Pi, older ARM devices

### Platform-Specific Commands

```bash
# Auto-detect platform
./scripts/build.sh build -t api

# Explicit platform
./scripts/build.sh build -t api --platform linux/arm64

# Apple Silicon optimized
DOCKER_PLATFORM=linux/arm64 ./scripts/build.sh build -t api
```

## Dockerfile Structure

The unified `Dockerfile` uses multi-stage builds:

1. **Base Stage** - Common dependencies and tools
2. **Builder Stage** - Compilation and testing
3. **Development Stage** - Development tools and environment
4. **Runtime Stage** - Minimal production image

### Build Arguments

| Argument | Default | Description |
|----------|---------|-------------|
| `BUILD_TARGET` | `api` | Component to build |
| `BUILD_TYPE` | `release` | Build configuration |
| `ENABLE_TESTS` | `false` | Include test dependencies |
| `ENABLE_DEV_TOOLS` | `false` | Include development tools |
| `ENABLE_GRPC` | `false` | Include gRPC dependencies |
| `ENABLE_REST_API` | `false` | Include REST API dependencies |

## Migration from Old System

### Old vs New Commands

| Old Command | New Command |
|-------------|-------------|
| `scripts/docker-build.sh` | `./scripts/build.sh build` |
| `scripts/dev-test.sh -l extract` | `./scripts/build.sh test -t extract` |
| `scripts/docker-dev.sh up` | `./scripts/build.sh dev` |
| `scripts/rebuild-docs.sh` | `./scripts/build.sh docs` |
| `scripts/detect-architecture.sh` | `./scripts/build.sh detect-arch` |

### File Mapping

| Old Files | New File |
|-----------|----------|
| `Dockerfile.*` (12 files) | `Dockerfile` |
| `scripts/*.sh` (6 scripts) | `scripts/build.sh` |

## Examples

### Complete Development Workflow
```bash
# 1. Detect architecture
./scripts/build.sh detect-arch

# 2. Start development environment
./scripts/build.sh dev

# 3. Enter development shell
./scripts/build.sh shell

# 4. Build and test specific component
./scripts/build.sh test -t extract --verbose

# 5. Format code
./scripts/build.sh format

# 6. Build documentation
./scripts/build.sh docs

# 7. Clean up
./scripts/build.sh down
```

### Production Build and Deploy
```bash
# 1. Clean build with tests
./scripts/build.sh build -t api --clean --tests

# 2. Run comprehensive tests
./scripts/build.sh test -t api

# 3. Build for production platform
./scripts/build.sh build -t api --platform linux/amd64

# 4. Start production services
./scripts/build.sh up --profiles "api,postgres"
```

### Library Development
```bash
# 1. Build specific library with tests
./scripts/build.sh build -t extract --tests --dev-tools

# 2. Run library tests
./scripts/build.sh test -t extract

# 3. Run static analysis
./scripts/build.sh lint

# 4. Test integration
./scripts/build.sh test -t extract --filter "*Integration*"
```

## Troubleshooting

### Common Issues

1. **Architecture Mismatch**
   ```bash
   ./scripts/build.sh detect-arch
   export DOCKER_PLATFORM=linux/arm64  # For Apple Silicon
   ```

2. **Permission Errors**
   ```bash
   chmod +x scripts/build.sh
   ```

3. **Build Cache Issues**
   ```bash
   ./scripts/build.sh clean
   ./scripts/build.sh build -t api --clean
   ```

4. **Memory Issues**
   ```bash
   # Reduce parallel jobs in development container
   docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "export MAKEFLAGS=-j2"
   ```

### Debug Information

```bash
# Enable verbose output
./scripts/build.sh test -t api --verbose

# Check container logs
docker-compose -f scripts/docker-compose.yml logs omop-etl-dev

# Inspect build arguments
docker-compose -f scripts/docker-compose.yml config
```

## Performance Optimization

### Multi-Core Builds
The system automatically detects CPU cores and optimizes parallel builds:

```bash
# Manual core specification in container
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cmake --build build -j8"
```

### Build Cache
Docker build cache is preserved between builds. Clean when needed:

```bash
./scripts/build.sh clean
docker builder prune -f
```

### Resource Limits
Development container includes resource limits for optimal performance:

- **CPU**: 6 cores max, 2 cores reserved
- **Memory**: 12GB max, 6GB reserved  
- **Shared Memory**: 4GB for compilation

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Build OMOP ETL
  run: |
    ./scripts/build.sh build -t api --tests --platform linux/amd64
    ./scripts/build.sh test -t api

- name: Build for multiple architectures
  run: |
    ./scripts/build.sh build -t api --platform linux/amd64
    ./scripts/build.sh build -t api --platform linux/arm64
```

### Jenkins Pipeline Example
```groovy
stage('Build') {
    steps {
        sh './scripts/build.sh build -t api --tests'
    }
}
stage('Test') {
    steps {
        sh './scripts/build.sh test -t api'
    }
}
```

## Support

For issues with the unified build system:

1. Check architecture compatibility: `./scripts/build.sh detect-arch`
2. Review build logs: `docker-compose -f scripts/docker-compose.yml logs`
3. Clean and rebuild: `./scripts/build.sh clean && ./scripts/build.sh build -t api --clean`
4. Verify Docker and Docker Compose versions
5. Check available memory and disk space

## Additional Configuration Files

## Database Infrastructure

This section describes the database initialization scripts for the OMOP ETL pipeline's database infrastructure.

### Script Overview

#### Source Databases (Extraction)

These databases contain source clinical data that will be extracted and transformed into OMOP CDM format.

**`init-source-clinical-postgres.sql`**
- **Purpose**: Initializes PostgreSQL source clinical database
- **Database**: `clinical_db` (PostgreSQL)
- **Schema**: `clinical`
- **Tables**: `patients`, `visits`, `medications`, `conditions`
- **Data**: Generic clinical data for extraction testing
- **Used by**: PostgreSQL extraction tests and clinical data extraction

**`init-source-clinical-mysql.sql`**
- **Purpose**: Initializes MySQL source clinical database
- **Database**: `test_mysql_db` (MySQL)
- **Schema**: `test_extract`, `multi_source_test`
- **Tables**: `patients`, `visits`, `medications`, `source_patients`
- **Data**: UK healthcare data (NHS numbers, British postcodes, UK medications)
- **Used by**: MySQL extraction tests and multi-source extraction testing

#### Target Database (Load)

This database contains the standardized OMOP CDM schema where transformed data is loaded.

**`init-target-omop-postgres.sql`**
- **Purpose**: Initializes PostgreSQL target OMOP CDM database
- **Database**: `omop_cdm` (PostgreSQL)
- **Schema**: `cdm`, `vocab`
- **Tables**: Standard OMOP CDM tables (`person`, `visit_occurrence`, `condition_occurrence`, `drug_exposure`, etc.)
- **Data**: Empty OMOP CDM schema (data loaded via ETL process)
- **Used by**: OMOP CDM data loading and transformation testing

### Database Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    OMOP ETL Pipeline                        │
├─────────────────────────────────────────────────────────────┤
│  EXTRACT PHASE                    │  LOAD PHASE             │
│  ┌─────────────────────────────┐  │  ┌─────────────────────┐ │
│  │ Source Databases            │  │  │ Target Database     │ │
│  │                             │  │  │                     │ │
│  │ ┌─────────────────────────┐ │  │  │ ┌─────────────────┐ │ │
│  │ │ PostgreSQL Clinical     │ │  │  │ │ PostgreSQL      │ │ │
│  │ │ (init-source-clinical-  │ │  │  │ │ OMOP CDM        │ │ │
│  │ │  postgres.sql)          │ │  │  │ │ (init-target-   │ │ │
│  │ └─────────────────────────┘ │  │  │ │  omop-postgres. │ │ │
│  │                             │  │  │ │  sql)           │ │ │
│  │ ┌─────────────────────────┐ │  │  │ └─────────────────┘ │ │
│  │ │ MySQL Clinical          │ │  │  │                     │ │
│  │ │ (init-source-clinical-  │ │  │  │                     │ │
│  │ │  mysql.sql)             │ │  │  │                     │ │
│  │ └─────────────────────────┘ │  │  │                     │ │
│  └─────────────────────────────┘  │  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Database Usage

#### Starting All Databases
```bash
docker-compose up postgres omop-db mysql
```

#### Starting Source Databases Only
```bash
docker-compose up postgres mysql
```

#### Starting Target Database Only
```bash
docker-compose up omop-db
```

### Data Flow

1. **Extraction**: Data is extracted from source databases (PostgreSQL/MySQL clinical data)
2. **Transformation**: Data is transformed from source format to OMOP CDM format
3. **Loading**: Transformed data is loaded into the target OMOP CDM database

### Database Testing

- **Unit Tests**: Use mock data or in-memory databases
- **Integration Tests**: Use the actual source and target databases
- **Extraction Tests**: Test extraction from both PostgreSQL and MySQL source databases
- **Load Tests**: Test loading into the OMOP CDM target database

### Database Notes

- Source databases contain realistic clinical data for testing extraction functionality
- Target database contains the standardized OMOP CDM schema
- All databases are containerized for consistent testing environments
- UK healthcare data is used in MySQL source for testing NHS-specific scenarios

### Monitoring Configuration  
- **`scripts/prometheus.yml`** - Prometheus monitoring configuration for metrics collection
- **`scripts/nginx.conf`** - Reverse proxy configuration for production deployments
- **`scripts/grafana/`** - Grafana dashboards and datasources for visualization

These files work automatically with the Docker Compose setup and don't require manual configuration.

## Recommended Workflows

### Development Workflow (Recommended)
```bash
# 1. Start with architecture detection
./scripts/build.sh detect-arch

# 2. Begin development with auto-configuration
./scripts/build.sh dev -e dev

# 3. Test specific components with filters
./scripts/build.sh test -t extract --filter "ConnectionPool*"

# 4. Format and lint code
./scripts/build.sh format
./scripts/build.sh lint

# 5. Build documentation
./scripts/build.sh docs
```

### CI/CD Workflow
```bash
# 1. Validate configuration
./scripts/build.sh config -e prod --validate

# 2. Build and test all components
./scripts/build.sh build -e prod --tests --clean

# 3. Run comprehensive tests
./scripts/build.sh test -t all

# 4. Deploy services
./scripts/build.sh up -e prod --profiles "api,postgres"
```

### Multi-Architecture Deployment
```bash
# 1. Check architecture compatibility
./scripts/build.sh detect-arch

# 2. Build for multiple platforms
./scripts/build.sh build -t api --platform linux/amd64
./scripts/build.sh build -t api --platform linux/arm64

# 3. Push to registry
./scripts/build.sh build -t api --push --registry myregistry.com
```

## Quick Reference

### Most Common Tasks
| Task | Command |
|------|---------|
| **Start development** | `./scripts/build.sh dev` |
| **Test all libraries** | `./scripts/build.sh test -t all` |
| **Test specific library** | `./scripts/build.sh test -t extract` |
| **Test with filter** | `./scripts/build.sh test -t extract --filter "ConnectionPool*"` |
| **Interactive shell** | `./scripts/build.sh shell` |
| **Build production** | `./scripts/build.sh build -e prod` |
| **Generate config** | `./scripts/build.sh config -e dev` |
| **Architecture check** | `./scripts/build.sh detect-arch` |
| **Start full stack** | `./scripts/build.sh up --profiles "api,postgres"` |
| **Clean everything** | `./scripts/build.sh clean` |

### Environment-Specific Commands
| Environment | Configuration | Build | Deploy |
|-------------|---------------|-------|--------|
| **Development** | `./scripts/build.sh config -e dev` | `./scripts/build.sh build -e dev` | `./scripts/build.sh dev -e dev` |
| **Staging** | `./scripts/build.sh config -e staging` | `./scripts/build.sh build -e staging` | `./scripts/build.sh up -e staging` |
| **Production** | `./scripts/build.sh config -e prod --validate` | `./scripts/build.sh build -e prod --tests` | `./scripts/build.sh up -e prod` |

### Component Testing
| Component | Unit Tests | Integration Tests |
|-----------|------------|-------------------|
| **Common** | `./scripts/build.sh test -t common` | `./scripts/build.sh test -t common --filter "*Integration*"` |
| **Core** | `./scripts/build.sh test -t core` | `./scripts/build.sh test -t core --filter "*Integration*"` |
| **Extract** | `./scripts/build.sh test -t extract` | `./scripts/build.sh test -t extract --filter "*Integration*"` |
| **Transform** | `./scripts/build.sh test -t transform` | `./scripts/build.sh test -t transform --filter "*Integration*"` |
| **Load** | `./scripts/build.sh test -t load` | `./scripts/build.sh test -t load --filter "*Integration*"` |
| **API** | `./scripts/build.sh test -t api` | `./scripts/build.sh test -t api --filter "*Integration*"` |

## Legacy File Cleanup

The following files have been consolidated into the unified system and can be safely removed:

### Dockerfiles (12 files) ✅ Replaced by `Dockerfile`
- `Dockerfile.api`, `Dockerfile.cdm`, `Dockerfile.cli`, `Dockerfile.common`
- `Dockerfile.core`, `Dockerfile.dev`, `Dockerfile.dev.arm64`, `Dockerfile.extract`
- `Dockerfile.load`, `Dockerfile.service`, `Dockerfile.transform`

### Build Scripts (6 files) ✅ Replaced by `scripts/build.sh`  
- `scripts/detect-architecture.sh` → `./scripts/build.sh detect-arch`
- `scripts/dev-test.sh` → `./scripts/build.sh test`
- `scripts/docker-build.sh` → `./scripts/build.sh build`
- `scripts/docker-dev-build.sh` → `./scripts/build.sh build --dev-tools`
- `scripts/docker-dev.sh` → `./scripts/build.sh dev`
- `scripts/rebuild-docs.sh` → `./scripts/build.sh docs`

### Additional Legacy Files ✅ Previously removed
- `docker-build-multiarch.sh`, `docker-build-simple.sh`, `docker-build-with-toolchain.sh`
- `demo-docker-cmake.sh`, `test-docker-build.sh`, `deploy_postgres.sh`, `build-targets.sh`

**Migration Status**: ✅ Complete - All functionality preserved in unified system 