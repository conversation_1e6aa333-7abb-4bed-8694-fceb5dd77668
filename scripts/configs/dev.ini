[environment]
name = development
description = Local development environment

[build]
target = all
type = debug
enable_tests = true
enable_grpc = true
enable_rest_api = true

[dev]
build_target = all
build_type = debug
enable_tests = true
enable_grpc = true
enable_rest_api = true

[docker]
platform = linux/amd64
buildkit = 1
compose_cli_build = 1

[database.postgres]
host = postgres
port = 5432
db = clinical_db_dev
user = clinical_user_dev
password = dev_clinical_pass_123

[database.omop]
host = omop-db
port = 5432
db = omop_cdm_dev
user = omop_user_dev
password = dev_omop_pass_123

[security]
jwt_secret = dev-jwt-secret-key-not-for-production
encryption_key = dev-encryption-key-32-chars-long

[logging]
level = DEBUG
format = json
rotation = daily

[paths]
config = /etc/omop-etl
logs = /var/log/omop-etl
data = /var/lib/omop-etl/data
backup = /var/lib/omop-etl/backup

[performance]
max_parallel_jobs = 4
memory_limit = 8G
cpu_limit = 4.0

[registry]
enabled = false
url = 
tag = dev-latest
username = 
password = 

[features]
monitoring = true
metrics = true
profiling = true
debug = true

[environment_overrides]
DEVELOPMENT_MODE = true
DEBUG_LOGGING = true
SKIP_MIGRATIONS = false
ENABLE_HOT_RELOAD = true 