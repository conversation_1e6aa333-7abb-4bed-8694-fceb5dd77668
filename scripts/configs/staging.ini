[environment]
name = staging
description = Staging environment for testing

[build]
target = api
type = release
enable_tests = true
enable_grpc = true
enable_rest_api = true

[dev]
build_target = api
build_type = release
enable_tests = true
enable_grpc = true
enable_rest_api = true

[docker]
platform = linux/amd64
buildkit = 1
compose_cli_build = 1

[database.postgres]
host = staging-clinical-db.internal
port = 5432
db = clinical_db_staging
user = clinical_user_staging
password = ${POSTGRES_PASSWORD}

[database.omop]
host = staging-omop-db.internal
port = 5432
db = omop_cdm_staging
user = omop_user_staging
password = ${OMOP_PASSWORD}

[security]
jwt_secret = ${JWT_SECRET}
encryption_key = ${ENCRYPTION_KEY}

[logging]
level = DEBUG
format = json
rotation = daily

[paths]
config = /etc/omop-etl
logs = /var/log/omop-etl
data = /var/lib/omop-etl/data
backup = /var/lib/omop-etl/backup

[performance]
max_parallel_jobs = 6
memory_limit = 12G
cpu_limit = 6.0

[registry]
enabled = true
url = ${DOCKER_REGISTRY}
tag = staging-${BUILD_VERSION}
username = ${REGISTRY_USERNAME}
password = ${REGISTRY_PASSWORD}

[features]
monitoring = true
metrics = true
profiling = true
debug = true

[environment_overrides]
STAGING_MODE = true
ENABLE_SSL = true
REQUIRE_AUTH = true
DATA_ENCRYPTION = false
AUDIT_LOGGING = true
TEST_DATA_ENABLED = true 