# Template Configuration System

This directory contains environment-specific configuration files for the OMOP ETL Pipeline build system. The configuration system uses Jinja2 templates to generate environment-specific build configurations.

## Overview

The template configuration system provides:

- **Environment-specific configurations** for dev, staging, and production
- **Jinja2 template rendering** for dynamic value substitution  
- **Environment variable expansion** for sensitive values
- **Configuration validation** to ensure required values are present
- **Automatic integration** with the build system

## Files Structure

```
scripts/
├── build.env.j2              # Jinja2 template for build configuration
├── render_config.py          # Configuration renderer script
├── configs/
│   ├── dev.ini               # Development environment configuration
│   ├── staging.ini           # Staging environment configuration
│   ├── prod.ini              # Production environment configuration
│   └── README.md             # This file
└── build.env                 # Generated configuration (auto-created)
```

## Configuration Files

### Development (`dev.ini`)
- **Purpose**: Local development environment
- **Features**: Debug builds, verbose logging, test data
- **Security**: Development-only secrets (not for production)
- **Database**: Local Docker containers

### Staging (`staging.ini`)
- **Purpose**: Pre-production testing environment
- **Features**: Production-like builds with debugging enabled
- **Security**: Environment variables for secrets
- **Database**: Staging database servers

### Production (`prod.ini`)
- **Purpose**: Production deployment
- **Features**: Optimized release builds, structured logging
- **Security**: Environment variables for all secrets
- **Database**: Production database servers

## Usage

### Automatic Configuration (Recommended)

The build system automatically renders configurations when an environment is specified:

```bash
# Build for development (auto-renders dev.ini)
./scripts/build.sh build -e dev

# Build for production (auto-renders prod.ini)
./scripts/build.sh build -e prod

# Start development environment (auto-renders dev.ini)
./scripts/build.sh dev -e staging
```

### Manual Configuration Management

You can also manually manage configurations:

```bash
# Generate development configuration
./scripts/build.sh config -e dev

# Validate production configuration without generating
./scripts/build.sh config -e prod --validate

# Preview configuration without writing file
./scripts/build.sh config -e staging --dry-run

# Generate with custom output file
./scripts/build.sh config -e prod --config-file custom.env
```

### Direct Python Usage

You can also use the renderer directly:

```bash
# Basic usage
python3 scripts/render_config.py --env dev

# With validation
python3 scripts/render_config.py --env prod --validate

# Dry run (preview only)
python3 scripts/render_config.py --env staging --dry-run

# Custom output
python3 scripts/render_config.py --env dev --output custom.env

# Verbose output
python3 scripts/render_config.py --env prod --verbose
```

## Template Variables

The Jinja2 template (`build.env.j2`) supports the following variable categories:

### Build Configuration
```ini
[build]
target = api                    # Build target
type = release                  # Build type (release/debug)
enable_tests = false           # Enable test building
enable_grpc = true             # Enable gRPC support
enable_rest_api = true         # Enable REST API support
```

### Development Configuration
```ini
[dev]
build_target = all             # Development build target
build_type = debug             # Development build type
enable_tests = true            # Enable tests in development
enable_grpc = true             # Enable gRPC in development
enable_rest_api = true         # Enable REST API in development
```

### Docker Configuration
```ini
[docker]
platform = linux/amd64        # Target platform
buildkit = 1                   # Enable BuildKit
compose_cli_build = 1          # Enable Compose CLI build
```

### Database Configuration
```ini
[database.postgres]
host = postgres                # PostgreSQL host
port = 5432                    # PostgreSQL port
db = clinical_db               # Database name
user = clinical_user           # Database user
password = ${POSTGRES_PASSWORD} # Password (from environment)

[database.omop]
host = omop-db                 # OMOP database host
port = 5432                    # OMOP database port
db = omop_cdm                  # OMOP database name
user = omop_user               # OMOP database user
password = ${OMOP_PASSWORD}    # Password (from environment)
```

### Security Configuration
```ini
[security]
jwt_secret = ${JWT_SECRET}           # JWT signing secret
encryption_key = ${ENCRYPTION_KEY}   # Data encryption key
```

### Other Sections
- `[logging]` - Log levels, formats, rotation
- `[paths]` - File system paths  
- `[performance]` - Resource limits and parallel job settings
- `[registry]` - Docker registry configuration
- `[features]` - Feature flags
- `[environment_overrides]` - Environment-specific overrides

## Environment Variable Expansion

Configuration values can reference environment variables using `${VAR_NAME}` syntax:

```ini
# In configuration file
password = ${DATABASE_PASSWORD}

# At runtime
export DATABASE_PASSWORD="secure-password"
python3 scripts/render_config.py --env prod
```

## Security Best Practices

### Development Environment
- ✅ Hardcoded development values are acceptable
- ✅ Simple passwords for local development
- ✅ Debug features enabled

### Production Environment  
- ❌ **Never** hardcode production secrets
- ✅ Use environment variables for all sensitive data
- ✅ Validate all required environment variables are set
- ✅ Disable debug features

### Environment Variables for Production
```bash
export POSTGRES_PASSWORD="secure-production-password"
export OMOP_PASSWORD="secure-omop-password"  
export JWT_SECRET="production-jwt-secret-minimum-32-chars"
export ENCRYPTION_KEY="production-encryption-key-32-chars"
export DOCKER_REGISTRY="your-registry.example.com"
export REGISTRY_USERNAME="registry-user"
export REGISTRY_PASSWORD="registry-token"
```

## Configuration Validation

The system validates configurations to ensure:

- All required sections are present
- Database configurations are complete
- Production environments use secure secrets
- Environment variables are expandable

### Validation Rules

1. **Required Sections**: build, docker, database, security, logging, paths
2. **Database**: Both postgres and omop configurations must be present
3. **Production Security**: JWT secrets cannot start with "dev-"
4. **Environment Variables**: Variables like `${VAR_NAME}` are expanded from environment

## Customization

### Adding New Environments

1. Create a new INI file (e.g., `test.ini`)
2. Update the renderer script to include the new environment
3. Add validation rules if needed

```bash
cp scripts/configs/staging.ini scripts/configs/test.ini
# Edit test.ini with test-specific values
# Update render_config.py choices list
```

### Adding New Configuration Sections

1. Add the section to your INI files:
```ini
[new_section]
setting1 = value1
setting2 = value2
```

2. Add corresponding template variables to `build.env.j2`:
```jinja2
# New Section Configuration
NEW_SETTING1={{ new_section.setting1 }}
NEW_SETTING2={{ new_section.setting2 }}
```

### Custom Templates

You can create custom templates for specific use cases:

```bash
# Create custom template
cp scripts/build.env.j2 scripts/custom.env.j2

# Use custom template
python3 scripts/render_config.py --env dev --template scripts/custom.env.j2
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Setup Environment
  run: |
    export POSTGRES_PASSWORD="${{ secrets.POSTGRES_PASSWORD }}"
    export JWT_SECRET="${{ secrets.JWT_SECRET }}"
    ./scripts/build.sh config -e prod --validate

- name: Build Production
  run: ./scripts/build.sh build -e prod
```

### Jenkins Pipeline Example
```groovy
stage('Configure') {
    steps {
        withCredentials([
            string(credentialsId: 'postgres-password', variable: 'POSTGRES_PASSWORD'),
            string(credentialsId: 'jwt-secret', variable: 'JWT_SECRET')
        ]) {
            sh './scripts/build.sh config -e prod --validate'
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **Missing Jinja2**
   ```bash
   pip install jinja2
   ```

2. **Environment Variable Not Set**
   ```bash
   export REQUIRED_VAR="value"
   ./scripts/build.sh config -e prod --validate
   ```

3. **Configuration Validation Failed**
   ```bash
   ./scripts/build.sh config -e prod --validate --verbose
   ```

4. **Template Rendering Failed**
   ```bash
   python3 scripts/render_config.py --env dev --verbose
   ```

### Debug Information

```bash
# Check what configuration would be generated
./scripts/build.sh config -e dev --dry-run

# Validate configuration
./scripts/build.sh config -e prod --validate

# Verbose rendering
python3 scripts/render_config.py --env staging --verbose

# Check generated configuration
cat scripts/build.env
```

## Examples

### Complete Development Workflow
```bash
# 1. Generate dev configuration
./scripts/build.sh config -e dev

# 2. Build with dev configuration
./scripts/build.sh build -e dev

# 3. Start development environment
./scripts/build.sh dev -e dev
```

### Production Deployment
```bash
# 1. Set production secrets
export POSTGRES_PASSWORD="prod-secure-password"
export JWT_SECRET="production-jwt-secret-32-chars-min"

# 2. Validate production configuration
./scripts/build.sh config -e prod --validate

# 3. Build production images
./scripts/build.sh build -e prod --clean

# 4. Deploy services
./scripts/build.sh up -e prod --profiles "api,postgres"
```

### Multi-Environment Testing
```bash
# Test all environments
for env in dev staging prod; do
    echo "Testing $env environment..."
    ./scripts/build.sh config -e $env --validate
done
```

This template configuration system provides a robust, secure, and flexible way to manage environment-specific configurations for the OMOP ETL Pipeline. 