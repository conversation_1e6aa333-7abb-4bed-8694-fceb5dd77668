[environment]
name = production
description = Production environment

[build]
target = api
type = release
enable_tests = false
enable_grpc = true
enable_rest_api = true

[dev]
build_target = api
build_type = release
enable_tests = false
enable_grpc = true
enable_rest_api = true

[docker]
platform = linux/amd64
buildkit = 1
compose_cli_build = 1

[database.postgres]
host = prod-clinical-db.internal
port = 5432
db = clinical_db
user = clinical_user
password = ${POSTGRES_PASSWORD}

[database.omop]
host = prod-omop-db.internal
port = 5432
db = omop_cdm
user = omop_user
password = ${OMOP_PASSWORD}

[security]
jwt_secret = ${JWT_SECRET}
encryption_key = ${ENCRYPTION_KEY}

[logging]
level = INFO
format = structured
rotation = weekly

[paths]
config = /etc/omop-etl
logs = /var/log/omop-etl
data = /var/lib/omop-etl/data
backup = /var/lib/omop-etl/backup

[performance]
max_parallel_jobs = 8
memory_limit = 16G
cpu_limit = 8.0

[registry]
enabled = true
url = ${DOCKER_REGISTRY}
tag = ${BUILD_VERSION}
username = ${REGISTRY_USERNAME}
password = ${REGISTRY_PASSWORD}

[features]
monitoring = true
metrics = true
profiling = false
debug = false

[environment_overrides]
PRODUCTION_MODE = true
ENABLE_SSL = true
REQUIRE_AUTH = true
DATA_ENCRYPTION = true
AUDIT_LOGGING = true 