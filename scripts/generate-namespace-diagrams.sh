#!/bin/bash

# Script to generate namespace-wide class diagrams using external tools
# This script extracts class information from Doxygen XML output and generates diagrams

set -e

echo "Generating namespace-wide class diagrams..."

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Check multiple possible XML output locations
DOXYGEN_XML_DIRS=(
    "$PROJECT_ROOT/docs/sources/xml"
    "$PROJECT_ROOT/build/docs/sources/xml"
    "$PROJECT_ROOT/docs/sources/html/xml"
    "$PROJECT_ROOT/build/docs/xml"
)

DOXYGEN_XML_DIR=""
for dir in "${DOXYGEN_XML_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        DOXYGEN_XML_DIR="$dir"
        echo "Found Doxygen XML output at: $DOXYGEN_XML_DIR"
        break
    fi
done

if [ -z "$DOXYGEN_XML_DIR" ]; then
    echo "Doxygen XML output not found in any of these locations:"
    for dir in "${DOXYGEN_XML_DIRS[@]}"; do
        echo "  - $dir"
    done
    echo ""
    echo "Please build documentation first:"
    echo "  ./scripts/rebuild-docs.sh"
    exit 1
fi

OUTPUT_DIR="$PROJECT_ROOT/docs/sources/html/namespace_diagrams"

# Check if Doxygen XML output exists
if [ ! -d "$DOXYGEN_XML_DIR" ]; then
    echo "Doxygen XML output not found. Please build documentation first:"
    echo "  ./scripts/rebuild-docs.sh"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Check for required tools
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required for generating diagrams"
    exit 1
fi

# Create Python script to generate namespace diagrams
cat > "$OUTPUT_DIR/generate_diagrams.py" << 'EOF'
#!/usr/bin/env python3
"""
Generate namespace-wide class diagrams from Doxygen XML output
"""

import xml.etree.ElementTree as ET
import os
import sys
from collections import defaultdict

def parse_doxygen_xml(xml_dir):
    """Parse Doxygen XML files to extract class information"""
    classes = defaultdict(list)
    
    # Find all compound XML files
    for filename in os.listdir(xml_dir):
        if filename.startswith('class') and filename.endswith('.xml'):
            filepath = os.path.join(xml_dir, filename)
            try:
                tree = ET.parse(filepath)
                root = tree.getroot()
                
                # Extract class information
                compound = root.find('compounddef')
                if compound is not None:
                    kind = compound.get('kind')
                    if kind == 'class' or kind == 'struct':
                        class_name = compound.find('compoundname')
                        if class_name is not None:
                            full_name = class_name.text
                            if '::' in full_name:
                                namespace = full_name.rsplit('::', 1)[0]
                                class_short_name = full_name.rsplit('::', 1)[1]
                                
                                # Get base classes
                                base_classes = []
                                for base in compound.findall('.//basecompoundref'):
                                    base_name = base.text
                                    if base_name:
                                        base_classes.append(base_name)
                                
                                classes[namespace].append({
                                    'name': class_short_name,
                                    'full_name': full_name,
                                    'base_classes': base_classes,
                                    'kind': kind
                                })
            except ET.ParseError:
                continue
    
    return classes

def generate_plantuml_diagram(namespace, classes):
    """Generate PlantUML diagram for a namespace"""
    diagram = f"""@startuml {namespace.replace('::', '_')}
!theme plain
title Class Diagram for {namespace}

"""
    
    # Add classes
    for cls in classes:
        class_type = 'class' if cls['kind'] == 'class' else 'struct'
        diagram += f"{class_type} {cls['name']} {{\n"
        diagram += "}\n\n"
    
    # Add inheritance relationships
    for cls in classes:
        for base in cls['base_classes']:
            base_short = base.split('::')[-1]
            diagram += f"{base_short} <|-- {cls['name']}\n"
    
    diagram += "@enduml\n"
    return diagram

def generate_dot_diagram(namespace, classes):
    """Generate DOT diagram for a namespace"""
    diagram = f"""digraph {namespace.replace('::', '_')} {{
    rankdir=TB;
    node [shape=record, fontname="Arial", fontsize=10];
    edge [fontname="Arial", fontsize=8];
    
    label="Class Diagram for {namespace}";
    labelloc="t";
    fontsize=16;
    
"""
    
    # Add classes
    for cls in classes:
        class_type = 'class' if cls['kind'] == 'class' else 'struct'
        color = 'lightblue' if class_type == 'class' else 'lightgreen'
        diagram += f'    "{cls["name"]}" [label="{{{{{cls["name"]}|{class_type}}}}}", fillcolor="{color}", style=filled];\n'
    
    # Add inheritance relationships
    for cls in classes:
        for base in cls['base_classes']:
            base_short = base.split('::')[-1]
            diagram += f'    "{base_short}" -> "{cls["name"]}" [arrowhead=empty];\n'
    
    diagram += "}\n"
    return diagram

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 generate_diagrams.py <xml_directory>")
        sys.exit(1)
    
    xml_dir = sys.argv[1]
    if not os.path.exists(xml_dir):
        print(f"XML directory not found: {xml_dir}")
        sys.exit(1)
    
    # Parse classes from XML
    classes_by_namespace = parse_doxygen_xml(xml_dir)
    
    # Generate diagrams for each namespace
    for namespace, classes in classes_by_namespace.items():
        if not classes:
            continue
            
        print(f"Generating diagram for {namespace} ({len(classes)} classes)")
        
        # Generate PlantUML diagram
        plantuml_content = generate_plantuml_diagram(namespace, classes)
        plantuml_file = f"namespace_{namespace.replace('::', '_')}.puml"
        with open(plantuml_file, 'w') as f:
            f.write(plantuml_content)
        
        # Generate DOT diagram
        dot_content = generate_dot_diagram(namespace, classes)
        dot_file = f"namespace_{namespace.replace('::', '_')}.dot"
        with open(dot_file, 'w') as f:
            f.write(dot_content)
        
        # Generate HTML page
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Class Diagram - {namespace}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .diagram {{ margin: 20px 0; padding: 10px; border: 1px solid #ccc; }}
        .info {{ background: #f0f0f0; padding: 10px; margin: 10px 0; }}
    </style>
</head>
<body>
    <h1>Class Diagram for {namespace}</h1>
    
    <div class="info">
        <h3>Namespace Information</h3>
        <p><strong>Namespace:</strong> {namespace}</p>
        <p><strong>Total Classes:</strong> {len(classes)}</p>
        <p><strong>Classes:</strong> {', '.join([cls['name'] for cls in classes])}</p>
    </div>
    
    <div class="diagram">
        <h3>PlantUML Diagram</h3>
        <p>Copy the content below to a PlantUML editor or online tool:</p>
        <pre><code>{plantuml_content}</code></pre>
    </div>
    
    <div class="diagram">
        <h3>DOT Diagram</h3>
        <p>Use Graphviz to render this diagram:</p>
        <pre><code>{dot_content}</code></pre>
    </div>
    
    <div class="diagram">
        <h3>Class Details</h3>
        <ul>
"""
        
        for cls in classes:
            html_content += f"            <li><strong>{cls['name']}</strong> ({cls['kind']})"
            if cls['base_classes']:
                html_content += f" - extends: {', '.join(cls['base_classes'])}"
            html_content += "</li>\n"
        
        html_content += """        </ul>
    </div>
</body>
</html>"""
        
        html_file = f"namespace_{namespace.replace('::', '_')}.html"
        with open(html_file, 'w') as f:
            f.write(html_content)
        
        print(f"  Generated: {plantuml_file}, {dot_file}, {html_file}")

if __name__ == "__main__":
    main()
EOF

# Make the Python script executable
chmod +x "$OUTPUT_DIR/generate_diagrams.py"

# Run the diagram generation
echo "Extracting class information from Doxygen XML..."
cd "$OUTPUT_DIR"
python3 generate_diagrams.py "$DOXYGEN_XML_DIR"

echo ""
echo "Namespace diagrams generated in: $OUTPUT_DIR"
echo ""
echo "Generated files:"
echo "- *.puml files: PlantUML diagrams (can be rendered online at plantuml.com)"
echo "- *.dot files: Graphviz DOT diagrams"
echo "- *.html files: HTML pages with diagrams and class information"
echo ""
echo "To render the diagrams:"
echo "1. PlantUML: Copy .puml content to plantuml.com or use PlantUML tool"
echo "2. Graphviz: Use 'dot -Tsvg file.dot -o file.svg' to generate SVG"
echo "3. HTML: Open .html files in a browser to view class information" 