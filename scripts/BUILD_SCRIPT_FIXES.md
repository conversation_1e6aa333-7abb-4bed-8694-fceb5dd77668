# Build Script Fixes for Unit and Integration Tests

## Problem Description

The original `scripts/build.sh` script had several issues with test execution:

1. **Incorrect test type selection**: The script used the `--filter` parameter to determine whether to run unit or integration tests, which was flawed because filters are meant for filtering test names, not test types.

2. **Hardcoded test patterns**: The script had hardcoded test target patterns that didn't properly distinguish between unit and integration tests.

3. **Limited test target support**: The script only supported a subset of available test targets.

4. **Poor test discovery**: The script didn't properly discover and run tests from the correct directories (`tests/unit` vs `tests/integration`).

## Solution Implemented

### 1. New `--test-type` Parameter

Added a new command-line parameter to explicitly specify test type:

```bash
--test-type TYPE       Test type: unit, integration (default: unit)
```

**Usage Examples:**
```bash
# Run unit tests for extract target
./scripts/build.sh test -t extract --test-type unit

# Run integration tests for extract target  
./scripts/build.sh test -t extract --test-type integration

# Run all unit tests
./scripts/build.sh test -t all --test-type unit

# Run all integration tests
./scripts/build.sh test -t all --test-type integration
```

### 2. Improved Test Pattern Logic

The script now properly determines test patterns based on test type:

- **Unit tests**: Uses pattern `test_omop_{target}_unit` or `-L unit` for all targets
- **Integration tests**: Uses pattern `test_omop_{target}_integration` or `-L integration` for all targets

### 3. Enhanced Test Discovery

The script now:
- Builds the project first using CMake presets
- Uses `ctest` with proper labels (`-L unit` or `-L integration`) for test type selection
- Falls back to pattern matching (`-R pattern`) for specific targets
- Supports both filtered and unfiltered test execution

### 4. Expanded Target Support

Added support for additional test targets:
- `security` - Security and authentication components
- `ml` - Machine learning and medical term classification

### 5. Better Error Handling

- Validates test type parameter (only accepts `unit` or `integration`)
- Provides clear error messages for invalid parameters
- Handles cases where no test executables are found

## Key Changes Made

### 1. Added TEST_TYPE Variable
```bash
TEST_TYPE="unit"  # New parameter: unit or integration
```

### 2. Updated Usage Documentation
```bash
TEST OPTIONS:
    --filter FILTER        Run only tests matching filter
    --test-type TYPE       Test type: unit, integration (default: unit)
    -v, --verbose          Enable verbose test output
```

### 3. Rewritten test_target() Function
- Removes dependency on hardcoded test commands
- Uses CMake presets for building
- Implements proper test pattern logic
- Supports both filtered and unfiltered execution
- Uses `ctest` with appropriate labels and patterns

### 4. Enhanced Argument Validation
```bash
--test-type)
    TEST_TYPE="$2"
    if [[ "$TEST_TYPE" != "unit" && "$TEST_TYPE" != "integration" ]]; then
        print_error "Invalid test type: $TEST_TYPE. Must be unit or integration."
        exit 1
    fi
    shift 2
    ;;
```

## Benefits

1. **Clear separation**: Unit and integration tests are now clearly separated and run from their respective directories.

2. **Flexible filtering**: The `--filter` parameter now works correctly for filtering test names within a specific test type.

3. **Better discoverability**: Tests are discovered using CMake's test infrastructure rather than hardcoded patterns.

4. **Improved maintainability**: The script is more maintainable and follows CMake best practices.

5. **Enhanced user experience**: Clear documentation and examples make it easier for users to run the right tests.

## Migration Guide

### Old Usage (Deprecated)
```bash
# This was problematic - using filter for test type
./scripts/build.sh test -t extract --filter "unit"
./scripts/build.sh test -t extract --filter "integration"
```

### New Usage (Recommended)
```bash
# Clear and explicit test type specification
./scripts/build.sh test -t extract --test-type unit
./scripts/build.sh test -t extract --test-type integration

# Filter still works for test name filtering
./scripts/build.sh test -t extract --test-type unit --filter "ConnectionPool*"
./scripts/build.sh test -t extract --test-type integration --filter "Database*"
```

## Testing the Fixes

The fixes have been tested with:
- ✅ Help output validation
- ✅ Parameter validation (rejects invalid test types)
- ✅ Command syntax validation
- ✅ Updated documentation verification

The script now properly handles unit and integration test execution as separate concerns, making it more reliable and user-friendly. 