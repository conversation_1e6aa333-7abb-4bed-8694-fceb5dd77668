#!/bin/bash

# Unified Build Script for OMOP ETL Pipeline
# Consolidates all build, test, development, and documentation functionality

set -euo pipefail

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
COMMAND=""
BUILD_TARGET="api"
BUILD_TYPE="release"
PLATFORM=""
CLEAN_BUILD=false
ENABLE_TESTS=false
ENABLE_DEV_TOOLS=false
ENABLE_GRPC=false
ENABLE_REST_API=false
PUSH_IMAGE=false
REGISTRY=""
IMAGE_TAG="latest"
TEST_FILTER=""
TEST_TYPE="unit"
VERBOSE=false
PROFILES="default"
DOCKER_COMPOSE_FILE="scripts/docker-compose.yml"
ENVIRONMENT=""
AUTO_RENDER=true
CONFIG_FILE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Available build targets
AVAILABLE_TARGETS=("api" "cli" "common" "core" "cdm" "extract" "transform" "load" "service" "security" "ml" "all")

# Available build types
AVAILABLE_BUILD_TYPES=("release" "debug")

# Available commands
AVAILABLE_COMMANDS=("build" "test" "dev" "clean" "lint" "format" "docs" "up" "down" "shell" "detect-arch" "config")

# Summary tracking variables
BUILD_START_TIME=""
BUILD_END_TIME=""
BUILD_TARGETS_BUILT=()
BUILD_ARTIFACTS=()
BUILD_ERRORS=()
DOCKER_COMMANDS=()
ARTIFACT_PATHS=()
TEST_START_TIME=""
TEST_END_TIME=""
TEST_RESULTS=()
TEST_FAILURES=()
TEST_SUMMARY_STATS=()
TEST_REPORTS=()
COVERAGE_REPORTS=()

# Print functions
print_header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_recommendation() {
    echo -e "${GREEN}[RECOMMENDATION]${NC} $1"
}

# Summary functions
record_build_start() {
    BUILD_START_TIME=$(date +%s)
}

record_build_end() {
    BUILD_END_TIME=$(date +%s)
}

record_build_target() {
    local target="$1"
    BUILD_TARGETS_BUILT+=("$target")
}

record_build_artifact() {
    local artifact="$1"
    BUILD_ARTIFACTS+=("$artifact")
}

record_build_error() {
    local error="$1"
    BUILD_ERRORS+=("$error")
}

record_test_start() {
    TEST_START_TIME=$(date +%s)
}

record_test_end() {
    TEST_END_TIME=$(date +%s)
}

record_test_result() {
    local result="$1"
    TEST_RESULTS+=("$result")
}

record_test_failure() {
    local failure="$1"
    TEST_FAILURES+=("$failure")
}

record_test_stats() {
    local stats="$1"
    TEST_SUMMARY_STATS+=("$stats")
}

record_docker_command() {
    local target="$1"
    local command="$2"
    DOCKER_COMMANDS+=("$target:$command")
}

record_artifact_path() {
    local path="$1"
    local rel_path=$(realpath --relative-to="$PROJECT_ROOT" "$path" 2>/dev/null || echo "$path")
    
    # Check if path already exists to avoid duplicates
    local exists=false
    if [[ ${#ARTIFACT_PATHS[@]} -gt 0 ]]; then
        for existing_path in "${ARTIFACT_PATHS[@]}"; do
            if [[ "$existing_path" == "$rel_path" ]]; then
                exists=true
                break
            fi
        done
    fi
    
    if [[ "$exists" == "false" ]]; then
        ARTIFACT_PATHS+=("$rel_path")
    fi
}

record_test_report() {
    local report="$1"
    TEST_REPORTS+=("$report")
}

record_coverage_report() {
    local report="$1"
    COVERAGE_REPORTS+=("$report")
}

# Record test reports and coverage files
record_test_reports_and_coverage() {
    print_info "Scanning for test reports and coverage files..."
    
    # Look for common test report locations
    local build_dir="$PROJECT_ROOT/build"
    local test_dirs=("$build_dir" "$PROJECT_ROOT/tests" "$PROJECT_ROOT/test-results" "$PROJECT_ROOT/reports")
    
    # Search for test reports
    for dir in "${test_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            # JUnit XML reports
            while IFS= read -r -d '' file; do
                record_test_report "$file"
            done < <(find "$dir" -name "*test*.xml" -o -name "*Test*.xml" -o -name "junit*.xml" -print0 2>/dev/null)
            
            # HTML test reports
            while IFS= read -r -d '' file; do
                record_test_report "$file"
            done < <(find "$dir" -name "*test*.html" -o -name "*Test*.html" -o -name "test-report*.html" -print0 2>/dev/null)
            
            # JSON test reports
            while IFS= read -r -d '' file; do
                record_test_report "$file"
            done < <(find "$dir" -name "*test*.json" -o -name "*Test*.json" -print0 2>/dev/null)
            
            # Test log files
            while IFS= read -r -d '' file; do
                record_test_report "$file"
            done < <(find "$dir" -name "*test*.log" -o -name "*Test*.log" -o -name "ctest*.log" -print0 2>/dev/null)
        fi
    done
    
    # Search for coverage reports
    local coverage_dirs=("$build_dir" "$PROJECT_ROOT/coverage" "$PROJECT_ROOT/htmlcov" "$PROJECT_ROOT/cov_html")
    
    for dir in "${coverage_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            # HTML coverage reports (directories)
            while IFS= read -r -d '' file; do
                if [[ -d "$file" ]]; then
                    record_coverage_report "$file"
                fi
            done < <(find "$dir" -name "htmlcov" -o -name "coverage_html" -o -name "html" -type d -print0 2>/dev/null)
            
            # Individual HTML coverage files
            while IFS= read -r -d '' file; do
                record_coverage_report "$file"
            done < <(find "$dir" -name "coverage*.html" -o -name "index.html" -path "*/coverage/*" -print0 2>/dev/null)
            
            # LCOV reports
            while IFS= read -r -d '' file; do
                record_coverage_report "$file"
            done < <(find "$dir" -name "*.lcov" -o -name "coverage.info" -o -name "*.info" -print0 2>/dev/null)
            
            # XML coverage reports
            while IFS= read -r -d '' file; do
                record_coverage_report "$file"
            done < <(find "$dir" -name "coverage*.xml" -o -name "cobertura*.xml" -print0 2>/dev/null)
            
            # JSON coverage reports
            while IFS= read -r -d '' file; do
                record_coverage_report "$file"
            done < <(find "$dir" -name "coverage*.json" -print0 2>/dev/null)
        fi
    done
    
    # Record common build artifacts that might exist
    local common_artifacts=(
        "$PROJECT_ROOT/build/docker-debug/Testing/Temporary/LastTest.log"
        "$PROJECT_ROOT/build/docker-debug/Testing/Temporary/LastTestsFailed.log"
        "$PROJECT_ROOT/build/Testing/"
        "$PROJECT_ROOT/CMakeCache.txt"
        "$PROJECT_ROOT/compile_commands.json"
    )
    
    for artifact in "${common_artifacts[@]}"; do
        if [[ -f "$artifact" || -d "$artifact" ]]; then
            record_artifact_path "$artifact"
        fi
    done
    
    local test_report_count=${#TEST_REPORTS[@]}
    local coverage_report_count=${#COVERAGE_REPORTS[@]}
    
    if [[ $test_report_count -gt 0 ]]; then
        print_info "Found $test_report_count test report(s)"
    fi
    
    if [[ $coverage_report_count -gt 0 ]]; then
        print_info "Found $coverage_report_count coverage report(s)"
    fi
}

# Discover actual build artifacts based on what the build target should produce
discover_build_artifacts() {
    print_info "Discovering build artifacts for target: $BUILD_TARGET"
    
    case "$BUILD_TARGET" in
        "api")
            # API builds produce Docker image - no file artifacts
            print_info "API build produces Docker image (shown in Docker Images section)"
            ;;
        "cli")
            # CLI builds produce Docker image - no file artifacts  
            print_info "CLI build produces Docker image (shown in Docker Images section)"
            ;;
        "service")
            # Service builds produce Docker image - no file artifacts
            print_info "Service build produces Docker image (shown in Docker Images section)"
            ;;
        "all")
            # All builds produce multiple Docker images - no file artifacts
            print_info "All builds produce Docker images (shown in Docker Images section)"
            ;;
        "documentation")
            # Documentation build produces HTML docs
            discover_documentation_artifacts
            ;;
        "common"|"core"|"cdm"|"extract"|"transform"|"load"|"security"|"ml")
            # Library targets produce specific library files
            discover_target_library_artifacts "$BUILD_TARGET"
            ;;
        *)
            # Fallback for unknown targets
            print_info "Unknown target type - checking for general artifacts"
            discover_general_artifacts
            ;;
    esac
    
    local artifact_count=${#ARTIFACT_PATHS[@]}
    if [[ $artifact_count -gt 0 ]]; then
        print_info "Found $artifact_count target-specific artifact(s)"
    else
        print_info "No file artifacts generated (containerized build)"
    fi
}

# Discover documentation artifacts
discover_documentation_artifacts() {
    # Function to run commands in dev container
    run_in_container() {
        docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "$1"
    }
    
    # Check for generated documentation files
    if run_in_container "test -d /workspace/docs/sources/html" 2>/dev/null; then
        record_artifact_path "docs/sources/html/"
    fi
    
    if run_in_container "test -d /workspace/build/docs" 2>/dev/null; then
        record_artifact_path "build/docs/"
    fi
    
    # Look for specific documentation files
    local doc_files=(
        "/workspace/docs/sources/html/index.html"
        "/workspace/build/docs/html/index.html"
    )
    
    for file in "${doc_files[@]}"; do
        if run_in_container "test -f $file" 2>/dev/null; then
            local rel_path="${file#/workspace/}"
            record_artifact_path "$rel_path"
        fi
    done
}

# Discover artifacts for specific library targets
discover_target_library_artifacts() {
    local target="$1"
    
    # Function to run commands in dev container
    run_in_container() {
        docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "$1"
    }
    
    # Look for the specific library that this target should produce
    local lib_patterns=(
        "libomop_${target}.so*"
        "libomop_${target}_d.a"
        "libomop_${target}.a"
        "libomop_${target}_d.so*"
        "libomop_${target}.dylib"
        "libomop_${target}_d.dylib"
    )
    
    for pattern in "${lib_patterns[@]}"; do
        local lib_search_result=$(run_in_container "find /workspace/build -name '$pattern' -type f -mmin -10 2>/dev/null || echo 'none'" 2>/dev/null)
        
        if [[ "$lib_search_result" != "none" && -n "$lib_search_result" ]]; then
            echo "$lib_search_result" | while IFS= read -r file; do
                if [[ -n "$file" ]]; then
                    local rel_path="${file#/workspace/}"
                    record_artifact_path "$rel_path"
                fi
            done
        fi
    done
    
    # Look for target-specific executables (if this target produces them)
    case "$target" in
        "cli"|"core")
            # These targets might produce executables
            local exe_search_result=$(run_in_container "find /workspace/build -name '*${target}*' -type f -executable -mmin -10 2>/dev/null | grep -v test | head -5 || echo 'none'" 2>/dev/null)
            
            if [[ "$exe_search_result" != "none" && -n "$exe_search_result" ]]; then
                echo "$exe_search_result" | while IFS= read -r file; do
                    if [[ -n "$file" ]]; then
                        local rel_path="${file#/workspace/}"
                        record_artifact_path "$rel_path"
                    fi
                done
            fi
            ;;
    esac
}

# Discover general artifacts (fallback)
discover_general_artifacts() {
    # Function to run commands in dev container
    run_in_container() {
        docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "$1"
    }
    
    # Look for any recently built libraries
    local lib_search_result=$(run_in_container "find /workspace/build -type f \\( -name '*.so' -o -name '*.so.*' -o -name '*.a' -o -name '*.dylib' \\) -mmin -10 2>/dev/null | head -10 || echo 'none'" 2>/dev/null)
    
    if [[ "$lib_search_result" != "none" && -n "$lib_search_result" ]]; then
        echo "$lib_search_result" | while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                local rel_path="${file#/workspace/}"
                record_artifact_path "$rel_path"
            fi
        done
    fi
}

# Format duration
format_duration() {
    local seconds="$1"
    local hours=$((seconds / 3600))
    local minutes=$(((seconds % 3600) / 60))
    local secs=$((seconds % 60))
    
    if [[ $hours -gt 0 ]]; then
        echo "${hours}h ${minutes}m ${secs}s"
    elif [[ $minutes -gt 0 ]]; then
        echo "${minutes}m ${secs}s"
    else
        echo "${secs}s"
    fi
}

# Show build summary
show_build_summary() {
    if [[ -z "$BUILD_START_TIME" ]]; then
        return 0
    fi
    
    local duration=""
    if [[ -n "$BUILD_END_TIME" ]]; then
        duration=$(format_duration $((BUILD_END_TIME - BUILD_START_TIME)))
    fi
    
    print_header "Build Summary"
    
    echo -e "${CYAN}Build Information:${NC}"
    echo -e "  Target: ${GREEN}${BUILD_TARGET}${NC}"
    echo -e "  Type: ${GREEN}${BUILD_TYPE}${NC}"
    echo -e "  Platform: ${GREEN}${PLATFORM:-auto-detected}${NC}"
    if [[ -n "$duration" ]]; then
        echo -e "  Duration: ${GREEN}${duration}${NC}"
    fi
    echo
    
    if [[ ${#BUILD_TARGETS_BUILT[@]} -gt 0 ]]; then
        echo -e "${CYAN}Targets Built:${NC}"
        for target in "${BUILD_TARGETS_BUILT[@]}"; do
            echo -e "  ${GREEN}✓${NC} $target"
            
            # Show associated Docker commands
            for docker_cmd in "${DOCKER_COMMANDS[@]}"; do
                if [[ "$docker_cmd" == "$target:"* ]]; then
                    local cmd="${docker_cmd#*:}"
                    echo -e "    ${BLUE}🚀 Run:${NC} $cmd"
                fi
            done
        done
        echo
    fi
    
    # Show generated file artifacts (excluding Docker images)
    if [[ ${#ARTIFACT_PATHS[@]} -gt 0 ]]; then
        echo -e "${CYAN}Generated Artifacts:${NC}"
        for rel_path in "${ARTIFACT_PATHS[@]}"; do
            local full_path="$PROJECT_ROOT/$rel_path"
            
            if [[ -f "$full_path" || -d "$full_path" ]]; then
                local size=""
                local type_indicator="📦"
                if [[ -f "$full_path" ]]; then
                    type_indicator="📄"
                    if command -v stat >/dev/null 2>&1; then
                        if [[ "$(uname)" == "Darwin" ]]; then
                            size=" ($(stat -f%z "$full_path" | numfmt --to=iec-i --suffix=B 2>/dev/null || echo "unknown size"))"
                        else
                            size=" ($(stat -c%s "$full_path" | numfmt --to=iec-i --suffix=B 2>/dev/null || echo "unknown size"))"
                        fi
                    fi
                elif [[ -d "$full_path" ]]; then
                    type_indicator="📁"
                    # Count files in directory
                    local file_count=$(find "$full_path" -type f | wc -l 2>/dev/null | xargs)
                    if [[ $file_count -gt 0 ]]; then
                        size=" ($file_count files)"
                    fi
                fi
                echo -e "  ${GREEN}$type_indicator${NC} $rel_path$size"
            else
                echo -e "  ${YELLOW}📦${NC} $rel_path (not found)"
            fi
        done
        echo
    fi
    
    if [[ ${#BUILD_ERRORS[@]} -gt 0 ]]; then
        echo -e "${CYAN}Build Issues:${NC}"
        for error in "${BUILD_ERRORS[@]}"; do
            echo -e "  ${RED}✗${NC} $error"
        done
        echo
    fi
    
    # Show Docker images created
    echo -e "${CYAN}Docker Images:${NC}"
    local images_found=false
    while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            images_found=true
            local image_name=$(echo "$line" | awk '{print $1":"$2}')
            local image_size=$(echo "$line" | awk '{print $7" "$8}')
            local image_id=$(echo "$line" | awk '{print $3}')
            local created=$(echo "$line" | awk '{print $4" "$5" "$6}')
            echo -e "  ${GREEN}🐳${NC} $image_name ($image_size, ID: ${image_id:0:12})"
            echo -e "    ${BLUE}📅 Created:${NC} $created"
            
            # Show Docker run command based on image type
            local repo_name=$(echo "$image_name" | cut -d: -f1)
            case "$repo_name" in
                *-api)
                    echo -e "    ${BLUE}🚀 Run:${NC} docker run -p 8080:8080 $image_name"
                    echo -e "    ${BLUE}🔗 Access:${NC} http://localhost:8080"
                    ;;
                *-cli)
                    echo -e "    ${BLUE}🚀 Run:${NC} docker run -it $image_name [command]"
                    echo -e "    ${BLUE}💡 Help:${NC} docker run $image_name --help"
                    ;;
                *-service)
                    echo -e "    ${BLUE}🚀 Run:${NC} docker run $image_name"
                    echo -e "    ${BLUE}🔧 Config:${NC} Mount /config volume for configuration"
                    ;;
                *-dev)
                    echo -e "    ${BLUE}🚀 Run:${NC} docker run -it -v \$(pwd):/workspace $image_name bash"
                    echo -e "    ${BLUE}🛠️  Dev:${NC} Development environment with build tools"
                    ;;
                *)
                    echo -e "    ${BLUE}🚀 Run:${NC} docker run $image_name"
                    ;;
            esac
        fi
    done < <(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" | grep omop-etl | grep -v REPOSITORY || true)
    
    if [[ "$images_found" == "false" ]]; then
        echo -e "  ${YELLOW}No OMOP ETL images found${NC}"
    fi
    echo
}

# Show test summary
show_test_summary() {
    if [[ -z "$TEST_START_TIME" ]]; then
        return 0
    fi
    
    local duration=""
    if [[ -n "$TEST_END_TIME" ]]; then
        duration=$(format_duration $((TEST_END_TIME - TEST_START_TIME)))
    fi
    
    print_header "Test Summary"
    
    echo -e "${CYAN}Test Information:${NC}"
    echo -e "  Target: ${GREEN}${BUILD_TARGET}${NC}"
    echo -e "  Type: ${GREEN}${TEST_TYPE}${NC}"
    if [[ -n "$TEST_FILTER" ]]; then
        echo -e "  Filter: ${GREEN}${TEST_FILTER}${NC}"
    fi
    if [[ -n "$duration" ]]; then
        echo -e "  Duration: ${GREEN}${duration}${NC}"
    fi
    echo
    
    if [[ ${#TEST_SUMMARY_STATS[@]} -gt 0 ]]; then
        echo -e "${CYAN}Test Statistics:${NC}"
        for stat in "${TEST_SUMMARY_STATS[@]}"; do
            echo -e "  $stat"
        done
        echo
    fi
    
    if [[ ${#TEST_RESULTS[@]} -gt 0 ]]; then
        echo -e "${CYAN}Test Results:${NC}"
        local passed=0
        local failed=0
        for result in "${TEST_RESULTS[@]}"; do
            if [[ "$result" == *"PASSED"* || "$result" == *"✓"* ]]; then
                ((passed++))
                echo -e "  ${GREEN}✓${NC} $result"
            elif [[ "$result" == *"FAILED"* || "$result" == *"✗"* ]]; then
                ((failed++))
                echo -e "  ${RED}✗${NC} $result"
            else
                echo -e "  ${BLUE}ℹ${NC} $result"
            fi
        done
        echo
        echo -e "${CYAN}Summary: ${GREEN}$passed passed${NC}, ${RED}$failed failed${NC}${NC}"
        echo
    fi
    
    if [[ ${#TEST_FAILURES[@]} -gt 0 ]]; then
        echo -e "${CYAN}Test Failures:${NC}"
        for failure in "${TEST_FAILURES[@]}"; do
            echo -e "  ${RED}✗${NC} $failure"
        done
        echo
    fi
    
    # Show test reports
    if [[ ${#TEST_REPORTS[@]} -gt 0 ]]; then
        echo -e "${CYAN}Test Reports:${NC}"
        for report in "${TEST_REPORTS[@]}"; do
            local rel_path=$(realpath --relative-to="$PROJECT_ROOT" "$report" 2>/dev/null || echo "$report")
            if [[ -f "$report" ]]; then
                local size=""
                if command -v stat >/dev/null 2>&1; then
                    if [[ "$(uname)" == "Darwin" ]]; then
                        size=" ($(stat -f%z "$report" | numfmt --to=iec-i --suffix=B 2>/dev/null || echo "unknown size"))"
                    else
                        size=" ($(stat -c%s "$report" | numfmt --to=iec-i --suffix=B 2>/dev/null || echo "unknown size"))"
                    fi
                fi
                echo -e "  ${GREEN}📄${NC} $rel_path$size"
                
                # Detect report type and provide viewing commands
                case "$report" in
                    *.xml)
                        echo -e "    ${BLUE}📊 Type:${NC} JUnit XML Report"
                        echo -e "    ${BLUE}🔍 View:${NC} cat $rel_path | xmllint --format -"
                        ;;
                    *.html)
                        echo -e "    ${BLUE}📊 Type:${NC} HTML Test Report"
                        echo -e "    ${BLUE}🌐 View:${NC} open $rel_path"
                        ;;
                    *.json)
                        echo -e "    ${BLUE}📊 Type:${NC} JSON Test Report"
                        echo -e "    ${BLUE}🔍 View:${NC} cat $rel_path | jq ."
                        ;;
                    *.log|*.txt)
                        echo -e "    ${BLUE}📊 Type:${NC} Test Log"
                        echo -e "    ${BLUE}🔍 View:${NC} less $rel_path"
                        ;;
                    *)
                        echo -e "    ${BLUE}📊 Type:${NC} Test Report"
                        echo -e "    ${BLUE}🔍 View:${NC} cat $rel_path"
                        ;;
                esac
            else
                echo -e "  ${YELLOW}📄${NC} $rel_path (not found)"
            fi
        done
        echo
    fi
    
    # Show coverage reports
    if [[ ${#COVERAGE_REPORTS[@]} -gt 0 ]]; then
        echo -e "${CYAN}Coverage Reports:${NC}"
        for report in "${COVERAGE_REPORTS[@]}"; do
            local rel_path=$(realpath --relative-to="$PROJECT_ROOT" "$report" 2>/dev/null || echo "$report")
            if [[ -f "$report" || -d "$report" ]]; then
                local size=""
                local type_indicator="📄"
                if [[ -f "$report" ]]; then
                    if command -v stat >/dev/null 2>&1; then
                        if [[ "$(uname)" == "Darwin" ]]; then
                            size=" ($(stat -f%z "$report" | numfmt --to=iec-i --suffix=B 2>/dev/null || echo "unknown size"))"
                        else
                            size=" ($(stat -c%s "$report" | numfmt --to=iec-i --suffix=B 2>/dev/null || echo "unknown size"))"
                        fi
                    fi
                elif [[ -d "$report" ]]; then
                    type_indicator="📁"
                    local file_count=$(find "$report" -name "*.html" -o -name "*.xml" -o -name "*.json" | wc -l 2>/dev/null | xargs)
                    if [[ $file_count -gt 0 ]]; then
                        size=" ($file_count files)"
                    fi
                fi
                echo -e "  ${GREEN}$type_indicator${NC} $rel_path$size"
                
                # Detect coverage report type and provide viewing commands
                case "$report" in
                    */html/*|*.html)
                        echo -e "    ${BLUE}📊 Type:${NC} HTML Coverage Report"
                        if [[ -d "$report" ]]; then
                            echo -e "    ${BLUE}🌐 View:${NC} open $rel_path/index.html"
                        else
                            echo -e "    ${BLUE}🌐 View:${NC} open $rel_path"
                        fi
                        ;;
                    *.xml)
                        echo -e "    ${BLUE}📊 Type:${NC} XML Coverage Report"
                        echo -e "    ${BLUE}🔍 View:${NC} cat $rel_path | xmllint --format -"
                        ;;
                    *.json)
                        echo -e "    ${BLUE}📊 Type:${NC} JSON Coverage Report"
                        echo -e "    ${BLUE}🔍 View:${NC} cat $rel_path | jq ."
                        ;;
                    *.lcov)
                        echo -e "    ${BLUE}📊 Type:${NC} LCOV Coverage Report"
                        echo -e "    ${BLUE}🔧 Generate HTML:${NC} genhtml $rel_path -o coverage_html"
                        ;;
                    *.info)
                        echo -e "    ${BLUE}📊 Type:${NC} Coverage Info File"
                        echo -e "    ${BLUE}🔍 View:${NC} lcov --list $rel_path"
                        ;;
                    *)
                        echo -e "    ${BLUE}📊 Type:${NC} Coverage Report"
                        echo -e "    ${BLUE}🔍 View:${NC} cat $rel_path"
                        ;;
                esac
            else
                echo -e "  ${YELLOW}📄${NC} $rel_path (not found)"
            fi
        done
        echo
    fi
}

# Show overall summary
show_overall_summary() {
    show_build_summary
    show_test_summary
    
    # Show final recommendations
    if [[ ${#BUILD_ERRORS[@]} -gt 0 || ${#TEST_FAILURES[@]} -gt 0 ]]; then
        print_header "Recommendations"
        if [[ ${#BUILD_ERRORS[@]} -gt 0 ]]; then
            echo -e "${YELLOW}Build Issues Detected:${NC}"
            echo -e "  - Review build logs above for detailed error information"
            echo -e "  - Try running with --clean to force a clean build"
            echo -e "  - Check Docker daemon status and available disk space"
            echo
        fi
        if [[ ${#TEST_FAILURES[@]} -gt 0 ]]; then
            echo -e "${YELLOW}Test Failures Detected:${NC}"
            echo -e "  - Review test output above for specific failure details"
            echo -e "  - Run individual failing tests with --filter for debugging"
            echo -e "  - Check test data and configuration files"
            echo
        fi
    else
        local total_targets=${#BUILD_TARGETS_BUILT[@]}
        local total_artifacts=${#BUILD_ARTIFACTS[@]}
        if [[ $total_targets -gt 0 || $total_artifacts -gt 0 ]]; then
            echo -e "${GREEN}🎉 All operations completed successfully!${NC}"
            echo
        fi
    fi
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 COMMAND [OPTIONS]

Unified Build Script for OMOP ETL Pipeline

COMMANDS:
    build               Build Docker images for specified target (shows build summary)
    test               Run tests for specified target (shows test summary)
    dev                Start development environment (shows summary when done)
    clean              Clean build artifacts and containers
    lint               Run static analysis (clang-tidy, cppcheck)
    format             Format code using clang-format
    docs               Build documentation (shows build summary)
    up                 Start services with docker-compose
    down               Stop and remove services
    shell              Start interactive shell in development container
    detect-arch        Detect system architecture and provide recommendations
    config             Generate configuration from templates

BUILD OPTIONS:
    -t, --target TARGET     Build target: ${AVAILABLE_TARGETS[*]} (default: api)
    -b, --build-type TYPE   Build type: ${AVAILABLE_BUILD_TYPES[*]} (default: release)
    -p, --platform PLATFORM Docker platform (auto-detected if not specified)
    --tests                 Enable test building and execution
    --dev-tools            Enable development tools in container
    --grpc                 Enable gRPC support
    --rest-api             Enable REST API support
    -c, --clean            Clean build (remove existing containers and images)

DOCKER OPTIONS:
    --push                 Push image to registry after build
    -r, --registry REGISTRY Registry URL (e.g., docker.io/myorg)
    --tag TAG              Image tag (default: latest)
    --profiles PROFILES    Docker compose profiles (default: default)

TEST OPTIONS:
    --filter FILTER        Run only tests matching filter
    --test-type TYPE       Test type: unit, integration (default: unit)
    -v, --verbose          Enable verbose test output

ENVIRONMENT OPTIONS:
    -e, --env ENVIRONMENT  Target environment: dev, staging, prod
    --config-file FILE     Use specific configuration file
    --no-auto-render       Skip automatic configuration rendering

EXAMPLES:
    $0 build                               # Build API service (default)
    $0 build -t cli --tests               # Build CLI with tests
    $0 build -t all -b debug --dev-tools  # Build all targets in debug with dev tools
    $0 test -t extract --test-type unit   # Run unit tests for extract target
    $0 test -t extract --test-type integration  # Run integration tests for extract target
    $0 test -t extract --filter "ConnectionPool*"  # Run extract tests with filter
    $0 dev                                 # Start development environment
    $0 up --profiles "api,postgres"       # Start API and database services
    $0 clean                               # Clean all build artifacts
    $0 docs                                # Build documentation
    $0 detect-arch                         # Detect architecture
    
ENVIRONMENT EXAMPLES:
    $0 config --env dev                    # Generate dev configuration
    $0 build -e prod                       # Build for production environment
    $0 dev -e staging                      # Start staging development environment
    $0 config --env prod --validate       # Validate production config

TARGET DESCRIPTIONS:
    api        - REST API service with gRPC support
    cli        - Command-line interface application
    common     - Common utilities and configuration libraries
    core       - Core pipeline and job management
    cdm        - OMOP CDM table definitions and schemas
    extract    - Data extraction from various sources
    transform  - Data transformation and validation
    load       - Data loading and batch processing
    service    - Service layer and microservice components
    security   - Security and authentication components
    ml         - Machine learning and medical term classification
    all        - Build all targets

SUMMARY FEATURES:
    The script now provides comprehensive summaries after operations:
    
    BUILD SUMMARY:
    - Lists all successfully built targets with Docker run commands
    - Shows generated Docker images with sizes, IDs, and creation dates
    - Displays build duration and configuration details
    - Reports any build errors or issues with recommendations
    - Shows created artifacts with relative paths and file sizes
    - Provides specific Docker commands to run each target
    - Includes file type indicators (📄 files, 📁 directories, 🐳 images)
    
    TEST SUMMARY:
    - Reports test execution results (passed/failed) with statistics
    - Shows test duration and configuration details
    - Displays test report files with viewing commands
    - Lists coverage reports (HTML, XML, LCOV) with access instructions
    - Provides specific commands to view different report types
    - Includes relative paths for all test artifacts
    - Shows test log locations and failure details
    
    ARTIFACT TRACKING:
    - Discovers actual build outputs: compiled libraries (.so, .a, .dylib)
    - Finds executables and binaries (excluding test executables)
    - Locates generated documentation (HTML, Doxygen output)
    - Tracks CMake-generated files (CMakeCache.txt, compile_commands.json)
    - Finds configuration files and pkg-config files
    - Shows relative paths from project root with file sizes
    - Automatically discovers test reports (JUnit XML, HTML, JSON, logs)
    - Finds coverage reports in common locations
    
    DOCKER INTEGRATION:
    - Shows specific run commands for each image type (API, CLI, service)
    - Includes port mappings and volume mounts
    - Provides access URLs for web services
    - Shows development environment setup commands

EOF
}

# Validate target
validate_target() {
    local target="$1"
    for valid_target in "${AVAILABLE_TARGETS[@]}"; do
        if [[ "$target" == "$valid_target" ]]; then
            return 0
        fi
    done
    return 1
}

# Validate build type
validate_build_type() {
    local type="$1"
    for valid_type in "${AVAILABLE_BUILD_TYPES[@]}"; do
        if [[ "$type" == "$valid_type" ]]; then
            return 0
        fi
    done
    return 1
}

# Validate command
validate_command() {
    local cmd="$1"
    for valid_cmd in "${AVAILABLE_COMMANDS[@]}"; do
        if [[ "$cmd" == "$valid_cmd" ]]; then
            return 0
        fi
    done
    return 1
}

# Detect architecture and provide recommendations
detect_architecture() {
    print_header "Architecture Detection for OMOP ETL Pipeline"
    
    # Detect operating system
    OS=$(uname -s)
    print_info "Operating System: $OS"
    
    # Detect architecture
    ARCH=$(uname -m)
    print_info "Architecture: $ARCH"
    
    # Detect processor information
    if [[ "$OS" == "Darwin" ]]; then
        # macOS
        if command -v sysctl &> /dev/null; then
            CPU_BRAND=$(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "Unknown")
            print_info "CPU: $CPU_BRAND"
            
            # Detect Apple Silicon
            if [[ "$CPU_BRAND" == *"Apple"* ]]; then
                print_info "Apple Silicon detected"
                print_recommendation "Use --platform linux/arm64 for optimal performance"
                print_recommendation "Docker Compose: export DOCKER_PLATFORM=linux/arm64"
                PLATFORM="linux/arm64"
            else
                print_info "Intel Mac detected"
                print_recommendation "Use --platform linux/amd64 for optimal performance"
                PLATFORM="linux/amd64"
            fi
        fi
    elif [[ "$OS" == "Linux" ]]; then
        # Linux
        if [ -f /proc/cpuinfo ]; then
            CPU_INFO=$(grep "model name" /proc/cpuinfo | head -1 | cut -d: -f2 | xargs)
            print_info "CPU: $CPU_INFO"
        fi
        
        case "$ARCH" in
            "x86_64")
                print_info "x86_64 (AMD64) architecture detected"
                print_recommendation "Use --platform linux/amd64"
                PLATFORM="linux/amd64"
                ;;
            "aarch64")
                print_info "ARM64 architecture detected"
                print_recommendation "Use --platform linux/arm64"
                PLATFORM="linux/arm64"
                ;;
            "armv7l")
                print_info "ARM v7 architecture detected"
                print_recommendation "Use --platform linux/arm/v7"
                PLATFORM="linux/arm/v7"
                ;;
            *)
                print_warning "Unknown architecture: $ARCH"
                ;;
        esac
    fi
    
    # Memory and CPU recommendations
    if command -v nproc &> /dev/null; then
        CPU_CORES=$(nproc)
        print_info "CPU Cores: $CPU_CORES"
        
        if [[ $CPU_CORES -ge 8 ]]; then
            print_recommendation "High-performance build recommended: use -j$CPU_CORES for parallel builds"
        elif [[ $CPU_CORES -ge 4 ]]; then
            print_recommendation "Standard build performance: use -j$CPU_CORES for parallel builds"
        else
            print_warning "Limited CPU cores detected. Build may be slow."
        fi
    fi
    
    # Memory check (Linux)
    if [[ "$OS" == "Linux" ]] && [ -f /proc/meminfo ]; then
        MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        MEMORY_GB=$((MEMORY_KB / 1024 / 1024))
        print_info "Total Memory: ${MEMORY_GB}GB"
        
        if [[ $MEMORY_GB -ge 16 ]]; then
            print_recommendation "Sufficient memory for parallel builds and development"
        elif [[ $MEMORY_GB -ge 8 ]]; then
            print_recommendation "Adequate memory for standard builds"
        else
            print_warning "Limited memory detected. Consider reducing parallel build jobs."
        fi
    fi
    
    # Docker recommendations
    if command -v docker &> /dev/null; then
        print_success "Docker is available"
        if command -v docker-compose &> /dev/null; then
            print_success "Docker Compose is available"
        else
            print_warning "Docker Compose not found. Please install docker-compose."
        fi
    else
        print_error "Docker not found. Please install Docker."
    fi
    
    print_header "Build Recommendations"
    echo -e "For optimal performance on your system:"
    echo -e "${GREEN}Platform:${NC} ${PLATFORM:-auto-detect}"
    echo -e "${GREEN}Build command:${NC} $0 build -t api --platform ${PLATFORM:-auto}"
    echo -e "${GREEN}Development:${NC} $0 dev --platform ${PLATFORM:-auto}"
    echo -e "${GREEN}Environment:${NC} export DOCKER_PLATFORM=${PLATFORM:-linux/amd64}"
}

# Render configuration from template
render_config() {
    local env="$1"
    local validate_only="${2:-false}"
    local config_args=""
    
    if [[ "$validate_only" == "true" ]]; then
        config_args="--validate"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        config_args="$config_args --verbose"
    fi
    
    if [[ -n "$CONFIG_FILE" ]]; then
        config_args="$config_args --output $CONFIG_FILE"
    fi
    
    print_info "Rendering configuration for $env environment..."
    
    if python3 "$SCRIPT_DIR/render_config.py" --env "$env" $config_args; then
        if [[ "$validate_only" != "true" ]]; then
            print_success "Configuration rendered successfully"
            # Source the rendered configuration
            if [[ -f "$SCRIPT_DIR/build.env" ]]; then
                set -a
                source "$SCRIPT_DIR/build.env"
                set +a
                print_info "Configuration loaded from build.env"
            fi
        fi
        return 0
    else
        print_error "Configuration rendering failed"
        return 1
    fi
}

# Configuration command
config_command() {
    local validate_only=false
    local dry_run=false
    
    # Parse additional config arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --validate)
                validate_only=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                print_error "Unknown config option: $1"
                print_info "Available config options: --validate, --dry-run"
                exit 1
                ;;
        esac
    done
    
    if [[ -z "$ENVIRONMENT" ]]; then
        print_error "Environment must be specified with -e/--env"
        print_info "Available environments: dev, staging, prod"
        exit 1
    fi
    
    print_header "Configuration Management - Environment: $ENVIRONMENT"
    
    if [[ "$dry_run" == "true" ]]; then
        python3 "$SCRIPT_DIR/render_config.py" --env "$ENVIRONMENT" --dry-run --verbose
    elif [[ "$validate_only" == "true" ]]; then
        render_config "$ENVIRONMENT" true
    else
        render_config "$ENVIRONMENT" false
    fi
}

# Set environment variables for Docker Compose
set_docker_env() {
    export BUILD_TARGET="$BUILD_TARGET"
    export BUILD_TYPE="$BUILD_TYPE"
    export ENABLE_TESTS="$ENABLE_TESTS"
    export ENABLE_GRPC="$ENABLE_GRPC"
    export ENABLE_REST_API="$ENABLE_REST_API"
    export DEV_BUILD_TARGET="$BUILD_TARGET"
    export DEV_BUILD_TYPE="$BUILD_TYPE"
    export DEV_ENABLE_TESTS="$ENABLE_TESTS"
    export DEV_ENABLE_GRPC="$ENABLE_GRPC"
    export DEV_ENABLE_REST_API="$ENABLE_REST_API"
    
    if [[ -n "$PLATFORM" ]]; then
        export DOCKER_PLATFORM="$PLATFORM"
    fi
}

# Build function
build_target() {
    record_build_start
    print_header "Building OMOP ETL - Target: $BUILD_TARGET"
    
    cd "$PROJECT_ROOT"
    
    # Store command line parameters before configuration rendering
    local cmdline_build_target="$BUILD_TARGET"
    local cmdline_build_type="$BUILD_TYPE"
    local cmdline_enable_tests="$ENABLE_TESTS"
    local cmdline_enable_grpc="$ENABLE_GRPC"
    local cmdline_enable_rest_api="$ENABLE_REST_API"
    
    # Auto-render configuration if environment is specified
    if [[ -n "$ENVIRONMENT" && "$AUTO_RENDER" == "true" ]]; then
        if ! render_config "$ENVIRONMENT"; then
            print_error "Failed to render configuration for $ENVIRONMENT environment"
            exit 1
        fi
        
        # Restore command line parameters if they were specified
        if [[ -n "$cmdline_build_target" ]]; then
            BUILD_TARGET="$cmdline_build_target"
        fi
        if [[ -n "$cmdline_build_type" ]]; then
            BUILD_TYPE="$cmdline_build_type"
        fi
        if [[ -n "$cmdline_enable_tests" ]]; then
            ENABLE_TESTS="$cmdline_enable_tests"
        fi
        if [[ -n "$cmdline_enable_grpc" ]]; then
            ENABLE_GRPC="$cmdline_enable_grpc"
        fi
        if [[ -n "$cmdline_enable_rest_api" ]]; then
            ENABLE_REST_API="$cmdline_enable_rest_api"
        fi
    fi
    
    set_docker_env
    
    # Prepare build arguments
    local build_args=""
    
    if [[ "$CLEAN_BUILD" == "true" ]]; then
        print_info "Cleaning existing containers and images..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans || true
        
        # Remove specific images
        if [[ -n "$REGISTRY" ]]; then
            docker rmi "$REGISTRY/omop-etl-$BUILD_TARGET:$IMAGE_TAG" 2>/dev/null || true
        else
            docker rmi "omop-etl-$BUILD_TARGET:$IMAGE_TAG" 2>/dev/null || true
        fi
        
        docker builder prune -f
        print_success "Cleanup completed"
    fi
    
    # Build the target
    print_info "Building target: $BUILD_TARGET"
    
    local build_success=true
    
    case "$BUILD_TARGET" in
        "api")
            if docker-compose -f "$DOCKER_COMPOSE_FILE" build omop-etl-api; then
                record_build_target "omop-etl-api"
                record_docker_command "omop-etl-api" "docker-compose -f scripts/docker-compose.yml up omop-etl-api"
            else
                build_success=false
                record_build_error "Failed to build omop-etl-api"
            fi
            ;;
        "cli")
            if docker-compose -f "$DOCKER_COMPOSE_FILE" --profile cli build omop-etl-cli; then
                record_build_target "omop-etl-cli"
                record_docker_command "omop-etl-cli" "docker-compose -f scripts/docker-compose.yml --profile cli up omop-etl-cli"
            else
                build_success=false
                record_build_error "Failed to build omop-etl-cli"
            fi
            ;;
        "service")
            if docker-compose -f "$DOCKER_COMPOSE_FILE" --profile service build omop-etl-service; then
                record_build_target "omop-etl-service"
                record_docker_command "omop-etl-service" "docker-compose -f scripts/docker-compose.yml --profile service up omop-etl-service"
            else
                build_success=false
                record_build_error "Failed to build omop-etl-service"
            fi
            ;;
        "all")
            local all_success=true
            
            if docker-compose -f "$DOCKER_COMPOSE_FILE" build; then
                record_build_target "omop-etl-api"
                record_docker_command "omop-etl-api" "docker-compose -f scripts/docker-compose.yml up omop-etl-api"
            else
                all_success=false
                record_build_error "Failed to build omop-etl-api"
            fi
            
            if docker-compose -f "$DOCKER_COMPOSE_FILE" --profile cli build; then
                record_build_target "omop-etl-cli"
                record_docker_command "omop-etl-cli" "docker-compose -f scripts/docker-compose.yml --profile cli up omop-etl-cli"
            else
                all_success=false
                record_build_error "Failed to build omop-etl-cli"
            fi
            
            if docker-compose -f "$DOCKER_COMPOSE_FILE" --profile service build; then
                record_build_target "omop-etl-service"
                record_docker_command "omop-etl-service" "docker-compose -f scripts/docker-compose.yml --profile service up omop-etl-service"
            else
                all_success=false
                record_build_error "Failed to build omop-etl-service"
            fi
            
            build_success=$all_success
            ;;
        *)
            # For library targets, build using development container
            if docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev build omop-etl-dev; then
                record_build_target "omop-etl-dev ($BUILD_TARGET library)"
                record_docker_command "omop-etl-dev" "docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash"
                # Record actual build artifacts for library targets - will be discovered after build
            else
                build_success=false
                record_build_error "Failed to build omop-etl-dev for $BUILD_TARGET"
            fi
            ;;
    esac
    
    # Discover actual build artifacts after build completion
    if [[ "$build_success" == "true" ]]; then
        discover_build_artifacts
    fi
    
    record_build_end
    
    if [[ "$build_success" == "true" ]]; then
        print_success "Build completed successfully for target: $BUILD_TARGET"
    else
        print_error "Build failed for target: $BUILD_TARGET"
        show_build_summary
        exit 1
    fi
    
    # Push if requested
    if [[ "$PUSH_IMAGE" == "true" ]]; then
        if [[ -z "$REGISTRY" ]]; then
            print_error "Registry must be specified when pushing images"
            exit 1
        fi
        
        print_info "Pushing image to registry..."
        # Add push logic here based on target
        print_success "Image pushed successfully"
    fi
    
    # Show build summary
    show_build_summary
}

# Test function
test_target() {
    record_test_start
    print_header "Testing OMOP ETL - Target: $BUILD_TARGET, Type: $TEST_TYPE"
    
    cd "$PROJECT_ROOT"
    
    # Auto-render configuration if environment is specified
    if [[ -n "$ENVIRONMENT" && "$AUTO_RENDER" == "true" ]]; then
        if ! render_config "$ENVIRONMENT"; then
            print_error "Failed to render configuration for $ENVIRONMENT environment"
            exit 1
        fi
    fi
    
    set_docker_env
    export DEV_ENABLE_TESTS=true
    
    # Ensure development environment is running
    print_info "Starting development environment..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev up -d omop-etl-dev
    
    # Function to run commands in dev container
    run_in_container() {
        docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "$1"
    }
    
    # Build the project first
    print_info "Building project..."
    run_in_container "cd /workspace && cmake --preset docker-debug"
    
    # Build only the specific target and its tests
    if [[ "$BUILD_TARGET" == "all" ]]; then
        run_in_container "cd /workspace && cmake --build build/docker-debug -j\$(nproc)"
    else
        # Build the target library first
        run_in_container "cd /workspace && cmake --build build/docker-debug --target omop_${BUILD_TARGET} -j\$(nproc)"
        
        # Build the corresponding test target
        if [[ "$TEST_TYPE" == "unit" ]]; then
            run_in_container "cd /workspace && cmake --build build/docker-debug --target ${BUILD_TARGET}_unit_tests -j\$(nproc)" || print_warning "Unit test target not found for ${BUILD_TARGET}"
        elif [[ "$TEST_TYPE" == "integration" ]]; then
            run_in_container "cd /workspace && cmake --build build/docker-debug --target ${BUILD_TARGET}_integration_tests -j\$(nproc)" || print_warning "Integration test target not found for ${BUILD_TARGET}"
        fi
    fi
    
    # Determine test pattern based on test type and target
    local test_pattern=""
    local ctest_args=""
    
    if [[ "$TEST_TYPE" == "unit" ]]; then
        print_info "Running unit tests for target: $BUILD_TARGET"
        # For unit tests, use the specific target pattern
        if [[ "$BUILD_TARGET" == "all" ]]; then
            test_pattern="unit"
            ctest_args="-L unit"
        else
            test_pattern="test_omop_${BUILD_TARGET}_unit"
            ctest_args="-R ${test_pattern}"
        fi
    elif [[ "$TEST_TYPE" == "integration" ]]; then
        print_info "Running integration tests for target: $BUILD_TARGET"
        # For integration tests, use the integration pattern
        if [[ "$BUILD_TARGET" == "all" ]]; then
            test_pattern="integration"
            ctest_args="-L integration"
        else
            test_pattern="test_omop_${BUILD_TARGET}_integration"
            ctest_args="-R ${test_pattern}"
        fi
    fi
    
    # Run the tests using ctest
    print_info "Running tests with pattern: $test_pattern"
    
    local test_success=true
    local test_output=""
    
    if [[ -n "$TEST_FILTER" ]]; then
        # If filter is specified, run specific test executables with filter
        print_info "Using test filter: $TEST_FILTER"
        
        # Find test executables matching the pattern
        local test_executables=$(run_in_container "cd /workspace/build/docker-debug && find . -name '${test_pattern}' -type f -executable")
        
        if [[ -n "$test_executables" ]]; then
            local test_count=0
            for test_exe in $test_executables; do
                print_info "Running test executable: $test_exe"
                ((test_count++))
                if [[ "$VERBOSE" == "true" ]]; then
                    test_output=$(run_in_container "cd /workspace/build/docker-debug && $test_exe --gtest_filter='$TEST_FILTER' --gtest_verbose" 2>&1)
                else
                    test_output=$(run_in_container "cd /workspace/build/docker-debug && $test_exe --gtest_filter='$TEST_FILTER'" 2>&1)
                fi
                
                if [[ $? -eq 0 ]]; then
                    record_test_result "$test_exe: PASSED"
                else
                    test_success=false
                    record_test_failure "$test_exe: FAILED"
                fi
                
                # Extract test statistics from output
                if [[ "$test_output" =~ ([0-9]+)\ tests?\ from ]]; then
                    local total_tests="${BASH_REMATCH[1]}"
                    record_test_stats "${GREEN}$total_tests${NC} tests executed from $test_exe"
                fi
            done
            record_test_stats "${GREEN}$test_count${NC} test executables processed"
        else
            print_warning "No test executables found matching pattern: $test_pattern"
            record_test_failure "No test executables found for pattern: $test_pattern"
            test_success=false
        fi
    else
        # Run all tests for the pattern using ctest
        if [[ "$VERBOSE" == "true" ]]; then
            test_output=$(run_in_container "cd /workspace/build/docker-debug && ctest $ctest_args --verbose --output-on-failure" 2>&1)
        else
            test_output=$(run_in_container "cd /workspace/build/docker-debug && ctest $ctest_args --output-on-failure" 2>&1)
        fi
        
        if [[ $? -eq 0 ]]; then
            record_test_result "CTest execution: PASSED"
        else
            test_success=false
            record_test_failure "CTest execution: FAILED"
        fi
        
        # Parse ctest output for statistics
        if [[ "$test_output" =~ ([0-9]+)%\ tests\ passed,\ ([0-9]+)\ tests\ failed\ out\ of\ ([0-9]+) ]]; then
            local passed_tests="${BASH_REMATCH[2]}"
            local failed_tests="${BASH_REMATCH[3]}"
            local total_tests="${BASH_REMATCH[4]}"
            record_test_stats "${GREEN}$passed_tests${NC} passed, ${RED}$failed_tests${NC} failed out of ${BLUE}$total_tests${NC} total tests"
        elif [[ "$test_output" =~ ([0-9]+)/([0-9]+)\ Test\ #.*Passed ]]; then
            local passed_count=$(echo "$test_output" | grep -o "Passed" | wc -l | xargs)
            local failed_count=$(echo "$test_output" | grep -o "Failed" | wc -l | xargs)
            local total_count=$((passed_count + failed_count))
            if [[ $total_count -gt 0 ]]; then
                record_test_stats "${GREEN}$passed_count${NC} passed, ${RED}$failed_count${NC} failed out of ${BLUE}$total_count${NC} total tests"
            fi
        fi
    fi
    
    # Record test reports and coverage
    record_test_reports_and_coverage
    
    record_test_end
    
    if [[ "$test_success" == "true" ]]; then
        print_success "Tests completed for target: $BUILD_TARGET ($TEST_TYPE)"
    else
        print_error "Tests failed for target: $BUILD_TARGET ($TEST_TYPE)"
    fi
    
    # Show test summary
    show_test_summary
    
    if [[ "$test_success" != "true" ]]; then
        exit 1
    fi
}

# Development environment function
start_dev_environment() {
    print_header "Starting Development Environment"
    
    cd "$PROJECT_ROOT"
    
    # Auto-render configuration if environment is specified
    if [[ -n "$ENVIRONMENT" && "$AUTO_RENDER" == "true" ]]; then
        if ! render_config "$ENVIRONMENT"; then
            print_error "Failed to render configuration for $ENVIRONMENT environment"
            exit 1
        fi
    else
        # Default to dev environment for development
        if [[ -z "$ENVIRONMENT" ]]; then
            ENVIRONMENT="dev"
            print_info "No environment specified, defaulting to dev"
            if [[ "$AUTO_RENDER" == "true" ]]; then
                render_config "$ENVIRONMENT" || true  # Don't fail if config rendering fails for dev
            fi
        fi
    fi
    
    set_docker_env
    export DEV_ENABLE_TESTS=true
    export DEV_ENABLE_DEV_TOOLS=true
    
    print_info "Starting development services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev up -d
    
    print_success "Development environment started"
    print_info "Available services:"
    print_info "  - Development container: omop-etl-dev"
    print_info "  - PostgreSQL (source): clinical-db:5432"
    print_info "  - PostgreSQL (OMOP): omop-cdm-db:5433"
    
    print_info ""
    print_info "To enter the development container:"
    print_info "  $0 shell"
    print_info ""
    print_info "To build inside the container:"
    print_info "  docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash"
    print_info "  cd /workspace && cmake --preset docker-debug && cmake --build --preset docker-debug"
}

# Shell function
start_shell() {
    print_header "Starting Interactive Shell"
    
    cd "$PROJECT_ROOT"
    set_docker_env
    
    # Ensure dev environment is running
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev up -d omop-etl-dev
    
    print_info "Entering development container..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash
}

# Clean function
clean_build() {
    print_header "Cleaning Build Artifacts"
    
    cd "$PROJECT_ROOT"
    
    print_info "Stopping all containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans || true
    
    print_info "Removing build images..."
    docker images | grep omop-etl | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true
    
    print_info "Cleaning build cache..."
    docker builder prune -f
    
    print_info "Cleaning local build directories..."
    rm -rf "$PROJECT_ROOT/build" || true
    
    print_success "Cleanup completed"
}

# Lint function
run_lint() {
    print_header "Running Static Analysis"
    
    cd "$PROJECT_ROOT"
    set_docker_env
    
    # Ensure dev environment is running
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev up -d omop-etl-dev
    
    print_info "Running clang-tidy..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "cd /workspace && find src -name '*.cpp' -o -name '*.h' | xargs clang-tidy"
    
    print_info "Running cppcheck..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "cd /workspace && cppcheck --enable=all --std=c++20 src/"
    
    print_success "Static analysis completed"
}

# Format function
run_format() {
    print_header "Formatting Code"
    
    cd "$PROJECT_ROOT"
    set_docker_env
    
    # Ensure dev environment is running
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev up -d omop-etl-dev
    
    print_info "Running clang-format..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "cd /workspace && find src -name '*.cpp' -o -name '*.h' | xargs clang-format -i"
    
    print_success "Code formatting completed"
}

# Documentation function
build_docs() {
    record_build_start
    print_header "Building Documentation"
    
    cd "$PROJECT_ROOT"
    set_docker_env
    
    # Ensure dev environment is running
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev up -d omop-etl-dev
    
    print_info "Building documentation with Doxygen..."
    if docker-compose -f "$DOCKER_COMPOSE_FILE" --profile dev exec omop-etl-dev bash -c "cd /workspace && if [ ! -d 'build' ]; then mkdir -p build; fi && cd build && cmake .. -DBUILD_DOCS=ON && cmake --build . --target docs"; then
        record_build_target "documentation"
        discover_build_artifacts
        print_success "Documentation build completed"
    else
        record_build_error "Documentation build failed"
        print_error "Documentation build failed"
    fi
    
    record_build_end
    print_info "Documentation location: $PROJECT_ROOT/docs/sources/html/"
    
    # Try to open the documentation
    if command -v open >/dev/null 2>&1; then
        print_info "Opening documentation in browser..."
        open "$PROJECT_ROOT/docs/sources/html/index.html"
    elif command -v xdg-open >/dev/null 2>&1; then
        print_info "Opening documentation in browser..."
        xdg-open "$PROJECT_ROOT/docs/sources/html/index.html"
    else
        print_info "Please manually open: $PROJECT_ROOT/docs/sources/html/index.html"
    fi
    
    # Show build summary
    show_build_summary
}

# Docker Compose up function
compose_up() {
    print_header "Starting Services"
    
    cd "$PROJECT_ROOT"
    set_docker_env
    
    local compose_args=""
    if [[ "$PROFILES" != "default" ]]; then
        # Split profiles by comma and add --profile for each
        IFS=',' read -ra PROFILE_ARRAY <<< "$PROFILES"
        for profile in "${PROFILE_ARRAY[@]}"; do
            compose_args="$compose_args --profile $profile"
        done
    fi
    
    print_info "Starting services with profiles: $PROFILES"
    docker-compose -f "$DOCKER_COMPOSE_FILE" $compose_args up -d
    
    print_success "Services started successfully"
    print_info "View logs: docker-compose -f scripts/docker-compose.yml logs -f"
}

# Docker Compose down function
compose_down() {
    print_header "Stopping Services"
    
    cd "$PROJECT_ROOT"
    
    print_info "Stopping all services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans
    
    print_success "Services stopped successfully"
}

# Parse command line arguments
if [[ $# -eq 0 ]]; then
    show_usage
    exit 1
fi

COMMAND="$1"
shift

# Validate command
if ! validate_command "$COMMAND"; then
    print_error "Invalid command: $COMMAND"
    print_info "Available commands: ${AVAILABLE_COMMANDS[*]}"
    show_usage
    exit 1
fi

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            BUILD_TARGET="$2"
            if ! validate_target "$BUILD_TARGET"; then
                print_error "Invalid target: $BUILD_TARGET"
                print_info "Available targets: ${AVAILABLE_TARGETS[*]}"
                exit 1
            fi
            shift 2
            ;;
        -b|--build-type)
            BUILD_TYPE="$2"
            if ! validate_build_type "$BUILD_TYPE"; then
                print_error "Invalid build type: $BUILD_TYPE"
                print_info "Available build types: ${AVAILABLE_BUILD_TYPES[*]}"
                exit 1
            fi
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        --tests)
            ENABLE_TESTS=true
            shift
            ;;
        --dev-tools)
            ENABLE_DEV_TOOLS=true
            shift
            ;;
        --grpc)
            ENABLE_GRPC=true
            shift
            ;;
        --rest-api)
            ENABLE_REST_API=true
            shift
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --profiles)
            PROFILES="$2"
            shift 2
            ;;
        --filter)
            TEST_FILTER="$2"
            shift 2
            ;;
        --test-type)
            TEST_TYPE="$2"
            if [[ "$TEST_TYPE" != "unit" && "$TEST_TYPE" != "integration" ]]; then
                print_error "Invalid test type: $TEST_TYPE. Must be unit or integration."
                exit 1
            fi
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "prod" ]]; then
                print_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod"
                exit 1
            fi
            shift 2
            ;;
        --config-file)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --no-auto-render)
            AUTO_RENDER=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Auto-detect platform if not specified
if [[ -z "$PLATFORM" ]] && [[ "$COMMAND" != "detect-arch" ]]; then
    ARCH=$(uname -m)
    case "$ARCH" in
        "x86_64") PLATFORM="linux/amd64" ;;
        "aarch64"|"arm64") PLATFORM="linux/arm64" ;;
        "armv7l") PLATFORM="linux/arm/v7" ;;
        *) PLATFORM="linux/amd64" ;;  # default fallback
    esac
fi

# Execute command
case "$COMMAND" in
    "build")
        build_target
        ;;
    "test")
        test_target
        ;;
    "dev")
        start_dev_environment
        show_overall_summary
        ;;
    "clean")
        clean_build
        ;;
    "lint")
        run_lint
        ;;
    "format")
        run_format
        ;;
    "docs")
        build_docs
        ;;
    "up")
        compose_up
        ;;
    "down")
        compose_down
        ;;
    "shell")
        start_shell
        ;;
    "detect-arch")
        detect_architecture
        ;;
    "config")
        shift  # Remove 'config' from arguments
        config_command "$@"
        ;;
    *)
        print_error "Command not implemented: $COMMAND"
        exit 1
        ;;
esac 