# OMOP ETL Pipeline - Build Configuration Template
# This file is automatically generated from build.env.j2
# Environment: {{ environment }}
# Generated at: {{ timestamp }}

# Build Configuration
BUILD_TARGET={{ build.target }}
BUILD_TYPE={{ build.type }}
ENABLE_TESTS={{ build.enable_tests }}
ENABLE_GRPC={{ build.enable_grpc }}
ENABLE_REST_API={{ build.enable_rest_api }}

# Development Configuration  
DEV_BUILD_TARGET={{ dev.build_target }}
DEV_BUILD_TYPE={{ dev.build_type }}
DEV_ENABLE_TESTS={{ dev.enable_tests }}
DEV_ENABLE_GRPC={{ dev.enable_grpc }}
DEV_ENABLE_REST_API={{ dev.enable_rest_api }}

# Docker Configuration
DOCKER_PLATFORM={{ docker.platform }}
DOCKER_BUILDKIT={{ docker.buildkit }}
COMPOSE_DOCKER_CLI_BUILD={{ docker.compose_cli_build }}

# Database Configuration
POSTGRES_HOST={{ database.postgres.host }}
POSTGRES_PORT={{ database.postgres.port }}
POSTGRES_DB={{ database.postgres.db }}
POSTGRES_USER={{ database.postgres.user }}
POSTGRES_PASSWORD={{ database.postgres.password }}

OMOP_HOST={{ database.omop.host }}
OMOP_PORT={{ database.omop.port }}
OMOP_DB={{ database.omop.db }}
OMOP_USER={{ database.omop.user }}
OMOP_PASSWORD={{ database.omop.password }}

# Security Configuration
JWT_SECRET={{ security.jwt_secret }}
ENCRYPTION_KEY={{ security.encryption_key }}

# Logging Configuration
LOG_LEVEL={{ logging.level }}
LOG_FORMAT={{ logging.format }}
LOG_ROTATION={{ logging.rotation }}

# Paths
OMOP_CONFIG_PATH={{ paths.config }}
OMOP_LOG_PATH={{ paths.logs }}
OMOP_DATA_PATH={{ paths.data }}
OMOP_BACKUP_PATH={{ paths.backup }}

# Performance Configuration
MAX_PARALLEL_JOBS={{ performance.max_parallel_jobs }}
MEMORY_LIMIT={{ performance.memory_limit }}
CPU_LIMIT={{ performance.cpu_limit }}

# Registry Configuration
{% if registry.enabled -%}
REGISTRY={{ registry.url }}
IMAGE_TAG={{ registry.tag }}
REGISTRY_USERNAME={{ registry.username }}
REGISTRY_PASSWORD={{ registry.password }}
{%- endif %}

# Feature Flags
ENABLE_MONITORING={{ features.monitoring }}
ENABLE_METRICS={{ features.metrics }}
ENABLE_PROFILING={{ features.profiling }}
ENABLE_DEBUG={{ features.debug }}

# Environment-specific overrides
{% for key, value in environment_overrides.items() -%}
{{ key }}={{ value }}
{% endfor %} 