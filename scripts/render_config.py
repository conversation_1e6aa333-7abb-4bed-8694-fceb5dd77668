#!/usr/bin/env python3
"""
Configuration Template Renderer for OMOP ETL Pipeline

This script reads environment-specific INI configuration files and renders
the Jinja2 template to generate build.env files for different environments.

Usage:
    python3 scripts/render_config.py --env dev
    python3 scripts/render_config.py --env prod --output custom.env
    python3 scripts/render_config.py --env staging --validate
"""

import argparse
import configparser
import os
import sys
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

try:
    from jinja2 import Environment, FileSystemLoader, select_autoescape
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def install_jinja2():
    """Install Jinja2 if not available"""
    print_warning("Jinja2 not found. Attempting to install...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "jinja2"])
        print_success("Jinja2 installed successfully")
        return True
    except subprocess.CalledProcessError:
        print_error("Failed to install Jinja2. Please install manually: pip install jinja2")
        return False

def expand_environment_variables(value: str) -> str:
    """Expand environment variables in configuration values"""
    if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
        env_var = value[2:-1]
        return os.getenv(env_var, value)
    return value

def parse_ini_file(ini_path: Path) -> Dict[str, Any]:
    """Parse INI configuration file into nested dictionary"""
    config = configparser.ConfigParser()
    config.read(ini_path)
    
    result = {}
    
    for section_name in config.sections():
        # Handle nested sections like database.postgres
        section_parts = section_name.split('.')
        current_dict = result
        
        # Navigate/create nested dictionary structure
        for part in section_parts[:-1]:
            if part not in current_dict:
                current_dict[part] = {}
            current_dict = current_dict[part]
        
        # Add the final section
        final_section = section_parts[-1]
        current_dict[final_section] = {}
        
        # Parse values and convert types
        for key, value in config[section_name].items():
            # Expand environment variables
            expanded_value = expand_environment_variables(value)
            
            # Convert boolean strings
            if expanded_value.lower() in ('true', 'false'):
                expanded_value = expanded_value.lower() == 'true'
            # Convert numeric strings
            elif expanded_value.isdigit():
                expanded_value = int(expanded_value)
            elif '.' in expanded_value and expanded_value.replace('.', '').isdigit():
                expanded_value = float(expanded_value)
            
            current_dict[final_section][key] = expanded_value
    
    return result

def validate_config(config: Dict[str, Any], environment: str) -> bool:
    """Validate configuration for required fields"""
    required_sections = ['build', 'docker', 'database', 'security', 'logging', 'paths']
    missing_sections = []
    
    for section in required_sections:
        if section not in config:
            missing_sections.append(section)
    
    if missing_sections:
        print_error(f"Missing required sections in {environment} config: {missing_sections}")
        return False
    
    # Validate critical database config
    if 'postgres' not in config.get('database', {}):
        print_error("Missing postgres database configuration")
        return False
    
    if 'omop' not in config.get('database', {}):
        print_error("Missing OMOP database configuration")
        return False
    
    # Validate security config for production
    if environment == 'prod':
        security = config.get('security', {})
        if not security.get('jwt_secret') or security.get('jwt_secret', '').startswith('dev-'):
            print_error("Production environment requires secure JWT secret")
            return False
    
    print_success(f"Configuration validation passed for {environment}")
    return True

def render_template(config: Dict[str, Any], environment: str, template_path: Path, output_path: Path):
    """Render Jinja2 template with configuration values"""
    if not JINJA2_AVAILABLE:
        if not install_jinja2():
            sys.exit(1)
    
    # Import jinja2 after installation if needed
    from jinja2 import Environment, FileSystemLoader, select_autoescape
    
    # Set up Jinja2 environment
    template_dir = template_path.parent
    env = Environment(
        loader=FileSystemLoader(template_dir),
        autoescape=select_autoescape(['html', 'xml']),
        trim_blocks=True,
        lstrip_blocks=True
    )
    
    # Load template
    template = env.get_template(template_path.name)
    
    # Add metadata
    config['environment'] = environment
    config['timestamp'] = datetime.now().isoformat()
    
    # Render template
    rendered = template.render(**config)
    
    # Write output
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, 'w') as f:
        f.write(rendered)
    
    print_success(f"Configuration rendered: {output_path}")

def get_script_dir() -> Path:
    """Get the directory containing this script"""
    return Path(__file__).parent.absolute()

def main():
    parser = argparse.ArgumentParser(
        description="Render OMOP ETL build configuration from templates",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python3 scripts/render_config.py --env dev
    python3 scripts/render_config.py --env prod --output build.env
    python3 scripts/render_config.py --env staging --validate
    
Environment Files:
    dev      - Development configuration (scripts/configs/dev.ini)
    staging  - Staging configuration (scripts/configs/staging.ini)  
    prod     - Production configuration (scripts/configs/prod.ini)
        """
    )
    
    parser.add_argument(
        '--env', '--environment',
        required=True,
        choices=['dev', 'staging', 'prod'],
        help='Target environment (dev, staging, prod)'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=Path,
        help='Output file path (default: scripts/build.env)'
    )
    
    parser.add_argument(
        '--template', '-t',
        type=Path,
        help='Template file path (default: scripts/build.env.j2)'
    )
    
    parser.add_argument(
        '--config-dir',
        type=Path,
        help='Configuration directory (default: scripts/configs)'
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Validate configuration without rendering'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show rendered output without writing file'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    
    args = parser.parse_args()
    
    # Set up paths
    script_dir = get_script_dir()
    config_dir = args.config_dir or script_dir / 'configs'
    template_path = args.template or script_dir / 'build.env.j2'
    output_path = args.output or script_dir / 'build.env'
    
    # Configuration file path
    config_file = config_dir / f'{args.env}.ini'
    
    # Validate paths
    if not config_file.exists():
        print_error(f"Configuration file not found: {config_file}")
        sys.exit(1)
    
    if not template_path.exists():
        print_error(f"Template file not found: {template_path}")
        sys.exit(1)
    
    if args.verbose:
        print_info(f"Environment: {args.env}")
        print_info(f"Config file: {config_file}")
        print_info(f"Template: {template_path}")
        print_info(f"Output: {output_path}")
    
    try:
        # Parse configuration
        print_info(f"Loading configuration for {args.env} environment...")
        config = parse_ini_file(config_file)
        
        if args.verbose:
            print_info("Configuration loaded successfully")
        
        # Validate configuration
        if not validate_config(config, args.env):
            sys.exit(1)
        
        # If only validating, exit here
        if args.validate:
            print_success("Configuration validation completed")
            sys.exit(0)
        
        # Render template
        print_info("Rendering configuration template...")
        
        if args.dry_run:
            # For dry run, render to stdout
            if not JINJA2_AVAILABLE:
                if not install_jinja2():
                    sys.exit(1)
            
            # Import jinja2 after installation if needed
            from jinja2 import Environment, FileSystemLoader, select_autoescape
            
            template_dir = template_path.parent
            env = Environment(
                loader=FileSystemLoader(template_dir),
                autoescape=select_autoescape(['html', 'xml']),
                trim_blocks=True,
                lstrip_blocks=True
            )
            
            template = env.get_template(template_path.name)
            config['environment'] = args.env
            config['timestamp'] = datetime.now().isoformat()
            
            rendered = template.render(**config)
            print("\n" + "="*50)
            print("RENDERED CONFIGURATION:")
            print("="*50)
            print(rendered)
            print("="*50)
        else:
            render_template(config, args.env, template_path, output_path)
            print_success(f"Build configuration generated for {args.env} environment")
            print_info(f"Configuration file: {output_path}")
            print_info("You can now run: ./scripts/build.sh build")
    
    except Exception as e:
        print_error(f"Failed to render configuration: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 