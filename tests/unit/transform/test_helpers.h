// tests/unit/transform/test_helpers.h

#pragma once

#include "transform/transformations.h"
#include "transform/transformation_engine.h"
#include "transform/string_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/date_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/custom_transformations.h"
#include "transform/vocabulary_service.h"
#include <memory>

namespace omop::transform::test {

/**
 * @brief Helper class to ensure transformations are registered for tests
 */
class TransformationRegistrar {
public:
    static void RegisterAll() {
        static bool registered = false;
        if (registered) return;
        
        auto& registry = TransformationRegistry::instance();
        
        // Register basic transformations
        registry.register_transformation("direct",
            []() { return std::make_unique<DirectTransformation>(); });
        
        // Register string transformations
        registry.register_transformation("string_manipulation",
            []() { return std::make_unique<StringManipulationTransformation>(); });
        registry.register_transformation("string_pattern_extraction",
            []() { return std::make_unique<StringPatternExtractionTransformation>(); });
        registry.register_transformation("string_concatenation",
            []() { return std::make_unique<StringConcatenationTransformation>(); });
        
        // Register date transformations
        registry.register_transformation("date_transform",
            []() { return std::make_unique<DateTransformation>(); });
        registry.register_transformation("date_calculation",
            []() { return std::make_unique<DateCalculationTransformation>(); });
        registry.register_transformation("date_range_validation",
            []() { return std::make_unique<DateRangeValidationTransformation>(); });
        
        // Register numeric transformations
        registry.register_transformation("numeric_transform",
            []() { return std::make_unique<NumericTransformation>(); });
        registry.register_transformation("advanced_numeric_transform",
            []() { return std::make_unique<AdvancedNumericTransformation>(); });
        registry.register_transformation("numeric_validation",
            []() { return std::make_unique<NumericValidationTransformation>(); });
        
        // Register conditional transformations
        registry.register_transformation("conditional",
            []() { return std::make_unique<ConditionalTransformation>(); });
        registry.register_transformation("advanced_conditional",
            []() { return std::make_unique<AdvancedConditionalTransformation>(); });
        registry.register_transformation("lookup_table",
            []() { return std::make_unique<LookupTableTransformation>(); });
        
        // Register vocabulary transformations (only if vocabulary service is initialized)
        if (VocabularyServiceManager::is_initialized()) {
            registry.register_transformation("vocabulary_mapping",
                []() { return std::make_unique<VocabularyTransformation>(
                    VocabularyServiceManager::instance()); });
            registry.register_transformation("concept_hierarchy",
                []() { return std::make_unique<ConceptHierarchyTransformation>(); });
            registry.register_transformation("domain_mapping",
                []() { return std::make_unique<DomainMappingTransformation>(); });
            registry.register_transformation("concept_relationship",
                []() { return std::make_unique<ConceptRelationshipTransformation>(); });
        }
        
        // Register custom transformations
        CustomTransformationFactory::register_custom_transformations();
        
        registered = true;
    }
};

} // namespace omop::transform::test 