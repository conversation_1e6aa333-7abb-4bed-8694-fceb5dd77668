// tests/unit/transform/transformation_memory_management_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/field_transformations.h"
#include "core/interfaces.h"
#include "test_helpers.h"
#include <memory>
#include <thread>
#include <atomic>

// Conditional Valgrind support
#ifdef HAVE_VALGRIND
#include <valgrind/valgrind.h>
#define RUNNING_ON_VALGRIND RUNNING_ON_VALGRIND
#define VALGRIND_DO_LEAK_CHECK VALGRIND_DO_LEAK_CHECK
#define VALGRIND_COUNT_LEAKS(leaked, dubious, reachable, suppressed) \
    VALGRIND_COUNT_LEAKS(leaked, dubious, reachable, suppressed)
#else
#define RUNNING_ON_VALGRIND 0
#define VALGRIND_DO_LEAK_CHECK
#define VALGRIND_COUNT_LEAKS(leaked, dubious, reachable, suppressed) \
    do { leaked = 0; dubious = 0; reachable = 0; suppressed = 0; } while(0)
#endif

namespace omop::transform::test {

class TransformationMemoryManagementTest : public ::testing::Test {
protected:
    void SetUp() override {
        TransformationRegistrar::RegisterAll();
        context_ = std::make_unique<core::ProcessingContext>();
    }

    void TearDown() override {
        context_.reset();
    }

    // Helper function to check for memory leaks (works with Valgrind)
    bool checkMemoryLeaks() {
        if (RUNNING_ON_VALGRIND) {
            unsigned long leaked;
            [[maybe_unused]] unsigned long dubious, reachable, suppressed;
            VALGRIND_DO_LEAK_CHECK;
            VALGRIND_COUNT_LEAKS(leaked, dubious, reachable, suppressed);
            return leaked == 0;
        }
        return true; // Assume no leaks if not running under Valgrind
    }

    std::unique_ptr<core::ProcessingContext> context_;
};

// Test transformation lifecycle and cleanup
TEST_F(TransformationMemoryManagementTest, TransformationLifecycle) {
    // Create and destroy transformations multiple times
    for (int i = 0; i < 100; ++i) {
        auto transform = TransformationRegistry::instance().create_transformation("string_manipulation");
        
        YAML::Node params;
        params["operation"] = "uppercase";
        transform->configure(params);
        
        std::string input = "test " + std::to_string(i);
        auto result = transform->transform_safe(input, *context_);
        
        EXPECT_TRUE(result.is_success());
        
        // Transform goes out of scope and should be cleaned up
    }
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test engine lifecycle with large configurations
TEST_F(TransformationMemoryManagementTest, EngineLifecycleLargeConfig) {
    // Create and destroy engines with increasingly large configurations
    for (int size = 10; size <= 1000; size *= 10) {
        auto engine = std::make_unique<TransformationEngine>();
        
        // Create large configuration
        std::unordered_map<std::string, std::any> config;
        config["table_name"] = std::string("test_table");
        config["test_mode"] = true;
        
        // Add many custom parameters
        for (int i = 0; i < size; ++i) {
            config["param_" + std::to_string(i)] = std::string("value_" + std::to_string(i));
        }
        
        engine->initialize(config, *context_);
        
        // Process some records
        for (int j = 0; j < 10; ++j) {
            core::Record record;
            record.setField("field1", std::string("value1"));
            record.setField("field2", j);
            
            auto result = engine->transform(record, *context_);
            EXPECT_TRUE(result.has_value() || true); // May or may not transform
        }
        
        // Engine goes out of scope and should clean up all resources
    }
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test record batch memory management
TEST_F(TransformationMemoryManagementTest, RecordBatchMemoryManagement) {
    // Test with very large records
    const size_t num_fields = 1000;
    const size_t batch_size = 100;
    
    for (int iteration = 0; iteration < 10; ++iteration) {
        core::RecordBatch batch;
        batch.reserve(batch_size);
        
        // Create large records
        for (size_t i = 0; i < batch_size; ++i) {
            core::Record record;
            
            // Add many fields with large values
            for (size_t j = 0; j < num_fields; ++j) {
                std::string field_name = "field_" + std::to_string(j);
                std::string large_value(1000, 'X'); // 1KB per field
                record.setField(field_name, large_value);
            }
            
            batch.addRecord(std::move(record));
        }
        
        // Transform the batch
        auto engine = std::make_unique<TransformationEngine>();
        std::unordered_map<std::string, std::any> config;
        config["table_name"] = std::string("test");
        config["test_mode"] = true;
        engine->initialize(config, *context_);
        
        auto result_batch = engine->transform_batch(batch, *context_);
        
        // Clear batches
        batch.clear();
        result_batch.clear();
    }
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test transformation cache memory bounds
TEST_F(TransformationMemoryManagementTest, TransformationCacheMemoryBounds) {
    const size_t cache_size = 100;
    TransformationCache cache(cache_size);
    
    // Fill cache beyond its limit
    for (size_t i = 0; i < cache_size * 10; ++i) {
        TransformationCache::CacheKey key = "key_" + std::to_string(i);
        TransformationCache::CacheValue value = std::string("value_" + std::to_string(i));
        
        cache.put(key, value);
    }
    
    // Cache should maintain its size limit
    auto stats = cache.get_stats();
    EXPECT_LE(stats.size, cache_size);
    
    // Access pattern to test LRU eviction
    for (size_t i = 0; i < cache_size; ++i) {
        TransformationCache::CacheKey key = "key_" + std::to_string(i);
        auto value = cache.get(key);
        // Early keys might have been evicted
    }
    
    // Clear cache and verify cleanup
    cache.clear();
    stats = cache.get_stats();
    EXPECT_EQ(0, stats.size);
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test concurrent transformation memory safety
TEST_F(TransformationMemoryManagementTest, ConcurrentTransformationMemory) {
    const int num_threads = 20;
    const int operations_per_thread = 1000;
    std::atomic<int> total_operations{0};
    
    auto thread_func = [&total_operations]() {
        auto context = std::make_unique<core::ProcessingContext>();
        
        for (int i = 0; i < operations_per_thread; ++i) {
            // Create different types of transformations
            std::string transform_type;
            switch (i % 5) {
                case 0: transform_type = "string_manipulation"; break;
                case 1: transform_type = "numeric_transform"; break;
                case 2: transform_type = "date_transform"; break;
                case 3: transform_type = "conditional"; break;
                case 4: transform_type = "direct"; break;
            }
            
            auto transform = TransformationRegistry::instance().create_transformation(transform_type);
            
            // Configure based on type
            YAML::Node params;
            if (transform_type == "string_manipulation") {
                params["operation"] = "uppercase";
            } else if (transform_type == "numeric_transform") {
                params["operation"] = "multiply";
                params["operand"] = 2.0;
            }
            transform->configure(params);
            
            // Transform some data
            std::any input = std::string("test_" + std::to_string(i));
            try {
                auto result = transform->transform_safe(input, *context);
                if (result.is_success()) {
                    total_operations++;
                }
            } catch (...) {
                // Ignore errors in stress test
            }
        }
    };
    
    // Launch threads
    std::vector<std::thread> threads;
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(thread_func);
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    // Should have completed many operations without crashes
    EXPECT_GT(total_operations.load(), 0);
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test exception safety and resource cleanup
TEST_F(TransformationMemoryManagementTest, ExceptionSafetyResourceCleanup) {
    // Test transformation that throws during processing
    class ThrowingTransformation : public FieldTransformation {
    public:
        std::any transform(const std::any& input,
                          core::ProcessingContext& context) override {
            throw std::runtime_error("Intentional error for testing");
        }
        
        bool validate_input(const std::any& input) const override {
            return true;
        }
        
        std::string get_type() const override { return "throwing"; }
        
        void configure(const YAML::Node& params) override {}
    };
    
    // Register throwing transformation
    TransformationRegistry::instance().register_transformation("throwing",
        []() { return std::make_unique<ThrowingTransformation>(); });
    
    // Test exception handling in various contexts
    for (int i = 0; i < 100; ++i) {
        try {
            auto transform = TransformationRegistry::instance().create_transformation("throwing");
            std::string input = "test";
            transform->transform(input, *context_);
            FAIL() << "Should have thrown";
        } catch (const std::runtime_error& e) {
            // Expected exception
            EXPECT_STREQ("Intentional error for testing", e.what());
        }
    }
    
    // Test in transformation chain
    for (int i = 0; i < 50; ++i) {
        TransformationChain chain;
        
        // Add normal transformation
        auto normal = std::make_unique<DirectTransformation>();
        chain.add_transformation(std::move(normal));
        
        // Add throwing transformation
        auto throwing = std::make_unique<ThrowingTransformation>();
        chain.add_transformation(std::move(throwing));
        
        try {
            std::string input = "test";
            chain.apply(input, *context_);
            FAIL() << "Should have thrown";
        } catch (const common::TransformationException& e) {
            // Expected - chain should wrap the error
        } catch (const std::exception& e) {
            // Handle cases where chain doesn't wrap the exception
            EXPECT_STREQ("Intentional error for testing", e.what());
        }
    }
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test field metadata memory management
TEST_F(TransformationMemoryManagementTest, FieldMetadataMemoryManagement) {
    // Create records with extensive metadata
    for (int iteration = 0; iteration < 100; ++iteration) {
        core::Record record;
        
        // Add fields with metadata
        for (int i = 0; i < 100; ++i) {
            std::string field_name = "field_" + std::to_string(i);
            record.setField(field_name, std::string("value_" + std::to_string(i)));
            
            // Add field metadata
            core::Record::FieldMetadata metadata;
            metadata.name = field_name;
            metadata.data_type = "string";
            metadata.is_nullable = true;
            metadata.source_column = "source_" + field_name;
            metadata.description = "Long description for field " + std::to_string(i) + 
                                 " with additional details to increase memory usage";
            
            record.setFieldMetadata(field_name, metadata);
        }
        
        // Add record metadata
        core::Record::RecordMetadata rec_metadata;
        rec_metadata.source_table = "large_source_table_with_long_name";
        rec_metadata.target_table = "large_target_table_with_long_name";
        rec_metadata.source_row_number = iteration * 1000;
        rec_metadata.extraction_time = std::chrono::system_clock::now();
        rec_metadata.record_id = "RECORD_" + std::to_string(iteration) + "_WITH_LONG_ID";
        
        // Add custom metadata
        for (int j = 0; j < 50; ++j) {
            rec_metadata.custom["key_" + std::to_string(j)] = 
                "Custom metadata value " + std::to_string(j) + " with long content";
        }
        
        record.setMetadata(rec_metadata);
        
        // Copy and merge records to test memory handling
        core::Record copy = record;
        core::Record merged;
        merged.merge(record, true);
        merged.merge(copy, false);
        
        // Select subset of fields
        std::vector<std::string> field_names;
        for (int k = 0; k < 10; ++k) {
            field_names.push_back("field_" + std::to_string(k));
        }
        auto subset = record.selectFields(field_names);
        
        // All records go out of scope and should be cleaned up
    }
    
    EXPECT_TRUE(checkMemoryLeaks());
}

// Test vocabulary service memory management
TEST_F(TransformationMemoryManagementTest, VocabularyServiceMemoryManagement) {
    // Initialize vocabulary service if not already done
    if (!VocabularyServiceManager::is_initialized()) {
        // Create mock connection that simulates large vocabulary data
        class LargeVocabMockConnection : public extract::IDatabaseConnection {
        public:
            void connect(const ConnectionParams&) override {}
            void disconnect() override {}
            bool is_connected() const override { return true; }
            
            std::unique_ptr<extract::IResultSet> execute_query(const std::string& query) override {
                class LargeResult : public extract::IResultSet {
                private:
                    int current_row_{-1};
                    const int total_rows_{1000};
                    
                public:
                    bool next() override {
                        return ++current_row_ < total_rows_;
                    }
                    
                    std::any get_value(size_t index) const override {
                        switch (index) {
                            case 0: return current_row_ + 1000000; // concept_id
                            case 1: return std::string("Concept " + std::to_string(current_row_));
                            case 2: return std::string("Domain");
                            case 3: return std::string("Vocabulary");
                            case 4: return std::string("Class");
                            case 5: return std::string("S");
                            case 6: return std::string("CODE" + std::to_string(current_row_));
                            default: return std::any{};
                        }
                    }
                    
                    std::any get_value(const std::string&) const override {
                        return std::any{};
                    }
                    
                    bool is_null(size_t) const override { return false; }
                    bool is_null(const std::string&) const override { return false; }
                    size_t column_count() const override { return 7; }
                    std::string column_name(size_t) const override { return ""; }
                    std::string column_type(size_t) const override { return ""; }
                    std::vector<std::string> get_column_names() const override { return {}; }
                    core::Record to_record() const override { return core::Record{}; }
                };
                
                return std::make_unique<LargeResult>();
            }
            
            size_t execute_update(const std::string&) override { return 0; }
            std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string&) override {
                return nullptr;
            }
            void begin_transaction() override {}
            void commit() override {}
            void rollback() override {}
            bool in_transaction() const override { return false; }
            std::string get_database_type() const override { return "large_mock"; }
            std::string get_version() const override { return "1.0"; }
            void set_query_timeout(int) override {}
            bool table_exists(const std::string&, const std::string& = "") const override {
                return true;
            }
        };
        
        VocabularyServiceManager::initialize(
            std::make_unique<LargeVocabMockConnection>(), 100); // Small cache
    }
    
    auto& vocab_service = VocabularyServiceManager::instance();
    
    // Add many custom mappings
    for (int i = 0; i < 10000; ++i) {
        VocabularyMapping mapping;
        mapping.source_value = "source_" + std::to_string(i);
        mapping.source_vocabulary = "TestVocab";
        mapping.target_concept_id = 1000000 + i;
        mapping.mapping_confidence = 1.0f;
        mapping.mapping_type = "custom";
        
        vocab_service.add_mapping(mapping);
    }
    
    // Perform many lookups to stress the cache
    for (int i = 0; i < 5000; ++i) {
        [[maybe_unused]] auto concept_result = vocab_service.get_concept(1000000 + (i % 1000));
        [[maybe_unused]] auto concept_id = vocab_service.map_to_concept_id("source_" + std::to_string(i % 1000), "TestVocab");
    }
    
    // Clear cache multiple times
    for (int i = 0; i < 10; ++i) {
        vocab_service.clear_cache();
        
        // Do more lookups
        for (int j = 0; j < 100; ++j) {
            [[maybe_unused]] auto concept_result = vocab_service.get_concept(1000000 + j);
        }
    }
    
    EXPECT_TRUE(checkMemoryLeaks());
}

} // namespace omop::transform::test