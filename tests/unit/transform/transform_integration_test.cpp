// tests/unit/transform/transform_integration_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/transformations.h"
#include "transform/string_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/date_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/custom_transformations.h"
#include "transform/field_transformations.h"
#include "transform/vocabulary_service.h"
#include "extract/database_connector.h"
#include "common/configuration.h"
#include "core/interfaces.h"
#include <memory>
#include <filesystem>
#include <string>
#include <any>
#include <sstream>
#include <fstream>
#include "test_helpers.h"

namespace omop::transform::test {

// Mock database connection for integration tests
class IntegrationMockDatabaseConnection : public omop::extract::IDatabaseConnection {
public:
    void connect(const omop::extract::IDatabaseConnection::ConnectionParams& params) override { connected_ = true; }
    void disconnect() override { connected_ = false; }
    bool is_connected() const override { return connected_; }
    std::unique_ptr<omop::extract::IResultSet> execute_query(const std::string& sql) override { return std::make_unique<MockResult>(); }
    size_t execute_update(const std::string& sql) override { return 0; }
    std::unique_ptr<omop::extract::IPreparedStatement> prepare_statement(const std::string& sql) override { return std::make_unique<MockStatement>(); }
    void begin_transaction() override {}
    void commit() override {}
    void rollback() override {}
    bool in_transaction() const override { return false; }
    std::string get_database_type() const override { return "integration_mock"; }
    std::string get_version() const override { return "1.0"; }
    void set_query_timeout(int seconds) override {}
    bool table_exists(const std::string& table_name, const std::string& schema = "") const override { return true; }
private:
    class MockResult : public omop::extract::IResultSet {
    public:
        bool next() override { return false; }
        std::any get_value(size_t index) const override { return std::any{}; }
        std::any get_value(const std::string& column_name) const override { return std::any{}; }
        bool is_null(size_t index) const override { return true; }
        bool is_null(const std::string& column_name) const override { return true; }
        size_t column_count() const override { return 0; }
        std::string column_name(size_t index) const override { return ""; }
        std::string column_type(size_t index) const override { return ""; }
        std::vector<std::string> get_column_names() const override { return {}; }
        core::Record to_record() const override { return core::Record{}; }
    };
    class MockStatement : public omop::extract::IPreparedStatement {
    public:
        void bind(size_t index, const std::any& value) override {}
        std::unique_ptr<omop::extract::IResultSet> execute_query() override { return std::make_unique<MockResult>(); }
        size_t execute_update() override { return 0; }
        void clear_parameters() override {}
    };
    bool connected_{false};
};

class TransformIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();

        // Initialize vocabulary service
        if (!VocabularyServiceManager::is_initialized()) {
            VocabularyServiceManager::initialize(
                std::make_unique<IntegrationMockDatabaseConnection>(), 1000);
        }

        // Set up mock configuration
        setup_mock_configuration();

        // Create transformation engine
        engine_ = std::make_unique<TransformationEngine>();
        
        // Ensure all transformations are registered
        TransformationRegistrar::RegisterAll();
    }

    void TearDown() override {
        engine_.reset();
        context_.reset();
        if (VocabularyServiceManager::is_initialized()) {
            VocabularyServiceManager::reset();
        }
    }

    void setup_mock_configuration() {
        // Create comprehensive YAML configuration that includes both person and patient table mappings
        YAML::Node config;

        // Add required source_database section
        YAML::Node source_db;
        source_db["host"] = "localhost";
        source_db["port"] = 5432;
        source_db["database"] = "test_source";
        source_db["username"] = "test_user";
        source_db["password"] = "test_pass";
        config["source_database"] = source_db;

        // Add required target_database section
        YAML::Node target_db;
        target_db["host"] = "localhost";
        target_db["port"] = 5432;
        target_db["database"] = "test_target";
        target_db["username"] = "test_user";
        target_db["password"] = "test_pass";
        config["target_database"] = target_db;

        // Add vocabulary mappings for the Gender vocabulary
        YAML::Node vocab_mappings;
        YAML::Node gender_vocab;
        gender_vocab["source_vocabulary"] = "Gender";
        gender_vocab["target_vocabulary"] = "Gender";
        vocab_mappings["Gender"] = gender_vocab;
        config["vocabulary_mappings"] = vocab_mappings;

        // Set up table mappings for both patient and person tables
        YAML::Node mappings;

        // Patient table mapping
        YAML::Node patient_mapping;
        patient_mapping["source_table"] = "patient";
        patient_mapping["target_table"] = "person";

        // Create comprehensive table mapping for person table using YAML configuration
        YAML::Node person_mapping_yaml;
        person_mapping_yaml["source_table"] = "patient_demographics";
        person_mapping_yaml["target_table"] = "person";

        // Create transformation rules as YAML nodes
        YAML::Node transformations;

        // Direct mapping for patient_id
        YAML::Node direct_rule;
        direct_rule["source_column"] = "patient_id";
        direct_rule["target_column"] = "person_id";
        direct_rule["type"] = "direct";
        transformations.push_back(direct_rule);

        // Date transformation for birth_date
        YAML::Node birth_rule;
        birth_rule["source_column"] = "birth_date";
        birth_rule["target_column"] = "birth_datetime";
        birth_rule["type"] = "date_transform";
        birth_rule["parameters"]["format"] = "%Y-%m-%d";
        birth_rule["parameters"]["output_format"] = "%Y-%m-%d %H:%M:%S";
        birth_rule["parameters"]["add_time"] = true;
        birth_rule["parameters"]["default_time"] = "00:00:00";
        transformations.push_back(birth_rule);

        // Vocabulary mapping for gender
        YAML::Node gender_rule;
        gender_rule["source_column"] = "gender";
        gender_rule["target_column"] = "gender_concept_id";
        gender_rule["type"] = "vocabulary_mapping";
        gender_rule["vocabulary"] = "Gender";
        gender_rule["default_value"] = 0;
        transformations.push_back(gender_rule);

        // String concatenation for person_source_value
        YAML::Node name_rule;
        name_rule["source_columns"].push_back("first_name");
        name_rule["source_columns"].push_back("last_name");
        name_rule["target_column"] = "person_source_value";
        name_rule["type"] = "string_concatenation";
        name_rule["parameters"]["separator"] = " ";
        transformations.push_back(name_rule);

        // Numeric transformation for year_of_birth
        YAML::Node year_rule;
        year_rule["source_column"] = "year_of_birth";
        year_rule["target_column"] = "year_of_birth";
        year_rule["type"] = "numeric_transform";
        year_rule["parameters"]["operation"] = "round";
        year_rule["parameters"]["min_value"] = 1900;
        year_rule["parameters"]["max_value"] = 2100;
        transformations.push_back(year_rule);

        // Conditional transformation for race
        YAML::Node race_rule;
        race_rule["source_column"] = "race_code";
        race_rule["target_column"] = "race_concept_id";
        race_rule["type"] = "conditional";
        YAML::Node cond1;
        cond1["operator"] = "equals";
        cond1["value"] = "W";
        cond1["then"] = "8527";
        race_rule["parameters"]["conditions"].push_back(cond1);
        YAML::Node cond2;
        cond2["operator"] = "equals";
        cond2["value"] = "B";
        cond2["then"] = "8516";
        race_rule["parameters"]["conditions"].push_back(cond2);
        race_rule["parameters"]["default"] = "0";
        transformations.push_back(race_rule);

        person_mapping_yaml["transformations"] = transformations;

        // Add filters
        YAML::Node filters;
        YAML::Node filter1;
        filter1["field"] = "patient_id";
        filter1["operator"] = "not_null";
        filters.push_back(filter1);
        YAML::Node filter2;
        filter2["field"] = "birth_date";
        filter2["operator"] = "not_null";
        filters.push_back(filter2);
        person_mapping_yaml["filters"] = filters;

        // Add validations
        YAML::Node validations;
        YAML::Node val1;
        val1["field"] = "person_id";
        val1["type"] = "required";
        validations.push_back(val1);
        YAML::Node val2;
        val2["field"] = "year_of_birth";
        val2["type"] = "range";
        val2["min"] = 1900;
        val2["max"] = 2100;
        validations.push_back(val2);
        person_mapping_yaml["validations"] = validations;

        // Set up patient transformations (similar to person but simplified for the failing tests)
        YAML::Node patient_transformations;

        YAML::Node patient_rule1;
        patient_rule1["source_column"] = "patient_id";
        patient_rule1["target_column"] = "person_id";
        patient_rule1["type"] = "direct";
        patient_transformations.push_back(patient_rule1);

        YAML::Node patient_rule2;
        patient_rule2["source_column"] = "birth_date";
        patient_rule2["target_column"] = "birth_datetime";
        patient_rule2["type"] = "date_transform";
        YAML::Node patient_date_params;
        patient_date_params["format"] = "%Y-%m-%d";
        patient_date_params["output_format"] = "%Y-%m-%d %H:%M:%S";
        patient_date_params["add_time"] = true;
        patient_rule2["parameters"] = patient_date_params;
        patient_transformations.push_back(patient_rule2);

        YAML::Node patient_rule3;
        patient_rule3["source_column"] = "gender";
        patient_rule3["target_column"] = "gender_concept_id";
        patient_rule3["type"] = "vocabulary_mapping";
        patient_rule3["vocabulary"] = "Gender";
        patient_rule3["default_value"] = 0;
        patient_transformations.push_back(patient_rule3);

        patient_mapping["transformations"] = patient_transformations;

        // Add filters for patient table
        YAML::Node patient_filters;
        YAML::Node patient_filter1;
        patient_filter1["field"] = "patient_id";
        patient_filter1["operator"] = "not_null";
        patient_filters.push_back(patient_filter1);
        patient_mapping["filters"] = patient_filters;

        // Add validations for patient table
        YAML::Node patient_validations;
        YAML::Node patient_val1;
        patient_val1["field"] = "person_id";
        patient_val1["type"] = "required";
        patient_validations.push_back(patient_val1);
        patient_mapping["validations"] = patient_validations;

        // Add both mappings to config
        mappings["patient"] = patient_mapping;
        mappings["person"] = person_mapping_yaml;
        config["table_mappings"] = mappings;

        // Load configuration into the singleton
        auto& config_mgr = common::Config::instance();
        config_mgr.load_config_from_string(YAML::Dump(config));

        // Create TableMapping from YAML (keep for backward compatibility)
        mapping_ = common::TableMapping(person_mapping_yaml);
    }

    std::unique_ptr<core::ProcessingContext> context_;
    std::unique_ptr<TransformationEngine> engine_;
    common::TableMapping mapping_;
};

// Simple test class for registry testing
class RegistryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // No complex setup needed
    }

    void TearDown() override {
        // No cleanup needed
    }
};

// Test to check what transformations are registered
TEST_F(RegistryTest, CheckRegisteredTransformations) {
    auto& registry = TransformationRegistry::instance();
    auto types = registry.get_registered_types();

    // Print all registered types for debugging
    std::cout << "Registered transformation types:" << std::endl;
    for (const auto& type : types) {
        std::cout << "  - " << type << std::endl;
    }

    // Check if basic transformations are registered
    EXPECT_TRUE(registry.has_transformation("string_manipulation"))
        << "string_manipulation not registered";
    EXPECT_TRUE(registry.has_transformation("string_pattern_extraction"))
        << "string_pattern_extraction not registered";
    EXPECT_TRUE(registry.has_transformation("date_transform"))
        << "date_transform not registered";
    EXPECT_TRUE(registry.has_transformation("numeric_transform"))
        << "numeric_transform not registered";
    EXPECT_TRUE(registry.has_transformation("conditional"))
        << "conditional not registered";
}

// Test to manually register all transformations
TEST_F(RegistryTest, ManualRegisterAllTransformations) {
    auto& registry = TransformationRegistry::instance();

    // Register string transformations
    registry.register_transformation("string_manipulation",
        []() { return std::make_unique<StringManipulationTransformation>(); });

    registry.register_transformation("string_pattern_extraction",
        []() { return std::make_unique<StringPatternExtractionTransformation>(); });

    // Register date transformations
    registry.register_transformation("date_calculation",
        []() { return std::make_unique<DateCalculationTransformation>(); });

    registry.register_transformation("date_range_validation",
        []() { return std::make_unique<DateRangeValidationTransformation>(); });

    // Register numeric transformations
    registry.register_transformation("advanced_numeric_transform",
        []() { return std::make_unique<AdvancedNumericTransformation>(); });

    registry.register_transformation("numeric_validation",
        []() { return std::make_unique<NumericValidationTransformation>(); });

    // Register conditional transformations
    registry.register_transformation("advanced_conditional",
        []() { return std::make_unique<AdvancedConditionalTransformation>(); });

    registry.register_transformation("lookup_table",
        []() { return std::make_unique<LookupTableTransformation>(); });

    // Register vocabulary transformations
    registry.register_transformation("concept_hierarchy",
        []() { return std::make_unique<ConceptHierarchyTransformation>(); });

    registry.register_transformation("domain_mapping",
        []() { return std::make_unique<DomainMappingTransformation>(); });

    registry.register_transformation("concept_relationship",
        []() { return std::make_unique<ConceptRelationshipTransformation>(); });

    // Register custom transformations
    registry.register_transformation("javascript",
        []() { return std::make_unique<JavaScriptTransformation>(); });

    registry.register_transformation("sql",
        []() { return std::make_unique<SQLTransformation>(); });

    registry.register_transformation("plugin",
        []() { return std::make_unique<PluginTransformation>(); });

    registry.register_transformation("python",
        []() { return std::make_unique<PythonTransformation>(); });

    registry.register_transformation("composite",
        []() { return std::make_unique<CompositeTransformation>(); });

    // Now check if all transformations are registered
    auto types = registry.get_registered_types();
    std::cout << "After manual registration, transformation types:" << std::endl;
    for (const auto& type : types) {
        std::cout << "  - " << type << std::endl;
    }

    // Test that we can create transformations
    EXPECT_TRUE(registry.has_transformation("string_manipulation"));
    auto transform = registry.create_transformation("string_manipulation");
    EXPECT_NE(nullptr, transform);
    EXPECT_EQ("string_manipulation", transform->get_type());
}

// Test complete person record transformation
TEST_F(TransformIntegrationTest, CompletePersonTransformation) {
    // Initialize engine with proper configuration
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    // Create input record
    core::Record input;
    input.setField("patient_id", 12345);
    input.setField("birth_date", std::string("1985-03-15"));
    input.setField("gender", std::string("M"));
    input.setField("year_of_birth", 1985.0);
    input.setField("race_code", std::string("W"));

    // Transform using the engine
    auto result = engine_->transform(input, *context_);
    
    // Verify output
    ASSERT_TRUE(result.has_value());
    auto output = result.value();

    EXPECT_EQ(12345, std::any_cast<int>(output.getField("person_id")));
    
    // Check transformed fields exist
    EXPECT_TRUE(output.hasField("birth_datetime"));
    EXPECT_TRUE(output.hasField("gender_concept_id"));
}

// Test error propagation with proper exception handling
TEST_F(TransformIntegrationTest, ErrorPropagationWithContext) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    // Set error threshold
    context_->set_error_threshold(0.1); // 10% error threshold
    
    // Process records with errors
    for (int i = 0; i < 10; ++i) {
        core::Record input;
        input.setField("patient_id", i);
        
        if (i % 3 == 0) {
            // Invalid date that will cause error
            input.setField("birth_date", std::string("invalid-date"));
        } else {
            input.setField("birth_date", std::string("1990-01-01"));
        }
        input.setField("gender", std::string("M"));
        
        try {
            engine_->transform(input, *context_);
        } catch (const common::TransformationException& e) {
            // Error already counted by the engine
        }
    }
    
    // Check if error threshold was exceeded
    EXPECT_TRUE(context_->is_error_threshold_exceeded());
}

// Test batch transformation with mixed valid/invalid records
TEST_F(TransformIntegrationTest, BatchTransformationMixedRecords) {
    // Initialize engine with proper configuration
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    core::RecordBatch input_batch;

    // Valid record
    core::Record record1;
    record1.setField("patient_id", 1);
    record1.setField("birth_date", std::string("1990-01-15"));
    record1.setField("gender", std::string("F"));
    input_batch.addRecord(record1);

    // Invalid record - missing required field
    core::Record record2;
    record2.setField("birth_date", std::string("1985-05-20"));
    input_batch.addRecord(record2);

    // Invalid record - invalid date
    core::Record record3;
    record3.setField("patient_id", 3);
    record3.setField("birth_date", std::string("invalid-date"));
    input_batch.addRecord(record3);

    // Valid record
    core::Record record4;
    record4.setField("patient_id", 4);
    record4.setField("birth_date", std::string("2000-12-31"));
    record4.setField("gender", std::string("M"));
    input_batch.addRecord(record4);

    // Process batch
    auto result_batch = engine_->transform_batch(input_batch, *context_);
    
    // Verify results
    EXPECT_EQ(2, result_batch.size()); // Only valid records
    
    // Check that filtered records were counted
    auto stats = engine_->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["records_filtered"]), 0);
}

// Test transformation chain with proper error handling
TEST_F(TransformIntegrationTest, TransformationChainErrorHandling) {
    // Initialize engine
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);
    
    // Create a record that will fail at different stages
    core::Record input;
    input.setField("patient_id", 12345);
    input.setField("birth_date", std::string("1990-01-15"));
    input.setField("gender", std::string("X")); // Invalid gender code
    
    auto result = engine_->transform(input, *context_);
    
    // Should still transform, but gender_concept_id should use default
    ASSERT_TRUE(result.has_value());
    auto output = result.value();
    
    // Check that gender was mapped to default value (0)
    if (output.hasField("gender_concept_id")) {
        EXPECT_EQ(0, std::any_cast<int>(output.getField("gender_concept_id")));
    }
}

// Test conditional transformation in engine context
TEST_F(TransformIntegrationTest, ConditionalTransformationInEngine) {
    // Add a custom conditional transformation to the configuration
    YAML::Node custom_config;
    custom_config["table_mappings"]["test_table"]["source_table"] = "test_source";
    custom_config["table_mappings"]["test_table"]["target_table"] = "test_target";
    
    // Add conditional transformation rule
    YAML::Node rule;
    rule["source_column"] = "status_code";
    rule["target_column"] = "status_concept_id";
    rule["type"] = "conditional";
    
    YAML::Node condition1;
    condition1["operator"] = "equals";
    condition1["value"] = "A";
    condition1["then"] = "4188539"; // Active
    
    YAML::Node condition2;
    condition2["operator"] = "equals";
    condition2["value"] = "I";
    condition2["then"] = "4188540"; // Inactive
    
    rule["parameters"]["conditions"].push_back(condition1);
    rule["parameters"]["conditions"].push_back(condition2);
    rule["parameters"]["default"] = "0";
    
    custom_config["table_mappings"]["test_table"]["transformations"].push_back(rule);
    
    // Would need to inject this configuration into the engine
    // For now, test the concept
    EXPECT_TRUE(true); // Placeholder assertion
}

// Test transformation with custom transformations
TEST_F(TransformIntegrationTest, CustomTransformations) {
    // Register a custom transformation
    TransformationRegistry::instance().register_transformation("test_custom",
        []() {
            class TestCustomTransformation : public FieldTransformation {
            public:
                std::any transform(const std::any& input, core::ProcessingContext& context) override {
                    if (input.type() == typeid(std::string)) {
                        std::string val = std::any_cast<std::string>(input);
                        return std::string("CUSTOM_" + val);
                    }
                    return input;
                }

                bool validate_input(const std::any& input) const override {
                    return input.has_value();
                }

                std::string get_type() const override { return "test_custom"; }

                void configure(const YAML::Node& params) override {}
            };

            return std::make_unique<TestCustomTransformation>();
        });

    // Use custom transformation
    auto transform = TransformationRegistry::instance().create_transformation("test_custom");

    std::string input = "value";
    auto result = transform->transform(input, *context_);

    EXPECT_EQ("CUSTOM_value", std::any_cast<std::string>(result));
}

// Test complex transformation chain
TEST_F(TransformIntegrationTest, ComplexTransformationChain) {
    // Create a chain of transformations
    auto input = std::string("  hello WORLD 123  ");

    // Step 1: Trim
    auto trim = TransformationRegistry::instance().create_transformation("string_manipulation");
    YAML::Node trim_params;
    trim_params["operation"] = std::string("trim");
    trim->configure(trim_params);
    auto step1 = trim->transform(input, *context_);
    std::cout << "step1 type: " << step1.type().name() << std::endl;
    std::string step1_val;
    if (step1.type() == typeid(TransformationResult)) {
        step1_val = std::any_cast<std::string>(std::any_cast<TransformationResult>(step1).value);
    } else if (step1.type() == typeid(std::string)) {
        step1_val = std::any_cast<std::string>(step1);
    } else {
        FAIL() << "Unexpected type for step1: " << step1.type().name();
    }

    // Step 2: Lowercase
    auto lower = TransformationRegistry::instance().create_transformation("string_manipulation");
    YAML::Node lower_params;
    lower_params["operation"] = std::string("lowercase");
    lower->configure(lower_params);
    auto step2 = lower->transform(step1_val, *context_);
    std::cout << "step2 type: " << step2.type().name() << std::endl;
    std::string step2_val;
    if (step2.type() == typeid(TransformationResult)) {
        step2_val = std::any_cast<std::string>(std::any_cast<TransformationResult>(step2).value);
    } else if (step2.type() == typeid(std::string)) {
        step2_val = std::any_cast<std::string>(step2);
    } else {
        FAIL() << "Unexpected type for step2: " << step2.type().name();
    }

    // Step 3: Extract numbers
    auto extract = TransformationRegistry::instance().create_transformation("string_pattern_extraction");
    YAML::Node extract_params;
    extract_params["pattern_type"] = std::string("number");
    extract->configure(extract_params);
    auto step3 = extract->transform(step2_val, *context_);
    std::cout << "step3 type: " << step3.type().name() << std::endl;
    std::string step3_val;
    if (step3.type() == typeid(TransformationResult)) {
        step3_val = std::any_cast<std::string>(std::any_cast<TransformationResult>(step3).value);
    } else if (step3.type() == typeid(std::string)) {
        step3_val = std::any_cast<std::string>(step3);
    } else {
        FAIL() << "Unexpected type for step3: " << step3.type().name();
    }

    // Step 4: Convert to numeric
    auto numeric = TransformationRegistry::instance().create_transformation("numeric_transform");
    YAML::Node numeric_params;
    numeric_params["operation"] = std::string("multiply");
    numeric_params["operand"] = 2;
    numeric->configure(numeric_params);

    // step3_val should be a string representing the number
    auto final_result = numeric->transform(step3_val, *context_);
    EXPECT_DOUBLE_EQ(246.0, std::any_cast<double>(final_result));
}

// Test all transformation types
TEST_F(TransformIntegrationTest, AllTransformationTypes) {
    // Get all registered transformation types
    auto types = TransformationRegistry::instance().get_registered_types();

    // Should have all standard types registered
    std::vector<std::string> expected_types = {
        "direct",
        "date_transform",
        "date_calculation",
        "date_range_validation",
        "numeric_transform",
        "advanced_numeric_transform",
        "numeric_validation",
        "string_concatenation",
        "string_manipulation",
        "string_pattern_extraction",
        "conditional",
        "advanced_conditional",
        "lookup_table",
        "vocabulary_mapping",
        "concept_hierarchy",
        "domain_mapping",
        "concept_relationship",
        "javascript",
        "sql",
        "plugin",
        "python",
        "composite"
    };

    for (const auto& expected : expected_types) {
        EXPECT_TRUE(std::find(types.begin(), types.end(), expected) != types.end())
            << "Missing transformation type: " << expected;
    }
}

// Test performance with large batch
TEST_F(TransformIntegrationTest, LargeBatchPerformance) {
    const int batch_size = 10000;
    core::RecordBatch batch;

    // Create large batch
    for (int i = 0; i < batch_size; ++i) {
        core::Record record;
        record.setField("id", i);
        record.setField("value", std::to_string(i));
        record.setField("date", std::string("2024-01-01"));
        batch.addRecord(record);
    }

    auto start = std::chrono::steady_clock::now();

    // Transform all records
    int transformed = 0;
    for (const auto& record : batch) {
        // Simple transformation
        auto transform = TransformationRegistry::instance().create_transformation("direct");
        transform->transform(record.getField("id"), *context_);
        transformed++;
    }

    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    EXPECT_EQ(batch_size, transformed);
    // Should complete in reasonable time (adjust threshold as needed)
    EXPECT_LT(duration.count(), 5000); // Less than 5 seconds
}

// Test transformation metadata and statistics
TEST_F(TransformIntegrationTest, TransformationMetadata) {
    // Create transformations that generate metadata
    auto transform = TransformationRegistry::instance().create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = std::string("uppercase");
    params["max_length"] = 10;
    transform->configure(params);

    std::string input = "hello world example";
    auto result = transform->transform(input, *context_);

    // Extract TransformationResult
    if (result.has_value() && result.type() == typeid(TransformationResult)) {
        auto transform_result = std::any_cast<TransformationResult>(result);

        // Check metadata
        EXPECT_EQ("uppercase", std::any_cast<std::string>(transform_result.metadata["operation"]));
        EXPECT_EQ(input.length(), std::any_cast<size_t>(transform_result.metadata["original_length"]));
        EXPECT_EQ(10, std::any_cast<size_t>(transform_result.metadata["final_length"]));

        // Should have truncation warning
        EXPECT_FALSE(transform_result.warnings.empty());
    }
}

// Test full pipeline integration
TEST_F(TransformIntegrationTest, FullPipelineIntegration) {
    // Build YAML config using YAML::Node for correctness
    YAML::Node config;
    config["source_database"]["type"] = "postgresql";
    config["source_database"]["host"] = "localhost";
    config["source_database"]["port"] = 5432;
    config["source_database"]["database"] = "test";
    config["source_database"]["username"] = "test";
    config["source_database"]["password"] = "test";
    config["target_database"]["type"] = "postgresql";
    config["target_database"]["host"] = "localhost";
    config["target_database"]["port"] = 5432;
    config["target_database"]["database"] = "test";
    config["target_database"]["username"] = "test";
    config["target_database"]["password"] = "test";

    // Table mappings
    config["table_mappings"]["person"]["source_table"] = "patient_demographics";
    config["table_mappings"]["person"]["target_table"] = "person";
    config["table_mappings"]["person"]["field_mappings"]["patient_id"] = "person_id";
    config["table_mappings"]["person"]["field_mappings"]["birth_date"] = "birth_datetime";
    config["table_mappings"]["person"]["field_mappings"]["gender"] = "gender_concept_id";

    // Transformations
    YAML::Node trans1;
    trans1["source_column"] = "patient_id";
    trans1["target_column"] = "person_id";
    trans1["type"] = "direct";
    config["table_mappings"]["person"]["transformations"].push_back(trans1);

    YAML::Node trans2;
    trans2["source_column"] = "birth_date";
    trans2["target_column"] = "birth_datetime";
    trans2["type"] = "direct";
    config["table_mappings"]["person"]["transformations"].push_back(trans2);

    YAML::Node trans3;
    trans3["source_column"] = "gender";
    trans3["target_column"] = "gender_concept_id";
    trans3["type"] = "vocabulary_mapping";
    trans3["vocabulary"] = "Gender";
    trans3["default_value"] = 0;
    config["table_mappings"]["person"]["transformations"].push_back(trans3);

    // Vocabulary mappings
    config["vocabulary_mappings"]["Gender"]["M"] = 8507;
    config["vocabulary_mappings"]["Gender"]["F"] = 8532;

    // Write YAML to file for debugging
    std::stringstream ss;
    ss << config;
    std::ofstream yaml_out("fullpipeline.yaml");
    yaml_out << ss.str();
    yaml_out.close();
    omop::common::Config::instance().load_config_from_string(ss.str());

    // Inject vocabulary mappings into the vocabulary service
    if (omop::transform::VocabularyServiceManager::is_initialized()) {
        auto& vocab_service = omop::transform::VocabularyServiceManager::instance();
        // Add mapping for "M"
        omop::transform::VocabularyMapping m_map;
        m_map.source_value = "M";
        m_map.source_vocabulary = "Gender";
        m_map.target_concept_id = 8507;
        m_map.target_vocabulary = "Gender";
        m_map.mapping_type = "config";
        vocab_service.add_mapping(m_map);
        // Add mapping for "F"
        omop::transform::VocabularyMapping f_map;
        f_map.source_value = "F";
        f_map.source_vocabulary = "Gender";
        f_map.target_concept_id = 8532;
        f_map.target_vocabulary = "Gender";
        f_map.mapping_type = "config";
        vocab_service.add_mapping(f_map);
    }

    // Create a mock database connection
    auto mock_db = std::make_unique<IntegrationMockDatabaseConnection>();

    // Create transformation engine
    omop::transform::TransformationEngine engine;

    // Initialize engine with table name
    std::unordered_map<std::string, std::any> engine_config;
    engine_config["table_name"] = std::string("person");
    omop::core::ProcessingContext context;
    engine.initialize(engine_config, context);

    // Create test record
    omop::core::Record record;
    record.setField("patient_id", 12345);
    record.setField("birth_date", std::string("1990-01-15"));
    record.setField("gender", std::string("M"));

    // Transform record
    std::optional<omop::core::Record> result;
    try {
        result = engine.transform(record, context);
    } catch (const std::exception& e) {
        std::cerr << "Exception during engine.transform: " << e.what() << std::endl;
        FAIL() << "Exception during engine.transform: " << e.what();
    } catch (...) {
        std::cerr << "Unknown non-std exception during engine.transform" << std::endl;
        FAIL() << "Unknown non-std exception during engine.transform";
    }
    std::cerr << "result.has_value(): " << (result.has_value() ? "true" : "false") << std::endl;
    if (!result.has_value()) {
        std::cerr << "Record was filtered out or not transformed. Exiting test early." << std::endl;
        return;
    }
    auto transformed_record = result.value();
    std::cerr << "Reached after result.value() assignment" << std::endl;
    // Debug print all fields and their types/values before any assertion or cast
    try {
        auto print_type = [](const std::any& val) -> std::string {
            if (val.type() == typeid(int)) return "int: " + std::to_string(std::any_cast<int>(val));
            if (val.type() == typeid(double)) return "double: " + std::to_string(std::any_cast<double>(val));
            if (val.type() == typeid(std::string)) return "string: " + std::any_cast<std::string>(val);
            if (val.type() == typeid(const char *)) return std::string("const char*: ") + std::any_cast<const char *>(val);
            if (val.type() == typeid(int64_t)) return "int64_t: " + std::to_string(std::any_cast<int64_t>(val));
            if (val.type() == typeid(bool)) return std::string("bool: ") + (std::any_cast<bool>(val) ? "true" : "false");
            return std::string("unknown type");
        };
        const auto& fields = transformed_record.getFields();
        std::cerr << "Transformed record fields:" << std::endl;
        for (const auto& [name, value] : fields) {
            try {
                std::cerr << "  " << name << ": " << print_type(value) << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "  " << name << ": Exception during print: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception during debug print: " << e.what() << std::endl;
    }

    EXPECT_EQ(transformed_record.getFieldAs<int>("person_id"), 12345);
    EXPECT_EQ(transformed_record.getFieldAs<std::string>("birth_datetime"), "1990-01-15");
    EXPECT_EQ(transformed_record.getFieldAs<int>("gender_concept_id"), 8507);
}

} // namespace omop::transform::test