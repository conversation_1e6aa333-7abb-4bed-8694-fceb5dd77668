// tests/unit/transform/custom_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/custom_transformations.h"
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <memory>

namespace omop::transform::test {

class CustomTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test JavaScript transformation simple expression
TEST_F(CustomTransformationsTest, JavaScriptTransformationSimpleExpression) {
    auto transform = registry_->create_transformation("javascript");

    YAML::Node params;
    params["expression"] = "value.toUpperCase()";
    params["output_type"] = "string";
    transform->configure(params);

    std::string input = "hello world";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    // Since JavaScript execution is mocked, should return uppercase
    EXPECT_EQ("HELLO WORLD", std::any_cast<std::string>(transform_result.value));
}

// Test JavaScript transformation length expression
TEST_F(CustomTransformationsTest, JavaScriptTransformationLength) {
    auto transform = registry_->create_transformation("javascript");

    YAML::Node params;
    params["expression"] = "value.length";
    params["output_type"] = "number";
    transform->configure(params);

    std::string input = "test string";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_DOUBLE_EQ(11.0, std::any_cast<double>(transform_result.value));
}

// Test JavaScript transformation multiplication
TEST_F(CustomTransformationsTest, JavaScriptTransformationMultiplication) {
    auto transform = registry_->create_transformation("javascript");

    YAML::Node params;
    params["expression"] = "value * 2.5";
    params["output_type"] = "number";
    transform->configure(params);

    double input = 10.0;
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_DOUBLE_EQ(25.0, std::any_cast<double>(transform_result.value));
}

// Test JavaScript transformation with boolean output
TEST_F(CustomTransformationsTest, JavaScriptTransformationBoolean) {
    auto transform = registry_->create_transformation("javascript");

    YAML::Node params;
    params["expression"] = "value === 'true'";
    params["output_type"] = "boolean";
    transform->configure(params);

    std::string input = "true";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_TRUE(std::any_cast<bool>(transform_result.value));
}

// Test JavaScript transformation with imports
TEST_F(CustomTransformationsTest, JavaScriptTransformationWithImports) {
    auto transform = registry_->create_transformation("javascript");

    YAML::Node params;
    params["expression"] = "Math.round(value)";
    params["output_type"] = "integer";
    params["imports"] = std::vector<std::string>{"math"};
    transform->configure(params);

    double input = 3.7;
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ(4, std::any_cast<int>(transform_result.value));
}

// Test SQL transformation uppercase
TEST_F(CustomTransformationsTest, SQLTransformationUppercase) {
    auto transform = registry_->create_transformation("sql");

    YAML::Node params;
    params["expression"] = "UPPER(value)";
    params["result_type"] = "string";
    transform->configure(params);

    std::string input = "hello sql";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ("HELLO SQL", std::any_cast<std::string>(transform_result.value));
}

// Test SQL transformation length
TEST_F(CustomTransformationsTest, SQLTransformationLength) {
    auto transform = registry_->create_transformation("sql");

    YAML::Node params;
    params["expression"] = "LENGTH(value)";
    params["result_type"] = "integer";
    transform->configure(params);

    std::string input = "test";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ(4, std::any_cast<int>(transform_result.value));
}

// Test SQL transformation with context parameters
TEST_F(CustomTransformationsTest, SQLTransformationWithContext) {
    auto transform = registry_->create_transformation("sql");

    YAML::Node params;
    params["expression"] = "CASE WHEN value > threshold THEN 'high' ELSE 'low' END";
    params["context_params"] = std::vector<std::string>{"threshold"};
    params["result_type"] = "string";
    transform->configure(params);

    // Set context parameter
    context_->set_data("threshold", 100.0);

    double input = 150.0;
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    // Since SQL execution is mocked, returns placeholder
    EXPECT_FALSE(transform_result.value.has_value() &&
                transform_result.value.type() == typeid(std::string) &&
                std::any_cast<std::string>(transform_result.value).empty());
}

// Test plugin transformation configuration
TEST_F(CustomTransformationsTest, PluginTransformationConfiguration) {
    auto transform = registry_->create_transformation("plugin");

    YAML::Node params;
    params["plugin_name"] = "test_plugin";
    params["plugin_path"] = "./plugins/test_plugin.so";
    params["function_name"] = "custom_transform";

    YAML::Node plugin_config;
    plugin_config["option1"] = "value1";
    plugin_config["option2"] = 42;
    params["config"] = plugin_config;

    transform->configure(params);

    // Plugin loading would fail in test environment
    std::string input = "test";
    auto complex_transform = dynamic_cast<ComplexTransformation*>(transform.get());
    ASSERT_NE(complex_transform, nullptr);
    auto result = complex_transform->transform_detailed(input, *context_);

    EXPECT_FALSE(result.is_success());
}

// Test Python transformation uppercase
TEST_F(CustomTransformationsTest, PythonTransformationUppercase) {
    auto transform = registry_->create_transformation("python");

    YAML::Node params;
    params["script"] = "result = value.upper()";
    transform->configure(params);

    std::string input = "python test";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ("PYTHON TEST", std::any_cast<std::string>(transform_result.value));
}

// Test Python transformation length
TEST_F(CustomTransformationsTest, PythonTransformationLength) {
    auto transform = registry_->create_transformation("python");

    YAML::Node params;
    params["script"] = "result = len(value)";
    transform->configure(params);

    std::string input = "test";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ(4, std::any_cast<int>(transform_result.value));
}

// Test Python transformation with modules
TEST_F(CustomTransformationsTest, PythonTransformationWithModules) {
    auto transform = registry_->create_transformation("python");

    YAML::Node params;
    params["script"] = "import math\nresult = math.sqrt(value)";
    params["modules"] = std::vector<std::string>{"math"};
    transform->configure(params);

    double input = 16.0;
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    // Mocked implementation returns input with warning
    EXPECT_TRUE(transform_result.value.has_value());
    EXPECT_FALSE(transform_result.warnings.empty());
}

// Test composite transformation with single step
TEST_F(CustomTransformationsTest, CompositeTransformationSingleStep) {
    auto transform = registry_->create_transformation("composite");

    YAML::Node params;
    YAML::Node step1;
    step1["type"] = "string_manipulation";
    step1["name"] = "uppercase_step";
    step1["params"]["operation"] = "uppercase";

    params["transformations"].push_back(step1);
    transform->configure(params);

    std::string input = "hello";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ("HELLO", std::any_cast<std::string>(transform_result.value));
    EXPECT_EQ(1, std::any_cast<size_t>(transform_result.metadata["total_steps"]));
}

// Test composite transformation with multiple steps
TEST_F(CustomTransformationsTest, CompositeTransformationMultipleSteps) {
    auto transform = registry_->create_transformation("composite");

    YAML::Node params;

    // Step 1: Uppercase
    YAML::Node step1;
    step1["type"] = "string_manipulation";
    step1["name"] = "uppercase";
    step1["params"]["operation"] = "uppercase";
    params["transformations"].push_back(step1);

    // Step 2: Trim
    YAML::Node step2;
    step2["type"] = "string_manipulation";
    step2["name"] = "trim";
    step2["params"]["operation"] = "trim";
    params["transformations"].push_back(step2);

    // Step 3: Pad left
    YAML::Node step3;
    step3["type"] = "string_manipulation";
    step3["name"] = "pad";
    step3["params"]["operation"] = "pad_left";
    step3["params"]["target_length"] = 10;
    step3["params"]["pad_char"] = "*";
    params["transformations"].push_back(step3);

    transform->configure(params);

    std::string input = "  hello  ";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    // Should be uppercase, trimmed, and padded
    EXPECT_EQ("*****HELLO", std::any_cast<std::string>(transform_result.value));
    EXPECT_EQ(3, std::any_cast<size_t>(transform_result.metadata["total_steps"]));
}

// Test composite transformation with stop on error
TEST_F(CustomTransformationsTest, CompositeTransformationStopOnError) {
    auto transform = registry_->create_transformation("composite");

    YAML::Node params;
    params["stop_on_error"] = true;

    // Step 1: Will succeed
    YAML::Node step1;
    step1["type"] = "string_manipulation";
    step1["params"]["operation"] = "uppercase";
    params["transformations"].push_back(step1);

    // Step 2: Will fail (invalid transformation type)
    YAML::Node step2;
    step2["type"] = "invalid_transform_type";
    params["transformations"].push_back(step2);

    // Step 3: Should not be reached
    YAML::Node step3;
    step3["type"] = "string_manipulation";
    step3["params"]["operation"] = "lowercase";
    params["transformations"].push_back(step3);

    transform->configure(params);

    std::string input = "test";
    auto complex_transform = dynamic_cast<ComplexTransformation*>(transform.get());
    ASSERT_NE(complex_transform, nullptr);
    auto result = complex_transform->transform_detailed(input, *context_);

    // First step should succeed, then stop on error
    EXPECT_EQ("TEST", std::any_cast<std::string>(result.value));
}

// Test composite transformation metadata
TEST_F(CustomTransformationsTest, CompositeTransformationMetadata) {
    auto transform = registry_->create_transformation("composite");

    YAML::Node params;

    YAML::Node step1;
    step1["type"] = "string_manipulation";
    step1["name"] = "normalize";
    step1["params"]["operation"] = "normalize_whitespace";
    params["transformations"].push_back(step1);

    transform->configure(params);

    std::string input = "  test   value  ";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());

    // Check metadata
    EXPECT_EQ("string_manipulation",
              std::any_cast<std::string>(transform_result.metadata["step_0_type"]));
    EXPECT_EQ("normalize",
              std::any_cast<std::string>(transform_result.metadata["step_0_name"]));
}

// Test JavaScript transformation with invalid expression
TEST_F(CustomTransformationsTest, JavaScriptTransformationInvalidExpression) {
    auto transform = registry_->create_transformation("javascript");

    YAML::Node params;
    params["expression"] = "invalid.function()";
    params["output_type"] = "string";
    transform->configure(params);

    std::string input = "test";
    auto transform_result = transform->transform_safe(input, *context_);

    EXPECT_FALSE(transform_result.is_success());
    EXPECT_TRUE(transform_result.error_message.has_value());
    EXPECT_TRUE(transform_result.error_message->find("Invalid JavaScript syntax") != std::string::npos);
}

// Test SQL transformation with numeric result type
TEST_F(CustomTransformationsTest, SQLTransformationNumericResult) {
    auto transform = registry_->create_transformation("sql");

    YAML::Node params;
    params["expression"] = "CAST(value AS INTEGER) * 2";
    params["result_type"] = "integer";
    transform->configure(params);

    std::string input = "10";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    // SQL transformation should multiply by 2: CAST(10 AS INTEGER) * 2 = 20
    EXPECT_EQ(20, std::any_cast<int>(transform_result.value));
}

// Test Python transformation with script file
TEST_F(CustomTransformationsTest, PythonTransformationScriptFile) {
    auto transform = registry_->create_transformation("python");

    YAML::Node params;
    params["script_file"] = "/path/to/script.py";
    transform->configure(params);

    std::string input = "test";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    // Script file loading is mocked
    EXPECT_TRUE(transform_result.value.has_value());
}

// Test composite transformation with different transformation types
TEST_F(CustomTransformationsTest, CompositeTransformationMixedTypes) {
    auto transform = registry_->create_transformation("composite");

    YAML::Node params;

    // Step 1: String manipulation
    YAML::Node step1;
    step1["type"] = "string_manipulation";
    step1["params"]["operation"] = "trim";
    params["transformations"].push_back(step1);

    // Step 2: Pattern extraction
    YAML::Node step2;
    step2["type"] = "string_pattern_extraction";
    step2["params"]["pattern_type"] = "number";
    step2["params"]["extract_all"] = true;
    params["transformations"].push_back(step2);

    transform->configure(params);

    std::string input = "  Value: 123, Count: 456  ";
    auto transform_result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(transform_result.is_success());
    EXPECT_EQ("123, 456", std::any_cast<std::string>(transform_result.value));
}

} // namespace omop::transform::test