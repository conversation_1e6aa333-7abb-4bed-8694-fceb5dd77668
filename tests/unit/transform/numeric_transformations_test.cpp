// tests/unit/transform/numeric_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "transform/numeric_transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <cmath>
#include <limits>
#include <yaml-cpp/yaml.h>
#include <iostream>

namespace omop::transform::test {

class NumericTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test numeric transformation multiply operation
TEST_F(NumericTransformationsTest, NumericTransformationMultiply) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.5;
    transform->configure(params);

    double input = 10.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(25.0, std::any_cast<double>(result.value));
}

// Test numeric transformation divide operation
TEST_F(NumericTransformationsTest, NumericTransformationDivide) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "divide";
    params["operand"] = 2.0;
    transform->configure(params);

    double input = 10.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(5.0, std::any_cast<double>(result.value));
}

// Test numeric transformation divide by zero
TEST_F(NumericTransformationsTest, NumericTransformationDivideByZero) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "divide";
    params["operand"] = 0.0;
    transform->configure(params);

    double input = 10.0;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test numeric transformation round operation
TEST_F(NumericTransformationsTest, NumericTransformationRound) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "round";
    params["precision"] = 2;
    transform->configure(params);

    double input = 3.14159;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(result.value));
}

// Test numeric transformation with min/max constraints
TEST_F(NumericTransformationsTest, NumericTransformationConstraints) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "add";
    params["operand"] = 0.0;
    params["min_value"] = 0.0;
    params["max_value"] = 100.0;
    transform->configure(params);

    // Test clamping to min
    double input = -10.0;
    auto result = transform->transform_safe(input, *context_);
    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(0.0, std::any_cast<double>(result.value));

    // Test clamping to max
    input = 150.0;
    result = transform->transform_safe(input, *context_);
    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(100.0, std::any_cast<double>(result.value));
}

// Test numeric transformation with string input
TEST_F(NumericTransformationsTest, NumericTransformationStringInput) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.0;
    transform->configure(params);

    std::string input = "10.5";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(21.0, std::any_cast<double>(result.value));
}

// Test numeric transformation with invalid string input
TEST_F(NumericTransformationsTest, NumericTransformationInvalidStringInput) {
    auto transform = registry_->create_transformation("numeric_transform");

    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.0;
    transform->configure(params);

    std::string input = "invalid";
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test numeric transformation validation
TEST_F(NumericTransformationsTest, NumericTransformationValidation) {
    auto transform = registry_->create_transformation("numeric_transform");

    EXPECT_TRUE(transform->validate_input(10.0));
    EXPECT_TRUE(transform->validate_input(10));
    EXPECT_TRUE(transform->validate_input(std::string("10.5")));
    EXPECT_FALSE(transform->validate_input(std::string("invalid")));
    EXPECT_FALSE(transform->validate_input(std::any{}));
}

// Test numeric transformation type
TEST_F(NumericTransformationsTest, NumericTransformationType) {
    auto transform = registry_->create_transformation("numeric_transform");
    EXPECT_EQ("numeric_transform", transform->get_type());
}

// Test advanced numeric transformations
TEST_F(NumericTransformationsTest, AdvancedNumericUnitConversion) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "unit_conversion";
    params["from_unit"] = "kg";
    params["to_unit"] = "g";
    transform->configure(params);

    double input = 1.5;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(1500.0, std::any_cast<double>(result.value));
}

// Test advanced numeric logarithm
TEST_F(NumericTransformationsTest, AdvancedNumericLogarithm) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "logarithm";
    params["base"] = 10.0;
    transform->configure(params);

    double input = 100.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(2.0, std::any_cast<double>(result.value));
}

// Test advanced numeric exponential
TEST_F(NumericTransformationsTest, AdvancedNumericExponential) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "exponential";
    params["base"] = 2.0;
    transform->configure(params);

    double input = 3.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(8.0, std::any_cast<double>(result.value));
}

// Test advanced numeric power
TEST_F(NumericTransformationsTest, AdvancedNumericPower) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "power";
    params["exponent"] = 3.0;
    transform->configure(params);

    double input = 2.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(8.0, std::any_cast<double>(result.value));
}

// Test advanced numeric square root
TEST_F(NumericTransformationsTest, AdvancedNumericSquareRoot) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "square_root";
    transform->configure(params);

    double input = 16.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(4.0, std::any_cast<double>(result.value));
}

// Test advanced numeric percentage
TEST_F(NumericTransformationsTest, AdvancedNumericPercentage) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "percentage";
    params["percentage_total"] = 200.0;
    transform->configure(params);

    double input = 50.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(25.0, std::any_cast<double>(result.value));
}

// Test advanced numeric z-score
TEST_F(NumericTransformationsTest, AdvancedNumericZScore) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "z_score";
    params["mean"] = 100.0;
    params["std_deviation"] = 15.0;
    transform->configure(params);

    double input = 115.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(1.0, std::any_cast<double>(result.value));
}

// Test advanced numeric min/max normalization
TEST_F(NumericTransformationsTest, AdvancedNumericMinMax) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "min_max";
    params["min_value"] = 0.0;
    params["max_value"] = 100.0;
    transform->configure(params);

    double input = 50.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(0.5, std::any_cast<double>(result.value));
}

// Test advanced numeric clamp
TEST_F(NumericTransformationsTest, AdvancedNumericClamp) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "clamp";
    params["min_value"] = 0.0;
    params["max_value"] = 100.0;
    transform->configure(params);

    double input = 150.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(100.0, std::any_cast<double>(result.value));
}

// Test advanced numeric bucket range
TEST_F(NumericTransformationsTest, AdvancedNumericBucketRange) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "bucket_range";
    params["bucket_size"] = 10.0;
    transform->configure(params);

    double input = 25.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    
    EXPECT_EQ("20-30", std::any_cast<std::string>(result.value));
}

// Test advanced numeric with rounding
TEST_F(NumericTransformationsTest, AdvancedNumericWithRounding) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "power";
    params["exponent"] = 2.0;
    params["round_to_decimal"] = 2;
    transform->configure(params);

    double input = 3.14159;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(9.87, std::any_cast<double>(result.value));
}

// Test advanced numeric output as integer
TEST_F(NumericTransformationsTest, AdvancedNumericOutputAsInteger) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "power";
    params["exponent"] = 2.0;
    params["output_as_integer"] = true;
    transform->configure(params);

    double input = 3.7;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ(13, std::any_cast<int64_t>(result.value));
}

// Test numeric validation valid range
TEST_F(NumericTransformationsTest, NumericValidationValidRange) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["min_allowed"] = 0.0;
    params["max_allowed"] = 100.0;
    transform->configure(params);

    double input = 50.0;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(50.0, std::any_cast<double>(result.value));
}

// Test numeric validation precision enforcement
TEST_F(NumericTransformationsTest, NumericValidationPrecisionEnforcement) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["required_precision"] = 2;
    params["enforce_precision"] = true;
    transform->configure(params);

    double input = 3.14159;
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(result.value));
}

// Test numeric validation string input
TEST_F(NumericTransformationsTest, NumericValidationStringInput) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["min_allowed"] = 0.0;
    params["max_allowed"] = 100.0;
    transform->configure(params);

    std::string input = "50.5";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(50.5, std::any_cast<double>(result.value));
}

// Test numeric validation out of range
TEST_F(NumericTransformationsTest, NumericValidationOutOfRange) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["min_allowed"] = 0.0;
    params["max_allowed"] = 10.0;
    transform->configure(params);

    double input = 20.0;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test numeric validation with NaN
TEST_F(NumericTransformationsTest, NumericValidationNaN) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["reject_nan"] = true;
    transform->configure(params);

    double input = std::numeric_limits<double>::quiet_NaN();
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test numeric validation with infinity
TEST_F(NumericTransformationsTest, NumericValidationInfinity) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["reject_infinity"] = true;
    transform->configure(params);

    double input = std::numeric_limits<double>::infinity();
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test numeric validation with allowed values
TEST_F(NumericTransformationsTest, NumericValidationAllowedValues) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["allowed_values"] = std::vector<double>{1.0, 2.0, 3.0};
    transform->configure(params);

    double input = 4.0;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test numeric validation with clamping
TEST_F(NumericTransformationsTest, NumericValidationWithClamping) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["min_allowed"] = 0.0;
    params["max_allowed"] = 100.0;
    params["clamp_to_range"] = true;
    transform->configure(params);

    double input = -10.0;
    auto result = transform->transform_safe(input, *context_);
    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(0.0, std::any_cast<double>(result.value));
}

// Test numeric validation with clamping to max
TEST_F(NumericTransformationsTest, NumericValidationClampToMax) {
    auto transform = registry_->create_transformation("numeric_validation");

    YAML::Node params;
    params["min_allowed"] = 0.0;
    params["max_allowed"] = 100.0;
    params["clamp_to_range"] = true;
    transform->configure(params);

    double input = 150.0;
    auto result = transform->transform_safe(input, *context_);
    ASSERT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(100.0, std::any_cast<double>(result.value));
}

// Test advanced numeric unit conversion missing units
TEST_F(NumericTransformationsTest, AdvancedNumericUnitConversionMissingUnits) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "unit_conversion";
    transform->configure(params);

    double input = 150.0;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test advanced numeric logarithm negative value
TEST_F(NumericTransformationsTest, AdvancedNumericLogarithmNegative) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "logarithm";
    transform->configure(params);

    double input = -10.0;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test advanced numeric square root negative
TEST_F(NumericTransformationsTest, AdvancedNumericSquareRootNegative) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "square_root";
    transform->configure(params);

    double input = -16.0;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test advanced numeric transformation with invalid input
TEST_F(NumericTransformationsTest, AdvancedNumericInvalidInput) {
    auto transform = registry_->create_transformation("advanced_numeric_transform");

    YAML::Node params;
    params["operation"] = "unit_conversion";
    params["from_unit"] = "kg";
    params["to_unit"] = "g";
    transform->configure(params);

    std::string input = "invalid";
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

} // namespace omop::transform::test