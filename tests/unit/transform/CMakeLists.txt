# tests/unit/transform/CMakeLists.txt - Standardized Unit Tests for Transform Library

# Test source files (alphabetically sorted)
set(TRANSFORM_TEST_SOURCES
    all_transformations_comprehensive_test.cpp
    comprehensive_edge_cases_test.cpp
    conditional_transformations_test.cpp
    custom_transformations_test.cpp
    date_transformations_test.cpp
    field_transformation_helpers_test.cpp
    field_transformation_test.cpp
    numeric_transformations_test.cpp
    string_transformations_test.cpp
    transform_integration_test.cpp
    transform_utils_test.cpp
    transformation_edge_cases_test.cpp
    transformation_engine_test.cpp
    transformation_memory_management_test.cpp
    transformation_pipeline_comprehensive_test.cpp
    transformation_registry_test.cpp
    uk_localized_comprehensive_test.cpp
    validation_engine_test.cpp
    vocabulary_service_test.cpp
    vocabulary_transformations_test.cpp
)

# Use the consolidated approach from parent CMakeLists.txt
add_component_unit_tests(transform ${TRANSFORM_TEST_SOURCES})

# Add additional dependencies for transform tests
target_link_libraries(test_omop_transform_unit PRIVATE
    yaml-cpp::yaml-cpp
    nlohmann_json::nlohmann_json
    fmt::fmt
)

# Add UNIT_TESTING flag for test-only methods
target_compile_definitions(test_omop_transform_unit PRIVATE UNIT_TESTING)

# Set longer timeout for transform tests due to complex logic
set_tests_properties(test_omop_transform_unit PROPERTIES
    LABELS "unit;transform"
    TIMEOUT 900  # 15 minutes for complex transformation tests
)

# Enable sanitizers for transform tests if enabled
if(ENABLE_SANITIZERS)
    target_compile_options(test_omop_transform_unit PRIVATE
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )
    target_link_options(test_omop_transform_unit PRIVATE
        -fsanitize=address,undefined
    )
endif()