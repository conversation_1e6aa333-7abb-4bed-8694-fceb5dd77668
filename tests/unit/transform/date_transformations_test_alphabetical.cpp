// tests/unit/transform/date_transformations_test.cpp
// Date transformation tests with alphabetical ordering and explanatory comments

#include <gtest/gtest.h>
#include "transform/transformations.h"
#include "transform/date_transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class DateTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::chrono::system_clock::time_point create_date(int year, int month, int day) {
        std::tm tm = {};
        tm.tm_year = year - 1900;
        tm.tm_mon = month - 1;
        tm.tm_mday = day;
        return std::chrono::system_clock::from_time_t(std::mktime(&tm));
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test case: Add days operation with date arithmetic
TEST_F(DateTransformationsTest, DateCalculationAddDays) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_days";
    params["offset"] = 7;
    transform->configure(params);

    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-01-08", std::any_cast<std::string>(result.value));
}

// Test case: Add months operation with proper month boundaries
TEST_F(DateTransformationsTest, DateCalculationAddMonths) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_months";
    params["offset"] = 2;
    transform->configure(params);

    std::string input = "2024-01-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-03-15", std::any_cast<std::string>(result.value));
}

// Test case: Add years operation with leap year handling
TEST_F(DateTransformationsTest, DateCalculationAddYears) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_years";
    params["offset"] = 5;
    transform->configure(params);

    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2029-01-01", std::any_cast<std::string>(result.value));
}

// Test case: Age calculation from birth date to current date
TEST_F(DateTransformationsTest, DateCalculationAge) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);

    // Input should be the birth date
    std::string input = "1990-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Age will be calculated from birth date to current date
    int age = std::any_cast<int>(result.value);
    EXPECT_GT(age, 30); // Should be around 34 in 2024
}

// Test case: Age calculation when birthday hasn't occurred yet this year
TEST_F(DateTransformationsTest, DateCalculationAgeBeforeBirthday) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);

    // Input should be the birth date
    std::string input = "1990-12-31";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Age will be calculated from birth date to current date
    int age = std::any_cast<int>(result.value);
    EXPECT_GT(age, 30); // Should be around 33 in 2024
}

// Test case: Date difference calculation in days
TEST_F(DateTransformationsTest, DateCalculationDateDiffDays) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "date_diff";
    params["unit"] = "days";
    params["reference_date"] = "2024-01-10";
    transform->configure(params);

    // Input date, reference date is 2024-01-10, so 2024-01-01 should give 9 days
    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ(9, std::any_cast<int>(result.value));
}

// Test case: Date difference calculation in months
TEST_F(DateTransformationsTest, DateCalculationDateDiffMonths) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "date_diff";
    params["unit"] = "months";
    params["reference_date"] = "2024-03-15";
    transform->configure(params);

    // Input date, reference date is 2024-03-15, so 2024-01-01 should give 2 months
    std::string input = "2024-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ(2, std::any_cast<int>(result.value));
}

// Test case: End of month calculation with leap year handling
TEST_F(DateTransformationsTest, DateCalculationEndOfMonth) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "end_of_month";
    transform->configure(params);

    std::string input = "2024-02-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-02-29", std::any_cast<std::string>(result.value)); // Leap year
}

// Test case: End of year calculation
TEST_F(DateTransformationsTest, DateCalculationEndOfYear) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "end_of_year";
    transform->configure(params);

    std::string input = "2024-06-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-12-31", std::any_cast<std::string>(result.value));
}

// Test case: Age calculation using current date as reference
TEST_F(DateTransformationsTest, DateCalculationReferenceNow) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "age";
    transform->configure(params);

    // Use a birth date from 1990
    std::string input = "1990-01-01";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Age should be greater than 30 (born in 1990, now in 2024+)
    EXPECT_GT(std::any_cast<int>(result.value), 30);
}

// Test case: Start of month calculation
TEST_F(DateTransformationsTest, DateCalculationStartOfMonth) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "start_of_month";
    transform->configure(params);

    std::string input = "2024-03-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-03-01", std::any_cast<std::string>(result.value));
}

// Test case: Start of year calculation
TEST_F(DateTransformationsTest, DateCalculationStartOfYear) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "start_of_year";
    transform->configure(params);

    std::string input = "2024-06-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-01-01", std::any_cast<std::string>(result.value));
}

// Test case: Date calculation with time_point input for compatibility
TEST_F(DateTransformationsTest, DateCalculationTimePointInput) {
    auto transform = registry_->create_transformation("date_calculation");

    YAML::Node params;
    params["operation"] = "add_days";
    params["offset"] = 1;
    transform->configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform->transform_safe(now, *context_);

    ASSERT_TRUE(result.is_success());
    // Should return a date string
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_FALSE(output.empty());
}

// Test case: Date range validation with time_point input
TEST_F(DateTransformationsTest, DateRangeValidationTimePointInput) {
    auto transform = registry_->create_transformation("date_range_validation");

    YAML::Node params;
    params["min_date"] = "2020-01-01";
    params["max_date"] = "2030-12-31";
    transform->configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform->transform_safe(now, *context_);

    ASSERT_TRUE(result.is_success());
    // Should return a formatted date string
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_FALSE(output.empty());
}

// Test case: Date transformation with invalid input handling
TEST_F(DateTransformationsTest, DateTransformationInvalidInput) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    transform->configure(params);

    std::string input = "invalid-date";
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test case: Basic date transformation with string input and time addition
TEST_F(DateTransformationsTest, DateTransformationStringInput) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d %H:%M:%S";
    params["add_time"] = true;
    params["default_time"] = "12:00:00";
    transform->configure(params);

    std::string input = "2024-03-15";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("2024-03-15 12:00:00", std::any_cast<std::string>(result.value));
}

// Test case: Date transformation with time_point input
TEST_F(DateTransformationsTest, DateTransformationTimePointInput) {
    auto transform = registry_->create_transformation("date_transform");

    YAML::Node params;
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform->transform_safe(now, *context_);

    ASSERT_TRUE(result.is_success());
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_EQ(10, output.length()); // YYYY-MM-DD format
}

// Test case: Date transformation type identification
TEST_F(DateTransformationsTest, DateTransformationType) {
    auto transform = registry_->create_transformation("date_transform");
    EXPECT_EQ("date_transform", transform->get_type());
}

// Test case: Date transformation input validation with different types
TEST_F(DateTransformationsTest, DateTransformationValidation) {
    auto transform = registry_->create_transformation("date_transform");

    EXPECT_TRUE(transform->validate_input(std::string("2024-03-15")));
    EXPECT_TRUE(transform->validate_input(std::chrono::system_clock::now()));
    EXPECT_FALSE(transform->validate_input(std::any{}));
    EXPECT_FALSE(transform->validate_input(123));
}

} // namespace omop::transform::test