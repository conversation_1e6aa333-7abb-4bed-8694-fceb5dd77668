// tests/unit/transform/transformation_pipeline_comprehensive_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/field_transformations.h"
#include "core/interfaces.h"
#include "common/configuration.h"
#include "test_helpers.h"
#include <memory>
#include <chrono>

namespace omop::transform::test {

class TransformationPipelineComprehensiveTest : public ::testing::Test {
protected:
    void SetUp() override {
        TransformationRegistrar::RegisterAll();
        context_ = std::make_unique<core::ProcessingContext>();
        engine_ = std::make_unique<TransformationEngine>();
        
        // Set up comprehensive configuration
        setupComprehensiveConfig();
    }

    void TearDown() override {
        engine_.reset();
        context_.reset();
    }

    void setupComprehensiveConfig() {
        YAML::Node config;
        
        // Database configuration
        config["source_database"]["type"] = "postgresql";
        config["source_database"]["host"] = "localhost";
        config["source_database"]["port"] = 5432;
        config["source_database"]["database"] = "test_source";
        config["source_database"]["username"] = "test_user";
        config["source_database"]["password"] = "test_pass";
        
        config["target_database"]["type"] = "postgresql";
        config["target_database"]["host"] = "localhost";
        config["target_database"]["port"] = 5432;
        config["target_database"]["database"] = "test_target";
        config["target_database"]["username"] = "test_user";
        config["target_database"]["password"] = "test_pass";
        
        // Complex table mapping with multiple transformation types
        config["table_mappings"]["patient"]["source_table"] = "patient_data";
        config["table_mappings"]["patient"]["target_table"] = "person";
        
        // Transformation rules covering all types
        YAML::Node transformations;
        
        // Direct mapping
        transformations.push_back(createTransformRule("patient_id", "person_id", "direct"));
        
        // Date transformation with validation
        auto date_rule = createTransformRule("birth_date", "birth_datetime", "date_transform");
        date_rule["parameters"]["format"] = "%Y-%m-%d";
        date_rule["parameters"]["output_format"] = "%Y-%m-%d %H:%M:%S";
        date_rule["parameters"]["add_time"] = true;
        transformations.push_back(date_rule);
        
        // Numeric transformation with range validation
        auto numeric_rule = createTransformRule("age", "age_at_enrollment", "numeric_transform");
        numeric_rule["operation"] = "multiply";
        numeric_rule["operand"] = 365.25; // Convert years to days
        numeric_rule["min_value"] = 0;
        numeric_rule["max_value"] = 50000; // ~137 years
        transformations.push_back(numeric_rule);
        
        // String whitespace normalization transformation
        auto name_rule = createTransformRule("full_name", "person_source_value", "string_manipulation");
        name_rule["operation"] = "normalize_whitespace";
        transformations.push_back(name_rule);
        
        // Conditional transformation with multiple conditions
        auto status_rule = createTransformRule("patient_status", "status_concept_id", "conditional");
        YAML::Node conditions;
        conditions[0]["operator"] = "equals";
        conditions[0]["value"] = "ACTIVE";
        conditions[0]["then"] = "4188539";
        conditions[1]["operator"] = "equals";
        conditions[1]["value"] = "INACTIVE";
        conditions[1]["then"] = "4188540";
        conditions[2]["operator"] = "equals";
        conditions[2]["value"] = "DECEASED";
        conditions[2]["then"] = "4216643";
        status_rule["conditions"] = conditions;
        status_rule["default"] = "0";
        transformations.push_back(status_rule);
        
        config["table_mappings"]["patient"]["transformations"] = transformations;
        
        // Add comprehensive filters
        YAML::Node filters;
        filters.push_back(createFilter("patient_id", "not_null"));
        filters.push_back(createFilter("birth_date", "not_null"));
        filters.push_back(createFilter("age", "greater_than", "0"));
        config["table_mappings"]["patient"]["filters"] = filters;
        
        // Add comprehensive validations
        YAML::Node validations;
        validations.push_back(createValidation("person_id", "required"));
        validations.push_back(createValidation("birth_datetime", "date_format", "%Y-%m-%d %H:%M:%S"));
        validations.push_back(createValidation("age_at_enrollment", "range", "0", "50000"));
        config["table_mappings"]["patient"]["validations"] = validations;
        
        config_ = config;
        
        // Load configuration
        auto& config_mgr = common::Config::instance();
        config_mgr.load_config_from_string(YAML::Dump(config));
    }

    YAML::Node createTransformRule(const std::string& source, const std::string& target, 
                                   const std::string& type) {
        YAML::Node rule;
        rule["source_column"] = source;
        rule["target_column"] = target;
        rule["type"] = type;
        return rule;
    }

    YAML::Node createFilter(const std::string& field, const std::string& op, 
                           const std::string& value = "") {
        YAML::Node filter;
        filter["field"] = field;
        filter["operator"] = op;
        if (!value.empty()) filter["value"] = value;
        return filter;
    }

    YAML::Node createValidation(const std::string& field, const std::string& type,
                               const std::string& param1 = "", const std::string& param2 = "") {
        YAML::Node validation;
        validation["field"] = field;
        validation["type"] = type;
        if (!param1.empty()) validation["param1"] = param1;
        if (!param2.empty()) validation["param2"] = param2;
        return validation;
    }

    std::unique_ptr<core::ProcessingContext> context_;
    std::unique_ptr<TransformationEngine> engine_;
    YAML::Node config_;
};

// Test complete transformation pipeline with all transformation types
TEST_F(TransformationPipelineComprehensiveTest, CompleteTransformationPipeline) {
    std::unordered_map<std::string, std::any> engine_config;
    engine_config["table_name"] = std::string("patient");
    engine_->initialize(engine_config, *context_);
    
    // Create comprehensive input record
    core::Record input;
    input.setField("patient_id", 12345);
    input.setField("birth_date", std::string("1990-03-15"));
    input.setField("age", 34);
    input.setField("full_name", std::string("  John   Michael   Doe  "));
    input.setField("patient_status", std::string("ACTIVE"));
    
    // Transform record
    auto result = engine_->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto output = result.value();
    
    // Verify all transformations
    EXPECT_EQ(12345, std::any_cast<int>(output.getField("person_id")));
    EXPECT_EQ("1990-03-15 00:00:00", std::any_cast<std::string>(output.getField("birth_datetime")));
    EXPECT_DOUBLE_EQ(12418.5, std::any_cast<double>(output.getField("age_at_enrollment"))); // 34 * 365.25
    EXPECT_EQ("John Michael Doe", std::any_cast<std::string>(output.getField("person_source_value")));
    EXPECT_EQ("4188539", std::any_cast<std::string>(output.getField("status_concept_id")));
}

// Test error handling across transformation types
TEST_F(TransformationPipelineComprehensiveTest, ErrorHandlingAcrossTransformations) {
    std::unordered_map<std::string, std::any> engine_config;
    engine_config["table_name"] = std::string("patient");
    engine_->initialize(engine_config, *context_);
    
    // Set error threshold
    context_->set_error_threshold(0.2); // 20% error threshold
    
    core::RecordBatch batch;
    
    // Record 1: Valid
    core::Record record1;
    record1.setField("patient_id", 1);
    record1.setField("birth_date", std::string("1990-01-01"));
    record1.setField("age", 34);
    record1.setField("full_name", std::string("Valid Name"));
    record1.setField("patient_status", std::string("ACTIVE"));
    batch.addRecord(record1);
    
    // Record 2: Invalid date
    core::Record record2;
    record2.setField("patient_id", 2);
    record2.setField("birth_date", std::string("invalid-date"));
    record2.setField("age", 25);
    record2.setField("full_name", std::string("Name Two"));
    record2.setField("patient_status", std::string("INACTIVE"));
    batch.addRecord(record2);
    
    // Record 3: Invalid numeric value
    core::Record record3;
    record3.setField("patient_id", 3);
    record3.setField("birth_date", std::string("1985-05-20"));
    record3.setField("age", -5); // Negative age
    record3.setField("full_name", std::string("Name Three"));
    record3.setField("patient_status", std::string("ACTIVE"));
    batch.addRecord(record3);
    
    // Record 4: Valid
    core::Record record4;
    record4.setField("patient_id", 4);
    record4.setField("birth_date", std::string("2000-12-31"));
    record4.setField("age", 24);
    record4.setField("full_name", std::string("Name Four"));
    record4.setField("patient_status", std::string("DECEASED"));
    batch.addRecord(record4);
    
    // Transform batch
    auto result_batch = engine_->transform_batch(batch, *context_);
    
    // Should have processed some records despite errors
    EXPECT_GT(result_batch.size(), 0);
    EXPECT_LT(result_batch.size(), batch.size()); // Some filtered out
    
    // Check error tracking
    EXPECT_GT(context_->error_count(), 0);
    
    // Verify statistics
    auto stats = engine_->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["transformation_errors"]), 0);
    EXPECT_GT(std::any_cast<size_t>(stats["records_filtered"]), 0);
}

// Test transformation performance with large batch
TEST_F(TransformationPipelineComprehensiveTest, TransformationPerformance) {
    std::unordered_map<std::string, std::any> engine_config;
    engine_config["table_name"] = std::string("patient");
    engine_->initialize(engine_config, *context_);
    
    const size_t batch_size = 10000;
    core::RecordBatch batch;
    batch.reserve(batch_size);
    
    // Create large batch
    for (size_t i = 0; i < batch_size; ++i) {
        core::Record record;
        record.setField("patient_id", static_cast<int>(i));
        record.setField("birth_date", std::string("1990-01-01"));
        record.setField("age", static_cast<int>(20 + (i % 60)));
        record.setField("full_name", std::string("Patient Name " + std::to_string(i)));
        record.setField("patient_status", i % 3 == 0 ? "ACTIVE" : "INACTIVE");
        batch.addRecord(record);
    }
    
    auto start = std::chrono::steady_clock::now();
    auto result_batch = engine_->transform_batch(batch, *context_);
    auto duration = std::chrono::steady_clock::now() - start;
    
    // All records should be transformed (no errors)
    EXPECT_EQ(batch_size, result_batch.size());
    
    // Performance check - should complete in reasonable time
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    double records_per_second = (batch_size * 1000.0) / ms;
    
    // Should process at least 1000 records per second
    EXPECT_GT(records_per_second, 1000.0);
    
    // Verify a sample of results
    auto& first_record = result_batch.getRecord(0);
    EXPECT_EQ(0, std::any_cast<int>(first_record.getField("person_id")));
    EXPECT_TRUE(first_record.hasField("birth_datetime"));
    EXPECT_TRUE(first_record.hasField("age_at_enrollment"));
}

// Test transformation metadata and traceability
TEST_F(TransformationPipelineComprehensiveTest, TransformationMetadata) {
    std::unordered_map<std::string, std::any> engine_config;
    engine_config["table_name"] = std::string("patient");
    engine_config["enable_metadata"] = true;
    engine_->initialize(engine_config, *context_);
    
    // Enable detailed tracking
    context_->set_job_id("test_job_123");
    
    core::Record input;
    input.setField("patient_id", 999);
    input.setField("birth_date", std::string("1995-06-15"));
    input.setField("age", 29);
    input.setField("full_name", std::string("Test Patient"));
    input.setField("patient_status", std::string("ACTIVE"));
    
    // Set input metadata
    core::Record::RecordMetadata metadata;
    metadata.source_table = "patient_data";
    metadata.source_row_number = 12345;
    metadata.extraction_time = std::chrono::system_clock::now();
    metadata.record_id = "REC_999";
    input.setMetadata(metadata);
    
    auto result = engine_->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto output = result.value();
    
    // Check metadata preservation
    auto output_metadata = output.getMetadata();
    EXPECT_EQ("patient_data", output_metadata.source_table);
    EXPECT_EQ("person", output_metadata.target_table);
    EXPECT_EQ(12345, output_metadata.source_row_number);
    EXPECT_EQ("REC_999", output_metadata.record_id);
    
    // Check field metadata if supported
    auto field_metadata = output.getFieldMetadata("birth_datetime");
    if (field_metadata.has_value()) {
        EXPECT_EQ("birth_date", field_metadata->source_column);
        EXPECT_EQ("date_transform", field_metadata->description);
    }
}

// Test complex validation scenarios
TEST_F(TransformationPipelineComprehensiveTest, ComplexValidationScenarios) {
    std::unordered_map<std::string, std::any> engine_config;
    engine_config["table_name"] = std::string("patient");
    engine_->initialize(engine_config, *context_);
    
    // Enable strict validation
    context_->set_data("validate_records", true);
    context_->set_data("strict_validation", true);
    
    // Test various validation failures
    std::vector<core::Record> test_records;
    
    // Missing required field
    core::Record record1;
    record1.setField("birth_date", std::string("1990-01-01"));
    record1.setField("age", 34);
    test_records.push_back(record1);
    
    // Invalid date format
    core::Record record2;
    record2.setField("patient_id", 2);
    record2.setField("birth_date", std::string("90-01-01")); // Wrong format
    record2.setField("age", 34);
    test_records.push_back(record2);
    
    // Out of range value
    core::Record record3;
    record3.setField("patient_id", 3);
    record3.setField("birth_date", std::string("1990-01-01"));
    record3.setField("age", 200); // Too old
    test_records.push_back(record3);
    
    // Process each record and verify validation
    for (const auto& record : test_records) {
        auto result = engine_->transform(record, *context_);
        
        // With strict validation, all should fail
        EXPECT_FALSE(result.has_value());
    }
    
    // Check validation statistics
    auto stats = engine_->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["validation_errors"]), 0);
}

} // namespace omop::transform::test