// tests/unit/transform/validation_engine_test.cpp

#include <gtest/gtest.h>
#include "transform/validation_engine.h"
#include "transform/vocabulary_service.h"
#include "core/interfaces.h"
#include <memory>
#include <yaml-cpp/yaml.h>
#include <chrono>
#include <cmath>
#include <limits>

namespace omop::transform::test {

class ValidationEngineTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        engine_ = create_validation_engine();
    }

    void TearDown() override {
        engine_.reset();
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    std::unique_ptr<IValidationEngine> engine_;
};

// Test validation engine initialization
TEST_F(ValidationEngineTest, ValidationEngineInitialization) {
    EXPECT_NE(nullptr, engine_);
    
    // Should have no rules initially
    auto types = engine_->get_registered_types();
    EXPECT_FALSE(types.empty()); // Should have built-in validators
}

// Test registering custom validator
TEST_F(ValidationEngineTest, RegisterCustomValidator) {
    // Create a custom validator factory
    auto factory = []() {
        class TestValidator : public IFieldValidator {
        public:
            omop::common::ValidationResult validate(const std::any& value,
                                          core::ProcessingContext& context) override {
                omop::common::ValidationResult result;
                if (value.type() == typeid(std::string)) {
                    std::string str_val = std::any_cast<std::string>(value);
                    if (str_val == "invalid") {
                        result.add_error(get_field_name(), "Test validation failed", "test_rule");
                    }
                }
                return result;
            }
            
            std::string get_type() const override { return "test_validator"; }
            
            void configure(const YAML::Node& params) override {}
            
            const std::string& get_field_name() const override { return field_name_; }
            
            void set_field_name(const std::string& name) override { field_name_ = name; }
            
        private:
            std::string field_name_;
        };
        return std::make_unique<TestValidator>();
    };
    
    engine_->register_validator("test_validator", factory);
    
    auto types = engine_->get_registered_types();
    EXPECT_TRUE(std::find(types.begin(), types.end(), "test_validator") != types.end());
}

// Test loading validation rules
TEST_F(ValidationEngineTest, LoadValidationRules) {
    YAML::Node config;
    
    // Rule for field1 - required
    YAML::Node rule1;
    rule1["field"] = "field1";
    rule1["validators"][0]["type"] = "required";
    rule1["validators"][0]["allow_empty_string"] = false;
    
    // Rule for field2 - data type
    YAML::Node rule2;
    rule2["field"] = "field2";
    rule2["validators"][0]["type"] = "data_type";
    rule2["validators"][0]["expected_type"] = "integer";
    
    config["rules"].push_back(rule1);
    config["rules"].push_back(rule2);
    
    EXPECT_NO_THROW(engine_->load_validation_rules(config));
}

// Test validating a record
TEST_F(ValidationEngineTest, ValidateRecord) {
    // Load rules
    YAML::Node config;
    YAML::Node rule;
    rule["field"] = "person_id";
    rule["validators"][0]["type"] = "required";
    config["rules"].push_back(rule);
    
    engine_->load_validation_rules(config);
    
    // Create record without required field
    core::Record record;
    
    auto result = engine_->validate_record(record, *context_);
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(1, result.errors().size());
    EXPECT_EQ("person_id", result.errors()[0].field_name);
}

// Test validating a single field
TEST_F(ValidationEngineTest, ValidateField) {
    // Register and configure a range validator
    YAML::Node config;
    YAML::Node rule;
    rule["field"] = "age";
    rule["validators"][0]["type"] = "range";
    rule["validators"][0]["min"] = 0;
    rule["validators"][0]["max"] = 150;
    config["rules"].push_back(rule);
    
    engine_->load_validation_rules(config);
    
    // Test valid value
    auto result = engine_->validate_field("age", 25, *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test invalid value
    result = engine_->validate_field("age", -5, *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test required field validator
TEST_F(ValidationEngineTest, RequiredFieldValidator) {
    RequiredFieldValidator validator;
    validator.set_field_name("test_field");
    
    YAML::Node params;
    params["allow_empty_string"] = false;
    validator.configure(params);
    
    // Test with value present
    auto result = validator.validate(std::string("value"), *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test with empty string
    result = validator.validate(std::string(""), *context_);
    EXPECT_FALSE(result.is_valid());
    
    // Test with null value
    result = validator.validate(std::any{}, *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test data type validator
TEST_F(ValidationEngineTest, DataTypeValidator) {
    DataTypeValidator validator;
    validator.set_field_name("test_field");
    
    YAML::Node params;
    params["expected_type"] = "integer";
    params["required"] = true;
    validator.configure(params);
    
    // Test with correct type
    auto result = validator.validate(42, *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test with wrong type
    result = validator.validate(std::string("not a number"), *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test date format validation
TEST_F(ValidationEngineTest, DataTypeValidatorDateFormat) {
    DataTypeValidator validator;
    validator.set_field_name("birth_date");
    
    YAML::Node params;
    params["expected_type"] = "date";
    params["date_formats"].push_back("%Y-%m-%d");
    params["date_formats"].push_back("%m/%d/%Y");
    validator.configure(params);
    
    // Test valid date format
    auto result = validator.validate(std::string("2024-03-15"), *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test alternative format
    result = validator.validate(std::string("03/15/2024"), *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test invalid date format
    result = validator.validate(std::string("invalid-date"), *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test range validator
TEST_F(ValidationEngineTest, RangeValidator) {
    RangeValidator validator;
    validator.set_field_name("score");
    
    YAML::Node params;
    params["min"] = 0.0;
    params["max"] = 100.0;
    validator.configure(params);
    
    // Test value in range
    auto result = validator.validate(50.0, *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test value below range
    result = validator.validate(-10.0, *context_);
    EXPECT_FALSE(result.is_valid());
    
    // Test value above range
    result = validator.validate(150.0, *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test pattern validator
TEST_F(ValidationEngineTest, PatternValidator) {
    PatternValidator validator;
    validator.set_field_name("email");
    
    YAML::Node params;
    params["pattern"] = R"(^[\w\.-]+@[\w\.-]+\.\w+$)";
    params["case_sensitive"] = false;
    validator.configure(params);
    
    // Test valid pattern
    auto result = validator.validate(std::string("<EMAIL>"), *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test invalid pattern
    result = validator.validate(std::string("not-an-email"), *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test length validator
TEST_F(ValidationEngineTest, LengthValidator) {
    LengthValidator validator;
    validator.set_field_name("code");
    
    YAML::Node params;
    params["min_length"] = 3;
    params["max_length"] = 10;
    validator.configure(params);
    
    // Test valid length
    auto result = validator.validate(std::string("ABC123"), *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test too short
    result = validator.validate(std::string("AB"), *context_);
    EXPECT_FALSE(result.is_valid());
    
    // Test too long
    result = validator.validate(std::string("ABCDEFGHIJK"), *context_);
    EXPECT_FALSE(result.is_valid());
}

// Test custom validator
TEST_F(ValidationEngineTest, CustomValidator) {
    CustomValidator validator;
    validator.set_field_name("custom_field");
    
    // Set validation function
    validator.set_validation_function([](const std::any& value, core::ProcessingContext& ctx) {
        if (value.type() == typeid(int)) {
            return std::any_cast<int>(value) % 2 == 0; // Even numbers only
        }
        return false;
    });
    
    YAML::Node params;
    params["error_message"] = "Value must be an even number";
    validator.configure(params);
    
    // Test even number
    auto result = validator.validate(4, *context_);
    EXPECT_TRUE(result.is_valid());
    
    // Test odd number
    result = validator.validate(5, *context_);
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ("Value must be an even number", result.errors()[0].error_message);
}

// Test clearing validation rules
TEST_F(ValidationEngineTest, ClearRules) {
    // Load some rules
    YAML::Node config;
    YAML::Node rule;
    rule["field"] = "test";
    rule["validators"][0]["type"] = "required";
    config["rules"].push_back(rule);
    
    engine_->load_validation_rules(config);
    
    // Clear rules
    engine_->clear_rules();
    
    // Validation should pass now (no rules)
    core::Record record;
    auto result = engine_->validate_record(record, *context_);
    EXPECT_TRUE(result.is_valid());
}

// Test validation with multiple validators per field
TEST_F(ValidationEngineTest, MultipleValidatorsPerField) {
    YAML::Node config;
    YAML::Node rule;
    rule["field"] = "age";
    
    // Add required validator
    rule["validators"][0]["type"] = "required";
    
    // Add data type validator
    rule["validators"][1]["type"] = "data_type";
    rule["validators"][1]["expected_type"] = "integer";
    
    // Add range validator
    rule["validators"][2]["type"] = "range";
    rule["validators"][2]["min"] = 0;
    rule["validators"][2]["max"] = 150;
    
    config["rules"].push_back(rule);
    engine_->load_validation_rules(config);
    
    core::Record record;
    record.setField("age", std::string("invalid"));
    
    auto result = engine_->validate_record(record, *context_);
    EXPECT_FALSE(result.is_valid());
    // Should have at least one error (data type mismatch)
    EXPECT_GT(result.errors().size(), 0);
}

// Test complex validation scenario
TEST_F(ValidationEngineTest, ComplexValidationScenario) {
    YAML::Node config;
    
    // Person ID - required integer
    YAML::Node rule1;
    rule1["field"] = "person_id";
    rule1["validators"][0]["type"] = "required";
    rule1["validators"][1]["type"] = "data_type";
    rule1["validators"][1]["expected_type"] = "integer";
    
    // Birth date - required date
    YAML::Node rule2;
    rule2["field"] = "birth_date";
    rule2["validators"][0]["type"] = "required";
    rule2["validators"][1]["type"] = "data_type";
    rule2["validators"][1]["expected_type"] = "date";
    rule2["validators"][1]["date_formats"].push_back("%Y-%m-%d");
    
    // Email - optional but must match pattern if present
    YAML::Node rule3;
    rule3["field"] = "email";
    rule3["validators"][0]["type"] = "pattern";
    rule3["validators"][0]["pattern"] = R"(^[\w\.-]+@[\w\.-]+\.\w+$)";
    
    config["rules"].push_back(rule1);
    config["rules"].push_back(rule2);
    config["rules"].push_back(rule3);
    
    engine_->load_validation_rules(config);
    
    // Create valid record
    core::Record valid_record;
    valid_record.setField("person_id", 12345);
    valid_record.setField("birth_date", std::string("1990-01-15"));
    valid_record.setField("email", std::string("<EMAIL>"));
    
    auto result = engine_->validate_record(valid_record, *context_);
    
    // Debug: Print validation errors if any
    if (!result.is_valid()) {
        std::cout << "Validation failed with " << result.errors().size() << " errors:" << std::endl;
        for (const auto& error : result.errors()) {
            std::cout << "  Field: " << error.field_name << ", Error: " << error.error_message << ", Rule: " << error.rule_name << std::endl;
        }
    }
    
    EXPECT_TRUE(result.is_valid());
    
    // Create invalid record
    core::Record invalid_record;
    invalid_record.setField("person_id", std::string("not a number"));
    invalid_record.setField("birth_date", std::string("invalid-date"));
    invalid_record.setField("email", std::string("not-an-email"));
    
    result = engine_->validate_record(invalid_record, *context_);
    EXPECT_FALSE(result.is_valid());
    EXPECT_GE(result.errors().size(), 3); // At least 3 validation errors
}

} // namespace omop::transform::test