# Security unit tests

set(SECURITY_TEST_SOURCES
    audit_logger_test.cpp
    auth_manager_test.cpp
    authorization_test.cpp
)

# Create test executable
add_executable(security_unit_tests ${SECURITY_TEST_SOURCES})

target_link_libraries(security_unit_tests
    PRIVATE
        omop_security
        omop_common
        gtest
        gtest_main
        gmock
        Threads::Threads
        $<$<PLATFORM_ID:Windows>:bcrypt>
        $<$<NOT:$<PLATFORM_ID:Windows>>:OpenSSL::Crypto>
)

target_include_directories(security_unit_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
)

# Add tests
include(GoogleTest)
gtest_discover_tests(security_unit_tests
    PROPERTIES
        LABELS "unit;security"
        TIMEOUT 30
)

# Add to CTest
add_test(NAME security_unit_tests COMMAND security_unit_tests) 