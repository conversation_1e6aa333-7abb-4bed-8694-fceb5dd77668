#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "security/auth_manager.h"
#include <chrono>
#include <thread>

namespace omop::security::test {

using namespace std::chrono_literals;
using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;

class AuthManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        auth_manager_ = std::make_unique<AuthManager>();
        
        // Default configuration
        config_.enabled = true;
        config_.token_lifetime = 1h;
        config_.refresh_token_lifetime = 7 * 24h;
        config_.create_default_admin = false;
    }
    
    void TearDown() override {
        // Cleanup
    }
    
    // Helper to create test user
    UserInfo create_test_user(const std::string& user_id = "test_user") {
        UserInfo user;
        user.user_id = user_id;
        user.username = user_id;
        user.email = user_id + "@example.com";
        user.roles = {"user"};
        user.status = UserStatus::Active;
        user.created_at = std::chrono::system_clock::now();
        return user;
    }
    
    // Helper to create test credentials
    AuthCredentials create_test_credentials(const std::string& username = "test_user", 
                                          const std::string& password = "TestPass123!") {
        AuthCredentials creds;
        creds.username = username;
        creds.password = password;
        return creds;
    }
    
    std::unique_ptr<AuthManager> auth_manager_;
    AuthConfig config_;
};

// Tests initialization with various configurations
TEST_F(AuthManagerTest, InitializationSuccess) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    auto retrieved_config = auth_manager_->get_config();
    EXPECT_EQ(retrieved_config.enabled, config_.enabled);
    EXPECT_EQ(retrieved_config.token_lifetime, config_.token_lifetime);
}

// Tests initialization with disabled authentication
TEST_F(AuthManagerTest, InitializationDisabled) {
    config_.enabled = false;
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Should not authenticate when disabled
    auto creds = create_test_credentials();
    auto result = auth_manager_->authenticate(creds);
    EXPECT_EQ(result.first, AuthResult::SystemError);
}

// Tests initialization with default admin user
TEST_F(AuthManagerTest, InitializationWithDefaultAdmin) {
    config_.create_default_admin = true;
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Should be able to authenticate with default admin
    AuthCredentials admin_creds;
    admin_creds.username = "admin";
    admin_creds.password = "admin123";
    
    auto result = auth_manager_->authenticate(admin_creds);
    EXPECT_EQ(result.first, AuthResult::Success);
    EXPECT_TRUE(result.second.has_value());
    
    auto token = result.second.value();
    EXPECT_EQ(token.subject, "admin");
    EXPECT_EQ(token.issuer, "omop-etl");
}

// Tests user creation and authentication
TEST_F(AuthManagerTest, UserCreationAndAuthentication) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    // Authenticate with correct credentials
    auto creds = create_test_credentials();
    auto result = auth_manager_->authenticate(creds);
    EXPECT_EQ(result.first, AuthResult::Success);
    EXPECT_TRUE(result.second.has_value());
    
    auto token = result.second.value();
    EXPECT_EQ(token.subject, user.user_id);
    EXPECT_EQ(token.issuer, "omop-etl");
    EXPECT_FALSE(token.scopes.empty());
}

// Tests authentication with invalid credentials
TEST_F(AuthManagerTest, AuthenticationInvalidCredentials) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    // Try to authenticate with wrong password
    auto creds = create_test_credentials("test_user", "WrongPassword");
    auto result = auth_manager_->authenticate(creds);
    EXPECT_EQ(result.first, AuthResult::InvalidCredentials);
    EXPECT_FALSE(result.second.has_value());
}

// Tests authentication with non-existent user
TEST_F(AuthManagerTest, AuthenticationNonExistentUser) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    auto creds = create_test_credentials("nonexistent", "password");
    auto result = auth_manager_->authenticate(creds);
    EXPECT_EQ(result.first, AuthResult::InvalidCredentials);
    EXPECT_FALSE(result.second.has_value());
}

// Tests token validation
TEST_F(AuthManagerTest, TokenValidation) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user and get token
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    auto creds = create_test_credentials();
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
    
    auto token = auth_result.second.value();
    
    // Validate token
    auto validation_result = auth_manager_->validate_token(token.token);
    EXPECT_EQ(validation_result.first, AuthResult::Success);
    EXPECT_TRUE(validation_result.second.has_value());
    
    auto validated_user = validation_result.second.value();
    EXPECT_EQ(validated_user.user_id, user.user_id);
    EXPECT_EQ(validated_user.username, user.username);
}

// Tests token validation with invalid token
TEST_F(AuthManagerTest, TokenValidationInvalidToken) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    auto result = auth_manager_->validate_token("invalid_token");
    EXPECT_EQ(result.first, AuthResult::InvalidToken);
    EXPECT_FALSE(result.second.has_value());
}

// Tests token validation with expired token
TEST_F(AuthManagerTest, TokenValidationExpiredToken) {
    config_.token_lifetime = 1ms; // Very short lifetime
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user and get token
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    auto creds = create_test_credentials();
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
    
    auto token = auth_result.second.value();
    
    // Wait for token to expire
    std::this_thread::sleep_for(10ms);
    
    // Try to validate expired token
    auto validation_result = auth_manager_->validate_token(token.token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenExpired);
    EXPECT_FALSE(validation_result.second.has_value());
}

// Tests token refresh
TEST_F(AuthManagerTest, TokenRefresh) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user and get token
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    auto creds = create_test_credentials();
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
    
    auto original_token = auth_result.second.value();
    
    // Refresh token
    auto refresh_result = auth_manager_->refresh_token(original_token.token);
    EXPECT_EQ(refresh_result.first, AuthResult::Success);
    EXPECT_TRUE(refresh_result.second.has_value());
    
    auto new_token = refresh_result.second.value();
    EXPECT_NE(new_token.token, original_token.token);
    EXPECT_EQ(new_token.subject, original_token.subject);
    EXPECT_EQ(new_token.issuer, original_token.issuer);
}

// Tests token revocation
TEST_F(AuthManagerTest, TokenRevocation) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user and get token
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    auto creds = create_test_credentials();
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
    
    auto token = auth_result.second.value();
    
    // Revoke token
    EXPECT_TRUE(auth_manager_->revoke_token(token.token));
    
    // Try to validate revoked token
    auto validation_result = auth_manager_->validate_token(token.token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);
    EXPECT_FALSE(validation_result.second.has_value());
}

// Tests user management operations
TEST_F(AuthManagerTest, UserManagement) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    // Get user info
    auto user_info = auth_manager_->get_user_info(user.user_id);
    EXPECT_TRUE(user_info.has_value());
    EXPECT_EQ(user_info->user_id, user.user_id);
    EXPECT_EQ(user_info->username, user.username);
    
    // Update user
    user.email = "<EMAIL>";
    user.roles = {"admin", "user"};
    EXPECT_TRUE(auth_manager_->update_user(user));
    
    // Verify update
    auto updated_info = auth_manager_->get_user_info(user.user_id);
    EXPECT_TRUE(updated_info.has_value());
    EXPECT_EQ(updated_info->email, "<EMAIL>");
    EXPECT_EQ(updated_info->roles.size(), 2);
    
    // Lock user
    EXPECT_TRUE(auth_manager_->lock_user(user.user_id));
    
    // Try to authenticate with locked user
    auto creds = create_test_credentials();
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::AccountLocked);
    
    // Unlock user
    EXPECT_TRUE(auth_manager_->unlock_user(user.user_id));
    
    // Should be able to authenticate again
    auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
    
    // Delete user
    EXPECT_TRUE(auth_manager_->delete_user(user.user_id));
    
    // User should no longer exist
    auto deleted_info = auth_manager_->get_user_info(user.user_id);
    EXPECT_FALSE(deleted_info.has_value());
}

// Tests password management
TEST_F(AuthManagerTest, PasswordManagement) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    // Change password
    EXPECT_TRUE(auth_manager_->change_password(user.user_id, "TestPass123!", "NewPass456!"));
    
    // Should be able to authenticate with new password
    AuthCredentials new_creds;
    new_creds.username = user.username;
    new_creds.password = "NewPass456!";
    
    auto auth_result = auth_manager_->authenticate(new_creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
    
    // Should not be able to authenticate with old password
    AuthCredentials old_creds;
    old_creds.username = user.username;
    old_creds.password = "TestPass123!";
    
    auth_result = auth_manager_->authenticate(old_creds);
    EXPECT_EQ(auth_result.first, AuthResult::InvalidCredentials);
    
    // Reset password
    EXPECT_TRUE(auth_manager_->reset_password(user.user_id, "ResetPass789!"));
    
    // Should be able to authenticate with reset password
    AuthCredentials reset_creds;
    reset_creds.username = user.username;
    reset_creds.password = "ResetPass789!";
    
    auth_result = auth_manager_->authenticate(reset_creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
}

// Tests password strength validation
TEST_F(AuthManagerTest, PasswordStrengthValidation) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    auto user = create_test_user();
    
    // Weak passwords should be rejected
    EXPECT_FALSE(auth_manager_->create_user(user, "weak"));
    EXPECT_FALSE(auth_manager_->create_user(user, "12345678"));
    EXPECT_FALSE(auth_manager_->create_user(user, "password"));
    EXPECT_FALSE(auth_manager_->create_user(user, "PASSWORD"));
    
    // Strong password should be accepted
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
}

// Tests session management
TEST_F(AuthManagerTest, SessionManagement) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    // Create multiple sessions
    auto creds = create_test_credentials();
    auto auth_result1 = auth_manager_->authenticate(creds);
    auto auth_result2 = auth_manager_->authenticate(creds);
    auto auth_result3 = auth_manager_->authenticate(creds);
    
    EXPECT_EQ(auth_result1.first, AuthResult::Success);
    EXPECT_EQ(auth_result2.first, AuthResult::Success);
    EXPECT_EQ(auth_result3.first, AuthResult::Success);
    
    // Get active sessions
    auto sessions = auth_manager_->get_active_sessions(user.user_id);
    EXPECT_EQ(sessions.size(), 3);
    
    // Terminate specific session
    EXPECT_TRUE(auth_manager_->terminate_session(user.user_id, auth_result1.second->token));
    
    // Verify session was terminated
    auto validation_result = auth_manager_->validate_token(auth_result1.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);
    
    // Other sessions should still be valid
    validation_result = auth_manager_->validate_token(auth_result2.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::Success);
    
    // Terminate all sessions
    EXPECT_TRUE(auth_manager_->terminate_all_sessions(user.user_id));
    
    // All sessions should be terminated
    validation_result = auth_manager_->validate_token(auth_result2.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);
    
    validation_result = auth_manager_->validate_token(auth_result3.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);
}

// Tests concurrent authentication
TEST_F(AuthManagerTest, ConcurrentAuthentication) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    const int num_threads = 10;
    const int auths_per_thread = 5;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, &success_count, auths_per_thread]() {
            auto creds = create_test_credentials();
            for (int j = 0; j < auths_per_thread; ++j) {
                auto result = auth_manager_->authenticate(creds);
                if (result.first == AuthResult::Success) {
                    success_count++;
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(success_count.load(), num_threads * auths_per_thread);
}

// Tests statistics
TEST_F(AuthManagerTest, GetStatistics) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create some users and sessions
    for (int i = 0; i < 3; ++i) {
        auto user = create_test_user("user_" + std::to_string(i));
        auth_manager_->create_user(user, "TestPass123!");
        
        auto creds = create_test_credentials("user_" + std::to_string(i), "TestPass123!");
        auth_manager_->authenticate(creds);
    }
    
    auto stats = auth_manager_->get_statistics();
    EXPECT_FALSE(stats.empty());
    
    // Check that statistics contain expected keys
    EXPECT_TRUE(stats.find("total_users") != stats.end());
    EXPECT_TRUE(stats.find("active_tokens") != stats.end());
    EXPECT_TRUE(stats.find("active_sessions") != stats.end());
}

// Tests error handling with invalid user operations
TEST_F(AuthManagerTest, InvalidUserOperations) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Try to get non-existent user
    auto user_info = auth_manager_->get_user_info("nonexistent");
    EXPECT_FALSE(user_info.has_value());
    
    // Try to update non-existent user
    auto user = create_test_user("nonexistent");
    EXPECT_FALSE(auth_manager_->update_user(user));
    
    // Try to delete non-existent user
    EXPECT_FALSE(auth_manager_->delete_user("nonexistent"));
    
    // Try to lock non-existent user
    EXPECT_FALSE(auth_manager_->lock_user("nonexistent"));
    
    // Try to unlock non-existent user
    EXPECT_FALSE(auth_manager_->unlock_user("nonexistent"));
}

// Tests error handling with invalid password operations
TEST_F(AuthManagerTest, InvalidPasswordOperations) {
    EXPECT_TRUE(auth_manager_->initialize(config_));
    
    // Create user
    auto user = create_test_user();
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));
    
    // Try to change password with wrong old password
    EXPECT_FALSE(auth_manager_->change_password(user.user_id, "WrongPassword", "NewPassword"));
    
    // Try to change password with weak new password
    EXPECT_FALSE(auth_manager_->change_password(user.user_id, "TestPass123!", "weak"));
    
    // Try to reset password with weak password
    EXPECT_FALSE(auth_manager_->reset_password(user.user_id, "weak"));
}

} // namespace omop::security::test 