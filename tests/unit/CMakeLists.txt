# tests/unit/CMakeLists.txt - Unit test configuration for individual components

# Enable testing
enable_testing()

# Create a consolidated unit test executable for all components
set(ALL_UNIT_TEST_SOURCES "")

# Function to add component unit tests with consolidated approach
function(add_component_unit_tests component_name)
    # Add all remaining arguments as test sources
    list(APPEND ALL_UNIT_TEST_SOURCES ${ARGN})
    
    # Create individual test executable for the component with consistent naming
    create_unit_test_executable(test_omop_${component_name}_unit "${ARGN}")
    
    # Link component-specific libraries
    target_link_libraries(test_omop_${component_name}_unit PRIVATE omop_${component_name} omop_common omop_core spdlog::spdlog nlohmann_json::nlohmann_json)
    
    # Add component-specific include directories
    target_include_directories(test_omop_${component_name}_unit PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/${component_name}
        ${CMAKE_CURRENT_SOURCE_DIR}/${component_name}
        ${CMAKE_BINARY_DIR}/_deps/spdlog-src/include
        ${CMAKE_BINARY_DIR}/_deps/nlohmann_json-src/include
    )
    
    # Set test-specific properties
    set_target_properties(test_omop_${component_name}_unit PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )
    
    # Add coverage flags if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(test_omop_${component_name}_unit PRIVATE --coverage)
        target_link_options(test_omop_${component_name}_unit PRIVATE --coverage)
    endif()
    
    # Create convenience target to run component tests with consistent naming
    add_custom_target(test_omop_${component_name}_tests
        COMMAND ${CMAKE_CTEST_COMMAND} -R "test_omop_${component_name}_unit" --output-on-failure
        DEPENDS test_omop_${component_name}_unit
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Running ${component_name} unit tests"
    )
endfunction()

# Function to create a single consolidated test executable for all unit tests
function(create_consolidated_unit_tests)
    if(ALL_UNIT_TEST_SOURCES)
        # Create a single consolidated test executable
        create_unit_test_executable(test_omop_unit_tests "${ALL_UNIT_TEST_SOURCES}")
        
        # Link all libraries for the consolidated test
        target_link_libraries(test_omop_unit_tests PRIVATE
            omop_common
            omop_core
            omop_cdm
            omop_extract
            omop_transform
            omop_load
            spdlog::spdlog
            nlohmann_json::nlohmann_json
#            omop_service
        )
        
        # Add all include directories
        target_include_directories(test_omop_unit_tests PRIVATE
            ${CMAKE_SOURCE_DIR}/src/lib
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_BINARY_DIR}/_deps/spdlog-src/include
            ${CMAKE_BINARY_DIR}/_deps/nlohmann_json-src/include
        )
        
        # Set properties for consolidated test
        set_target_properties(test_omop_unit_tests PROPERTIES
            CXX_STANDARD 20
            CXX_STANDARD_REQUIRED ON
        )
        
        # Add coverage flags if enabled for consolidated test
        if(ENABLE_COVERAGE)
            target_compile_options(test_omop_unit_tests PRIVATE --coverage)
            target_link_options(test_omop_unit_tests PRIVATE --coverage)
        endif()
        
        # Create convenience target to run consolidated tests
        add_custom_target(test_omop_unit_tests_run
            COMMAND ${CMAKE_CTEST_COMMAND} -R "test_omop_unit_tests" --output-on-failure
            DEPENDS test_omop_unit_tests
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Running consolidated unit tests"
        )
    endif()
endfunction()

# Add subdirectories for each component's unit tests
add_subdirectory(common)
add_subdirectory(cdm)
add_subdirectory(load)
add_subdirectory(core)
add_subdirectory(extract)
add_subdirectory(transform)

# Create consolidated test executable after all components are added
create_consolidated_unit_tests()

# Create convenience target to run all unit tests with consistent naming
add_custom_target(test_omop_unit_tests_all
    COMMAND ${CMAKE_CTEST_COMMAND} -L "unit" --output-on-failure
    DEPENDS test_omop_unit_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all unit tests"
)

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Unit test configuration summary
message(STATUS "Unit Test Configuration:")
message(STATUS "  Test files: ${ALL_UNIT_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit")
message(STATUS "  C++ Standard: 20")
message(STATUS "  Consolidated test executable: test_omop_unit_tests")