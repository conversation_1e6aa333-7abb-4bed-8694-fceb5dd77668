# tests/unit/load/CMakeLists.txt

# Unit tests for OMOP Load library

# Test source files in the load directory
set(LOAD_TEST_SOURCES
    additional_loaders_test.cpp
    additional_loaders_test_complex.cpp
    batch_loader_test.cpp
    database_loader_test.cpp
    # database_integration_test.cpp  # Disabled - requires PostgreSQL
    loader_base_test.cpp
    mmap_batch_loader_tests.cpp
    mmap_simple_test.cpp
)

# Use the consolidated approach from parent CMakeLists.txt
add_component_unit_tests(load ${LOAD_TEST_SOURCES})

# Set longer timeout for load tests (database operations)
set_tests_properties(test_omop_load_unit PROPERTIES
    LABELS "unit;load"
    TIMEOUT 600  # 10 minutes for database operations
)

# Enable sanitizers for load tests if enabled
if(ENABLE_SANITIZERS)
    target_compile_options(test_omop_load_unit PRIVATE
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )
    target_link_options(test_omop_load_unit PRIVATE
        -fsanitize=address,undefined
    )
endif()

