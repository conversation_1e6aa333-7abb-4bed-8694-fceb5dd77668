// Integration tests for database loaders with real database connections

#include <gtest/gtest.h>
#include "load/database_loader.h"
#include "load_test_mocks.h"
#include "extract/postgresql_connector.h"
#ifdef HAVE_MYSQL
#include "extract/mysql_connector.h"
#endif
#include "cdm/omop_tables.h"
#include <cstdlib>
#include <format>

using namespace omop::load;
using namespace omop::load::test;
using namespace omop::core;
using namespace omop::extract;
using namespace omop::cdm;

// Integration test configuration from environment
class DatabaseIntegrationConfig {
public:
    static DatabaseIntegrationConfig& instance() {
        static DatabaseIntegrationConfig config;
        return config;
    }

    bool is_enabled() const { return enabled_; }

    omop::extract::IDatabaseConnection::ConnectionParams postgresql_connection_params() const {
        omop::extract::IDatabaseConnection::ConnectionParams params;
        params.host = pg_host_;
        params.port = pg_port_.empty() ? 5432 : std::stoi(pg_port_);
        params.database = pg_db_;
        params.username = pg_user_;
        params.password = pg_pass_;
        return params;
    }

    omop::extract::IDatabaseConnection::ConnectionParams mysql_connection_params() const {
        omop::extract::IDatabaseConnection::ConnectionParams params;
        params.host = mysql_host_;
        params.port = mysql_port_.empty() ? 3306 : std::stoi(mysql_port_);
        params.database = mysql_db_;
        params.username = mysql_user_;
        params.password = mysql_pass_;
        return params;
    }

private:
    DatabaseIntegrationConfig() {
        // Check if integration tests are enabled
        enabled_ = std::getenv("OMOP_RUN_INTEGRATION_TESTS") != nullptr;

        if (enabled_) {
            // PostgreSQL configuration
            pg_host_ = get_env_or_default("OMOP_PG_HOST", "localhost");
            pg_port_ = get_env_or_default("OMOP_PG_PORT", "5432");
            pg_db_ = get_env_or_default("OMOP_PG_DB", "omop_test");
            pg_user_ = get_env_or_default("OMOP_PG_USER", "omop_user");
            pg_pass_ = get_env_or_default("OMOP_PG_PASSWORD", "omop_pass");

            // MySQL configuration
            mysql_host_ = get_env_or_default("OMOP_MYSQL_HOST", "localhost");
            mysql_port_ = get_env_or_default("OMOP_MYSQL_PORT", "3306");
            mysql_db_ = get_env_or_default("OMOP_MYSQL_DB", "omop_test");
            mysql_user_ = get_env_or_default("OMOP_MYSQL_USER", "omop_user");
            mysql_pass_ = get_env_or_default("OMOP_MYSQL_PASSWORD", "omop_pass");
        }
    }

    std::string get_env_or_default(const char* name, const char* default_value) {
        const char* value = std::getenv(name);
        return value ? value : default_value;
    }

    bool enabled_;
    std::string pg_host_, pg_port_, pg_db_, pg_user_, pg_pass_;
    std::string mysql_host_, mysql_port_, mysql_db_, mysql_user_, mysql_pass_;
};

// Base class for database integration tests
class DatabaseIntegrationTest : public LoaderTestBase {
protected:
    void SetUp() override {
        LoaderTestBase::SetUp();

        auto& config = DatabaseIntegrationConfig::instance();
        if (!config.is_enabled()) {
            GTEST_SKIP() << "Database integration tests disabled. "
                        << "Set OMOP_RUN_INTEGRATION_TESTS=1 to enable.";
        }
    }

    void TearDown() override {
        // Clean up test data
        cleanup_test_data();
        LoaderTestBase::TearDown();
    }

    virtual void cleanup_test_data() = 0;

    // Helper to verify data was loaded
    size_t count_table_rows(std::unique_ptr<IDatabaseConnection>& connection,
                           const std::string& schema,
                           const std::string& table) {
        auto query = std::format("SELECT COUNT(*) FROM {}.{}", schema, table);
        auto result = connection->execute_query(query);

        if (result && result->next()) {
            auto value_opt = result->get_value(size_t(0));
            return std::any_cast<int64_t>(value_opt);
        }
        return 0;
    }

    // Create test schema if needed
    void ensure_test_schema(std::unique_ptr<IDatabaseConnection>& connection,
                           const std::string& schema) {
        try {
            connection->execute_update(
                std::format("CREATE SCHEMA IF NOT EXISTS {}", schema));
        } catch (...) {
            // Schema might already exist
        }
    }

    // Create basic OMOP tables for testing
    void create_test_tables(std::unique_ptr<IDatabaseConnection>& connection,
                           const std::string& schema) {
        // Create person table
        connection->execute_update(std::format(R"(
            CREATE TABLE IF NOT EXISTS {}.person (
                person_id BIGINT PRIMARY KEY,
                gender_concept_id INT NOT NULL,
                year_of_birth INT NOT NULL,
                race_concept_id INT NOT NULL,
                ethnicity_concept_id INT NOT NULL,
                month_of_birth INT,
                day_of_birth INT,
                person_source_value VARCHAR(50)
            )
        )", schema));

        // Create visit_occurrence table
        connection->execute_update(std::format(R"(
            CREATE TABLE IF NOT EXISTS {}.visit_occurrence (
                visit_occurrence_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                visit_concept_id INT NOT NULL,
                visit_start_date TIMESTAMP NOT NULL,
                visit_end_date TIMESTAMP,
                visit_type_concept_id INT NOT NULL,
                FOREIGN KEY (person_id) REFERENCES {}.person(person_id)
            )
        )", schema, schema));

        // Create measurement table
        connection->execute_update(std::format(R"(
            CREATE TABLE IF NOT EXISTS {}.measurement (
                measurement_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                measurement_concept_id INT NOT NULL,
                measurement_date TIMESTAMP NOT NULL,
                measurement_type_concept_id INT NOT NULL,
                value_as_number DECIMAL(10,3),
                value_as_concept_id INT,
                unit_concept_id INT,
                FOREIGN KEY (person_id) REFERENCES {}.person(person_id)
            )
        )", schema, schema));
    }
};

// PostgreSQL integration tests
class PostgreSQLIntegrationTest : public DatabaseIntegrationTest {
protected:
    void SetUp() override {
        DatabaseIntegrationTest::SetUp();

        // Create connection
        connection = std::make_unique<PostgreSQLConnection>();
        connection->connect(DatabaseIntegrationConfig::instance().postgresql_connection_params());

        // Set up test schema and tables
        ensure_test_schema(connection, test_schema_);
        create_test_tables(connection, test_schema_);

        // Create loader
        DatabaseLoaderOptions options;
        options.batch_size = 100;
        options.use_copy_command = true;

        loader = std::make_unique<PostgreSQLLoader>(
            std::make_unique<PostgreSQLConnection>(), options);
    }

    void cleanup_test_data() override {
        if (connection && connection->is_connected()) {
            try {
                connection->execute_update(
                    std::format("DROP SCHEMA IF EXISTS {} CASCADE", test_schema_));
            } catch (...) {
                // Ignore cleanup errors
            }
        }
    }

protected:
    std::unique_ptr<IDatabaseConnection> connection;
    std::unique_ptr<PostgreSQLLoader> loader;
    std::string test_schema_ = "omop_test";
};

// Test PostgreSQL bulk loading with COPY
TEST_F(PostgreSQLIntegrationTest, BulkLoadWithCopy) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("person");
    config["schema_name"] = test_schema_;

    loader->initialize(config, context);

    // Create test data
    const size_t record_count = 1000;
    for (size_t i = 1; i <= record_count; ++i) {
        Record record;
        record.setField("person_id", int64_t(i));
        record.setField("gender_concept_id", int32_t(8507));
        record.setField("year_of_birth", int32_t(1980));
        record.setField("race_concept_id", int32_t(8527));
        record.setField("ethnicity_concept_id", int32_t(38003564));
        loader->load(record, context);
    }

    // Commit to trigger bulk load
    loader->commit(context);

    // Verify data was loaded
    size_t loaded = count_table_rows(connection, test_schema_, "person");
    EXPECT_EQ(loaded, record_count);

    // Check statistics
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), record_count);
}

// Test transaction rollback
TEST_F(PostgreSQLIntegrationTest, TransactionRollback) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("person");
    config["schema_name"] = test_schema_;

    loader->initialize(config, context);

    // Load some data
    for (int i = 1; i <= 50; ++i) {
        Record record;
        record.setField("person_id", int64_t(i));
        record.setField("gender_concept_id", int32_t(8507));
        record.setField("year_of_birth", int32_t(1980));
        record.setField("race_concept_id", int32_t(8527));
        record.setField("ethnicity_concept_id", int32_t(38003564));
        loader->load(record, context);
    }

    // Rollback instead of commit
    loader->rollback(context);

    // Verify no data was persisted
    size_t loaded = count_table_rows(connection, test_schema_, "person");
    EXPECT_EQ(loaded, 0);
}

// Test constraint handling
TEST_F(PostgreSQLIntegrationTest, ConstraintHandling) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("visit_occurrence");
    config["schema_name"] = test_schema_;
    config["disable_constraints"] = true;

    loader->initialize(config, context);

    // Try to load visit without corresponding person (would fail with FK constraint)
    Record visit;
    visit.setField("visit_occurrence_id", int64_t(1001));
    visit.setField("person_id", int64_t(999)); // person 999 doesn't exist
    visit.setField("visit_concept_id", int32_t(9201));
    visit.setField("visit_start_date", std::chrono::system_clock::now());
    visit.setField("visit_type_concept_id", int32_t(44818517));
    bool result = loader->load(visit, context);
    EXPECT_TRUE(result); // Should succeed with constraints disabled

    loader->commit(context);

    // Verify data was loaded despite FK violation
    size_t loaded = count_table_rows(connection, test_schema_, "visit_occurrence");
    EXPECT_EQ(loaded, 1);
}

#ifdef HAVE_MYSQL
// MySQL integration tests
class MySQLIntegrationTest : public DatabaseIntegrationTest {
protected:
    void SetUp() override {
        DatabaseIntegrationTest::SetUp();

        // Create connection
        connection = std::make_unique<MySQLConnection>();
        connection->connect(DatabaseIntegrationConfig::instance().mysql_connection_params());

        // Set up test schema and tables
        ensure_test_schema(connection, test_schema_);
        create_test_tables(connection, test_schema_);

        // Create loader
        loader = std::make_unique<MySQLLoader>(
            std::make_unique<MySQLConnection>(), DatabaseLoaderOptions{});
    }

    void cleanup_test_data() override {
        if (connection && connection->is_connected()) {
            try {
                connection->execute_update(
                    std::format("DROP DATABASE IF EXISTS {}", test_schema_));
            } catch (...) {
                // Ignore cleanup errors
            }
        }
    }

protected:
    std::unique_ptr<IDatabaseConnection> connection;
    std::unique_ptr<MySQLLoader> loader;
    std::string test_schema_ = "omop_test";
};

// Test MySQL multi-row insert
TEST_F(MySQLIntegrationTest, MultiRowInsert) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("measurement");
    config["schema_name"] = test_schema_;

    loader->initialize(config, context);

    // Create test measurements
    const size_t record_count = 500;
    for (size_t i = 1; i <= record_count; ++i) {
        Record record;
        record.setField("measurement_id", int64_t(i));
        record.setField("person_id", int64_t(1)); // Assuming person 1 exists
        record.setField("measurement_concept_id", int32_t(3004249)); // Systolic BP
        record.setField("measurement_date", std::chrono::system_clock::now());
        record.setField("measurement_type_concept_id", int32_t(44818701));
        record.setField("value_as_number", 120.0 + (i % 20));
        record.setField("unit_concept_id", int32_t(8876)); // mmHg

        loader->load(record, context);
    }

    loader->commit(context);

    // Verify data was loaded
    size_t loaded = count_table_rows(connection, test_schema_, "measurement");
    EXPECT_EQ(loaded, record_count);
}
#endif // HAVE_MYSQL

// OMOP-specific loader integration test
class OmopDatabaseIntegrationTest : public PostgreSQLIntegrationTest {
protected:
    void SetUp() override {
        PostgreSQLIntegrationTest::SetUp();

        // Create OMOP loader
        omop_loader = std::make_unique<OmopDatabaseLoader>(
            std::make_unique<PostgreSQLConnection>(), DatabaseLoaderOptions{});

        // Pre-populate some reference data for FK validation
        populate_reference_data();
    }

    void populate_reference_data() {
        // Add some persons for FK constraints
        connection->execute_update(std::format(
            "INSERT INTO {}.person VALUES (1, 8507, 1980, 8527, 38003564, NULL, NULL, 'P1')",
            test_schema_));
        connection->execute_update(std::format(
            "INSERT INTO {}.person VALUES (2, 8532, 1985, 8527, 38003564, NULL, NULL, 'P2')",
            test_schema_));
    }

protected:
    std::unique_ptr<OmopDatabaseLoader> omop_loader;
};

// Test OMOP validation and conversion
TEST_F(OmopDatabaseIntegrationTest, OmopValidationAndConversion) {
    std::unordered_map<std::string, std::any> config;
    config["omop_table"] = std::string("visit_occurrence");
    config["schema_name"] = test_schema_;
    config["validate_foreign_keys"] = false; // Skip FK cache loading for test

    omop_loader->initialize(config, context);

    // Create valid visit records
    std::vector<Record> visits;
    for (int i = 1; i <= 10; ++i) {
        Record visit;
        visit.setField("visit_occurrence_id", int64_t(1000 + i));
        visit.setField("person_id", int64_t((i % 2) + 1));
        visit.setField("visit_concept_id", int32_t(9201));
        visit.setField("visit_start_date", std::chrono::system_clock::now());
        visit.setField("visit_type_concept_id", int32_t(44818517));
        visits.push_back(visit);
    }

    // Load visits
    for (const auto& visit : visits) {
        bool result = omop_loader->load(visit, context);
        EXPECT_TRUE(result);
    }

    omop_loader->commit(context);

    // Verify all visits were loaded
    size_t loaded = count_table_rows(connection, test_schema_, "visit_occurrence");
    EXPECT_EQ(loaded, visits.size());
}

// Parallel loader integration test
class ParallelLoaderIntegrationTest : public PostgreSQLIntegrationTest {
protected:
    void SetUp() override {
        PostgreSQLIntegrationTest::SetUp();

        // Connection factory
        auto factory = []() {
            auto conn = std::make_unique<PostgreSQLConnection>();
            conn->connect(DatabaseIntegrationConfig::instance().postgresql_connection_params());
            return conn;
        };

        // Create parallel loader with 4 workers
        parallel_loader = std::make_unique<ParallelDatabaseLoader>(
            factory, 4, DatabaseLoaderOptions{});
    }

protected:
    std::unique_ptr<ParallelDatabaseLoader> parallel_loader;
};

// Test parallel loading performance
TEST_F(ParallelLoaderIntegrationTest, ParallelLoadingPerformance) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("person");
    config["schema_name"] = test_schema_;

    parallel_loader->initialize(config, context);

    // Create large batch
    const size_t batch_size = 10000;
    RecordBatch batch;
    for (size_t i = 1; i <= batch_size; ++i) {
        Record record;
        record.setField("person_id", int64_t(i));
        record.setField("gender_concept_id", int32_t(8507));
        record.setField("year_of_birth", int32_t(1980));
        record.setField("race_concept_id", int32_t(8527));
        record.setField("ethnicity_concept_id", int32_t(38003564));
        batch.addRecord(record);
    }

    // Measure loading time
    auto start_time = std::chrono::high_resolution_clock::now();

    size_t loaded = parallel_loader->load_batch(batch, context);

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double elapsed = duration.count() / 1000.0;
    double throughput = loaded / elapsed;

    parallel_loader->commit(context);

    // Verify all records loaded
    EXPECT_EQ(loaded, batch_size);

    // Verify in database
    size_t db_count = count_table_rows(connection, test_schema_, "person");
    EXPECT_EQ(db_count, batch_size);

    // Log performance metrics
    std::cout << "Parallel loading performance:" << std::endl;
    std::cout << "  Records: " << batch_size << std::endl;
    std::cout << "  Time: " << elapsed << " seconds" << std::endl;
    std::cout << "  Throughput: " << throughput << " records/second" << std::endl;
}

// Performance comparison test
TEST_F(PostgreSQLIntegrationTest, PerformanceComparison) {
    const size_t test_size = 5000;
    RecordBatch test_batch;
    for (size_t i = 1; i <= test_size; ++i) {
        Record record;
        record.setField("person_id", int64_t(i));
        record.setField("gender_concept_id", int32_t(8507));
        record.setField("year_of_birth", int32_t(1980));
        record.setField("race_concept_id", int32_t(8527));
        record.setField("ethnicity_concept_id", int32_t(38003564));
        test_batch.addRecord(record);
    }

    // Test 1: Regular bulk insert
    {
        DatabaseLoaderOptions options;
        options.use_bulk_insert = true;
        options.use_copy_command = false;

        auto bulk_loader = std::make_unique<DatabaseLoader>(
            std::make_unique<PostgreSQLConnection>(), options);

        std::unordered_map<std::string, std::any> config;
        config["table_name"] = std::string("person");
        config["schema_name"] = test_schema_;

        bulk_loader->initialize(config, context);

        auto start_time = std::chrono::high_resolution_clock::now();
        size_t loaded = bulk_loader->load_batch(test_batch, context);
        bulk_loader->commit(context);
        auto end_time = std::chrono::high_resolution_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        double bulk_time = duration.count() / 1000.0;
        double bulk_throughput = loaded / bulk_time;

        std::cout << "Bulk insert: " << bulk_throughput << " records/sec" << std::endl;

        // Clean up
        connection->execute_update(std::format("TRUNCATE TABLE {}.person", test_schema_));
    }

    // Test 2: PostgreSQL COPY
    {
        std::unordered_map<std::string, std::any> config;
        config["table_name"] = std::string("person");
        config["schema_name"] = test_schema_;

        loader->initialize(config, context);

        auto start_time = std::chrono::high_resolution_clock::now();
        size_t loaded = loader->load_batch(test_batch, context);
        loader->commit(context);
        auto end_time = std::chrono::high_resolution_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        double copy_time = duration.count() / 1000.0;
        double copy_throughput = loaded / copy_time;

        std::cout << "COPY command: " << copy_throughput << " records/sec" << std::endl;
    }
}

// Main function to run only when integration tests are enabled
// Commented out to allow integration with main test executable
/*
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);

    auto& config = DatabaseIntegrationConfig::instance();
    if (!config.is_enabled()) {
        std::cout << "Database integration tests are disabled." << std::endl;
        std::cout << "To enable, set environment variable: OMOP_RUN_INTEGRATION_TESTS=1" << std::endl;
        std::cout << "Also configure database connection parameters:" << std::endl;
        std::cout << "  PostgreSQL: OMOP_PG_HOST, OMOP_PG_PORT, OMOP_PG_DB, OMOP_PG_USER, OMOP_PG_PASSWORD" << std::endl;
#ifdef HAVE_MYSQL
        std::cout << "  MySQL: OMOP_MYSQL_HOST, OMOP_MYSQL_PORT, OMOP_MYSQL_DB, OMOP_MYSQL_USER, OMOP_MYSQL_PASSWORD" << std::endl;
#endif
        return 0;
    }

    return RUN_ALL_TESTS();
}
*/