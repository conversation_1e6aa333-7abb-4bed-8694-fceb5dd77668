# Unit tests for OMOP Common library

# Test source files in the common directory
set(COMMON_TEST_SOURCES
    configuration_test.cpp
    exceptions_test.cpp
    http_client_test.cpp
    logging_test.cpp
    metrics_collector_test.cpp
    utilities_test.cpp
    validation_test.cpp
)

# Use the new consolidated approach
add_component_unit_tests(common ${COMMON_TEST_SOURCES})

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "Common Unit Tests Configuration:")
message(STATUS "  Test files: ${COMMON_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/common")
message(STATUS "  C++ Standard: 20")