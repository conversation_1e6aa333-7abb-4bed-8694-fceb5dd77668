/**
 * @file exceptions_test.cpp
 * @brief Unit tests for exception classes with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "common/exceptions.h"
#include <string_view>
#include <locale>
#include <iomanip>
#include <sstream>

using namespace omop::common;

namespace omop::common::test {

// Test fixture for exception tests with UK locale setup
class ExceptionsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        // UK-specific test data
        uk_currency_symbol_ = "£";
        uk_postcode_ = "M60 1QD";
        nhs_number_ = "************";
    }

    void TearDown() override {}

    std::string uk_currency_symbol_;
    std::string uk_postcode_;
    std::string nhs_number_;
    
    // Helper to format UK currency
    std::string formatUKCurrency(double amount) {
        std::ostringstream oss;
        oss << uk_currency_symbol_ << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }
};

// Test that API exception handles UK endpoint errors correctly
TEST_F(ExceptionsTest, APIExceptionHandlesUKEndpointErrorsCorrectly) {
    std::string uk_endpoint = "/api/v1/uk/nhs/patient-lookup";
    int http_status = 404;
    std::string message = "NHS patient lookup service unavailable";
    
    ApiException ex(message, http_status, uk_endpoint);
    
    EXPECT_EQ(ex.http_status(), 404);
    EXPECT_EQ(ex.endpoint(), uk_endpoint);
    EXPECT_TRUE(std::string(ex.what()).find("NHS patient lookup") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("404") != std::string::npos);
}

// Test that base exception preserves UK-specific error context
TEST_F(ExceptionsTest, BaseExceptionPreservesUKSpecificErrorContext) {
    std::string uk_message = "NHS number " + nhs_number_ + " validation failed";
    
    // First capture the current source location for verification
    auto test_location = std::source_location::current();
    
    try {
        throw OmopException(uk_message, test_location);
    } catch (const OmopException& ex) {
        EXPECT_EQ(ex.message(), uk_message);
        EXPECT_NE(ex.what(), nullptr);
        EXPECT_TRUE(std::string(ex.what()).find("NHS number") != std::string::npos);
        EXPECT_TRUE(std::string(ex.what()).find(nhs_number_) != std::string::npos);
        
        // Verify location information matches what we provided
        const auto& location = ex.location();
        EXPECT_EQ(location.line(), test_location.line());
        EXPECT_EQ(location.file_name(), test_location.file_name());
        EXPECT_EQ(location.function_name(), test_location.function_name());
    }
}

// Test CDM exception handles UK OMOP operations
TEST_F(ExceptionsTest, CdmExceptionHandlesUKOMOPOperations) {
    std::string message = "Failed to create UK OMOP CDM table";
    std::string table_name = "uk_person";
    std::string operation = "create_table";
    
    CdmException ex(message, table_name, operation);
    
    EXPECT_EQ(ex.table_name(), table_name);
    EXPECT_EQ(ex.operation(), operation);
    EXPECT_TRUE(std::string(ex.what()).find("UK OMOP CDM") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_person") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("create_table") != std::string::npos);
}

// Test exception messages include formatted location information
TEST_F(ExceptionsTest, ExceptionMessagesIncludeFormattedLocationInfo) {
    std::string uk_message = "NHS number " + nhs_number_ + " validation failed";
    
    try {
        throw OmopException(uk_message);
    } catch (const OmopException& ex) {
        EXPECT_EQ(ex.message(), uk_message);
        EXPECT_NE(ex.what(), nullptr);
        
        // The formatted message should include location info
        std::string formatted = ex.what();
        EXPECT_TRUE(formatted.find("[") != std::string::npos); // Should have location brackets
        EXPECT_TRUE(formatted.find(":") != std::string::npos); // Should have line number separator
        EXPECT_TRUE(formatted.find(uk_message) != std::string::npos); // Should contain original message
    }
}

// Test that configuration exception handles UK configuration keys
TEST_F(ExceptionsTest, ConfigurationExceptionHandlesUKConfigurationKeys) {
    std::string message = "Invalid UK postcode format in configuration";
    std::string config_key = "etl_settings.uk_postcode_validation";
    
    ConfigurationException ex(message, config_key);
    
    EXPECT_EQ(ex.config_key(), config_key);
    EXPECT_TRUE(std::string(ex.what()).find("UK postcode") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find(config_key) != std::string::npos);
}

// Test that database exception includes UK database details
TEST_F(ExceptionsTest, DatabaseExceptionIncludesUKDatabaseDetails) {
    std::string message = "Connection to UK NHS database failed";
    std::string database_type = "PostgreSQL";
    int error_code = 28000;
    
    DatabaseException ex(message, database_type, error_code);
    
    EXPECT_EQ(ex.database_type(), database_type);
    EXPECT_EQ(ex.error_code(), error_code);
    EXPECT_TRUE(std::string(ex.what()).find("UK NHS database") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("PostgreSQL") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("28000") != std::string::npos);
}

// Test that extraction exception handles UK NHS data sources
TEST_F(ExceptionsTest, ExtractionExceptionHandlesUKNHSDataSources) {
    std::string message = "Failed to extract patient demographics";
    std::string source_name = "uk_nhs_patient_registry";
    
    ExtractionException ex(message, source_name);
    
    EXPECT_EQ(ex.source_name(), source_name);
    EXPECT_TRUE(std::string(ex.what()).find("patient demographics") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_nhs_patient_registry") != std::string::npos);
}

// Test that load exception handles UK OMOP target tables
TEST_F(ExceptionsTest, LoadExceptionHandlesUKOMOPTargetTables) {
    std::string message = "Failed to load UK patient data into OMOP person table";
    std::string target_table = "uk_omop_cdm.person";
    
    LoadException ex(message, target_table);
    
    EXPECT_EQ(ex.target_table(), target_table);
    EXPECT_TRUE(std::string(ex.what()).find("UK patient data") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_omop_cdm.person") != std::string::npos);
}

// Test that platform exception handles UK system operations
TEST_F(ExceptionsTest, PlatformExceptionHandlesUKSystemOperations) {
    std::string message = "Failed to access UK NHS network drive";
    std::string operation_name = "nhs_file_system_mount";
    std::string platform_type = "Linux";
    
    PlatformException ex(message, operation_name, platform_type);
    
    EXPECT_EQ(ex.operation_name(), operation_name);
    EXPECT_EQ(ex.platform_type(), platform_type);
    EXPECT_TRUE(std::string(ex.what()).find("UK NHS network") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("nhs_file_system_mount") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("Linux") != std::string::npos);
}

// Test that security exception handles UK healthcare security context
TEST_F(ExceptionsTest, SecurityExceptionHandlesUKHealthcareSecurityContext) {
    std::string message = "Unauthorized access to NHS patient records";
    std::string security_context = "uk_nhs_data_protection";
    
    SecurityException ex(message, security_context);
    
    EXPECT_EQ(ex.security_context(), security_context);
    EXPECT_TRUE(std::string(ex.what()).find("NHS patient records") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_nhs_data_protection") != std::string::npos);
}

// Test that transformation exception handles UK field transformations
TEST_F(ExceptionsTest, TransformationExceptionHandlesUKFieldTransformations) {
    std::string message = "Invalid UK postcode format cannot be transformed";
    std::string field_name = "patient_postcode";
    std::string transformation_type = "uk_postcode_standardization";
    
    TransformationException ex(message, field_name, transformation_type);
    
    EXPECT_EQ(ex.field_name(), field_name);
    EXPECT_EQ(ex.transformation_type(), transformation_type);
    EXPECT_TRUE(std::string(ex.what()).find("UK postcode") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("patient_postcode") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_postcode_standardization") != std::string::npos);
}

// Test that validation exception handles UK healthcare validation rules
TEST_F(ExceptionsTest, ValidationExceptionHandlesUKHealthcareValidationRules) {
    std::string message = "NHS number checksum validation failed";
    std::string rule_name = "uk_nhs_number_checksum";
    std::string field_value = nhs_number_;
    
    ValidationException ex(message, rule_name, field_value);
    
    EXPECT_EQ(ex.rule_name(), rule_name);
    EXPECT_EQ(ex.field_value(), field_value);
    EXPECT_TRUE(std::string(ex.what()).find("NHS number checksum") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_nhs_number_checksum") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find(nhs_number_) != std::string::npos);
}

// Test that vocabulary exception handles UK medical vocabularies
TEST_F(ExceptionsTest, VocabularyExceptionHandlesUKMedicalVocabularies) {
    std::string message = "SNOMED CT code not found in UK terminology server";
    std::string vocabulary_name = "uk_snomed_ct";
    std::string source_value = "123456789";
    
    VocabularyException ex(message, vocabulary_name, source_value);
    
    EXPECT_EQ(ex.vocabulary_name(), vocabulary_name);
    EXPECT_EQ(ex.source_value(), source_value);
    EXPECT_TRUE(std::string(ex.what()).find("SNOMED CT") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("uk_snomed_ct") != std::string::npos);
    EXPECT_TRUE(std::string(ex.what()).find("123456789") != std::string::npos);
}

// Test exception copy and move semantics
TEST_F(ExceptionsTest, ExceptionCopyAndMoveSemantics) {
    std::string message = "Test exception for copy/move";
    
    // Test OmopException
    OmopException original(message);
    
    // Copy constructor
    OmopException copied(original);
    EXPECT_EQ(copied.message(), original.message());
    EXPECT_STREQ(copied.what(), original.what());
    
    // Move constructor
    OmopException moved(std::move(OmopException(message)));
    EXPECT_EQ(moved.message(), message);
}

// Test exception inheritance and polymorphism
TEST_F(ExceptionsTest, ExceptionInheritanceAndPolymorphism) {
    std::string message = "Polymorphic exception test";
    
    // Create various exception types
    std::vector<std::unique_ptr<OmopException>> exceptions;
    exceptions.push_back(std::make_unique<ConfigurationException>(message, "test_key"));
    exceptions.push_back(std::make_unique<ExtractionException>(message, "test_source"));
    exceptions.push_back(std::make_unique<LoadException>(message, "test_table"));
    exceptions.push_back(std::make_unique<SecurityException>(message, "test_context"));
    
    // Verify polymorphic behavior
    for (const auto& ex : exceptions) {
        EXPECT_TRUE(std::string(ex->what()).find(message) != std::string::npos);
        EXPECT_FALSE(ex->message().empty());
    }
}

// Test exception with empty strings
TEST_F(ExceptionsTest, ExceptionWithEmptyStrings) {
    // Test that exceptions handle empty strings gracefully
    EXPECT_NO_THROW(OmopException(""));
    EXPECT_NO_THROW(ConfigurationException("", ""));
    EXPECT_NO_THROW(ExtractionException("", ""));
    EXPECT_NO_THROW(TransformationException("", "", ""));
    EXPECT_NO_THROW(LoadException("", ""));
    EXPECT_NO_THROW(DatabaseException("", "", 0));
    EXPECT_NO_THROW(SecurityException("", ""));
    EXPECT_NO_THROW(ValidationException("", "", ""));
    EXPECT_NO_THROW(VocabularyException("", "", ""));
    EXPECT_NO_THROW(ApiException("", 0, ""));
    EXPECT_NO_THROW(PlatformException("", "", ""));
    EXPECT_NO_THROW(CdmException("", "", ""));
}

// Test exception with very long strings
TEST_F(ExceptionsTest, ExceptionWithVeryLongStrings) {
    std::string long_message(1000, 'x');
    std::string long_key(500, 'y');
    
    // Test that exceptions handle long strings without buffer overflows
    EXPECT_NO_THROW({
        ConfigurationException ex(long_message, long_key);
        EXPECT_EQ(ex.config_key(), long_key);
    });
    
    EXPECT_NO_THROW({
        ExtractionException ex(long_message, long_key);
        EXPECT_EQ(ex.source_name(), long_key);
    });
}

// Test all exception default error messages
TEST_F(ExceptionsTest, AllExceptionDefaultErrorMessages) {
    // Test that each exception type formats its error message correctly
    ConfigurationException config_ex("Config error", "db.host");
    EXPECT_TRUE(std::string(config_ex.what()).find("db.host") != std::string::npos);
    
    ExtractionException extract_ex("Extract error", "patients.csv");
    EXPECT_TRUE(std::string(extract_ex.what()).find("patients.csv") != std::string::npos);
    
    DatabaseException db_ex("Connection failed", "PostgreSQL", 5432);
    EXPECT_TRUE(std::string(db_ex.what()).find("5432") != std::string::npos);
}

} // namespace omop::common::test