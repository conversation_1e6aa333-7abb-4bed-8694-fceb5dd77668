/**
 * @file utilities_test.cpp
 * @brief Unit tests for utility functions with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "common/utilities.h"
#include <fstream>
#include <filesystem>
#include <chrono>
#include <thread>
#include <locale>
#include <iomanip>
#include <sstream>
#include <random>
#include <cstring>

using namespace omop::common;

namespace omop::common::test {

// Test fixture for utility functions with UK locale setup
class UtilitiesTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        temp_dir = std::filesystem::temp_directory_path() / "omop_test";
        std::filesystem::create_directories(temp_dir);
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        uk_time_format_ = "%H:%M";
    }

    void TearDown() override {
        if (std::filesystem::exists(temp_dir)) {
            std::filesystem::remove_all(temp_dir);
        }
    }

    std::filesystem::path temp_dir;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string uk_time_format_;

    // Helper to format UK currency
    std::string formatUKCurrency(double amount) {
        std::ostringstream oss;
        oss << uk_currency_symbol_ << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }

    // Helper to format UK temperature
    std::string formatUKTemperature(double celsius) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << celsius << "°C";
        return oss.str();
    }
};

// Test any_to_string function
TEST_F(UtilitiesTest, AnyToStringFunction) {
    // Test various types
    EXPECT_EQ(any_to_string(std::any(42)), "42");
    EXPECT_EQ(any_to_string(std::any(3.14)), "3.140000");
    EXPECT_EQ(any_to_string(std::any(true)), "true");
    EXPECT_EQ(any_to_string(std::any(false)), "false");
    EXPECT_EQ(any_to_string(std::any(std::string("test"))), "test");
    EXPECT_EQ(any_to_string(std::any('A')), "A");
    
    // Test empty any
    EXPECT_EQ(any_to_string(std::any()), "");
    
    // Test various numeric types
    EXPECT_EQ(any_to_string(std::any(42L)), "42");
    EXPECT_EQ(any_to_string(std::any(42LL)), "42");
    EXPECT_EQ(any_to_string(std::any(42U)), "42");
    EXPECT_EQ(any_to_string(std::any(42UL)), "42");
    EXPECT_EQ(any_to_string(std::any(42ULL)), "42");
    EXPECT_EQ(any_to_string(std::any(3.14f)), "3.140000");
    
    // Test unknown type
    struct UnknownType { int x; };
    UnknownType unknown{42};
    std::string result = any_to_string(std::any(unknown));
    EXPECT_TRUE(result.find("<") == 0);
    EXPECT_TRUE(result.find(">") == result.length() - 1);
}

// Test UK-specific validation functions
TEST_F(UtilitiesTest, UKValidationFunctionsComprehensive) {
    // Test UK postcode validation
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M1 1AA"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M60 1QD"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("B33 8TH"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("W1A 0AX"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("EC1A 1BB"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("SW1A 1AA"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("INVALID"));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M60 1QDX"));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("123 456"));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode(""));
    
    // Test NHS number validation
    EXPECT_TRUE(ValidationUtils::is_valid_nhs_number("**********"));
    EXPECT_TRUE(ValidationUtils::is_valid_nhs_number("************"));
    EXPECT_TRUE(ValidationUtils::is_valid_nhs_number("************"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("**********"));
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("450557710"));
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("**********0"));
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number(""));
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("abcdefghij"));
    
    // Test UK National Insurance number validation
    EXPECT_TRUE(ValidationUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_ni_number("QQ123456C"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("DO123456C")); // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("FY123456C")); // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("AB12345C"));  // Wrong length
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("AB1234567C")); // Wrong length
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number(""));
    
    // Test UK phone number validation
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("+44 20 7946 0958"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("020 7946 0958"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("07700 900123"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("+44 7700 900123"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("0161 496 0000"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("************")); // US format
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("123456789"));     // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("abcdefghij"));
    
    // Test SNOMED CT code validation
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("404684003"));
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("195967001"));
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("22298006"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code("123"));      // Too short
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("**********1")); // Valid length (11 digits)
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code("abcdefgh"));
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code(""));
    
    // Test Read code validation  
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("G30.."));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("F25.."));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("14A.."));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("G30z."));
    
    EXPECT_FALSE(ValidationUtils::is_valid_read_code(""));
    EXPECT_FALSE(ValidationUtils::is_valid_read_code("G"));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("G30")); // Valid 3-character code
    EXPECT_FALSE(ValidationUtils::is_valid_read_code("G30.......")); // Too long (10 characters)
}

// Test UK localization functions
TEST_F(UtilitiesTest, UKLocalizationFunctions) {
    using namespace UKLocalization;
    
    // Test UK currency formatting
    EXPECT_EQ(format_uk_currency(125.50), "£125.50");
    EXPECT_EQ(format_uk_currency(0.01), "£0.01");
    EXPECT_EQ(format_uk_currency(1000.00), "£1000.00");
    EXPECT_EQ(format_uk_currency(-50.75), "£-50.75");
    
    // Test UK date formatting
    auto test_time = std::chrono::system_clock::from_time_t(1640995200); // 2022-01-01 00:00:00 UTC
    std::string uk_date = format_uk_date(test_time);
    EXPECT_TRUE(uk_date.find("01/01/2022") != std::string::npos);
    
    // Test UK datetime formatting
    std::string uk_datetime = format_uk_datetime(test_time);
    EXPECT_TRUE(uk_datetime.find("01/01/2022") != std::string::npos);
    EXPECT_TRUE(uk_datetime.find(":") != std::string::npos);
    
    // Test temperature conversion
    EXPECT_DOUBLE_EQ(celsius_to_fahrenheit(0.0), 32.0);
    EXPECT_DOUBLE_EQ(celsius_to_fahrenheit(100.0), 212.0);
    EXPECT_DOUBLE_EQ(celsius_to_fahrenheit(37.0), 98.6);
    
    EXPECT_DOUBLE_EQ(fahrenheit_to_celsius(32.0), 0.0);
    EXPECT_DOUBLE_EQ(fahrenheit_to_celsius(212.0), 100.0);
    EXPECT_NEAR(fahrenheit_to_celsius(98.6), 37.0, 0.01);
    
    // Test temperature formatting
    EXPECT_EQ(format_temperature_celsius(20.5), "20.5°C");
    EXPECT_EQ(format_temperature_celsius(0.0), "0.0°C");
    EXPECT_EQ(format_temperature_celsius(-5.2), "-5.2°C");
    
    // Test UK number formatting
    EXPECT_EQ(format_uk_number(1234.567, 2), "1234.57");
    EXPECT_EQ(format_uk_number(0.123, 3), "0.123");
    EXPECT_EQ(format_uk_number(1000, 0), "1000");
    
    // Test UK address formatting
    std::string address = format_uk_address(
        "123 High Street",
        "Flat 2",
        "Manchester",
        "Greater Manchester",
        "M1 1AA"
    );
    EXPECT_TRUE(address.find("123 High Street") != std::string::npos);
    EXPECT_TRUE(address.find("Flat 2") != std::string::npos);
    EXPECT_TRUE(address.find("Manchester") != std::string::npos);
    EXPECT_TRUE(address.find("Greater Manchester") != std::string::npos);
    EXPECT_TRUE(address.find("M1 1AA") != std::string::npos);
    
    // Test with empty optional fields
    std::string minimal_address = format_uk_address(
        "123 High Street",
        "",
        "Manchester",
        "",
        "M1 1AA"
    );
    EXPECT_TRUE(minimal_address.find("123 High Street") != std::string::npos);
    EXPECT_TRUE(minimal_address.find("Manchester") != std::string::npos);
    EXPECT_TRUE(minimal_address.find("M1 1AA") != std::string::npos);
    EXPECT_FALSE(minimal_address.find("Flat 2") != std::string::npos);
}

// Test age calculation function
TEST_F(UtilitiesTest, AgeCalculationFunction) {
    // Test age calculation for someone born on 15/01/1985
    std::tm birth_tm = {};
    birth_tm.tm_year = 85;  // Years since 1900
    birth_tm.tm_mon = 0;    // January (0-based)
    birth_tm.tm_mday = 15;
    auto birth_date = std::chrono::system_clock::from_time_t(std::mktime(&birth_tm));
    
    // Calculate age as of 15/01/2025
    std::tm ref_tm = {};
    ref_tm.tm_year = 125;  // 2025 - 1900
    ref_tm.tm_mon = 0;
    ref_tm.tm_mday = 15;
    auto ref_date = std::chrono::system_clock::from_time_t(std::mktime(&ref_tm));
    
    int age = DateTimeUtils::calculate_age(birth_date, ref_date);
    EXPECT_EQ(age, 40);
    
    // Test age calculation when birthday hasn't occurred yet this year
    ref_tm.tm_mday = 14;  // One day before birthday
    ref_date = std::chrono::system_clock::from_time_t(std::mktime(&ref_tm));
    age = DateTimeUtils::calculate_age(birth_date, ref_date);
    EXPECT_EQ(age, 39);
}

// Test that base64 encoding works correctly
TEST_F(UtilitiesTest, Base64EncodingWorksCorrectly) {
    std::vector<uint8_t> data = {72, 101, 108, 108, 111}; // "Hello"
    std::string encoded = CryptoUtils::base64_encode(data);
    EXPECT_EQ(encoded, "SGVsbG8=");
    
    std::vector<uint8_t> decoded = CryptoUtils::base64_decode(encoded);
    EXPECT_EQ(data, decoded);
}

// Test that camel case conversion works properly
TEST_F(UtilitiesTest, CamelCaseConversionWorksCorrectly) {
    EXPECT_EQ(StringUtils::to_camel_case("hello_world"), "helloWorld");
    EXPECT_EQ(StringUtils::to_camel_case("nhs_patient_data"), "nhsPatientData");
    EXPECT_EQ(StringUtils::to_camel_case("gp_practice_code"), "gpPracticeCode");
    EXPECT_EQ(StringUtils::to_camel_case(""), "");
    EXPECT_EQ(StringUtils::to_camel_case("single"), "single");
}

// Test that cryptographic hash functions work correctly
TEST_F(UtilitiesTest, CryptographicHashFunctionsWorkCorrectly) {
    std::string data = "NHS patient data";
    
    std::string md5_hash = CryptoUtils::md5(data);
    EXPECT_EQ(md5_hash.length(), 32u); // MD5 is 32 hex characters
    
    std::string sha256_hash = CryptoUtils::sha256(data);
    EXPECT_EQ(sha256_hash.length(), 64u); // SHA256 is 64 hex characters
    
    // Verify consistency
    EXPECT_EQ(CryptoUtils::md5(data), md5_hash);
    EXPECT_EQ(CryptoUtils::sha256(data), sha256_hash);
}

// Test that date parsing works with UK formats
TEST_F(UtilitiesTest, DateParsingWorksWithUKFormats) {
    // Test UK date format DD/MM/YYYY
    auto parsed_date = DateTimeUtils::parse_date("15/01/2025", "%d/%m/%Y");
    ASSERT_TRUE(parsed_date.has_value());
    
    std::string formatted = DateTimeUtils::format_date(*parsed_date, "%d/%m/%Y");
    EXPECT_EQ(formatted, "15/01/2025");
    
    // Test invalid UK date
    auto invalid_date = DateTimeUtils::parse_date("32/01/2025", "%d/%m/%Y");
    EXPECT_FALSE(invalid_date.has_value());
}

// Test that date validation recognizes UK date formats
TEST_F(UtilitiesTest, DateValidationRecognizesUKDateFormats) {
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("15/01/2025", "%d/%m/%Y"));
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("29/02/2024", "%d/%m/%Y")); // Leap year
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("29/02/2025", "%d/%m/%Y")); // Not leap year
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("32/01/2025", "%d/%m/%Y"));
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("15/13/2025", "%d/%m/%Y"));
}

// Test that file operations work correctly
TEST_F(UtilitiesTest, FileOperationsWorkCorrectly) {
    std::string test_file = temp_dir / "test.txt";
    std::string uk_content = "NHS patient data: £125.50, Temperature: " + formatUKTemperature(36.5);
    
    EXPECT_TRUE(FileUtils::write_file(test_file, uk_content));
    EXPECT_TRUE(FileUtils::file_exists(test_file));
    
    auto content = FileUtils::read_file(test_file);
    ASSERT_TRUE(content.has_value());
    EXPECT_EQ(*content, uk_content);
    
    auto size = FileUtils::file_size(test_file);
    ASSERT_TRUE(size.has_value());
    EXPECT_EQ(*size, uk_content.length());
}

// Test that JSON validation works correctly
TEST_F(UtilitiesTest, JSONValidationWorksCorrectly) {
    EXPECT_TRUE(ValidationUtils::is_valid_json("{}"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("{\"nhs_number\": \"**********\"}"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("[1, 2, 3]"));
    EXPECT_FALSE(ValidationUtils::is_valid_json("{invalid json}"));
    EXPECT_FALSE(ValidationUtils::is_valid_json(""));
}

// Test that NHS number validation works correctly
TEST_F(UtilitiesTest, NHSNumberValidationWorksCorrectly) {
    // Valid NHS numbers (with proper checksums)
    EXPECT_TRUE(ValidationUtils::is_valid_nhs_number("**********"));
    EXPECT_TRUE(ValidationUtils::is_valid_nhs_number("************")); // With spaces
    
    // Invalid NHS numbers
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("**********")); // Wrong checksum
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("123456789"));  // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("**********1")); // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number("abcd567890"));  // Contains letters
    EXPECT_FALSE(ValidationUtils::is_valid_nhs_number(""));            // Empty
}

// Test that performance timing works accurately
TEST_F(UtilitiesTest, PerformanceTimingWorksAccurately) {
    PerformanceUtils::Timer timer;
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double elapsed = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed, 95.0);  // Allow some variance
    EXPECT_LE(elapsed, 150.0); // But not too much
}

// Test that phone number validation recognizes UK numbers
TEST_F(UtilitiesTest, PhoneNumberValidationRecognizesUKNumbers) {
    // Valid UK phone numbers
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("07123456789"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("+44 7123 456789"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("0044 7123 456789"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("020 7946 0958"));
    
    // Invalid UK phone numbers
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("123456789"));     // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("**********123")); // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("1712345678"));    // Wrong prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone(""));              // Empty
}

// Test that random string generation produces valid results
TEST_F(UtilitiesTest, RandomStringGenerationProducesValidResults) {
    std::string random1 = StringUtils::random_string(10);
    std::string random2 = StringUtils::random_string(10);
    
    EXPECT_EQ(random1.length(), 10u);
    EXPECT_EQ(random2.length(), 10u);
    EXPECT_NE(random1, random2); // Should be different
    
    // Should contain only alphanumeric characters
    for (char c : random1) {
        EXPECT_TRUE(std::isalnum(c));
    }
}

// Test that Read code validation works for UK legacy codes
TEST_F(UtilitiesTest, ReadCodeValidationWorksForUKLegacyCodes) {
    // Valid Read codes
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("G20.."));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("14A6."));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("C10z."));
    EXPECT_TRUE(ValidationUtils::is_valid_read_code("ABC123"));
    
    // Invalid Read codes
    EXPECT_FALSE(ValidationUtils::is_valid_read_code(""));
    EXPECT_FALSE(ValidationUtils::is_valid_read_code("AB"));      // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_read_code("ABCDEFGH")); // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_read_code("A B C"));    // Contains spaces
}

// Test that SNOMED CT code validation works correctly
TEST_F(UtilitiesTest, SNOMEDCTCodeValidationWorksCorrectly) {
    // Valid SNOMED CT codes
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("123456"));
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("**********"));
    EXPECT_TRUE(ValidationUtils::is_valid_snomed_code("**********12345678")); // 18 digits
    
    // Invalid SNOMED CT codes
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code(""));
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code("12345"));  // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code("**********123456789")); // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_snomed_code("123ABC"));  // Contains letters
}

// Test that SQL identifier validation prevents injection
TEST_F(UtilitiesTest, SQLIdentifierValidationPreventsInjection) {
    // Valid SQL identifiers
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("patient_id"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("_private_field"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("table123"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("nhs_number"));
    
    // Invalid SQL identifiers
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier(""));
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("123invalid"));  // Starts with number
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("field-name"));  // Contains hyphen
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("SELECT"));      // Reserved word
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("DROP"));        // Reserved word
}

// Test that string case conversion handles UK terms correctly
TEST_F(UtilitiesTest, StringCaseConversionHandlesUKTermsCorrectly) {
    EXPECT_EQ(StringUtils::to_lower("NHS Patient"), "nhs patient");
    EXPECT_EQ(StringUtils::to_upper("gp practice"), "GP PRACTICE");
    EXPECT_EQ(StringUtils::to_lower("GREATER MANCHESTER"), "greater manchester");
    EXPECT_EQ(StringUtils::to_upper("royal mail"), "ROYAL MAIL");
    EXPECT_EQ(StringUtils::to_lower(""), "");
    EXPECT_EQ(StringUtils::to_upper(""), "");
}

// Test that string contains function works for UK-specific terms
TEST_F(UtilitiesTest, StringContainsFunctionWorksForUKSpecificTerms) {
    EXPECT_TRUE(StringUtils::contains("NHS number validation", "NHS"));
    EXPECT_TRUE(StringUtils::contains("GP practice code", "practice"));
    EXPECT_TRUE(StringUtils::contains("Royal Mail postcode", "Mail"));
    EXPECT_FALSE(StringUtils::contains("patient data", "NHS"));
    EXPECT_FALSE(StringUtils::contains("", "test"));
}

// Test that string joining works with UK formatting
TEST_F(UtilitiesTest, StringJoiningWorksWithUKFormatting) {
    std::vector<std::string> uk_cities = {"London", "Manchester", "Birmingham", "Glasgow"};
    EXPECT_EQ(StringUtils::join(uk_cities, ", "), "London, Manchester, Birmingham, Glasgow");
    EXPECT_EQ(StringUtils::join(uk_cities, " and "), "London and Manchester and Birmingham and Glasgow");
    EXPECT_EQ(StringUtils::join({}, ", "), "");
    EXPECT_EQ(StringUtils::join({"London"}, ", "), "London");
}

// Test that string prefix and suffix detection works
TEST_F(UtilitiesTest, StringPrefixAndSuffixDetectionWorks) {
    EXPECT_TRUE(StringUtils::starts_with("NHS12345", "NHS"));
    EXPECT_TRUE(StringUtils::ends_with("patient.csv", ".csv"));
    EXPECT_FALSE(StringUtils::starts_with("patient", "NHS"));
    EXPECT_FALSE(StringUtils::ends_with("data.txt", ".csv"));
    EXPECT_FALSE(StringUtils::starts_with("", "test"));
    EXPECT_FALSE(StringUtils::ends_with("", "test"));
}

// Test that string replacement works for UK terminology
TEST_F(UtilitiesTest, StringReplacementWorksForUKTerminology) {
    EXPECT_EQ(StringUtils::replace_all("US postcode", "US", "UK"), "UK postcode");
    EXPECT_EQ(StringUtils::replace_all("zip code validation", "zip code", "postcode"), "postcode validation");
    EXPECT_EQ(StringUtils::replace_all("$100.50", "$", "£"), "£100.50");
    EXPECT_EQ(StringUtils::replace_all("", "old", "new"), "");
    EXPECT_EQ(StringUtils::replace_all("no change", "missing", "new"), "no change");
}

// Test that string sanitization removes dangerous characters
TEST_F(UtilitiesTest, StringSanitizationRemovesDangerousCharacters) {
    EXPECT_EQ(ValidationUtils::sanitize_string("<script>alert('xss')</script>"), 
              "&lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;");
    EXPECT_EQ(ValidationUtils::sanitize_string("Safe NHS number"), "Safe NHS number");
    EXPECT_EQ(ValidationUtils::sanitize_string("Patient \"John\" & 'Jane'"), 
              "Patient &quot;John&quot; &amp; &#39;Jane&#39;");
}

// Test that string splitting works with UK data formats
TEST_F(UtilitiesTest, StringSplittingWorksWithUKDataFormats) {
    std::vector<std::string> expected = {"01/01/2025", "15/06/2025", "31/12/2025"};
    EXPECT_EQ(StringUtils::split("01/01/2025,15/06/2025,31/12/2025", ','), expected);
    
    std::vector<std::string> postcode_parts = {"M60", "1QD"};
    EXPECT_EQ(StringUtils::split("M60 1QD", ' '), postcode_parts);
    
    EXPECT_EQ(StringUtils::split("", ','), std::vector<std::string>{});
    EXPECT_EQ(StringUtils::split("single", ','), std::vector<std::string>{"single"});
}

// Test that string trimming removes UK-specific whitespace
TEST_F(UtilitiesTest, StringTrimmingRemovesUKSpecificWhitespace) {
    EXPECT_EQ(StringUtils::trim("  NHS patient data  "), "NHS patient data");
    EXPECT_EQ(StringUtils::trim("\t\nGP practice code\r\n"), "GP practice code");
    EXPECT_EQ(StringUtils::trim(""), "");
    EXPECT_EQ(StringUtils::trim("   "), "");
    EXPECT_EQ(StringUtils::trim("no-trim-needed"), "no-trim-needed");
}

// Test that UK National Insurance number validation works
TEST_F(UtilitiesTest, UKNationalInsuranceNumberValidationWorks) {
    // Valid UK NI numbers
    EXPECT_TRUE(ValidationUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_ni_number("QR555666A"));
    
    // Invalid UK NI numbers
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("AB12345C"));   // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("AB1234567C"));  // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("1B123456C"));   // First char not letter
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("A1123456C"));   // Second char not letter
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("ABCDEFGHC"));   // Middle not digits
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********"));   // Last char not letter
    
    // Invalid prefixes
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********"));   // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********"));   // Invalid prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_ni_number("*********"));   // Invalid prefix
}

// Test that UK postcode validation works correctly
TEST_F(UtilitiesTest, UKPostcodeValidationWorksCorrectly) {
    // Valid UK postcodes
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M1 1AA"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M60 1NW"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("B33 8TH"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("W1A 0AX"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("GIR 0AA"));  // Special case
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("SW1A1AA"));  // Without space
    
    // Invalid UK postcodes
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M1"));       // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M1 1AAA"));  // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("1M 1AA"));   // Invalid format
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("MA 1AA"));   // Invalid format
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M1 A1A"));   // Invalid format
}

// Test that UUID generation and validation works
TEST_F(UtilitiesTest, UUIDGenerationAndValidationWorks) {
    std::string uuid1 = CryptoUtils::generate_uuid();
    std::string uuid2 = CryptoUtils::generate_uuid();
    
    EXPECT_TRUE(ValidationUtils::is_valid_uuid(uuid1));
    EXPECT_TRUE(ValidationUtils::is_valid_uuid(uuid2));
    EXPECT_NE(uuid1, uuid2); // Should be different
    
    // Invalid UUIDs
    EXPECT_FALSE(ValidationUtils::is_valid_uuid(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uuid("invalid-uuid"));
    EXPECT_FALSE(ValidationUtils::is_valid_uuid("12345678-1234-1234-1234-**********abc")); // Too long
}

// Test memory tracker functionality
TEST_F(UtilitiesTest, MemoryTrackerFunctionality) {
    PerformanceUtils::MemoryTracker tracker;
    
    // Current usage should be non-negative
    EXPECT_GE(tracker.current_usage(), 0u);
    
    // Peak usage should be at least current usage
    EXPECT_GE(tracker.peak_usage(), tracker.current_usage());
    
    // Global peak usage should be non-negative
    EXPECT_GE(PerformanceUtils::MemoryTracker::global_peak_usage(), 0u);
    
    // Test reset
    tracker.reset();
    EXPECT_GE(tracker.current_usage(), 0u);
}

// Test format functions with UK localization
TEST_F(UtilitiesTest, FormatFunctionsWithUKLocalization) {
    // Test format_bytes
    EXPECT_EQ(PerformanceUtils::format_bytes(1024), "1.00 KB");
    EXPECT_EQ(PerformanceUtils::format_bytes(1048576), "1.00 MB");
    EXPECT_EQ(PerformanceUtils::format_bytes(1073741824), "1.00 GB");
    EXPECT_EQ(PerformanceUtils::format_bytes(512), "512.00 B");
    
    // Test format_duration
    EXPECT_EQ(PerformanceUtils::format_duration(0.5), "500ms");
    EXPECT_EQ(PerformanceUtils::format_duration(65), "1m 5s");
    EXPECT_EQ(PerformanceUtils::format_duration(3665), "1h 1m 5s");
    EXPECT_EQ(PerformanceUtils::format_duration(0.001), "1ms");
    
    // Test calculate_throughput
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(1000, 10), 100.0);
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(0, 10), 0.0);
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(1000, 0), 0.0);
}

// Test ProcessingUtils functions
TEST_F(UtilitiesTest, ProcessingUtilsFunctions) {
    EXPECT_EQ(ProcessingUtils::stage_name(0), "Extract");
    EXPECT_EQ(ProcessingUtils::stage_name(1), "Transform");
    EXPECT_EQ(ProcessingUtils::stage_name(2), "Load");
    EXPECT_EQ(ProcessingUtils::stage_name(99), "Unknown");
    EXPECT_EQ(ProcessingUtils::stage_name(-1), "Unknown");
}

// Test UK localization namespace functions
TEST_F(UtilitiesTest, UKLocalizationNamespaceFunctions) {
    // Test format_uk_currency
    EXPECT_EQ(UKLocalization::format_uk_currency(100.00), "£100.00");
    EXPECT_EQ(UKLocalization::format_uk_currency(1234.56), "£1234.56");
    EXPECT_EQ(UKLocalization::format_uk_currency(0.99), "£0.99");
    
    // Test format_uk_date
    std::tm test_tm = {};
    test_tm.tm_year = 125; // 2025
    test_tm.tm_mon = 0;    // January
    test_tm.tm_mday = 15;
    auto test_date = std::chrono::system_clock::from_time_t(std::mktime(&test_tm));
    EXPECT_EQ(UKLocalization::format_uk_date(test_date), "15/01/2025");
    
    // Test temperature conversions
    EXPECT_DOUBLE_EQ(UKLocalization::celsius_to_fahrenheit(0), 32.0);
    EXPECT_DOUBLE_EQ(UKLocalization::celsius_to_fahrenheit(100), 212.0);
    EXPECT_DOUBLE_EQ(UKLocalization::fahrenheit_to_celsius(32), 0.0);
    EXPECT_DOUBLE_EQ(UKLocalization::fahrenheit_to_celsius(212), 100.0);
    
    // Test format_temperature_celsius
    EXPECT_EQ(UKLocalization::format_temperature_celsius(36.5), "36.5°C");
    EXPECT_EQ(UKLocalization::format_temperature_celsius(0), "0.0°C");
    EXPECT_EQ(UKLocalization::format_temperature_celsius(-10.5), "-10.5°C");
}

// Test edge cases for validation functions
TEST_F(UtilitiesTest, ValidationEdgeCases) {
    // Test email validation edge cases
    EXPECT_FALSE(ValidationUtils::is_valid_email("@example.com"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@.com"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@@example.com"));
    
    // Test IP validation edge cases
    EXPECT_FALSE(ValidationUtils::is_valid_ip("256.256.256.256"));
    EXPECT_FALSE(ValidationUtils::is_valid_ip("192.168.1"));
    EXPECT_FALSE(ValidationUtils::is_valid_ip("***********.1"));
    
    // Test URL validation edge cases
    EXPECT_FALSE(ValidationUtils::is_valid_url("http://"));
    EXPECT_FALSE(ValidationUtils::is_valid_url("://example.com"));
}

} // namespace omop::common::test