/**
 * @file metrics_collector_test.cpp
 * @brief Unit tests for metrics collector with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "common/metrics_collector.h"
#include <thread>
#include <chrono>
#include <locale>
#include <iomanip>
#include <sstream>

using namespace omop::monitoring;
using namespace testing;

namespace omop::monitoring::test {

// Test fixture for metrics collector with UK locale setup
class MetricsCollectorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        collector = create_metrics_collector();
        config = get_default_metrics_config();
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        uk_time_format_ = "%H:%M:%S";
        uk_decimal_separator_ = ".";
        uk_thousands_separator_ = ",";
        
        // Initialize collector with UK timezone
        config.additional_config["timezone"] = std::string("Europe/London");
        // Disable collection thread to avoid hanging tests
        config.enabled = false;
        collector->initialize(config);
    }

    void TearDown() override {
        collector.reset();
    }

    std::unique_ptr<IMetricsCollector> collector;
    MetricsConfig config;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string uk_time_format_;
    std::string uk_decimal_separator_;
    std::string uk_thousands_separator_;
};

// Test factory function creates valid collector
TEST_F(MetricsCollectorTest, FactoryCreatesValidCollector) {
    auto test_collector = create_metrics_collector();
    ASSERT_NE(test_collector, nullptr);
    
    auto default_config = get_default_metrics_config();
    // Disable collection thread to avoid hanging test
    default_config.enabled = false;
    EXPECT_TRUE(test_collector->initialize(default_config));
}

// Test default metrics configuration
TEST_F(MetricsCollectorTest, DefaultMetricsConfiguration) {
    auto default_config = get_default_metrics_config();
    
    EXPECT_TRUE(default_config.enabled);
    EXPECT_EQ(default_config.collection_interval, std::chrono::seconds(30));
    EXPECT_EQ(default_config.retention_period, std::chrono::seconds(86400));
    EXPECT_EQ(default_config.max_metrics, 10000);
    EXPECT_EQ(default_config.max_values_per_metric, 1000);
    EXPECT_EQ(default_config.export_format, "prometheus");
    EXPECT_EQ(default_config.export_endpoint, "/metrics");
    EXPECT_EQ(default_config.storage_backend, "memory");
    EXPECT_FALSE(default_config.enable_compression);
}

// Test metric type string conversion
TEST_F(MetricsCollectorTest, MetricTypeStringConversion) {
    EXPECT_EQ(metric_type_to_string(MetricType::Counter), "counter");
    EXPECT_EQ(metric_type_to_string(MetricType::Gauge), "gauge");
    EXPECT_EQ(metric_type_to_string(MetricType::Histogram), "histogram");
    EXPECT_EQ(metric_type_to_string(MetricType::Summary), "summary");
    EXPECT_EQ(metric_type_to_string(MetricType::Timer), "timer");
    
    EXPECT_EQ(string_to_metric_type("counter"), MetricType::Counter);
    EXPECT_EQ(string_to_metric_type("gauge"), MetricType::Gauge);
    EXPECT_EQ(string_to_metric_type("histogram"), MetricType::Histogram);
    EXPECT_EQ(string_to_metric_type("summary"), MetricType::Summary);
    EXPECT_EQ(string_to_metric_type("timer"), MetricType::Timer);
    EXPECT_EQ(string_to_metric_type("unknown"), MetricType::Counter); // Default fallback
}

// Test counter metric registration and increment
TEST_F(MetricsCollectorTest, CounterMetricRegistrationAndIncrement) {
    MetricDefinition counter_def;
    counter_def.name = "test_counter";
    counter_def.description = "Test counter metric";
    counter_def.type = MetricType::Counter;
    counter_def.label_names = {"operation", "status"};
    
    EXPECT_TRUE(collector->register_metric(counter_def));
    
    // Test increment with UK formatting
    std::unordered_map<std::string, std::string> labels = {
        {"operation", "data_processing"},
        {"status", "success"}
    };
    
    EXPECT_TRUE(collector->increment_counter("test_counter", 1.0, labels));
    EXPECT_TRUE(collector->increment_counter("test_counter", 2.5, labels));
    
    auto metric = collector->get_metric("test_counter");
    ASSERT_TRUE(metric.has_value());
    EXPECT_EQ(metric->definition.name, "test_counter");
    EXPECT_EQ(metric->definition.type, MetricType::Counter);
    EXPECT_EQ(metric->values.size(), 2);
}

// Test gauge metric with UK currency values
TEST_F(MetricsCollectorTest, GaugeMetricWithUKCurrency) {
    MetricDefinition gauge_def;
    gauge_def.name = "uk_processing_cost";
    gauge_def.description = "Processing cost in British Pounds";
    gauge_def.type = MetricType::Gauge;
    gauge_def.label_names = {"department", "project"};
    
    EXPECT_TRUE(collector->register_metric(gauge_def));
    
    // UK currency values
    std::vector<double> uk_costs = {1250.75, 2300.50, 5600.25, 10250.00};
    
    std::unordered_map<std::string, std::string> labels = {
        {"department", "healthcare"},
        {"project", "omop_etl"}
    };
    
    for (double cost : uk_costs) {
        EXPECT_TRUE(collector->set_gauge("uk_processing_cost", cost, labels));
    }
    
    auto metric = collector->get_metric("uk_processing_cost");
    ASSERT_TRUE(metric.has_value());
    EXPECT_EQ(metric->definition.name, "uk_processing_cost");
    EXPECT_EQ(metric->definition.type, MetricType::Gauge);
    EXPECT_EQ(metric->values.size(), 1); // Gauge keeps only latest value per label set
}

// Test histogram metric with UK measurement units
TEST_F(MetricsCollectorTest, HistogramMetricWithUKMeasurements) {
    MetricDefinition histogram_def;
    histogram_def.name = "uk_processing_duration_seconds";
    histogram_def.description = "Processing duration in seconds (UK timezone)";
    histogram_def.type = MetricType::Histogram;
    histogram_def.label_names = {"table", "region"};
    histogram_def.histogram_buckets = {0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0};
    
    EXPECT_TRUE(collector->register_metric(histogram_def));
    
    std::unordered_map<std::string, std::string> labels = {
        {"table", "patient"},
        {"region", "london"}
    };
    
    // Observe various durations
    std::vector<double> durations = {0.002, 0.008, 0.015, 0.045, 0.12, 0.8, 2.3, 8.5, 25.0, 55.0};
    
    for (double duration : durations) {
        EXPECT_TRUE(collector->observe_histogram("uk_processing_duration_seconds", duration, labels));
    }
    
    auto metric = collector->get_metric("uk_processing_duration_seconds");
    ASSERT_TRUE(metric.has_value());
    EXPECT_EQ(metric->definition.type, MetricType::Histogram);
    EXPECT_TRUE(metric->histogram_data.has_value());
    EXPECT_EQ(metric->histogram_data->total_count, durations.size());
    EXPECT_GT(metric->histogram_data->sum, 0);
}

// Test summary metric with UK postal code processing
TEST_F(MetricsCollectorTest, SummaryMetricWithUKPostalCodes) {
    MetricDefinition summary_def;
    summary_def.name = "uk_postcode_processing_time";
    summary_def.description = "UK postcode processing time distribution";
    summary_def.type = MetricType::Summary;
    summary_def.label_names = {"region", "format"};
    summary_def.summary_quantiles = {0.5, 0.75, 0.9, 0.95, 0.99};
    
    EXPECT_TRUE(collector->register_metric(summary_def));
    
    std::unordered_map<std::string, std::string> labels = {
        {"region", "england"},
        {"format", "standard"}
    };
    
    // Simulate processing times for various UK postcodes
    std::vector<double> processing_times = {
        0.001, 0.002, 0.001, 0.003, 0.002, 0.004, 0.001, 0.005, 0.003, 0.002,
        0.006, 0.004, 0.002, 0.007, 0.003, 0.008, 0.002, 0.009, 0.004, 0.010
    };
    
    for (double time : processing_times) {
        EXPECT_TRUE(collector->observe_summary("uk_postcode_processing_time", time, labels));
    }
    
    auto metric = collector->get_metric("uk_postcode_processing_time");
    ASSERT_TRUE(metric.has_value());
    EXPECT_EQ(metric->definition.type, MetricType::Summary);
    EXPECT_TRUE(metric->summary_data.has_value());
    EXPECT_EQ(metric->summary_data->count, processing_times.size());
    EXPECT_GT(metric->summary_data->sum, 0);
    EXPECT_EQ(metric->summary_data->quantiles.size(), 5);
}

// Test timer functionality with UK timezone
TEST_F(MetricsCollectorTest, TimerFunctionalityWithUKTimezone) {
    MetricDefinition timer_def;
    timer_def.name = "uk_operation_duration";
    timer_def.description = "UK operation duration timer";
    timer_def.type = MetricType::Histogram;
    timer_def.label_names = {"operation", "timezone"};
    timer_def.histogram_buckets = {0.001, 0.01, 0.1, 1.0, 10.0};
    
    EXPECT_TRUE(collector->register_metric(timer_def));
    
    std::unordered_map<std::string, std::string> labels = {
        {"operation", "data_validation"},
        {"timezone", "Europe/London"}
    };
    
    // Test timer start/stop
    std::string timer_id = collector->start_timer("uk_operation_duration", labels);
    EXPECT_FALSE(timer_id.empty());
    
    // Simulate work
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    auto elapsed = collector->stop_timer(timer_id);
    EXPECT_TRUE(elapsed.has_value());
    EXPECT_GT(*elapsed, 0.0);
    EXPECT_LT(*elapsed, 1.0); // Should be less than 1 second
    
    // Test stopping invalid timer
    auto invalid_elapsed = collector->stop_timer("invalid_timer_id");
    EXPECT_FALSE(invalid_elapsed.has_value());
}

// Test RAII Timer class with UK date formatting
TEST_F(MetricsCollectorTest, RAIITimerWithUKDateFormatting) {
    MetricDefinition timer_def;
    timer_def.name = "uk_raii_timer";
    timer_def.description = "UK RAII timer test";
    timer_def.type = MetricType::Histogram;
    timer_def.label_names = {"test_case", "date"};
    timer_def.histogram_buckets = {0.001, 0.01, 0.1, 1.0};
    
    EXPECT_TRUE(collector->register_metric(timer_def));
    
    // Format current date in UK format
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::tm tm{};
    
#ifdef _WIN32
    localtime_s(&tm, &time_t);
#else
    localtime_r(&time_t, &tm);
#endif
    
    std::ostringstream uk_date_stream;
    uk_date_stream << std::put_time(&tm, uk_date_format_.c_str());
    
    std::unordered_map<std::string, std::string> labels = {
        {"test_case", "raii_timer"},
        {"date", uk_date_stream.str()}
    };
    
    // Test RAII timer
    {
        Timer timer(*collector, "uk_raii_timer", labels);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        // Timer should automatically stop when going out of scope
        auto elapsed = timer.stop();
        EXPECT_TRUE(elapsed.has_value());
        EXPECT_GT(*elapsed, 0.0);
    }
    
    // Check that metric was recorded
    auto metric = collector->get_metric("uk_raii_timer");
    ASSERT_TRUE(metric.has_value());
    EXPECT_TRUE(metric->histogram_data.has_value());
    EXPECT_GT(metric->histogram_data->total_count, 0);
}

// Test metrics query functionality
TEST_F(MetricsCollectorTest, MetricsQueryFunctionality) {
    // Register multiple metrics
    std::vector<MetricDefinition> metrics = {
        {"uk_test_counter", "Test counter", MetricType::Counter, {"region"}},
        {"uk_test_gauge", "Test gauge", MetricType::Gauge, {"region"}},
        {"uk_test_histogram", "Test histogram", MetricType::Histogram, {"region"}}
    };
    
    for (const auto& metric : metrics) {
        EXPECT_TRUE(collector->register_metric(metric));
    }
    
    // Test query all metrics
    MetricsQuery query_all;
    auto all_metrics = collector->query_metrics(query_all);
    EXPECT_EQ(all_metrics.size(), 3);
    
    // Test query by type
    MetricsQuery query_counters;
    query_counters.type = MetricType::Counter;
    auto counter_metrics = collector->query_metrics(query_counters);
    EXPECT_EQ(counter_metrics.size(), 1);
    EXPECT_EQ(counter_metrics[0].definition.type, MetricType::Counter);
    
    // Test query by name pattern
    MetricsQuery query_pattern;
    query_pattern.name_pattern = "uk_test_.*";
    auto pattern_metrics = collector->query_metrics(query_pattern);
    EXPECT_EQ(pattern_metrics.size(), 3);
    
    // Test pagination
    MetricsQuery query_limited;
    query_limited.limit = 2;
    auto limited_metrics = collector->query_metrics(query_limited);
    EXPECT_EQ(limited_metrics.size(), 2);
    
    MetricsQuery query_offset;
    query_offset.offset = 1;
    query_offset.limit = 2;
    auto offset_metrics = collector->query_metrics(query_offset);
    EXPECT_EQ(offset_metrics.size(), 2);
}

// Test metric names retrieval
TEST_F(MetricsCollectorTest, MetricNamesRetrieval) {
    std::vector<std::string> expected_names = {
        "uk_metric_one",
        "uk_metric_two",
        "uk_metric_three"
    };
    
    for (const auto& name : expected_names) {
        MetricDefinition def;
        def.name = name;
        def.description = "Test metric";
        def.type = MetricType::Counter;
        
        EXPECT_TRUE(collector->register_metric(def));
    }
    
    auto metric_names = collector->get_metric_names();
    EXPECT_EQ(metric_names.size(), expected_names.size());
    
    // Check that all expected names are present
    for (const auto& expected : expected_names) {
        EXPECT_THAT(metric_names, Contains(expected));
    }
}

// Test Prometheus export format
TEST_F(MetricsCollectorTest, PrometheusExportFormat) {
    // Register and populate a simple counter
    MetricDefinition counter_def;
    counter_def.name = "uk_test_requests_total";
    counter_def.description = "Total number of UK test requests";
    counter_def.type = MetricType::Counter;
    counter_def.label_names = {"region", "status"};
    
    EXPECT_TRUE(collector->register_metric(counter_def));
    
    std::unordered_map<std::string, std::string> labels = {
        {"region", "london"},
        {"status", "success"}
    };
    
    EXPECT_TRUE(collector->increment_counter("uk_test_requests_total", 5.0, labels));
    
    // Export in Prometheus format
    std::string prometheus_output = collector->export_metrics("prometheus");
    EXPECT_FALSE(prometheus_output.empty());
    
    // Check for expected Prometheus format elements
    EXPECT_THAT(prometheus_output, HasSubstr("# HELP uk_test_requests_total"));
    EXPECT_THAT(prometheus_output, HasSubstr("# TYPE uk_test_requests_total counter"));
    EXPECT_THAT(prometheus_output, HasSubstr("uk_test_requests_total"));
}

// Test JSON export format
TEST_F(MetricsCollectorTest, JSONExportFormat) {
    // Register and populate a simple gauge
    MetricDefinition gauge_def;
    gauge_def.name = "uk_test_value";
    gauge_def.description = "Test value in UK format";
    gauge_def.type = MetricType::Gauge;
    gauge_def.label_names = {"unit"};
    
    EXPECT_TRUE(collector->register_metric(gauge_def));
    
    std::unordered_map<std::string, std::string> labels = {
        {"unit", "pounds"}
    };
    
    EXPECT_TRUE(collector->set_gauge("uk_test_value", 1234.56, labels));
    
    // Export in JSON format
    std::string json_output = collector->export_metrics("json");
    EXPECT_FALSE(json_output.empty());
    
    // Check for expected JSON format elements
    EXPECT_THAT(json_output, HasSubstr("\"metrics\""));
    EXPECT_THAT(json_output, HasSubstr("\"name\": \"uk_test_value\""));
    EXPECT_THAT(json_output, HasSubstr("\"type\": \"gauge\""));
    EXPECT_THAT(json_output, HasSubstr("\"values\""));
}

// Test metrics clearing
TEST_F(MetricsCollectorTest, MetricsClearing) {
    // Register multiple metrics
    std::vector<std::string> metric_names = {"uk_metric_1", "uk_metric_2", "uk_metric_3"};
    
    for (const auto& name : metric_names) {
        MetricDefinition def;
        def.name = name;
        def.description = "Test metric";
        def.type = MetricType::Counter;
        
        EXPECT_TRUE(collector->register_metric(def));
    }
    
    // Clear specific metric
    EXPECT_TRUE(collector->clear_metrics("uk_metric_1"));
    
    auto metric_1 = collector->get_metric("uk_metric_1");
    EXPECT_FALSE(metric_1.has_value());
    
    auto metric_2 = collector->get_metric("uk_metric_2");
    EXPECT_TRUE(metric_2.has_value());
    
    // Clear all metrics
    EXPECT_TRUE(collector->clear_metrics());
    
    auto all_names = collector->get_metric_names();
    EXPECT_TRUE(all_names.empty());
}

// Test statistics retrieval
TEST_F(MetricsCollectorTest, StatisticsRetrieval) {
    auto stats = collector->get_statistics();
    
    // Check expected statistics keys
    EXPECT_EQ(std::any_cast<size_t>(stats["total_metrics"]), 0);
    EXPECT_EQ(std::any_cast<size_t>(stats["active_timers"]), 0);
    EXPECT_EQ(std::any_cast<bool>(stats["config_enabled"]), false); // Match the fixture setup
    EXPECT_EQ(std::any_cast<int64_t>(stats["collection_interval_seconds"]), 30);
    EXPECT_EQ(std::any_cast<int64_t>(stats["retention_period_seconds"]), 86400);
    EXPECT_EQ(std::any_cast<size_t>(stats["total_metric_values"]), 0);
}

// Test configuration update
TEST_F(MetricsCollectorTest, ConfigurationUpdate) {
    auto original_config = collector->get_config();
    EXPECT_FALSE(original_config.enabled); // Match the fixture setup
    
    // Update configuration
    MetricsConfig new_config = original_config;
    new_config.enabled = true; // Enable it now
    new_config.collection_interval = std::chrono::seconds(60);
    new_config.retention_period = std::chrono::seconds(172800); // 48 hours
    
    EXPECT_TRUE(collector->update_config(new_config));
    
    auto updated_config = collector->get_config();
    EXPECT_TRUE(updated_config.enabled);
    EXPECT_EQ(updated_config.collection_interval, std::chrono::seconds(60));
    EXPECT_EQ(updated_config.retention_period, std::chrono::seconds(172800));
}

// Test old data deletion
TEST_F(MetricsCollectorTest, OldDataDeletion) {
    // Register metric
    MetricDefinition counter_def;
    counter_def.name = "uk_test_counter";
    counter_def.description = "Test counter";
    counter_def.type = MetricType::Counter;
    
    EXPECT_TRUE(collector->register_metric(counter_def));
    
    // Add some data
    EXPECT_TRUE(collector->increment_counter("uk_test_counter", 1.0));
    EXPECT_TRUE(collector->increment_counter("uk_test_counter", 2.0));
    
    // Delete old data (everything older than now should be 0)
    auto cutoff_time = std::chrono::system_clock::now() + std::chrono::seconds(1);
    size_t deleted_count = collector->delete_old_data(cutoff_time);
    EXPECT_EQ(deleted_count, 2);
    
    auto metric = collector->get_metric("uk_test_counter");
    ASSERT_TRUE(metric.has_value());
    EXPECT_TRUE(metric->values.empty());
}

// Test standard ETL metrics creation
TEST_F(MetricsCollectorTest, StandardETLMetricsCreation) {
    EXPECT_TRUE(create_standard_etl_metrics(*collector));
    
    // Check that standard metrics were created
    auto metric_names = collector->get_metric_names();
    EXPECT_THAT(metric_names, Contains("etl_records_processed_total"));
    EXPECT_THAT(metric_names, Contains("etl_processing_duration_seconds"));
    EXPECT_THAT(metric_names, Contains("etl_active_connections"));
    EXPECT_THAT(metric_names, Contains("etl_queue_size"));
    EXPECT_THAT(metric_names, Contains("etl_errors_total"));
    EXPECT_THAT(metric_names, Contains("etl_memory_usage_bytes"));
}

// Test concurrent access to metrics collector
TEST_F(MetricsCollectorTest, ConcurrentAccess) {
    // Register a counter metric
    MetricDefinition counter_def;
    counter_def.name = "uk_concurrent_counter";
    counter_def.description = "Concurrent counter test";
    counter_def.type = MetricType::Counter;
    counter_def.label_names = {"thread"};
    
    EXPECT_TRUE(collector->register_metric(counter_def));
    
    const int num_threads = 10;
    const int increments_per_thread = 100;
    
    std::vector<std::thread> threads;
    
    // Launch concurrent threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, increments_per_thread]() {
            std::unordered_map<std::string, std::string> labels = {
                {"thread", std::to_string(i)}
            };
            
            for (int j = 0; j < increments_per_thread; ++j) {
                collector->increment_counter("uk_concurrent_counter", 1.0, labels);
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify metric was updated
    auto metric = collector->get_metric("uk_concurrent_counter");
    ASSERT_TRUE(metric.has_value());
    EXPECT_EQ(metric->values.size(), num_threads * increments_per_thread);
}

} // namespace omop::monitoring::test