# Unit tests for core module

# Test source files in the core directory
set(CORE_TEST_SOURCES
    interfaces_test.cpp
    encoding_test.cpp
    job_manager_test.cpp
    job_scheduler_test.cpp
    pipeline_test.cpp
    record_test.cpp
    job_scheduler_strategies_test.cpp
    pipeline_integration_test.cpp
    processing_context_thread_safety_test.cpp
    validation_result_test.cpp
    processing_context_test.cpp
    additional_core_tests.cpp
    pipeline_fix_validation_test.cpp
)

# Use the new consolidated approach
add_component_unit_tests(core 
    interfaces_test.cpp
    encoding_test.cpp
    job_manager_test.cpp
    job_scheduler_test.cpp
    pipeline_test.cpp
    record_test.cpp
    job_scheduler_strategies_test.cpp
    pipeline_integration_test.cpp
    processing_context_thread_safety_test.cpp
    validation_result_test.cpp
    processing_context_test.cpp
    additional_core_tests.cpp
    pipeline_fix_validation_test.cpp
)

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "Core Unit Tests Configuration:")
message(STATUS "  Test files: ${CORE_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/core")
message(STATUS "  C++ Standard: 20")

# Remove or comment out the custom target to avoid duplicate errors
# add_custom_target(test_core_tests ...)
