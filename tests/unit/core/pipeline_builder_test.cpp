/**
 * @file pipeline_builder_test.cpp
 * @brief Unit tests for pipeline builder functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/exceptions.h"
#include <fstream>
#include <filesystem>

namespace omop::core {

using namespace testing;

class MockExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize, (const std::unordered_map<std::string, std::any>&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (const, override));
};

class MockTransformer : public ITransformer {
public:
    MOCK_METHOD(void, initialize, (const std::unordered_map<std::string, std::any>&, ProcessingContext&), (override));
    MOCK_METHOD(std::optional<Record>, transform, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, transform_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(omop::common::ValidationResult, validate, (const Record&), (const, override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (const, override));
};

class MockLoader : public ILoader {
public:
    MOCK_METHOD(void, initialize, (const std::unordered_map<std::string, std::any>&, ProcessingContext&), (override));
    MOCK_METHOD(bool, load, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(size_t, load_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(void, commit, (ProcessingContext&), (override));
    MOCK_METHOD(void, rollback, (ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (const, override));
};

class PipelineBuilderTest : public ::testing::Test {
protected:
    PipelineBuilder builder;
};

// Test default construction
TEST_F(PipelineBuilderTest, DefaultConstruction) {
    EXPECT_NO_THROW(PipelineBuilder{});
}

// Test configuration setting
TEST_F(PipelineBuilderTest, WithConfig) {
    PipelineConfig config;
    config.batch_size = 500;
    config.max_parallel_batches = 8;
    config.error_threshold = 0.05;
    
    EXPECT_NO_THROW(builder.with_config(config));
}

// Test extractor setting
TEST_F(PipelineBuilderTest, WithExtractor) {
    auto extractor = std::make_unique<MockExtractor>();
    EXPECT_CALL(*extractor, get_type()).WillRepeatedly(Return("mock"));
    
    EXPECT_NO_THROW(builder.with_extractor(std::move(extractor)));
}

// Test transformer setting
TEST_F(PipelineBuilderTest, WithTransformer) {
    auto transformer = std::make_unique<MockTransformer>();
    EXPECT_CALL(*transformer, get_type()).WillRepeatedly(Return("mock"));
    
    EXPECT_NO_THROW(builder.with_transformer(std::move(transformer)));
}

// Test loader setting
TEST_F(PipelineBuilderTest, WithLoader) {
    auto loader = std::make_unique<MockLoader>();
    EXPECT_CALL(*loader, get_type()).WillRepeatedly(Return("mock"));
    
    EXPECT_NO_THROW(builder.with_loader(std::move(loader)));
}

// Test null component validation
TEST_F(PipelineBuilderTest, RejectsNullComponents) {
    EXPECT_THROW(builder.with_extractor(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_transformer(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_loader(nullptr), common::ConfigurationException);
}

// Test callback setting
TEST_F(PipelineBuilderTest, WithCallbacks) {
    auto progress_callback = [](const JobInfo&) {};
    auto error_callback = [](const std::string&, const std::exception&) {};
    
    EXPECT_NO_THROW(builder.with_progress_callback(progress_callback));
    EXPECT_NO_THROW(builder.with_error_callback(error_callback));
}

// Test null callback validation
TEST_F(PipelineBuilderTest, RejectsNullCallbacks) {
    EXPECT_THROW(builder.with_progress_callback(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_error_callback(nullptr), common::ConfigurationException);
}

// Test processor addition
TEST_F(PipelineBuilderTest, WithProcessors) {
    auto pre_processor = [](RecordBatch&, ProcessingContext&) {};
    auto post_processor = [](RecordBatch&, ProcessingContext&) {};
    
    EXPECT_NO_THROW(builder.with_pre_processor(pre_processor));
    EXPECT_NO_THROW(builder.with_post_processor(post_processor));
}

// Test null processor validation
TEST_F(PipelineBuilderTest, RejectsNullProcessors) {
    EXPECT_THROW(builder.with_pre_processor(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_post_processor(nullptr), common::ConfigurationException);
}

// Test building pipeline
TEST_F(PipelineBuilderTest, BuildsPipeline) {
    auto pipeline = builder.build();
    ASSERT_NE(pipeline, nullptr);
}

// Test configuration file loading
TEST_F(PipelineBuilderTest, WithConfigFile) {
    // Create temporary config file
    std::string config_content = R"(
pipeline:
  batch_size: 2000
  max_parallel_batches: 6
  error_threshold: 0.02
  stop_on_error: true
)";
    
    std::filesystem::path temp_file = std::filesystem::temp_directory_path() / "test_config.yml";
    std::ofstream file(temp_file);
    file << config_content;
    file.close();
    
    EXPECT_NO_THROW(builder.with_config_file(temp_file.string()));
    
    // Clean up
    std::filesystem::remove(temp_file);
}

// Test invalid config file
TEST_F(PipelineBuilderTest, RejectsInvalidConfigFile) {
    EXPECT_THROW(builder.with_config_file("nonexistent_file.yml"), 
                 common::ConfigurationException);
}

// Test method chaining
TEST_F(PipelineBuilderTest, SupportsMethodChaining) {
    PipelineConfig config;
    auto extractor = std::make_unique<MockExtractor>();
    auto transformer = std::make_unique<MockTransformer>();
    auto loader = std::make_unique<MockLoader>();
    
    EXPECT_CALL(*extractor, get_type()).WillRepeatedly(Return("mock"));
    EXPECT_CALL(*transformer, get_type()).WillRepeatedly(Return("mock"));
    EXPECT_CALL(*loader, get_type()).WillRepeatedly(Return("mock"));
    
    auto pipeline = builder
        .with_config(config)
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();
        
    ASSERT_NE(pipeline, nullptr);
}

} // namespace omop::core