// File: tests/unit/core/processing_context_thread_safety_test.cpp

#include <gtest/gtest.h>
#include "core/interfaces.h"
#include <thread>
#include <vector>
#include <atomic>
#include <barrier>

namespace omop::core {

class ProcessingContextThreadSafetyTest : public ::testing::Test {
protected:
    ProcessingContext context;
};

TEST_F(ProcessingContextThreadSafetyTest, ConcurrentIncrementProcessed) {
    const int num_threads = 10;
    const int increments_per_thread = 1000;
    std::vector<std::thread> threads;
    std::barrier sync_point(num_threads);

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, &sync_point, increments_per_thread]() {
            sync_point.arrive_and_wait(); // Synchronize thread start
            for (int j = 0; j < increments_per_thread; ++j) {
                context.increment_processed();
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    EXPECT_EQ(context.processed_count(), num_threads * increments_per_thread);
}

TEST_F(ProcessingContextThreadSafetyTest, ConcurrentIncrementErrors) {
    const int num_threads = 10;
    const int increments_per_thread = 1000;
    std::vector<std::thread> threads;

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, increments_per_thread]() {
            for (int j = 0; j < increments_per_thread; ++j) {
                context.increment_errors();
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    EXPECT_EQ(context.error_count(), num_threads * increments_per_thread);
}

TEST_F(ProcessingContextThreadSafetyTest, ErrorThresholdWithZeroProcessed) {
    context.set_error_threshold(0.1);
    
    // Should not crash with zero processed records
    EXPECT_FALSE(context.is_error_threshold_exceeded());
    
    context.increment_errors(5);
    EXPECT_FALSE(context.is_error_threshold_exceeded()); // Still false with 0 processed
    
    context.increment_processed(10);
    EXPECT_TRUE(context.is_error_threshold_exceeded()); // 5/10 = 0.5 > 0.1
}

} // namespace omop::core