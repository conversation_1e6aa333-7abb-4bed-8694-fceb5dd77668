// File: tests/unit/core/pipeline_integration_test.cpp

#include <gtest/gtest.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include <atomic>
#include <chrono>

namespace omop::core {

class IntegrationExtractor : public IExtractor {
private:
    std::atomic<size_t> batch_count{0};
    size_t total_batches = 3;
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
        RecordBatch batch;
        if (batch_count < total_batches) {
            for (size_t i = 0; i < batch_size; ++i) {
                Record r;
                r.setField("batch", static_cast<int>(batch_count.load()));
                r.setField("index", static_cast<int>(i));
                batch.addRecord(r);
            }
            batch_count++;
        }
        return batch;
    }
    
    bool has_more_data() const override { return batch_count < total_batches; }
    std::string get_type() const override { return "integration_extractor"; }
    void finalize(ProcessingContext&) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"batches_extracted", batch_count.load()}};
    }
};

class IntegrationTransformer : public ITransformer {
private:
    std::atomic<size_t> transform_count{0};
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    std::optional<Record> transform(const Record& record, ProcessingContext&) override {
        Record r = record;
        r.setField("transformed", true);
        transform_count++;
        return r;
    }
    
    RecordBatch transform_batch(const RecordBatch& batch, ProcessingContext& context) override {
        RecordBatch result;
        for (const auto& record : batch.getRecords()) {
            auto transformed = transform(record, context);
            if (transformed) {
                result.addRecord(*transformed);
            }
        }
        return result;
    }
    
    std::string get_type() const override { return "integration_transformer"; }
    omop::common::ValidationResult validate(const Record&) const override { return omop::common::ValidationResult(); }
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"records_transformed", transform_count.load()}};
    }
};

class IntegrationLoader : public ILoader {
private:
    std::atomic<size_t> load_count{0};
    std::atomic<size_t> commit_count{0};
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    bool load(const Record&, ProcessingContext&) override {
        load_count++;
        return true;
    }
    
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        for (const auto& record : batch.getRecords()) {
            load(record, context);
        }
        return batch.size();
    }
    
    void commit(ProcessingContext&) override { commit_count++; }
    void rollback(ProcessingContext&) override {}
    std::string get_type() const override { return "integration_loader"; }
    void finalize(ProcessingContext&) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"records_loaded", load_count.load()},
            {"commits", commit_count.load()}
        };
    }
    
    size_t get_load_count() const { return load_count; }
    size_t get_commit_count() const { return commit_count; }
};

class PipelineIntegrationTest : public ::testing::Test {
protected:
    std::unique_ptr<ETLPipeline> pipeline;
    
    void SetUp() override {
        PipelineConfig config;
        config.batch_size = 10;
        config.commit_interval = 20;
        config.error_threshold = 0.1;
        pipeline = std::make_unique<ETLPipeline>(config);
    }
};

TEST_F(PipelineIntegrationTest, EndToEndExecution) {
    auto extractor = std::make_unique<IntegrationExtractor>();
    auto transformer = std::make_unique<IntegrationTransformer>();
    auto loader = std::make_unique<IntegrationLoader>();
    
    auto loader_ptr = loader.get();
    
    pipeline->set_extractor(std::move(extractor));
    pipeline->set_transformer(std::move(transformer));
    pipeline->set_loader(std::move(loader));
    
    auto future = pipeline->start("integration-test-job");
    auto job_info = future.get();
    
    EXPECT_EQ(job_info.status, JobStatus::Completed);
    EXPECT_EQ(loader_ptr->get_load_count(), 30); // 3 batches * 10 records
    EXPECT_GT(loader_ptr->get_commit_count(), 0); // At least one commit
}

TEST_F(PipelineIntegrationTest, ErrorThresholdHandling) {
    // Test pipeline behavior when error threshold is exceeded
    class FailingTransformer : public IntegrationTransformer {
    private:
        std::atomic<size_t> count{0};
        
    public:
        std::optional<Record> transform(const Record& record, ProcessingContext& context) override {
            if (++count % 3 == 0) {
                context.increment_errors();
                return std::nullopt; // Fail every 3rd record
            }
            return IntegrationTransformer::transform(record, context);
        }
    };
    
    // Create a new pipeline with the correct error threshold config
    PipelineConfig config;
    config.batch_size = 10;
    config.commit_interval = 20;
    config.error_threshold = 0.2; // 20% error threshold
    config.stop_on_error = true;
    
    auto error_pipeline = std::make_unique<ETLPipeline>(config);
    
    error_pipeline->set_extractor(std::make_unique<IntegrationExtractor>());
    error_pipeline->set_transformer(std::make_unique<FailingTransformer>());
    error_pipeline->set_loader(std::make_unique<IntegrationLoader>());
    
    auto future = error_pipeline->start("error-test-job");
    auto job_info = future.get();
    
    // Pipeline should fail due to error threshold
    EXPECT_EQ(job_info.status, JobStatus::Failed);
}

} // namespace omop::core