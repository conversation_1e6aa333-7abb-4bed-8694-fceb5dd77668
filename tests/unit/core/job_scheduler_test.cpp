/**
 * @file test_job_scheduler.cpp
 * @brief Unit tests for JobScheduler class
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/job_scheduler.h"
#include "core/job_manager.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <thread>
#include <chrono>

using namespace omop::core;
using namespace testing;

// For testing, we'll use a real JobManager since it's not designed for mocking

// JobSchedule tests
class JobScheduleTest : public ::testing::Test {
protected:
    JobSchedule schedule;
};

// Test verifies that JobSchedule default constructor initializes all fields to expected defaults
TEST_F(JobScheduleTest, DefaultConstruction) {
    EXPECT_TRUE(schedule.schedule_id.empty());
    EXPECT_TRUE(schedule.job_config_id.empty());
    EXPECT_EQ(schedule.trigger_type, TriggerType::MANUAL);
    EXPECT_TRUE(schedule.cron_expression.empty());
    EXPECT_TRUE(schedule.enabled);
    EXPECT_TRUE(schedule.dependencies.empty());
    EXPECT_TRUE(schedule.parameters.empty());
}

// Test verifies that JobSchedule fields can be configured and retrieved correctly
TEST_F(JobScheduleTest, ScheduleConfiguration) {
    schedule.schedule_id = "sched-123";
    schedule.job_config_id = "job-config-456";
    schedule.trigger_type = TriggerType::SCHEDULED;
    schedule.cron_expression = "0 * * * *";
    schedule.enabled = false;
    schedule.dependencies = {"dep1", "dep2"};
    schedule.parameters["param1"] = "value1";

    EXPECT_EQ(schedule.schedule_id, "sched-123");
    EXPECT_EQ(schedule.trigger_type, TriggerType::SCHEDULED);
    EXPECT_FALSE(schedule.enabled);
    EXPECT_EQ(schedule.dependencies.size(), 2);
}

// QueuedJob tests
class QueuedJobTest : public ::testing::Test {
protected:
    QueuedJob job;
};

// Test verifies that QueuedJob default constructor initializes all fields to expected defaults
TEST_F(QueuedJobTest, DefaultConstruction) {
    EXPECT_TRUE(job.job_id.empty());
    EXPECT_TRUE(job.dependencies.empty());
    EXPECT_EQ(job.callback, nullptr);
}

// Test verifies that QueuedJob fields can be configured and retrieved correctly
TEST_F(QueuedJobTest, QueuedJobSetup) {
    job.job_id = "job-123";
    job.job_config.job_name = "Test Job";
    job.priority = JobPriority::HIGH;
    job.enqueue_time = std::chrono::system_clock::now();
    job.deadline = std::chrono::system_clock::now() + std::chrono::hours(1);
    job.dependencies = {"dep1", "dep2", "dep3"};
    job.callback = []() { /* do something */ };

    EXPECT_EQ(job.job_id, "job-123");
    EXPECT_EQ(job.priority, JobPriority::HIGH);
    EXPECT_EQ(job.dependencies.size(), 3);
    EXPECT_NE(job.callback, nullptr);
}

// JobScheduler tests
class JobSchedulerTest : public ::testing::Test {
protected:
    std::shared_ptr<JobManager> job_manager;
    std::unique_ptr<JobScheduler> scheduler;

    void SetUp() override {
        auto config = std::make_shared<omop::common::ConfigurationManager>();
        auto logger = omop::common::Logger::get("test");
        job_manager = std::make_shared<JobManager>(config, logger);
        scheduler = std::make_unique<JobScheduler>(job_manager);
    }

    void TearDown() override {
        if (scheduler) {
            scheduler->stop();
        }
    }
};

// Test verifies that JobScheduler constructor initializes with zero job statistics
TEST_F(JobSchedulerTest, Construction) {
    EXPECT_NE(scheduler, nullptr);
    auto stats = scheduler->getStatistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["jobs_scheduled"]), 0);
    EXPECT_EQ(std::any_cast<size_t>(stats["jobs_executed"]), 0);
}

// Test verifies that JobScheduler throws exception when constructed with null job manager
TEST_F(JobSchedulerTest, InvalidConstructionThrows) {
    EXPECT_THROW(JobScheduler(nullptr), omop::common::ConfigurationException);
}

// Test verifies that JobScheduler start and stop operations work correctly
TEST_F(JobSchedulerTest, StartAndStop) {
    EXPECT_TRUE(scheduler->start());
    EXPECT_FALSE(scheduler->start()); // Already running

    scheduler->stop();
    // Can call stop multiple times
    scheduler->stop();
}

// Test verifies that JobScheduler can add schedules and retrieve them by ID
TEST_F(JobSchedulerTest, AddSchedule) {
    JobSchedule schedule;
    schedule.job_config_id = "config-123";
    schedule.trigger_type = TriggerType::MANUAL;

    auto schedule_id = scheduler->addSchedule(schedule);
    EXPECT_FALSE(schedule_id.empty());

    auto retrieved = scheduler->getSchedule(schedule_id);
    ASSERT_TRUE(retrieved.has_value());
    EXPECT_EQ(retrieved->job_config_id, "config-123");
}

// Test verifies that JobScheduler can add schedules with cron expressions and calculates next run time
TEST_F(JobSchedulerTest, AddScheduleWithCron) {
    scheduler->start();

    JobSchedule schedule;
    schedule.trigger_type = TriggerType::SCHEDULED;
    schedule.cron_expression = "0 * * * *"; // Every hour

    auto schedule_id = scheduler->addSchedule(schedule);

    auto retrieved = scheduler->getSchedule(schedule_id);
    ASSERT_TRUE(retrieved.has_value());
    EXPECT_GT(retrieved->next_run.time_since_epoch().count(), 0);
}

// Test verifies that JobScheduler can remove schedules and handles removal of non-existent schedules
TEST_F(JobSchedulerTest, RemoveSchedule) {
    JobSchedule schedule;
    auto schedule_id = scheduler->addSchedule(schedule);

    EXPECT_TRUE(scheduler->removeSchedule(schedule_id));
    EXPECT_FALSE(scheduler->removeSchedule(schedule_id)); // Already removed

    auto retrieved = scheduler->getSchedule(schedule_id);
    EXPECT_FALSE(retrieved.has_value());
}

// Test verifies that JobScheduler can update existing schedules with new configuration
TEST_F(JobSchedulerTest, UpdateSchedule) {
    JobSchedule schedule;
    schedule.job_config_id = "original";
    auto schedule_id = scheduler->addSchedule(schedule);

    JobSchedule updated;
    updated.job_config_id = "updated";
    updated.trigger_type = TriggerType::EVENT;

    EXPECT_TRUE(scheduler->updateSchedule(schedule_id, updated));

    auto retrieved = scheduler->getSchedule(schedule_id);
    ASSERT_TRUE(retrieved.has_value());
    EXPECT_EQ(retrieved->job_config_id, "updated");
    EXPECT_EQ(retrieved->trigger_type, TriggerType::EVENT);
}

// Test verifies that JobScheduler returns false when updating non-existent schedule
TEST_F(JobSchedulerTest, UpdateNonExistentSchedule) {
    JobSchedule schedule;
    EXPECT_FALSE(scheduler->updateSchedule("nonexistent", schedule));
}

// Test verifies that JobScheduler can enable and disable schedules correctly
TEST_F(JobSchedulerTest, EnableDisableSchedule) {
    JobSchedule schedule;
    auto schedule_id = scheduler->addSchedule(schedule);

    EXPECT_TRUE(scheduler->setScheduleEnabled(schedule_id, false));

    auto retrieved = scheduler->getSchedule(schedule_id);
    ASSERT_TRUE(retrieved.has_value());
    EXPECT_FALSE(retrieved->enabled);

    EXPECT_TRUE(scheduler->setScheduleEnabled(schedule_id, true));
    retrieved = scheduler->getSchedule(schedule_id);
    EXPECT_TRUE(retrieved->enabled);
}

// Test verifies that JobScheduler returns all registered schedules
TEST_F(JobSchedulerTest, GetAllSchedules) {
    auto initial = scheduler->getAllSchedules();
    size_t initial_count = initial.size();

    // Add multiple schedules
    for (int i = 0; i < 5; ++i) {
        JobSchedule schedule;
        schedule.job_config_id = "config-" + std::to_string(i);
        scheduler->addSchedule(schedule);
    }

    auto all = scheduler->getAllSchedules();
    EXPECT_EQ(all.size(), initial_count + 5);
}

// Test verifies that JobScheduler can submit jobs and updates statistics
TEST_F(JobSchedulerTest, SubmitJob) {
    scheduler->start();
    job_manager->start(); // Start the job manager too

    JobConfig config;
    config.job_name = "Test Job";
    config.pipeline_config_path = "test_pipeline.yaml"; // Add required field

    auto job_id = scheduler->submitJob(config, JobPriority::HIGH);
    EXPECT_FALSE(job_id.empty());

    auto stats = scheduler->getStatistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["jobs_scheduled"]), 1);
}

// Test verifies that JobScheduler can submit jobs with dependency requirements
TEST_F(JobSchedulerTest, SubmitJobWithDependencies) {
    scheduler->start();

    JobConfig config;
    config.job_name = "Dependent Job";
    std::vector<std::string> deps = {"dep1", "dep2"};

    auto job_id = scheduler->submitJob(config, JobPriority::NORMAL, deps);
    EXPECT_FALSE(job_id.empty());
}

// Test verifies that JobScheduler returns queued jobs in priority order
TEST_F(JobSchedulerTest, GetQueuedJobs) {
    // Submit some jobs without starting scheduler
    for (int i = 0; i < 3; ++i) {
        JobConfig config;
        config.job_name = "Job " + std::to_string(i);
        scheduler->submitJob(config, static_cast<JobPriority>(i));
    }

    auto queued = scheduler->getQueuedJobs();
    // Jobs should be in priority order
    EXPECT_GE(queued.size(), 3);
}
