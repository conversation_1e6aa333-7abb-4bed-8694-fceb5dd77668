/**
 * @file json_extractor_test.cpp
 * @brief Unit tests for JSON extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/json_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <iostream>
#include "extract/extractor_factory.h"

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using json = nlohmann::json;

// Test fixture for JSON extractor tests
class JsonExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_json_test";
        std::filesystem::create_directories(test_dir_);
        
        // Register JSON extractors with the factory registry
        JsonExtractorFactory::register_extractors();
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Helper function to create test JSON file
    std::string createTestJson(const std::string& filename, const json& content) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        file << content.dump(2);
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// ============================================================================
// JsonExtractorTest Tests
// ============================================================================

// Tests detailed error reporting
TEST_F(JsonExtractorTest, DetailedErrorReporting) {
    // Create invalid JSON file
    std::string filepath = (test_dir_ / "invalid.json").string();
    std::ofstream file(filepath);
    file << "{\"invalid\": json}"; // Invalid JSON
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests extract JSON array
TEST_F(JsonExtractorTest, ExtractJsonArray) {
    json content = json::array({
        {{"name", "John"}, {"age", 30}, {"city", "New York"}},
        {{"name", "Jane"}, {"age", 25}, {"city", "Los Angeles"}}
    });

    std::string filepath = createTestJson("array.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ("John", std::any_cast<std::string>(records[0].getField("name")));
    EXPECT_EQ(30LL, std::any_cast<int64_t>(records[0].getField("age")));
}

// Tests extract single object
TEST_F(JsonExtractorTest, ExtractSingleObject) {
    json content = {
        {"id", 1},
        {"name", "John Doe"},
        {"email", "<EMAIL>"}
    };

    std::string filepath = createTestJson("single.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(1, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("John Doe", std::any_cast<std::string>(records[0].getField("name")));
}

// Tests extract with root path
TEST_F(JsonExtractorTest, ExtractWithRootPath) {
    json content = {
        {"data", {
            {"patients", json::array({
                {{"id", 1}, {"name", "John Doe"}},
                {{"id", 2}, {"name", "Jane Smith"}}
            })}
        }}
    };

    std::string filepath = createTestJson("nested.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["root_path"] = std::string("data.patients");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("Jane Smith", std::any_cast<std::string>(records[1].getField("name")));
}

// Tests extreme limits and boundaries
TEST_F(JsonExtractorTest, ExtremeLimitsAndBoundaries) {
    // Test with very large JSON
    json content = json::array();
    for (int i = 0; i < 1000; ++i) {
        json record = {
            {"id", i},
            {"name", "User" + std::to_string(i)},
            {"data", std::string(1000, 'x')} // Large string
        };
        content.push_back(record);
    }

    std::string filepath = createTestJson("large.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["batch_size"] = size_t(100);

    ProcessingContext context;
    extractor.initialize(config, context);

    // Extract in batches
    int total_records = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(100, context);
        total_records += batch.size();
    }

    EXPECT_EQ(1000, total_records);
}

// Tests flatten arrays
TEST_F(JsonExtractorTest, FlattenArrays) {
    json content = json::array({
        {
            {"id", 1},
            {"tags", json::array({"tag1", "tag2", "tag3"})},
            {"scores", json::array({85, 90, 95})}
        }
    });

    std::string filepath = createTestJson("array_flatten.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["flatten_nested"] = true;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    EXPECT_EQ("tag1", std::any_cast<std::string>(records[0].getField("tags_0")));
    EXPECT_EQ("tag2", std::any_cast<std::string>(records[0].getField("tags_1")));
    EXPECT_EQ(85LL, std::any_cast<int64_t>(records[0].getField("scores_0")));
}

// Tests flatten nested objects
TEST_F(JsonExtractorTest, FlattenNestedObjects) {
    json content = json::array({
        {
            {"id", 1},
            {"person", {
                {"name", "John"},
                {"address", {
                    {"street", "123 Main St"},
                    {"city", "New York"}
                }}
            }}
        }
    });

    std::string filepath = createTestJson("nested_flatten.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["flatten_nested"] = true;
    config["array_delimiter"] = std::string("_");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    EXPECT_EQ("John", std::any_cast<std::string>(records[0].getField("person_name")));
    EXPECT_EQ("123 Main St", std::any_cast<std::string>(records[0].getField("person_address_street")));
    EXPECT_EQ("New York", std::any_cast<std::string>(records[0].getField("person_address_city")));
}

// Tests get statistics
TEST_F(JsonExtractorTest, GetStatistics) {
    json content = json::array({
        {{"id", 1}, {"name", "John"}},
        {{"id", 2}, {"name", "Jane"}},
        {{"id", 3}, {"name", "Bob"}}
    });

    std::string filepath = createTestJson("stats.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto stats = extractor.get_statistics();

    EXPECT_EQ(3, std::any_cast<size_t>(stats["total_records"]));
    EXPECT_EQ(3, std::any_cast<size_t>(stats["successful_records"]));
    EXPECT_EQ(0, std::any_cast<size_t>(stats["failed_records"]));
}

// Tests handle invalid JSON
TEST_F(JsonExtractorTest, HandleInvalidJson) {
    // Create invalid JSON file
    std::string filepath = (test_dir_ / "invalid.json").string();
    std::ofstream file(filepath);
    file << "{\"invalid\": json}"; // Invalid JSON
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests handle missing file
TEST_F(JsonExtractorTest, HandleMissingFile) {
    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string("/nonexistent/file.json");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests handle null values
TEST_F(JsonExtractorTest, HandleNullValues) {
    json content = json::array({
        {
            {"id", 1},
            {"name", "John"},
            {"age", nullptr},
            {"city", json::value_t::null}
        }
    });

    std::string filepath = createTestJson("nulls.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("John", std::any_cast<std::string>(records[0].getField("name")));
    // Null values should be handled gracefully
    EXPECT_FALSE(records[0].hasField("age"));
    EXPECT_FALSE(records[0].hasField("city"));
}

// Tests JSON extractor factory
TEST_F(JsonExtractorTest, JsonExtractorFactory) {
    // Test factory registration
    auto extractor = omop::extract::ExtractorFactoryRegistry::create("json");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "json");

    // Test JSONL alias
    auto jsonl_extractor = omop::extract::ExtractorFactoryRegistry::create("jsonl");
    EXPECT_NE(jsonl_extractor, nullptr);
    EXPECT_EQ(jsonl_extractor->get_type(), "jsonl");
}

// Tests JSON lines edge cases
TEST_F(JsonExtractorTest, JsonLinesEdgeCases) {
    // Create JSONL file with various edge cases
    std::string filepath = (test_dir_ / "edges.jsonl").string();
    std::ofstream file(filepath);
    file << "{\"id\": 1, \"name\": \"John\"}\n";
    file << "{\"id\": 2, \"name\": \"Jane\", \"extra\": null}\n";
    file << "{\"id\": 3}\n"; // Missing name
    file << "{\"id\": 4, \"nested\": {\"value\": 42}}\n";
    file << "\n"; // Empty line
    file << "{\"id\": 5, \"array\": [1, 2, 3]}\n";
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["format"] = std::string("jsonl");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(5, batch.size()); // Should skip empty line
}

// Tests JSON lines extractor
TEST_F(JsonExtractorTest, JsonLinesExtractor) {
    // Create JSONL file
    std::string filepath = (test_dir_ / "test.jsonl").string();
    std::ofstream file(filepath);
    file << "{\"id\": 1, \"name\": \"John\"}\n";
    file << "{\"id\": 2, \"name\": \"Jane\"}\n";
    file << "{\"id\": 3, \"name\": \"Bob\"}\n";
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["format"] = std::string("jsonl");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(3, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ(1LL, std::any_cast<int64_t>(records[0].getField("id")));
    EXPECT_EQ("Jane", std::any_cast<std::string>(records[1].getField("name")));
}

// Tests JSON lines with errors
TEST_F(JsonExtractorTest, JsonLinesWithErrors) {
    // Create JSONL file with invalid lines
    std::string filepath = (test_dir_ / "errors.jsonl").string();
    std::ofstream file(filepath);
    file << "{\"id\": 1, \"name\": \"John\"}\n";
    file << "invalid json line\n";
    file << "{\"id\": 2, \"name\": \"Jane\"}\n";
    file.close();

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["format"] = std::string("jsonl");
    config["continue_on_error"] = true;

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size()); // Should skip invalid line
}

// Tests max depth limit
TEST_F(JsonExtractorTest, MaxDepthLimit) {
    // Create deeply nested JSON
    json content = {
        {"level1", {
            {"level2", {
                {"level3", {
                    {"level4", {
                        {"level5", {
                            {"level6", {
                                {"level7", {
                                    {"level8", {
                                        {"level9", {
                                            {"level10", "value"}
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
    };

    std::string filepath = createTestJson("deep.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["max_depth"] = int(5);

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    // Should not exceed max depth
    EXPECT_FALSE(records[0].hasField("level1_level2_level3_level4_level5_level6"));
}

// Tests parse dates
TEST_F(JsonExtractorTest, ParseDates) {
    json content = json::array({
        {
            {"id", 1},
            {"created_date", "2024-01-15"},
            {"updated_time", "2024-01-15 10:30:45"}
        }
    });

    std::string filepath = createTestJson("dates.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["parse_dates"] = true;
    config["date_formats"] = std::vector<std::string>{"%Y-%m-%d", "%Y-%m-%d %H:%M:%S"};

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    auto date_field = records[0].getFieldOptional("created_date");
    EXPECT_TRUE(date_field->type() == typeid(std::chrono::system_clock::time_point));
}

// Tests streaming JSON extractor
TEST_F(JsonExtractorTest, StreamingJsonExtractor) {
    // Create large JSON file for streaming test
    json content = json::array();
    for (int i = 0; i < 100; ++i) {
        json record = {
            {"id", i},
            {"name", "User" + std::to_string(i)},
            {"data", "Some data for user " + std::to_string(i)}
        };
        content.push_back(record);
    }

    std::string filepath = createTestJson("streaming.json", content);

    JsonExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["streaming"] = true;
    config["batch_size"] = size_t(10);

    ProcessingContext context;
    extractor.initialize(config, context);

    // Extract in small batches
    int total_records = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(10, context);
        total_records += batch.size();
        EXPECT_LE(batch.size(), 10); // Should not exceed batch size
    }

    EXPECT_EQ(100, total_records);
}