/**
 * @file extractor_base_test.cpp
 * @brief Unit tests for extractor base class functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_base.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "common/validation.h"
#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Invoke;

// Mock extractor implementation for testing ExtractorBase functionality
class MockExtractor : public ExtractorBase {
public:
    MockExtractor(const std::string& name = "test_extractor",
                  std::shared_ptr<ConfigurationManager> config = nullptr,
                  std::shared_ptr<Logger> logger = nullptr)
        : ExtractorBase(name, config, logger),
          mock_records_(),
          current_record_(0),
          should_connect_(true),
          should_validate_(true),
          mock_schema_() {

        // Set up default mock schema
        mock_schema_.source_name = "test_source";
        mock_schema_.source_type = "mock";
        SourceSchema::Column col1{"id", "integer", false, {}, {}, "Primary key"};
        SourceSchema::Column col2{"name", "string", true, 255, {}, "Name field"};
        SourceSchema::Column col3{"value", "double", true, {}, {}, "Value field"};
        mock_schema_.columns = {col1, col2, col3};
        mock_schema_.primary_keys = {"id"};
    }

    // Override abstract methods
    std::string get_type() const override { return "mock"; }

    SourceSchema getSchema() const override { return mock_schema_; }

    omop::common::ValidationResult validateSource() override {
        if (should_validate_) {
            return omop::common::ValidationResult(); // Valid by default
        } else {
            omop::common::ValidationResult result;
            result.add_error("schema", "Mock validation failed", "test");
            return result;
        }
    }

    // Mock protected methods
    bool connect() override { return should_connect_; }
    void disconnect() override { /* Mock disconnect */ }

    std::vector<omop::core::Record> extractBatchImpl(size_t batch_size) override {
        std::vector<omop::core::Record> batch;
        size_t records_to_extract = std::min(batch_size, mock_records_.size() - current_record_);

        for (size_t i = 0; i < records_to_extract; ++i) {
            if (current_record_ < mock_records_.size()) {
                batch.push_back(mock_records_[current_record_++]);
            }
        }

        return batch;
    }

    omop::core::Record convertToRecord(const std::any& source_data) override {
        // Simple conversion - assume source_data is already a Record
        if (source_data.type() == typeid(omop::core::Record)) {
            return std::any_cast<omop::core::Record>(source_data);
        }
        // Create a default record if conversion fails
        omop::core::Record record;
        record.setField("converted", true);
        return record;
    }

    // Test helper methods
    void setMockRecords(const std::vector<omop::core::Record>& records) {
        mock_records_ = records;
        current_record_ = 0;
    }

    void setShouldConnect(bool should) { should_connect_ = should; }
    void setShouldValidate(bool should) { should_validate_ = should; }

    void setMockSchema(const SourceSchema& schema) { mock_schema_ = schema; }

    // Expose protected members for testing
    bool isConnected() const { return is_connected_; }
    bool isInitialized() const { return is_initialized_; }
    size_t getCurrentPosition() const { return current_position_; }
    const ExtractionStats& getStats() const { return stats_; }
    const ExtractionOptions& getOptions() const { return options_; }

    using ExtractorBase::selectColumns;
    using ExtractorBase::handleError;
    using ExtractorBase::updateProgress;

    void initialize(const std::unordered_map<std::string, std::any>& config, omop::core::ProcessingContext& context) override {
        if (auto it = config.find("columns"); it != config.end()) {
            options_.columns = std::any_cast<std::vector<std::string>>(it->second);
        }
        if (auto it = config.find("skip_records"); it != config.end()) {
            options_.skip_records = std::any_cast<size_t>(it->second);
        }
        if (auto it = config.find("filter_expression"); it != config.end()) {
            options_.filter_expression = std::any_cast<std::string>(it->second);
        }
        ExtractorBase::initialize(config, context);
    }

protected:
    void resetImpl() override {
        current_record_ = 0;
    }

private:
    std::vector<omop::core::Record> mock_records_;
    size_t current_record_;
    bool should_connect_;
    bool should_validate_;
    SourceSchema mock_schema_;
};

// Test fixture for extractor base tests
class ExtractorBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<ConfigurationManager>();
        logger_ = std::make_shared<Logger>("test");
        extractor_ = std::make_unique<MockExtractor>("test_extractor", config_, logger_);
    }

    void TearDown() override {
        if (extractor_ && extractor_->isInitialized()) {
            extractor_->close();
        }
    }

    std::shared_ptr<ConfigurationManager> config_;
    std::shared_ptr<Logger> logger_;
    std::unique_ptr<MockExtractor> extractor_;
};

// ============================================================================
// ExtractorBaseTest Tests
// ============================================================================

// Tests constructor functionality
TEST_F(ExtractorBaseTest, Constructor) {
    EXPECT_NO_THROW({
        MockExtractor extractor("test_name", config_, logger_);
    });

    EXPECT_EQ(extractor_->getName(), "test_extractor");
    EXPECT_EQ(extractor_->get_type(), "mock");
    EXPECT_FALSE(extractor_->isInitialized());
    EXPECT_FALSE(extractor_->isConnected());
}

// Tests custom options configuration
TEST_F(ExtractorBaseTest, CustomOptions) {
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(1000);
    config["max_records"] = size_t(5000);
    config["continue_on_error"] = true;
    config["validate_schema"] = false;
    config["columns"] = std::vector<std::string>{"id", "name"};
    config["skip_records"] = size_t(10);
    config["filter_expression"] = std::string("id > 100");

    ProcessingContext context;
    EXPECT_NO_THROW(extractor_->initialize(config, context));

    auto options = extractor_->getOptions();
    EXPECT_EQ(options.batch_size, 1000);
    EXPECT_EQ(options.max_records, 5000);
    EXPECT_TRUE(options.continue_on_error);
    EXPECT_FALSE(options.validate_schema);
    EXPECT_EQ(options.columns, (std::vector<std::string>{"id", "name"}));
    EXPECT_EQ(options.skip_records, 10);
    EXPECT_EQ(options.filter_expression, "id > 100");
}

// Tests current position tracking
TEST_F(ExtractorBaseTest, CurrentPositionTracking) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 10; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract in batches and verify position tracking
    EXPECT_EQ(extractor_->getCurrentPosition(), 0);

    auto batch1 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch1.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 3);

    auto batch2 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch2.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 6);

    auto batch3 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch3.size(), 4); // Only 4 records left
    EXPECT_EQ(extractor_->getCurrentPosition(), 10);
}

// Tests default options
TEST_F(ExtractorBaseTest, DefaultOptions) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto options = extractor_->getOptions();
    EXPECT_EQ(options.batch_size, 1000); // Default batch size
    EXPECT_EQ(options.max_records, 0); // No limit by default
    EXPECT_TRUE(options.continue_on_error); // Continue on error by default
    EXPECT_TRUE(options.validate_schema); // Validate schema by default
}

// Tests double close and finalize
TEST_F(ExtractorBaseTest, DoubleCloseAndFinalize) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // First close and finalize
    EXPECT_NO_THROW(extractor_->close());
    EXPECT_NO_THROW(extractor_->finalize(context));

    // Second close and finalize should not throw
    EXPECT_NO_THROW(extractor_->close());
    EXPECT_NO_THROW(extractor_->finalize(context));
}

// Tests extract after close
TEST_F(ExtractorBaseTest, ExtractAfterClose) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Close the extractor
    extractor_->close();

    // Try to extract after close - should throw or return empty batch
    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 0);
}

// Tests extract batch with column selection
TEST_F(ExtractorBaseTest, ExtractBatchWithColumnSelection) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        record.setField("value", 100.0 + i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    config["columns"] = std::vector<std::string>{"id", "name"};
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);

    // Verify only selected columns are present
    for (const auto& record : batch.getRecords()) {
        EXPECT_TRUE(record.hasField("id"));
        EXPECT_TRUE(record.hasField("name"));
        EXPECT_FALSE(record.hasField("value")); // Should not be selected
    }
}

// Tests extract batch with empty records
TEST_F(ExtractorBaseTest, ExtractBatchWithEmptyRecords) {
    // Set up extractor with no records
    extractor_->setMockRecords({});

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 0);
}

// Tests extract batch with error handling
TEST_F(ExtractorBaseTest, ExtractBatchWithErrorHandling) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    config["continue_on_error"] = true;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);
}

// Tests extract batch with error throwing
TEST_F(ExtractorBaseTest, ExtractBatchWithErrorThrowing) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    config["continue_on_error"] = false;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);
}

// Tests extract batch with missing fields
TEST_F(ExtractorBaseTest, ExtractBatchWithMissingFields) {
    // Create test records with missing fields
    std::vector<Record> test_records;
    for (int i = 0; i < 3; ++i) {
        Record record;
        record.setField("id", i);
        // Some records missing 'name' field
        if (i % 2 == 0) {
            record.setField("name", "record_" + std::to_string(i));
        }
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Tests extract batch not initialized
TEST_F(ExtractorBaseTest, ExtractBatchNotInitialized) {
    ProcessingContext context;
    EXPECT_THROW(extractor_->extract_batch(10, context), std::runtime_error);
}

// Tests extract batch success
TEST_F(ExtractorBaseTest, ExtractBatchSuccess) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 10; ++i) {
        Record record;
        record.setField("id", i);
        record.setField("name", "record_" + std::to_string(i));
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract first batch
    auto batch1 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch1.size(), 5);

    // Extract second batch
    auto batch2 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch2.size(), 5);

    // Extract third batch (should be empty)
    auto batch3 = extractor_->extract_batch(5, context);
    EXPECT_EQ(batch3.size(), 0);
}

// Tests get name returns correct value
TEST_F(ExtractorBaseTest, GetNameReturnsCorrectValue) {
    MockExtractor extractor("custom_name", config_, logger_);
    EXPECT_EQ(extractor.getName(), "custom_name");
}

// Tests get schema
TEST_F(ExtractorBaseTest, GetSchema) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto schema = extractor_->getSchema();
    EXPECT_EQ(schema.source_name, "test_source");
    EXPECT_EQ(schema.source_type, "mock");
    EXPECT_EQ(schema.columns.size(), 3);
    EXPECT_EQ(schema.primary_keys.size(), 1);
    EXPECT_EQ(schema.primary_keys[0], "id");
}

// Tests get statistics internal
TEST_F(ExtractorBaseTest, GetStatisticsInternal) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.total_records, 0);
    EXPECT_EQ(stats.successful_records, 0);
    EXPECT_EQ(stats.failed_records, 0);
}

// Tests get type returns correct value
TEST_F(ExtractorBaseTest, GetTypeReturnsCorrectValue) {
    EXPECT_EQ(extractor_->get_type(), "mock");
}

// Tests handle error
TEST_F(ExtractorBaseTest, HandleError) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Test error message";
    EXPECT_NO_THROW(extractor_->handleError(error_message, std::nullopt));

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.failed_records, 1);
}

// Tests handle error with context
TEST_F(ExtractorBaseTest, HandleErrorWithContext) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Test error with context";
    std::any context_info = std::string("Additional context");
    EXPECT_NO_THROW(extractor_->handleError(error_message, context_info));

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.failed_records, 1);
}

// Tests handle multiple errors in batch
TEST_F(ExtractorBaseTest, HandleMultipleErrorsInBatch) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Handle multiple errors
    extractor_->handleError("Error 1", std::nullopt);
    extractor_->handleError("Error 2", std::nullopt);
    extractor_->handleError("Error 3", std::nullopt);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.failed_records, 3);
}

// Tests handle error no colon
TEST_F(ExtractorBaseTest, HandleErrorNoColon) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Error without colon";
    EXPECT_NO_THROW(extractor_->handleError(error_message, std::nullopt));
}

// Tests handle error special characters
TEST_F(ExtractorBaseTest, HandleErrorSpecialCharacters) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    std::string error_message = "Error with special chars: \n\t\r\"'\\";
    EXPECT_NO_THROW(extractor_->handleError(error_message, std::nullopt));
}

// Tests has more data initialized
TEST_F(ExtractorBaseTest, HasMoreDataInitialized) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Should have more data initially
    EXPECT_TRUE(extractor_->has_more_data());

    // Extract all records
    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 5);

    // Should not have more data after extraction
    EXPECT_FALSE(extractor_->has_more_data());
}

// Tests has more data not initialized
TEST_F(ExtractorBaseTest, HasMoreDataNotInitialized) {
    EXPECT_FALSE(extractor_->has_more_data());
}

// Tests initialize already initialized
TEST_F(ExtractorBaseTest, InitializeAlreadyInitialized) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // First initialization
    EXPECT_NO_THROW(extractor_->initialize(config, context));
    EXPECT_TRUE(extractor_->isInitialized());

    // Second initialization should not throw
    EXPECT_NO_THROW(extractor_->initialize(config, context));
    EXPECT_TRUE(extractor_->isInitialized());
}

// Tests initialize connection failure
TEST_F(ExtractorBaseTest, InitializeConnectionFailure) {
    extractor_->setShouldConnect(false);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_THROW(extractor_->initialize(config, context), std::runtime_error);
    EXPECT_FALSE(extractor_->isInitialized());
}

// Tests initialize success
TEST_F(ExtractorBaseTest, InitializeSuccess) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_NO_THROW(extractor_->initialize(config, context));

    EXPECT_TRUE(extractor_->isInitialized());
    EXPECT_TRUE(extractor_->isConnected());
}

// Tests initialize validation failure
TEST_F(ExtractorBaseTest, InitializeValidationFailure) {
    extractor_->setShouldValidate(false);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_THROW(extractor_->initialize(config, context), std::runtime_error);
    EXPECT_FALSE(extractor_->isInitialized());
}

// Tests initialize with config
TEST_F(ExtractorBaseTest, InitializeWithConfig) {
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(5000);
    config["max_records"] = size_t(10000);
    config["continue_on_error"] = false;
    config["validate_schema"] = true;

    ProcessingContext context;
    EXPECT_NO_THROW(extractor_->initialize(config, context));

    auto options = extractor_->getOptions();
    EXPECT_EQ(options.batch_size, 5000);
    EXPECT_EQ(options.max_records, 10000);
    EXPECT_FALSE(options.continue_on_error);
    EXPECT_TRUE(options.validate_schema);
}

// Tests initialize with invalid config types
TEST_F(ExtractorBaseTest, InitializeWithInvalidConfigTypes) {
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = std::string("invalid"); // Should be size_t
    config["max_records"] = std::string("invalid"); // Should be size_t

    ProcessingContext context;
    EXPECT_NO_THROW(extractor_->initialize(config, context));
    // Should handle invalid types gracefully
}

// Tests progress callback
TEST_F(ExtractorBaseTest, ProgressCallback) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    bool callback_called = false;
    auto progress_callback = [&](size_t current, size_t total) {
        callback_called = true;
        EXPECT_GE(current, 0);
        EXPECT_GE(total, 0);
    };

    extractor_->setProgressCallback(progress_callback);
    EXPECT_NO_THROW(extractor_->updateProgress(5, 10));
}

// Tests progress callback called
TEST_F(ExtractorBaseTest, ProgressCallbackCalled) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    bool callback_called = false;
    extractor_->setProgressCallback([&](size_t current, size_t total) {
        callback_called = true;
    });

    extractor_->updateProgress(1, 10);
    EXPECT_TRUE(callback_called);
}

// Tests reset functionality
TEST_F(ExtractorBaseTest, Reset) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract some records
    auto batch1 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch1.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 3);

    // Reset the extractor
    EXPECT_NO_THROW(extractor_->reset());

    // Should be able to extract again from the beginning
    auto batch2 = extractor_->extract_batch(3, context);
    EXPECT_EQ(batch2.size(), 3);
    EXPECT_EQ(extractor_->getCurrentPosition(), 3);
}

// Tests select columns empty selection
TEST_F(ExtractorBaseTest, SelectColumnsEmptySelection) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Create a test record
    Record test_record;
    test_record.setField("id", 1);
    test_record.setField("name", "test");
    
    auto result = extractor_->selectColumns(test_record);
    EXPECT_EQ(result.getFieldNames().size(), 2); // Should return all fields
}

// Tests select columns with selection
TEST_F(ExtractorBaseTest, SelectColumnsWithSelection) {
    std::unordered_map<std::string, std::any> config;
    config["columns"] = std::vector<std::string>{"id", "name"};
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Create a test record
    Record test_record;
    test_record.setField("id", 1);
    test_record.setField("name", "test");
    test_record.setField("value", 100.0);
    
    auto result = extractor_->selectColumns(test_record);
    EXPECT_EQ(result.getFieldNames().size(), 2); // Should only return id and name
    EXPECT_TRUE(result.hasField("id"));
    EXPECT_TRUE(result.hasField("name"));
    EXPECT_FALSE(result.hasField("value"));
}

// Tests statistics after extraction
TEST_F(ExtractorBaseTest, StatisticsAfterExtraction) {
    // Create test records
    std::vector<Record> test_records;
    for (int i = 0; i < 10; ++i) {
        Record record;
        record.setField("id", i);
        test_records.push_back(record);
    }

    extractor_->setMockRecords(test_records);

    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    // Extract records
    auto batch = extractor_->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 10);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.total_records, 10);
    EXPECT_EQ(stats.successful_records, 10);
}

// Tests statistics default empty
TEST_F(ExtractorBaseTest, StatisticsDefaultEmpty) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;
    extractor_->initialize(config, context);

    auto stats = extractor_->getStatistics();
    EXPECT_EQ(stats.total_records, 0);
    EXPECT_EQ(stats.successful_records, 0);
    EXPECT_EQ(stats.failed_records, 0);
}

// Tests validate source failure
TEST_F(ExtractorBaseTest, ValidateSourceFailure) {
    extractor_->setShouldValidate(false);

    auto result = extractor_->validateSource();
    EXPECT_FALSE(result.is_valid());
    EXPECT_GT(result.errors().size(), 0);
}

// Tests validate source success
TEST_F(ExtractorBaseTest, ValidateSourceSuccess) {
    auto result = extractor_->validateSource();
    EXPECT_TRUE(result.is_valid());
    EXPECT_EQ(result.errors().size(), 0);
}