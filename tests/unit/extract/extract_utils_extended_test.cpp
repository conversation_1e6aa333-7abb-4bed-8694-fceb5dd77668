/**
 * @file extract_utils_extended_test.cpp
 * @brief Extended unit tests for extract utilities covering missing test cases
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extract.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <atomic>
#include <mutex>

namespace omop::extract::test {

using namespace ::testing;
using namespace omop::core;
using namespace omop::common;

// Mock database connection for testing extract_table
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD((std::unique_ptr<IPreparedStatement>), prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, ((const std::string& table_name), (const std::string& schema)), (const, override));
};

// Mock result set for database tests
class MockResultSet : public IResultSet {
public:
    MOCK_METHOD(bool, next, (), (override));
    MOCK_METHOD(std::any, get_value, (size_t index), (const, override));
    MOCK_METHOD(std::any, get_value, (const std::string& column_name), (const, override));
    MOCK_METHOD(bool, is_null, (size_t index), (const, override));
    MOCK_METHOD(bool, is_null, (const std::string& column_name), (const, override));
    MOCK_METHOD(size_t, column_count, (), (const, override));
    MOCK_METHOD(std::string, column_name, (size_t index), (const, override));
    MOCK_METHOD(std::string, column_type, (size_t index), (const, override));
    MOCK_METHOD(std::vector<std::string>, get_column_names, (), (const, override));
    MOCK_METHOD(Record, to_record, (), (const, override));
};

// Mock extractor with statistics
class MockExtractorWithStats : public IExtractor {
public:
    MOCK_METHOD(void, initialize,
                ((const std::unordered_map<std::string, std::any>&), (ProcessingContext&)),
                (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));
};

// Test fixture for extended tests
class ExtractUtilsExtendedTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_extract_extended_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests BatchExtractor statistics collection
TEST_F(ExtractUtilsExtendedTest, BatchExtractorStatistics) {
    auto mock_extractor = std::make_unique<MockExtractorWithStats>();
    auto mock_ptr = mock_extractor.get();

    // Set up mock statistics
    std::unordered_map<std::string, std::any> mock_stats;
    mock_stats["total_records"] = size_t(100);
    mock_stats["extraction_time_seconds"] = 5.5;
    mock_stats["errors"] = size_t(2);

    EXPECT_CALL(*mock_ptr, get_statistics())
        .WillRepeatedly(Return(mock_stats));

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 10; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch));

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)));
    batch_extractor.extract_all();

    // Get and verify statistics
    auto stats = batch_extractor.get_statistics();

    // Should include original extractor stats
    EXPECT_TRUE(stats.contains("total_records"));
    EXPECT_EQ(size_t(100), std::any_cast<size_t>(stats["total_records"]));

    // Should include batch extractor specific stats
    EXPECT_TRUE(stats.contains("batch_size"));
    EXPECT_TRUE(stats.contains("max_records"));
    EXPECT_TRUE(stats.contains("continue_on_error"));
}

// Tests ParallelExtractor statistics aggregation with real extractors
TEST_F(ExtractUtilsExtendedTest, ParallelExtractorStatistics) {
    auto parallel_extractor = std::make_unique<ParallelExtractor>();

    // Add multiple CSV extractors with different data sizes
    for (int i = 0; i < 3; ++i) {
        // Create CSV files with different record counts
        std::string csv_content = "id,value\n";
        for (int j = 0; j <= i; ++j) {
            csv_content += std::format("{},{}\n", j, j * 10);
        }
        
        std::string filepath = createTestFile(std::format("stats_test_{}.csv", i), csv_content);
        
        auto csv_extractor = std::make_unique<CsvExtractor>();
        std::unordered_map<std::string, std::any> config;
        config["filepath"] = filepath;
        
        ProcessingContext init_context;
        csv_extractor->initialize(config, init_context);

        parallel_extractor->add_extractor(std::move(csv_extractor),
                                        std::format("extractor_{}", i));
    }

    // Extract all data
    auto all_records = parallel_extractor->extract_all();

    // Get aggregated statistics
    auto all_stats = parallel_extractor->get_all_statistics();

    EXPECT_EQ(3, all_stats.size());

    // Verify each extractor's statistics
    for (int i = 0; i < 3; ++i) {
        std::string name = std::format("extractor_{}", i);
        EXPECT_TRUE(all_stats.contains(name));

        auto& stats = all_stats[name];
        EXPECT_TRUE(stats.contains("records_extracted"));
        // Each extractor i has (i+1) data records
        EXPECT_EQ(size_t(i + 1),
                  std::any_cast<size_t>(stats["records_extracted"]));
    }
}

// Tests utils::extract_table function
TEST_F(ExtractUtilsExtendedTest, ExtractTable) {
    auto mock_connection = std::make_unique<MockDatabaseConnection>();
    auto conn_ptr = mock_connection.get();

    // Set up connection expectations
    EXPECT_CALL(*conn_ptr, is_connected())
        .WillRepeatedly(Return(true));

    EXPECT_CALL(*conn_ptr, table_exists("person", ""))
        .WillOnce(Return(true));

    EXPECT_CALL(*conn_ptr, get_database_type())
        .WillRepeatedly(Return("postgresql"));

    // Create mock result set
    auto mock_result = std::make_unique<MockResultSet>();
    auto result_ptr = mock_result.get();

    // Set up result set to return 3 records
    EXPECT_CALL(*result_ptr, next())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    Record r1, r2, r3;
    r1.setField("person_id", 1);
    r1.setField("name", std::string("John"));
    r2.setField("person_id", 2);
    r2.setField("name", std::string("Jane"));
    r3.setField("person_id", 3);
    r3.setField("name", std::string("Bob"));

    EXPECT_CALL(*result_ptr, to_record())
        .WillOnce(Return(r1))
        .WillOnce(Return(r2))
        .WillOnce(Return(r3));

    EXPECT_CALL(*conn_ptr, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    // Test extract_table
    auto records = utils::extract_table(std::move(mock_connection),
                                       "person",
                                       "person_id > 0");

    EXPECT_EQ(3, records.size());
    EXPECT_EQ(1, records[0].getFieldAs<int>("person_id"));
    EXPECT_EQ("Jane", records[1].getFieldAs<std::string>("name"));
}

// Tests case-insensitive file extension detection
TEST_F(ExtractUtilsExtendedTest, DetectSourceTypeCaseInsensitive) {
    // Test uppercase extensions
    EXPECT_EQ("csv", detect_source_type("DATA.CSV"));
    EXPECT_EQ("json", detect_source_type("DATA.JSON"));
    EXPECT_EQ("jsonl", detect_source_type("DATA.JSONL"));

    // Test mixed case
    EXPECT_EQ("compressed_csv", detect_source_type("data.CsV.Gz"));
    EXPECT_EQ("compressed_csv", detect_source_type("DATA.csv.ZIP"));

    // Test complex patterns
    EXPECT_EQ("compressed_csv", detect_source_type("my.data.csv.GZ"));
    EXPECT_EQ("compressed_csv", detect_source_type("report_2024.CSV.BZ2"));
}

// Tests directory detection with no CSV files
TEST_F(ExtractUtilsExtendedTest, DetectSourceTypeEmptyDirectory) {
    // Create directory with no CSV files
    std::filesystem::create_directories(test_dir_ / "empty_dir");
    createTestFile("empty_dir/data.txt", "text file");
    createTestFile("empty_dir/data.json", "{}");

    // Should return empty string for directory without CSV files
    EXPECT_EQ("", detect_source_type((test_dir_ / "empty_dir").string()));
}

// Tests extended configuration validation
TEST_F(ExtractUtilsExtendedTest, ValidateExtractorConfigExtended) {
    // Test JSON configuration validation
    std::unordered_map<std::string, std::any> json_config;
    json_config["filepath"] = std::string(createTestFile("test.json", "[]"));

    auto [valid_json, error_json] = validate_extractor_config("json", json_config);
    EXPECT_TRUE(valid_json);
    EXPECT_TRUE(error_json.empty());

    // Test CSV directory configuration
    std::filesystem::create_directories(test_dir_ / "csv_test");
    createTestFile("csv_test/file.csv", "id\n1\n");

    std::unordered_map<std::string, std::any> dir_config;
    dir_config["directory"] = std::string((test_dir_ / "csv_test").string());

    auto [valid_dir, error_dir] = validate_extractor_config("csv_directory", dir_config);
    EXPECT_TRUE(valid_dir);

    // Test non-existent directory
    dir_config["directory"] = std::string("/nonexistent/directory");
    auto [invalid_dir, error_msg] = validate_extractor_config("csv_directory", dir_config);
    EXPECT_FALSE(invalid_dir);
    EXPECT_NE(error_msg.find("not found"), std::string::npos);
}

// Tests URL parsing edge cases
TEST_F(ExtractUtilsExtendedTest, CreateConnectionFromUrlEdgeCases) {
    // Test URL without port (should use default)
    EXPECT_NO_THROW({
        try {
            utils::create_connection_from_url("postgresql://user:pass@localhost/db");
        } catch (const std::exception& e) {
            // Expected in test environment without actual database
            std::string error = e.what();
            // Should parse URL successfully even if connection fails
            EXPECT_TRUE(error.find("Invalid database URL") == std::string::npos);
        }
    });

    // Test URL without credentials
    EXPECT_NO_THROW({
        try {
            utils::create_connection_from_url("mysql://localhost:3306/testdb");
        } catch (const std::exception& e) {
            std::string error = e.what();
            EXPECT_TRUE(error.find("Invalid database URL") == std::string::npos);
        }
    });

    // Test various database types
    std::vector<std::string> db_types = {"postgresql", "postgres", "mysql", "mariadb"};
    for (const auto& db_type : db_types) {
        std::string url = db_type + "://localhost/test";
        EXPECT_NO_THROW({
            try {
                utils::create_connection_from_url(url);
            } catch (const ConfigurationException&) {
                // Re-throw configuration exceptions (invalid URL format)
                throw;
            } catch (...) {
                // Other exceptions (connection failures) are expected
            }
        });
    }
}

// Tests BatchExtractor with empty batches
TEST_F(ExtractUtilsExtendedTest, BatchExtractorEmptyBatches) {
    auto mock_extractor = std::make_unique<MockExtractorWithStats>();
    auto mock_ptr = mock_extractor.get();

    // Set up to return normal batch first, then empty batch to stop
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))  // First call - will get normal batch
        .WillOnce(Return(true)); // Second call - will get empty batch and stop

    RecordBatch empty_batch;
    RecordBatch normal_batch;
    normal_batch.addRecord(Record());

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(normal_batch))  // Return normal batch first
        .WillOnce(Return(empty_batch));  // Then empty batch to stop

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)));
    auto records = batch_extractor.extract_all();

    // Should handle empty batches gracefully
    EXPECT_EQ(1, records.size());
}

// Tests ParallelExtractor error handling with real extractors
TEST_F(ExtractUtilsExtendedTest, ParallelExtractorWithFailures) {
    auto parallel_extractor = std::make_unique<ParallelExtractor>();

    // Add extractor that will fail (non-existent file)
    auto failing_extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> failing_config;
    failing_config["filepath"] = "/non/existent/file.csv";
    
    // This should not throw during add_extractor, but during extraction
    parallel_extractor->add_extractor(std::move(failing_extractor), "failing");

    // Add normal extractor
    std::string csv_content = "id,value\n1,100\n";
    std::string filepath = createTestFile("normal_test.csv", csv_content);
    
    auto normal_extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> normal_config;
    normal_config["filepath"] = filepath;
    
    ProcessingContext init_context;
    normal_extractor->initialize(normal_config, init_context);
    
    parallel_extractor->add_extractor(std::move(normal_extractor), "normal");

    // Should continue despite one extractor failing
    auto records = parallel_extractor->extract_all();
    EXPECT_EQ(1, records.size());
}

// Tests progress callback with zero total
TEST_F(ExtractUtilsExtendedTest, ProgressCallbackZeroTotal) {
    auto mock_extractor = std::make_unique<MockExtractorWithStats>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 5; ++i) {
        batch.addRecord(Record());
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch));

    std::vector<std::pair<size_t, size_t>> progress_updates;

    BatchExtractor::Config config;
    config.batch_size = 10;
    config.max_records = 0; // No limit
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.push_back({current, total});
    };

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);
    batch_extractor.extract_all();

    // When max_records is 0, total should be 0 in progress updates
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(0, progress_updates[0].second);
}

// Tests detect_source_type with symbolic links
TEST_F(ExtractUtilsExtendedTest, DetectSourceTypeSymbolicLinks) {
    // Create a CSV file and a symbolic link to it
    std::string csv_file = createTestFile("original.csv", "id\n1\n");
    std::filesystem::path symlink = test_dir_ / "link_to_csv.csv";

    // Create symbolic link (if supported by the filesystem)
    try {
        std::filesystem::create_symlink(csv_file, symlink);
        EXPECT_EQ("csv", detect_source_type(symlink.string()));
    } catch (const std::filesystem::filesystem_error&) {
        // Skip test if symbolic links are not supported
        GTEST_SKIP() << "Symbolic links not supported on this filesystem";
    }
}

// Tests configuration validation for unknown extractor type
TEST_F(ExtractUtilsExtendedTest, ValidateUnknownExtractorType) {
    std::unordered_map<std::string, std::any> config;
    config["some_param"] = "value";

    auto [valid, error] = validate_extractor_config("unknown_type", config);
    EXPECT_FALSE(valid);
    EXPECT_NE(error.find("Unknown extractor type"), std::string::npos);
}

// Thread-safe mock extractor for parallel testing
class ThreadSafeMockExtractor : public core::IExtractor {
private:
    mutable std::mutex mutex_;
    std::atomic<bool> has_data_{true};
    std::atomic<size_t> extracted_count_{0};
    size_t total_records_;
    std::string type_;

public:
    explicit ThreadSafeMockExtractor(const std::string& type, size_t records = 10)
        : type_(type), total_records_(records) {}

    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override {
        // Thread-safe initialization
    }

    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override {
        std::lock_guard<std::mutex> lock(mutex_);
        core::RecordBatch batch;
        
        size_t current = extracted_count_.load();
        size_t to_extract = std::min(batch_size, total_records_ - current);
        
        for (size_t i = 0; i < to_extract; ++i) {
            core::Record r;
            r.setField("id", static_cast<int>(current + i));
            r.setField("value", static_cast<int>((current + i) * 100));
            batch.addRecord(r);
        }
        
        extracted_count_ += to_extract;
        if (extracted_count_ >= total_records_) {
            has_data_ = false;
        }
        
        return batch;
    }

    bool has_more_data() const override { return has_data_.load(); }
    std::string get_type() const override { return type_; }
    void finalize(core::ProcessingContext& context) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"extracted", extracted_count_.load()}};
    }
};

// Replace the disabled ParallelExtractor test with thread-safe implementation
TEST_F(ExtractUtilsExtendedTest, ParallelExtractorThreadSafe) {
    ParallelExtractor::Config config;
    config.num_threads = 4;
    config.preserve_order = false;

    ParallelExtractor parallel_extractor(config);

    // Add thread-safe extractors
    for (int i = 0; i < 6; ++i) {
        parallel_extractor.add_extractor(
            std::make_unique<ThreadSafeMockExtractor>(
                std::format("extractor_{}", i), 100),
            std::format("extractor_{}", i));
    }

    auto records = parallel_extractor.extract_all();
    EXPECT_EQ(600, records.size());

    auto all_stats = parallel_extractor.get_all_statistics();
    for (const auto& [name, stats] : all_stats) {
        EXPECT_EQ(100, std::any_cast<size_t>(stats.at("extracted")));
    }
}

} // namespace omop::extract::test
