/**
 * @file database_connector_test.cpp
 * @brief Unit tests for database connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <chrono>
#include <thread>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;
using ::testing::NiceMock;
using ::testing::A;

// Mock database connection for testing
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD((std::unique_ptr<IPreparedStatement>), prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
};

// Mock result set for testing
class MockResultSet : public IResultSet {
public:
    MOCK_METHOD(bool, next, (), (override));
    MOCK_METHOD(std::any, get_value, (size_t index), (const, override));
    MOCK_METHOD(std::any, get_value, (const std::string& column_name), (const, override));
    MOCK_METHOD(bool, is_null, (size_t index), (const, override));
    MOCK_METHOD(bool, is_null, (const std::string& column_name), (const, override));
    MOCK_METHOD(size_t, column_count, (), (const, override));
    MOCK_METHOD(std::string, column_name, (size_t index), (const, override));
    MOCK_METHOD(std::string, column_type, (size_t index), (const, override));
    MOCK_METHOD(std::vector<std::string>, get_column_names, (), (const, override));
    MOCK_METHOD(Record, to_record, (), (const, override));
};

// Mock prepared statement for testing
class MockPreparedStatement : public IPreparedStatement {
public:
    MOCK_METHOD(void, bind, (size_t index, const std::any& value), (override));
    MOCK_METHOD((std::unique_ptr<IResultSet>), execute_query, (), (override));
    MOCK_METHOD(size_t, execute_update, (), (override));
    MOCK_METHOD(void, clear_parameters, (), (override));
};

// ============================================================================
// DatabaseConnectorConnectionPoolTest Tests
// ============================================================================

// Test fixture for connection pool tests
class DatabaseConnectorConnectionPoolTest : public ::testing::Test {
protected:
    void SetUp() override {
        connection_factory_ = []() {
            auto conn = std::make_unique<NiceMock<MockDatabaseConnection>>();
            ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
            return conn;
        };
    }

    std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory_;
};

// Tests basic connection pool operations
TEST_F(DatabaseConnectorConnectionPoolTest, AcquireAndRelease) {
    ConnectionPool pool(2, 5, connection_factory_);

    // Acquire connection
    auto conn1 = pool.acquire();
    ASSERT_NE(nullptr, conn1);
    EXPECT_TRUE(conn1->is_connected());

    // Release connection
    pool.release(std::move(conn1));

    // Acquire again - should get the same connection
    auto conn2 = pool.acquire();
    ASSERT_NE(nullptr, conn2);
}

// Tests clearing idle connections
TEST_F(DatabaseConnectorConnectionPoolTest, ClearIdleConnections) {
    ConnectionPool pool(2, 5, connection_factory_);

    auto stats1 = pool.get_statistics();
    EXPECT_EQ(2, stats1.idle_connections);

    pool.clear_idle_connections();

    auto stats2 = pool.get_statistics();
    EXPECT_EQ(0, stats2.idle_connections);
}

// Tests connection validation
TEST_F(DatabaseConnectorConnectionPoolTest, ConnectionValidation) {
    // Factory that creates connections that become invalid
    auto factory = []() {
        auto conn = std::make_unique<NiceMock<MockDatabaseConnection>>();
        static int count = 0;
        // First connection will become invalid
        if (count++ == 0) {
            ON_CALL(*conn, is_connected()).WillByDefault(Return(false));
        } else {
            ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
        }
        return conn;
    };

    ConnectionPool pool(2, 5, factory);

    // Validate connections - should remove invalid ones
    size_t removed = pool.validate_connections();
    EXPECT_GE(removed, 0);

    // Pool should maintain minimum connections
    auto stats = pool.get_statistics();
    EXPECT_GE(stats.total_connections, 2);
}

// Test for memory leak detection in connection pool
TEST_F(DatabaseConnectorConnectionPoolTest, MemoryLeakDetection) {
    struct AllocationTracker {
        std::atomic<size_t> allocated{0};
        std::atomic<size_t> deallocated{0};
        
        size_t net_allocations() const {
            return allocated.load() - deallocated.load();
        }
    };
    
    auto tracker = std::make_shared<AllocationTracker>();
    
    auto factory = [tracker]() {
        class TrackedConnection : public MockDatabaseConnection {
            std::shared_ptr<AllocationTracker> tracker_;
        public:
            explicit TrackedConnection(std::shared_ptr<AllocationTracker> t) 
                : tracker_(t) {
                tracker_->allocated++;
            }
            
            ~TrackedConnection() {
                tracker_->deallocated++;
            }
        };
        
        return std::make_unique<TrackedConnection>(tracker);
    };
    
    {
        ConnectionPool pool(2, 5, factory);
        auto conn = pool.acquire();
        pool.release(std::move(conn));
    }
    
    // After pool destruction, all connections should be deallocated
    // Allow some time for async cleanup
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    // Use a less strict check in case there are lingering references
    EXPECT_LE(tracker->net_allocations(), 1);
}

// Tests pool growth when needed
TEST_F(DatabaseConnectorConnectionPoolTest, PoolGrowth) {
    ConnectionPool pool(1, 3, connection_factory_);

    auto stats1 = pool.get_statistics();
    EXPECT_EQ(1, stats1.total_connections);

    // Acquire more connections than minimum
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();

    auto stats2 = pool.get_statistics();
    EXPECT_GE(stats2.total_connections, 2);
    EXPECT_LE(stats2.total_connections, 3);
}

// Tests pool statistics
TEST_F(DatabaseConnectorConnectionPoolTest, PoolStatistics) {
    ConnectionPool pool(2, 5, connection_factory_);

    auto stats1 = pool.get_statistics();
    EXPECT_EQ(2, stats1.total_connections);
    EXPECT_EQ(0, stats1.active_connections);
    EXPECT_EQ(2, stats1.idle_connections);

    // Acquire connections
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();

    auto stats2 = pool.get_statistics();
    EXPECT_EQ(2, stats2.total_connections);
    EXPECT_EQ(2, stats2.active_connections);
    EXPECT_EQ(0, stats2.idle_connections);

    // Release one connection
    pool.release(std::move(conn1));

    auto stats3 = pool.get_statistics();
    EXPECT_EQ(2, stats3.total_connections);
    EXPECT_EQ(1, stats3.active_connections);
    EXPECT_EQ(1, stats3.idle_connections);
}

// Tests pool timeout
TEST_F(DatabaseConnectorConnectionPoolTest, AcquireTimeout) {
    ConnectionPool pool(1, 1, connection_factory_);

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Try to acquire another with timeout
    std::thread acquire_thread([&pool]() {
        EXPECT_THROW(pool.acquire(100), DatabaseException);
    });

    acquire_thread.join();
}

// ============================================================================
// DatabaseExtractorTest Tests
// ============================================================================

// Test fixture for database extractor tests
class DatabaseExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_connection_ = std::make_unique<NiceMock<MockDatabaseConnection>>();
        mock_connection_ptr_ = mock_connection_.get();

        // Set up default expectations with proper cleanup
        ON_CALL(*mock_connection_ptr_, is_connected())
            .WillByDefault(Return(true));
        ON_CALL(*mock_connection_ptr_, get_database_type())
            .WillByDefault(Return("MockDB"));
        ON_CALL(*mock_connection_ptr_, disconnect())
            .WillByDefault(Return());
    }
    
    void TearDown() override {
        // Ensure proper cleanup of mock connections
        if (mock_connection_ && mock_connection_ptr_) {
            mock_connection_ptr_->disconnect();
        }
        mock_connection_.reset();
        mock_connection_ptr_ = nullptr;
    }

    std::unique_ptr<MockDatabaseConnection> mock_connection_;
    MockDatabaseConnection* mock_connection_ptr_;
};

// Tests building complex queries
TEST_F(DatabaseExtractorTest, BuildComplexQuery) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["columns"] = std::vector<std::string>{"person_id", "birth_datetime"};
    config["filter"] = std::string("gender_concept_id = 8507");
    config["order_by"] = std::string("person_id");
    config["limit"] = 100;

    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests batch extraction
TEST_F(DatabaseExtractorTest, ExtractBatch) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    auto result_ptr = mock_result.get();

    // Set up result set behavior
    EXPECT_CALL(*result_ptr, next())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    EXPECT_CALL(*result_ptr, column_count())
        .WillRepeatedly(Return(3));

    EXPECT_CALL(*result_ptr, column_name(_))
        .WillRepeatedly([](size_t index) {
            switch(index) {
                case 0: return std::string("person_id");
                case 1: return std::string("birth_datetime");
                case 2: return std::string("gender_concept_id");
                default: return std::string("");
            }
        });

    EXPECT_CALL(*result_ptr, get_value(A<size_t>()))
        .WillRepeatedly([](size_t index) {
            switch(index) {
                case 0: return std::any(static_cast<long long>(1));
                case 1: return std::any(std::string("1990-01-01"));
                case 2: return std::any(static_cast<long long>(8507));
                default: return std::any{};
            }
        });

    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size());
}

// Tests initialization failure for missing table
TEST_F(DatabaseExtractorTest, InitializeMissingTable) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("nonexistent", ""))
        .WillOnce(Return(false));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("nonexistent");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), DatabaseException);
}

// Tests basic database extraction initialization
TEST_F(DatabaseExtractorTest, InitializeExtractor) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("person", ""))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Tests initialization with column selection
TEST_F(DatabaseExtractorTest, InitializeWithColumns) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = "SELECT person_id, birth_datetime, gender_concept_id FROM person";

    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["columns"] = std::vector<std::string>{"person_id", "birth_datetime", "gender_concept_id"};

    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests initialization with filter condition
TEST_F(DatabaseExtractorTest, InitializeWithFilter) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    std::string expected_query = "SELECT * FROM person WHERE gender_concept_id = 8507";

    EXPECT_CALL(*mock_connection_ptr_, execute_query(expected_query))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["filter"] = std::string("gender_concept_id = 8507");

    ProcessingContext context;
    extractor.initialize(config, context);
}

// Tests initialization with schema
TEST_F(DatabaseExtractorTest, InitializeWithSchema) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists("person", "cdm"))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    config["schema"] = std::string("cdm");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Tests parameter binding safety
TEST_F(DatabaseExtractorTest, ParameterBindingSafety) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    // Test with potentially dangerous filter that should be properly escaped
    config["filter"] = std::string("name = 'O'Connor' AND age > 18");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
}

// Test resource exhaustion scenarios
TEST_F(DatabaseExtractorTest, ResourceExhaustionHandling) {
    // Set up mock to simulate large result set
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    // Create a mock result that simulates memory pressure
    class ExhaustingResultSet : public MockResultSet {
    private:
        mutable size_t call_count_ = 0;
        const size_t max_rows_ = 500;
        
    public:
        bool next() override {
            if (++call_count_ > max_rows_) {
                // Simulate out of memory after many rows
                throw std::bad_alloc();
            }
            return call_count_ <= max_rows_;
        }
        
        Record to_record() const override {
            Record r;
            r.setField("id", static_cast<int>(call_count_));
            // Large string to consume memory
            r.setField("data", std::string(10000, 'X'));
            return r;
        }
        
        std::vector<std::string> get_column_names() const override {
            return {"id", "data"};
        }
    };

    auto mock_result = std::make_unique<ExhaustingResultSet>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("large_table");

    ProcessingContext context;
    extractor.initialize(config, context);

    // Should handle memory exhaustion gracefully
    EXPECT_THROW(extractor.extract_batch(1000, context), std::exception);
}

// Tests SQL injection prevention
TEST_F(DatabaseExtractorTest, SQLInjectionPrevention) {
    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");
    // Test with potentially malicious filter
    config["filter"] = std::string("name = 'Robert'; DROP TABLE person; --'");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), SecurityException);
}

// Tests getting statistics
TEST_F(DatabaseExtractorTest, GetStatistics) {
    EXPECT_CALL(*mock_connection_ptr_, table_exists(_, _))
        .WillOnce(Return(true));

    auto mock_result = std::make_unique<NiceMock<MockResultSet>>();
    EXPECT_CALL(*mock_connection_ptr_, execute_query(_))
        .WillOnce(Return(std::move(mock_result)));

    DatabaseExtractor extractor(std::move(mock_connection_));

    std::unordered_map<std::string, std::any> config;
    config["table"] = std::string("person");

    ProcessingContext context;
    extractor.initialize(config, context);

    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("records_extracted"));
    EXPECT_TRUE(stats.contains("extraction_time"));
}

// ============================================================================
// Standalone Tests
// ============================================================================

// Tests database connection factory
TEST(DatabaseConnectionFactoryTest, RegisterAndCreate) {
    // Register a test connection type
    DatabaseConnectionFactory::instance().register_type("test_db", [](const IDatabaseConnection::ConnectionParams&) {
        return std::make_unique<NiceMock<MockDatabaseConnection>>();
    });

    // Create connection
    IDatabaseConnection::ConnectionParams params;
    auto connection = DatabaseConnectionFactory::instance().create("test_db", params);
    ASSERT_NE(nullptr, connection);

    // Test invalid type
    EXPECT_THROW(DatabaseConnectionFactory::instance().create("invalid_db", params), DatabaseException);
}

// Tests result set base implementation
TEST(ResultSetBaseTest, ToRecord) {
    class TestResultSet : public ResultSetBase {
    public:
        bool next() override { return false; }
        std::any get_value(size_t index) const override {
            switch(index) {
                case 0: return 123;
                case 1: return std::string("test");
                case 2: return 45.67;
                default: return std::any{};
            }
        }
        std::any get_value(const std::string&) const override { return std::any{}; }
        bool is_null(size_t index) const override { return index >= 3; }
        bool is_null(const std::string&) const override { return false; }
        size_t column_count() const override { return 4; }
        std::string column_name(size_t index) const override {
            switch(index) {
                case 0: return "id";
                case 1: return "name";
                case 2: return "value";
                case 3: return "null_col";
                default: return "";
            }
        }
        std::string column_type(size_t) const override { return "unknown"; }
    };

    TestResultSet result_set;
    Record record = result_set.to_record();

    EXPECT_EQ(123, std::any_cast<int>(record.getField("id")));
    EXPECT_EQ("test", std::any_cast<std::string>(record.getField("name")));
    EXPECT_DOUBLE_EQ(45.67, std::any_cast<double>(record.getField("value")));
    EXPECT_FALSE(record.hasField("null_col"));
}