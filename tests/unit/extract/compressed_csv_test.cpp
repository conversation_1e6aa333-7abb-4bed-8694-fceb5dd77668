/**
 * @file compressed_csv_extractor_test.cpp
 * @brief Unit tests for CompressedCsvExtractor class
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "extract/csv_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <zlib.h>

namespace omop::extract::test {

using namespace ::testing;

// Helper class to create test files
class CompressedCsvExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        test_dir_ = std::filesystem::temp_directory_path() / "compressed_csv_test";
        std::filesystem::create_directories(test_dir_);

        // Create sample CSV content
        csv_content_ = "id,name,value\n1,test1,100\n2,test2,200\n3,test3,300\n";
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Create a gzip compressed file
    std::string CreateGzipFile(const std::string& content, const std::string& filename) {
        std::string filepath = (test_dir_ / filename).string();
        gzFile gz = gzopen(filepath.c_str(), "wb");
        EXPECT_NE(gz, nullptr);

        int written = gzwrite(gz, content.c_str(), content.length());
        EXPECT_EQ(written, content.length());

        gzclose(gz);
        return filepath;
    }

    // Create an uncompressed CSV file
    std::string CreateCsvFile(const std::string& content, const std::string& filename) {
        std::string filepath = (test_dir_ / filename).string();
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath;
    }

    std::filesystem::path test_dir_;
    std::string csv_content_;
};

// ============================================================================
// CompressedCsvExtractorTest Tests
// ============================================================================

// Tests batch extraction with limit
TEST_F(CompressedCsvExtractorTest, BatchExtractionWithLimit) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["max_records"] = size_t(2);

    extractor.initialize(config, context);

    // Extract with limit
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 2); // Should respect max_records limit

    // Verify extracted data
    auto records = batch.getRecords();
    EXPECT_EQ(records.size(), 2);

    // Check first record
    EXPECT_EQ(std::any_cast<long long>(records[0].getField("id")), 1);
    EXPECT_EQ(std::any_cast<std::string>(records[0].getField("name")), "test1");

    // Check second record
    EXPECT_EQ(std::any_cast<long long>(records[1].getField("id")), 2);
    EXPECT_EQ(std::any_cast<std::string>(records[1].getField("name")), "test2");
}

// Tests cleanup temporary files
TEST_F(CompressedCsvExtractorTest, CleanupTemporaryFiles) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["cleanup_temp_file"] = true;

    extractor.initialize(config, context);

    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Get temp file path before finalization
    auto stats = extractor.get_statistics();

    // Finalize should clean up temp file
    extractor.finalize(context);

    // Verify temp file is cleaned up
    // Note: We can't directly check the temp file as it's private,
    // but we can verify the extractor completes without errors
    EXPECT_NO_THROW(extractor.get_statistics());
}

// Tests corrupted compressed file
TEST_F(CompressedCsvExtractorTest, CorruptedCompressedFile) {
    // Create a corrupted gzip file with invalid magic numbers
    std::string filepath = (test_dir_ / "corrupted.csv.gz").string();
    std::ofstream file(filepath, std::ios::binary);
    // Write some bytes that don't match gzip magic numbers (0x1f, 0x8b)
    file.write("\x00\x00", 2); // Definitely not gzip magic
    file << "invalid gzip content";
    file.close();

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["has_header"] = true;

    // Should throw an exception for corrupted file
    EXPECT_THROW(extractor.initialize(config, context), omop::common::ExtractionException);
}

// Tests detect compression by extension
TEST_F(CompressedCsvExtractorTest, DetectCompressionByExtension) {
    struct TestCase {
        std::string filename;
        CompressedCsvExtractor::CompressionFormat expected;
    };

    std::vector<TestCase> test_cases = {
        {"test.csv.gz", CompressedCsvExtractor::CompressionFormat::Gzip},
        {"test.csv.zip", CompressedCsvExtractor::CompressionFormat::Zip},
        {"test.csv.bz2", CompressedCsvExtractor::CompressionFormat::Bzip2},
        {"test.csv.xz", CompressedCsvExtractor::CompressionFormat::Xz},
        {"test.csv", CompressedCsvExtractor::CompressionFormat::None}
    };

    for (const auto& tc : test_cases) {
        std::string filepath = (test_dir_ / tc.filename).string();
        std::ofstream(filepath) << "dummy";

        // Note: detect_compression is protected, so we can't test it directly
        // CompressedCsvExtractor extractor;
        // auto format = extractor.detect_compression(filepath);
        // EXPECT_EQ(format, tc.expected) << "Failed for file: " << tc.filename;
    }
}

// Tests disable cleanup
TEST_F(CompressedCsvExtractorTest, DisableCleanup) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["cleanup_temp_file"] = false; // Disable cleanup

    extractor.initialize(config, context);

    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Finalize without cleanup
    extractor.finalize(context);

    // Should still work normally
    EXPECT_NO_THROW(extractor.get_statistics());
}

// Tests extract from compressed file
TEST_F(CompressedCsvExtractorTest, ExtractFromCompressedFile) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;

    extractor.initialize(config, context);

    // Extract records
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Verify extracted data
    auto records = batch.getRecords();
    EXPECT_EQ(records.size(), 3);

    // Check first record
    const auto& field_id = records[0].getField("id");
    EXPECT_EQ(std::any_cast<long long>(field_id), 1);

    const auto& field_name = records[0].getField("name");
    EXPECT_EQ(std::any_cast<std::string>(field_name), "test1");
}

// Tests explicit compression format
TEST_F(CompressedCsvExtractorTest, ExplicitCompressionFormat) {
    // Create file with misleading extension
    std::string gz_file = CreateGzipFile(csv_content_, "test.dat");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["compression_format"] = std::string("gzip");

    EXPECT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Tests get type
TEST_F(CompressedCsvExtractorTest, GetType) {
    CompressedCsvExtractor extractor;
    EXPECT_EQ(extractor.get_type(), "compressed_csv");
}

// Tests handle non compressed file
TEST_F(CompressedCsvExtractorTest, HandleNonCompressedFile) {
    std::string csv_file = CreateCsvFile(csv_content_, "test.csv");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file;
    config["has_header"] = true;

    // Should work like regular CSV extractor
    EXPECT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Tests initialize with gzip file
TEST_F(CompressedCsvExtractorTest, InitializeWithGzipFile) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;

    EXPECT_NO_THROW(extractor.initialize(config, context));
    EXPECT_TRUE(extractor.has_more_data());
}

// Tests large compressed file
TEST_F(CompressedCsvExtractorTest, LargeCompressedFile) {
    // Create large CSV content
    std::string large_csv = "id,name,value\n";
    for (int i = 1; i <= 1000; ++i) {
        large_csv += std::to_string(i) + ",test" + std::to_string(i) + "," + std::to_string(i * 100) + "\n";
    }

    std::string gz_file = CreateGzipFile(large_csv, "large.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["batch_size"] = size_t(100);

    extractor.initialize(config, context);

    // Extract in batches
    int total_records = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(100, context);
        total_records += batch.size();
    }

    EXPECT_EQ(total_records, 1000);
}

// Tests statistics
TEST_F(CompressedCsvExtractorTest, Statistics) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");

    CompressedCsvExtractor extractor;
    core::ProcessingContext context;

    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;

    extractor.initialize(config, context);

    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Get statistics
    auto stats = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_records"]), 3);
    EXPECT_EQ(std::any_cast<size_t>(stats["successful_records"]), 3);
    EXPECT_EQ(std::any_cast<size_t>(stats["failed_records"]), 0);

    // Should contain compression info
    EXPECT_TRUE(stats.find("compression_format") != stats.end());
    EXPECT_TRUE(stats.find("original_size") != stats.end());
    EXPECT_TRUE(stats.find("compressed_size") != stats.end());
}

// Tests bzip2 decompression functionality
TEST_F(CompressedCsvExtractorTest, Bzip2Decompression) {
    // Create a test bzip2 file
    std::string test_content = "id,name,value\n1,John,100\n2,Jane,200\n";
    std::string bz2_filepath = (test_dir_ / "test.csv.bz2").string();
    
    // Create bzip2 compressed file using external tool (cross-platform approach)
    std::string temp_csv = (test_dir_ / "temp.csv").string();
    std::ofstream temp_file(temp_csv);
    temp_file << test_content;
    temp_file.close();
    
    // Use system command to create bzip2 file (simplified test approach)
    // In real implementation, we'd use libbz2 API
    GTEST_SKIP() << "Bzip2 test requires system bzip2 command";
}

// Tests xz decompression functionality  
TEST_F(CompressedCsvExtractorTest, XzDecompression) {
    // Create a test xz file
    std::string test_content = "id,name,value\n1,John,100\n2,Jane,200\n";
    std::string xz_filepath = (test_dir_ / "test.csv.xz").string();
    
    // Use system command to create xz file (simplified test approach)
    // In real implementation, we'd use liblzma API
    GTEST_SKIP() << "XZ test requires system xz command";
}

// Tests zip decompression functionality
TEST_F(CompressedCsvExtractorTest, ZipDecompression) {
    // This test would require libarchive to create test zip files
    GTEST_SKIP() << "ZIP test requires libarchive to create test data";
}

// Tests gzip decompression with UK localized data
TEST_F(CompressedCsvExtractorTest, GzipWithUKLocalizedData) {
    // UK localized CSV content
    std::string uk_csv_content = "id,date,amount,temperature,postcode\n"
                                "1,25/12/2024,£123.45,22.5°C,SW1A 1AA\n"
                                "2,01/01/2025,£1,234.56,15.2°C,M1 1AA\n"
                                "3,31/03/2025,£67.89,18.7°C,B1 1TT\n";
    
    std::string gz_file = CreateGzipFile(uk_csv_content, "uk_data.csv.gz");
    
    CompressedCsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["compression_format"] = std::string("gzip");
    
    core::ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    // Extract and verify UK formatted data is handled correctly
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Verify UK date format is parsed (DD/MM/YYYY)
    const auto& first_record = batch.getRecord(0);
    EXPECT_TRUE(first_record.hasField("date"));
    
    // Verify UK currency format is preserved
    EXPECT_TRUE(first_record.hasField("amount"));
    
    // Verify UK postal code format
    EXPECT_TRUE(first_record.hasField("postcode"));
}

// Tests error handling for unsupported compression formats
TEST_F(CompressedCsvExtractorTest, UnsupportedCompressionFormat) {
    CompressedCsvExtractor extractor;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string("test.csv.rar"); // Unsupported format
    
    core::ProcessingContext context;
    
    // Should handle unsupported format gracefully
    EXPECT_THROW(extractor.initialize(config, context), common::ExtractionException);
}

// Tests automatic compression format detection
TEST_F(CompressedCsvExtractorTest, AutomaticCompressionDetection) {
    std::string gz_file = CreateGzipFile(csv_content_, "auto_detect.csv.gz");
    
    CompressedCsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    // Don't specify compression_format to test auto-detection
    
    core::ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    auto stats = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<std::string>(stats["compression_format"]), "gzip");
}

} // namespace omop::extract::test
