/**
 * @file extract_utils_test.cpp
 * @brief Unit tests for extract utilities and factory functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extract.h"
#include "extract/extractor_factory.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#include "common/exceptions.h"
#include "common/logging.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <future>
#include <regex>
#include <algorithm>
#include <iostream>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::Throw;
using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;

// Mock extractor for testing
class MockIExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize,
                ((const std::unordered_map<std::string, std::any>&), (ProcessingContext&)),
                (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));
};

// Test fixture for extract utilities
class ExtractUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_extract_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// ============================================================================
// ExtractUtilsTest Tests
// ============================================================================

// Tests batch extractor
TEST_F(ExtractUtilsTest, BatchExtractor) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    // Set up expectations
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch1, batch2;
    for (int i = 0; i < 5; ++i) {
        Record r;
        r.setField("id", i);
        batch1.addRecord(r);
    }
    for (int i = 5; i < 8; ++i) {
        Record r;
        r.setField("id", i);
        batch2.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch1))
        .WillOnce(Return(batch2));

    BatchExtractor::Config config;
    config.batch_size = 5;

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(8, records.size());
}

// Tests batch extractor error handling
TEST_F(ExtractUtilsTest, BatchExtractorErrorHandling) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data()).WillOnce(Return(true));
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Throw(std::runtime_error("Extraction error")));

    BatchExtractor::Config config;
    config.continue_on_error = true;

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    EXPECT_THROW(batch_extractor.extract_all(), std::runtime_error);
}

// Tests batch extractor max records
TEST_F(ExtractUtilsTest, BatchExtractorMaxRecords) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    // Set up expectations for exactly 2 batches, then no more data
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true));

    RecordBatch large_batch;
    for (int i = 0; i < 10; ++i) {
        Record r;
        r.setField("id", i);
        large_batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(large_batch))
        .WillOnce(Return(large_batch));

    BatchExtractor::Config config;
    config.batch_size = 5;
    config.max_records = 15; // Limit to 15 records

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(15, records.size()); // Should stop at max_records
}

// Tests batch extractor race conditions
TEST_F(ExtractUtilsTest, BatchExtractorRaceConditions) {
    class SlowMockExtractor : public MockIExtractor {
    private:
        mutable std::mutex mutex_;
        std::atomic<int> batch_count_{0};
        std::atomic<bool> has_data_{true};

    public:
        RecordBatch extract_batch(size_t batch_size, 
                                 ProcessingContext& context) override {
            std::lock_guard<std::mutex> lock(mutex_);
            
            // Simulate slow extraction
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
            RecordBatch batch;
            int current_batch = batch_count_++;
            
            for (int i = 0; i < 5; ++i) {
                Record r;
                r.setField("batch", current_batch);
                r.setField("id", i);
                batch.addRecord(r);
            }
            
            if (batch_count_ >= 3) {
                has_data_ = false;
            }
            
            return batch;
        }

        bool has_more_data() const override {
            return has_data_;
        }
    };

    auto mock_extractor = std::make_unique<SlowMockExtractor>();
    BatchExtractor::Config config;
    config.batch_size = 5;

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    // Extract in multiple threads to test race conditions
    std::vector<std::future<std::vector<Record>>> futures;
    std::mutex results_mutex;
    std::vector<Record> all_results;

    for (int i = 0; i < 3; ++i) {
        futures.push_back(std::async(std::launch::async, [&batch_extractor]() {
            return batch_extractor.extract_all();
        }));
    }

    // Collect results
    for (auto& future : futures) {
        auto results = future.get();
        std::lock_guard<std::mutex> lock(results_mutex);
        all_results.insert(all_results.end(), results.begin(), results.end());
    }

    // Should have extracted all records without duplicates
    EXPECT_EQ(15, all_results.size());
}

// Tests batch extractor with callback
TEST_F(ExtractUtilsTest, BatchExtractorWithCallback) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 3; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _)).WillOnce(Return(batch));

    std::vector<std::pair<size_t, size_t>> progress_updates;
    BatchExtractor::Config config;
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.emplace_back(current, total);
    };

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(3, records.size());
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(3, progress_updates.back().first);
    EXPECT_EQ(3, progress_updates.back().second);
}

// Tests configuration validation
TEST_F(ExtractUtilsTest, ValidateExtractorConfig) {
    // Valid CSV configuration
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = createTestFile("valid.csv", "id\n1\n");
    auto [valid, error] = validate_extractor_config("csv", csv_config);
    EXPECT_TRUE(valid);
    EXPECT_TRUE(error.empty());

    // Missing required parameter
    std::unordered_map<std::string, std::any> invalid_config;
    auto [invalid, error_msg] = validate_extractor_config("csv", invalid_config);
    EXPECT_FALSE(invalid);
    EXPECT_FALSE(error_msg.empty());

    // Non-existent file
    csv_config["filepath"] = std::string("/nonexistent/file.csv");
    auto [not_found, not_found_error] = validate_extractor_config("csv", csv_config);
    EXPECT_FALSE(not_found);
    EXPECT_FALSE(not_found_error.empty());

    // Valid JSON configuration
    std::unordered_map<std::string, std::any> json_config;
    json_config["filepath"] = createTestFile("valid.json", "{\"data\": [{\"id\": 1}]}");
    auto [json_valid, json_error] = validate_extractor_config("json", json_config);
    EXPECT_TRUE(json_valid);
    EXPECT_TRUE(json_error.empty());
}

// Tests create connection from URL
TEST_F(ExtractUtilsTest, CreateConnectionFromUrl) {
    // Initialize database connection factories
    PostgreSQLRegistrar::register_components();
#ifdef OMOP_HAS_MYSQL
    MySQLRegistrar::register_components();
#endif
    
    // Test PostgreSQL URL - expect connection failure since no server is running, 
    // but the database type should be recognized correctly
    try {
        auto pg_conn = omop::extract::utils::create_connection_from_url("postgresql://user:pass@localhost:5432/dbname");
        // If connection succeeds (unlikely in test environment), verify the type
        EXPECT_NE(nullptr, pg_conn);
        EXPECT_EQ("postgresql", pg_conn->get_database_type());
    } catch (const omop::common::DatabaseException& e) {
        // Connection failure is expected in test environment
        // Check that it was trying to connect to PostgreSQL
        std::string error_msg = e.what();
        EXPECT_TRUE(error_msg.find("PostgreSQL") != std::string::npos || 
                   error_msg.find("postgresql") != std::string::npos);
    }

#ifdef OMOP_HAS_MYSQL
    // Test MySQL URL - similar approach
    try {
        auto mysql_conn = omop::extract::utils::create_connection_from_url("mysql://user:pass@localhost:3306/dbname");
        EXPECT_NE(nullptr, mysql_conn);
        EXPECT_EQ("mysql", mysql_conn->get_database_type());
    } catch (const omop::common::DatabaseException& e) {
        // Connection failure is expected in test environment
        std::string error_msg = e.what();
        EXPECT_TRUE(error_msg.find("MySQL") != std::string::npos || 
                   error_msg.find("mysql") != std::string::npos);
    }
#endif

    // Test invalid URL - this should throw a configuration exception, not database exception
    EXPECT_THROW(omop::extract::utils::create_connection_from_url("invalid://url"), 
                 omop::common::ConfigurationException);
}

// Tests create extractor auto
TEST_F(ExtractUtilsTest, CreateExtractorAuto) {
    // Initialize extractors if not already done
    initialize_extractors();

    // Test CSV file detection
    std::string csv_file = createTestFile("data.csv", "id,name\n1,test\n");
    auto csv_extractor = create_extractor_auto(csv_file);
    ASSERT_NE(nullptr, csv_extractor);
    EXPECT_EQ("csv", csv_extractor->get_type());

    // Test JSON file detection
    std::string json_file = createTestFile("data.json", "[{\"id\":1}]");
    auto json_extractor = create_extractor_auto(json_file);
    ASSERT_NE(nullptr, json_extractor);
    EXPECT_EQ("json", json_extractor->get_type());

    // Test JSONL file detection
    std::string jsonl_file = createTestFile("data.jsonl", "{\"id\":1}\n{\"id\":2}\n");
    auto jsonl_extractor = create_extractor_auto(jsonl_file);
    ASSERT_NE(nullptr, jsonl_extractor);
    EXPECT_EQ("jsonl", jsonl_extractor->get_type());

    // Test directory detection
    auto dir_extractor = create_extractor_auto(test_dir_.string());
    ASSERT_NE(nullptr, dir_extractor);
    EXPECT_EQ("csv_directory", dir_extractor->get_type());
}

// Tests database config builder
TEST_F(ExtractUtilsTest, DatabaseConfigBuilder) {
    // Create database configuration directly
    std::unordered_map<std::string, std::any> config;
    config["host"] = std::string("localhost");
    config["port"] = 5432;
    config["database"] = std::string("test_db");
    config["username"] = std::string("test_user");
    config["password"] = std::string("test_pass");
    config["table"] = std::string("test_table");

    EXPECT_EQ("localhost", std::any_cast<std::string>(config["host"]));
    EXPECT_EQ(5432, std::any_cast<int>(config["port"]));
    EXPECT_EQ("test_db", std::any_cast<std::string>(config["database"]));
    EXPECT_EQ("test_user", std::any_cast<std::string>(config["username"]));
    EXPECT_EQ("test_pass", std::any_cast<std::string>(config["password"]));
    EXPECT_EQ("test_table", std::any_cast<std::string>(config["table"]));
}

// Tests detect source type
TEST_F(ExtractUtilsTest, DetectSourceType) {
    // File extensions
    EXPECT_EQ("csv", detect_source_type("data.csv"));
    EXPECT_EQ("json", detect_source_type("data.json"));
    EXPECT_EQ("jsonl", detect_source_type("data.jsonl"));
    EXPECT_EQ("jsonl", detect_source_type("data.ndjson"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.csv.gz"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.csv.zip"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.gz"));

    // Database URLs
    EXPECT_EQ("postgresql", detect_source_type("postgresql://localhost/db"));
    EXPECT_EQ("postgres", detect_source_type("postgres://localhost/db"));
    EXPECT_EQ("mysql", detect_source_type("mysql://localhost/db"));
    EXPECT_EQ("mariadb", detect_source_type("mariadb://localhost/db"));

    // Directory
    std::filesystem::create_directories(test_dir_ / "csv_dir");
    createTestFile("csv_dir/file1.csv", "id\n1\n");
    EXPECT_EQ("csv_directory", detect_source_type((test_dir_ / "csv_dir").string()));

    // Unknown
    EXPECT_EQ("", detect_source_type("unknown.xyz"));
}

// Tests extractor config builder
TEST_F(ExtractUtilsTest, ExtractorConfigBuilder) {
    auto config = ExtractorConfigBuilder("csv")
        .with_file("test.csv")
        .set("batch_size", size_t(1000))
        .set("max_records", size_t(5000))
        .set("continue_on_error", true)
        .get_config();

    EXPECT_EQ("csv", std::any_cast<std::string>(config["type"]));
    EXPECT_EQ("test.csv", std::any_cast<std::string>(config["filepath"]));
    EXPECT_EQ(1000, std::any_cast<size_t>(config["batch_size"]));
    EXPECT_EQ(5000, std::any_cast<size_t>(config["max_records"]));
    EXPECT_EQ(true, std::any_cast<bool>(config["continue_on_error"]));
}

// Tests extractor factory concurrent modification
TEST_F(ExtractUtilsTest, FactoryConcurrentModification) {
    // Test concurrent registration and creation
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    std::atomic<int> failure_count{0};

    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([i, &success_count, &failure_count]() {
            try {
                // Register a new type
                std::string type_name = "concurrent_type_" + std::to_string(i);
                ExtractorFactoryRegistry::register_type(type_name, []() {
                    return std::make_unique<MockIExtractor>();
                });

                // Try to create it
                auto extractor = ExtractorFactoryRegistry::create(type_name);
                if (extractor) {
                    success_count++;
                }
            } catch (const std::exception&) {
                failure_count++;
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    // Should have some successful registrations
    EXPECT_GT(success_count, 0);
}

// Tests extractor factory registry
TEST_F(ExtractUtilsTest, ExtractorFactoryRegistry) {
    // Test registration
    ExtractorFactoryRegistry::register_type("test_type", []() {
        return std::make_unique<MockIExtractor>();
    });

    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_type"));

    // Test creation
    auto extractor = ExtractorFactoryRegistry::create("test_type");
    EXPECT_NE(nullptr, extractor);

    // Test unknown type
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"), omop::common::ConfigurationException);

    // Test duplicate registration - should not throw, just overwrite
    EXPECT_NO_THROW(ExtractorFactoryRegistry::register_type("test_type", []() {
        return std::make_unique<MockIExtractor>();
    }));
}

// Tests extractor factory thread safety
TEST_F(ExtractUtilsTest, FactoryThreadSafety) {
    // Test thread-safe registration and creation
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([i, &success_count]() {
            try {
                std::string type_name = "thread_type_" + std::to_string(i);
                
                // Register type
                ExtractorFactoryRegistry::register_type(type_name, [type_name]() {
                    auto mock = std::make_unique<MockIExtractor>();
                    // Set up mock to return the correct type name
                    EXPECT_CALL(*mock, get_type())
                        .WillRepeatedly(Return(type_name));
                    return mock;
                });

                // Create extractor
                auto extractor = ExtractorFactoryRegistry::create(type_name);
                if (extractor && extractor->get_type() == type_name) {
                    success_count++;
                }
            } catch (const std::exception&) {
                // Expected for duplicate registrations
            }
        });
    }

    for (auto& t : threads) {
        t.join();
    }

    // Should have successful operations
    EXPECT_GT(success_count, 0);
}

// Tests get extractor info
TEST_F(ExtractUtilsTest, GetExtractorInfo) {
    auto info_list = get_extractor_info();
    EXPECT_FALSE(info_list.empty());

    // Check that each info entry has required fields
    for (const auto& info : info_list) {
        EXPECT_FALSE(info.type.empty());
        EXPECT_FALSE(info.description.empty());
    }

    // Check for specific expected types
    auto csv_info = std::find_if(info_list.begin(), info_list.end(),
        [](const auto& info) { return info.type == "csv"; });
    EXPECT_NE(csv_info, info_list.end());
}

// Tests initialize extractors
TEST_F(ExtractUtilsTest, InitializeExtractors) {
    // Clear any existing registrations
    ExtractorFactoryRegistry::clear();

    // Initialize extractors
    EXPECT_NO_THROW(initialize_extractors());

    // Verify some expected types are registered
    std::vector<std::string> expected_types = {
        "csv", "json", "jsonl", "mysql", "postgresql"
    };

    for (const auto& type : expected_types) {
        if (ExtractorFactoryRegistry::is_type_registered(type)) {
            auto extractor = ExtractorFactoryRegistry::create(type);
            EXPECT_NE(nullptr, extractor);
            EXPECT_EQ(type, extractor->get_type());
        }
    }

    // Test that calling initialize again is safe
    EXPECT_NO_THROW(initialize_extractors());
}

// Tests ParallelExtractor functionality with thread-safe real extractors
TEST_F(ExtractUtilsTest, ParallelExtractor) {
    ParallelExtractor::Config config;
    config.num_threads = 2;
    config.queue_size = 5;

    ParallelExtractor parallel_extractor(config);

    // Add multiple extractors
    for (int i = 0; i < 3; ++i) {
        // Create test CSV file
        std::string csv_content = std::format("extractor_id,value\n{},{}\n", i, i * 100);
        std::string filepath = createTestFile(std::format("parallel_{}.csv", i), csv_content);
        
        // Create real CSV extractor
        auto csv_extractor = std::make_unique<CsvExtractor>();
        std::unordered_map<std::string, std::any> csv_config;
        csv_config["filepath"] = filepath;
        
        ProcessingContext init_context;
        csv_extractor->initialize(csv_config, init_context);
        
        parallel_extractor.add_extractor(std::move(csv_extractor),
                                        std::format("extractor_{}", i));
    }

    // Extract all records
    auto all_records = parallel_extractor.extract_all();
    EXPECT_EQ(3, all_records.size()); // 3 extractors * 1 record each
}

// Tests ParallelExtractor streaming functionality
TEST_F(ExtractUtilsTest, ParallelExtractorStreaming) {
    ParallelExtractor parallel_extractor;

    // Create test CSV file
    std::string csv_content = "id\n1\n";
    std::string filepath = createTestFile("streaming_test.csv", csv_content);
    
    // Create real CSV extractor
    auto csv_extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = filepath;
    
    ProcessingContext init_context;
    csv_extractor->initialize(csv_config, init_context);
    
    parallel_extractor.add_extractor(std::move(csv_extractor), "test_extractor");

    // Test streaming
    auto batch = parallel_extractor.extract_batch(10);
    EXPECT_EQ(1, batch.size());
}

// Tests streaming callback functionality with thread-safe implementation
TEST_F(ExtractUtilsTest, ParallelExtractorStreamingCallback) {
    ParallelExtractor parallel_extractor;

    // Create test CSV file
    std::string csv_content = "id\n1\n";
    std::string filepath = createTestFile("streaming_test.csv", csv_content);
    
    // Create real CSV extractor
    auto csv_extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = filepath;
    
    ProcessingContext init_context;
    csv_extractor->initialize(csv_config, init_context);
    
    parallel_extractor.add_extractor(std::move(csv_extractor), "test");

    std::atomic<int> batch_count{0};
    std::mutex result_mutex;
    parallel_extractor.extract_streaming(
        [&batch_count, &result_mutex](const RecordBatch& b, const std::string& name) {
            std::lock_guard<std::mutex> lock(result_mutex);
            batch_count++;
            EXPECT_EQ("test", name);
            EXPECT_EQ(1, b.size());
        });

    EXPECT_EQ(1, batch_count);
}

// Tests progress tracking
TEST_F(ExtractUtilsTest, ProgressTracking) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();

    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));

    RecordBatch batch;
    for (int i = 0; i < 5; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(r);
    }

    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _)).WillOnce(Return(batch));

    std::vector<std::pair<size_t, size_t>> progress_updates;
    BatchExtractor::Config config;
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.emplace_back(current, total);
    };

    BatchExtractor batch_extractor(std::unique_ptr<omop::core::IExtractor>(std::move(mock_extractor)), config);

    auto records = batch_extractor.extract_all();
    EXPECT_EQ(5, records.size());
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(5, progress_updates.back().first);
    EXPECT_EQ(5, progress_updates.back().second);
}

// Tests utility extract CSV
TEST_F(ExtractUtilsTest, UtilityExtractCsv) {
    // Create test CSV file
    std::string csv_content = "id,name,value\n1,John,100.5\n2,Jane,200.75\n";
    std::string csv_file = createTestFile("test.csv", csv_content);

    // Test CSV extraction utility
    auto records = omop::extract::utils::extract_csv(csv_file);
    EXPECT_EQ(2, records.size());

    // Safely check field types and values
    EXPECT_TRUE(records[0].hasField("id"));
    EXPECT_TRUE(records[0].hasField("name"));

    // Get fields and check their types
    auto id_field = records[0].getField("id");
    auto name_field = records[0].getField("name");

    // Handle multiple possible types for id field
    if (id_field.type() == typeid(std::string)) {
        EXPECT_EQ("1", std::any_cast<std::string>(id_field));
    } else if (id_field.type() == typeid(int)) {
        EXPECT_EQ(1, std::any_cast<int>(id_field));
    } else if (id_field.type() == typeid(long long)) {
        EXPECT_EQ(1LL, std::any_cast<long long>(id_field));
    } else {
        FAIL() << "Unexpected type for id field: " << id_field.type().name();
    }

    // Name field should be string
    if (name_field.type() == typeid(std::string)) {
        EXPECT_EQ("John", std::any_cast<std::string>(name_field));
    } else {
        FAIL() << "Unexpected type for name field: " << name_field.type().name();
    }
}

// Tests utility extract JSON
TEST_F(ExtractUtilsTest, UtilityExtractJson) {
    // Create test JSON file
    std::string json_content = R"([
        {"id": 1, "name": "John", "active": true},
        {"id": 2, "name": "Jane", "active": false}
    ])";
    std::string json_file = createTestFile("test.json", json_content);

    // Test JSON extraction utility
    auto records = omop::extract::utils::extract_json(json_file);
    EXPECT_EQ(2, records.size());

    // Verify first record fields exist
    EXPECT_TRUE(records[0].hasField("id"));
    EXPECT_TRUE(records[0].hasField("name"));
    EXPECT_TRUE(records[0].hasField("active"));

    // Get fields and check their types safely
    auto id_field = records[0].getField("id");
    auto name_field = records[0].getField("name");
    auto active_field = records[0].getField("active");

    // Handle multiple possible types for id field
    if (id_field.type() == typeid(int)) {
        EXPECT_EQ(1, std::any_cast<int>(id_field));
    } else if (id_field.type() == typeid(long)) {
        EXPECT_EQ(1L, std::any_cast<long>(id_field));
    } else if (id_field.type() == typeid(long long)) {
        EXPECT_EQ(1LL, std::any_cast<long long>(id_field));
    } else if (id_field.type() == typeid(double)) {
        EXPECT_EQ(1.0, std::any_cast<double>(id_field));
    } else if (id_field.type() == typeid(std::string)) {
        EXPECT_EQ("1", std::any_cast<std::string>(id_field));
    } else {
        FAIL() << "Unexpected type for id field: " << id_field.type().name();
    }

    // Name field should be string
    if (name_field.type() == typeid(std::string)) {
        EXPECT_EQ("John", std::any_cast<std::string>(name_field));
    } else {
        FAIL() << "Unexpected type for name field: " << name_field.type().name();
    }

    // Active field should be bool
    if (active_field.type() == typeid(bool)) {
        EXPECT_EQ(true, std::any_cast<bool>(active_field));
    } else if (active_field.type() == typeid(std::string)) {
        EXPECT_EQ("true", std::any_cast<std::string>(active_field));
    } else {
        FAIL() << "Unexpected type for active field: " << active_field.type().name();
    }
}