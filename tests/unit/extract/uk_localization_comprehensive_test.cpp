/**
 * @file uk_localization_comprehensive_test.cpp
 * @brief Comprehensive unit tests for UK localization features in extract library
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains comprehensive tests for UK regional differences including:
 * - Date/time formats (DD/MM/YYYY vs MM/DD/YYYY)
 * - Currency formats (£ symbol, pence handling)
 * - Temperature units (Celsius vs Fahrenheit)
 * - Postal codes (UK format validation)
 * - Telephone numbers (UK format validation)
 * - Measurement units (metric vs imperial)
 * - Address formats
 * - Number formatting (decimal separators, thousands separators)
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/extract_utils.h"
#include "extract/extractor_factory.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <chrono>
#include <locale>
#include <iomanip>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;

// ============================================================================
// UK Localization Comprehensive Tests
// ============================================================================

class UKLocalizationTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_uk_localization_test";
        std::filesystem::create_directories(test_dir_);
        
        // Set UK locale for tests
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            // Fallback if locale not available
            std::locale::global(std::locale("C"));
        }
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests UK date format parsing (DD/MM/YYYY)
TEST_F(UKLocalizationTest, UKDateFormatParsing) {
    CsvOptions options;
    options.date_format = "%d/%m/%Y";
    options.datetime_format = "%d/%m/%Y %H:%M:%S";
    options.has_header = true;
    
    CsvFieldParser parser(options);
    
    // Test various UK date formats
    std::vector<std::string> uk_dates = {
        "15/01/2025",       // Standard UK date
        "01/01/2025",       // New Year's Day
        "31/12/2024",       // New Year's Eve
        "29/02/2024",       // Leap year date
        "25/12/2024",       // Christmas Day
        "04/07/2025",       // Should be 4th July, not April 7th
        "11/09/2024"        // Should be 11th September, not November 9th
    };
    
    for (const auto& date_str : uk_dates) {
        auto date_value = parser.convert_field(date_str, "date");
        EXPECT_TRUE(date_value.has_value()) << "Failed to parse UK date: " << date_str;
        EXPECT_TRUE(date_value.type() == typeid(std::chrono::system_clock::time_point)) 
            << "Date should be parsed as time_point for: " << date_str;
    }
    
    // Test UK datetime format with 24-hour time
    std::vector<std::string> uk_datetimes = {
        "15/01/2025 09:30:00",  // Morning
        "15/01/2025 13:45:30",  // Afternoon (24-hour format)
        "15/01/2025 23:59:59",  // Late evening
        "01/01/2025 00:00:00"   // Midnight
    };
    
    for (const auto& datetime_str : uk_datetimes) {
        auto datetime_value = parser.convert_field(datetime_str, "datetime");
        EXPECT_TRUE(datetime_value.has_value()) << "Failed to parse UK datetime: " << datetime_str;
        EXPECT_TRUE(datetime_value.type() == typeid(std::chrono::system_clock::time_point))
            << "DateTime should be parsed as time_point for: " << datetime_str;
    }
}

// Tests UK currency format handling (£ symbol, pence)
TEST_F(UKLocalizationTest, UKCurrencyFormatHandling) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK currency formats
    std::vector<std::pair<std::string, std::string>> uk_currencies = {
        {"£0.99", "Ninety-nine pence"},
        {"£1.00", "One pound"},
        {"£12.50", "Twelve pounds fifty pence"},
        {"£1,234.56", "Thousands separator with pounds"},
        {"£10,000.00", "Ten thousand pounds"},
        {"£999,999.99", "Just under one million"},
        {"£1,000,000.00", "One million pounds"},
        {"£0.01", "One penny"},
        {"£0.02", "Two pence"},
        {"£5.00", "Five pound note"},
        {"£10.00", "Ten pound note"},
        {"£20.00", "Twenty pound note"},
        {"£50.00", "Fifty pound note"}
    };
    
    for (const auto& [currency_str, description] : uk_currencies) {
        auto currency_value = parser.convert_field(currency_str, "string");
        EXPECT_TRUE(currency_value.has_value()) << "Failed to parse UK currency: " << currency_str;
        EXPECT_EQ(currency_str, std::any_cast<std::string>(currency_value))
            << "Currency should be preserved as string for: " << description;
    }
    
    // Test CSV with UK currency data
    std::string csv_content = R"(item,price,category
Coffee,£2.50,Beverages
Sandwich,£4.95,Food
Newspaper,£1.20,Media
Magazine,£3.99,Media
Bus Ticket,£2.30,Transport
Train Ticket,£15.60,Transport
)";
    
    std::string csv_file = createTestFile("uk_prices.csv", csv_content);
    auto records = omop::extract::utils::extract_csv(csv_file, options);
    
    EXPECT_EQ(6, records.size());
    
    // Verify currency fields are preserved correctly
    EXPECT_TRUE(records[0].hasField("price"));
    auto coffee_price = records[0].getField("price");
    if (coffee_price.type() == typeid(std::string)) {
        EXPECT_EQ("£2.50", std::any_cast<std::string>(coffee_price));
    }
}

// Tests UK temperature format (Celsius)
TEST_F(UKLocalizationTest, UKTemperatureFormat) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK temperature formats (Celsius)
    std::vector<std::pair<std::string, std::string>> uk_temperatures = {
        {"5°C", "Winter temperature"},
        {"15°C", "Mild spring temperature"},
        {"23°C", "Pleasant summer temperature"},
        {"30°C", "Hot summer day"},
        {"-2°C", "Below freezing"},
        {"0°C", "Freezing point"},
        {"37°C", "Body temperature"},
        {"100°C", "Boiling point of water"}
    };
    
    for (const auto& [temp_str, description] : uk_temperatures) {
        auto temp_value = parser.convert_field(temp_str, "string");
        EXPECT_TRUE(temp_value.has_value()) << "Failed to parse UK temperature: " << temp_str;
        EXPECT_EQ(temp_str, std::any_cast<std::string>(temp_value))
            << "Temperature should be preserved as string for: " << description;
    }
    
    // Test CSV with UK weather data
    std::string weather_csv = R"(city,temperature,humidity,condition
London,15°C,78%,Cloudy
Manchester,12°C,82%,Rainy
Birmingham,18°C,65%,Partly Cloudy
Edinburgh,8°C,85%,Overcast
Cardiff,16°C,70%,Sunny
)";
    
    std::string weather_file = createTestFile("uk_weather.csv", weather_csv);
    auto weather_records = omop::extract::utils::extract_csv(weather_file, CsvOptions{});
    
    EXPECT_EQ(5, weather_records.size());
    
    // Verify temperature field preservation
    EXPECT_TRUE(weather_records[0].hasField("temperature"));
    auto london_temp = weather_records[0].getField("temperature");
    if (london_temp.type() == typeid(std::string)) {
        EXPECT_EQ("15°C", std::any_cast<std::string>(london_temp));
    }
}

// Tests UK postal code format validation
TEST_F(UKLocalizationTest, UKPostalCodeValidation) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various valid UK postal codes
    std::vector<std::pair<std::string, std::string>> uk_postcodes = {
        {"SW1A 1AA", "Buckingham Palace"},
        {"M1 1AA", "Manchester"},
        {"B33 8TH", "Birmingham"},
        {"W1A 0AX", "BBC Broadcasting House"},
        {"EC1A 1BB", "City of London"},
        {"E1 6AN", "Tower of London area"},
        {"NW1 6XE", "Camden area"},
        {"SE1 9RT", "South London"},
        {"N1 9GU", "North London"},
        {"WC2N 5DU", "Covent Garden"},
        {"CR0 2YY", "Croydon"},
        {"BR1 3XX", "Bromley"},
        {"TW1 3QS", "Twickenham"},
        {"KT1 2EE", "Kingston upon Thames"},
        {"SM1 1AA", "Sutton"}
    };
    
    for (const auto& [postcode, area] : uk_postcodes) {
        auto postcode_value = parser.convert_field(postcode, "string");
        EXPECT_TRUE(postcode_value.has_value()) << "Failed to parse UK postcode: " << postcode;
        EXPECT_EQ(postcode, std::any_cast<std::string>(postcode_value))
            << "Postcode should be preserved for area: " << area;
    }
    
    // Test CSV with UK address data
    std::string address_csv = R"(name,street,city,postcode,country
John Smith,"123 High Street",London,SW1A 1AA,United Kingdom
Jane Doe,"456 Market Street",Manchester,M1 1AA,United Kingdom
Bob Johnson,"789 Castle Street",Edinburgh,EH1 2NG,United Kingdom
Alice Brown,"321 Queen Street",Cardiff,CF10 2HQ,United Kingdom
)";
    
    std::string address_file = createTestFile("uk_addresses.csv", address_csv);
    auto address_records = omop::extract::utils::extract_csv(address_file, CsvOptions{});
    
    EXPECT_EQ(4, address_records.size());
    
    // Verify postcode field preservation
    for (size_t i = 0; i < address_records.size(); ++i) {
        EXPECT_TRUE(address_records[i].hasField("postcode"));
        EXPECT_TRUE(address_records[i].hasField("country"));
        
        auto country = address_records[i].getField("country");
        if (country.type() == typeid(std::string)) {
            EXPECT_EQ("United Kingdom", std::any_cast<std::string>(country));
        }
    }
}

// Tests UK telephone number format validation
TEST_F(UKLocalizationTest, UKTelephoneNumberValidation) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK telephone number formats
    std::vector<std::pair<std::string, std::string>> uk_phone_numbers = {
        {"+44 20 7946 0958", "London number with country code"},
        {"020 7946 0958", "London number"},
        {"0161 496 0000", "Manchester number"},
        {"0121 496 0000", "Birmingham number"},
        {"0131 496 0000", "Edinburgh number"},
        {"029 2000 0000", "Cardiff number"},
        {"01273 000000", "Brighton number"},
        {"01223 000000", "Cambridge number"},
        {"01865 000000", "Oxford number"},
        {"01904 000000", "York number"},
        {"07700 900000", "Mobile number"},
        {"07400 123456", "Mobile number"},
        {"0800 123456", "Freephone number"},
        {"0845 123456", "Lo-call number"},
        {"0870 123456", "National rate number"}
    };
    
    for (const auto& [phone_str, description] : uk_phone_numbers) {
        auto phone_value = parser.convert_field(phone_str, "string");
        EXPECT_TRUE(phone_value.has_value()) << "Failed to parse UK phone number: " << phone_str;
        EXPECT_EQ(phone_str, std::any_cast<std::string>(phone_value))
            << "Phone number should be preserved for: " << description;
    }
    
    // Test CSV with UK contact data
    std::string contact_csv = R"(name,office_phone,mobile_phone,department
John Smith,020 7946 0958,07700 900123,Finance
Jane Doe,0161 496 0001,07400 123456,Marketing
Bob Johnson,0131 496 0002,07500 987654,Engineering
Alice Brown,029 2000 0003,07600 555777,Human Resources
)";
    
    std::string contact_file = createTestFile("uk_contacts.csv", contact_csv);
    auto contact_records = omop::extract::utils::extract_csv(contact_file, CsvOptions{});
    
    EXPECT_EQ(4, contact_records.size());
    
    // Verify phone number field preservation
    for (const auto& record : contact_records) {
        EXPECT_TRUE(record.hasField("office_phone"));
        EXPECT_TRUE(record.hasField("mobile_phone"));
    }
}

// Tests UK measurement units (metric system)
TEST_F(UKLocalizationTest, UKMeasurementUnits) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK measurement formats (metric system)
    std::vector<std::pair<std::string, std::string>> uk_measurements = {
        {"1.75m", "Height in metres"},
        {"70kg", "Weight in kilograms"},
        {"180cm", "Height in centimetres"},
        {"500ml", "Volume in millilitres"},
        {"2.5l", "Volume in litres"},
        {"250g", "Weight in grams"},
        {"1.5km", "Distance in kilometres"},
        {"50mm", "Length in millimetres"},
        {"10°C", "Temperature in Celsius"},
        {"750ml", "Wine bottle volume"},
        {"330ml", "Beer can volume"},
        {"2L", "Large bottle volume"},
        {"100g", "Food portion weight"}
    };
    
    for (const auto& [measurement_str, description] : uk_measurements) {
        auto measurement_value = parser.convert_field(measurement_str, "string");
        EXPECT_TRUE(measurement_value.has_value()) << "Failed to parse UK measurement: " << measurement_str;
        EXPECT_EQ(measurement_str, std::any_cast<std::string>(measurement_value))
            << "Measurement should be preserved for: " << description;
    }
    
    // Test CSV with UK product data
    std::string product_csv = R"(product,weight,volume,price,category
Milk,1kg,1l,£1.20,Dairy
Orange Juice,500g,500ml,£2.50,Beverages
Bread,800g,N/A,£1.50,Bakery
Wine,750g,750ml,£8.99,Alcohol
Beer,330g,330ml,£1.75,Alcohol
)";
    
    std::string product_file = createTestFile("uk_products.csv", product_csv);
    auto product_records = omop::extract::utils::extract_csv(product_file, CsvOptions{});
    
    EXPECT_EQ(5, product_records.size());
    
    // Verify measurement fields are preserved
    for (const auto& record : product_records) {
        EXPECT_TRUE(record.hasField("weight"));
        EXPECT_TRUE(record.hasField("volume"));
        EXPECT_TRUE(record.hasField("price"));
    }
}

// Tests UK number formatting (decimal separator, thousands separator)
TEST_F(UKLocalizationTest, UKNumberFormatting) {
    CsvFieldParser parser(CsvOptions{});
    
    // Test various UK number formats
    std::vector<std::pair<std::string, std::string>> uk_numbers = {
        {"1,234", "Thousands separator"},
        {"12,345", "Ten thousands"},
        {"123,456", "Hundreds of thousands"},
        {"1,234,567", "Millions"},
        {"1,234,567.89", "Millions with decimal"},
        {"123.45", "Decimal number"},
        {"0.99", "Less than one"},
        {"1000", "Thousand without separator"},
        {"3.14159", "Pi approximation"},
        {"2.718", "Euler's number approximation"}
    };
    
    for (const auto& [number_str, description] : uk_numbers) {
        auto number_value = parser.convert_field(number_str, "");
        EXPECT_TRUE(number_value.has_value()) << "Failed to parse UK number: " << number_str;
        // Numbers should be auto-detected as numeric types or preserved as strings
    }
    
    // Test CSV with UK financial data
    std::string financial_csv = R"(account,balance,transactions,interest_rate
Savings,£12,345.67,145,2.5%
Current,£1,234.56,89,0.1%
ISA,£20,000.00,52,3.2%
Premium,£50,000.00,23,1.8%
)";
    
    std::string financial_file = createTestFile("uk_financial.csv", financial_csv);
    auto financial_records = omop::extract::utils::extract_csv(financial_file, CsvOptions{});
    
    EXPECT_EQ(4, financial_records.size());
    
    // Verify financial fields are preserved
    for (const auto& record : financial_records) {
        EXPECT_TRUE(record.hasField("balance"));
        EXPECT_TRUE(record.hasField("transactions"));
        EXPECT_TRUE(record.hasField("interest_rate"));
    }
}

// Tests comprehensive UK localized CSV extraction
TEST_F(UKLocalizationTest, ComprehensiveUKLocalizedCSVExtraction) {
    // Create a comprehensive UK-localized CSV file
    std::string comprehensive_csv = R"(patient_id,name,dob,address,postcode,phone,weight,height,temperature,visit_date,cost
1001,John Smith,15/01/1980,"123 High Street, London",SW1A 1AA,020 7946 0958,75kg,1.75m,36.5°C,25/12/2024,£150.00
1002,Jane Doe,03/07/1975,"456 Market Street, Manchester",M1 1AA,0161 496 0000,65kg,1.68m,37.0°C,26/12/2024,£125.50
1003,Bob Johnson,22/11/1990,"789 Castle Street, Edinburgh",EH1 2NG,0131 496 0000,80kg,1.82m,36.8°C,27/12/2024,£175.25
1004,Alice Brown,08/05/1985,"321 Queen Street, Cardiff",CF10 2HQ,029 2000 0000,60kg,1.65m,36.2°C,28/12/2024,£200.75
)";
    
    // Configure CSV options for UK formats
    CsvOptions uk_options;
    uk_options.date_format = "%d/%m/%Y";
    uk_options.datetime_format = "%d/%m/%Y %H:%M:%S";
    uk_options.has_header = true;
    uk_options.delimiter = ',';
    uk_options.trim_fields = true;
    
    std::string comprehensive_file = createTestFile("uk_patients.csv", comprehensive_csv);
    auto patient_records = omop::extract::utils::extract_csv(comprehensive_file, uk_options);
    
    EXPECT_EQ(4, patient_records.size());
    
    // Verify all UK-specific fields are properly extracted
    for (size_t i = 0; i < patient_records.size(); ++i) {
        const auto& record = patient_records[i];
        
        // Check all expected fields exist
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("name"));
        EXPECT_TRUE(record.hasField("dob"));
        EXPECT_TRUE(record.hasField("address"));
        EXPECT_TRUE(record.hasField("postcode"));
        EXPECT_TRUE(record.hasField("phone"));
        EXPECT_TRUE(record.hasField("weight"));
        EXPECT_TRUE(record.hasField("height"));
        EXPECT_TRUE(record.hasField("temperature"));
        EXPECT_TRUE(record.hasField("visit_date"));
        EXPECT_TRUE(record.hasField("cost"));
        
        // Verify UK postcode format is preserved
        auto postcode = record.getField("postcode");
        if (postcode.type() == typeid(std::string)) {
            std::string postcode_str = std::any_cast<std::string>(postcode);
            EXPECT_TRUE(postcode_str.find(" ") != std::string::npos) 
                << "UK postcode should contain space: " << postcode_str;
        }
        
        // Verify UK currency format is preserved
        auto cost = record.getField("cost");
        if (cost.type() == typeid(std::string)) {
            std::string cost_str = std::any_cast<std::string>(cost);
            EXPECT_TRUE(cost_str.starts_with("£")) 
                << "UK cost should start with £: " << cost_str;
        }
        
        // Verify UK phone number format is preserved
        auto phone = record.getField("phone");
        if (phone.type() == typeid(std::string)) {
            std::string phone_str = std::any_cast<std::string>(phone);
            EXPECT_TRUE(phone_str.starts_with("0") || phone_str.starts_with("+44"))
                << "UK phone should start with 0 or +44: " << phone_str;
        }
    }
}

// Tests JSON extraction with UK localized data
TEST_F(UKLocalizationTest, UKLocalizedJSONExtraction) {
    // Create a JSON file with UK-specific data
    std::string uk_json = R"([
  {
    "patient_id": 1001,
    "name": "John Smith",
    "dob": "15/01/1980",
    "address": {
      "street": "123 High Street",
      "city": "London",
      "postcode": "SW1A 1AA",
      "country": "United Kingdom"
    },
    "contact": {
      "phone": "020 7946 0958",
      "mobile": "07700 900123"
    },
    "measurements": {
      "weight": "75kg",
      "height": "1.75m",
      "temperature": "36.5°C"
    },
    "visit": {
      "date": "25/12/2024",
      "cost": "£150.00"
    }
  },
  {
    "patient_id": 1002,
    "name": "Jane Doe",
    "dob": "03/07/1975",
    "address": {
      "street": "456 Market Street",
      "city": "Manchester",
      "postcode": "M1 1AA",
      "country": "United Kingdom"
    },
    "contact": {
      "phone": "0161 496 0000",
      "mobile": "07400 123456"
    },
    "measurements": {
      "weight": "65kg",
      "height": "1.68m",
      "temperature": "37.0°C"
    },
    "visit": {
      "date": "26/12/2024",
      "cost": "£125.50"
    }
  }
])";
    
    JsonOptions uk_json_options;
    uk_json_options.flatten_nested = true;
    uk_json_options.parse_dates = true;
    uk_json_options.date_formats = {"%d/%m/%Y", "%d/%m/%Y %H:%M:%S"};
    
    std::string json_file = createTestFile("uk_patients.json", uk_json);
    auto json_records = omop::extract::utils::extract_json(json_file, uk_json_options);
    
    EXPECT_EQ(2, json_records.size());
    
    // Verify flattened UK-specific fields are properly extracted
    for (const auto& record : json_records) {
        // Check flattened address fields
        EXPECT_TRUE(record.hasField("address_postcode") || record.hasField("address.postcode"));
        EXPECT_TRUE(record.hasField("address_country") || record.hasField("address.country"));
        
        // Check flattened contact fields
        EXPECT_TRUE(record.hasField("contact_phone") || record.hasField("contact.phone"));
        EXPECT_TRUE(record.hasField("contact_mobile") || record.hasField("contact.mobile"));
        
        // Check flattened measurement fields
        EXPECT_TRUE(record.hasField("measurements_weight") || record.hasField("measurements.weight"));
        EXPECT_TRUE(record.hasField("measurements_height") || record.hasField("measurements.height"));
        EXPECT_TRUE(record.hasField("measurements_temperature") || record.hasField("measurements.temperature"));
        
        // Check flattened visit fields
        EXPECT_TRUE(record.hasField("visit_cost") || record.hasField("visit.cost"));
        EXPECT_TRUE(record.hasField("visit_date") || record.hasField("visit.date"));
    }
}