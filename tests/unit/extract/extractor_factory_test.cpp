/**
 * @file extractor_factory_impl_test.cpp
 * @brief Unit tests for ExtractorFactoryRegistry and related factory functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_factory.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <thread>
#include <set>
#include <iostream>

namespace omop::extract::test {

using namespace ::testing;

// Mock ProcessingContext for testing
// Note: ProcessingContext methods are not virtual, so we use composition instead of inheritance
class MockProcessingContext {
public:
    // Mock methods that would be useful for testing
    MOCK_METHOD(void, log, (const std::string& level, const std::string& message));
    MOCK_METHOD(void, increment_errors, ());
    MOCK_METHOD(bool, should_continue_on_error, (), (const));

    // Provide a real ProcessingContext for tests that need it
    core::ProcessingContext& get_real_context() { return real_context_; }

private:
    core::ProcessingContext real_context_;
};

// Mock extractor for testing
class MockExtractor : public core::IExtractor {
public:
    explicit MockExtractor(const std::string& type = "mock") : type_(type) {}

    MOCK_METHOD(void, initialize,
        ((const std::unordered_map<std::string, std::any>&), (core::ProcessingContext&)),
        (override));
    MOCK_METHOD(core::RecordBatch, extract_batch,
        (size_t batch_size, core::ProcessingContext& context),
        (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(void, finalize, (core::ProcessingContext& context), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));

    std::string get_type() const override { return type_; }

private:
    std::string type_;
};

// Test fixture for factory tests
class ExtractorFactoryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Don't clear registrations - let each test manage its own state
    }

    void TearDown() override {
        // Don't clear registrations - let each test manage its own state
    }
};

// ============================================================================
// ExtractorFactoryTest Tests
// ============================================================================

// Tests clear registry functionality
TEST_F(ExtractorFactoryTest, ClearRegistry) {
    // Register a type
    ExtractorFactoryRegistry::register_type("test_clear",
        []() { return std::make_unique<MockExtractor>("test_clear"); });

    // Verify it's registered
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_clear"));

    // Clear registry
    ExtractorFactoryRegistry::clear();

    // Verify it's no longer registered
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("test_clear"));
    EXPECT_THROW(ExtractorFactoryRegistry::create("test_clear"),
                 common::ConfigurationException);
}

// Tests create extractor with config
TEST_F(ExtractorFactoryTest, CreateExtractorWithConfig) {
    // Register a type
    ExtractorFactoryRegistry::register_type("config_test",
        []() { return std::make_unique<MockExtractor>("config_test"); });

    // Create with config
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(1000);
    config["max_records"] = size_t(5000);

    auto extractor = ExtractorFactoryRegistry::create("config_test");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "config_test");
}

// Tests create unknown type
TEST_F(ExtractorFactoryTest, CreateUnknownType) {
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("unknown_type"));
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"),
                 common::ConfigurationException);
}

// Tests create various extractor types
TEST_F(ExtractorFactoryTest, CreateVariousExtractorTypes) {
    // Initialize built-in extractors
    initialize_extractors();

    struct TestCase {
        std::string type;
        std::string expected_class_type;
    };

    std::vector<TestCase> test_cases = {
        {"csv", "CsvExtractor"},
        {"json", "JsonExtractor"},
        {"jsonl", "JsonExtractor"},
        {"json_lines", "JsonExtractor"},
        {"streaming_json", "JsonExtractor"},
        {"multi_csv", "CsvExtractor"},
        {"csv_directory", "CsvExtractor"},
        {"compressed_csv", "CompressedCsvExtractor"}
    };

    for (const auto& test_case : test_cases) {
        if (ExtractorFactoryRegistry::is_type_registered(test_case.type)) {
            auto extractor = ExtractorFactoryRegistry::create(test_case.type);
            EXPECT_NE(extractor, nullptr);
            EXPECT_EQ(extractor->get_type(), test_case.type);
        }
    }
}

// Tests database extractor aliases
TEST_F(ExtractorFactoryTest, DatabaseExtractorAliases) {
    // Initialize built-in extractors
    initialize_extractors();

    // Test database extractor aliases
    std::vector<std::string> db_aliases = {
        "mysql", "postgresql", "postgres", "sqlite", "oracle", "sqlserver"
    };

    for (const auto& alias : db_aliases) {
        if (ExtractorFactoryRegistry::is_type_registered(alias)) {
            auto extractor = ExtractorFactoryRegistry::create(alias);
            EXPECT_NE(extractor, nullptr);
            EXPECT_EQ(extractor->get_type(), alias);
        }
    }
}

// Tests empty type handling
TEST_F(ExtractorFactoryTest, EmptyTypeHandling) {
    // Test empty type creation
    EXPECT_THROW(ExtractorFactoryRegistry::create(""),
                 common::ConfigurationException);
}

// Tests exception details
TEST_F(ExtractorFactoryTest, ExceptionDetails) {
    try {
        ExtractorFactoryRegistry::create("nonexistent_type");
        FAIL() << "Expected ConfigurationException";
    } catch (const common::ConfigurationException& e) {
        std::string message = e.what();
        EXPECT_TRUE(message.find("nonexistent_type") != std::string::npos);
        // Different implementations may have different error messages
    }
}

// Tests extractor config builder
TEST_F(ExtractorFactoryTest, ExtractorConfigBuilder) {
    // Test config builder for file-based extractors
    auto config = ExtractorConfigBuilder("csv")
        .with_file("test.csv")
        .set("batch_size", size_t(1000))
        .set("max_records", size_t(5000))
        .get_config();

    EXPECT_EQ(std::any_cast<std::string>(config["type"]), std::string("csv"));
    EXPECT_EQ(std::any_cast<std::string>(config["filepath"]), std::string("test.csv"));
    EXPECT_EQ(std::any_cast<size_t>(config["batch_size"]), size_t(1000));
    EXPECT_EQ(std::any_cast<size_t>(config["max_records"]), size_t(5000));
}

// Tests extractor config builder database
TEST_F(ExtractorFactoryTest, ExtractorConfigBuilderDatabase) {
    // Test config builder for database extractors
    auto config = ExtractorConfigBuilder("mysql")
        .with_database("localhost", 3306, "test_db", "user", "pass")
        .with_table("table")
        .get_config();

    EXPECT_EQ(std::any_cast<std::string>(config["type"]), std::string("mysql"));
    EXPECT_EQ(std::any_cast<std::string>(config["host"]), std::string("localhost"));
    EXPECT_EQ(std::any_cast<int>(config["port"]), int(3306));
    EXPECT_EQ(std::any_cast<std::string>(config["database"]), std::string("test_db"));
    EXPECT_EQ(std::any_cast<std::string>(config["username"]), std::string("user"));
    EXPECT_EQ(std::any_cast<std::string>(config["password"]), std::string("pass"));
    EXPECT_EQ(std::any_cast<std::string>(config["table"]), std::string("table"));
}

// Tests get extractor info
TEST_F(ExtractorFactoryTest, GetExtractorInfo) {
    // Get info for all extractors
    auto info_list = get_extractor_info();
    EXPECT_FALSE(info_list.empty());

    // Check that each info entry has required fields
    for (const auto& info : info_list) {
        EXPECT_FALSE(info.type.empty());
        EXPECT_FALSE(info.description.empty());
    }

    // Check for specific expected types
    auto csv_info = std::find_if(info_list.begin(), info_list.end(),
        [](const auto& info) { return info.type == "csv"; });
    EXPECT_NE(csv_info, info_list.end());
}

// Tests get registered types
TEST_F(ExtractorFactoryTest, GetRegisteredTypes) {
    // Clear any existing registrations
    ExtractorFactoryRegistry::clear();

    // Register multiple types
    std::set<std::string> expected_types = {"type_a", "type_b", "type_c"};

    for (const auto& type : expected_types) {
        ExtractorFactoryRegistry::register_type(type,
            [type]() { return std::make_unique<MockExtractor>(type); });
    }

    // Get registered types
    auto registered = ExtractorFactoryRegistry::get_registered_types();

    // Should be sorted
    EXPECT_TRUE(std::is_sorted(registered.begin(), registered.end()));

    // Should contain all expected types
    std::set<std::string> actual_types(registered.begin(), registered.end());
    EXPECT_EQ(actual_types, expected_types);
}

// Tests initialize builtin extractors
TEST_F(ExtractorFactoryTest, InitializeBuiltinExtractors) {
    // Initialize built-in extractors
    initialize_extractors();

    // Print all registered types for debugging
    auto all_types = ExtractorFactoryRegistry::get_registered_types();
    std::cout << "All registered extractor types:" << std::endl;
    for (const auto& type : all_types) {
        std::cout << "  - " << type << std::endl;
    }

    // Verify some expected built-in types are registered
    std::vector<std::string> expected_types = {
        "csv", "multi_csv", "csv_directory", "compressed_csv",
        "json", "jsonl", "json_lines", "streaming_json",
        "mysql", "postgresql", "postgres", "sqlite"
    };

    for (const auto& type : expected_types) {
        if (ExtractorFactoryRegistry::is_type_registered(type)) {
            auto extractor = ExtractorFactoryRegistry::create(type);
            EXPECT_NE(extractor, nullptr);
            EXPECT_EQ(extractor->get_type(), type);
        }
    }
}

// Tests odbc conditional support
TEST_F(ExtractorFactoryTest, OdbcConditionalSupport) {
    // Initialize built-in extractors
    initialize_extractors();

    // Test ODBC support (may not be available on all platforms)
    if (ExtractorFactoryRegistry::is_type_registered("odbc")) {
        auto extractor = ExtractorFactoryRegistry::create("odbc");
        EXPECT_NE(extractor, nullptr);
        EXPECT_EQ(extractor->get_type(), "odbc");
    }
}

// Tests register and create extractor
TEST_F(ExtractorFactoryTest, RegisterAndCreateExtractor) {
    // Register a mock extractor type
    ExtractorFactoryRegistry::register_type("test_mock",
        []() { return std::make_unique<MockExtractor>("test_mock"); });

    // Verify registration
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_mock"));

    // Create extractor
    auto extractor = ExtractorFactoryRegistry::create("test_mock");
    EXPECT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "test_mock");
}

// Tests register duplicate type
TEST_F(ExtractorFactoryTest, RegisterDuplicateType) {
    // Register a type
    ExtractorFactoryRegistry::register_type("duplicate_test",
        []() { return std::make_unique<MockExtractor>("duplicate_test"); });

    // Try to register the same type again - may not throw, some implementations allow overriding
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("duplicate_test"));
}

// Tests thread safe creation
TEST_F(ExtractorFactoryTest, ThreadSafeCreation) {
    // Register a type
    ExtractorFactoryRegistry::register_type("concurrent_type",
        []() { return std::make_unique<MockExtractor>("concurrent_type"); });

    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    // Launch multiple threads to create extractors
    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([&success_count]() {
            try {
                auto extractor = ExtractorFactoryRegistry::create("concurrent_type");
                if (extractor && extractor->get_type() == "concurrent_type") {
                    success_count++;
                }
            } catch (...) {
                // Should not throw
            }
        });
    }

    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }

    // All creations should succeed
    EXPECT_EQ(success_count, 20);
}

// Tests thread safe registration
TEST_F(ExtractorFactoryTest, ThreadSafeRegistration) {
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    // Launch multiple threads to register different types
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([i, &success_count]() {
            std::string type = "thread_type_" + std::to_string(i);
            try {
                ExtractorFactoryRegistry::register_type(type,
                    [type]() { return std::make_unique<MockExtractor>(type); });
                success_count++;
            } catch (...) {
                // Registration might fail if type already exists
            }
        });
    }

    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }

    // All registrations should succeed
    EXPECT_EQ(success_count, 10);

    // Verify all types are registered
    for (int i = 0; i < 10; ++i) {
        std::string type = "thread_type_" + std::to_string(i);
        EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered(type));
    }
}

} // namespace omop::extract::test
