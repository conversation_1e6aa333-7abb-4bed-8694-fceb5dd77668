/**
 * @file table_definitions_test.cpp
 * @brief Unit tests for OMOP CDM table definitions with UK healthcare localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "cdm/table_definitions.h"
#include <algorithm>
#include <chrono>
#include <thread>
#include <vector>
#include <locale>
#include <iomanip>
#include <sstream>

using namespace omop::cdm;

namespace omop::cdm::test {

// Test fixture for UK-localized table definition tests
class UKTableDefinitionTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        table = std::make_unique<TableDefinition>("uk_test_table");
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
    }

    std::unique_ptr<TableDefinition> table;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    
    // Helper to format UK currency
    std::string formatUKCurrency(double amount) {
        std::ostringstream oss;
        oss << uk_currency_symbol_ << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }
};

// Test fixture for UK-localized schema definitions tests
class UKSchemaDefinitionsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        schema = &SchemaDefinitions::instance();
    }
    
    SchemaDefinitions* schema;
};

// Test fixture for UK-localized SQL generator tests
class UKSqlGeneratorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
    }
};

// Tests that composite primary keys generate correct UK SQL syntax with multiple key fields
TEST_F(UKTableDefinitionTest, CompositePrimaryKeyGeneratesUKSQLCorrectly) {
    table->add_field({"tenant_id", "INTEGER", false, true, "", "UK tenant identifier"});
    table->add_field({"user_id", "INTEGER", false, true, "", "UK user identifier"});
    table->add_field({"name", "VARCHAR(50)", true, false, "", "UK patient name"});

    std::string sql = table->generate_create_table_sql("uk_schema");

    EXPECT_TRUE(sql.find("PRIMARY KEY (\"tenant_id\", \"user_id\")") != std::string::npos);
}

// Tests that the table definition constructor properly initializes the UK table name
TEST_F(UKTableDefinitionTest, ConstructorInitializesUKTableNameCorrectly) {
    EXPECT_EQ(table->get_name(), "uk_test_table");
}

// Tests that CREATE TABLE SQL generation properly includes default values for UK fields
// Verifies that default values are properly included in CREATE TABLE statements
TEST_F(UKTableDefinitionTest, CreateTableSQLHandlesUKDefaultValuesCorrectly) {
    table->add_field({"id", "BIGINT", false, true, "", ""});
    table->add_field({"status", "VARCHAR(20)", false, false, "'active'", "UK status field"});

    std::string sql = table->generate_create_table_sql("uk_schema");

    EXPECT_TRUE(sql.find("DEFAULT 'active'") != std::string::npos);
}

// Tests that CREATE TABLE SQL uses correct MySQL dialect syntax for UK database
// Ensures MySQL-specific syntax like backticks is used for UK database objects
TEST_F(UKTableDefinitionTest, CreateTableSQLHandlesUKMySQLDialectCorrectly) {
    table->add_field({"id", "INTEGER", false, true, "", "UK primary key"});
    table->add_field({"description", "TEXT", true, false, "", "UK description"});

    std::string sql = table->generate_create_table_sql("uk_db", DatabaseDialect::MySQL);

    EXPECT_TRUE(sql.find("CREATE TABLE `uk_db`.`uk_test_table`") != std::string::npos);
    EXPECT_TRUE(sql.find("`id` INT NOT NULL") != std::string::npos);
    EXPECT_TRUE(sql.find("`description` TEXT") != std::string::npos);
}

// Tests that CREATE TABLE SQL uses correct PostgreSQL dialect syntax for UK database
// Verifies PostgreSQL-specific syntax with double quotes for identifiers
TEST_F(UKTableDefinitionTest, CreateTableSQLHandlesUKPostgreSQLDialectCorrectly) {
    table->add_field({"id", "BIGINT", false, true, "", "UK primary key"});
    table->add_field({"name", "VARCHAR(50)", true, false, "", "UK name field"});

    std::string sql = table->generate_create_table_sql("uk_schema", DatabaseDialect::PostgreSQL);

    EXPECT_TRUE(sql.find("CREATE TABLE \"uk_schema\".\"uk_test_table\"") != std::string::npos);
    EXPECT_TRUE(sql.find("\"id\" BIGINT NOT NULL") != std::string::npos);
    EXPECT_TRUE(sql.find("\"name\" VARCHAR(50)") != std::string::npos);
    EXPECT_TRUE(sql.find("CONSTRAINT \"pk_uk_test_table\" PRIMARY KEY (\"id\")") != std::string::npos);
}

// Tests that CREATE TABLE SQL uses correct SQL Server dialect syntax for UK database
// Validates SQL Server-specific syntax with square brackets for identifiers
TEST_F(UKTableDefinitionTest, CreateTableSQLHandlesUKSQLServerDialectCorrectly) {
    table->add_field({"id", "BIGINT", false, true, "", ""});
    table->add_field({"data", "TEXT", true, false, "", ""});

    std::string sql = table->generate_create_table_sql("uk_schema", DatabaseDialect::SQLServer);

    EXPECT_TRUE(sql.find("[uk_schema].[uk_test_table]") != std::string::npos);
    EXPECT_TRUE(sql.find("[data] VARCHAR(MAX)") != std::string::npos);
}

// Tests that CREATE TABLE SQL generates valid syntax for UK tables with no fields
// Ensures empty tables still generate valid SQL structure
TEST_F(UKTableDefinitionTest, CreateTableSQLHandlesUKTablesWithNoFields) {
    std::string sql = table->generate_create_table_sql("uk_schema");

    // Should still generate valid UK SQL structure
    EXPECT_TRUE(sql.find("CREATE TABLE") != std::string::npos);
    // Check for empty parentheses (may have newlines)
    EXPECT_TRUE(sql.find("(\n);") != std::string::npos || sql.find("()") != std::string::npos);
}

// Tests that data type mapping preserves parameters for UK decimal and varchar fields
TEST_F(UKTableDefinitionTest, DataTypeMappingHandlesUKParametersCorrectly) {
    table->add_field({"decimal_field", "DECIMAL(10,2)", false, false, "", "UK currency field"});
    table->add_field({"varchar_field", "VARCHAR(255)", false, false, "", "UK text field"});

    std::string sql_postgres = table->generate_create_table_sql("uk_schema", DatabaseDialect::PostgreSQL);
    std::string sql_mysql = table->generate_create_table_sql("uk_schema", DatabaseDialect::MySQL);

    // UK parameters should be preserved
    EXPECT_TRUE(sql_postgres.find("DECIMAL(10,2)") != std::string::npos);
    EXPECT_TRUE(sql_postgres.find("VARCHAR(255)") != std::string::npos);
    EXPECT_TRUE(sql_mysql.find("DECIMAL(10,2)") != std::string::npos);
    EXPECT_TRUE(sql_mysql.find("VARCHAR(255)") != std::string::npos);
}

// Tests that field addition correctly stores UK-specific field metadata
TEST_F(UKTableDefinitionTest, FieldAdditionStoresUKFieldsCorrectly) {
    FieldDefinition field{"nhs_number", "VARCHAR(10)", false, true, "", "UK NHS number"};
    table->add_field(field);

    const auto& fields = table->get_fields();
    EXPECT_EQ(fields.size(), 1);
    EXPECT_EQ(fields[0].name, "nhs_number");
    EXPECT_EQ(fields[0].data_type, "VARCHAR(10)");
    EXPECT_FALSE(fields[0].is_nullable);
    EXPECT_TRUE(fields[0].is_primary_key);
    EXPECT_EQ(fields[0].comment, "UK NHS number");
}

// Tests that foreign key addition correctly stores UK constraint definitions
TEST_F(UKTableDefinitionTest, ForeignKeyAdditionStoresUKConstraintsCorrectly) {
    ForeignKeyDefinition fk{"fk_uk_user", "uk_test_table", "user_id", "uk_users", "id"};
    table->add_foreign_key(fk);

    const auto& foreign_keys = table->get_foreign_keys();
    EXPECT_EQ(foreign_keys.size(), 1);
    EXPECT_EQ(foreign_keys[0].name, "fk_uk_user");
    EXPECT_EQ(foreign_keys[0].column_name, "user_id");
    EXPECT_EQ(foreign_keys[0].referenced_table, "uk_users");
}

// Tests that foreign key SQL generation creates correct ALTER TABLE statements for UK constraints
TEST_F(UKTableDefinitionTest, ForeignKeySQLGenerationHandlesUKConstraintsCorrectly) {
    table->add_foreign_key({"fk_uk_user", "uk_test_table", "user_id", "uk_users", "id"});
    table->add_foreign_key({"fk_uk_category", "uk_test_table", "category_id", "uk_categories", "id"});

    auto sql_statements = table->generate_foreign_key_sql("uk_schema", DatabaseDialect::PostgreSQL);

    EXPECT_EQ(sql_statements.size(), 2);
    EXPECT_TRUE(sql_statements[0].find("ALTER TABLE \"uk_schema\".\"uk_test_table\"") != std::string::npos);
    EXPECT_TRUE(sql_statements[0].find("ADD CONSTRAINT \"fk_uk_user\"") != std::string::npos);
    EXPECT_TRUE(sql_statements[0].find("FOREIGN KEY (\"user_id\")") != std::string::npos);
    EXPECT_TRUE(sql_statements[0].find("REFERENCES \"uk_schema\".\"uk_users\" (\"id\")") != std::string::npos);
}

// Tests that index addition correctly stores UK index definitions with multiple columns
TEST_F(UKTableDefinitionTest, IndexAdditionStoresUKIndexesCorrectly) {
    IndexDefinition index{"idx_uk_nhs", "uk_test_table", {"nhs_number", "postcode"}, false, false};
    table->add_index(index);

    const auto& indexes = table->get_indexes();
    EXPECT_EQ(indexes.size(), 1);
    EXPECT_EQ(indexes[0].name, "idx_uk_nhs");
    EXPECT_EQ(indexes[0].columns.size(), 2);
    EXPECT_EQ(indexes[0].columns[0], "nhs_number");
    EXPECT_EQ(indexes[0].columns[1], "postcode");
}

// Test that index SQL generation handles UK clustered indexes correctly
TEST_F(UKTableDefinitionTest, IndexSQLGenerationHandlesUKClusteredIndexesCorrectly) {
    table->add_index({"idx_uk_clustered", "uk_test_table", {"id"}, false, true});

    auto sql_statements = table->generate_create_index_sql("uk_schema", DatabaseDialect::SQLServer);

    EXPECT_EQ(sql_statements.size(), 1);
    EXPECT_TRUE(sql_statements[0].find("CREATE CLUSTERED INDEX") != std::string::npos);
}

// Test that index SQL generation handles UK indexes correctly
TEST_F(UKTableDefinitionTest, IndexSQLGenerationHandlesUKIndexesCorrectly) {
    table->add_index({"idx_uk_name", "uk_test_table", {"name"}, false, false});
    table->add_index({"idx_uk_unique_email", "uk_test_table", {"email"}, true, false});

    auto sql_statements = table->generate_create_index_sql("uk_schema", DatabaseDialect::PostgreSQL);

    EXPECT_EQ(sql_statements.size(), 2);
    EXPECT_TRUE(sql_statements[0].find("CREATE INDEX \"idx_uk_name\"") != std::string::npos);
    EXPECT_TRUE(sql_statements[1].find("CREATE UNIQUE INDEX \"idx_uk_unique_email\"") != std::string::npos);
}

// Test that multiple UK field addition stores all fields correctly
TEST_F(UKTableDefinitionTest, MultipleUKFieldAdditionStoresAllFieldsCorrectly) {
    table->add_field({"nhs_number", "VARCHAR(10)", false, true, "", "UK NHS number"});
    table->add_field({"name", "VARCHAR(50)", true, false, "", "UK patient name"});
    table->add_field({"created_at", "DATETIME", false, false, "CURRENT_TIMESTAMP", "UK creation time"});

    const auto& fields = table->get_fields();
    EXPECT_EQ(fields.size(), 3);
}

// Test that creation order respects UK dependencies correctly
TEST_F(UKSchemaDefinitionsTest, CreationOrderRespectsUKDependenciesCorrectly) {
    auto creation_order = schema->get_creation_order();

    EXPECT_FALSE(creation_order.empty());

    // UK location should come before care_site and person
    auto location_pos = std::find(creation_order.begin(), creation_order.end(), "location");
    auto care_site_pos = std::find(creation_order.begin(), creation_order.end(), "care_site");
    auto person_pos = std::find(creation_order.begin(), creation_order.end(), "person");

    EXPECT_LT(location_pos, care_site_pos);
    EXPECT_LT(location_pos, person_pos);
}

// Test that drop order is reverse of UK creation order
TEST_F(UKSchemaDefinitionsTest, DropOrderIsReverseOfUKCreationOrder) {
    auto creation_order = schema->get_creation_order();
    auto drop_order = schema->get_drop_order();

    EXPECT_EQ(creation_order.size(), drop_order.size());

    std::reverse(creation_order.begin(), creation_order.end());
    EXPECT_EQ(creation_order, drop_order);
}

// Test that foreign key generation handles UK constraints without hanging
TEST_F(UKSchemaDefinitionsTest, ForeignKeyGenerationHandlesUKConstraintsWithoutHanging) {
    const TableDefinition* person_table = schema->get_table("person");
    ASSERT_NE(person_table, nullptr);

    // This should complete quickly for UK system
    auto start = std::chrono::high_resolution_clock::now();
    auto fk_sql = person_table->generate_foreign_key_sql("uk_cdm", DatabaseDialect::PostgreSQL);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    // Should complete in under 100ms for UK system
    EXPECT_LT(duration.count(), 100);

    // Should generate UK foreign key statements
    EXPECT_FALSE(fk_sql.empty());

    // Should contain UK ALTER TABLE statements
    bool has_alter_table = false;
    for (const auto& sql : fk_sql) {
        if (sql.find("ALTER TABLE") != std::string::npos) {
            has_alter_table = true;
            break;
        }
    }
    EXPECT_TRUE(has_alter_table);

    // Should contain UK FOREIGN KEY references
    bool has_foreign_key = false;
    for (const auto& sql : fk_sql) {
        if (sql.find("FOREIGN KEY") != std::string::npos) {
            has_foreign_key = true;
            break;
        }
    }
    EXPECT_TRUE(has_foreign_key);
}

// Test that invalid UK table returns null pointer
TEST_F(UKSchemaDefinitionsTest, InvalidUKTableReturnsNullPointer) {
    const TableDefinition* invalid_table = schema->get_table("uk_non_existent_table");

    EXPECT_EQ(invalid_table, nullptr);
}

// Test that person table has correct UK fields
TEST_F(UKSchemaDefinitionsTest, PersonTableHasCorrectUKFields) {
    const TableDefinition* person_table = schema->get_table("person");
    ASSERT_NE(person_table, nullptr);

    const auto& fields = person_table->get_fields();

    // Check UK required fields exist
    auto find_field = [&fields](const std::string& name) {
        return std::find_if(fields.begin(), fields.end(),
            [&name](const FieldDefinition& f) { return f.name == name; });
    };

    EXPECT_NE(find_field("person_id"), fields.end());
    EXPECT_NE(find_field("gender_concept_id"), fields.end());
    EXPECT_NE(find_field("year_of_birth"), fields.end());
    EXPECT_NE(find_field("race_concept_id"), fields.end());
    EXPECT_NE(find_field("ethnicity_concept_id"), fields.end());
}

// Test that schema SQL generation handles UK constraints correctly
TEST_F(UKSchemaDefinitionsTest, SchemaSQLGenerationHandlesUKConstraintsCorrectly) {
    auto sql_statements = schema->generate_schema_sql("uk_cdm", DatabaseDialect::PostgreSQL, true, false);

    EXPECT_FALSE(sql_statements.empty());

    // Should contain UK CREATE SCHEMA
    EXPECT_TRUE(sql_statements[0].find("CREATE SCHEMA IF NOT EXISTS \"uk_cdm\"") != std::string::npos);

    // Should contain UK CREATE TABLE statements
    bool has_create_table = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("CREATE TABLE") != std::string::npos) {
            has_create_table = true;
            break;
        }
    }
    EXPECT_TRUE(has_create_table);

    // Should contain UK CREATE INDEX statements
    bool has_create_index = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("CREATE INDEX") != std::string::npos) {
            has_create_index = true;
            break;
        }
    }
    EXPECT_TRUE(has_create_index);

    // Should not contain UK ALTER TABLE ADD CONSTRAINT statements for foreign keys
    bool has_foreign_keys = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("ALTER TABLE") != std::string::npos && sql.find("FOREIGN KEY") != std::string::npos) {
            has_foreign_keys = true;
            break;
        }
    }
    EXPECT_FALSE(has_foreign_keys);
}

// Test that schema SQL generation handles UK indexes and constraints correctly
TEST_F(UKSchemaDefinitionsTest, SchemaSQLGenerationHandlesUKIndexesAndConstraintsCorrectly) {
    auto sql_statements = schema->generate_schema_sql("uk_cdm", DatabaseDialect::PostgreSQL, true, true);

    EXPECT_FALSE(sql_statements.empty());

    // Should contain UK CREATE SCHEMA
    EXPECT_TRUE(sql_statements[0].find("CREATE SCHEMA IF NOT EXISTS \"uk_cdm\"") != std::string::npos);

    // Should contain UK CREATE TABLE statements
    bool has_create_table = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("CREATE TABLE") != std::string::npos) {
            has_create_table = true;
            break;
        }
    }
    EXPECT_TRUE(has_create_table);

    // Should contain UK CREATE INDEX statements
    bool has_create_index = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("CREATE INDEX") != std::string::npos) {
            has_create_index = true;
            break;
        }
    }
    EXPECT_TRUE(has_create_index);

    // Should contain UK ALTER TABLE ADD CONSTRAINT statements for foreign keys
    bool has_foreign_keys = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("ALTER TABLE") != std::string::npos && sql.find("FOREIGN KEY") != std::string::npos) {
            has_foreign_keys = true;
            break;
        }
    }
    EXPECT_TRUE(has_foreign_keys);

    // Should have substantial number of UK statements (schema + tables + indexes + constraints)
    // At least: 1 schema + 15 tables + some indexes + some foreign keys = 50+ statements
    EXPECT_GE(sql_statements.size(), 50);
}

// Test that schema SQL generation handles UK tables without indexes or constraints
TEST_F(UKSchemaDefinitionsTest, SchemaSQLGenerationHandlesUKTablesWithoutIndexesOrConstraints) {
    auto sql_statements = schema->generate_schema_sql("uk_cdm", DatabaseDialect::PostgreSQL, false, false);

    EXPECT_FALSE(sql_statements.empty());

    // Should contain UK CREATE SCHEMA
    EXPECT_TRUE(sql_statements[0].find("CREATE SCHEMA IF NOT EXISTS \"uk_cdm\"") != std::string::npos);

    // Should contain UK CREATE TABLE statements
    bool has_create_table = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("CREATE TABLE") != std::string::npos) {
            has_create_table = true;
            break;
        }
    }
    EXPECT_TRUE(has_create_table);

    // Should not contain UK CREATE INDEX statements
    bool has_create_index = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("CREATE INDEX") != std::string::npos) {
            has_create_index = true;
            break;
        }
    }
    EXPECT_FALSE(has_create_index);

    // Should not contain UK ALTER TABLE ADD CONSTRAINT statements (except primary keys)
    bool has_foreign_keys = false;
    for (const auto& sql : sql_statements) {
        if (sql.find("ALTER TABLE") != std::string::npos && sql.find("FOREIGN KEY") != std::string::npos) {
            has_foreign_keys = true;
            break;
        }
    }
    EXPECT_FALSE(has_foreign_keys);
}

// Test that singleton is UK thread safe
TEST_F(UKSchemaDefinitionsTest, SingletonIsUKThreadSafe) {
    std::vector<SchemaDefinitions*> instances;
    std::mutex mutex;

    auto get_instance = [&instances, &mutex]() {
        auto& inst = SchemaDefinitions::instance();
        std::lock_guard<std::mutex> lock(mutex);
        instances.push_back(&inst);
    };

    std::vector<std::thread> threads;
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back(get_instance);
    }

    for (auto& t : threads) {
        t.join();
    }

    // All UK instances should be the same
    for (size_t i = 1; i < instances.size(); ++i) {
        EXPECT_EQ(instances[0], instances[i]);
    }
}

// Test that singleton returns same UK instance
TEST_F(UKSchemaDefinitionsTest, SingletonReturnsSameUKInstance) {
    auto& instance1 = SchemaDefinitions::instance();
    auto& instance2 = SchemaDefinitions::instance();

    EXPECT_EQ(&instance1, &instance2);
}

// Test that table names include all UK supported tables
TEST_F(UKSchemaDefinitionsTest, TableNamesIncludeAllUKSupportedTables) {
    auto table_names = schema->get_table_names();

    EXPECT_FALSE(table_names.empty());

    // Check for UK expected tables
    EXPECT_TRUE(std::find(table_names.begin(), table_names.end(), "person") != table_names.end());
    EXPECT_TRUE(std::find(table_names.begin(), table_names.end(), "location") != table_names.end());
    EXPECT_TRUE(std::find(table_names.begin(), table_names.end(), "concept") != table_names.end());
    EXPECT_TRUE(std::find(table_names.begin(), table_names.end(), "observation_period") != table_names.end());
    EXPECT_TRUE(std::find(table_names.begin(), table_names.end(), "visit_occurrence") != table_names.end());

    // Should have all 15 UK tables
    EXPECT_EQ(table_names.size(), 15);
}

// Test that valid UK table returns correct definition
TEST_F(UKSchemaDefinitionsTest, ValidUKTableReturnsCorrectDefinition) {
    const TableDefinition* person_table = schema->get_table("person");

    ASSERT_NE(person_table, nullptr);
    EXPECT_EQ(person_table->get_name(), "person");
}

// Test that auto increment syntax handles UK database dialects correctly
TEST_F(UKSqlGeneratorTest, AutoIncrementSyntaxHandlesUKDatabaseDialectsCorrectly) {
    EXPECT_EQ(SqlGenerator::get_auto_increment_syntax(DatabaseDialect::PostgreSQL),
              "GENERATED ALWAYS AS IDENTITY");
    EXPECT_EQ(SqlGenerator::get_auto_increment_syntax(DatabaseDialect::MySQL),
              "AUTO_INCREMENT");
    EXPECT_EQ(SqlGenerator::get_auto_increment_syntax(DatabaseDialect::SQLServer),
              "IDENTITY(1,1)");
    EXPECT_EQ(SqlGenerator::get_auto_increment_syntax(DatabaseDialect::SQLite),
              "AUTOINCREMENT");
    EXPECT_EQ(SqlGenerator::get_auto_increment_syntax(DatabaseDialect::Oracle),
              "GENERATED BY DEFAULT AS IDENTITY");
}

// Test that current timestamp function handles UK database dialects correctly
TEST_F(UKSqlGeneratorTest, CurrentTimestampFunctionHandlesUKDatabaseDialectsCorrectly) {
    EXPECT_EQ(SqlGenerator::get_current_timestamp_function(DatabaseDialect::PostgreSQL),
              "CURRENT_TIMESTAMP");
    EXPECT_EQ(SqlGenerator::get_current_timestamp_function(DatabaseDialect::MySQL),
              "NOW()");
    EXPECT_EQ(SqlGenerator::get_current_timestamp_function(DatabaseDialect::SQLServer),
              "GETDATE()");
    EXPECT_EQ(SqlGenerator::get_current_timestamp_function(DatabaseDialect::SQLite),
              "DATETIME('now')");
    EXPECT_EQ(SqlGenerator::get_current_timestamp_function(DatabaseDialect::Oracle),
              "SYSDATE");
}

// Test that datetime formatting handles UK database dialects correctly
TEST_F(UKSqlGeneratorTest, DatetimeFormattingHandlesUKDatabaseDialectsCorrectly) {
    auto test_time = std::chrono::system_clock::now();

    // UK PostgreSQL format
    std::string pg_result = SqlGenerator::format_datetime(test_time, DatabaseDialect::PostgreSQL);
    EXPECT_TRUE(pg_result.find("'") == 0);
    EXPECT_TRUE(pg_result.find("'") != std::string::npos);

    // UK Oracle format should use TO_DATE
    std::string oracle_result = SqlGenerator::format_datetime(test_time, DatabaseDialect::Oracle);
    EXPECT_TRUE(oracle_result.find("TO_DATE(") == 0);
    EXPECT_TRUE(oracle_result.find("YYYY-MM-DD HH24:MI:SS") != std::string::npos);
}

// Test that quote identifier handles UK database dialects correctly
TEST_F(UKSqlGeneratorTest, QuoteIdentifierHandlesUKDatabaseDialectsCorrectly) {
    EXPECT_EQ(SqlGenerator::quote_identifier("uk_table_name", DatabaseDialect::PostgreSQL), "\"uk_table_name\"");
    EXPECT_EQ(SqlGenerator::quote_identifier("uk_table_name", DatabaseDialect::MySQL), "`uk_table_name`");
    EXPECT_EQ(SqlGenerator::quote_identifier("uk_table_name", DatabaseDialect::SQLServer), "[uk_table_name]");
    EXPECT_EQ(SqlGenerator::quote_identifier("uk_table_name", DatabaseDialect::SQLite), "\"uk_table_name\"");
    EXPECT_EQ(SqlGenerator::quote_identifier("uk_table_name", DatabaseDialect::Oracle), "\"uk_table_name\"");
}

// Test that quote value escapes UK backslashes for MySQL correctly
TEST_F(UKSqlGeneratorTest, QuoteValueEscapesUKBackslashesForMySQLCorrectly) {
    EXPECT_EQ(SqlGenerator::quote_value("C:\\NHS\\path\\to\\file", DatabaseDialect::MySQL),
              "'C:\\\\NHS\\\\path\\\\to\\\\file'");
    EXPECT_EQ(SqlGenerator::quote_value("C:\\NHS\\path\\to\\file", DatabaseDialect::PostgreSQL),
              "'C:\\NHS\\path\\to\\file'");
}

// Test that quote value escapes UK single quotes correctly
TEST_F(UKSqlGeneratorTest, QuoteValueEscapesUKSingleQuotesCorrectly) {
    EXPECT_EQ(SqlGenerator::quote_value("O'Brien", DatabaseDialect::PostgreSQL), "'O''Brien'");
    EXPECT_EQ(SqlGenerator::quote_value("It's a UK test", DatabaseDialect::MySQL), "'It''s a UK test'");
    EXPECT_EQ(SqlGenerator::quote_value("Quote''UK''Test", DatabaseDialect::SQLServer), "'Quote''''UK''''Test'");
}

// Test that table name formatting handles UK schemas correctly
TEST_F(UKSqlGeneratorTest, TableNameFormattingHandlesUKSchemasCorrectly) {
    EXPECT_EQ(SqlGenerator::format_table_name("uk_schema", "users", DatabaseDialect::PostgreSQL),
              "\"uk_schema\".\"users\"");
    EXPECT_EQ(SqlGenerator::format_table_name("uk_db", "users", DatabaseDialect::MySQL),
              "`uk_db`.`users`");
    EXPECT_EQ(SqlGenerator::format_table_name("uk_schema", "users", DatabaseDialect::SQLServer),
              "[uk_schema].[users]");
}

// Test that table name formatting handles UK tables without schema
TEST_F(UKSqlGeneratorTest, TableNameFormattingHandlesUKTablesWithoutSchema) {
    EXPECT_EQ(SqlGenerator::format_table_name("", "uk_users", DatabaseDialect::PostgreSQL),
              "\"uk_users\"");
    EXPECT_EQ(SqlGenerator::format_table_name("", "uk_users", DatabaseDialect::MySQL),
              "`uk_users`");
}

// Test that field definition stores UK metadata correctly
TEST_F(UKTableDefinitionTest, FieldDefinitionStoresUKMetadataCorrectly) {
    FieldDefinition field;
    field.name = "nhs_number";
    field.data_type = "VARCHAR(10)";
    field.is_nullable = false;
    field.is_primary_key = true;
    field.default_value = "";
    field.comment = "UK NHS patient identifier";
    
    EXPECT_EQ(field.name, "nhs_number");
    EXPECT_EQ(field.data_type, "VARCHAR(10)");
    EXPECT_FALSE(field.is_nullable);
    EXPECT_TRUE(field.is_primary_key);
    EXPECT_EQ(field.comment, "UK NHS patient identifier");
}

// Test that index definition stores UK index properties correctly
TEST_F(UKTableDefinitionTest, IndexDefinitionStoresUKIndexPropertiesCorrectly) {
    IndexDefinition index;
    index.name = "idx_uk_patient_nhs";
    index.table_name = "uk_patients";
    index.columns = {"nhs_number", "postcode"};
    index.is_unique = true;
    index.is_clustered = false;
    
    EXPECT_EQ(index.name, "idx_uk_patient_nhs");
    EXPECT_EQ(index.table_name, "uk_patients");
    EXPECT_EQ(index.columns.size(), 2);
    EXPECT_EQ(index.columns[0], "nhs_number");
    EXPECT_EQ(index.columns[1], "postcode");
    EXPECT_TRUE(index.is_unique);
    EXPECT_FALSE(index.is_clustered);
}

// Test that foreign key definition stores UK relationships correctly
TEST_F(UKTableDefinitionTest, ForeignKeyDefinitionStoresUKRelationshipsCorrectly) {
    ForeignKeyDefinition fk;
    fk.name = "fk_uk_patient_gp";
    fk.table_name = "uk_patients";
    fk.column_name = "gp_practice_id";
    fk.referenced_table = "uk_gp_practices";
    fk.referenced_column = "practice_id";
    
    EXPECT_EQ(fk.name, "fk_uk_patient_gp");
    EXPECT_EQ(fk.table_name, "uk_patients");
    EXPECT_EQ(fk.column_name, "gp_practice_id");
    EXPECT_EQ(fk.referenced_table, "uk_gp_practices");
    EXPECT_EQ(fk.referenced_column, "practice_id");
}

// Test that database dialect enum handles UK database types correctly
TEST_F(UKSqlGeneratorTest, DatabaseDialectEnumHandlesUKDatabaseTypesCorrectly) {
    // Test that all UK supported database dialects are available
    DatabaseDialect postgresql = DatabaseDialect::PostgreSQL;
    DatabaseDialect mysql = DatabaseDialect::MySQL;
    DatabaseDialect sqlserver = DatabaseDialect::SQLServer;
    DatabaseDialect sqlite = DatabaseDialect::SQLite;
    DatabaseDialect oracle = DatabaseDialect::Oracle;
    
    // Verify they have distinct values (compilation test)
    EXPECT_NE(static_cast<int>(postgresql), static_cast<int>(mysql));
    EXPECT_NE(static_cast<int>(mysql), static_cast<int>(sqlserver));
    EXPECT_NE(static_cast<int>(sqlserver), static_cast<int>(sqlite));
    EXPECT_NE(static_cast<int>(sqlite), static_cast<int>(oracle));
}

// Test that schema SQL generation handles UK empty schema names
TEST_F(UKSchemaDefinitionsTest, SchemaSQLGenerationHandlesUKEmptySchemaNames) {
    auto sql_statements = schema->generate_schema_sql("", DatabaseDialect::PostgreSQL, false, false);
    
    EXPECT_FALSE(sql_statements.empty());
    
    // Should still generate CREATE SCHEMA with quoted empty name
    EXPECT_TRUE(sql_statements[0].find("CREATE SCHEMA IF NOT EXISTS \"\"") != std::string::npos);
}

// Test that table definition handles UK field access correctly
TEST_F(UKTableDefinitionTest, TableDefinitionHandlesUKFieldAccessCorrectly) {
    table->add_field({"id", "BIGINT", false, true, "", "UK identifier"});
    table->add_field({"name", "VARCHAR(50)", true, false, "", "UK name"});
    
    const auto& fields = table->get_fields();
    EXPECT_EQ(fields.size(), 2);
    
    // Test field ordering is preserved
    EXPECT_EQ(fields[0].name, "id");
    EXPECT_EQ(fields[1].name, "name");
}

// Test that table definition handles UK index access correctly
TEST_F(UKTableDefinitionTest, TableDefinitionHandlesUKIndexAccessCorrectly) {
    table->add_index({"idx_uk_name", "uk_test_table", {"name"}, false, false});
    table->add_index({"idx_uk_unique_id", "uk_test_table", {"id"}, true, false});
    
    const auto& indexes = table->get_indexes();
    EXPECT_EQ(indexes.size(), 2);
    
    // Test index ordering is preserved
    EXPECT_EQ(indexes[0].name, "idx_uk_name");
    EXPECT_EQ(indexes[1].name, "idx_uk_unique_id");
}

// Test that table definition handles UK foreign key access correctly
TEST_F(UKTableDefinitionTest, TableDefinitionHandlesUKForeignKeyAccessCorrectly) {
    table->add_foreign_key({"fk_uk_user", "uk_test_table", "user_id", "uk_users", "id"});
    table->add_foreign_key({"fk_uk_category", "uk_test_table", "category_id", "uk_categories", "id"});
    
    const auto& foreign_keys = table->get_foreign_keys();
    EXPECT_EQ(foreign_keys.size(), 2);
    
    // Test foreign key ordering is preserved
    EXPECT_EQ(foreign_keys[0].name, "fk_uk_user");
    EXPECT_EQ(foreign_keys[1].name, "fk_uk_category");
}

// Test comprehensive UK schema generation with all database dialects
TEST_F(UKSchemaDefinitionsTest, ComprehensiveUKSchemaGenerationWithAllDatabaseDialects) {
    std::vector<DatabaseDialect> dialects = {
        DatabaseDialect::PostgreSQL,
        DatabaseDialect::MySQL,
        DatabaseDialect::SQLServer,
        DatabaseDialect::SQLite,
        DatabaseDialect::Oracle
    };
    
    for (auto dialect : dialects) {
        // Test full schema generation for each UK database dialect
        auto sql_statements = schema->generate_schema_sql("uk_test_schema", dialect, true, true);
        EXPECT_FALSE(sql_statements.empty()) << "No SQL statements generated for dialect";
        
        // Verify schema creation statement exists and is properly formatted
        bool has_schema_creation = false;
        for (const auto& sql : sql_statements) {
            if (sql.find("CREATE SCHEMA") != std::string::npos) {
                has_schema_creation = true;
                EXPECT_TRUE(sql.find("uk_test_schema") != std::string::npos) << "Schema name not found in creation statement";
                break;
            }
        }
        EXPECT_TRUE(has_schema_creation) << "Schema creation statement missing";
        
        // Verify all expected UK table creation statements exist
        std::vector<std::string> expected_tables = {
            "person", "observation_period", "visit_occurrence", "condition_occurrence",
            "drug_exposure", "procedure_occurrence", "measurement", "observation",
            "death", "note", "concept", "location", "care_site", "provider", "visit_detail"
        };
        
        for (const auto& table_name : expected_tables) {
            bool found_table = false;
            for (const auto& sql : sql_statements) {
                if (sql.find("CREATE TABLE") != std::string::npos && 
                    sql.find(table_name) != std::string::npos) {
                    found_table = true;
                    break;
                }
            }
            EXPECT_TRUE(found_table) << "Table creation missing: " + table_name;
        }
    }
}

// Test comprehensive UK data type mapping for all database dialects
TEST_F(UKSqlGeneratorTest, ComprehensiveUKDataTypeMappingForAllDatabaseDialects) {
    // Test all UK field types with all dialects
    std::vector<DatabaseDialect> dialects = {
        DatabaseDialect::PostgreSQL,
        DatabaseDialect::MySQL,
        DatabaseDialect::SQLServer,
        DatabaseDialect::SQLite,
        DatabaseDialect::Oracle
    };
    
    std::vector<std::string> uk_field_types = {
        "BIGINT", "INTEGER", "VARCHAR(50)", "TEXT", "DATE", "DATETIME", "FLOAT", "DECIMAL(10,2)"
    };
    
    for (auto dialect : dialects) {
        for (const auto& field_type : uk_field_types) {
            // Create a test table with this field type
            auto test_table = std::make_unique<TableDefinition>("uk_test_type_table");
            test_table->add_field({
                "test_field", 
                field_type, 
                false, 
                false, 
                "", 
                "UK test field for " + field_type
            });
            
            // Generate SQL and verify it doesn't throw
            EXPECT_NO_THROW({
                std::string sql = test_table->generate_create_table_sql("uk_schema", dialect);
                EXPECT_FALSE(sql.empty()) << "Empty SQL for type: " + field_type + " dialect: " + std::to_string(static_cast<int>(dialect));
                EXPECT_TRUE(sql.find("CREATE TABLE") != std::string::npos) << "Missing CREATE TABLE for type: " + field_type;
            }) << "SQL generation failed for type: " + field_type + " with dialect: " + std::to_string(static_cast<int>(dialect));
        }
    }
}

// Test comprehensive UK error handling and edge cases
TEST_F(UKTableDefinitionTest, ComprehensiveUKErrorHandlingAndEdgeCases) {
    // Test empty table name construction
    EXPECT_NO_THROW({
        auto empty_table = std::make_unique<TableDefinition>("");
        EXPECT_EQ(empty_table->get_name(), "");
    });
    
    // Test table with special characters in UK context
    auto special_table = std::make_unique<TableDefinition>("uk_table_with_special-chars_123");
    special_table->add_field({"nhs_number", "VARCHAR(10)", false, true, "", "UK NHS identifier"});
    
    std::string sql = special_table->generate_create_table_sql("uk_schema");
    EXPECT_TRUE(sql.find("uk_table_with_special-chars_123") != std::string::npos);
    
    // Test very long UK field names
    std::string long_uk_field_name(100, 'a');
    long_uk_field_name += "_uk_field";
    special_table->add_field({long_uk_field_name, "TEXT", true, false, "", "Very long UK field name"});
    
    EXPECT_NO_THROW({
        sql = special_table->generate_create_table_sql("uk_schema");
        EXPECT_TRUE(sql.find(long_uk_field_name) != std::string::npos);
    });
    
    // Test UK field with complex default values
    special_table->add_field({
        "uk_timestamp", 
        "DATETIME", 
        false, 
        false, 
        "CURRENT_TIMESTAMP", 
        "UK timestamp with default"
    });
    
    sql = special_table->generate_create_table_sql("uk_schema");
    EXPECT_TRUE(sql.find("DEFAULT CURRENT_TIMESTAMP") != std::string::npos);
    
    // Test multiple UK primary key constraints
    auto pk_table = std::make_unique<TableDefinition>("uk_composite_pk_table");
    pk_table->add_field({"uk_tenant_id", "INTEGER", false, true, "", "UK tenant"});
    pk_table->add_field({"uk_user_id", "INTEGER", false, true, "", "UK user"});
    pk_table->add_field({"uk_record_id", "INTEGER", false, true, "", "UK record"});
    
    sql = pk_table->generate_create_table_sql("uk_schema");
    EXPECT_TRUE(sql.find("PRIMARY KEY") != std::string::npos);
    EXPECT_TRUE(sql.find("uk_tenant_id") != std::string::npos);
    EXPECT_TRUE(sql.find("uk_user_id") != std::string::npos);
    EXPECT_TRUE(sql.find("uk_record_id") != std::string::npos);
}

// Test comprehensive UK foreign key relationship validation
TEST_F(UKTableDefinitionTest, ComprehensiveUKForeignKeyRelationshipValidation) {
    // Create a complex UK table relationship scenario
    auto patient_table = std::make_unique<TableDefinition>("uk_patients");
    patient_table->add_field({"patient_id", "BIGINT", false, true, "", "UK patient identifier"});
    patient_table->add_field({"nhs_number", "VARCHAR(10)", false, false, "", "UK NHS number"});
    patient_table->add_field({"gp_practice_id", "INTEGER", true, false, "", "UK GP practice"});
    patient_table->add_field({"postcode", "VARCHAR(8)", true, false, "", "UK postcode"});
    
    // Add UK-specific foreign key relationships
    patient_table->add_foreign_key({
        "fk_uk_patient_gp",
        "uk_patients",
        "gp_practice_id",
        "uk_gp_practices",
        "practice_id"
    });
    
    patient_table->add_foreign_key({
        "fk_uk_patient_postcode",
        "uk_patients",
        "postcode",
        "uk_postcodes",
        "postcode"
    });
    
    // Generate foreign key SQL
    auto fk_statements = patient_table->generate_foreign_key_sql("uk_health_schema");
    EXPECT_EQ(fk_statements.size(), 2);
    
    // Verify each UK foreign key statement
    for (const auto& fk_sql : fk_statements) {
        EXPECT_TRUE(fk_sql.find("ALTER TABLE") != std::string::npos);
        EXPECT_TRUE(fk_sql.find("uk_health_schema") != std::string::npos);
        EXPECT_TRUE(fk_sql.find("FOREIGN KEY") != std::string::npos);
        EXPECT_TRUE(fk_sql.find("REFERENCES") != std::string::npos);
    }
    
    // Verify specific UK foreign key references
    bool found_gp_fk = false, found_postcode_fk = false;
    for (const auto& fk_sql : fk_statements) {
        if (fk_sql.find("fk_uk_patient_gp") != std::string::npos) {
            found_gp_fk = true;
            EXPECT_TRUE(fk_sql.find("uk_gp_practices") != std::string::npos);
        }
        if (fk_sql.find("fk_uk_patient_postcode") != std::string::npos) {
            found_postcode_fk = true;
            EXPECT_TRUE(fk_sql.find("uk_postcodes") != std::string::npos);
        }
    }
    EXPECT_TRUE(found_gp_fk);
    EXPECT_TRUE(found_postcode_fk);
}

} // namespace omop::cdm::test