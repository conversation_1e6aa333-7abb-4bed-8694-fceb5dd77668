/**
 * @file test_death_integration.cpp
 * @brief Integration tests for Death table with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <memory>
#include <sstream>
#include <regex>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

class DeathIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up UK locale for date formatting
        std::setlocale(LC_ALL, "en_GB.UTF-8");
    }

    // Helper to create UK date from DD/MM/YYYY string
    system_clock::time_point parse_uk_date(const std::string& date_str) {
        std::regex date_regex(R"((\d{2})/(\d{2})/(\d{4}))");
        std::smatch matches;
        
        if (std::regex_match(date_str, matches, date_regex)) {
            int day = std::stoi(matches[1]);
            int month = std::stoi(matches[2]);
            int year = std::stoi(matches[3]);
            
            std::tm tm = {};
            tm.tm_year = year - 1900;
            tm.tm_mon = month - 1;
            tm.tm_mday = day;
            tm.tm_hour = 12; // Noon UK time
            
            return system_clock::from_time_t(std::mktime(&tm));
        }
        
        return system_clock::time_point{};
    }
};

// Test basic Death record creation and field access
TEST_F(DeathIntegrationTest, CreateDeathRecordWithRequiredFields) {
    Death death;
    death.person_id = 12345;
    death.death_date = parse_uk_date("15/03/2023");
    
    EXPECT_EQ(death.person_id, 12345);
    EXPECT_EQ(death.table_name(), "death");
    EXPECT_EQ(death.schema_name(), "cdm");
}

// Test Death record with all optional fields populated
TEST_F(DeathIntegrationTest, CreateDeathRecordWithAllFields) {
    Death death;
    death.person_id = 67890;
    death.death_date = parse_uk_date("01/07/2024");
    death.death_datetime = parse_uk_date("01/07/2024");
    death.death_type_concept_id = 38003569; // UK death certificate
    death.cause_concept_id = 4306655; // COVID-19
    death.cause_source_value = "U07.1";
    death.cause_source_concept_id = 45581485;
    
    EXPECT_TRUE(death.death_datetime.has_value());
    EXPECT_TRUE(death.death_type_concept_id.has_value());
    EXPECT_TRUE(death.cause_concept_id.has_value());
    EXPECT_EQ(death.cause_source_value.value(), "U07.1");
}

// Test validation with valid Death record
TEST_F(DeathIntegrationTest, ValidateValidDeathRecord) {
    Death death;
    death.person_id = 99999;
    death.death_date = parse_uk_date("25/12/2022");
    
    EXPECT_TRUE(death.validate());
    auto errors = death.validation_errors();
    EXPECT_TRUE(errors.empty());
}

// Test validation with invalid person_id
TEST_F(DeathIntegrationTest, ValidateInvalidPersonId) {
    Death death;
    death.person_id = 0; // Invalid
    death.death_date = parse_uk_date("10/10/2023");
    
    EXPECT_FALSE(death.validate());
    auto errors = death.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("person_id") != std::string::npos; }));
}

// Test validation with future death date
TEST_F(DeathIntegrationTest, ValidateFutureDeathDate) {
    Death death;
    death.person_id = 11111;
    
    // Set death date to tomorrow
    auto tomorrow = system_clock::now() + hours(24);
    death.death_date = tomorrow;
    
    EXPECT_FALSE(death.validate());
    auto errors = death.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("future") != std::string::npos; }));
}

// Test SQL generation with proper escaping
TEST_F(DeathIntegrationTest, GenerateInsertSqlWithEscaping) {
    Death death;
    death.person_id = 22222;
    death.death_date = parse_uk_date("31/01/2024");
    death.cause_source_value = "O'Brien's syndrome"; // Test apostrophe escaping
    
    std::string sql = death.to_insert_sql(true);
    
    EXPECT_TRUE(sql.find("INSERT INTO cdm.death") != std::string::npos);
    EXPECT_TRUE(sql.find("22222") != std::string::npos);
    EXPECT_TRUE(sql.find("O''Brien''s syndrome") != std::string::npos); // Properly escaped
}

// Test SQL generation without escaping
TEST_F(DeathIntegrationTest, GenerateInsertSqlWithoutEscaping) {
    Death death;
    death.person_id = 33333;
    death.death_date = parse_uk_date("28/02/2023");
    death.cause_source_value = "Heart failure";
    
    std::string sql = death.to_insert_sql(false);
    
    EXPECT_TRUE(sql.find("'Heart failure'") != std::string::npos);
    EXPECT_TRUE(sql.find("''Heart failure''") == std::string::npos); // Not double escaped
}

// Test field names retrieval
TEST_F(DeathIntegrationTest, GetFieldNames) {
    Death death;
    auto field_names = death.field_names();
    
    EXPECT_EQ(field_names.size(), 7);
    EXPECT_EQ(field_names[0], "person_id");
    EXPECT_EQ(field_names[1], "death_date");
    EXPECT_EQ(field_names[2], "death_datetime");
    EXPECT_EQ(field_names[3], "death_type_concept_id");
    EXPECT_EQ(field_names[4], "cause_concept_id");
    EXPECT_EQ(field_names[5], "cause_source_value");
    EXPECT_EQ(field_names[6], "cause_source_concept_id");
}

// Test field values retrieval
TEST_F(DeathIntegrationTest, GetFieldValues) {
    Death death;
    death.person_id = 44444;
    death.death_date = parse_uk_date("05/05/2023");
    death.cause_concept_id = 4306655;
    
    auto field_values = death.field_values();
    
    EXPECT_EQ(field_values.size(), 7);
    EXPECT_EQ(std::any_cast<int64_t>(field_values[0]), 44444);
    EXPECT_TRUE(std::any_cast<int32_t>(field_values[4]) == 4306655);
}

// Test field visitor pattern
TEST_F(DeathIntegrationTest, FieldVisitorPattern) {
    class TestVisitor : public FieldVisitor {
    public:
        std::vector<std::pair<std::string, std::any>> visited_fields;
        
        void visit(const std::string& name, const std::any& value) override {
            visited_fields.push_back({name, value});
        }
    };
    
    Death death;
    death.person_id = 55555;
    death.death_date = parse_uk_date("20/08/2023");
    death.cause_source_value = "Pneumonia";
    
    TestVisitor visitor;
    death.visit_fields(visitor);
    
    EXPECT_EQ(visitor.visited_fields.size(), 7);
    EXPECT_EQ(visitor.visited_fields[0].first, "person_id");
    EXPECT_EQ(std::any_cast<int64_t>(visitor.visited_fields[0].second), 55555);
} 