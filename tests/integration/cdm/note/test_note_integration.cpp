/**
 * @file test_note_integration.cpp
 * @brief Integration tests for Note table with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <memory>
#include <sstream>
#include <regex>
#include <iomanip>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

class NoteIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up UK locale for date formatting
        std::setlocale(LC_ALL, "en_GB.UTF-8");
    }

    // Helper to create UK date from DD/MM/YYYY string
    system_clock::time_point parse_uk_date(const std::string& date_str) {
        std::regex date_regex(R"((\d{2})/(\d{2})/(\d{4}))");
        std::smatch matches;
        
        if (std::regex_match(date_str, matches, date_regex)) {
            int day = std::stoi(matches[1]);
            int month = std::stoi(matches[2]);
            int year = std::stoi(matches[3]);
            
            std::tm tm = {};
            tm.tm_year = year - 1900;
            tm.tm_mon = month - 1;
            tm.tm_mday = day;
            tm.tm_hour = 10; // 10 AM UK time (typical consultation time)
            
            return system_clock::from_time_t(std::mktime(&tm));
        }
        
        return system_clock::time_point{};
    }
    
    // Helper to create UK datetime from DD/MM/YYYY HH:MM string
    system_clock::time_point parse_uk_datetime(const std::string& datetime_str) {
        std::regex datetime_regex(R"((\d{2})/(\d{2})/(\d{4}) (\d{2}):(\d{2}))");
        std::smatch matches;
        
        if (std::regex_match(datetime_str, matches, datetime_regex)) {
            int day = std::stoi(matches[1]);
            int month = std::stoi(matches[2]);
            int year = std::stoi(matches[3]);
            int hour = std::stoi(matches[4]);
            int minute = std::stoi(matches[5]);
            
            std::tm tm = {};
            tm.tm_year = year - 1900;
            tm.tm_mon = month - 1;
            tm.tm_mday = day;
            tm.tm_hour = hour;
            tm.tm_min = minute;
            
            return system_clock::from_time_t(std::mktime(&tm));
        }
        
        return system_clock::time_point{};
    }
    
    // Helper to generate UK medical note template
    std::string generate_uk_medical_note(const std::string& patient_name, 
                                       const std::string& nhs_number,
                                       const std::string& date) {
        std::stringstream ss;
        ss << "NHS Number: " << nhs_number << "\n";
        ss << "Patient: " << patient_name << "\n";
        ss << "Date: " << date << "\n";
        ss << "Time: 10:30 GMT\n";
        ss << "Location: GP Surgery, Bristol\n\n";
        ss << "Presenting Complaint:\n";
        ss << "Patient presents with persistent cough for 2 weeks.\n\n";
        ss << "History of Presenting Complaint:\n";
        ss << "Dry cough, worse at night. No haemoptysis. No fever.\n";
        ss << "No significant weight loss. Non-smoker.\n\n";
        ss << "Examination:\n";
        ss << "Temperature: 36.8°C\n";
        ss << "BP: 125/80 mmHg\n";
        ss << "Chest: Clear on auscultation\n\n";
        ss << "Plan:\n";
        ss << "1. Chest X-ray arranged\n";
        ss << "2. Review in 1 week\n";
        ss << "3. Advise to return if symptoms worsen\n";
        return ss.str();
    }
};

// Test basic Note record creation with required fields
TEST_F(NoteIntegrationTest, CreateNoteWithRequiredFields) {
    Note note;
    note.note_id = 90001;
    note.person_id = 100001;
    note.note_date = parse_uk_date("01/04/2023");
    note.note_type_concept_id = 44814637; // Consultation note
    note.note_class_concept_id = 36206173; // Clinical note
    note.note_title = "GP Consultation";
    note.note_text = "Patient reviewed in surgery. Stable condition.";
    note.encoding_concept_id = 32678; // UTF-8
    note.language_concept_id = 4180186; // English
    
    EXPECT_EQ(note.note_id, 90001);
    EXPECT_EQ(note.person_id, 100001);
    EXPECT_EQ(note.table_name(), "note");
    EXPECT_EQ(note.schema_name(), "cdm");
    EXPECT_EQ(note.note_title, "GP Consultation");
}

// Test Note record with all optional fields populated
TEST_F(NoteIntegrationTest, CreateNoteWithAllFields) {
    Note note;
    note.note_id = 90002;
    note.person_id = 100002;
    note.note_date = parse_uk_date("15/06/2023");
    note.note_datetime = parse_uk_datetime("15/06/2023 14:30");
    note.note_type_concept_id = 44814638; // Discharge summary
    note.note_class_concept_id = 36206173;
    note.note_title = "Discharge Summary - Bristol Royal Infirmary";
    note.note_text = generate_uk_medical_note("John Smith", "**********", "15/06/2023");
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    note.provider_id = 110001;
    note.visit_occurrence_id = 120001;
    note.visit_detail_id = 130001;
    note.note_source_value = "DISCH_SUMM";
    note.note_event_id = 140001;
    note.note_event_field_concept_id = 0;
    
    EXPECT_TRUE(note.note_datetime.has_value());
    EXPECT_TRUE(note.provider_id.has_value());
    EXPECT_EQ(note.provider_id.value(), 110001);
    EXPECT_TRUE(note.note_text.find("NHS Number: **********") != std::string::npos);
}

// Test validation with valid Note record
TEST_F(NoteIntegrationTest, ValidateValidNote) {
    Note note;
    note.note_id = 90003;
    note.person_id = 100003;
    note.note_date = parse_uk_date("01/07/2023");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "Annual Review";
    note.note_text = "Annual health check completed. All parameters within normal range.";
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    EXPECT_TRUE(note.validate());
    auto errors = note.validation_errors();
    EXPECT_TRUE(errors.empty());
}

// Test validation with invalid note_id
TEST_F(NoteIntegrationTest, ValidateInvalidNoteId) {
    Note note;
    note.note_id = 0; // Invalid
    note.person_id = 100004;
    note.note_date = parse_uk_date("01/08/2023");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "Consultation";
    note.note_text = "Test note";
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    EXPECT_FALSE(note.validate());
    auto errors = note.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("note_id") != std::string::npos; }));
}

// Test SQL generation with proper escaping
TEST_F(NoteIntegrationTest, GenerateInsertSqlWithEscaping) {
    Note note;
    note.note_id = 90004;
    note.person_id = 100005;
    note.note_date = parse_uk_date("01/09/2023");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "O'Brien's Consultation"; // Test apostrophe
    note.note_text = "Patient's condition stable. Follow-up in 2 weeks."; // Multiple apostrophes
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    std::string sql = note.to_insert_sql(true);
    
    EXPECT_TRUE(sql.find("INSERT INTO cdm.note") != std::string::npos);
    EXPECT_TRUE(sql.find("90004") != std::string::npos);
    EXPECT_TRUE(sql.find("O''Brien''s Consultation") != std::string::npos); // Escaped
    EXPECT_TRUE(sql.find("Patient''s condition stable") != std::string::npos);
}

// Test UK-specific clinical note types
TEST_F(NoteIntegrationTest, UkClinicalNoteTypes) {
    // GP consultation note
    Note gp_note;
    gp_note.note_id = 90005;
    gp_note.person_id = 100006;
    gp_note.note_date = parse_uk_date("01/10/2023");
    gp_note.note_type_concept_id = 44814637; // GP consultation
    gp_note.note_class_concept_id = 36206173;
    gp_note.note_title = "GP Consultation - NHS Practice";
    gp_note.note_text = "Routine consultation at local GP surgery.\nNHS Number: **********\n";
    gp_note.encoding_concept_id = 32678;
    gp_note.language_concept_id = 4180186; // English (UK)
    
    // A&E note
    Note ae_note;
    ae_note.note_id = 90006;
    ae_note.person_id = 100006;
    ae_note.note_date = parse_uk_date("02/10/2023");
    ae_note.note_type_concept_id = 44814639; // Emergency note
    ae_note.note_class_concept_id = 36206173;
    ae_note.note_title = "A&E Department Note";
    ae_note.note_text = "Patient attended A&E with acute symptoms.\nTriage category: 3\n";
    ae_note.encoding_concept_id = 32678;
    ae_note.language_concept_id = 4180186;
    
    EXPECT_TRUE(gp_note.validate());
    EXPECT_TRUE(ae_note.validate());
    
    // Check UK-specific content
    EXPECT_TRUE(gp_note.note_text.find("NHS Number") != std::string::npos);
    EXPECT_TRUE(ae_note.note_text.find("A&E") != std::string::npos);
}

// Test NHS letter formatting
TEST_F(NoteIntegrationTest, NhsLetterFormatting) {
    Note letter;
    letter.note_id = 90007;
    letter.person_id = 100007;
    letter.note_date = parse_uk_date("15/11/2023");
    letter.note_datetime = parse_uk_datetime("15/11/2023 16:45");
    letter.note_type_concept_id = 44814640; // Referral letter
    letter.note_class_concept_id = 36206174; // Correspondence
    letter.note_title = "NHS Referral Letter";
    
    std::stringstream letter_text;
    letter_text << "Bristol Royal Infirmary\n";
    letter_text << "Upper Maudlin Street\n";
    letter_text << "Bristol BS2 8HW\n\n";
    letter_text << "Date: 15/11/2023\n\n";
    letter_text << "Dear Dr Williams,\n\n";
    letter_text << "Re: Mrs Sarah Jones, DOB: 12/05/1965, NHS No: **********\n\n";
    letter_text << "Thank you for referring this pleasant 58-year-old lady to our cardiology clinic.\n\n";
    letter_text << "Yours sincerely,\n";
    letter_text << "Mr J Thompson\n";
    letter_text << "Consultant Cardiologist\n";
    
    letter.note_text = letter_text.str();
    letter.encoding_concept_id = 32678;
    letter.language_concept_id = 4180186;
    letter.provider_id = 110002;
    
    EXPECT_TRUE(letter.validate());
    
    // Check UK address format
    EXPECT_TRUE(letter.note_text.find("BS2 8HW") != std::string::npos); // UK postcode
    EXPECT_TRUE(letter.note_text.find("15/11/2023") != std::string::npos); // UK date format
}

// Test concurrent Note record creation
TEST_F(NoteIntegrationTest, ConcurrentNoteCreation) {
    const int num_threads = 10;
    std::vector<std::thread> threads;
    std::vector<std::unique_ptr<Note>> notes(num_threads);
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&notes, i, this]() {
            auto note = std::make_unique<Note>();
            note->note_id = 100000 + i;
            note->person_id = 110000 + i;
            note->note_date = parse_uk_date("01/01/2023") + hours(24 * i);
            note->note_type_concept_id = 44814637;
            note->note_class_concept_id = 36206173;
            note->note_title = "Concurrent Note " + std::to_string(i);
            note->note_text = "Test concurrent note creation " + std::to_string(i);
            note->encoding_concept_id = 32678;
            note->language_concept_id = 4180186;
            notes[i] = std::move(note);
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    for (int i = 0; i < num_threads; ++i) {
        ASSERT_NE(notes[i], nullptr);
        EXPECT_EQ(notes[i]->note_id, 100000 + i);
        EXPECT_TRUE(notes[i]->validate());
    }
}

// Test field visitor pattern
TEST_F(NoteIntegrationTest, FieldVisitorPattern) {
    class TestVisitor : public FieldVisitor {
    public:
        std::vector<std::pair<std::string, std::any>> visited_fields;
        
        void visit(const std::string& name, const std::any& value) override {
            visited_fields.push_back({name, value});
        }
    };
    
    Note note;
    note.note_id = 90008;
    note.person_id = 100008;
    note.note_date = parse_uk_date("01/12/2023");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "Test Visit";
    note.note_text = "Testing field visitor pattern";
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    TestVisitor visitor;
    note.visit_fields(visitor);
    
    EXPECT_EQ(visitor.visited_fields.size(), 16); // All fields
    EXPECT_EQ(visitor.visited_fields[0].first, "note_id");
    EXPECT_EQ(std::any_cast<int64_t>(visitor.visited_fields[0].second), 90008);
}

// Test UK medical terminology in notes
TEST_F(NoteIntegrationTest, UkMedicalTerminology) {
    Note note;
    note.note_id = 90009;
    note.person_id = 100009;
    note.note_date = parse_uk_date("01/01/2024");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "Ward Round Note";
    
    std::stringstream note_text;
    note_text << "Ward Round - Medical Ward 7\n";
    note_text << "Consultant: Mr Davies\n";
    note_text << "Registrar: Dr Patel\n\n";
    note_text << "Patient stable overnight. Observations:\n";
    note_text << "- Apyrexial (36.5°C)\n";
    note_text << "- BP 130/85 mmHg\n";
    note_text << "- SpO2 98% on room air\n";
    note_text << "- Bowels opened\n";
    note_text << "- Mobilising with physiotherapy\n\n";
    note_text << "Plan:\n";
    note_text << "- Continue current medications\n";
    note_text << "- Bloods for U&Es and FBC\n";
    note_text << "- Consider discharge if bloods satisfactory\n";
    
    note.note_text = note_text.str();
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    EXPECT_TRUE(note.validate());
    
    // Check UK medical terms
    EXPECT_TRUE(note.note_text.find("Registrar") != std::string::npos); // UK role
    EXPECT_TRUE(note.note_text.find("Ward Round") != std::string::npos); // UK practice
    EXPECT_TRUE(note.note_text.find("U&Es") != std::string::npos); // UK abbreviation
    EXPECT_TRUE(note.note_text.find("FBC") != std::string::npos); // Full Blood Count (UK)
}

// Test batch validation of notes
TEST_F(NoteIntegrationTest, BatchNoteValidation) {
    std::vector<Note> notes;
    
    // Create mix of valid and invalid records
    for (int i = 0; i < 50; ++i) {
        Note note;
        
        if (i % 10 == 0) {
            // Invalid: zero note_id
            note.note_id = 0;
            note.person_id = 120000 + i;
        } else if (i % 10 == 5) {
            // Invalid: missing required type
            note.note_id = 110000 + i;
            note.person_id = 120000 + i;
            note.note_type_concept_id = 0; // Invalid
        } else {
            // Valid
            note.note_id = 110000 + i;
            note.person_id = 120000 + i;
            note.note_type_concept_id = 44814637;
        }
        
        note.note_date = parse_uk_date("01/01/2023");
        note.note_class_concept_id = 36206173;
        note.note_title = "Note " + std::to_string(i);
        note.note_text = "Test note " + std::to_string(i);
        note.encoding_concept_id = 32678;
        note.language_concept_id = 4180186;
        
        notes.push_back(std::move(note));
    }
    
    int valid_count = 0;
    int invalid_count = 0;
    
    for (const auto& note : notes) {
        if (note.validate()) {
            valid_count++;
        } else {
            invalid_count++;
        }
    }
    
    EXPECT_EQ(valid_count, 40);
    EXPECT_EQ(invalid_count, 10);
}

// Test maximum field lengths
TEST_F(NoteIntegrationTest, MaximumFieldLengths) {
    Note note;
    note.note_id = 9223372036854775807; // Max int64_t
    note.person_id = 9223372036854775806;
    note.note_date = parse_uk_date("01/01/2023");
    note.note_type_concept_id = 2147483647; // Max int32_t
    note.note_class_concept_id = **********;
    note.note_title = std::string(250, 'X'); // Max 250 chars
    note.note_text = std::string(10000, 'Y'); // Large text
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    // Create maximum length optional strings (50 characters)
    note.note_source_value = std::string(50, 'Z');
    
    EXPECT_TRUE(note.validate());
    
    std::string sql = note.to_insert_sql();
    EXPECT_TRUE(sql.find("9223372036854775807") != std::string::npos);
}

// Test NHS clinical coding in notes
TEST_F(NoteIntegrationTest, NhsClinicalCoding) {
    Note note;
    note.note_id = 90010;
    note.person_id = 100010;
    note.note_date = parse_uk_date("01/02/2024");
    note.note_type_concept_id = 44814638; // Discharge summary
    note.note_class_concept_id = 36206173;
    note.note_title = "Coded Discharge Summary";
    
    std::stringstream note_text;
    note_text << "Primary Diagnosis: I21.0 - Acute transmural myocardial infarction of anterior wall\n";
    note_text << "Secondary Diagnoses:\n";
    note_text << "- E11.9 - Type 2 diabetes mellitus without complications\n";
    note_text << "- I10 - Essential (primary) hypertension\n\n";
    note_text << "Procedures:\n";
    note_text << "- K49.2 - Percutaneous transluminal coronary angioplasty\n";
    note_text << "- OPCS-4: K63.3 - Insertion of coronary artery stent\n\n";
    note_text << "HRG: EB10Z - Percutaneous Coronary Intervention\n";
    
    note.note_text = note_text.str();
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    
    EXPECT_TRUE(note.validate());
    
    // Check NHS coding systems
    EXPECT_TRUE(note.note_text.find("OPCS-4") != std::string::npos); // UK procedure codes
    EXPECT_TRUE(note.note_text.find("HRG") != std::string::npos); // Healthcare Resource Groups
}

// Test integration with schema definitions
TEST_F(NoteIntegrationTest, SchemaIntegration) {
    auto& schema_def = SchemaDefinitions::instance();
    auto table_def = schema_def.get_table("note");
    
    ASSERT_NE(table_def, nullptr);
    EXPECT_EQ(table_def->get_name(), "note");
    
    // Verify fields match
    Note note;
    auto field_names = note.field_names();
    auto schema_fields = table_def->get_fields();
    
    EXPECT_EQ(field_names.size(), schema_fields.size());
    
    for (size_t i = 0; i < field_names.size(); ++i) {
        EXPECT_EQ(field_names[i], schema_fields[i].name);
    }
}

// Test factory creation
TEST_F(NoteIntegrationTest, FactoryCreation) {
    auto table = OmopTableFactory::create("note");
    
    ASSERT_NE(table, nullptr);
    EXPECT_EQ(table->table_name(), "note");
    
    // Cast to Note and test
    if (auto* note = dynamic_cast<Note*>(table.get())) {
        note->note_id = 90011;
        note->person_id = 100011;
        note->note_date = parse_uk_date("01/03/2024");
        note->note_type_concept_id = 44814637;
        note->note_class_concept_id = 36206173;
        note->note_title = "Factory Test";
        note->note_text = "Created via factory";
        note->encoding_concept_id = 32678;
        note->language_concept_id = 4180186;
        
        EXPECT_TRUE(note->validate());
    } else {
        FAIL() << "Failed to cast to Note type";
    }
}

// Test UK language variations in notes
TEST_F(NoteIntegrationTest, UkLanguageVariations) {
    Note note;
    note.note_id = 90012;
    note.person_id = 100012;
    note.note_date = parse_uk_date("01/04/2024");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "Outpatient Review";
    
    std::stringstream note_text;
    note_text << "Patient attended outpatients department today.\n";
    note_text << "Presenting with generalised abdominal pain, particularly in the right iliac fossa.\n";
    note_text << "No diarrhoea or vomiting. Appetite maintained.\n";
    note_text << "On examination: Soft abdomen, localised tenderness RIF.\n";
    note_text << "Plan: Arrange urgent ultrasound scan. Review with results.\n";
    note_text << "If symptoms worsen, advise to attend A&E immediately.\n";
    
    note.note_text = note_text.str();
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186; // English (UK)
    
    EXPECT_TRUE(note.validate());
    
    // Check UK spelling and terminology
    EXPECT_TRUE(note.note_text.find("generalised") != std::string::npos); // UK spelling
    EXPECT_TRUE(note.note_text.find("diarrhoea") != std::string::npos); // UK spelling
    EXPECT_TRUE(note.note_text.find("outpatients") != std::string::npos); // UK term
}

// Test GP practice note templates
TEST_F(NoteIntegrationTest, GpPracticeNoteTemplates) {
    Note note;
    note.note_id = 90013;
    note.person_id = 100013;
    note.note_date = parse_uk_date("15/05/2024");
    note.note_datetime = parse_uk_datetime("15/05/2024 09:15");
    note.note_type_concept_id = 44814637;
    note.note_class_concept_id = 36206173;
    note.note_title = "10-minute GP Consultation";
    
    std::stringstream note_text;
    note_text << "S: Sore throat for 3 days, difficulty swallowing\n";
    note_text << "O: Throat erythematous, tonsillar exudate present\n";
    note_text << "   Temperature 37.8°C, pulse 88 regular\n";
    note_text << "   Cervical lymphadenopathy\n";
    note_text << "A: Likely bacterial tonsillitis\n";
    note_text << "P: Phenoxymethylpenicillin 500mg QDS for 10 days\n";
    note_text << "   Advise rest, fluids, analgesia\n";
    note_text << "   Return if not improving in 48-72 hours\n";
    note_text << "   Safety net: A&E if breathing difficulties\n";
    
    note.note_text = note_text.str();
    note.encoding_concept_id = 32678;
    note.language_concept_id = 4180186;
    note.provider_id = 110003;
    
    EXPECT_TRUE(note.validate());
    
    // Check SOAP format commonly used in UK GP practices
    EXPECT_TRUE(note.note_text.find("S:") != std::string::npos); // Subjective
    EXPECT_TRUE(note.note_text.find("O:") != std::string::npos); // Objective
    EXPECT_TRUE(note.note_text.find("A:") != std::string::npos); // Assessment
    EXPECT_TRUE(note.note_text.find("P:") != std::string::npos); // Plan
    EXPECT_TRUE(note.note_text.find("QDS") != std::string::npos); // UK dosing abbreviation
} 