/**
 * @file test_observation_period_integration.cpp
 * @brief Integration tests for ObservationPeriod table with UK NHS coverage scenarios
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <algorithm>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for ObservationPeriod integration tests
 */
class ObservationPeriodIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // NHS start date (5 July 1948)
        nhs_start_date = createUKDate(5, 7, 1948);
        
        // Common UK dates
        uk_date_2020_01_01 = createUKDate(1, 1, 2020);
        uk_date_2023_04_01 = createUKDate(1, 4, 2023);
        uk_date_2024_03_31 = createUKDate(31, 3, 2024);
        uk_date_2025_01_01 = createUKDate(1, 1, 2025);
        current_date = system_clock::now();
    }

    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    int64_t daysBetween(const system_clock::time_point& start, 
                        const system_clock::time_point& end) {
        return duration_cast<days>(end - start).count();
    }

    system_clock::time_point nhs_start_date;
    system_clock::time_point uk_date_2020_01_01;
    system_clock::time_point uk_date_2023_04_01;
    system_clock::time_point uk_date_2024_03_31;
    system_clock::time_point uk_date_2025_01_01;
    system_clock::time_point current_date;
};

// Test complete NHS coverage period
TEST_F(ObservationPeriodIntegrationTest, CompleteNHSCoveragePeriod) {
    ObservationPeriod period;
    
    period.observation_period_id = 1000001;
    period.person_id = 100001;
    period.observation_period_start_date = uk_date_2020_01_01;
    period.observation_period_end_date = uk_date_2025_01_01;
    period.period_type_concept_id = 44814724; // Period while enrolled in insurance
    
    EXPECT_TRUE(period.validate());
    
    // Calculate coverage duration
    auto duration_days = daysBetween(period.observation_period_start_date, 
                                    period.observation_period_end_date);
    EXPECT_EQ(duration_days, 1827); // 5 years + 1 leap day
    
    // Test SQL generation
    auto sql = period.to_insert_sql(true);
    EXPECT_NE(sql.find("INSERT INTO cdm.observation_period"), std::string::npos);
    EXPECT_NE(sql.find("44814724"), std::string::npos);
}

// Test UK period type concepts
TEST_F(ObservationPeriodIntegrationTest, UKPeriodTypeConcepts) {
    struct PeriodTypeTest {
        int32_t concept_id;
        std::string description;
        system_clock::time_point start_date;
        system_clock::time_point end_date;
    };
    
    std::vector<PeriodTypeTest> period_types = {
        {44814724, "Period while enrolled in insurance", uk_date_2020_01_01, uk_date_2025_01_01},
        {44814725, "Period inferred by algorithm", uk_date_2023_04_01, uk_date_2024_03_31},
        {32817, "EHR observation period", uk_date_2020_01_01, current_date},
        {32880, "Hospital observation period", uk_date_2024_03_31, uk_date_2024_03_31 + days(7)}
    };
    
    for (const auto& period_type : period_types) {
        ObservationPeriod period;
        period.observation_period_id = 2000000 + period_type.concept_id;
        period.person_id = 100001;
        period.observation_period_start_date = period_type.start_date;
        period.observation_period_end_date = period_type.end_date;
        period.period_type_concept_id = period_type.concept_id;
        
        EXPECT_TRUE(period.validate()) << "Failed for period type: " << period_type.description;
    }
}

// Test GP registration periods
TEST_F(ObservationPeriodIntegrationTest, GPRegistrationPeriods) {
    // Simulate patient changing GP practices
    std::vector<std::pair<system_clock::time_point, system_clock::time_point>> gp_periods = {
        {createUKDate(1, 1, 2015), createUKDate(31, 12, 2017)}, // First GP
        {createUKDate(1, 1, 2018), createUKDate(30, 6, 2020)},  // Second GP
        {createUKDate(1, 7, 2020), current_date}                 // Current GP
    };
    
    int64_t period_id = 3000001;
    for (const auto& [start, end] : gp_periods) {
        ObservationPeriod period;
        period.observation_period_id = period_id++;
        period.person_id = 100002;
        period.observation_period_start_date = start;
        period.observation_period_end_date = end;
        period.period_type_concept_id = 44814724;
        
        EXPECT_TRUE(period.validate());
        EXPECT_LT(period.observation_period_start_date, period.observation_period_end_date);
    }
}

// Test overlapping period detection
TEST_F(ObservationPeriodIntegrationTest, OverlappingPeriodDetection) {
    std::vector<ObservationPeriod> periods;
    
    // Create overlapping periods
    ObservationPeriod period1;
    period1.observation_period_id = 4000001;
    period1.person_id = 100003;
    period1.observation_period_start_date = createUKDate(1, 1, 2023);
    period1.observation_period_end_date = createUKDate(31, 12, 2023);
    period1.period_type_concept_id = 44814724;
    periods.push_back(period1);
    
    ObservationPeriod period2;
    period2.observation_period_id = 4000002;
    period2.person_id = 100003;
    period2.observation_period_start_date = createUKDate(1, 6, 2023);
    period2.observation_period_end_date = createUKDate(30, 6, 2024);
    period2.period_type_concept_id = 44814724;
    periods.push_back(period2);
    
    // Both periods should be valid individually
    for (const auto& period : periods) {
        EXPECT_TRUE(period.validate());
    }
    
    // Check for overlap
    bool has_overlap = period1.observation_period_end_date >= period2.observation_period_start_date;
    EXPECT_TRUE(has_overlap);
}

// Test date range validation
TEST_F(ObservationPeriodIntegrationTest, DateRangeValidation) {
    ObservationPeriod period;
    period.observation_period_id = 5000001;
    period.person_id = 100004;
    period.period_type_concept_id = 44814724;
    
    // Test invalid date range (end before start)
    period.observation_period_start_date = uk_date_2025_01_01;
    period.observation_period_end_date = uk_date_2020_01_01;
    EXPECT_FALSE(period.validate());
    
    auto errors = period.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& error) { 
            return error.find("start_date") != std::string::npos && 
                   error.find("end_date") != std::string::npos; 
        }));
    
    // Test same day period (valid)
    period.observation_period_start_date = uk_date_2024_03_31;
    period.observation_period_end_date = uk_date_2024_03_31;
    EXPECT_TRUE(period.validate());
}

// Test NHS historical periods
TEST_F(ObservationPeriodIntegrationTest, NHSHistoricalPeriods) {
    // Test period from NHS inception
    ObservationPeriod historical_period;
    historical_period.observation_period_id = 6000001;
    historical_period.person_id = 100005;
    historical_period.observation_period_start_date = nhs_start_date;
    historical_period.observation_period_end_date = createUKDate(31, 12, 1999);
    historical_period.period_type_concept_id = 44814725; // Inferred by algorithm
    
    EXPECT_TRUE(historical_period.validate());
    
    // Calculate historical coverage
    auto years = daysBetween(historical_period.observation_period_start_date,
                            historical_period.observation_period_end_date) / 365.25;
    EXPECT_GT(years, 51); // Over 51 years of NHS history
}

// Test bulk period creation
TEST_F(ObservationPeriodIntegrationTest, BulkPeriodCreation) {
    const int num_patients = 1000;
    std::vector<ObservationPeriod> periods;
    periods.reserve(num_patients);
    
    auto start_time = steady_clock::now();
    
    for (int i = 0; i < num_patients; ++i) {
        ObservationPeriod period;
        period.observation_period_id = 7000000 + i;
        period.person_id = 200000 + i;
        
        // Vary start dates across different years
        int year = 2010 + (i % 15);
        int month = 1 + (i % 12);
        int day = 1 + (i % 28);
        
        period.observation_period_start_date = createUKDate(day, month, year);
        period.observation_period_end_date = period.observation_period_start_date + days(365 * (1 + (i % 5)));
        period.period_type_concept_id = 44814724;
        
        EXPECT_TRUE(period.validate());
        periods.push_back(period);
    }
    
    auto end_time = steady_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    EXPECT_LT(duration.count(), 2000); // Should complete within 2 seconds
    EXPECT_EQ(periods.size(), num_patients);
}

// Test field extraction
TEST_F(ObservationPeriodIntegrationTest, FieldExtraction) {
    ObservationPeriod period;
    period.observation_period_id = 8000001;
    period.person_id = 100006;
    period.observation_period_start_date = uk_date_2023_04_01;
    period.observation_period_end_date = uk_date_2024_03_31;
    period.period_type_concept_id = 44814724;
    
    auto field_names = period.field_names();
    auto field_values = period.field_values();
    
    EXPECT_EQ(field_names.size(), 5);
    EXPECT_EQ(field_values.size(), 5);
    
    // Verify field order
    EXPECT_EQ(field_names[0], "observation_period_id");
    EXPECT_EQ(field_names[1], "person_id");
    EXPECT_EQ(field_names[2], "observation_period_start_date");
    EXPECT_EQ(field_names[3], "observation_period_end_date");
    EXPECT_EQ(field_names[4], "period_type_concept_id");
    
    // Verify values
    EXPECT_EQ(std::any_cast<int64_t>(field_values[0]), 8000001);
    EXPECT_EQ(std::any_cast<int64_t>(field_values[1]), 100006);
}

// Test SQL generation variations
TEST_F(ObservationPeriodIntegrationTest, SQLGenerationVariations) {
    ObservationPeriod period;
    period.observation_period_id = 9000001;
    period.person_id = 100007;
    period.observation_period_start_date = uk_date_2024_03_31;
    period.observation_period_end_date = uk_date_2025_01_01;
    period.period_type_concept_id = 44814724;
    
    // Test with escaping
    auto sql_escaped = period.to_insert_sql(true);
    EXPECT_NE(sql_escaped.find("INSERT INTO"), std::string::npos);
    EXPECT_NE(sql_escaped.find("observation_period"), std::string::npos);
    
    // Test without escaping
    auto sql_no_escape = period.to_insert_sql(false);
    EXPECT_NE(sql_no_escape.find("INSERT INTO"), std::string::npos);
    
    // Both should produce valid SQL
    EXPECT_GT(sql_escaped.length(), 100);
    EXPECT_GT(sql_no_escape.length(), 100);
}

// Test field visitor pattern
TEST_F(ObservationPeriodIntegrationTest, FieldVisitorPattern) {
    class PeriodFieldVisitor : public FieldVisitor {
    public:
        std::vector<std::string> visited_fields;
        int date_fields = 0;
        
        void visit(const std::string& name, const std::any& value) override {
            visited_fields.push_back(name);
            if (name.find("date") != std::string::npos) {
                date_fields++;
            }
        }
    };
    
    ObservationPeriod period;
    period.observation_period_id = 10000001;
    period.person_id = 10000002;
    period.observation_period_start_date = uk_date_2024_03_31;
    period.observation_period_end_date = uk_date_2025_01_01;
    period.period_type_concept_id = 44814724;
    
    PeriodFieldVisitor visitor;
    period.visit_fields(visitor);
    
    // Check that all fields are visited
    EXPECT_EQ(visitor.visited_fields.size(), 5);
    // Check that at least two date fields are present
    EXPECT_GE(visitor.date_fields, 2);
}
