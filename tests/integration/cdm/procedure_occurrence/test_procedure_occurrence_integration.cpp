/**
 * @file test_procedure_occurrence_integration.cpp
 * @brief Integration tests for ProcedureOccurrence table with UK NHS procedure scenarios
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <memory>
#include <map>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for ProcedureOccurrence integration tests
 */
class ProcedureOccurrenceIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Common UK dates for testing
        uk_procedure_date_2024 = createUKDate(15, 8, 2024);
        uk_procedure_date_2023 = createUKDate(20, 3, 2023);
        uk_procedure_datetime_2024 = createUKDateTime(15, 8, 2024, 9, 30);
        uk_procedure_end_datetime_2024 = createUKDateTime(15, 8, 2024, 11, 45);
        current_date = system_clock::now();
        
        // Set up UK-specific test data
        setupUKTestData();
    }

    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point createUKDateTime(int day, int month, int year, int hour, int minute) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        timeinfo.tm_hour = hour;
        timeinfo.tm_min = minute;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    void setupUKTestData() {
        // UK surgical procedure concepts (OPCS-4 based)
        uk_appendectomy_concept = 4302541; // Appendectomy
        uk_cholecystectomy_concept = 4180790; // Cholecystectomy (laparoscopic)
        uk_cataract_surgery_concept = 4298749; // Cataract extraction
        uk_hip_replacement_concept = 4146449; // Hip replacement
        uk_coronary_angioplasty_concept = 4139201; // Coronary angioplasty
        
        // UK diagnostic procedures
        uk_colonoscopy_concept = 4149096; // Colonoscopy
        uk_endoscopy_concept = 4163261; // Upper GI endoscopy
        uk_ct_scan_concept = 4013635; // CT scan
        uk_mri_scan_concept = 4013638; // MRI scan
        uk_echocardiogram_concept = 4118793; // Echocardiography
        
        // UK therapeutic procedures
        uk_physiotherapy_concept = 4273629; // Physiotherapy
        uk_chemotherapy_concept = 4273732; // Chemotherapy administration
        uk_radiotherapy_concept = 4197167; // Radiotherapy
        uk_dialysis_concept = 4139525; // Haemodialysis
        
        // UK procedure type concepts
        uk_inpatient_procedure_concept = 38000275; // Inpatient procedure
        uk_outpatient_procedure_concept = 38000276; // Outpatient procedure
        uk_emergency_procedure_concept = 38000277; // Emergency procedure
        uk_day_case_procedure_concept = 38000278; // Day case procedure
        
        // UK modifier concepts (OPCS-4 modifiers)
        uk_laparoscopic_modifier = 4151928; // Laparoscopic approach
        uk_robotic_modifier = 4139847; // Robotic-assisted
        uk_bilateral_modifier = 4139361; // Bilateral procedure
        uk_emergency_modifier = 4053838; // Emergency procedure
        
        // UK provider and facility IDs
        uk_surgeon_provider_id = 50025;
        uk_anaesthetist_provider_id = 50026;
        uk_radiologist_provider_id = 50030;
        uk_cardiologist_provider_id = 50035;
        uk_nhs_hospital_care_site_id = 20001;
        uk_day_surgery_unit_care_site_id = 20025;
        uk_cardiac_centre_care_site_id = 20030;
    }

    system_clock::time_point uk_procedure_date_2024;
    system_clock::time_point uk_procedure_date_2023;
    system_clock::time_point uk_procedure_datetime_2024;
    system_clock::time_point uk_procedure_end_datetime_2024;
    system_clock::time_point current_date;
    
    // UK surgical procedures
    int32_t uk_appendectomy_concept;
    int32_t uk_cholecystectomy_concept;
    int32_t uk_cataract_surgery_concept;
    int32_t uk_hip_replacement_concept;
    int32_t uk_coronary_angioplasty_concept;
    
    // UK diagnostic procedures
    int32_t uk_colonoscopy_concept;
    int32_t uk_endoscopy_concept;
    int32_t uk_ct_scan_concept;
    int32_t uk_mri_scan_concept;
    int32_t uk_echocardiogram_concept;
    
    // UK therapeutic procedures
    int32_t uk_physiotherapy_concept;
    int32_t uk_chemotherapy_concept;
    int32_t uk_radiotherapy_concept;
    int32_t uk_dialysis_concept;
    
    // UK procedure types
    int32_t uk_inpatient_procedure_concept;
    int32_t uk_outpatient_procedure_concept;
    int32_t uk_emergency_procedure_concept;
    int32_t uk_day_case_procedure_concept;
    
    // UK modifiers
    int32_t uk_laparoscopic_modifier;
    int32_t uk_robotic_modifier;
    int32_t uk_bilateral_modifier;
    int32_t uk_emergency_modifier;
    
    // UK healthcare providers and facilities
    int32_t uk_surgeon_provider_id;
    int32_t uk_anaesthetist_provider_id;
    int32_t uk_radiologist_provider_id;
    int32_t uk_cardiologist_provider_id;
    int32_t uk_nhs_hospital_care_site_id;
    int32_t uk_day_surgery_unit_care_site_id;
    int32_t uk_cardiac_centre_care_site_id;
};

// Tests comprehensive NHS surgical procedure record
TEST_F(ProcedureOccurrenceIntegrationTest, CompleteNHSSurgicalProcedure) {
    ProcedureOccurrence procedure;
    
    // Set comprehensive NHS surgical procedure data
    procedure.procedure_occurrence_id = 5000001;
    procedure.person_id = 1000001;
    procedure.procedure_concept_id = uk_appendectomy_concept;
    procedure.procedure_date = uk_procedure_date_2024;
    procedure.procedure_datetime = uk_procedure_datetime_2024;
    procedure.procedure_end_date = uk_procedure_date_2024;
    procedure.procedure_end_datetime = uk_procedure_end_datetime_2024;
    procedure.procedure_type_concept_id = uk_inpatient_procedure_concept;
    procedure.modifier_concept_id = uk_laparoscopic_modifier;
    procedure.quantity = 1;
    procedure.provider_id = uk_surgeon_provider_id;
    procedure.visit_occurrence_id = 2000001;
    procedure.visit_detail_id = 3000001;
    procedure.procedure_source_value = "OPCS4_H01.1"; // OPCS-4 code for appendectomy
    procedure.procedure_source_concept_id = 0;
    procedure.modifier_source_value = "Laparoscopic";
    
    // Validate record
    EXPECT_TRUE(procedure.validate());
    auto errors = procedure.validation_errors();
    EXPECT_TRUE(errors.empty());
    
    // Test SQL generation
    auto sql = procedure.to_insert_sql(true);
    EXPECT_NE(sql.find("INSERT INTO cdm.procedure_occurrence"), std::string::npos);
    EXPECT_NE(sql.find("OPCS4_H01.1"), std::string::npos); // OPCS-4 code
    EXPECT_NE(sql.find("Laparoscopic"), std::string::npos);
}

// Tests UK OPCS-4 coded procedures
TEST_F(ProcedureOccurrenceIntegrationTest, UKOPCSFourCodedProcedures) {
    std::vector<ProcedureOccurrence> opcs_procedures;
    
    // Laparoscopic cholecystectomy
    ProcedureOccurrence cholecyst;
    cholecyst.procedure_occurrence_id = 5000002;
    cholecyst.person_id = 1000002;
    cholecyst.procedure_concept_id = uk_cholecystectomy_concept;
    cholecyst.procedure_date = uk_procedure_date_2024;
    cholecyst.procedure_type_concept_id = uk_day_case_procedure_concept;
    cholecyst.modifier_concept_id = uk_laparoscopic_modifier;
    cholecyst.provider_id = uk_surgeon_provider_id;
    cholecyst.procedure_source_value = "OPCS4_J18.1"; // Laparoscopic cholecystectomy
    cholecyst.modifier_source_value = "Laparoscopic approach";
    
    // Hip replacement
    ProcedureOccurrence hip_replacement;
    hip_replacement.procedure_occurrence_id = 5000003;
    hip_replacement.person_id = 1000003;
    hip_replacement.procedure_concept_id = uk_hip_replacement_concept;
    hip_replacement.procedure_date = uk_procedure_date_2024;
    hip_replacement.procedure_type_concept_id = uk_inpatient_procedure_concept;
    hip_replacement.quantity = 1;
    hip_replacement.provider_id = uk_surgeon_provider_id;
    hip_replacement.procedure_source_value = "OPCS4_W37.1"; // Total hip replacement
    hip_replacement.modifier_source_value = "Unilateral";
    
    // Coronary angioplasty
    ProcedureOccurrence angioplasty;
    angioplasty.procedure_occurrence_id = 5000004;
    angioplasty.person_id = 1000004;
    angioplasty.procedure_concept_id = uk_coronary_angioplasty_concept;
    angioplasty.procedure_date = uk_procedure_date_2024;
    angioplasty.procedure_type_concept_id = uk_inpatient_procedure_concept;
    angioplasty.provider_id = uk_cardiologist_provider_id;
    angioplasty.procedure_source_value = "OPCS4_K75.2"; // Coronary angioplasty
    angioplasty.modifier_source_value = "With stent insertion";
    
    opcs_procedures.push_back(std::move(cholecyst));
    opcs_procedures.push_back(std::move(hip_replacement));
    opcs_procedures.push_back(std::move(angioplasty));
    
    // Validate all OPCS-4 procedures
    for (const auto& proc : opcs_procedures) {
        EXPECT_TRUE(proc.validate());
        auto errors = proc.validation_errors();
        EXPECT_TRUE(errors.empty());
        
        // Verify OPCS-4 source values
        EXPECT_TRUE(proc.procedure_source_value.has_value());
        EXPECT_NE(proc.procedure_source_value.value().find("OPCS4_"), std::string::npos);
    }
}

// Tests NHS day case procedures
TEST_F(ProcedureOccurrenceIntegrationTest, NHSDayCaseProcedures) {
    ProcedureOccurrence day_case;
    
    // Set NHS day case cataract surgery
    day_case.procedure_occurrence_id = 5000005;
    day_case.person_id = 1000005;
    day_case.procedure_concept_id = uk_cataract_surgery_concept;
    day_case.procedure_date = uk_procedure_date_2024;
    day_case.procedure_datetime = uk_procedure_datetime_2024;
    day_case.procedure_end_datetime = createUKDateTime(15, 8, 2024, 10, 30); // 1-hour procedure
    day_case.procedure_type_concept_id = uk_day_case_procedure_concept;
    day_case.quantity = 1;
    day_case.provider_id = uk_surgeon_provider_id;
    day_case.visit_occurrence_id = 2000005;
    day_case.procedure_source_value = "OPCS4_C71.2"; // Cataract extraction
    day_case.modifier_source_value = "Phacoemulsification";
    
    // Validate day case procedure
    EXPECT_TRUE(day_case.validate());
    
    // Test procedure duration calculation (should be about 1 hour)
    auto duration = duration_cast<minutes>(
        day_case.procedure_end_datetime.value() - day_case.procedure_datetime.value()
    );
    EXPECT_EQ(duration.count(), 60); // 1 hour procedure
    
    // Test field extraction
    auto field_names = day_case.field_names();
    auto field_values = day_case.field_values();
    EXPECT_EQ(field_names.size(), field_values.size());
    EXPECT_EQ(field_names.size(), 16); // Total procedure occurrence fields
    
    // Test SQL generation contains day case data
    auto sql = day_case.to_insert_sql(true);
    EXPECT_NE(sql.find("OPCS4_C71.2"), std::string::npos);
    EXPECT_NE(sql.find("Phacoemulsification"), std::string::npos);
}

// Tests UK diagnostic procedures
TEST_F(ProcedureOccurrenceIntegrationTest, UKDiagnosticProcedures) {
    std::vector<ProcedureOccurrence> diagnostic_procedures;
    
    // Colonoscopy
    ProcedureOccurrence colonoscopy;
    colonoscopy.procedure_occurrence_id = 5000006;
    colonoscopy.person_id = 1000006;
    colonoscopy.procedure_concept_id = uk_colonoscopy_concept;
    colonoscopy.procedure_date = uk_procedure_date_2024;
    colonoscopy.procedure_type_concept_id = uk_outpatient_procedure_concept;
    colonoscopy.provider_id = uk_surgeon_provider_id; // Gastroenterologist
    colonoscopy.procedure_source_value = "OPCS4_H22.8"; // Colonoscopy
    colonoscopy.modifier_source_value = "Diagnostic";
    
    // CT scan
    ProcedureOccurrence ct_scan;
    ct_scan.procedure_occurrence_id = 5000007;
    ct_scan.person_id = 1000006;
    ct_scan.procedure_concept_id = uk_ct_scan_concept;
    ct_scan.procedure_date = uk_procedure_date_2024;
    ct_scan.procedure_type_concept_id = uk_outpatient_procedure_concept;
    ct_scan.provider_id = uk_radiologist_provider_id;
    ct_scan.procedure_source_value = "OPCS4_U07.1"; // CT scan abdomen
    ct_scan.modifier_source_value = "With contrast";
    
    // MRI scan
    ProcedureOccurrence mri_scan;
    mri_scan.procedure_occurrence_id = 5000008;
    mri_scan.person_id = 1000006;
    mri_scan.procedure_concept_id = uk_mri_scan_concept;
    mri_scan.procedure_date = uk_procedure_date_2024;
    mri_scan.procedure_type_concept_id = uk_outpatient_procedure_concept;
    mri_scan.provider_id = uk_radiologist_provider_id;
    mri_scan.procedure_source_value = "OPCS4_U05.1"; // MRI scan brain
    mri_scan.modifier_source_value = "With gadolinium";
    
    diagnostic_procedures.push_back(std::move(colonoscopy));
    diagnostic_procedures.push_back(std::move(ct_scan));
    diagnostic_procedures.push_back(std::move(mri_scan));
    
    // Validate all diagnostic procedures
    for (const auto& proc : diagnostic_procedures) {
        EXPECT_TRUE(proc.validate());
        
        // All should be outpatient procedures
        EXPECT_EQ(proc.procedure_type_concept_id, uk_outpatient_procedure_concept);
        
        // Should have appropriate providers
        EXPECT_TRUE(proc.provider_id.has_value());
        EXPECT_GT(proc.provider_id.value(), 0);
    }
}

// Tests NHS emergency procedures
TEST_F(ProcedureOccurrenceIntegrationTest, NHSEmergencyProcedures) {
    ProcedureOccurrence emergency_procedure;
    
    // Set NHS emergency appendectomy
    emergency_procedure.procedure_occurrence_id = 5000009;
    emergency_procedure.person_id = 1000007;
    emergency_procedure.procedure_concept_id = uk_appendectomy_concept;
    emergency_procedure.procedure_date = uk_procedure_date_2024;
    emergency_procedure.procedure_datetime = uk_procedure_datetime_2024;
    emergency_procedure.procedure_type_concept_id = uk_emergency_procedure_concept;
    emergency_procedure.modifier_concept_id = uk_emergency_modifier;
    emergency_procedure.quantity = 1;
    emergency_procedure.provider_id = uk_surgeon_provider_id;
    emergency_procedure.visit_occurrence_id = 2000007; // Emergency admission
    emergency_procedure.procedure_source_value = "OPCS4_H01.1"; // Emergency appendectomy
    emergency_procedure.modifier_source_value = "Emergency operation";
    
    // Validate emergency procedure
    EXPECT_TRUE(emergency_procedure.validate());
    
    // Test emergency-specific fields
    EXPECT_EQ(emergency_procedure.procedure_type_concept_id, uk_emergency_procedure_concept);
    EXPECT_EQ(emergency_procedure.modifier_concept_id.value(), uk_emergency_modifier);
    
    // Verify SQL contains emergency data
    auto sql = emergency_procedure.to_insert_sql(true);
    EXPECT_NE(sql.find("Emergency operation"), std::string::npos);
    EXPECT_NE(sql.find("OPCS4_H01.1"), std::string::npos);
}

// Tests UK therapeutic procedures
TEST_F(ProcedureOccurrenceIntegrationTest, UKTherapeuticProcedures) {
    std::vector<ProcedureOccurrence> therapeutic_procedures;
    
    // Chemotherapy administration
    ProcedureOccurrence chemotherapy;
    chemotherapy.procedure_occurrence_id = 5000010;
    chemotherapy.person_id = 1000008;
    chemotherapy.procedure_concept_id = uk_chemotherapy_concept;
    chemotherapy.procedure_date = uk_procedure_date_2024;
    chemotherapy.procedure_type_concept_id = uk_outpatient_procedure_concept;
    chemotherapy.quantity = 1; // One cycle
    chemotherapy.provider_id = 50040; // Oncologist
    chemotherapy.procedure_source_value = "OPCS4_X72.1"; // Chemotherapy
    chemotherapy.modifier_source_value = "Intravenous";
    
    // Physiotherapy session
    ProcedureOccurrence physiotherapy;
    physiotherapy.procedure_occurrence_id = 5000011;
    physiotherapy.person_id = 1000009;
    physiotherapy.procedure_concept_id = uk_physiotherapy_concept;
    physiotherapy.procedure_date = uk_procedure_date_2024;
    physiotherapy.procedure_type_concept_id = uk_outpatient_procedure_concept;
    physiotherapy.quantity = 1; // One session
    physiotherapy.provider_id = 50045; // Physiotherapist
    physiotherapy.procedure_source_value = "OPCS4_A57.1"; // Physiotherapy
    physiotherapy.modifier_source_value = "Individual session";
    
    // Haemodialysis
    ProcedureOccurrence dialysis;
    dialysis.procedure_occurrence_id = 5000012;
    dialysis.person_id = 1000010;
    dialysis.procedure_concept_id = uk_dialysis_concept;
    dialysis.procedure_date = uk_procedure_date_2024;
    dialysis.procedure_type_concept_id = uk_outpatient_procedure_concept;
    dialysis.quantity = 1; // One session
    dialysis.provider_id = 50050; // Nephrologist
    dialysis.procedure_source_value = "OPCS4_X40.3"; // Haemodialysis
    dialysis.modifier_source_value = "4-hour session";
    
    therapeutic_procedures.push_back(std::move(chemotherapy));
    therapeutic_procedures.push_back(std::move(physiotherapy));
    therapeutic_procedures.push_back(std::move(dialysis));
    
    // Validate all therapeutic procedures
    for (const auto& proc : therapeutic_procedures) {
        EXPECT_TRUE(proc.validate());
        
        // All should be outpatient procedures
        EXPECT_EQ(proc.procedure_type_concept_id, uk_outpatient_procedure_concept);
        
        // Should have quantity = 1 (one session/cycle)
        EXPECT_TRUE(proc.quantity.has_value());
        EXPECT_EQ(proc.quantity.value(), 1);
    }
}

// Tests procedure validation errors
TEST_F(ProcedureOccurrenceIntegrationTest, ProcedureValidationErrors) {
    ProcedureOccurrence invalid_procedure;
    
    // Create procedure with missing required fields
    invalid_procedure.procedure_occurrence_id = 0; // Invalid ID
    invalid_procedure.person_id = 0; // Invalid person ID
    invalid_procedure.procedure_concept_id = 0; // Invalid concept
    invalid_procedure.procedure_date = uk_procedure_date_2024;
    invalid_procedure.procedure_type_concept_id = 0; // Invalid type
    
    // Validate should return false
    EXPECT_FALSE(invalid_procedure.validate());
    
    // Check specific validation errors
    auto errors = invalid_procedure.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_GE(errors.size(), 4); // At least 4 validation errors
    
    // Verify error messages
    bool has_proc_id_error = false;
    bool has_person_id_error = false;
    bool has_concept_error = false;
    bool has_type_error = false;
    
    for (const auto& error : errors) {
        if (error.find("procedure_occurrence_id must be positive") != std::string::npos) {
            has_proc_id_error = true;
        }
        if (error.find("person_id must be positive") != std::string::npos) {
            has_person_id_error = true;
        }
        if (error.find("procedure_concept_id must be positive") != std::string::npos) {
            has_concept_error = true;
        }
        if (error.find("procedure_type_concept_id must be positive") != std::string::npos) {
            has_type_error = true;
        }
    }
    
    EXPECT_TRUE(has_proc_id_error);
    EXPECT_TRUE(has_person_id_error);
    EXPECT_TRUE(has_concept_error);
    EXPECT_TRUE(has_type_error);
}

// Tests bulk procedure creation performance
TEST_F(ProcedureOccurrenceIntegrationTest, BulkProcedureCreationPerformance) {
    const int num_procedures = 1000;
    std::vector<ProcedureOccurrence> procedures;
    procedures.reserve(num_procedures);
    
    auto start_time = high_resolution_clock::now();
    
    // Create bulk procedures
    for (int i = 0; i < num_procedures; ++i) {
        ProcedureOccurrence proc;
        proc.procedure_occurrence_id = 5000000 + i;
        proc.person_id = 1000000 + (i % 100);
        proc.procedure_concept_id = uk_physiotherapy_concept;
        proc.procedure_date = uk_procedure_date_2024;
        proc.procedure_type_concept_id = uk_outpatient_procedure_concept;
        proc.provider_id = 50045; // Physiotherapist
        proc.procedure_source_value = "BULK_PHYSIO_" + std::to_string(i);
        
        procedures.push_back(std::move(proc));
    }
    
    auto end_time = high_resolution_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    // Validate all procedures
    for (const auto& proc : procedures) {
        EXPECT_TRUE(proc.validate());
    }
    
    // Performance should be reasonable (less than 1000ms for 1000 procedures)
    EXPECT_LT(duration.count(), 1000);
    EXPECT_EQ(procedures.size(), num_procedures);
}

// Tests procedure field visitor implementation
TEST_F(ProcedureOccurrenceIntegrationTest, FieldVisitorImplementation) {
    class TestVisitor : public FieldVisitor {
    public:
        std::map<std::string, std::string> visited_fields;
        
        void visit(const std::string& name, const std::any& /* value */) override {
            // Simple string representation for testing
            visited_fields[name] = "visited";
        }
    };
    
    ProcedureOccurrence procedure;
    procedure.procedure_occurrence_id = 5000013;
    procedure.person_id = 1000001;
    procedure.procedure_concept_id = uk_appendectomy_concept;
    procedure.procedure_date = uk_procedure_date_2024;
    procedure.procedure_type_concept_id = uk_inpatient_procedure_concept;
    procedure.provider_id = uk_surgeon_provider_id;
    procedure.procedure_source_value = "OPCS4_H01.1";
    
    TestVisitor visitor;
    procedure.visit_fields(visitor);
    
    // Verify all procedure occurrence fields were visited
    EXPECT_EQ(visitor.visited_fields.size(), 16); // Total procedure occurrence fields
    EXPECT_TRUE(visitor.visited_fields.count("procedure_occurrence_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("person_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("procedure_concept_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("procedure_date") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("procedure_type_concept_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("procedure_source_value") > 0);
}

// Tests complex NHS surgical procedures with multiple modifiers
TEST_F(ProcedureOccurrenceIntegrationTest, ComplexNHSSurgicalProcedures) {
    ProcedureOccurrence complex_surgery;
    
    // Set complex robotic-assisted surgery
    complex_surgery.procedure_occurrence_id = 5000014;
    complex_surgery.person_id = 1000011;
    complex_surgery.procedure_concept_id = 4139847; // Robotic prostatectomy
    complex_surgery.procedure_date = uk_procedure_date_2024;
    complex_surgery.procedure_datetime = uk_procedure_datetime_2024;
    complex_surgery.procedure_end_datetime = createUKDateTime(15, 8, 2024, 15, 30); // 6-hour surgery
    complex_surgery.procedure_type_concept_id = uk_inpatient_procedure_concept;
    complex_surgery.modifier_concept_id = uk_robotic_modifier;
    complex_surgery.quantity = 1;
    complex_surgery.provider_id = uk_surgeon_provider_id;
    complex_surgery.visit_occurrence_id = 2000011;
    complex_surgery.visit_detail_id = 3000011;
    complex_surgery.procedure_source_value = "OPCS4_M61.2"; // Radical prostatectomy
    complex_surgery.modifier_source_value = "Robotic-assisted laparoscopic";
    
    // Validate complex surgery
    EXPECT_TRUE(complex_surgery.validate());
    
    // Test complex surgery duration (should be 6 hours)
    auto duration = duration_cast<hours>(
        complex_surgery.procedure_end_datetime.value() - complex_surgery.procedure_datetime.value()
    );
    EXPECT_EQ(duration.count(), 6); // 6-hour surgery
    
    // Verify complex modifiers
    EXPECT_TRUE(complex_surgery.modifier_concept_id.has_value());
    EXPECT_EQ(complex_surgery.modifier_concept_id.value(), uk_robotic_modifier);
    
    // Test SQL generation includes complex data
    auto sql = complex_surgery.to_insert_sql(true);
    EXPECT_NE(sql.find("OPCS4_M61.2"), std::string::npos);
    EXPECT_NE(sql.find("Robotic-assisted laparoscopic"), std::string::npos);
}

// Tests bilateral procedures
TEST_F(ProcedureOccurrenceIntegrationTest, BilateralProcedures) {
    ProcedureOccurrence bilateral_procedure;
    
    // Set bilateral cataract surgery
    bilateral_procedure.procedure_occurrence_id = 5000015;
    bilateral_procedure.person_id = 1000012;
    bilateral_procedure.procedure_concept_id = uk_cataract_surgery_concept;
    bilateral_procedure.procedure_date = uk_procedure_date_2024;
    bilateral_procedure.procedure_type_concept_id = uk_day_case_procedure_concept;
    bilateral_procedure.modifier_concept_id = uk_bilateral_modifier;
    bilateral_procedure.quantity = 2; // Both eyes
    bilateral_procedure.provider_id = uk_surgeon_provider_id;
    bilateral_procedure.procedure_source_value = "OPCS4_C71.2"; // Bilateral cataract
    bilateral_procedure.modifier_source_value = "Bilateral";
    
    // Validate bilateral procedure
    EXPECT_TRUE(bilateral_procedure.validate());
    
    // Test bilateral-specific fields
    EXPECT_EQ(bilateral_procedure.modifier_concept_id.value(), uk_bilateral_modifier);
    EXPECT_EQ(bilateral_procedure.quantity.value(), 2); // Both sides
    
    // Verify SQL contains bilateral data
    auto sql = bilateral_procedure.to_insert_sql(true);
    EXPECT_NE(sql.find("Bilateral"), std::string::npos);
    EXPECT_NE(sql.find("2"), std::string::npos); // Quantity = 2
} 