/**
 * @file test_drug_exposure_integration.cpp
 * @brief Integration tests for DrugExposure table with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <memory>
#include <sstream>
#include <regex>
#include <iomanip>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

class DrugExposureIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up UK locale for date formatting and decimal notation
        std::setlocale(LC_ALL, "en_GB.UTF-8");
    }

    // Helper to create UK date from DD/MM/YYYY string
    system_clock::time_point parse_uk_date(const std::string& date_str) {
        std::regex date_regex(R"((\d{2})/(\d{2})/(\d{4}))");
        std::smatch matches;
        
        if (std::regex_match(date_str, matches, date_regex)) {
            int day = std::stoi(matches[1]);
            int month = std::stoi(matches[2]);
            int year = std::stoi(matches[3]);
            
            std::tm tm = {};
            tm.tm_year = year - 1900;
            tm.tm_mon = month - 1;
            tm.tm_mday = day;
            tm.tm_hour = 9; // 9 AM UK time (typical prescription time)
            
            return system_clock::from_time_t(std::mktime(&tm));
        }
        
        return system_clock::time_point{};
    }
    
    // Helper to format UK currency
    std::string format_uk_currency(double amount) {
        std::stringstream ss;
        ss << "£" << std::fixed << std::setprecision(2) << amount;
        return ss.str();
    }
};

// Test basic DrugExposure record creation with required fields
TEST_F(DrugExposureIntegrationTest, CreateDrugExposureWithRequiredFields) {
    DrugExposure drug;
    drug.drug_exposure_id = 10001;
    drug.person_id = 20001;
    drug.drug_concept_id = 19073193; // Paracetamol concept
    drug.drug_exposure_start_date = parse_uk_date("01/04/2023");
    drug.drug_exposure_end_date = parse_uk_date("07/04/2023");
    drug.drug_type_concept_id = 38000177; // Prescription written
    
    EXPECT_EQ(drug.drug_exposure_id, 10001);
    EXPECT_EQ(drug.person_id, 20001);
    EXPECT_EQ(drug.table_name(), "drug_exposure");
    EXPECT_EQ(drug.schema_name(), "cdm");
}

// Test DrugExposure record with all optional fields populated
TEST_F(DrugExposureIntegrationTest, CreateDrugExposureWithAllFields) {
    DrugExposure drug;
    drug.drug_exposure_id = 10002;
    drug.person_id = 20002;
    drug.drug_concept_id = 1125315; // Metformin
    drug.drug_exposure_start_date = parse_uk_date("15/06/2023");
    drug.drug_exposure_start_datetime = parse_uk_date("15/06/2023");
    drug.drug_exposure_end_date = parse_uk_date("14/07/2023");
    drug.drug_exposure_end_datetime = parse_uk_date("14/07/2023");
    drug.verbatim_end_date = parse_uk_date("14/07/2023");
    drug.drug_type_concept_id = 38000177;
    drug.stop_reason = "Adverse reaction";
    drug.refills = 2;
    drug.quantity = 84.0; // 3 tablets daily for 28 days
    drug.days_supply = 28;
    drug.sig = "Take 500mg three times daily with meals";
    drug.route_concept_id = 4128794; // Oral route
    drug.lot_number = "LOT123UK";
    drug.provider_id = 30001;
    drug.visit_occurrence_id = 40001;
    drug.visit_detail_id = 50001;
    drug.drug_source_value = "UK-MET-500";
    drug.drug_source_concept_id = 0;
    drug.route_source_value = "PO";
    drug.dose_unit_source_value = "mg";
    
    EXPECT_TRUE(drug.drug_exposure_start_datetime.has_value());
    EXPECT_TRUE(drug.stop_reason.has_value());
    EXPECT_EQ(drug.quantity.value(), 84.0);
    EXPECT_EQ(drug.days_supply.value(), 28);
    EXPECT_EQ(drug.sig.value(), "Take 500mg three times daily with meals");
}

// Test validation with valid DrugExposure record
TEST_F(DrugExposureIntegrationTest, ValidateValidDrugExposure) {
    DrugExposure drug;
    drug.drug_exposure_id = 10003;
    drug.person_id = 20003;
    drug.drug_concept_id = 40213154; // Simvastatin
    drug.drug_exposure_start_date = parse_uk_date("01/01/2023");
    drug.drug_exposure_end_date = parse_uk_date("31/01/2023");
    drug.drug_type_concept_id = 38000177;
    drug.quantity = 30.0;
    drug.days_supply = 30;
    
    EXPECT_TRUE(drug.validate());
    auto errors = drug.validation_errors();
    EXPECT_TRUE(errors.empty());
}

// Test validation with invalid drug_exposure_id
TEST_F(DrugExposureIntegrationTest, ValidateInvalidDrugExposureId) {
    DrugExposure drug;
    drug.drug_exposure_id = 0; // Invalid
    drug.person_id = 20004;
    drug.drug_concept_id = 1125315;
    drug.drug_exposure_start_date = parse_uk_date("01/02/2023");
    drug.drug_exposure_end_date = parse_uk_date("28/02/2023");
    drug.drug_type_concept_id = 38000177;
    
    EXPECT_FALSE(drug.validate());
    auto errors = drug.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("drug_exposure_id") != std::string::npos; }));
}

// Test validation with end date before start date
TEST_F(DrugExposureIntegrationTest, ValidateInvalidDateRange) {
    DrugExposure drug;
    drug.drug_exposure_id = 10004;
    drug.person_id = 20005;
    drug.drug_concept_id = 1125315;
    drug.drug_exposure_start_date = parse_uk_date("15/03/2023");
    drug.drug_exposure_end_date = parse_uk_date("01/03/2023"); // Before start
    drug.drug_type_concept_id = 38000177;
    
    EXPECT_FALSE(drug.validate());
    auto errors = drug.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("start_date") != std::string::npos; }));
}

// Test validation with negative quantity
TEST_F(DrugExposureIntegrationTest, ValidateInvalidQuantity) {
    DrugExposure drug;
    drug.drug_exposure_id = 10005;
    drug.person_id = 20006;
    drug.drug_concept_id = 1125315;
    drug.drug_exposure_start_date = parse_uk_date("01/04/2023");
    drug.drug_exposure_end_date = parse_uk_date("30/04/2023");
    drug.drug_type_concept_id = 38000177;
    drug.quantity = -10.0; // Invalid negative
    
    EXPECT_FALSE(drug.validate());
    auto errors = drug.validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("quantity") != std::string::npos; }));
}

// Test validation with negative days_supply
TEST_F(DrugExposureIntegrationTest, ValidateInvalidDaysSupply) {
    DrugExposure drug;
    drug.drug_exposure_id = 10006;
    drug.person_id = 20007;
    drug.drug_concept_id = 1125315;
    drug.drug_exposure_start_date = parse_uk_date("01/05/2023");
    drug.drug_exposure_end_date = parse_uk_date("31/05/2023");
    drug.drug_type_concept_id = 38000177;
    drug.days_supply = -5; // Invalid negative
    
    EXPECT_FALSE(drug.validate());
    auto errors = drug.validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("days_supply") != std::string::npos; }));
}

// Test SQL generation with proper escaping
TEST_F(DrugExposureIntegrationTest, GenerateInsertSqlWithEscaping) {
    DrugExposure drug;
    drug.drug_exposure_id = 10007;
    drug.person_id = 20008;
    drug.drug_concept_id = 19073193;
    drug.drug_exposure_start_date = parse_uk_date("01/06/2023");
    drug.drug_exposure_end_date = parse_uk_date("07/06/2023");
    drug.drug_type_concept_id = 38000177;
    drug.sig = "Take O'Brien's formula twice daily"; // Test apostrophe escaping
    drug.drug_source_value = "Para'cetamol 500mg";
    
    std::string sql = drug.to_insert_sql(true);
    
    EXPECT_TRUE(sql.find("INSERT INTO cdm.drug_exposure") != std::string::npos);
    EXPECT_TRUE(sql.find("10007") != std::string::npos);
    EXPECT_TRUE(sql.find("O''Brien''s formula") != std::string::npos); // Properly escaped
    EXPECT_TRUE(sql.find("Para''cetamol") != std::string::npos);
}

// Test UK-specific drug formulations
TEST_F(DrugExposureIntegrationTest, UkDrugFormulations) {
    DrugExposure drug;
    drug.drug_exposure_id = 10008;
    drug.person_id = 20009;
    drug.drug_concept_id = 19073193; // Paracetamol
    drug.drug_exposure_start_date = parse_uk_date("15/07/2023");
    drug.drug_exposure_end_date = parse_uk_date("22/07/2023");
    drug.drug_type_concept_id = 38000175; // NHS dispensing
    drug.quantity = 32.0; // UK pack size (2 x 16)
    drug.days_supply = 8;
    drug.sig = "Two tablets four times daily";
    drug.drug_source_value = "PARA500UK"; // UK BNF code format
    drug.dose_unit_source_value = "tablet";
    
    EXPECT_TRUE(drug.validate());
    
    std::string sql = drug.to_insert_sql();
    EXPECT_TRUE(sql.find("PARA500UK") != std::string::npos);
    EXPECT_TRUE(sql.find("32") != std::string::npos); // UK pack size
}

// Test NHS prescription number tracking
TEST_F(DrugExposureIntegrationTest, NhsPrescriptionNumberTracking) {
    DrugExposure drug;
    drug.drug_exposure_id = 10009;
    drug.person_id = 20010;
    drug.drug_concept_id = 1125315;
    drug.drug_exposure_start_date = parse_uk_date("01/08/2023");
    drug.drug_exposure_end_date = parse_uk_date("31/08/2023");
    drug.drug_type_concept_id = 38000177;
    drug.lot_number = "NHS-RX-2023-080123"; // NHS prescription format
    drug.drug_source_value = "MET500";
    
    EXPECT_TRUE(drug.validate());
    
    std::string sql = drug.to_insert_sql();
    EXPECT_TRUE(sql.find("NHS-RX-2023-080123") != std::string::npos);
}

// Test concurrent DrugExposure record creation
TEST_F(DrugExposureIntegrationTest, ConcurrentDrugExposureCreation) {
    const int num_threads = 10;
    std::vector<std::thread> threads;
    std::vector<std::unique_ptr<DrugExposure>> drug_records(num_threads);
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&drug_records, i, this]() {
            auto drug = std::make_unique<DrugExposure>();
            drug->drug_exposure_id = 20000 + i;
            drug->person_id = 30000 + i;
            drug->drug_concept_id = 19073193;
            drug->drug_exposure_start_date = parse_uk_date("01/01/2023") + hours(24 * i);
            drug->drug_exposure_end_date = drug->drug_exposure_start_date + hours(24 * 7);
            drug->drug_type_concept_id = 38000177;
            drug->quantity = 28.0;
            drug->days_supply = 7;
            drug_records[i] = std::move(drug);
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    for (int i = 0; i < num_threads; ++i) {
        ASSERT_NE(drug_records[i], nullptr);
        EXPECT_EQ(drug_records[i]->drug_exposure_id, 20000 + i);
        EXPECT_TRUE(drug_records[i]->validate());
    }
}

// Test UK decimal notation for quantities
TEST_F(DrugExposureIntegrationTest, UkDecimalNotation) {
    DrugExposure drug;
    drug.drug_exposure_id = 10010;
    drug.person_id = 20011;
    drug.drug_concept_id = 1125315;
    drug.drug_exposure_start_date = parse_uk_date("01/09/2023");
    drug.drug_exposure_end_date = parse_uk_date("30/09/2023");
    drug.drug_type_concept_id = 38000177;
    drug.quantity = 90.5; // UK decimal notation
    drug.days_supply = 30;
    
    EXPECT_TRUE(drug.validate());
    EXPECT_EQ(drug.quantity.value(), 90.5);
    
    std::string sql = drug.to_insert_sql();
    EXPECT_TRUE(sql.find("90.5") != std::string::npos);
}

// Test field visitor pattern
TEST_F(DrugExposureIntegrationTest, FieldVisitorPattern) {
    class TestVisitor : public FieldVisitor {
    public:
        std::vector<std::pair<std::string, std::any>> visited_fields;
        
        void visit(const std::string& name, const std::any& value) override {
            visited_fields.push_back({name, value});
        }
    };
    
    DrugExposure drug;
    drug.drug_exposure_id = 10011;
    drug.person_id = 20012;
    drug.drug_concept_id = 19073193;
    drug.drug_exposure_start_date = parse_uk_date("01/10/2023");
    drug.drug_exposure_end_date = parse_uk_date("07/10/2023");
    drug.drug_type_concept_id = 38000177;
    drug.sig = "Take with food";
    drug.quantity = 14.0;
    
    TestVisitor visitor;
    drug.visit_fields(visitor);
    
    EXPECT_EQ(visitor.visited_fields.size(), 23); // All fields
    EXPECT_EQ(visitor.visited_fields[0].first, "drug_exposure_id");
    EXPECT_EQ(std::any_cast<int64_t>(visitor.visited_fields[0].second), 10011);
}

// Test batch validation of drug exposures
TEST_F(DrugExposureIntegrationTest, BatchDrugExposureValidation) {
    std::vector<DrugExposure> drugs;
    
    // Create mix of valid and invalid records
    for (int i = 0; i < 50; ++i) {
        DrugExposure drug;
        
        if (i % 10 == 0) {
            // Invalid: zero drug_exposure_id
            drug.drug_exposure_id = 0;
            drug.person_id = 40000 + i;
        } else if (i % 10 == 5) {
            // Invalid: negative quantity
            drug.drug_exposure_id = 30000 + i;
            drug.person_id = 40000 + i;
            drug.quantity = -10.0;
        } else {
            // Valid
            drug.drug_exposure_id = 30000 + i;
            drug.person_id = 40000 + i;
            drug.quantity = 30.0;
            drug.days_supply = 30;
        }
        
        drug.drug_concept_id = 19073193;
        drug.drug_exposure_start_date = parse_uk_date("01/01/2023");
        drug.drug_exposure_end_date = parse_uk_date("31/01/2023");
        drug.drug_type_concept_id = 38000177;
        
        drugs.push_back(std::move(drug));
    }
    
    int valid_count = 0;
    int invalid_count = 0;
    
    for (const auto& drug : drugs) {
        if (drug.validate()) {
            valid_count++;
        } else {
            invalid_count++;
        }
    }
    
    EXPECT_EQ(valid_count, 40);
    EXPECT_EQ(invalid_count, 10);
}

// Test UK-specific controlled drug handling
TEST_F(DrugExposureIntegrationTest, UkControlledDrugHandling) {
    DrugExposure drug;
    drug.drug_exposure_id = 10012;
    drug.person_id = 20013;
    drug.drug_concept_id = 1126658; // Morphine (controlled drug)
    drug.drug_exposure_start_date = parse_uk_date("01/11/2023");
    drug.drug_exposure_end_date = parse_uk_date("07/11/2023");
    drug.drug_type_concept_id = 38000177;
    drug.quantity = 14.0; // Limited quantity for controlled drugs
    drug.days_supply = 7; // Short supply period
    drug.sig = "10mg twice daily for severe pain";
    drug.drug_source_value = "CD2-MORPH-10"; // UK controlled drug schedule 2
    
    EXPECT_TRUE(drug.validate());
    EXPECT_LE(drug.days_supply.value(), 30); // UK controlled drug regulation
}

// Test prescription cost tracking (UK NHS)
TEST_F(DrugExposureIntegrationTest, NhsPrescriptionCostTracking) {
    DrugExposure drug;
    drug.drug_exposure_id = 10013;
    drug.person_id = 20014;
    drug.drug_concept_id = 40213154; // Simvastatin
    drug.drug_exposure_start_date = parse_uk_date("01/12/2023");
    drug.drug_exposure_end_date = parse_uk_date("31/12/2023");
    drug.drug_type_concept_id = 38000175; // NHS dispensing
    drug.quantity = 30.0;
    drug.days_supply = 30;
    drug.drug_source_value = "SIM20-£9.65"; // NHS prescription charge
    
    EXPECT_TRUE(drug.validate());
    
    // Verify UK currency format is preserved
    std::string sql = drug.to_insert_sql();
    EXPECT_TRUE(sql.find("£9.65") != std::string::npos);
}

// Test maximum field lengths
TEST_F(DrugExposureIntegrationTest, MaximumFieldLengths) {
    DrugExposure drug;
    drug.drug_exposure_id = 9223372036854775807; // Max int64_t
    drug.person_id = 9223372036854775806;
    drug.drug_concept_id = **********; // Max int32_t
    drug.drug_exposure_start_date = parse_uk_date("01/01/2023");
    drug.drug_exposure_end_date = parse_uk_date("31/01/2023");
    drug.drug_type_concept_id = 38000177;
    
    // Create maximum length strings (50 characters)
    drug.drug_source_value = std::string(50, 'X');
    drug.stop_reason = std::string(20, 'Y');
    drug.lot_number = std::string(50, 'Z');
    
    EXPECT_TRUE(drug.validate());
    
    std::string sql = drug.to_insert_sql();
    EXPECT_TRUE(sql.find("9223372036854775807") != std::string::npos);
}

// Test serialization and deserialization
TEST_F(DrugExposureIntegrationTest, SerializationRoundTrip) {
    DrugExposure original;
    original.drug_exposure_id = 10014;
    original.person_id = 20015;
    original.drug_concept_id = 1125315;
    original.drug_exposure_start_date = parse_uk_date("15/01/2023");
    original.drug_exposure_end_date = parse_uk_date("14/02/2023");
    original.drug_type_concept_id = 38000177;
    original.quantity = 60.0;
    original.days_supply = 30;
    original.sig = "Take one tablet daily";
    original.route_concept_id = 4128794;
    
    // Get field names and values
    auto names = original.field_names();
    auto values = original.field_values();
    
    EXPECT_EQ(names.size(), 23);
    EXPECT_EQ(values.size(), 23);
    
    // Verify critical fields
    EXPECT_EQ(std::any_cast<int64_t>(values[0]), 10014); // drug_exposure_id
    EXPECT_EQ(std::any_cast<double>(values[11]), 60.0); // quantity
    EXPECT_EQ(std::any_cast<int32_t>(values[12]), 30); // days_supply
}

// Test integration with schema definitions
TEST_F(DrugExposureIntegrationTest, SchemaIntegration) {
    auto& schema_def = SchemaDefinitions::instance();
    auto table_def = schema_def.get_table("drug_exposure");
    
    ASSERT_NE(table_def, nullptr);
    EXPECT_EQ(table_def->get_name(), "drug_exposure");
    
    // Verify fields match
    DrugExposure drug;
    auto field_names = drug.field_names();
    auto schema_fields = table_def->get_fields();
    
    EXPECT_EQ(field_names.size(), schema_fields.size());
    
    for (size_t i = 0; i < field_names.size(); ++i) {
        EXPECT_EQ(field_names[i], schema_fields[i].name);
    }
}

// Test factory creation
TEST_F(DrugExposureIntegrationTest, FactoryCreation) {
    auto table = OmopTableFactory::create("drug_exposure");
    
    ASSERT_NE(table, nullptr);
    EXPECT_EQ(table->table_name(), "drug_exposure");
    
    // Cast to DrugExposure and test
    if (auto* drug = dynamic_cast<DrugExposure*>(table.get())) {
        drug->drug_exposure_id = 10015;
        drug->person_id = 20016;
        drug->drug_concept_id = 19073193;
        drug->drug_exposure_start_date = parse_uk_date("01/03/2023");
        drug->drug_exposure_end_date = parse_uk_date("07/03/2023");
        drug->drug_type_concept_id = 38000177;
        
        EXPECT_TRUE(drug->validate());
    } else {
        FAIL() << "Failed to cast to DrugExposure type";
    }
}

// Test UK formulary compliance
TEST_F(DrugExposureIntegrationTest, UkFormularyCompliance) {
    DrugExposure drug;
    drug.drug_exposure_id = 10016;
    drug.person_id = 20017;
    drug.drug_concept_id = 19122891; // Omeprazole (common UK PPI)
    drug.drug_exposure_start_date = parse_uk_date("01/04/2023");
    drug.drug_exposure_end_date = parse_uk_date("28/04/2023");
    drug.drug_type_concept_id = 38000175; // NHS dispensing
    drug.quantity = 28.0; // UK standard 4-week supply
    drug.days_supply = 28;
    drug.sig = "One 20mg capsule daily before breakfast";
    drug.drug_source_value = "0401020P0AAAAAA"; // UK dm+d code format
    drug.dose_unit_source_value = "capsule";
    
    EXPECT_TRUE(drug.validate());
    
    // Verify UK formulary code format
    std::string sql = drug.to_insert_sql();
    EXPECT_TRUE(sql.find("0401020P0AAAAAA") != std::string::npos);
} 