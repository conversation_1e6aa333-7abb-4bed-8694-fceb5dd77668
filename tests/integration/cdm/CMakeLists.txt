# CDM integration tests
set(CDM_INTEGRATION_TEST_SOURCES
    test_omop_tables_integration.cpp
    test_schema_creation_integration.cpp
    test_table_definitions_integration.cpp
    test_database_operations_integration.cpp
    test_cdm_comprehensive_integration.cpp
    # Individual table integration tests with full ETL functionality
    condition_occurrence/test_condition_occurrence_integration.cpp
    observation_period/test_observation_period_integration.cpp
    person/test_person_integration.cpp
    visit_detail/test_visit_detail_integration.cpp
    visit_occurrence/test_visit_occurrence_integration.cpp
    death/test_death_integration.cpp
    drug_exposure/test_drug_exposure_integration.cpp
    measurement/test_measurement_integration.cpp
    note/test_note_integration.cpp
    observation/test_observation_integration.cpp
    procedure_occurrence/test_procedure_occurrence_integration.cpp
)

add_executable(cdm_integration_tests ${CDM_INTEGRATION_TEST_SOURCES})

target_link_libraries(cdm_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
        gmock_main
)

target_include_directories(cdm_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME cdm_integration_tests
    COMMAND cdm_integration_tests
)

set_tests_properties(cdm_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;cdm"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)