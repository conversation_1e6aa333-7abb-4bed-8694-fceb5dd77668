/**
 * @file test_database_operations_integration.cpp
 * @brief Comprehensive database integration tests for OMOP CDM with real database operations
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * 
 * This module provides integration tests that actually connect to real databases
 * and perform OMOP CDM operations including table creation, data insertion/retrieval,
 * UK NHS data validation, and referential integrity testing.
 */

#include <gtest/gtest.h>
#include <chrono>
#include <memory>
#include <vector>
#include <map>
#include <regex>
#include <thread>
#include <random>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/database_connection_factory.h"
#include "common/logging.h"
#include "extract/database_connector.h"

using namespace omop::cdm;
using namespace omop::test;
using namespace std::chrono;

/**
 * @brief Comprehensive database operations test fixture for CDM integration tests
 * 
 * This fixture establishes real database connections and provides utilities
 * for testing OMOP CDM operations with UK NHS data scenarios.
 */
class DatabaseOperationsIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger_ = omop::common::Logger::get("db-ops-integration-test");
        
        // Set up UK locale for proper date/number formatting
        std::setlocale(LC_ALL, "en_GB.UTF-8");
        
        // Initialize database connection if available
        try {
            setupDatabaseConnection();
            database_available_ = true;
            logger_->info("Database connection established successfully");
        } catch (const std::exception& e) {
            logger_->warn("Database not available for integration testing: {}", e.what());
            database_available_ = false;
        }
        
        // Initialize UK test data
        setupUKTestData();
    }
    
    void TearDown() override {
        if (database_available_ && connection_) {
            cleanupTestData();
            connection_.reset();
        }
    }

private:
    void setupDatabaseConnection() {
        // Try to establish database connection using available methods
        // This will work in environments with PostgreSQL/MySQL available
        try {
            // First try PostgreSQL (preferred for OMOP CDM)
            config_.host = getEnvOrDefault("TEST_DB_HOST", "localhost");
            config_.port = std::stoi(getEnvOrDefault("TEST_DB_PORT", "5432"));
            config_.database = getEnvOrDefault("TEST_DB_NAME", "omop_test");
            config_.username = getEnvOrDefault("TEST_DB_USER", "omop_user");
            config_.password = getEnvOrDefault("TEST_DB_PASSWORD", "omop_pass");
            
            connection_ = omop::test::DatabaseConnectionFactory::createConnection(
                omop::test::DatabaseConnectionFactory::DatabaseType::PostgreSQL, config_);
                
            if (!connection_ || !connection_->is_connected()) {
                throw std::runtime_error("Failed to establish PostgreSQL connection");
            }
            
            db_type_ = "PostgreSQL";
            logger_->info("Connected to PostgreSQL database: {}@{}:{}/{}", 
                config_.username, config_.host, config_.port, config_.database);
                
        } catch (const std::exception& e) {
            // Fallback to MySQL if PostgreSQL fails
            try {
                config_.port = std::stoi(getEnvOrDefault("TEST_MYSQL_PORT", "3306"));
                connection_ = omop::test::DatabaseConnectionFactory::createConnection(
                    omop::test::DatabaseConnectionFactory::DatabaseType::MySQL, config_);
                    
                if (!connection_ || !connection_->is_connected()) {
                    throw std::runtime_error("Failed to establish MySQL connection");
                }
                
                db_type_ = "MySQL";
                logger_->info("Connected to MySQL database: {}@{}:{}/{}", 
                    config_.username, config_.host, config_.port, config_.database);
                    
            } catch (const std::exception& mysql_e) {
                throw std::runtime_error("No database available - PostgreSQL: " + 
                    std::string(e.what()) + ", MySQL: " + std::string(mysql_e.what()));
            }
        }
    }
    
    void setupUKTestData() {
        // UK NHS patient data for testing
        uk_patients_ = {
            {
                .person_id = 1000001,
                .nhs_number = "************",
                .birth_date = createUKDate(25, 12, 1980),
                .postcode = "SW1A 1AA",
                .gender = "Male"
            },
            {
                .person_id = 1000002, 
                .nhs_number = "************",
                .birth_date = createUKDate(15, 6, 1965),
                .postcode = "M1 1AA", 
                .gender = "Female"
            },
            {
                .person_id = 1000003,
                .nhs_number = "************",
                .birth_date = createUKDate(29, 2, 2000), // Leap year
                .postcode = "B1 1TT",
                .gender = "Male"
            }
        };
        
        // UK clinical measurement data
        uk_measurements_ = {
            {
                .measurement_id = 5000001,
                .person_id = 1000001,
                .concept_id = 3013762, // Blood glucose
                .value = 5.5, // mmol/L (UK units)
                .unit = "mmol/L",
                .date = createUKDate(15, 1, 2024)
            },
            {
                .measurement_id = 5000002,
                .person_id = 1000002,
                .concept_id = 3020891, // Body temperature  
                .value = 37.2, // Celsius (UK standard)
                .unit = "°C",
                .date = createUKDate(20, 3, 2024)
            }
        };
    }
    
    void cleanupTestData() {
        if (!connection_) return;
        
        try {
            // Clean up test data in reverse dependency order
            std::vector<std::string> cleanup_tables = {
                "measurement", "observation", "procedure_occurrence",
                "condition_occurrence", "drug_exposure", "visit_occurrence",
                "observation_period", "person", "location", "care_site", "provider"
            };
            
            for (const auto& table : cleanup_tables) {
                std::string sql = "DELETE FROM cdm." + table + 
                    " WHERE person_id BETWEEN 1000001 AND 1000999";
                try {
                    connection_->execute_update(sql);
                } catch (const std::exception& e) {
                    logger_->debug("Cleanup failed for {}: {}", table, e.what());
                }
            }
            
            logger_->info("Test data cleanup completed");
        } catch (const std::exception& e) {
            logger_->error("Error during test data cleanup: {}", e.what());
        }
    }
    
    std::string getEnvOrDefault(const std::string& env_var, const std::string& default_val) {
        return omop::test::DatabaseConnectionFactory::getEnvOrDefault(env_var, default_val);
    }

protected:
    system_clock::time_point createUKDate(int day, int month, int year) {
        std::tm tm = {};
        tm.tm_mday = day;
        tm.tm_mon = month - 1;
        tm.tm_year = year - 1900;
        tm.tm_hour = 12; // Noon UK time
        return system_clock::from_time_t(std::mktime(&tm));
    }
    struct UKPatientData {
        int64_t person_id;
        std::string nhs_number;
        system_clock::time_point birth_date;
        std::string postcode;
        std::string gender;
    };
    
    struct UKMeasurementData {
        int64_t measurement_id;
        int64_t person_id;
        int32_t concept_id;
        double value;
        std::string unit;
        system_clock::time_point date;
    };
    
    // Test infrastructure
    std::shared_ptr<omop::common::Logger> logger_;
    std::unique_ptr<omop::extract::IDatabaseConnection> connection_;
    omop::test::DatabaseConnectionFactory::ConnectionConfig config_;
    bool database_available_ = false;
    std::string db_type_;
    
    // UK test data
    std::vector<UKPatientData> uk_patients_;
    std::vector<UKMeasurementData> uk_measurements_;
};

/**
 * @brief Test database connection establishment and basic operations
 */
TEST_F(DatabaseOperationsIntegrationTest, DatabaseConnectionAndBasicOperations) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    ASSERT_TRUE(connection_);
    ASSERT_TRUE(connection_->is_connected());
    
    // Test basic database connectivity
    auto result = connection_->execute_query("SELECT 1 as test_value");
    ASSERT_TRUE(result);
    ASSERT_TRUE(result->next());
    
    auto test_value = std::any_cast<int>(result->get_value("test_value"));
    EXPECT_EQ(1, test_value);
    
    logger_->info("Database connection test passed for {}", db_type_);
}

/**
 * @brief Test OMOP CDM schema creation in the database
 */
TEST_F(DatabaseOperationsIntegrationTest, OMOPCDMSchemaCreation) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    // Create CDM schema if it doesn't exist
    try {
        connection_->execute_update("CREATE SCHEMA IF NOT EXISTS cdm");
    } catch (const std::exception& e) {
        logger_->debug("Schema creation note: {}", e.what());
    }
    
    // Get schema definitions
    auto& schema_def = SchemaDefinitions::instance();
    auto creation_order = schema_def.get_creation_order();
    
    EXPECT_FALSE(creation_order.empty());
    
    // Create core OMOP tables in dependency order
    std::vector<std::string> core_tables = {"person", "observation_period", "visit_occurrence"};
    
    for (const auto& table_name : core_tables) {
        if (std::find(creation_order.begin(), creation_order.end(), table_name) == creation_order.end()) {
            continue; // Skip if not in creation order
        }
        
        auto table_def = schema_def.get_table(table_name);
        ASSERT_NE(nullptr, table_def);
        
        // Generate and execute CREATE TABLE SQL
        DatabaseDialect dialect = (db_type_ == "PostgreSQL") ? 
            DatabaseDialect::PostgreSQL : DatabaseDialect::MySQL;
            
        auto create_sql = table_def->generate_create_table_sql("cdm", dialect);
        EXPECT_FALSE(create_sql.empty());
        
        try {
            connection_->execute_update(create_sql);
            logger_->info("Created table: cdm.{}", table_name);
        } catch (const std::exception& e) {
            logger_->debug("Table creation note for {}: {}", table_name, e.what());
        }
        
        // Verify table exists
        std::string check_sql = (db_type_ == "PostgreSQL") ?
            "SELECT 1 FROM information_schema.tables WHERE table_schema='cdm' AND table_name='" + table_name + "'" :
            "SELECT 1 FROM information_schema.tables WHERE table_schema='cdm' AND table_name='" + table_name + "'";
            
        try {
            auto result = connection_->execute_query(check_sql);
            if (result && result->next()) {
                logger_->info("Verified table exists: cdm.{}", table_name);
            }
        } catch (const std::exception& e) {
            logger_->debug("Table verification note: {}", e.what());
        }
    }
}

/**
 * @brief Test UK NHS patient data insertion and retrieval
 */
TEST_F(DatabaseOperationsIntegrationTest, UKNHSPatientDataOperations) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    // Create Person records with UK NHS data
    for (const auto& patient_data : uk_patients_) {
        Person person;
        person.person_id = patient_data.person_id;
        person.gender_concept_id = (patient_data.gender == "Male") ? 8507 : 8532;
        person.year_of_birth = 1980; // Simplified for test
        person.race_concept_id = 8527; // White (UK majority)
        person.ethnicity_concept_id = 0; // Not Hispanic or Latino
        
        // Generate INSERT SQL with UK locale formatting
        auto insert_sql = person.to_insert_sql(true);
        EXPECT_FALSE(insert_sql.empty());
        EXPECT_TRUE(insert_sql.find("INSERT INTO") != std::string::npos);
        
        try {
            connection_->execute_update(insert_sql);
            logger_->info("Inserted UK NHS patient: {} (ID: {})", 
                patient_data.nhs_number, patient_data.person_id);
        } catch (const std::exception& e) {
            logger_->warn("Patient insertion note: {}", e.what());
        }
    }
    
    // Verify UK patient data retrieval
    try {
        auto result = connection_->execute_query(
            "SELECT person_id, gender_concept_id, year_of_birth FROM cdm.person "
            "WHERE person_id BETWEEN 1000001 AND 1000003 ORDER BY person_id");
            
        ASSERT_TRUE(result);
        
        int patient_count = 0;
        while (result->next()) {
            auto person_id = std::any_cast<int64_t>(result->get_value("person_id"));
            auto gender_concept_id = std::any_cast<int>(result->get_value("gender_concept_id"));
            
            EXPECT_GE(person_id, 1000001);
            EXPECT_LE(person_id, 1000003);
            EXPECT_TRUE(gender_concept_id == 8507 || gender_concept_id == 8532);
            
            patient_count++;
        }
        
        EXPECT_EQ(uk_patients_.size(), patient_count);
        logger_->info("Successfully retrieved {} UK NHS patient records", patient_count);
        
    } catch (const std::exception& e) {
        logger_->warn("Patient retrieval note: {}", e.what());
    }
}

/**
 * @brief Test UK clinical measurement data with proper units and formatting
 */
TEST_F(DatabaseOperationsIntegrationTest, UKClinicalMeasurementOperations) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    // Create UK clinical measurements
    for (const auto& measurement_data : uk_measurements_) {
        Measurement measurement;
        measurement.measurement_id = measurement_data.measurement_id;
        measurement.person_id = measurement_data.person_id;
        measurement.measurement_concept_id = measurement_data.concept_id;
        measurement.measurement_date = measurement_data.date;
        measurement.measurement_type_concept_id = 44818702; // Lab result
        measurement.value_as_number = measurement_data.value;
        measurement.unit_concept_id = (measurement_data.unit == "mmol/L") ? 8753 : 586323;
        measurement.unit_source_value = measurement_data.unit;
        
        auto insert_sql = measurement.to_insert_sql(true);
        EXPECT_FALSE(insert_sql.empty());
        
        // Verify UK-specific formatting in SQL
        if (measurement_data.unit == "°C") {
            EXPECT_TRUE(insert_sql.find("37.2") != std::string::npos);
        } else if (measurement_data.unit == "mmol/L") {
            EXPECT_TRUE(insert_sql.find("5.5") != std::string::npos);
        }
        
        try {
            connection_->execute_update(insert_sql);
            logger_->info("Inserted UK measurement: {} {} for patient {}", 
                measurement_data.value, measurement_data.unit, measurement_data.person_id);
        } catch (const std::exception& e) {
            logger_->warn("Measurement insertion note: {}", e.what());
        }
    }
    
    // Verify UK measurement data retrieval with proper formatting
    try {
        auto result = connection_->execute_query(
            "SELECT measurement_id, person_id, value_as_number, unit_source_value "
            "FROM cdm.measurement WHERE measurement_id BETWEEN 5000001 AND 5000010 "
            "ORDER BY measurement_id");
            
        ASSERT_TRUE(result);
        
        int measurement_count = 0;
        while (result->next()) {
            auto measurement_id = std::any_cast<int64_t>(result->get_value("measurement_id"));
            auto person_id = std::any_cast<int64_t>(result->get_value("person_id"));
            auto value = std::any_cast<double>(result->get_value("value_as_number"));
            auto unit = std::any_cast<std::string>(result->get_value("unit_source_value"));
            
            EXPECT_GE(measurement_id, 5000001);
            EXPECT_TRUE(person_id >= 1000001 && person_id <= 1000003);
            
            // Verify UK measurement units and values
            if (unit == "mmol/L") {
                EXPECT_DOUBLE_EQ(5.5, value);
            } else if (unit == "°C") {
                EXPECT_DOUBLE_EQ(37.2, value);
            }
            
            measurement_count++;
        }
        
        EXPECT_EQ(uk_measurements_.size(), measurement_count);
        logger_->info("Successfully retrieved {} UK clinical measurements", measurement_count);
        
    } catch (const std::exception& e) {
        logger_->warn("Measurement retrieval note: {}", e.what());
    }
}

/**
 * @brief Test referential integrity constraints with UK NHS data
 */
TEST_F(DatabaseOperationsIntegrationTest, ReferentialIntegrityWithUKData) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    // Test foreign key constraint validation
    try {
        // Attempt to insert measurement for non-existent patient
        Measurement invalid_measurement;
        invalid_measurement.measurement_id = 9999999;
        invalid_measurement.person_id = 9999999; // Non-existent person_id
        invalid_measurement.measurement_concept_id = 3013762;
        invalid_measurement.measurement_date = createUKDate(1, 1, 2024);
        invalid_measurement.measurement_type_concept_id = 44818702;
        
        auto insert_sql = invalid_measurement.to_insert_sql(true);
        
        // This should fail due to foreign key constraint (if enforced)
        bool constraint_enforced = false;
        try {
            connection_->execute_update(insert_sql);
            logger_->warn("Foreign key constraint not enforced - measurement inserted for non-existent patient");
        } catch (const std::exception& e) {
            constraint_enforced = true;
            logger_->info("Foreign key constraint properly enforced: {}", e.what());
        }
        
        // Log the constraint enforcement status
        logger_->info("Referential integrity constraint enforcement: {}", 
            constraint_enforced ? "ENABLED" : "DISABLED");
            
    } catch (const std::exception& e) {
        logger_->error("Error testing referential integrity: {}", e.what());
    }
}

/**
 * @brief Test UK data validation at database level
 */
TEST_F(DatabaseOperationsIntegrationTest, UKDataValidationDatabase) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    // Test UK NHS number format validation through person records
    std::vector<std::pair<std::string, bool>> nhs_number_tests = {
        {"************", true},   // Valid NHS number format
        {"************", true},   // Valid NHS number format  
        {"INVALID123", false},    // Invalid format
        {"12345", false},         // Too short
        {"", false}               // Empty NHS number
    };
    
    for (const auto& [nhs_number, should_be_valid] : nhs_number_tests) {
        try {
            // Create person record and test NHS number in source value
            Person person;
            person.person_id = 2000000 + rand() % 1000;
            person.gender_concept_id = 8507; // Male
            person.year_of_birth = 1980;
            person.race_concept_id = 8527; // White
            person.person_source_value = nhs_number;
            
            auto insert_sql = person.to_insert_sql(true);
            
            bool insertion_succeeded = false;
            try {
                connection_->execute_update(insert_sql);
                insertion_succeeded = true;
                logger_->info("NHS number validation test - '{}': ACCEPTED", nhs_number);
            } catch (const std::exception& e) {
                logger_->info("NHS number validation test - '{}': REJECTED ({})", nhs_number, e.what());
            }
            
            // Clean up
            if (insertion_succeeded) {
                connection_->execute_update("DELETE FROM cdm.person WHERE person_id = " + 
                    std::to_string(person.person_id));
            }
            
        } catch (const std::exception& e) {
            logger_->debug("NHS number test error for '{}': {}", nhs_number, e.what());
        }
    }
}

/**
 * @brief Test UK postcode validation at database level
 */
TEST_F(DatabaseOperationsIntegrationTest, UKPostcodeValidationDatabase) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    // Test UK postcode format validation through Location records
    std::vector<std::pair<std::string, bool>> postcode_tests = {
        {"SW1A 1AA", true},     // Valid London postcode
        {"M1 1AA", true},       // Valid Manchester postcode  
        {"B1 1TT", true},       // Valid Birmingham postcode
        {"EH1 1YZ", true},      // Valid Edinburgh postcode
        {"CF10 3NP", true},     // Valid Cardiff postcode
        {"INVALID", false},     // Invalid format
        {"12345", false},       // Numbers only
        {"", false}             // Empty postcode
    };
    
    for (const auto& [postcode, should_be_valid] : postcode_tests) {
        try {
            // Create location record and test postcode validation in source value
            Location location;
            location.location_id = 3000000 + rand() % 1000;
            location.address_1 = "123 Test Street";
            location.city = "London";
            location.state = "England";
            location.zip = postcode;
            location.country = "United Kingdom";
            location.location_source_value = postcode;
            
            auto insert_sql = location.to_insert_sql(true);
            
            bool insertion_succeeded = false;
            try {
                connection_->execute_update(insert_sql);
                insertion_succeeded = true;
                logger_->info("UK postcode validation test - '{}': ACCEPTED", postcode);
            } catch (const std::exception& e) {
                logger_->info("UK postcode validation test - '{}': REJECTED ({})", postcode, e.what());
            }
            
            // Clean up
            if (insertion_succeeded) {
                connection_->execute_update("DELETE FROM cdm.location WHERE location_id = " + 
                    std::to_string(location.location_id));
            }
            
        } catch (const std::exception& e) {
            logger_->debug("UK postcode test error for '{}': {}", postcode, e.what());
        }
    }
}

/**
 * @brief Test performance with bulk UK NHS data operations
 */
TEST_F(DatabaseOperationsIntegrationTest, BulkUKDataPerformance) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    const int bulk_size = 100;
    auto start_time = high_resolution_clock::now();
    
    // Generate bulk UK patient data
    std::vector<Person> bulk_patients;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> year_dist(1950, 2020);
    std::uniform_int_distribution<> gender_dist(0, 1);
    
    for (int i = 0; i < bulk_size; ++i) {
        Person person;
        person.person_id = 2000000 + i;
        person.gender_concept_id = gender_dist(gen) ? 8507 : 8532;
        person.year_of_birth = year_dist(gen);
        person.race_concept_id = 8527; // White
        person.ethnicity_concept_id = 0;
        
        bulk_patients.push_back(std::move(person));
    }
    
    // Perform bulk insertion
    int successful_insertions = 0;
    for (const auto& person : bulk_patients) {
        try {
            auto insert_sql = person.to_insert_sql(true);
            connection_->execute_update(insert_sql);
            successful_insertions++;
        } catch (const std::exception& e) {
            logger_->debug("Bulk insertion error: {}", e.what());
        }
    }
    
    auto end_time = high_resolution_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    logger_->info("Bulk operation performance: {} insertions in {} ms ({:.2f} ops/sec)",
        successful_insertions, duration.count(), 
        successful_insertions * 1000.0 / duration.count());
    
    EXPECT_GT(successful_insertions, bulk_size * 0.8); // At least 80% success rate
    
    // Clean up bulk data
    try {
        connection_->execute_update("DELETE FROM cdm.person WHERE person_id BETWEEN 2000000 AND 2000999");
    } catch (const std::exception& e) {
        logger_->debug("Bulk cleanup error: {}", e.what());
    }
}

/**
 * @brief Test transaction handling with UK NHS data operations
 */
TEST_F(DatabaseOperationsIntegrationTest, TransactionHandlingUKData) {
    if (!database_available_) {
        GTEST_SKIP() << "Database not available for integration testing";
    }
    
    try {
        // Begin transaction
        connection_->execute_update("BEGIN");
        
        // Insert UK patient data within transaction
        Person patient;
        patient.person_id = 3000001;
        patient.gender_concept_id = 8507;
        patient.year_of_birth = 1985;
        patient.race_concept_id = 8527;
        
        auto insert_sql = patient.to_insert_sql(true);
        connection_->execute_update(insert_sql);
        
        // Verify data exists within transaction
        auto result = connection_->execute_query(
            "SELECT COUNT(*) as count FROM cdm.person WHERE person_id = 3000001");
        
        ASSERT_TRUE(result);
        ASSERT_TRUE(result->next());
        EXPECT_EQ(1, std::any_cast<int>(result->get_value("count")));
        
        // Rollback transaction
        connection_->execute_update("ROLLBACK");
        
        // Verify data no longer exists after rollback
        result = connection_->execute_query(
            "SELECT COUNT(*) as count FROM cdm.person WHERE person_id = 3000001");
            
        ASSERT_TRUE(result);
        ASSERT_TRUE(result->next());
        EXPECT_EQ(0, std::any_cast<int>(result->get_value("count")));
        
        logger_->info("Transaction rollback test completed successfully");
        
    } catch (const std::exception& e) {
        logger_->warn("Transaction test note: {}", e.what());
        try {
            connection_->execute_update("ROLLBACK");
        } catch (...) {}
    }
}