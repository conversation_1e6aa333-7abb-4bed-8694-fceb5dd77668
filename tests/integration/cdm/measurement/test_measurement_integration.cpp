/**
 * @file test_measurement_integration.cpp
 * @brief Integration tests for Measurement table with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <memory>
#include <sstream>
#include <regex>
#include <iomanip>
#include <cmath>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

class MeasurementIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up UK locale for date formatting and decimal notation
        std::setlocale(LC_ALL, "en_GB.UTF-8");
    }

    // Helper to create UK date from DD/MM/YYYY string
    system_clock::time_point parse_uk_date(const std::string& date_str) {
        std::regex date_regex(R"((\d{2})/(\d{2})/(\d{4}))");
        std::smatch matches;
        
        if (std::regex_match(date_str, matches, date_regex)) {
            int day = std::stoi(matches[1]);
            int month = std::stoi(matches[2]);
            int year = std::stoi(matches[3]);
            
            std::tm tm = {};
            tm.tm_year = year - 1900;
            tm.tm_mon = month - 1;
            tm.tm_mday = day;
            tm.tm_hour = 8; // 8 AM UK time (typical lab test time)
            
            return system_clock::from_time_t(std::mktime(&tm));
        }
        
        return system_clock::time_point{};
    }
    
    // Helper to create UK time from HH:MM string
    system_clock::time_point parse_uk_time(const std::string& date_str, const std::string& time_str) {
        auto date = parse_uk_date(date_str);
        std::regex time_regex(R"((\d{2}):(\d{2}))");
        std::smatch matches;
        
        if (std::regex_match(time_str, matches, time_regex)) {
            int hour = std::stoi(matches[1]);
            int minute = std::stoi(matches[2]);
            
            auto time_t_val = system_clock::to_time_t(date);
            std::tm tm = *std::localtime(&time_t_val);
            tm.tm_hour = hour;
            tm.tm_min = minute;
            
            return system_clock::from_time_t(std::mktime(&tm));
        }
        
        return date;
    }
    
    // Helper to convert Celsius to Fahrenheit (UK uses Celsius)
    double celsius_to_fahrenheit(double celsius) {
        return (celsius * 9.0 / 5.0) + 32.0;
    }
    
    // Helper to extract time string from time_point (HH:MM format)
    std::string extract_time_string(const system_clock::time_point& tp) {
        auto time_t_val = system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t_val);
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(2) << tm.tm_hour 
            << ":" << std::setfill('0') << std::setw(2) << tm.tm_min;
        return oss.str();
    }
};

// Test basic Measurement record creation with required fields
TEST_F(MeasurementIntegrationTest, CreateMeasurementWithRequiredFields) {
    Measurement measurement;
    measurement.measurement_id = 50001;
    measurement.person_id = 60001;
    measurement.measurement_concept_id = 3004249; // Systolic blood pressure
    measurement.measurement_date = parse_uk_date("01/04/2023");
    measurement.measurement_type_concept_id = 44818702; // Lab result
    
    EXPECT_EQ(measurement.measurement_id, 50001);
    EXPECT_EQ(measurement.person_id, 60001);
    EXPECT_EQ(measurement.table_name(), "measurement");
    EXPECT_EQ(measurement.schema_name(), "cdm");
}

// Test Measurement record with all optional fields populated
TEST_F(MeasurementIntegrationTest, CreateMeasurementWithAllFields) {
    Measurement measurement;
    measurement.measurement_id = 50002;
    measurement.person_id = 60002;
    measurement.measurement_concept_id = 3020891; // Body temperature
    measurement.measurement_date = parse_uk_date("15/06/2023");
    measurement.measurement_datetime = parse_uk_time("15/06/2023", "14:30");
    measurement.measurement_time = extract_time_string(parse_uk_time("15/06/2023", "14:30"));
    measurement.measurement_type_concept_id = 44818702;
    measurement.operator_concept_id = 4172703; // Equal operator
    measurement.value_as_number = 37.2; // Celsius (UK standard)
    measurement.value_as_concept_id = 0;
    measurement.unit_concept_id = 586323; // Celsius unit
    measurement.range_low = 36.1;
    measurement.range_high = 37.2;
    measurement.provider_id = 70001;
    measurement.visit_occurrence_id = 80001;
    measurement.visit_detail_id = 90001;
    measurement.measurement_source_value = "TEMP_C";
    measurement.measurement_source_concept_id = 0;
    measurement.unit_source_value = "°C";
    measurement.unit_source_concept_id = 0;
    measurement.value_source_value = "37.2";
    measurement.measurement_event_id = 100001;
    measurement.meas_event_field_concept_id = 0;
    
    EXPECT_TRUE(measurement.measurement_datetime.has_value());
    EXPECT_TRUE(measurement.value_as_number.has_value());
    EXPECT_EQ(measurement.value_as_number.value(), 37.2);
    EXPECT_EQ(measurement.unit_source_value.value(), "°C");
}

// Test validation with valid Measurement record
TEST_F(MeasurementIntegrationTest, ValidateValidMeasurement) {
    Measurement measurement;
    measurement.measurement_id = 50003;
    measurement.person_id = 60003;
    measurement.measurement_concept_id = 3013762; // Serum glucose
    measurement.measurement_date = parse_uk_date("01/07/2023");
    measurement.measurement_type_concept_id = 44818702;
    measurement.value_as_number = 5.5; // mmol/L (UK units)
    measurement.unit_concept_id = 8753; // mmol/L
    measurement.range_low = 3.9;
    measurement.range_high = 5.8;
    
    EXPECT_TRUE(measurement.validate());
    auto errors = measurement.validation_errors();
    EXPECT_TRUE(errors.empty());
}

// Test validation with invalid measurement_id
TEST_F(MeasurementIntegrationTest, ValidateInvalidMeasurementId) {
    Measurement measurement;
    measurement.measurement_id = 0; // Invalid
    measurement.person_id = 60004;
    measurement.measurement_concept_id = 3004249;
    measurement.measurement_date = parse_uk_date("01/08/2023");
    measurement.measurement_type_concept_id = 44818702;
    
    EXPECT_FALSE(measurement.validate());
    auto errors = measurement.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("measurement_id") != std::string::npos; }));
}

// Test validation with invalid range (low > high)
TEST_F(MeasurementIntegrationTest, ValidateInvalidRange) {
    Measurement measurement;
    measurement.measurement_id = 50004;
    measurement.person_id = 60005;
    measurement.measurement_concept_id = 3004249;
    measurement.measurement_date = parse_uk_date("01/09/2023");
    measurement.measurement_type_concept_id = 44818702;
    measurement.range_low = 140.0; // Higher than high
    measurement.range_high = 90.0;  // Lower than low
    
    EXPECT_FALSE(measurement.validate());
    auto errors = measurement.validation_errors();
    EXPECT_TRUE(std::any_of(errors.begin(), errors.end(), 
        [](const std::string& e) { return e.find("range") != std::string::npos; }));
}

// Test UK-specific blood pressure measurements
TEST_F(MeasurementIntegrationTest, UkBloodPressureMeasurement) {
    // Systolic blood pressure
    Measurement systolic;
    systolic.measurement_id = 50005;
    systolic.person_id = 60006;
    systolic.measurement_concept_id = 3004249; // Systolic BP
    systolic.measurement_date = parse_uk_date("15/10/2023");
    systolic.measurement_datetime = parse_uk_time("15/10/2023", "09:30");
    systolic.measurement_type_concept_id = 44818701; // From physical examination
    systolic.value_as_number = 120.0; // mmHg
    systolic.unit_concept_id = 8876; // mmHg
    systolic.range_low = 90.0;
    systolic.range_high = 140.0;
    systolic.measurement_source_value = "BP_SYS";
    
    // Diastolic blood pressure
    Measurement diastolic;
    diastolic.measurement_id = 50006;
    diastolic.person_id = 60006;
    diastolic.measurement_concept_id = 3012888; // Diastolic BP
    diastolic.measurement_date = parse_uk_date("15/10/2023");
    diastolic.measurement_datetime = parse_uk_time("15/10/2023", "09:30");
    diastolic.measurement_type_concept_id = 44818701;
    diastolic.value_as_number = 80.0; // mmHg
    diastolic.unit_concept_id = 8876; // mmHg
    diastolic.range_low = 60.0;
    diastolic.range_high = 90.0;
    diastolic.measurement_source_value = "BP_DIA";
    
    EXPECT_TRUE(systolic.validate());
    EXPECT_TRUE(diastolic.validate());
    
    // Check UK NICE hypertension guidelines
    EXPECT_LT(systolic.value_as_number.value(), 140.0); // Not hypertensive
    EXPECT_LT(diastolic.value_as_number.value(), 90.0); // Not hypertensive
}

// Test UK laboratory measurements with NHS reference ranges
TEST_F(MeasurementIntegrationTest, NhsLaboratoryMeasurements) {
    // HbA1c in UK units (mmol/mol)
    Measurement hba1c;
    hba1c.measurement_id = 50007;
    hba1c.person_id = 60007;
    hba1c.measurement_concept_id = 3004410; // HbA1c
    hba1c.measurement_date = parse_uk_date("01/11/2023");
    hba1c.measurement_type_concept_id = 44818702; // Lab result
    hba1c.value_as_number = 48.0; // mmol/mol (UK units)
    hba1c.unit_concept_id = 8713; // mmol/mol
    hba1c.range_low = 20.0;
    hba1c.range_high = 42.0; // NHS normal range
    hba1c.measurement_source_value = "HBA1C_MMOL";
    hba1c.unit_source_value = "mmol/mol";
    
    EXPECT_TRUE(hba1c.validate());
    
    // Cholesterol in UK units (mmol/L)
    Measurement cholesterol;
    cholesterol.measurement_id = 50008;
    cholesterol.person_id = 60007;
    cholesterol.measurement_concept_id = 3019900; // Total cholesterol
    cholesterol.measurement_date = parse_uk_date("01/11/2023");
    cholesterol.measurement_type_concept_id = 44818702;
    cholesterol.value_as_number = 4.8; // mmol/L
    cholesterol.unit_concept_id = 8753; // mmol/L
    cholesterol.range_low = 0.0;
    cholesterol.range_high = 5.0; // NHS target
    cholesterol.measurement_source_value = "CHOL_TOT";
    
    EXPECT_TRUE(cholesterol.validate());
}

// Test SQL generation with proper escaping
TEST_F(MeasurementIntegrationTest, GenerateInsertSqlWithEscaping) {
    Measurement measurement;
    measurement.measurement_id = 50009;
    measurement.person_id = 60008;
    measurement.measurement_concept_id = 3020891;
    measurement.measurement_date = parse_uk_date("01/12/2023");
    measurement.measurement_type_concept_id = 44818702;
    measurement.value_as_number = 37.5;
    measurement.measurement_source_value = "O'Brien's thermometer"; // Test apostrophe
    measurement.unit_source_value = "°C"; // Special character
    
    std::string sql = measurement.to_insert_sql(true);
    
    EXPECT_TRUE(sql.find("INSERT INTO cdm.measurement") != std::string::npos);
    EXPECT_TRUE(sql.find("50009") != std::string::npos);
    EXPECT_TRUE(sql.find("O''Brien''s thermometer") != std::string::npos); // Escaped
    EXPECT_TRUE(sql.find("°C") != std::string::npos);
}

// Test UK temperature measurements (Celsius)
TEST_F(MeasurementIntegrationTest, UkTemperatureMeasurements) {
    Measurement temp;
    temp.measurement_id = 50010;
    temp.person_id = 60009;
    temp.measurement_concept_id = 3020891; // Body temperature
    temp.measurement_date = parse_uk_date("15/01/2024");
    temp.measurement_datetime = parse_uk_time("15/01/2024", "06:00");
    temp.measurement_type_concept_id = 44818701;
    temp.value_as_number = 38.5; // Celsius (fever in UK)
    temp.unit_concept_id = 586323; // Celsius
    temp.range_low = 36.1;
    temp.range_high = 37.2; // Normal range in Celsius
    temp.measurement_source_value = "TEMP_ORAL_C";
    temp.unit_source_value = "°C";
    
    EXPECT_TRUE(temp.validate());
    
    // Check fever threshold (UK uses 38°C)
    EXPECT_GT(temp.value_as_number.value(), 38.0);
    
    // Convert to Fahrenheit for comparison
    double fahrenheit = celsius_to_fahrenheit(temp.value_as_number.value());
    EXPECT_NEAR(fahrenheit, 101.3, 0.1);
}

// Test concurrent Measurement record creation
TEST_F(MeasurementIntegrationTest, ConcurrentMeasurementCreation) {
    const int num_threads = 10;
    std::vector<std::thread> threads;
    std::vector<std::unique_ptr<Measurement>> measurements(num_threads);
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&measurements, i, this]() {
            auto measurement = std::make_unique<Measurement>();
            measurement->measurement_id = 60000 + i;
            measurement->person_id = 70000 + i;
            measurement->measurement_concept_id = 3004249;
            measurement->measurement_date = parse_uk_date("01/01/2023") + hours(24 * i);
            measurement->measurement_type_concept_id = 44818702;
            measurement->value_as_number = 120.0 + i;
            measurement->unit_concept_id = 8876;
            measurements[i] = std::move(measurement);
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    for (int i = 0; i < num_threads; ++i) {
        ASSERT_NE(measurements[i], nullptr);
        EXPECT_EQ(measurements[i]->measurement_id, 60000 + i);
        EXPECT_TRUE(measurements[i]->validate());
    }
}

// Test field visitor pattern
TEST_F(MeasurementIntegrationTest, FieldVisitorPattern) {
    class TestVisitor : public FieldVisitor {
    public:
        std::vector<std::pair<std::string, std::any>> visited_fields;
        
        void visit(const std::string& name, const std::any& value) override {
            visited_fields.push_back({name, value});
        }
    };
    
    Measurement measurement;
    measurement.measurement_id = 50011;
    measurement.person_id = 60010;
    measurement.measurement_concept_id = 3013762;
    measurement.measurement_date = parse_uk_date("01/02/2024");
    measurement.measurement_type_concept_id = 44818702;
    measurement.value_as_number = 5.8;
    measurement.unit_concept_id = 8753;
    measurement.measurement_source_value = "GLUCOSE_FASTING";
    
    TestVisitor visitor;
    measurement.visit_fields(visitor);
    
    EXPECT_EQ(visitor.visited_fields.size(), 23); // All fields
    EXPECT_EQ(visitor.visited_fields[0].first, "measurement_id");
    EXPECT_EQ(std::any_cast<int64_t>(visitor.visited_fields[0].second), 50011);
}

// Test UK weight measurements (kilograms and stones)
TEST_F(MeasurementIntegrationTest, UkWeightMeasurements) {
    // Weight in kilograms (NHS standard)
    Measurement weight_kg;
    weight_kg.measurement_id = 50012;
    weight_kg.person_id = 60011;
    weight_kg.measurement_concept_id = 3013762; // Body weight
    weight_kg.measurement_date = parse_uk_date("01/03/2024");
    weight_kg.measurement_type_concept_id = 44818701;
    weight_kg.value_as_number = 75.5; // kg
    weight_kg.unit_concept_id = 9529; // kilogram
    weight_kg.measurement_source_value = "WEIGHT_KG";
    weight_kg.unit_source_value = "kg";
    
    EXPECT_TRUE(weight_kg.validate());
    
    // Convert to stones for UK patients (1 stone = 6.35029 kg)
    double stones = weight_kg.value_as_number.value() / 6.35029;
    double whole_stones = std::floor(stones);
    double pounds = (stones - whole_stones) * 14.0;
    
    // Approximately 11 stone 13 pounds
    EXPECT_NEAR(whole_stones, 11.0, 0.5);
    EXPECT_NEAR(pounds, 13.0, 1.0);
}

// Test UK height measurements (centimetres)
TEST_F(MeasurementIntegrationTest, UkHeightMeasurements) {
    Measurement height;
    height.measurement_id = 50013;
    height.person_id = 60012;
    height.measurement_concept_id = 3036277; // Body height
    height.measurement_date = parse_uk_date("01/03/2024");
    height.measurement_type_concept_id = 44818701;
    height.value_as_number = 175.0; // cm (UK uses metric for medical)
    height.unit_concept_id = 8582; // centimeter
    height.measurement_source_value = "HEIGHT_CM";
    height.unit_source_value = "cm";
    
    EXPECT_TRUE(height.validate());
    
    // Convert to feet and inches for reference
    double inches = height.value_as_number.value() / 2.54;
    double feet = std::floor(inches / 12.0);
    double remaining_inches = inches - (feet * 12.0);
    
    // Approximately 5 feet 9 inches
    EXPECT_NEAR(feet, 5.0, 0.1);
    EXPECT_NEAR(remaining_inches, 9.0, 0.5);
}

// Test BMI calculation (UK uses kg/m²)
TEST_F(MeasurementIntegrationTest, UkBmiCalculation) {
    Measurement bmi;
    bmi.measurement_id = 50014;
    bmi.person_id = 60013;
    bmi.measurement_concept_id = 3038553; // BMI
    bmi.measurement_date = parse_uk_date("01/03/2024");
    bmi.measurement_type_concept_id = 44818702;
    bmi.value_as_number = 24.6; // kg/m²
    bmi.unit_concept_id = 9531; // kilogram per square meter
    bmi.range_low = 18.5;  // NHS underweight threshold
    bmi.range_high = 25.0; // NHS overweight threshold
    bmi.measurement_source_value = "BMI_CALC";
    bmi.unit_source_value = "kg/m²";
    
    EXPECT_TRUE(bmi.validate());
    
    // Check NHS BMI categories
    EXPECT_GE(bmi.value_as_number.value(), 18.5); // Not underweight
    EXPECT_LT(bmi.value_as_number.value(), 25.0); // Not overweight
}

// Test batch validation of measurements
TEST_F(MeasurementIntegrationTest, BatchMeasurementValidation) {
    std::vector<Measurement> measurements;
    
    // Create mix of valid and invalid records
    for (int i = 0; i < 50; ++i) {
        Measurement m;
        
        if (i % 10 == 0) {
            // Invalid: zero measurement_id
            m.measurement_id = 0;
            m.person_id = 80000 + i;
        } else if (i % 10 == 5) {
            // Invalid: inverted ranges
            m.measurement_id = 70000 + i;
            m.person_id = 80000 + i;
            m.range_low = 100.0;
            m.range_high = 50.0;
        } else {
            // Valid
            m.measurement_id = 70000 + i;
            m.person_id = 80000 + i;
            m.value_as_number = 120.0 + i;
            m.range_low = 90.0;
            m.range_high = 140.0;
        }
        
        m.measurement_concept_id = 3004249;
        m.measurement_date = parse_uk_date("01/01/2023");
        m.measurement_type_concept_id = 44818702;
        
        measurements.push_back(std::move(m));
    }
    
    int valid_count = 0;
    int invalid_count = 0;
    
    for (const auto& m : measurements) {
        if (m.validate()) {
            valid_count++;
        } else {
            invalid_count++;
        }
    }
    
    EXPECT_EQ(valid_count, 40);
    EXPECT_EQ(invalid_count, 10);
}

// Test NHS pathology lab codes
TEST_F(MeasurementIntegrationTest, NhsPathologyLabCodes) {
    Measurement measurement;
    measurement.measurement_id = 50015;
    measurement.person_id = 60014;
    measurement.measurement_concept_id = 3000963; // Hemoglobin
    measurement.measurement_date = parse_uk_date("01/04/2024");
    measurement.measurement_type_concept_id = 44818702;
    measurement.value_as_number = 14.5; // g/dL
    measurement.unit_concept_id = 8840; // gram per deciliter
    measurement.range_low = 13.5;  // Male reference range
    measurement.range_high = 17.5;
    measurement.measurement_source_value = "XE2pb"; // NHS SNOMED code
    measurement.unit_source_value = "g/dL";
    
    EXPECT_TRUE(measurement.validate());
    
    // Verify NHS pathology code format
    std::string sql = measurement.to_insert_sql();
    EXPECT_TRUE(sql.find("XE2pb") != std::string::npos);
}

// Test maximum field lengths
TEST_F(MeasurementIntegrationTest, MaximumFieldLengths) {
    Measurement measurement;
    measurement.measurement_id = 9223372036854775807; // Max int64_t
    measurement.person_id = 9223372036854775806;
    measurement.measurement_concept_id = **********; // Max int32_t
    measurement.measurement_date = parse_uk_date("01/01/2023");
    measurement.measurement_type_concept_id = 44818702;
    
    // Create maximum length strings (50 characters)
    measurement.measurement_source_value = std::string(50, 'X');
    measurement.unit_source_value = std::string(50, 'Y');
    measurement.value_source_value = std::string(50, 'Z');
    
    EXPECT_TRUE(measurement.validate());
    
    std::string sql = measurement.to_insert_sql();
    EXPECT_TRUE(sql.find("9223372036854775807") != std::string::npos);
}

// Test integration with schema definitions
TEST_F(MeasurementIntegrationTest, SchemaIntegration) {
    auto& schema_def = SchemaDefinitions::instance();
    auto table_def = schema_def.get_table("measurement");
    
    ASSERT_NE(table_def, nullptr);
    EXPECT_EQ(table_def->get_name(), "measurement");
    
    // Verify fields match
    Measurement measurement;
    auto field_names = measurement.field_names();
    auto schema_fields = table_def->get_fields();
    
    EXPECT_EQ(field_names.size(), schema_fields.size());
    
    for (size_t i = 0; i < field_names.size(); ++i) {
        EXPECT_EQ(field_names[i], schema_fields[i].name);
    }
}

// Test factory creation
TEST_F(MeasurementIntegrationTest, FactoryCreation) {
    auto table = OmopTableFactory::create("measurement");
    
    ASSERT_NE(table, nullptr);
    EXPECT_EQ(table->table_name(), "measurement");
    
    // Cast to Measurement and test
    if (auto* measurement = dynamic_cast<Measurement*>(table.get())) {
        measurement->measurement_id = 50016;
        measurement->person_id = 60015;
        measurement->measurement_concept_id = 3004249;
        measurement->measurement_date = parse_uk_date("01/05/2024");
        measurement->measurement_type_concept_id = 44818702;
        
        EXPECT_TRUE(measurement->validate());
    } else {
        FAIL() << "Failed to cast to Measurement type";
    }
}

// Test UK laboratory units conversion
TEST_F(MeasurementIntegrationTest, UkLaboratoryUnitsConversion) {
    // Creatinine in UK units (μmol/L)
    Measurement creatinine;
    creatinine.measurement_id = 50017;
    creatinine.person_id = 60016;
    creatinine.measurement_concept_id = 3016723; // Creatinine
    creatinine.measurement_date = parse_uk_date("01/06/2024");
    creatinine.measurement_type_concept_id = 44818702;
    creatinine.value_as_number = 88.0; // μmol/L (UK units)
    creatinine.unit_concept_id = 8749; // micromole per liter
    creatinine.range_low = 59.0;   // Male reference range
    creatinine.range_high = 104.0;
    creatinine.measurement_source_value = "CREAT";
    creatinine.unit_source_value = "μmol/L";
    
    EXPECT_TRUE(creatinine.validate());
    
    // For reference: US uses mg/dL (1 mg/dL = 88.4 μmol/L)
    double us_value = creatinine.value_as_number.value() / 88.4;
    EXPECT_NEAR(us_value, 1.0, 0.1); // Approximately 1.0 mg/dL
}

// Test UK date and time formatting in measurements
TEST_F(MeasurementIntegrationTest, UkDateTimeFormatting) {
    Measurement measurement;
    measurement.measurement_id = 50018;
    measurement.person_id = 60017;
    measurement.measurement_concept_id = 3004249;
    measurement.measurement_date = parse_uk_date("29/02/2024"); // Leap year
    measurement.measurement_datetime = parse_uk_time("29/02/2024", "23:59");
    measurement.measurement_type_concept_id = 44818702;
    measurement.value_as_number = 130.0;
    
    EXPECT_TRUE(measurement.validate());
    
    std::string formatted_date = measurement.format_uk_date(measurement.measurement_date);
    EXPECT_EQ(formatted_date, "29/02/2024");
} 