/**
 * @file test_visit_occurrence_integration.cpp
 * @brief Integration tests for VisitOccurrence table with UK NHS hospital visit scenarios
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <memory>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for VisitOccurrence integration tests
 */
class VisitOccurrenceIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Common UK dates
        uk_date_2024_01_15 = createUKDate(15, 1, 2024);
        uk_date_2024_01_20 = createUKDate(20, 1, 2024);
        uk_date_2024_03_15 = createUKDate(15, 3, 2024);
        uk_date_2024_03_18 = createUKDate(18, 3, 2024);
        uk_date_2024_06_01 = createUKDate(1, 6, 2024);
        current_datetime = system_clock::now();
    }

    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point createUKDateTime(int day, int month, int year, int hour, int minute) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        timeinfo.tm_hour = hour;
        timeinfo.tm_min = minute;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point uk_date_2024_01_15;
    system_clock::time_point uk_date_2024_01_20;
    system_clock::time_point uk_date_2024_03_15;
    system_clock::time_point uk_date_2024_03_18;
    system_clock::time_point uk_date_2024_06_01;
    system_clock::time_point current_datetime;
};

// Test complete NHS hospital admission
TEST_F(VisitOccurrenceIntegrationTest, CompleteNHSHospitalAdmission) {
    VisitOccurrence visit;
    
    visit.visit_occurrence_id = 1000001;
    visit.person_id = 100001;
    visit.visit_concept_id = 9201; // Inpatient Visit
    visit.visit_start_date = uk_date_2024_03_15;
    visit.visit_end_date = uk_date_2024_03_18;
    visit.visit_type_concept_id = 44818518; // Visit derived from EHR
    
    // Optional fields
    visit.visit_start_datetime = createUKDateTime(15, 3, 2024, 14, 30);
    visit.visit_end_datetime = createUKDateTime(18, 3, 2024, 10, 15);
    visit.provider_id = 50001; // Admitting consultant
    visit.care_site_id = 20001; // NHS Trust
    visit.visit_source_value = "IP-2024-0315-001";
    visit.admitted_from_concept_id = 8870; // Emergency Room
    visit.admitted_from_source_value = "A&E";
    visit.discharged_to_concept_id = 8536; // Home
    visit.discharged_to_source_value = "HOME";
    
    EXPECT_TRUE(visit.validate());
    
    auto sql = visit.to_insert_sql(true);
    EXPECT_NE(sql.find("INSERT INTO cdm.visit_occurrence"), std::string::npos);
    EXPECT_NE(sql.find("IP-2024-0315-001"), std::string::npos);
}

// Test UK visit types
TEST_F(VisitOccurrenceIntegrationTest, UKVisitTypes) {
    struct VisitTypeTest {
        int32_t concept_id;
        std::string description;
        int duration_days;
    };
    
    std::vector<VisitTypeTest> visit_types = {
        {9201, "Inpatient Visit", 5},
        {9202, "Outpatient Visit", 0},
        {9203, "Emergency Room Visit", 0},
        {581476, "Emergency Room and Inpatient Visit", 3},
        {262, "Emergency Room and Inpatient Visit", 7},
        {8971, "Ambulatory Clinic / Center", 0},
        {8940, "Office Visit", 0},
        {8950, "Emergency Room", 0},
        {8717, "Ambulance", 0},
        {581478, "Ambulance Visit", 0}
    };
    
    for (const auto& visit_type : visit_types) {
        VisitOccurrence visit;
        visit.visit_occurrence_id = 2000000 + visit_type.concept_id;
        visit.person_id = 100001;
        visit.visit_concept_id = visit_type.concept_id;
        visit.visit_start_date = uk_date_2024_01_15;
        visit.visit_end_date = uk_date_2024_01_15 + days(visit_type.duration_days);
        visit.visit_type_concept_id = 44818518;
        
        EXPECT_TRUE(visit.validate()) << "Failed for visit type: " << visit_type.description;
    }
}

// Test A&E (Emergency) visits
TEST_F(VisitOccurrenceIntegrationTest, AandEVisits) {
    // Typical A&E visit with admission
    VisitOccurrence ae_visit;
    ae_visit.visit_occurrence_id = 3000001;
    ae_visit.person_id = 100002;
    ae_visit.visit_concept_id = 9203; // Emergency Room Visit
    ae_visit.visit_start_date = uk_date_2024_01_15;
    ae_visit.visit_start_datetime = createUKDateTime(15, 1, 2024, 22, 45);
    ae_visit.visit_end_date = uk_date_2024_01_15;
    ae_visit.visit_end_datetime = createUKDateTime(16, 1, 2024, 2, 30); // 3h 45min visit
    ae_visit.visit_type_concept_id = 44818518;
    ae_visit.care_site_id = 20002; // A&E department
    ae_visit.visit_source_value = "AE-2024-0115-2245";
    ae_visit.discharged_to_concept_id = 8717; // Inpatient admission
    ae_visit.discharged_to_source_value = "ADMIT-WARD";
    
    EXPECT_TRUE(ae_visit.validate());
    
    // Follow-up inpatient admission
    VisitOccurrence admission;
    admission.visit_occurrence_id = 3000002;
    admission.person_id = 100002;
    admission.visit_concept_id = 9201; // Inpatient Visit
    admission.visit_start_date = uk_date_2024_01_15;
    admission.visit_start_datetime = createUKDateTime(16, 1, 2024, 2, 30);
    admission.visit_end_date = uk_date_2024_01_20;
    admission.visit_end_datetime = createUKDateTime(20, 1, 2024, 14, 00);
    admission.visit_type_concept_id = 44818518;
    admission.care_site_id = 20001;
    admission.admitted_from_concept_id = 8870; // Emergency Room
    admission.preceding_visit_occurrence_id = ae_visit.visit_occurrence_id;
    
    EXPECT_TRUE(admission.validate());
}

// Test GP (General Practice) visits
TEST_F(VisitOccurrenceIntegrationTest, GPVisits) {
    std::vector<std::string> gp_visit_reasons = {
        "Annual health check",
        "Medication review",
        "Blood pressure monitoring",
        "Diabetes follow-up",
        "Mental health consultation"
    };
    
    int64_t visit_id = 4000001;
    for (const auto& reason : gp_visit_reasons) {
        VisitOccurrence gp_visit;
        gp_visit.visit_occurrence_id = visit_id++;
        gp_visit.person_id = 100003;
        gp_visit.visit_concept_id = 8940; // Office Visit
        gp_visit.visit_start_date = uk_date_2024_01_15 + days((visit_id % 30));
        gp_visit.visit_end_date = gp_visit.visit_start_date;
        gp_visit.visit_type_concept_id = 44818518;
        gp_visit.provider_id = 50002; // GP
        gp_visit.care_site_id = 20003; // GP surgery
        gp_visit.visit_source_value = "GP-" + std::to_string(visit_id);
        
        EXPECT_TRUE(gp_visit.validate()) << "Failed for GP visit: " << reason;
    }
}

// Test outpatient clinic visits
TEST_F(VisitOccurrenceIntegrationTest, OutpatientClinicVisits) {
    struct ClinicVisit {
        std::string specialty;
        int32_t provider_id;
        int32_t care_site_id;
        std::string source_value;
    };
    
    std::vector<ClinicVisit> clinics = {
        {"Cardiology", 50101, 20101, "CARD-OP"},
        {"Oncology", 50102, 20102, "ONC-OP"},
        {"Neurology", 50103, 20103, "NEUR-OP"},
        {"Gastroenterology", 50104, 20104, "GAST-OP"},
        {"Rheumatology", 50105, 20105, "RHEU-OP"}
    };
    
    int64_t visit_id = 5000001;
    for (const auto& clinic : clinics) {
        VisitOccurrence visit;
        visit.visit_occurrence_id = visit_id++;
        visit.person_id = 100004;
        visit.visit_concept_id = 9202; // Outpatient Visit
        visit.visit_start_date = uk_date_2024_03_15;
        visit.visit_end_date = uk_date_2024_03_15;
        visit.visit_type_concept_id = 44818518;
        visit.provider_id = clinic.provider_id;
        visit.care_site_id = clinic.care_site_id;
        visit.visit_source_value = clinic.source_value + "-2024-0315";
        
        EXPECT_TRUE(visit.validate()) << "Failed for clinic: " << clinic.specialty;
    }
}

// Test admission sources and discharge destinations
TEST_F(VisitOccurrenceIntegrationTest, AdmissionDischargeLocations) {
    struct TransferTest {
        int32_t admitted_from;
        std::string admitted_source;
        int32_t discharged_to;
        std::string discharged_source;
        std::string description;
    };
    
    std::vector<TransferTest> transfers = {
        {8870, "A&E", 8536, "HOME", "Emergency to Home"},
        {8870, "A&E", 8863, "SKILLED", "Emergency to Skilled Nursing"},
        {8536, "HOME", 8536, "HOME", "Elective admission from home"},
        {8650, "NURSHOME", 8650, "NURSHOME", "Nursing home resident"},
        {8716, "OTHER", 8546, "HOSPICE", "Transfer to hospice care"},
        {8892, "TRANSFER", 8536, "HOME", "Transfer from another hospital"}
    };
    
    int64_t visit_id = 6000001;
    for (const auto& transfer : transfers) {
        VisitOccurrence visit;
        visit.visit_occurrence_id = visit_id++;
        visit.person_id = 100005;
        visit.visit_concept_id = 9201;
        visit.visit_start_date = uk_date_2024_01_15;
        visit.visit_end_date = uk_date_2024_01_20;
        visit.visit_type_concept_id = 44818518;
        visit.admitted_from_concept_id = transfer.admitted_from;
        visit.admitted_from_source_value = transfer.admitted_source;
        visit.discharged_to_concept_id = transfer.discharged_to;
        visit.discharged_to_source_value = transfer.discharged_source;
        
        EXPECT_TRUE(visit.validate()) << "Failed for transfer: " << transfer.description;
    }
}

// Test visit chains (preceding visits)
TEST_F(VisitOccurrenceIntegrationTest, VisitChains) {
    // Create a chain of visits: A&E -> Admission -> ICU -> Ward -> Discharge
    std::vector<VisitOccurrence> visit_chain;
    
    // A&E visit
    VisitOccurrence ae_visit;
    ae_visit.visit_occurrence_id = 7000001;
    ae_visit.person_id = 100006;
    ae_visit.visit_concept_id = 9203;
    ae_visit.visit_start_date = uk_date_2024_01_15;
    ae_visit.visit_end_date = uk_date_2024_01_15;
    ae_visit.visit_type_concept_id = 44818518;
    visit_chain.push_back(ae_visit);
    
    // Initial admission
    VisitOccurrence admission;
    admission.visit_occurrence_id = 7000002;
    admission.person_id = 100006;
    admission.visit_concept_id = 9201;
    admission.visit_start_date = uk_date_2024_01_15;
    admission.visit_end_date = uk_date_2024_01_20;
    admission.visit_type_concept_id = 44818518;
    admission.preceding_visit_occurrence_id = ae_visit.visit_occurrence_id;
    visit_chain.push_back(admission);
    
    // Validate all visits in chain
    for (const auto& visit : visit_chain) {
        EXPECT_TRUE(visit.validate());
    }
    
    // Verify chain continuity
    EXPECT_EQ(visit_chain[1].preceding_visit_occurrence_id.value(), 
              visit_chain[0].visit_occurrence_id);
}

// Test visit duration calculations
TEST_F(VisitOccurrenceIntegrationTest, VisitDurationCalculations) {
    struct DurationTest {
        std::string visit_type;
        int expected_days;
        system_clock::time_point start;
        system_clock::time_point end;
    };
    
    std::vector<DurationTest> durations = {
        {"Same day GP visit", 0, uk_date_2024_01_15, uk_date_2024_01_15},
        {"Weekend admission", 2, 
         createUKDate(12, 1, 2024), // Friday
         createUKDate(14, 1, 2024)}, // Sunday
        {"Week-long admission", 7, uk_date_2024_01_15, uk_date_2024_01_15 + days(7)},
        {"Long-term admission", 30, uk_date_2024_01_15, uk_date_2024_01_15 + days(30)}
    };
    
    int64_t visit_id = 8000001;
    for (const auto& test : durations) {
        VisitOccurrence visit;
        visit.visit_occurrence_id = visit_id++;
        visit.person_id = 100007;
        visit.visit_concept_id = 9201;
        visit.visit_start_date = test.start;
        visit.visit_end_date = test.end;
        visit.visit_type_concept_id = 44818518;
        
        EXPECT_TRUE(visit.validate()) << "Failed for: " << test.visit_type;
        
        // Calculate actual duration
        auto actual_days = duration_cast<days>(test.end - test.start).count();
        EXPECT_EQ(actual_days, test.expected_days) << "Duration mismatch for: " << test.visit_type;
    }
}

// Test bulk visit creation performance
TEST_F(VisitOccurrenceIntegrationTest, BulkVisitCreationPerformance) {
    const int num_visits = 10000;
    auto start_time = steady_clock::now();
    
    std::vector<std::unique_ptr<VisitOccurrence>> visits;
    visits.reserve(num_visits);
    
    for (int i = 0; i < num_visits; ++i) {
        auto visit = std::make_unique<VisitOccurrence>();
        visit->visit_occurrence_id = 9000000 + i;
        visit->person_id = 200000 + (i % 1000); // 1000 different patients
        
        // Vary visit types
        std::vector<int32_t> visit_concepts = {9201, 9202, 9203, 8940, 8971};
        visit->visit_concept_id = visit_concepts[i % visit_concepts.size()];
        
        // Random dates within 2024
        int days_offset = i % 365;
        visit->visit_start_date = createUKDate(1, 1, 2024) + days(days_offset);
        
        // Variable duration based on visit type
        int duration = (visit->visit_concept_id == 9201) ? (1 + (i % 14)) : 0;
        visit->visit_end_date = visit->visit_start_date + days(duration);
        
        visit->visit_type_concept_id = 44818518;
        visit->visit_source_value = "VISIT-" + std::to_string(i);
        
        EXPECT_TRUE(visit->validate());
        visits.push_back(std::move(visit));
    }
    
    auto end_time = steady_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    EXPECT_LT(duration.count(), 5000); // Should complete within 5 seconds
    EXPECT_EQ(visits.size(), num_visits);
}

// Test SQL generation with complex data
TEST_F(VisitOccurrenceIntegrationTest, ComplexSQLGeneration) {
    VisitOccurrence visit;
    visit.visit_occurrence_id = 10000001;
    visit.person_id = 100008;
    visit.visit_concept_id = 9201;
    visit.visit_start_date = uk_date_2024_03_15;
    visit.visit_end_date = uk_date_2024_03_18;
    visit.visit_type_concept_id = 44818518;
    
    // Add all optional fields
    visit.visit_start_datetime = createUKDateTime(15, 3, 2024, 8, 30);
    visit.visit_end_datetime = createUKDateTime(18, 3, 2024, 16, 45);
    visit.provider_id = 50001;
    visit.care_site_id = 20001;
    visit.visit_source_value = "Complex'Visit\"Test\\2024";
    visit.visit_source_concept_id = 0;
    visit.admitted_from_concept_id = 8870;
    visit.admitted_from_source_value = "A&E - St. Mary's Hospital";
    visit.discharged_to_concept_id = 8536;
    visit.discharged_to_source_value = "Patient's Home";
    visit.preceding_visit_occurrence_id = 9999999;
    
    auto sql = visit.to_insert_sql(true);
    
    // Check SQL injection prevention
    EXPECT_EQ(sql.find("DROP TABLE"), std::string::npos);
    EXPECT_NE(sql.find("''"), std::string::npos); // Escaped quotes
    EXPECT_NE(sql.find("\\\\"), std::string::npos); // Escaped backslashes
}

// Test field visitor implementation
TEST_F(VisitOccurrenceIntegrationTest, FieldVisitorImplementation) {
    class VisitFieldAnalyzer : public FieldVisitor {
    public:
        int total_fields = 0;
        int optional_with_values = 0;
        std::vector<std::string> date_fields;
        
        void visit(const std::string& name, const std::any& value) override {
            total_fields++;
            
            if (name.find("date") != std::string::npos || 
                name.find("datetime") != std::string::npos) {
                date_fields.push_back(name);
            }
            
            // Count optional fields with values
            if (name.find("_id") != std::string::npos && value.has_value()) {
                optional_with_values++;
            }
        }
    };
    
    VisitOccurrence visit;
    visit.visit_occurrence_id = 11000001;
    visit.person_id = 11000002;
    visit.visit_concept_id = 9201;
    visit.visit_start_date = createUKDate(1, 7, 2024);
    visit.visit_end_date = createUKDate(2, 7, 2024);
    visit.visit_type_concept_id = 44818518;
    visit.visit_source_value = "FieldVisitorTest";
    
    VisitFieldAnalyzer analyzer;
    visit.visit_fields(analyzer);
    
    // Check that required fields are present
    EXPECT_GT(analyzer.total_fields, 0);
    EXPECT_GT(analyzer.date_fields.size(), 0);
    // Check that at least one optional field with value is counted
    EXPECT_GE(analyzer.optional_with_values, 1);
}
