#pragma once

#include <gtest/gtest.h>
#include "core/interfaces.h"
#include "load/database_loader.h"
#include <filesystem>
#include <random>
#include <string>
#include <vector>
#include <atomic>
#include <functional>
#include <thread>
#include <chrono>

namespace omop::test {

/**
 * @brief Base class for loader integration tests
 */
class LoaderIntegrationTestBase : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / 
                   ("omop_test_" + std::to_string(std::random_device{}()));
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up temporary files
        std::error_code ec;
        std::filesystem::remove_all(test_dir_, ec);
    }

    /**
     * @brief Generate unique ID for test data
     */
    int64_t generateUniqueId() {
        static std::atomic<int64_t> counter{1000000};
        return counter.fetch_add(1);
    }

    /**
     * @brief Verify record exists in database
     */
    bool verifyRecordExists(extract::IDatabaseConnection* conn,
                           const std::string& table,
                           const std::string& schema,
                           const std::string& id_column,
                           int64_t id) {
        auto query = "SELECT COUNT(*) FROM " + schema + "." + table + 
                    " WHERE " + id_column + " = " + std::to_string(id);
        auto result = conn->execute_query(query);
        if (result->next()) {
            return std::any_cast<int64_t>(result->get_value(0)) > 0;
        }
        return false;
    }

protected:
    std::filesystem::path test_dir_;
};

/**
 * @brief Mock implementation of missing loader classes
 */
namespace mock {

class ThreadPoolLoader : public load::BatchLoader {
public:
    struct Config {
        size_t num_threads{4};
        size_t queue_size{1000};
        size_t batch_size{100};
    };

    explicit ThreadPoolLoader(const Config& config) 
        : BatchLoader("thread_pool", convertConfig(config)) {}

    void set_completion_callback(std::function<void(const LoadResult&)> callback) {
        completion_callback_ = callback;
    }

    void submit(const core::Record& record) {
        // Mock implementation
        core::ProcessingContext context;
        load(record, context);
    }

    void wait_for_completion() {
        // Mock implementation
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

private:
    static load::BatchLoaderOptions convertConfig(const Config& config) {
        load::BatchLoaderOptions options;
        options.batch_size = config.batch_size;
        options.worker_threads = config.num_threads;
        return options;
    }

    std::function<void(const LoadResult&)> completion_callback_;
};

} // namespace mock

/**
 * @brief Test data validation utilities
 */
class DataValidator {
public:
    static bool validatePersonRecord(const core::Record& record) {
        // Required fields for person table
        std::vector<std::string> required_fields = {
            "person_id", "gender_concept_id", "year_of_birth",
            "race_concept_id", "ethnicity_concept_id"
        };
        
        for (const auto& field : required_fields) {
            if (!record.hasField(field)) {
                return false;
            }
        }
        
        // Validate data types and ranges
        try {
            auto person_id = std::any_cast<int64_t>(record.getField("person_id"));
            if (person_id <= 0) return false;
            
            auto year = std::any_cast<int32_t>(record.getField("year_of_birth"));
            if (year < 1850 || year > 2024) return false;
            
            return true;
        } catch (...) {
            return false;
        }
    }
};

} // namespace omop::test 