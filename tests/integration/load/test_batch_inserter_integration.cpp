/**
 * @brief Integration tests for batch inserter with UK localization
 * Tests batch insertion capabilities including bulk operations and data type handling
 */

#include <gtest/gtest.h>
#include "load/batch_loader.h"
#include "load/database_loader.h"
#include "core/interfaces.h"
#include "common/utilities.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <random>
#include <iomanip>
#include <locale>
#include <string>
#include <unordered_map>
#include <vector>
#include <any>
#include <filesystem>
#include <fstream>
#include <sstream>
#include <thread>
#include <atomic>
#include <chrono>

namespace omop::test {

/**
 * @brief Mock batch inserter implementation using database loader
 * Provides batch insertion functionality with UK localization support
 */
class BatchInserter : public omop::load::DatabaseLoader {
public:
    using Row = std::unordered_map<std::string, std::any>;

    struct Options {
        bool use_bulk_copy{false};
        size_t batch_size{1000};
        size_t bulk_copy_threshold{500};
        bool cache_prepared_statements{true};
        bool continue_on_error{false};
        size_t max_retries{3};
        bool generate_csv_files{false};
        std::string csv_directory{"/tmp"};
    };

    struct Statistics {
        size_t total_rows{0};
        size_t failed_rows{0};
        size_t batch_count{0};
        size_t bulk_copies{0};
        size_t regular_inserts{0};
        double average_batch_size{0.0};
        size_t retries{0};
        double insert_time_ms{0.0};
    };

    BatchInserter(omop::extract::IDatabaseConnection* conn,
                  const std::string& schema,
                  const std::string& table,
                  const Options& options = Options{})
        : DatabaseLoader(std::unique_ptr<omop::extract::IDatabaseConnection>(conn),
                        convert_options(options)),
          options_(options) {
        schema_name_ = schema;
        target_table_ = table;
        buffer_.reserve(options.batch_size);
    }

    void set_batch_size(size_t size) {
        options_.batch_size = size;
        buffer_.reserve(size);
    }

    void add_row(const Row& row) {
        buffer_.push_back(row);
        stats_.total_rows++;
        
        if (buffer_.size() >= options_.batch_size) {
            flush_buffer();
        }
    }

    size_t flush() {
        size_t flushed = buffer_.size();
        if (!buffer_.empty()) {
            flush_buffer();
        }
        return flushed;
    }

    size_t get_total_inserted() const {
        return total_inserted_;
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        stats_.average_batch_size = stats_.batch_count > 0 
            ? static_cast<double>(stats_.total_rows) / stats_.batch_count 
            : 0.0;
        return {
            {"total_rows", stats_.total_rows},
            {"batch_count", stats_.batch_count},
            {"average_batch_size", stats_.average_batch_size},
            {"insert_time_ms", stats_.insert_time_ms},
            {"failed_rows", stats_.failed_rows},
            {"bulk_copies", stats_.bulk_copies},
            {"regular_inserts", stats_.regular_inserts},
            {"retries", stats_.retries}
        };
    }

private:
    static omop::load::DatabaseLoaderOptions convert_options(const Options& options) {
        omop::load::DatabaseLoaderOptions db_options;
        db_options.batch_size = options.batch_size;
        db_options.use_bulk_insert = options.use_bulk_copy;
        return db_options;
    }

    void flush_buffer() {
        if (buffer_.empty()) return;
        
        stats_.batch_count++;
        
        // Convert rows to records
        core::RecordBatch batch;
        for (const auto& row : buffer_) {
            core::Record record;
            for (const auto& [field, value] : row) {
                record.setField(field, value);
            }
            batch.addRecord(record);
        }
        
        try {
            if (options_.use_bulk_copy && buffer_.size() >= options_.bulk_copy_threshold) {
                stats_.bulk_copies++;
                
                if (options_.generate_csv_files) {
                    generate_csv_file(batch);
                }
            } else {
                stats_.regular_inserts++;
            }
            
            // Use parent class batch loading
            core::ProcessingContext context;
            size_t loaded = load_batch(batch, context);
            total_inserted_ += loaded;
            
            if (loaded < batch.size()) {
                stats_.failed_rows += batch.size() - loaded;
            }
            
        } catch (const std::exception& e) {
            if (options_.continue_on_error) {
                stats_.failed_rows += buffer_.size();
            } else {
                throw;
            }
        }
        
        buffer_.clear();
    }

    void generate_csv_file(const core::RecordBatch& batch) {
        // Generate CSV for bulk copy operations
        static size_t file_counter = 0;
        std::string filename = options_.csv_directory + "/batch_" + 
                              std::to_string(++file_counter) + ".csv";
        
        std::ofstream csv_file(filename);
        
        // Write header
        bool first = true;
        std::vector<std::string> field_names;
        if (!batch.empty()) {
            field_names = batch.getRecords()[0].getFieldNames();
            for (const auto& field : field_names) {
                if (!first) csv_file << ",";
                csv_file << field;
                first = false;
            }
            csv_file << "\n";
        }
        
        // Write data
        for (const auto& record : batch) {
            first = true;
            for (const auto& field : field_names) {
                if (!first) csv_file << ",";
                csv_file << escape_csv_value(common::any_to_string(record.getField(field)));
                first = false;
            }
            csv_file << "\n";
        }
    }

    std::string escape_csv_value(const std::string& value) {
        if (value.find_first_of(",\"\n\r") == std::string::npos) {
            return value;
        }
        
        std::string escaped = "\"";
        for (char c : value) {
            if (c == '"') escaped += "\"\"";
            else escaped += c;
        }
        escaped += "\"";
        return escaped;
    }

    Options options_;
    std::vector<Row> buffer_;
    size_t total_inserted_{0};
    mutable Statistics stats_;
};

class BatchInserterIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger_ = common::Logger::get("test-batch-inserter");
        
        // Set UK locale
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (...) {
            std::locale::global(std::locale::classic());
        }

        // Setup database with UK settings
        db_fixture_ = std::make_unique<DatabaseFixture>();
        DatabaseConfig db_config = DatabaseConnectionFactory::getOmopConfig();

        try {
            db_fixture_->setup(db_config);
            createTestTables();
            
            // Set UK timezone
            auto conn = db_fixture_->get_connection();
            if (conn) {
                conn->execute_update("SET timezone = 'Europe/London'");
            }
        } catch (const std::exception& e) {
            GTEST_SKIP() << "Database not available: " << e.what();
        }
    }

    void TearDown() override {
        if (db_fixture_) {
            db_fixture_->teardown();
        }
    }

    void createTestTables() {
        auto conn = db_fixture_->get_connection();
        
        // Ensure test schema exists
        conn->execute_update("CREATE SCHEMA IF NOT EXISTS test_schema");

        // Create test table with various data types
        conn->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_schema.batch_test (
                id BIGINT PRIMARY KEY,
                name VARCHAR(100),
                value NUMERIC(10,2),
                created_date DATE,
                created_time TIMESTAMP,
                is_active BOOLEAN,
                data_blob TEXT,
                uk_postal_code VARCHAR(10),
                price_gbp DECIMAL(10,2)
            )
        )");

        // Create table for performance testing
        conn->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_schema.perf_test (
                id BIGINT PRIMARY KEY,
                field1 VARCHAR(50),
                field2 INTEGER,
                field3 NUMERIC(15,4),
                field4 TIMESTAMP,
                uk_phone VARCHAR(20)
            )
        )");
    }

    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::shared_ptr<common::Logger> logger_;
};

/**
 * @brief Test basic batch insertion with UK date formatting
 */
TEST_F(BatchInserterIntegrationTest, BasicBatchInsertion) {
    auto conn = db_fixture_->get_connection();
    BatchInserter inserter(conn.get(), "test_schema", "batch_test");

    // UK date formatter
    auto format_uk_date = [](const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%d/%m/%Y %H:%M:%S");
        return ss.str();
    };

    // Configure batch size
    inserter.set_batch_size(10);

    // Add records
    for (int i = 1; i <= 25; ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(i);
        row["name"] = "Test Record " + std::to_string(i);
        row["value"] = double(i * 10.5);
        row["created_date"] = std::chrono::system_clock::now();
        row["created_time"] = std::chrono::system_clock::now();
        row["is_active"] = (i % 2 == 0);
        row["data_blob"] = "Some test data for record " + std::to_string(i);
        row["uk_postal_code"] = "SW1A " + std::to_string(i % 10) + "AA";
        row["price_gbp"] = double(9.99 + i * 1.50); // UK pricing

        inserter.add_row(row);
    }

    // Flush remaining records
    size_t flushed = inserter.flush();

    // Verify
    EXPECT_EQ(flushed, 5); // Last batch of 5 records
    EXPECT_EQ(inserter.get_total_inserted(), 25);

    // Check database
    auto result = conn->execute_query("SELECT COUNT(*) FROM test_schema.batch_test");
    ASSERT_TRUE(result->next());
    EXPECT_EQ(std::any_cast<int64_t>(result->get_value(0)), 25);
}

/**
 * @brief Test bulk copy mode with UK currency values
 */
TEST_F(BatchInserterIntegrationTest, BulkCopyMode) {
    auto conn = db_fixture_->get_connection();

    // Enable bulk copy mode
    BatchInserter::Options options;
    options.use_bulk_copy = true;
    options.batch_size = 100;
    options.bulk_copy_threshold = 50;

    BatchInserter inserter(conn.get(), "test_schema", "batch_test", options);

    // Add many records
    const int num_records = 500;
    for (int i = 1; i <= num_records; ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(1000 + i);
        row["name"] = "Bulk Record " + std::to_string(i);
        row["value"] = double(i * 1.23); // Price in pounds
        row["created_date"] = std::chrono::system_clock::now();
        row["created_time"] = std::chrono::system_clock::now();
        row["is_active"] = true;
        row["uk_postal_code"] = "EC1A " + std::to_string(i % 100) + "BB";
        row["price_gbp"] = double(19.99 + i * 0.50); // UK VAT inclusive price

        inserter.add_row(row);
    }

    inserter.flush();

    // Verify bulk copy was used
    auto stats = inserter.get_statistics();
    auto bulk_copies = std::any_cast<size_t>(stats.at("bulk_copies"));
    auto total_rows = std::any_cast<size_t>(stats.at("total_rows"));
    auto regular_inserts = std::any_cast<size_t>(stats.at("regular_inserts"));
    auto average_batch_size = std::any_cast<double>(stats.at("average_batch_size"));
    
    EXPECT_GT(bulk_copies, 0) << "Bulk copy was not used";
    EXPECT_EQ(total_rows, num_records);

    logger_->info("Bulk copy statistics (UK):");
    logger_->info("  - Total rows: {:L}", total_rows);
    logger_->info("  - Bulk copies: {}", bulk_copies);
    logger_->info("  - Regular inserts: {}", regular_inserts);
    logger_->info("  - Average batch size: {:.1f}", average_batch_size);
}

/**
 * @brief Test prepared statement caching with UK phone numbers
 */
TEST_F(BatchInserterIntegrationTest, PreparedStatementCaching) {
    auto conn = db_fixture_->get_connection();

    BatchInserter::Options options;
    options.cache_prepared_statements = true;
    options.batch_size = 50;

    BatchInserter inserter(conn.get(), "test_schema", "batch_test", options);

    // UK phone number formatter
    auto format_uk_phone = [](int area, int number) -> std::string {
        return std::format("+44 {} {}", area, number);
    };

    // First batch - should create prepared statement
    auto start1 = std::chrono::high_resolution_clock::now();
    for (int i = 1; i <= 50; ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(2000 + i);
        row["name"] = "Cached " + std::to_string(i);
        row["value"] = double(i);
        row["uk_postal_code"] = "W1A " + std::to_string(i % 10) + "BC";
        row["price_gbp"] = double(4.99 + i * 0.10);
        inserter.add_row(row);
    }
    auto end1 = std::chrono::high_resolution_clock::now();
    auto duration1 = std::chrono::duration_cast<std::chrono::microseconds>(end1 - start1);

    // Second batch - should reuse prepared statement
    auto start2 = std::chrono::high_resolution_clock::now();
    for (int i = 51; i <= 100; ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(2000 + i);
        row["name"] = "Cached " + std::to_string(i);
        row["value"] = double(i);
        row["uk_postal_code"] = "N1 " + std::to_string(i % 100) + "CD";
        row["price_gbp"] = double(4.99 + i * 0.10);
        inserter.add_row(row);
    }
    auto end2 = std::chrono::high_resolution_clock::now();
    auto duration2 = std::chrono::duration_cast<std::chrono::microseconds>(end2 - start2);

    inserter.flush();

    // Second batch should be faster due to cached statement
    logger_->info("First batch time: {} μs", duration1.count());
    logger_->info("Second batch time: {} μs", duration2.count());

    // Verify data
    EXPECT_EQ(inserter.get_total_inserted(), 100);
}

/**
 * @brief Test error handling with UK postal code validation
 */
TEST_F(BatchInserterIntegrationTest, ErrorHandlingAndRecovery) {
    auto conn = db_fixture_->get_connection();

    BatchInserter::Options options;
    options.batch_size = 5;
    options.continue_on_error = true;
    options.max_retries = 2;

    BatchInserter inserter(conn.get(), "test_schema", "batch_test", options);

    // Add some valid records
    for (int i = 1; i <= 3; ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(3000 + i);
        row["name"] = "Valid " + std::to_string(i);
        row["value"] = double(i);
        row["uk_postal_code"] = "SE1 " + std::to_string(i) + "EF";
        row["price_gbp"] = double(12.50);
        inserter.add_row(row);
    }

    // Add invalid record (duplicate primary key)
    BatchInserter::Row duplicate;
    duplicate["id"] = int64_t(3001); // Duplicate
    duplicate["name"] = "Duplicate";
    duplicate["value"] = 999.0;
    duplicate["uk_postal_code"] = "INVALID"; // Invalid UK postal code
    duplicate["price_gbp"] = double(-10.00); // Invalid price
    inserter.add_row(duplicate);

    // Add more valid records
    for (int i = 4; i <= 6; ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(3000 + i);
        row["name"] = "Valid " + std::to_string(i);
        row["value"] = double(i);
        row["uk_postal_code"] = "E1 " + std::to_string(i) + "GH";
        row["price_gbp"] = double(15.00);
        inserter.add_row(row);
    }

    inserter.flush();

    // Verify error handling
    auto stats = inserter.get_statistics();
    auto failed_rows = std::any_cast<size_t>(stats.at("failed_rows"));
    auto total_rows = std::any_cast<size_t>(stats.at("total_rows"));
    auto retries = std::any_cast<size_t>(stats.at("retries"));
    
    EXPECT_GT(failed_rows, 0) << "Should have failed rows";
    EXPECT_LT(failed_rows, total_rows) << "Should have some successful rows";

    logger_->info("Error handling stats (UK):");
    logger_->info("  - Total rows: {:L}", total_rows);
    logger_->info("  - Failed rows: {:L}", failed_rows);
    logger_->info("  - Retries: {}", retries);

    // Check database for successful inserts
    auto result = conn->execute_query("SELECT COUNT(*) FROM test_schema.batch_test WHERE id >= 3000");
    ASSERT_TRUE(result->next());
    EXPECT_EQ(std::any_cast<int64_t>(result->get_value(0)), 6);
}

/**
 * @brief Test performance with UK regional data
 */
TEST_F(BatchInserterIntegrationTest, LargeBatchPerformance) {
    auto conn = db_fixture_->get_connection();

    // UK regions for testing
    std::vector<std::string> uk_regions = {"London", "Manchester", "Birmingham", 
                                           "Edinburgh", "Cardiff", "Belfast"};

    // Test different batch sizes
    std::vector<size_t> batch_sizes = {100, 500, 1000};
    const int total_records = 5000;

    for (auto batch_size : batch_sizes) {
        // Clear table
        conn->execute_update("TRUNCATE TABLE test_schema.perf_test");

        BatchInserter::Options options;
        options.batch_size = batch_size;
        options.use_bulk_copy = true;
        options.bulk_copy_threshold = batch_size / 2;

        BatchInserter inserter(conn.get(), "test_schema", "perf_test", options);

        auto start = std::chrono::high_resolution_clock::now();

        // Insert records
        for (int i = 1; i <= total_records; ++i) {
            BatchInserter::Row row;
            row["id"] = int64_t(i);
            row["field1"] = uk_regions[i % uk_regions.size()] + " " + std::to_string(i);
            row["field2"] = i % 1000;
            row["field3"] = double(i) / 7.0;
            row["field4"] = std::chrono::system_clock::now();
            // Format UK phone number: +44 20 7123 4567
            row["uk_phone"] = "+44 20 " + std::to_string(7000 + (i % 999)) + " " + std::to_string(1000 + (i % 9999));

            inserter.add_row(row);
        }

        inserter.flush();

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        double records_per_second = (total_records * 1000.0) / duration.count();

        logger_->info("Batch size {} performance (UK):", batch_size);
        logger_->info("  - Time: {:L} ms", duration.count());
        logger_->info("  - Throughput: {:L.0f} records/second", 
                     static_cast<size_t>(records_per_second));

        auto stats = inserter.get_statistics();
        auto total_rows = std::any_cast<size_t>(stats.at("total_rows"));
        EXPECT_EQ(total_rows, total_records);
        EXPECT_EQ(inserter.get_total_inserted(), total_records);
    }
}

/**
 * @brief Test data type handling with UK-specific formats
 */
TEST_F(BatchInserterIntegrationTest, DataTypeHandling) {
    auto conn = db_fixture_->get_connection();

    // UK date/time formatter
    auto format_uk_datetime = [](const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%d/%m/%Y %H:%M:%S");
        return ss.str();
    };

    // Create table with various data types
    conn->execute_update(R"(
        CREATE TABLE IF NOT EXISTS test_schema.type_test (
            id INTEGER PRIMARY KEY,
            small_int SMALLINT,
            big_int BIGINT,
            float_val REAL,
            double_val DOUBLE PRECISION,
            decimal_val NUMERIC(15,4),
            char_val CHAR(10),
            varchar_val VARCHAR(100),
            text_val TEXT,
            boolean_val BOOLEAN,
            date_val DATE,
            time_val TIME,
            timestamp_val TIMESTAMP,
            uuid_val UUID,
            json_val JSONB,
            array_val INTEGER[],
            uk_national_insurance VARCHAR(20),
            uk_vat_number VARCHAR(20)
        )
    )");

    BatchInserter inserter(conn.get(), "test_schema", "type_test");

    // Test various data types
    BatchInserter::Row row;
    row["id"] = 1;
    row["small_int"] = int16_t(32767);
    row["big_int"] = int64_t(9223372036854775807LL);
    row["float_val"] = float(3.14159f);
    row["double_val"] = double(2.718281828459045);
    row["decimal_val"] = double(123456.7890);
    row["char_val"] = std::string("UK");
    row["varchar_val"] = std::string("United Kingdom");
    row["text_val"] = std::string("Great Britain and Northern Ireland");
    row["boolean_val"] = true;
    row["date_val"] = std::chrono::system_clock::now();
    row["time_val"] = std::chrono::system_clock::now();
    row["timestamp_val"] = std::chrono::system_clock::now();
    row["uuid_val"] = std::string("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11");

    // JSON as string
    row["json_val"] = std::string(R"({"region": "UK", "city": "London", "postcode": "SW1A 1AA"})");

    // Array as vector
    row["array_val"] = std::vector<int>{1, 2, 3, 4, 5};

    // UK-specific identifiers
    row["uk_national_insurance"] = std::string("QQ 12 34 56 A");
    row["uk_vat_number"] = std::string("GB 123 4567 89");

    inserter.add_row(row);
    inserter.flush();

    // Verify
    auto result = conn->execute_query("SELECT COUNT(*) FROM test_schema.type_test");
    ASSERT_TRUE(result->next());
    EXPECT_EQ(std::any_cast<int64_t>(result->get_value(0)), 1);

    logger_->info("Data type handling test completed successfully");
}

/**
 * @brief Test concurrent batch insertion with UK bank sort codes
 */
TEST_F(BatchInserterIntegrationTest, ConcurrentBatchInsertion) {
    const int num_threads = 4;
    const int records_per_thread = 1000;

    std::vector<std::thread> threads;
    std::atomic<size_t> total_inserted(0);
    std::atomic<int> next_id(1);

    // UK bank sort codes for testing
    std::vector<std::string> uk_sort_codes = {"20-00-00", "30-00-00", "40-00-00", "60-00-00"};

    for (int thread_id = 0; thread_id < num_threads; ++thread_id) {
        threads.emplace_back([this, thread_id, &total_inserted, &next_id, &uk_sort_codes]() {
            auto conn = db_fixture_->get_connection();

            BatchInserter::Options options;
            options.batch_size = 100;
            options.use_bulk_copy = true;

            BatchInserter inserter(conn.get(), "test_schema", "perf_test", options);

            for (int i = 0; i < records_per_thread; ++i) {
                BatchInserter::Row row;
                row["id"] = int64_t(next_id.fetch_add(1));
                row["field1"] = "Thread " + std::to_string(thread_id) + 
                               " Sort: " + uk_sort_codes[thread_id % uk_sort_codes.size()];
                row["field2"] = thread_id * 1000 + i;
                row["field3"] = double(i) / 3.0;
                row["field4"] = std::chrono::system_clock::now();
                // Format UK phone number: +44 20 7123 4567
                row["uk_phone"] = "+44 " + std::to_string(20 + thread_id) + " " + std::to_string(7000000 + i);

                inserter.add_row(row);
            }

            inserter.flush();
            total_inserted.fetch_add(inserter.get_total_inserted());
        });
    }

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    logger_->info("Concurrent insertion completed (UK):");
    logger_->info("  - Total inserted: {:L}", total_inserted.load());

    // Verify
    auto conn = db_fixture_->get_connection();
    auto result = conn->execute_query("SELECT COUNT(*) FROM test_schema.perf_test");
    ASSERT_TRUE(result->next());
    EXPECT_EQ(std::any_cast<int64_t>(result->get_value(0)), total_inserted.load());
}

/**
 * @brief Test CSV generation with UK address formatting
 */
TEST_F(BatchInserterIntegrationTest, CsvGenerationForBulkCopy) {
    auto conn = db_fixture_->get_connection();

    // Create batch inserter with CSV generation
    BatchInserter::Options options;
    options.use_bulk_copy = true;
    options.generate_csv_files = true;
    options.csv_directory = "/tmp/omop_test_csv";
    options.batch_size = 10;

    // Create CSV directory
    std::filesystem::create_directories(options.csv_directory);

    BatchInserter inserter(conn.get(), "test_schema", "batch_test", options);

    // Add records with special characters that need escaping
    std::vector<std::string> test_strings = {
        "10 Downing Street",
        "Flat 2B, \"The Heights\"",
        "123 High Street, London",
        "Unit 5\nBusiness Park",
        "Ground Floor\tOffice Building",
        "C\\O Smith & Jones"
    };
    
    // UK cities
    std::vector<std::string> uk_cities = {
        "London", "Manchester", "Birmingham", "Glasgow", "Liverpool", "Bristol"
    };

    for (size_t i = 0; i < test_strings.size(); ++i) {
        BatchInserter::Row row;
        row["id"] = int64_t(5000 + i);
        row["name"] = test_strings[i] + ", " + uk_cities[i];
        row["value"] = double(i);
        row["created_date"] = std::chrono::system_clock::now();
        row["created_time"] = std::chrono::system_clock::now();
        row["is_active"] = true;
        row["data_blob"] = "Blob: " + test_strings[i];
        row["uk_postal_code"] = "SW1A " + std::to_string(i + 1) + "AA";
        row["price_gbp"] = double(99.99 + i * 10.00);

        inserter.add_row(row);
    }

    inserter.flush();

    // Verify CSV files were created
    std::vector<std::string> csv_files;
    for (const auto& entry : std::filesystem::directory_iterator(options.csv_directory)) {
        if (entry.path().extension() == ".csv") {
            csv_files.push_back(entry.path().string());
        }
    }

    EXPECT_GT(csv_files.size(), 0) << "No CSV files were generated";

    // Verify data was inserted
    auto result = conn->execute_query("SELECT COUNT(*) FROM test_schema.batch_test WHERE id >= 5000");
    ASSERT_TRUE(result->next());
    EXPECT_EQ(std::any_cast<int64_t>(result->get_value(0)), test_strings.size());

    // Clean up CSV files
    for (const auto& file : csv_files) {
        std::filesystem::remove(file);
    }
    std::filesystem::remove(options.csv_directory);
}

/**
 * @brief Helper to format UK phone numbers
 */
std::string format_uk_phone(int area_code, int number) {
    std::ostringstream phone;
    phone << "+44 " << area_code << " ";
    std::string num_str = std::to_string(number);
    if (num_str.length() >= 7) {
        phone << num_str.substr(0, 3) << " " << num_str.substr(3);
    } else {
        phone << num_str;
    }
    return phone.str();
}

} // namespace omop::test