/**
 * @brief Integration tests for specialized loaders with UK localization
 * Tests JSON, HTTP, S3, and multi-format loaders with UK-specific data formats
 */

#include <gtest/gtest.h>
#include "load/additional_loaders.h"
#include "load/batch_loader.h"
#include "core/interfaces.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/database_connection_factory.h"
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <locale>
#include <chrono>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <memory>
#include <regex>

namespace omop::test {

/**
 * @brief Test fixture for specialized loaders with UK settings
 */
class SpecializedLoadersIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger_ = common::Logger::get("test-specialized-loaders");

        // Set UK locale
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (...) {
            std::locale::global(std::locale::classic());
        }

        test_dir_ = std::filesystem::temp_directory_path() / "omop_loader_test";
        std::filesystem::create_directories(test_dir_);

        // Setup database for comparison tests
        db_fixture_ = std::make_unique<DatabaseFixture>();
        DatabaseConfig db_config = DatabaseConnectionFactory::getOmopConfig();

        try {
            db_fixture_->setup(db_config);
        } catch (const std::exception& e) {
            // Database tests are optional for file-based loaders
            logger_->warn("Database not available: {}", e.what());
        }

        // Create test records
        create_test_records();
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
        if (db_fixture_) {
            db_fixture_->teardown();
        }
    }

private:
    void create_test_records() {
        // UK-specific test data
        std::vector<std::string> uk_postcodes = {
            "SW1A 1AA", "EC1A 1BB", "M1 1AE", "G2 1DU", "CF10 1DD",
            "EH1 1YZ", "BT1 1AA", "OX1 1AA", "CB2 1TN", "BS1 1AA"
        };

        std::vector<std::string> uk_phones = {
            "+44 20 7123 4567", "+44 ************", "+44 ************",
            "+44 ************", "+44 ************"
        };

        for (int i = 0; i < 100; ++i) {
            core::Record record;
            record.setField("person_id", static_cast<int64_t>(1000 + i));
            record.setField("gender_concept_id", i % 2 == 0 ? 8507 : 8532);
            record.setField("year_of_birth", 1950 + (i % 50));
            record.setField("month_of_birth", 1 + (i % 12));
            record.setField("day_of_birth", 1 + (i % 28));
            record.setField("race_concept_id", 8527);
            record.setField("ethnicity_concept_id", 38003564);
            
            // Add UK-specific fields
            record.setField("location_source_value", uk_postcodes[i % uk_postcodes.size()]);
            record.setField("phone_number", uk_phones[i % uk_phones.size()]);
            
            // UK date format (DD/MM/YYYY)
            auto birth_date = std::chrono::system_clock::now() - 
                            std::chrono::hours(24 * 365 * (20 + i % 50));
            record.setField("birth_datetime", birth_date);
            
            // UK currency (GBP)
            record.setField("monthly_income_gbp", 2000.00 + (i * 50.00));
            record.setField("nhs_number", std::format("NHS{:08d}", 10000000 + i));

            test_records_.push_back(record);
        }
    }

protected:
    std::filesystem::path test_dir_;
    std::vector<core::Record> test_records_;
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::shared_ptr<common::Logger> logger_;

    // UK date formatter
    std::string format_uk_date(const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%d/%m/%Y");
        return ss.str();
    }
};

/**
 * @brief Test JSON batch loader with UK data formats
 */
TEST_F(SpecializedLoadersIntegrationTest, TestJsonBatchLoader) {
    load::BatchLoaderOptions batch_options;
    batch_options.batch_size = 25;
    batch_options.parallel_processing = true;
    batch_options.worker_threads = 2;

    load::JsonBatchLoader::JsonOptions json_options;
    json_options.pretty_print = true;
    json_options.indent_size = 2;
    json_options.include_metadata = true;
    json_options.array_output = true;
    json_options.date_format = "%d/%m/%Y %H:%M:%S"; // UK date format

    auto loader = std::make_unique<load::JsonBatchLoader>(batch_options, json_options);

    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "output.json").string();

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Load all test records
    for (const auto& record : test_records_) {
        EXPECT_TRUE(loader->load(record, context));
    }

    loader->commit(context);
    loader->finalize(context);

    // Verify JSON output
    std::ifstream json_file(test_dir_ / "output.json");
    ASSERT_TRUE(json_file.is_open());

    nlohmann::json json_data;
    json_file >> json_data;

    EXPECT_TRUE(json_data.is_array());
    EXPECT_EQ(json_data.size(), test_records_.size());

    // Verify first record
    auto first_record = json_data[0];
    EXPECT_EQ(first_record["person_id"], 1000);
    EXPECT_EQ(first_record["year_of_birth"], 1950);
    EXPECT_TRUE(first_record.contains("location_source_value"));
    EXPECT_EQ(first_record["location_source_value"], "SW1A 1AA");
    
    // Verify UK date format
    if (first_record.contains("birth_datetime")) {
        std::string date_str = first_record["birth_datetime"];
        // Should be in DD/MM/YYYY format
        EXPECT_TRUE(std::regex_match(date_str, 
            std::regex(R"(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2})")));
    }
    
    // Verify metadata
    if (json_options.include_metadata && first_record.contains("_metadata")) {
        auto metadata = first_record["_metadata"];
        EXPECT_TRUE(metadata.contains("nhs_number"));
    }
}

/**
 * @brief Test NDJSON format with UK timestamps
 */
TEST_F(SpecializedLoadersIntegrationTest, TestJsonLinesLoader) {
    load::BatchLoaderOptions batch_options;
    batch_options.batch_size = 50;

    load::JsonBatchLoader::JsonOptions json_options;
    json_options.array_output = false; // NDJSON mode
    json_options.pretty_print = false;
    json_options.date_format = "%d/%m/%Y %H:%M:%S"; // UK format

    auto loader = std::make_unique<load::JsonBatchLoader>(batch_options, json_options);

    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "output.jsonl").string();

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Load records
    core::RecordBatch batch;
    for (const auto& record : test_records_) {
        batch.addRecord(record);
    }
    size_t loaded = loader->load_batch(batch, context);
    EXPECT_EQ(loaded, test_records_.size());

    loader->finalize(context);

    // Verify JSONL output
    std::ifstream jsonl_file(test_dir_ / "output.jsonl");
    ASSERT_TRUE(jsonl_file.is_open());

    std::string line;
    int line_count = 0;
    while (std::getline(jsonl_file, line)) {
        EXPECT_FALSE(line.empty());

        nlohmann::json json_line = nlohmann::json::parse(line);
        EXPECT_TRUE(json_line.contains("person_id"));
        EXPECT_TRUE(json_line.contains("location_source_value"));
        EXPECT_TRUE(json_line.contains("monthly_income_gbp"));

        line_count++;
    }

    EXPECT_EQ(line_count, test_records_.size());
    
    logger_->info("JSONL test completed: {} lines written", line_count);
}

/**
 * @brief Test multi-format loader with UK-specific outputs
 */
TEST_F(SpecializedLoadersIntegrationTest, TestMultiFormatLoader) {
    auto multi_loader = std::make_unique<load::MultiFormatLoader>("multi_format_test");

    // Add JSON loader
    load::JsonBatchLoader::JsonOptions json_options;
    json_options.pretty_print = true;
    json_options.date_format = "%d/%m/%Y"; // UK date only
    auto json_loader = std::make_unique<load::JsonBatchLoader>(
        load::BatchLoaderOptions{}, json_options);

    // Add CSV loader
    auto csv_loader = std::make_unique<load::CsvBatchLoader>(
        load::BatchLoaderOptions{}, ',', '"');

    // Configure CSV loader for UK decimal format (though CSV typically uses '.')
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["output_file"] = (test_dir_ / "multi_output.csv").string();
    csv_config["include_headers"] = true;
    csv_config["date_format"] = "%d/%m/%Y";

    // Initialize CSV loader separately
    core::ProcessingContext csv_context;
    csv_loader->initialize(csv_config, csv_context);

    multi_loader->add_loader(std::move(json_loader), 1.0);
    multi_loader->add_loader(std::move(csv_loader), 1.0);

    std::unordered_map<std::string, std::any> config;
    config["json_output_file"] = (test_dir_ / "multi_output.json").string();
    config["csv_output_file"] = (test_dir_ / "multi_output.csv").string();
    config["output_file"] = config["json_output_file"]; // Primary output

    core::ProcessingContext context;
    multi_loader->initialize(config, context);

    // Load records
    for (const auto& record : test_records_) {
        EXPECT_TRUE(multi_loader->load(record, context));
    }

    multi_loader->commit(context);
    multi_loader->finalize(context);

    // Verify both outputs exist
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "multi_output.json"));
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "multi_output.csv"));

    // Verify statistics
    auto stats = multi_loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), test_records_.size());
    EXPECT_EQ(std::any_cast<size_t>(stats["loader_count"]), 2);

    // Verify CSV contains UK format data
    std::ifstream csv_file(test_dir_ / "multi_output.csv");
    std::string header_line;
    std::getline(csv_file, header_line);
    
    EXPECT_TRUE(header_line.find("location_source_value") != std::string::npos);
    EXPECT_TRUE(header_line.find("monthly_income_gbp") != std::string::npos);
    
    // Check first data line
    std::string data_line;
    std::getline(csv_file, data_line);
    
    // Should contain UK postcode
    EXPECT_TRUE(data_line.find("SW1A 1AA") != std::string::npos ||
                data_line.find("EC1A 1BB") != std::string::npos);
    
    logger_->info("Multi-format test completed: JSON and CSV files created");
}

/**
 * @brief Test memory-mapped file loader (with UK temperature data example)
 */
TEST_F(SpecializedLoadersIntegrationTest, TestMemoryMappedFileLoader) {
    GTEST_SKIP() << "Memory-mapped file loader not implemented - requires platform-specific code";
    
    // This test demonstrates what would be tested if mmap loader was implemented
    load::BatchLoaderOptions options;
    options.batch_size = 1000;
    options.worker_threads = 1; // Single-threaded for mmap

    size_t file_size_hint = test_records_.size() * 1024; // Estimate 1KB per record
    
    try {
        auto mmap_loader = std::make_unique<load::MmapBatchLoader>(options, file_size_hint);
        
        // Would test with UK temperature measurements
        for (auto& record : test_records_) {
            // Add UK temperature in Celsius
            record.setField("temperature_celsius", 5.0 + (std::rand() % 25));
            record.setField("measurement_location", "London Weather Station");
        }
        
        // Rest of test would follow...
        
    } catch (const common::LoadException& e) {
        // Expected for unimplemented feature
        EXPECT_TRUE(std::string(e.what()).find("not implemented") != std::string::npos);
    }
}

/**
 * @brief Test HTTP loader with UK API endpoints (mock)
 */
TEST_F(SpecializedLoadersIntegrationTest, TestHttpLoaderMock) {
    // Test HTTP loader with mock UK healthcare API endpoint
    load::HttpLoader::HttpOptions http_options;
    http_options.method = "POST";
    http_options.content_type = "application/json";
    http_options.timeout_seconds = 5;
    http_options.retry_count = 3;
    http_options.use_compression = true;
    http_options.headers["X-API-Key"] = "test-nhs-api-key";
    http_options.headers["X-Region"] = "UK";

    auto http_loader = std::make_unique<load::HttpLoader>(http_options);

    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = "mock://test"; // Use mock endpoint for testing
    config["batch_threshold"] = 10;

    core::ProcessingContext context;

    try {
        http_loader->initialize(config, context);
        
        // Test that loader is properly configured
        EXPECT_EQ(http_loader->get_type(), "http");
        
        // Test with UK-specific records
        core::RecordBatch batch;
        for (int i = 0; i < 5; ++i) {
            core::Record record;
            record.setField("nhs_number", std::format("NHS{:08d}", 10000000 + i));
            record.setField("gp_practice", std::format("GP{:04d}", 1000 + i));
            record.setField("ccg_code", "E38");  // UK Clinical Commissioning Group
            batch.addRecord(record);
        }
        
        // In mock mode, this should succeed
        size_t loaded = http_loader->load_batch(batch, context);
        EXPECT_EQ(loaded, 5);
        
        http_loader->finalize(context);
        
        logger_->info("HTTP mock test completed successfully");
        
    } catch (const common::LoadException& e) {
        // Real HTTP operations should fail with informative message
        if (std::string(e.what()).find("mock://test") == std::string::npos) {
            EXPECT_TRUE(std::string(e.what()).find("not implemented") != std::string::npos);
            logger_->info("HTTP loader correctly reports it needs external library");
        }
    }
}

/**
 * @brief Test S3 loader with UK region configuration (mock)
 */
TEST_F(SpecializedLoadersIntegrationTest, TestS3LoaderMock) {
    // Test S3 loader configured for UK (eu-west-2 London region)
    load::S3Loader::S3Options s3_options;
    s3_options.bucket_name = "mock-bucket"; // Use mock for testing
    s3_options.key_prefix = "uk-healthcare-data/omop/";
    s3_options.region = "eu-west-2"; // London region
    s3_options.access_key_id = "mock-key";
    s3_options.secret_access_key = "mock-secret";
    s3_options.use_multipart_upload = true;
    s3_options.storage_class = "STANDARD_IA"; // Infrequent access for UK compliance
    s3_options.server_side_encryption = true; // UK data protection requirement
    s3_options.metadata["Country"] = "UK";
    s3_options.metadata["DataProtectionAct"] = "2018";

    auto s3_loader = std::make_unique<load::S3Loader>(s3_options);

    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = "mock://s3";

    core::ProcessingContext context;

    try {
        s3_loader->initialize(config, context);
        
        // Test UK healthcare data upload
        for (const auto& record : test_records_) {
            // Would normally check if record contains UK PII and apply appropriate encryption
            s3_loader->load(record, context);
        }
        
        s3_loader->commit(context);
        
        logger_->info("S3 mock test completed - would upload to eu-west-2");
        
    } catch (const common::LoadException& e) {
        // Expected for unimplemented feature
        EXPECT_TRUE(std::string(e.what()).find("not implemented") != std::string::npos ||
                   std::string(e.what()).find("mock-bucket") != std::string::npos);
        logger_->info("S3 loader correctly reports it needs AWS SDK");
    }
}

/**
 * @brief Test performance comparison between loaders
 */
TEST_F(SpecializedLoadersIntegrationTest, TestLoaderPerformanceComparison) {
    const size_t num_records = 1000;
    
    // Generate larger dataset with UK data
    std::vector<core::Record> perf_records;
    for (size_t i = 0; i < num_records; ++i) {
        core::Record record = test_records_[i % test_records_.size()];
        record.setField("person_id", static_cast<int64_t>(10000 + i));
        record.setField("test_timestamp", std::chrono::system_clock::now());
        perf_records.push_back(record);
    }
    
    // Test JSON loader performance
    {
        load::JsonBatchLoader::JsonOptions json_options;
        json_options.pretty_print = false; // Faster without formatting
        json_options.array_output = false; // NDJSON is typically faster
        
        auto json_loader = std::make_unique<load::JsonBatchLoader>(
            load::BatchLoaderOptions{}, json_options);

        std::unordered_map<std::string, std::any> config;
        config["output_file"] = (test_dir_ / "perf_test.jsonl").string();

        core::ProcessingContext context;
        json_loader->initialize(config, context);

        auto start = std::chrono::steady_clock::now();
        
        for (const auto& record : perf_records) {
            json_loader->load(record, context);
        }
        json_loader->finalize(context);
        
        auto json_duration = std::chrono::steady_clock::now() - start;
        auto json_ms = std::chrono::duration_cast<std::chrono::milliseconds>(json_duration).count();
        
        logger_->info("JSON loader performance: {} records in {} ms ({:.0f} records/sec)",
                     num_records, json_ms, (num_records * 1000.0) / json_ms);
    }
    
    // Test CSV loader performance
    {
        auto csv_loader = std::make_unique<load::CsvBatchLoader>(
            load::BatchLoaderOptions{}, ',', '"');

        std::unordered_map<std::string, std::any> config;
        config["output_file"] = (test_dir_ / "perf_test.csv").string();

        core::ProcessingContext context;
        csv_loader->initialize(config, context);

        auto start = std::chrono::steady_clock::now();
        
        for (const auto& record : perf_records) {
            csv_loader->load(record, context);
        }
        csv_loader->finalize(context);
        
        auto csv_duration = std::chrono::steady_clock::now() - start;
        auto csv_ms = std::chrono::duration_cast<std::chrono::milliseconds>(csv_duration).count();
        
        logger_->info("CSV loader performance: {} records in {} ms ({:.0f} records/sec)",
                     num_records, csv_ms, (num_records * 1000.0) / csv_ms);
    }
    
    // Verify output file sizes
    auto json_size = std::filesystem::file_size(test_dir_ / "perf_test.jsonl");
    auto csv_size = std::filesystem::file_size(test_dir_ / "perf_test.csv");
    
    logger_->info("Output file sizes - JSON: {} bytes, CSV: {} bytes", 
                 json_size, csv_size);
    
    // CSV should typically be smaller than JSON
    EXPECT_LT(csv_size, json_size);
}

/**
 * @brief Test loader error handling with UK-specific validation
 */
TEST_F(SpecializedLoadersIntegrationTest, TestLoaderErrorHandling) {
    // Test JSON loader with invalid UK data
    load::JsonBatchLoader::JsonOptions json_options;
    json_options.pretty_print = true;
    
    auto json_loader = std::make_unique<load::JsonBatchLoader>(
        load::BatchLoaderOptions{}, json_options);

    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "error_test.json").string();

    core::ProcessingContext context;
    json_loader->initialize(config, context);

    // Create records with invalid UK data
    std::vector<core::Record> invalid_records;
    
    // Invalid NHS number (wrong format)
    core::Record invalid1;
    invalid1.setField("person_id", int64_t(9001));
    invalid1.setField("nhs_number", "INVALID-NHS");
    invalid1.setField("location_source_value", "BADPOST");
    invalid_records.push_back(invalid1);
    
    // Invalid UK phone number
    core::Record invalid2;
    invalid2.setField("person_id", int64_t(9002));
    invalid2.setField("phone_number", "123"); // Too short
    invalid2.setField("location_source_value", "SW1A 1AA");
    invalid_records.push_back(invalid2);
    
    // Process records - should handle errors gracefully
    size_t loaded = 0;
    size_t failed = 0;
    
    for (const auto& record : invalid_records) {
        try {
            if (json_loader->load(record, context)) {
                loaded++;
            } else {
                failed++;
            }
        } catch (const std::exception& e) {
            failed++;
            logger_->warn("Expected error: {}", e.what());
        }
    }
    
    json_loader->finalize(context);
    
    // Should have processed all records despite invalid data
    EXPECT_EQ(loaded + failed, invalid_records.size());
    
    // Verify JSON file was created
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "error_test.json"));
    
    logger_->info("Error handling test completed: {} loaded, {} failed", loaded, failed);
}

} // namespace omop::test