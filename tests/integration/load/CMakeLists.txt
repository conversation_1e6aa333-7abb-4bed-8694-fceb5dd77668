# Load module integration tests with UK localization support

# Find required packages
find_package(Threads REQUIRED)

# Add test helper library
add_library(load_test_helpers INTERFACE)
target_include_directories(load_test_helpers INTERFACE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Integration test sources
set(LOAD_INTEGRATION_TEST_SOURCES
    test_batch_inserter_integration.cpp
    test_database_loader_integration.cpp
    test_loader_strategies_integration.cpp
    test_parallel_loading_integration.cpp
    test_specialized_loaders_integration.cpp
    test_helpers.h  # Common test helpers header
)

add_executable(load_integration_tests ${LOAD_INTEGRATION_TEST_SOURCES})

target_link_libraries(load_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        load_test_helpers
        gtest
        gtest_main
        nlohmann_json::nlohmann_json
        Threads::Threads
        ${PostgreSQL_LIBRARIES}
)

target_include_directories(load_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
        ${PostgreSQL_INCLUDE_DIRS}
)

# Ensure proper C++ standard
target_compile_features(load_integration_tests PRIVATE cxx_std_20)

# Add UK locale compile definition
target_compile_definitions(load_integration_tests
    PRIVATE
        UK_LOCALE_ENABLED
        TEST_DATABASE_HOST="${TEST_DATABASE_HOST}"
        TEST_DATABASE_PORT="${TEST_DATABASE_PORT}"
)

add_test(
    NAME load_integration_tests
    COMMAND load_integration_tests
)

set_tests_properties(load_integration_tests PROPERTIES
    TIMEOUT 600  # Increased timeout for database operations
    LABELS "integration;load;specialized"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR};TZ=Europe/London;LC_ALL=en_GB.UTF-8"
)

# Set locale for UK tests
if(UNIX AND NOT APPLE)
    set_tests_properties(load_integration_tests PROPERTIES
        ENVIRONMENT "LANG=en_GB.UTF-8;LC_TIME=en_GB.UTF-8;LC_NUMERIC=en_GB.UTF-8"
    )
endif()

# Add individual test targets for better granularity
foreach(test_source ${LOAD_INTEGRATION_TEST_SOURCES})
    get_filename_component(test_name ${test_source} NAME_WE)
    add_test(
        NAME "load_integration::${test_name}"
        COMMAND load_integration_tests --gtest_filter="${test_name}*"
    )
    set_tests_properties("load_integration::${test_name}" PROPERTIES
        TIMEOUT 120
        LABELS "integration;load"
    )
endforeach()

# Add test dependencies
if(TARGET omop_extract_tests)
    add_dependencies(load_integration_tests omop_extract_tests)
endif()

if(TARGET omop_transform_tests)
    add_dependencies(load_integration_tests omop_transform_tests)
endif()

# Add custom target for UK-specific tests
add_custom_target(uk_integration_tests
    COMMAND ${CMAKE_CTEST_COMMAND} -R ".*UK.*" -V
    DEPENDS load_integration_tests
    COMMENT "Running UK-specific integration tests"
)