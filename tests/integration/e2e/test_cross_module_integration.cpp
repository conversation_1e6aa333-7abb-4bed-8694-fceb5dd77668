// tests/integration/cross_module/test_cross_module_integration.cpp
// Tests interactions and coordination between different modules in the ETL pipeline

#include <gtest/gtest.h>
#include <memory>
#include <thread>
#include <chrono>
#include <future>
#include "core/pipeline.h"
#include "core/job_manager.h"
#include "core/job_scheduler.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "load/database_loader.h"
#include "load/batch_loader.h"
#include "service/etl_service.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"

namespace omop::test {

class CrossModuleIntegrationTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize configuration
        config_ = std::make_shared<common::ConfigurationManager>();
        loadTestConfiguration();

        // Initialize logging
        common::LoggingConfig::initialize_default();
        logger_ = common::Logger::get("cross-module-test");

        // Initialize vocabulary service
        auto vocab_connection = createTestConnection();
        transform::VocabularyServiceManager::initialize(
            std::move(vocab_connection), 10000);
        loadTestVocabulary();

        // Initialize pipeline manager
        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);

        // Initialize ETL service
        etl_service_ = std::make_shared<service::ETLService>(
            config_, pipeline_manager_);
    }

    void TearDown() override {
        transform::VocabularyServiceManager::reset();
        pipeline_manager_->shutdown();
        DatabaseFixture::TearDown();
    }

    void loadTestConfiguration() {
        // Create test configuration
        std::string config_yaml = R"(
etl:
  batch_size: 1000
  error_threshold: 0.05
  validate_records: true

source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: source_db
  username: test
  password: test

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: omop_cdm
  username: test
  password: test

mappings:
  person:
    source_table: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary_name: Gender
      - source_column: birth_date
        target_column: year_of_birth
        type: date_calculation
        parameters:
          operation: year

  condition_occurrence:
    source_table: diagnoses
    target_table: condition_occurrence
    transformations:
      - source_column: diagnosis_code
        target_column: condition_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary_name: ICD10CM
          source_vocabulary: ICD10
)";

        config_->load_config_from_string(config_yaml);
    }

    void loadTestVocabulary() {
        // Load test vocabulary mappings
        auto& vocab_service = transform::VocabularyServiceManager::instance();

        // Add gender mappings
        vocab_service.add_mapping({
            .source_value = "M",
            .source_vocabulary = "Gender",
            .target_concept_id = 8507,
            .target_vocabulary = "OMOP"
        });

        vocab_service.add_mapping({
            .source_value = "F",
            .source_vocabulary = "Gender",
            .target_concept_id = 8532,
            .target_vocabulary = "OMOP"
        });

        // Add ICD10 mappings
        vocab_service.add_mapping({
            .source_value = "I10",
            .source_vocabulary = "ICD10CM",
            .target_concept_id = 320128,
            .target_vocabulary = "OMOP"
        });
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<common::Logger> logger_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
};

// Test extract-transform-load coordination
TEST_F(CrossModuleIntegrationTest, ExtractTransformLoadCoordination) {
    // Create test data with multiple formats
    auto csv_file = generateTestCSV(1000, "patients.csv", {
        {"patient_id", "gender", "birth_date", "race"},
        {"1", "M", "1980-05-15", "White"},
        {"2", "F", "1975-08-22", "Black"},
        {"3", "M", "1990-12-01", "Asian"}
    });

    auto json_file = generateTestJSON(500, "conditions.json", {
        {"patient_id", 1, "diagnosis_code", "I10", "diagnosis_date", "2023-01-15"},
        {"patient_id", 2, "diagnosis_code", "I10", "diagnosis_date", "2023-02-20"}
    });

    // Create pipeline builder
    core::PipelineBuilder builder;

    // Configure multi-source extraction
    auto csv_extractor = std::make_unique<extract::CsvExtractor>();
    csv_extractor->initialize({{"filepath", csv_file}}, core::ProcessingContext{});

    auto json_extractor = std::make_unique<extract::JsonExtractor>();
    json_extractor->initialize({{"filepath", json_file}}, core::ProcessingContext{});

    // Create composite extractor
    class CompositeExtractor : public core::IExtractor {
    public:
        void add_extractor(std::unique_ptr<core::IExtractor> extractor) {
            extractors_.push_back(std::move(extractor));
        }

        void initialize(const std::unordered_map<std::string, std::any>& config,
                       core::ProcessingContext& context) override {
            for (auto& extractor : extractors_) {
                extractor->initialize(config, context);
            }
        }

        core::RecordBatch extract_batch(size_t batch_size,
                                       core::ProcessingContext& context) override {
            core::RecordBatch batch;
            size_t remaining = batch_size;

            for (auto& extractor : extractors_) {
                if (extractor->has_more_data() && remaining > 0) {
                    auto sub_batch = extractor->extract_batch(remaining, context);
                    for (const auto& record : sub_batch) {
                        batch.addRecord(record);
                        remaining--;
                    }
                }
            }

            return batch;
        }

        bool has_more_data() const override {
            for (const auto& extractor : extractors_) {
                if (extractor->has_more_data()) return true;
            }
            return false;
        }

        std::string get_type() const override { return "composite"; }

        void finalize(core::ProcessingContext& context) override {
            for (auto& extractor : extractors_) {
                extractor->finalize(context);
            }
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            std::unordered_map<std::string, std::any> combined_stats;
            int total_records = 0;

            for (size_t i = 0; i < extractors_.size(); ++i) {
                auto stats = extractors_[i]->get_statistics();
                combined_stats[fmt::format("extractor_{}", i)] = stats;

                // Sum total records
                if (stats.find("extracted_count") != stats.end()) {
                    total_records += std::any_cast<size_t>(stats["extracted_count"]);
                }
            }

            combined_stats["total_extracted"] = total_records;
            return combined_stats;
        }

    private:
        std::vector<std::unique_ptr<core::IExtractor>> extractors_;
    };

    auto composite_extractor = std::make_unique<CompositeExtractor>();
    composite_extractor->add_extractor(std::move(csv_extractor));
    composite_extractor->add_extractor(std::move(json_extractor));

    // Configure transformation with vocabulary service
    auto transformer = std::make_unique<transform::TransformationEngine>();
    transformer->initialize({
        {"table_mapping", config_->get_table_mapping("person")}
    }, core::ProcessingContext{});

    // Configure multi-table loader
    auto connection = createTestConnection();
    auto loader = std::make_unique<load::OmopDatabaseLoader>(
        std::move(connection),
        load::DatabaseLoaderOptions{.batch_size = 500}
    );

    // Build and run pipeline
    auto pipeline = builder
        .with_config(core::PipelineConfig{
            .batch_size = 100,
            .validate_records = true
        })
        .with_extractor(std::move(composite_extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .with_progress_callback([this](const core::JobInfo& info) {
            logger_->info("Progress: {}% ({}/{})",
                         info.progress(),
                         info.processed_records,
                         info.total_records);
        })
        .build();

    // Execute pipeline
    auto future = pipeline->start("cross-module-test-001");
    auto result = future.get();

    // Verify results
    EXPECT_EQ(core::JobStatus::Completed, result.status);
    EXPECT_GT(result.processed_records, 0);
    EXPECT_LT(result.error_rate(), 0.05); // Less than 5% errors

    // Verify data was properly transformed and loaded
    auto verify_conn = createTestConnection();

    // Check person table
    auto person_result = verify_conn->execute_query(
        "SELECT COUNT(*) FROM omop_cdm.person WHERE gender_concept_id IN (8507, 8532)");

    if (person_result && person_result->next()) {
        auto count = std::any_cast<int64_t>(person_result->get_value(0));
        EXPECT_GT(count, 0);
    }
}

// Test job scheduler and manager integration
TEST_F(CrossModuleIntegrationTest, JobSchedulerManagerIntegration) {
    // Create job manager
    auto job_manager = std::make_shared<core::JobManager>(config_, logger_);
    job_manager->start();

    // Create job scheduler
    core::JobScheduler scheduler(job_manager);
    scheduler.start();

    // Define multiple job schedules
    std::vector<core::JobSchedule> schedules = {
        {
            .schedule_id = "hourly-person-etl",
            .job_config_id = "person-etl-config",
            .trigger_type = core::TriggerType::SCHEDULED,
            .cron_expression = "0 * * * *", // Every hour
            .enabled = true
        },
        {
            .schedule_id = "daily-condition-etl",
            .job_config_id = "condition-etl-config",
            .trigger_type = core::TriggerType::SCHEDULED,
            .cron_expression = "0 2 * * *", // Daily at 2 AM
            .enabled = true
        },
        {
            .schedule_id = "dependent-measurement-etl",
            .job_config_id = "measurement-etl-config",
            .trigger_type = core::TriggerType::DEPENDENCY,
            .dependencies = {"person-etl-config", "condition-etl-config"},
            .enabled = true
        }
    };

    // Add schedules
    for (const auto& schedule : schedules) {
        scheduler.addSchedule(schedule);
    }

    // Create job configurations
    core::JobConfig person_job_config{
        .job_id = "person-etl-001",
        .job_name = "Person ETL Job",
        .pipeline_config_path = "configs/person_etl.yaml",
        .priority = core::JobPriority::HIGH
    };

    core::JobConfig condition_job_config{
        .job_id = "condition-etl-001",
        .job_name = "Condition ETL Job",
        .pipeline_config_path = "configs/condition_etl.yaml",
        .priority = core::JobPriority::NORMAL
    };

    // Submit immediate job
    auto immediate_job_id = scheduler.submitJob(
        person_job_config,
        core::JobPriority::HIGH
    );

    EXPECT_FALSE(immediate_job_id.empty());

    // Trigger scheduled job manually
    auto triggered = scheduler.triggerSchedule("hourly-person-etl");
    EXPECT_TRUE(triggered.has_value());

    // Verify job queue
    auto queued_jobs = scheduler.getQueuedJobs();
    EXPECT_GE(queued_jobs.size(), 1);

    // Wait for some processing
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Check scheduler statistics
    auto stats = scheduler.getStatistics();
    EXPECT_GT(std::any_cast<size_t>(stats["jobs_scheduled"]), 0);

    // Verify schedules are active
    auto all_schedules = scheduler.getAllSchedules();
    EXPECT_EQ(3, all_schedules.size());

    // Clean up
    scheduler.stop();
    job_manager->stop();
}

// Test ETL service with monitoring integration
TEST_F(CrossModuleIntegrationTest, ETLServiceWithMonitoring) {
    // Create ETL monitor
    service::ETLMonitor monitor(etl_service_);

    // Set alert thresholds
    monitor.set_thresholds(
        0.1,    // 10% error rate threshold
        50.0,   // 50 records/sec performance threshold
        2048    // 2GB memory threshold
    );

    // Set up alert callback
    std::vector<service::ETLMonitor::Alert> received_alerts;
    std::mutex alert_mutex;

    monitor.set_alert_callback(
        [&received_alerts, &alert_mutex](const service::ETLMonitor::Alert& alert) {
            std::lock_guard<std::mutex> lock(alert_mutex);
            received_alerts.push_back(alert);
        });

    monitor.start();

    // Create multiple ETL job requests
    std::vector<service::ETLJobRequest> job_requests = {
        {
            .name = "Fast Job",
            .description = "Should complete quickly",
            .source_table = "fast_table",
            .target_table = "person",
            .extractor_config = {{"record_count", 1000}},
            .pipeline_config = {.batch_size = 100}
        },
        {
            .name = "Slow Job",
            .description = "Should trigger performance alert",
            .source_table = "slow_table",
            .target_table = "condition_occurrence",
            .extractor_config = {{"record_count", 10000}, {"delay_ms", 10}},
            .pipeline_config = {.batch_size = 10}
        },
        {
            .name = "Error Job",
            .description = "Should trigger error alert",
            .source_table = "error_table",
            .target_table = "measurement",
            .extractor_config = {{"error_rate", 0.2}},
            .pipeline_config = {.batch_size = 100}
        }
    };

    // Submit jobs
    std::vector<std::string> job_ids;
    for (const auto& request : job_requests) {
        auto job_id = etl_service_->create_job(request);
        job_ids.push_back(job_id);
    }

    // Wait for jobs to process
    std::this_thread::sleep_for(std::chrono::seconds(10));

    // Check job results
    for (const auto& job_id : job_ids) {
        auto result = etl_service_->get_job_result(job_id);
        ASSERT_TRUE(result.has_value());

        logger_->info("Job {} completed with status: {}, error rate: {}%",
                     job_id,
                     static_cast<int>(result->status),
                     result->error_records * 100.0 / result->total_records);
    }

    // Verify monitoring alerts
    {
        std::lock_guard<std::mutex> lock(alert_mutex);

        // Should have at least one alert
        EXPECT_GT(received_alerts.size(), 0);

        // Check alert types
        bool has_performance_alert = false;
        bool has_error_alert = false;

        for (const auto& alert : received_alerts) {
            if (alert.type == service::ETLMonitor::AlertType::SlowPerformance) {
                has_performance_alert = true;
            }
            if (alert.type == service::ETLMonitor::AlertType::HighErrorRate) {
                has_error_alert = true;
            }
        }

        EXPECT_TRUE(has_performance_alert || has_error_alert);
    }

    // Check system metrics
    auto system_metrics = monitor.get_system_metrics();
    EXPECT_GT(system_metrics["cpu_usage"], 0.0);
    EXPECT_GT(system_metrics["memory_usage"], 0.0);

    monitor.stop();
}

// Test vocabulary service integration with transformation
TEST_F(CrossModuleIntegrationTest, VocabularyServiceTransformationIntegration) {
    // Load additional vocabulary mappings
    auto& vocab_service = transform::VocabularyServiceManager::instance();

    // Add medication mappings
    std::vector<transform::VocabularyMapping> med_mappings = {
        {
            .source_value = "ASPIRIN 81MG",
            .source_vocabulary = "MEDICATION",
            .target_concept_id = 1112807,
            .mapping_confidence = 1.0f
        },
        {
            .source_value = "METFORMIN 500MG",
            .source_vocabulary = "MEDICATION",
            .target_concept_id = 1503297,
            .mapping_confidence = 0.95f
        }
    };

    for (const auto& mapping : med_mappings) {
        vocab_service.add_mapping(mapping);
    }

    // Create test data with vocabulary codes
    auto csv_file = generateTestCSV(500, "medications.csv", {
        {"patient_id", "medication_name", "start_date", "days_supply"},
        {"1", "ASPIRIN 81MG", "2023-01-15", "30"},
        {"1", "METFORMIN 500MG", "2023-01-20", "90"},
        {"2", "UNKNOWN DRUG", "2023-02-01", "30"}
    });

    // Create transformation engine with vocabulary support
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Register vocabulary transformation
    transformer->register_transformation("vocabulary_mapping",
        []() -> std::unique_ptr<transform::FieldTransformation> {
            return std::make_unique<transform::VocabularyTransformation>(
                transform::VocabularyServiceManager::instance());
        });

    // Configure transformation rules
    common::TableMapping drug_mapping;
    drug_mapping = common::TableMapping(YAML::Load(R"(
source_table: medications
target_table: drug_exposure
transformations:
  - source_column: patient_id
    target_column: person_id
    type: direct
  - source_column: medication_name
    target_column: drug_concept_id
    type: vocabulary_mapping
    parameters:
      vocabulary_name: MEDICATION
      default_concept_id: 0
  - source_column: start_date
    target_column: drug_exposure_start_date
    type: date_transform
    parameters:
      input_format: "%Y-%m-%d"
  - source_column: days_supply
    target_column: days_supply
    type: numeric_transform
)"));

    transformer->initialize({
        {"table_mapping", std::any(drug_mapping)}
    }, core::ProcessingContext{});

    // Set up pipeline
    auto extractor = std::make_unique<extract::CsvExtractor>();
    extractor->initialize({{"filepath", csv_file}}, core::ProcessingContext{});

    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(std::move(connection));

    core::ETLPipeline pipeline;
    pipeline.set_extractor(std::move(extractor));
    pipeline.set_transformer(std::move(transformer));
    pipeline.set_loader(std::move(loader));

    // Run pipeline
    auto future = pipeline.start("vocab-integration-test");
    auto result = future.get();

    EXPECT_EQ(core::JobStatus::Completed, result.status);
    EXPECT_EQ(3, result.total_records);

    // Verify vocabulary mappings were applied
    auto verify_conn = createTestConnection();

    // Check known medications
    auto known_meds = verify_conn->execute_query(
        "SELECT COUNT(*) FROM drug_exposure WHERE drug_concept_id IN (1112807, 1503297)");

    if (known_meds && known_meds->next()) {
        auto count = std::any_cast<int64_t>(known_meds->get_value(0));
        EXPECT_EQ(2, count); // Two known medications
    }

    // Check unknown medication (should use default concept ID)
    auto unknown_meds = verify_conn->execute_query(
        "SELECT COUNT(*) FROM drug_exposure WHERE drug_concept_id = 0");

    if (unknown_meds && unknown_meds->next()) {
        auto count = std::any_cast<int64_t>(unknown_meds->get_value(0));
        EXPECT_EQ(1, count); // One unknown medication
    }

    // Check vocabulary cache statistics
    auto cache_stats = vocab_service.get_cache_stats();
    EXPECT_GT(cache_stats.hits + cache_stats.misses, 0);
    logger_->info("Vocabulary cache hit rate: {:.2f}%", cache_stats.hit_rate * 100);
}

// Test parallel pipeline execution
TEST_F(CrossModuleIntegrationTest, ParallelPipelineExecution) {
    // Create multiple pipelines for different tables
    std::vector<std::pair<std::string, std::unique_ptr<core::ETLPipeline>>> pipelines;

    // Tables to process in parallel
    std::vector<std::string> tables = {"person", "visit_occurrence",
                                      "condition_occurrence", "drug_exposure"};

    for (const auto& table : tables) {
        // Generate test data for each table
        auto csv_file = generateTestCSV(2000,
            fmt::format("{}_data.csv", table));

        // Create pipeline
        auto pipeline = std::make_unique<core::ETLPipeline>(
            core::PipelineConfig{
                .batch_size = 500,
                .max_parallel_batches = 2
            });

        // Set up components
        auto extractor = std::make_unique<extract::CsvExtractor>();
        extractor->initialize({{"filepath", csv_file}}, core::ProcessingContext{});

        auto transformer = std::make_unique<transform::TransformationEngine>();

        auto connection = createTestConnection();
        auto loader = std::make_unique<load::DatabaseLoader>(
            std::move(connection),
            load::DatabaseLoaderOptions{
                .batch_size = 500,
                .use_bulk_insert = true
            });

        pipeline->set_extractor(std::move(extractor));
        pipeline->set_transformer(std::move(transformer));
        pipeline->set_loader(std::move(loader));

        pipelines.emplace_back(table, std::move(pipeline));
    }

    // Submit all pipelines to pipeline manager
    std::vector<std::pair<std::string, std::string>> job_ids;

    for (auto& [table, pipeline] : pipelines) {
        auto job_id = pipeline_manager_->submit_job(
            fmt::format("{}_parallel_load", table),
            std::move(pipeline));

        job_ids.emplace_back(table, job_id);
    }

    // Monitor progress
    bool all_completed = false;
    auto start_time = std::chrono::steady_clock::now();
    auto timeout = std::chrono::seconds(60);

    while (!all_completed) {
        all_completed = true;

        for (const auto& [table, job_id] : job_ids) {
            auto status = pipeline_manager_->get_job_status(job_id);

            if (status && *status != core::JobStatus::Completed &&
                *status != core::JobStatus::Failed) {
                all_completed = false;
            }

            // Log progress
            auto info = pipeline_manager_->get_job_info(job_id);
            if (info) {
                logger_->debug("Table {}: Status={}, Progress={:.1f}%",
                             table,
                             static_cast<int>(*status),
                             info->progress());
            }
        }

        // Check timeout
        if (std::chrono::steady_clock::now() - start_time > timeout) {
            FAIL() << "Parallel pipeline execution timed out";
        }

        if (!all_completed) {
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }

    // Verify all jobs completed successfully
    for (const auto& [table, job_id] : job_ids) {
        auto info = pipeline_manager_->get_job_info(job_id);
        ASSERT_TRUE(info.has_value());

        EXPECT_EQ(core::JobStatus::Completed, info->status);
        EXPECT_EQ(2000, info->total_records);
        EXPECT_GT(info->processed_records, 1900); // Allow some errors

        logger_->info("Table {} completed: {} records in {:.2f}s",
                     table,
                     info->processed_records,
                     info->duration().count());
    }

    // Check system handled parallel load efficiently
    auto all_jobs = pipeline_manager_->get_all_jobs();

    // Calculate total throughput
    double total_records = 0;
    double max_duration = 0;

    for (const auto& job_info : all_jobs) {
        total_records += job_info.processed_records;
        max_duration = std::max(max_duration, job_info.duration().count());
    }

    double throughput = total_records / max_duration;
    logger_->info("Parallel pipeline throughput: {:.1f} records/second", throughput);

    // Throughput should be better than sequential processing
    EXPECT_GT(throughput, 1000.0); // Expect at least 1000 records/sec
}

// Test end-to-end data lineage tracking
TEST_F(CrossModuleIntegrationTest, DataLineageTracking) {
    // Enable lineage tracking in processing context
    core::ProcessingContext context;
    context.set_data("enable_lineage", true);
    context.set_job_id("lineage-test-001");

    // Create source data with identifiable records
    auto csv_file = generateTestCSV(100, "lineage_source.csv", {
        {"record_id", "patient_id", "value", "timestamp"},
        {"SRC-001", "1001", "100.5", "2023-01-15T10:30:00"},
        {"SRC-002", "1002", "98.6", "2023-01-15T11:00:00"}
    });

    // Create extractor that adds source metadata
    class LineageExtractor : public extract::CsvExtractor {
    public:
        core::RecordBatch extract_batch(size_t batch_size,
                                       core::ProcessingContext& context) override {
            auto batch = CsvExtractor::extract_batch(batch_size, context);

            // Add lineage metadata to each record
            for (auto& record : batch.getRecordsMutable()) {
                auto& metadata = record.getMetadataMutable();
                metadata.source_table = filepath_;
                metadata.extraction_time = std::chrono::system_clock::now();

                // Add source row identifier
                if (record.hasField("record_id")) {
                    metadata.custom["source_record_id"] =
                        std::any_cast<std::string>(record.getField("record_id"));
                }
            }

            return batch;
        }
    };

    // Create transformer that tracks transformations
    class LineageTransformer : public transform::TransformationEngine {
    public:
        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            auto result = TransformationEngine::transform(record, context);

            if (result) {
                // Add transformation metadata
                auto& metadata = result->getMetadataMutable();
                metadata.custom["transformation_time"] =
                    std::chrono::system_clock::now();
                metadata.custom["transformation_version"] = "1.0";
                metadata.custom["job_id"] = context.job_id();

                // Track field transformations
                std::vector<std::string> transformed_fields;
                for (const auto& field : result->getFieldNames()) {
                    if (!record.hasField(field) ||
                        record.getField(field).type() != result->getField(field).type()) {
                        transformed_fields.push_back(field);
                    }
                }
                metadata.custom["transformed_fields"] = transformed_fields;
            }

            return result;
        }
    };

    // Create loader that records lineage
    class LineageLoader : public load::DatabaseLoader {
    public:
        using DatabaseLoader::DatabaseLoader;

        bool load(const core::Record& record, core::ProcessingContext& context) override {
            // Store lineage information
            const auto& metadata = record.getMetadata();

            if (metadata.custom.find("source_record_id") != metadata.custom.end()) {
                lineage_map_[std::any_cast<std::string>(
                    metadata.custom.at("source_record_id"))] = {
                    .target_table = metadata.target_table,
                    .load_time = std::chrono::system_clock::now(),
                    .job_id = context.job_id()
                };
            }

            return DatabaseLoader::load(record, context);
        }

        struct LineageInfo {
            std::string target_table;
            std::chrono::system_clock::time_point load_time;
            std::string job_id;
        };

        std::unordered_map<std::string, LineageInfo> lineage_map_;
    };

    // Build pipeline with lineage tracking
    auto extractor = std::make_unique<LineageExtractor>();
    extractor->initialize({{"filepath", csv_file}}, context);

    auto transformer = std::make_unique<LineageTransformer>();
    transformer->initialize({}, context);

    auto connection = createTestConnection();
    auto loader = std::make_unique<LineageLoader>(std::move(connection));
    auto loader_ptr = loader.get();

    core::ETLPipeline pipeline;
    pipeline.set_extractor(std::move(extractor));
    pipeline.set_transformer(std::move(transformer));
    pipeline.set_loader(std::move(loader));

    // Run pipeline
    auto future = pipeline.start("lineage-tracking-job");
    auto result = future.get();

    EXPECT_EQ(core::JobStatus::Completed, result.status);

    // Verify lineage was tracked
    EXPECT_GT(loader_ptr->lineage_map_.size(), 0);

    // Check specific lineage entries
    auto src001_lineage = loader_ptr->lineage_map_.find("SRC-001");
    ASSERT_NE(loader_ptr->lineage_map_.end(), src001_lineage);

    EXPECT_FALSE(src001_lineage->second.target_table.empty());
    EXPECT_EQ("lineage-test-001", src001_lineage->second.job_id);

    // Log lineage information
    for (const auto& [source_id, lineage] : loader_ptr->lineage_map_) {
        logger_->info("Lineage: {} -> {} (job: {})",
                     source_id,
                     lineage.target_table,
                     lineage.job_id);
    }
}

} // namespace omop::test