// Updated end-to-end integration test for current OMOP ETL implementation
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "core/pipeline.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <filesystem>
#include <fstream>
#include <chrono>
#include <thread>

namespace omop::test::integration {

/**
 * @brief Updated end-to-end integration test class for comprehensive ETL testing
 */
class E2EIntegrationTestUpdated : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize configuration
        config_manager_ = std::make_shared<common::ConfigurationManager>();
        load_comprehensive_config();

        // Initialize pipeline manager first
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        
        // Initialize ETL service
        etl_service_ = std::make_shared<service::ETLService>(config_manager_, pipeline_manager_);

        // Initialize core pipeline
        pipeline_ = std::make_shared<core::Pipeline>(config_manager_);

        // Create comprehensive test data
        create_comprehensive_test_data();

        // Set up OMOP CDM tables
        setup_omop_cdm_tables();
    }

    void TearDown() override {
        cleanup_test_data();
        DatabaseFixture::TearDown();
    }

    void load_comprehensive_config() {
        std::string config_yaml = R"(
            etl:
                batch_size: 50
                max_workers: 3
                error_threshold: 0.02
                enable_validation: true
                enable_metrics: true
                
            extractors:
                csv:
                    delimiter: ","
                    quote_char: "\""
                    has_header: true
                    encoding: "utf-8"
                    
                json:
                    root_path: "$"
                    date_format: "yyyy-MM-dd"
                    
            transformers:
                vocabulary_service:
                    cache_size: 1000
                    lookup_timeout: 5
                    
                validation:
                    strict_mode: false
                    log_validation_errors: true
                    
            loaders:
                database:
                    batch_size: 25
                    retry_attempts: 2
                    timeout_seconds: 30
                    
            omop:
                cdm_version: "6.0"
                vocabularies:
                    - "SNOMED"
                    - "ICD10CM"
                    - "LOINC"
                    - "RxNorm"
        )";
        
        config_manager_->load_config_from_string(config_yaml);
    }

    void create_comprehensive_test_data() {
        test_data_dir_ = std::filesystem::temp_directory_path() / "e2e_integration_test";
        std::filesystem::create_directories(test_data_dir_);

        create_clinical_data_files();
        create_vocabulary_files();
        create_mapping_configurations();
    }

    void create_clinical_data_files() {
        // Create comprehensive patient data
        std::ofstream patients_file(test_data_dir_ / "patients.csv");
        patients_file << "patient_id,first_name,last_name,birth_date,death_date,gender,race,ethnicity,address,zip_code\n";
        patients_file << "P001,John,Doe,1980-01-15,,M,White,Not Hispanic,123 Main St,12345\n";
        patients_file << "P002,Jane,Smith,1975-05-20,,F,White,Not Hispanic,456 Oak Ave,12346\n";
        patients_file << "P003,Maria,Garcia,1990-03-10,,F,Hispanic,Hispanic,789 Pine Rd,12347\n";
        patients_file << "P004,David,Chen,1985-07-25,,M,Asian,Not Hispanic,321 Elm St,12348\n";
        patients_file << "P005,Sarah,Johnson,1978-12-05,2023-06-15,F,Black,Not Hispanic,654 Maple Dr,12349\n";
        patients_file.close();

        // Create visit data
        std::ofstream visits_file(test_data_dir_ / "visits.csv");
        visits_file << "visit_id,patient_id,visit_start_date,visit_start_time,visit_end_date,visit_end_time,visit_type,provider_id,care_site_id\n";
        visits_file << "V001,P001,2023-01-15,09:00:00,2023-01-15,10:30:00,Outpatient,PR001,CS001\n";
        visits_file << "V002,P002,2023-02-20,14:00:00,2023-02-22,16:00:00,Inpatient,PR002,CS001\n";
        visits_file << "V003,P003,2023-03-10,11:00:00,2023-03-10,12:00:00,Emergency,PR003,CS002\n";
        visits_file << "V004,P001,2023-04-05,13:30:00,2023-04-05,14:45:00,Outpatient,PR001,CS001\n";
        visits_file << "V005,P004,2023-05-12,08:00:00,2023-05-14,10:00:00,Inpatient,PR002,CS001\n";
        visits_file.close();

        // Create condition data
        std::ofstream conditions_file(test_data_dir_ / "conditions.csv");
        conditions_file << "condition_id,patient_id,visit_id,condition_start_date,condition_end_date,condition_code,condition_code_system,condition_description\n";
        conditions_file << "C001,P001,V001,2023-01-15,,401.9,ICD10CM,Essential hypertension\n";
        conditions_file << "C002,P002,V002,2023-02-20,2023-02-22,250.00,ICD10CM,Diabetes mellitus\n";
        conditions_file << "C003,P003,V003,2023-03-10,,786.50,ICD10CM,Chest pain\n";
        conditions_file << "C004,P001,V004,2023-04-05,,401.9,ICD10CM,Essential hypertension\n";
        conditions_file << "C005,P004,V005,2023-05-12,,427.31,ICD10CM,Atrial fibrillation\n";
        conditions_file.close();

        // Create medication data
        std::ofstream medications_file(test_data_dir_ / "medications.csv");
        medications_file << "medication_id,patient_id,visit_id,drug_start_date,drug_end_date,drug_code,drug_code_system,drug_name,dose_value,dose_unit\n";
        medications_file << "M001,P001,V001,2023-01-15,2023-07-15,123456,RxNorm,Lisinopril,10,mg\n";
        medications_file << "M002,P002,V002,2023-02-20,,789012,RxNorm,Metformin,500,mg\n";
        medications_file << "M003,P004,V005,2023-05-12,,345678,RxNorm,Warfarin,5,mg\n";
        medications_file.close();

        // Create procedure data
        std::ofstream procedures_file(test_data_dir_ / "procedures.csv");
        procedures_file << "procedure_id,patient_id,visit_id,procedure_date,procedure_code,procedure_code_system,procedure_description\n";
        procedures_file << "PR001,P001,V001,2023-01-15,99213,CPT4,Office visit established patient\n";
        procedures_file << "PR002,P002,V002,2023-02-20,99223,CPT4,Initial hospital care\n";
        procedures_file << "PR003,P003,V003,2023-03-10,99281,CPT4,Emergency department visit\n";
        procedures_file << "PR004,P001,V004,2023-04-05,99214,CPT4,Office visit established patient\n";
        procedures_file << "PR005,P004,V005,2023-05-12,93005,CPT4,Electrocardiogram\n";
        procedures_file.close();

        // Create measurement/lab data
        std::ofstream measurements_file(test_data_dir_ / "measurements.csv");
        measurements_file << "measurement_id,patient_id,visit_id,measurement_date,measurement_code,measurement_code_system,measurement_name,value_numeric,value_text,unit,range_low,range_high\n";
        measurements_file << "L001,P001,V001,2023-01-15,33747-0,LOINC,Blood pressure systolic,140,,mmHg,90,140\n";
        measurements_file << "L002,P001,V001,2023-01-15,8462-4,LOINC,Blood pressure diastolic,90,,mmHg,60,90\n";
        measurements_file << "L003,P002,V002,2023-02-20,33747-0,LOINC,Glucose,180,mg/dL,mg/dL,70,100\n";
        measurements_file << "L004,P004,V005,2023-05-12,6598-7,LOINC,Troponin I,0.8,,ng/mL,0,0.04\n";
        measurements_file.close();
    }

    void create_vocabulary_files() {
        // Create vocabulary mapping files (simplified for testing)
        std::ofstream gender_map(test_data_dir_ / "gender_mapping.csv");
        gender_map << "source_value,target_concept_id,target_concept_name\n";
        gender_map << "M,8507,MALE\n";
        gender_map << "F,8532,FEMALE\n";
        gender_map << "Male,8507,MALE\n";
        gender_map << "Female,8532,FEMALE\n";
        gender_map.close();

        std::ofstream race_map(test_data_dir_ / "race_mapping.csv");
        race_map << "source_value,target_concept_id,target_concept_name\n";
        race_map << "White,8527,White\n";
        race_map << "Black,8516,Black or African American\n";
        race_map << "Asian,8515,Asian\n";
        race_map << "Hispanic,8527,White\n";
        race_map.close();

        std::ofstream visit_type_map(test_data_dir_ / "visit_type_mapping.csv");
        visit_type_map << "source_value,target_concept_id,target_concept_name\n";
        visit_type_map << "Outpatient,9202,Outpatient Visit\n";
        visit_type_map << "Inpatient,9201,Inpatient Visit\n";
        visit_type_map << "Emergency,9203,Emergency Room Visit\n";
        visit_type_map.close();
    }

    void create_mapping_configurations() {
        // Create transformation mapping configuration files
        nlohmann::json person_mapping = {
            {"source_table", "patients"},
            {"target_table", "person"},
            {"mappings", {
                {{"source_field", "patient_id"}, {"target_field", "person_id"}, {"transformation", "direct"}},
                {{"source_field", "gender"}, {"target_field", "gender_concept_id"}, {"transformation", "vocabulary_lookup"}, {"vocabulary_file", "gender_mapping.csv"}},
                {{"source_field", "birth_date"}, {"target_field", "year_of_birth"}, {"transformation", "extract_year"}},
                {{"source_field", "birth_date"}, {"target_field", "month_of_birth"}, {"transformation", "extract_month"}},
                {{"source_field", "birth_date"}, {"target_field", "day_of_birth"}, {"transformation", "extract_day"}},
                {{"source_field", "race"}, {"target_field", "race_concept_id"}, {"transformation", "vocabulary_lookup"}, {"vocabulary_file", "race_mapping.csv"}},
                {{"source_field", "ethnicity"}, {"target_field", "ethnicity_concept_id"}, {"transformation", "ethnicity_mapping"}}
            }}
        };

        std::ofstream person_config(test_data_dir_ / "person_mapping.json");
        person_config << person_mapping.dump(2);
        person_config.close();

        nlohmann::json visit_mapping = {
            {"source_table", "visits"},
            {"target_table", "visit_occurrence"},
            {"mappings", {
                {{"source_field", "visit_id"}, {"target_field", "visit_occurrence_id"}, {"transformation", "direct"}},
                {{"source_field", "patient_id"}, {"target_field", "person_id"}, {"transformation", "direct"}},
                {{"source_field", "visit_start_date"}, {"target_field", "visit_start_date"}, {"transformation", "date_format"}},
                {{"source_field", "visit_end_date"}, {"target_field", "visit_end_date"}, {"transformation", "date_format"}},
                {{"source_field", "visit_type"}, {"target_field", "visit_concept_id"}, {"transformation", "vocabulary_lookup"}, {"vocabulary_file", "visit_type_mapping.csv"}}
            }}
        };

        std::ofstream visit_config(test_data_dir_ / "visit_mapping.json");
        visit_config << visit_mapping.dump(2);
        visit_config.close();
    }

    void setup_omop_cdm_tables() {
        // Create essential OMOP CDM tables for testing
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS person (
                person_id BIGINT PRIMARY KEY,
                gender_concept_id INTEGER NOT NULL,
                year_of_birth INTEGER NOT NULL,
                month_of_birth INTEGER,
                day_of_birth INTEGER,
                birth_datetime TIMESTAMP,
                death_datetime TIMESTAMP,
                race_concept_id INTEGER NOT NULL,
                ethnicity_concept_id INTEGER NOT NULL,
                location_id INTEGER,
                provider_id INTEGER,
                care_site_id INTEGER,
                person_source_value VARCHAR(50),
                gender_source_value VARCHAR(50),
                race_source_value VARCHAR(50),
                ethnicity_source_value VARCHAR(50)
            );

            CREATE TABLE IF NOT EXISTS visit_occurrence (
                visit_occurrence_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                visit_concept_id INTEGER NOT NULL,
                visit_start_date DATE NOT NULL,
                visit_start_datetime TIMESTAMP,
                visit_end_date DATE NOT NULL,
                visit_end_datetime TIMESTAMP,
                visit_type_concept_id INTEGER NOT NULL,
                provider_id INTEGER,
                care_site_id INTEGER,
                visit_source_value VARCHAR(50),
                visit_source_concept_id INTEGER,
                admitted_from_concept_id INTEGER,
                admitted_from_source_value VARCHAR(50),
                discharged_to_concept_id INTEGER,
                discharged_to_source_value VARCHAR(50),
                preceding_visit_occurrence_id BIGINT
            );

            CREATE TABLE IF NOT EXISTS condition_occurrence (
                condition_occurrence_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                condition_concept_id INTEGER NOT NULL,
                condition_start_date DATE NOT NULL,
                condition_start_datetime TIMESTAMP,
                condition_end_date DATE,
                condition_end_datetime TIMESTAMP,
                condition_type_concept_id INTEGER NOT NULL,
                condition_status_concept_id INTEGER,
                stop_reason VARCHAR(20),
                provider_id INTEGER,
                visit_occurrence_id BIGINT,
                visit_detail_id BIGINT,
                condition_source_value VARCHAR(50),
                condition_source_concept_id INTEGER,
                condition_status_source_value VARCHAR(50)
            );

            CREATE TABLE IF NOT EXISTS drug_exposure (
                drug_exposure_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                drug_concept_id INTEGER NOT NULL,
                drug_exposure_start_date DATE NOT NULL,
                drug_exposure_start_datetime TIMESTAMP,
                drug_exposure_end_date DATE NOT NULL,
                drug_exposure_end_datetime TIMESTAMP,
                verbatim_end_date DATE,
                drug_type_concept_id INTEGER NOT NULL,
                stop_reason VARCHAR(20),
                refills INTEGER,
                quantity NUMERIC,
                days_supply INTEGER,
                sig TEXT,
                route_concept_id INTEGER,
                lot_number VARCHAR(50),
                provider_id INTEGER,
                visit_occurrence_id BIGINT,
                visit_detail_id BIGINT,
                drug_source_value VARCHAR(50),
                drug_source_concept_id INTEGER,
                route_source_value VARCHAR(50),
                dose_unit_source_value VARCHAR(50)
            );

            CREATE TABLE IF NOT EXISTS procedure_occurrence (
                procedure_occurrence_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                procedure_concept_id INTEGER NOT NULL,
                procedure_date DATE NOT NULL,
                procedure_datetime TIMESTAMP,
                procedure_end_date DATE,
                procedure_end_datetime TIMESTAMP,
                procedure_type_concept_id INTEGER NOT NULL,
                modifier_concept_id INTEGER,
                quantity INTEGER,
                provider_id INTEGER,
                visit_occurrence_id BIGINT,
                visit_detail_id BIGINT,
                procedure_source_value VARCHAR(50),
                procedure_source_concept_id INTEGER,
                modifier_source_value VARCHAR(50)
            );

            CREATE TABLE IF NOT EXISTS measurement (
                measurement_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                measurement_concept_id INTEGER NOT NULL,
                measurement_date DATE NOT NULL,
                measurement_datetime TIMESTAMP,
                measurement_time VARCHAR(10),
                measurement_type_concept_id INTEGER NOT NULL,
                operator_concept_id INTEGER,
                value_as_number NUMERIC,
                value_as_concept_id INTEGER,
                unit_concept_id INTEGER,
                range_low NUMERIC,
                range_high NUMERIC,
                provider_id INTEGER,
                visit_occurrence_id BIGINT,
                visit_detail_id BIGINT,
                measurement_source_value VARCHAR(50),
                measurement_source_concept_id INTEGER,
                unit_source_value VARCHAR(50),
                unit_source_concept_id INTEGER,
                value_source_value VARCHAR(50),
                measurement_event_id BIGINT,
                meas_event_field_concept_id INTEGER
            );
        )");
    }

    void cleanup_test_data() {
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
    std::shared_ptr<core::Pipeline> pipeline_;
    std::filesystem::path test_data_dir_;
};

// Test complete patient data ETL pipeline
TEST_F(E2EIntegrationTestUpdated, TestCompletePatientETL) {
    // Configure complete patient ETL job
    std::unordered_map<std::string, std::any> job_config = {
        {"job_name", std::string("Complete Patient ETL")},
        {"description", std::string("End-to-end patient data transformation")},
        {"source_config", std::unordered_map<std::string, std::any>{
            {"type", std::string("csv")},
            {"file_path", (test_data_dir_ / "patients.csv").string()},
            {"has_header", true}
        }},
        {"mapping_config", std::unordered_map<std::string, std::any>{
            {"config_file", (test_data_dir_ / "person_mapping.json").string()}
        }},
        {"target_config", std::unordered_map<std::string, std::any>{
            {"table", std::string("person")},
            {"validate_schema", true},
            {"batch_size", 10}
        }}
    };

    // Execute ETL job
    auto job_id = etl_service_->submit_job(job_config);
    EXPECT_FALSE(job_id.empty());

    // Wait for completion
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && (*status == "completed" || *status == "failed");
    }, 60000);

    EXPECT_TRUE(completed);

    // Verify successful completion
    auto final_status = etl_service_->get_job_status(job_id);
    EXPECT_EQ(*final_status, "completed");

    // Verify data was loaded correctly
    auto person_count = count_rows("person");
    EXPECT_EQ(person_count, 5); // 5 patients in test data

    // Verify data integrity
    auto result = execute_query("SELECT person_id, gender_concept_id, year_of_birth FROM person ORDER BY person_id");
    int record_count = 0;
    while (result->next()) {
        record_count++;
        auto person_id = std::any_cast<int64_t>(result->get_value("person_id"));
        auto gender_concept_id = std::any_cast<int>(result->get_value("gender_concept_id"));
        auto year_of_birth = std::any_cast<int>(result->get_value("year_of_birth"));
        
        EXPECT_GT(person_id, 0);
        EXPECT_TRUE(gender_concept_id == 8507 || gender_concept_id == 8532); // Male or Female
        EXPECT_GT(year_of_birth, 1900);
        EXPECT_LT(year_of_birth, 2025);
    }
    EXPECT_EQ(record_count, 5);
}

// Test multi-table ETL with dependencies
TEST_F(E2EIntegrationTestUpdated, TestMultiTableETLWithDependencies) {
    std::vector<std::string> job_ids;

    // Job 1: Load patients first
    {
        std::unordered_map<std::string, std::any> patient_job = {
            {"job_name", std::string("Load Patients")},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "patients.csv").string()},
            {"target_table", std::string("person")},
            {"mapping_config", (test_data_dir_ / "person_mapping.json").string()},
            {"priority", 1}
        };
        job_ids.push_back(etl_service_->submit_job(patient_job));
    }

    // Job 2: Load visits (depends on patients)
    {
        std::unordered_map<std::string, std::any> visit_job = {
            {"job_name", std::string("Load Visits")},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "visits.csv").string()},
            {"target_table", std::string("visit_occurrence")},
            {"mapping_config", (test_data_dir_ / "visit_mapping.json").string()},
            {"dependencies", std::vector<std::string>{job_ids[0]}},
            {"priority", 2}
        };
        job_ids.push_back(etl_service_->submit_job(visit_job));
    }

    // Job 3: Load conditions (depends on visits)
    {
        std::unordered_map<std::string, std::any> condition_job = {
            {"job_name", std::string("Load Conditions")},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "conditions.csv").string()},
            {"target_table", std::string("condition_occurrence")},
            {"dependencies", std::vector<std::string>{job_ids[1]}},
            {"priority", 3}
        };
        job_ids.push_back(etl_service_->submit_job(condition_job));
    }

    // Wait for all jobs to complete in order
    for (size_t i = 0; i < job_ids.size(); ++i) {
        bool completed = wait_for_condition([this, &job_ids, i]() {
            auto status = etl_service_->get_job_status(job_ids[i]);
            return status && (*status == "completed" || *status == "failed");
        }, 90000); // Longer timeout for dependent jobs

        EXPECT_TRUE(completed) << "Job " << i << " did not complete in time";
        
        auto final_status = etl_service_->get_job_status(job_ids[i]);
        EXPECT_EQ(*final_status, "completed") << "Job " << i << " failed";
    }

    // Verify all data was loaded correctly
    EXPECT_EQ(count_rows("person"), 5);
    EXPECT_EQ(count_rows("visit_occurrence"), 5);
    EXPECT_EQ(count_rows("condition_occurrence"), 5);

    // Verify referential integrity
    auto integrity_result = execute_query(R"(
        SELECT COUNT(*) as invalid_refs 
        FROM visit_occurrence v 
        LEFT JOIN person p ON v.person_id = p.person_id 
        WHERE p.person_id IS NULL
    )");
    
    EXPECT_TRUE(integrity_result->next());
    EXPECT_EQ(std::any_cast<int64_t>(integrity_result->get_value("invalid_refs")), 0);
}

// Test complex transformation scenarios
TEST_F(E2EIntegrationTestUpdated, TestComplexTransformations) {
    // Configure job with complex transformations
    std::unordered_map<std::string, std::any> job_config = {
        {"job_name", std::string("Complex Transformations Test")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "measurements.csv").string()},
        {"target_table", std::string("measurement")},
        {"transformations", std::vector<std::unordered_map<std::string, std::any>>{
            // Direct mappings
            {{"source", std::string("measurement_id")}, {"target", std::string("measurement_id")}, {"type", std::string("direct")}},
            {{"source", std::string("patient_id")}, {"target", std::string("person_id")}, {"type", std::string("direct")}},
            
            // Date transformations
            {{"source", std::string("measurement_date")}, {"target", std::string("measurement_date")}, {"type", std::string("date_parse")}, {"format", std::string("yyyy-MM-dd")}},
            
            // Vocabulary lookups
            {{"source", std::string("measurement_code")}, {"target", std::string("measurement_concept_id")}, {"type", std::string("vocabulary_lookup")}, {"vocabulary", std::string("LOINC")}},
            
            // Numeric transformations
            {{"source", std::string("value_numeric")}, {"target", std::string("value_as_number")}, {"type", std::string("numeric_conversion")}},
            
            // Conditional transformations
            {{"source", std::string("value_text")}, {"target", std::string("value_as_concept_id")}, {"type", std::string("conditional")}, {"condition", std::string("not_null")}},
            
            // Unit transformations
            {{"source", std::string("unit")}, {"target", std::string("unit_concept_id")}, {"type", std::string("unit_conversion")}}
        }},
        {"validation_rules", std::vector<std::string>{
            "required_fields:measurement_id,person_id,measurement_date",
            "numeric_range:value_as_number:0:10000",
            "date_validity:measurement_date"
        }}
    };

    auto job_id = etl_service_->submit_job(job_config);
    EXPECT_FALSE(job_id.empty());

    // Wait for completion
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && (*status == "completed" || *status == "failed");
    }, 60000);

    EXPECT_TRUE(completed);
    
    auto final_status = etl_service_->get_job_status(job_id);
    EXPECT_EQ(*final_status, "completed");

    // Verify transformations were applied correctly
    auto measurement_count = count_rows("measurement");
    EXPECT_GT(measurement_count, 0);

    // Verify specific transformation results
    auto result = execute_query("SELECT measurement_id, person_id, measurement_date, value_as_number FROM measurement WHERE measurement_id = 'L001'");
    EXPECT_TRUE(result->next());
    
    auto measurement_date = result->get_value("measurement_date");
    auto value_as_number = result->get_value("value_as_number");
    
    EXPECT_TRUE(measurement_date.has_value());
    EXPECT_TRUE(value_as_number.has_value());
}

// Test error handling and data quality validation
TEST_F(E2EIntegrationTestUpdated, TestDataQualityAndErrorHandling) {
    // Create problematic test data
    std::ofstream bad_data_file(test_data_dir_ / "bad_patients.csv");
    bad_data_file << "patient_id,first_name,last_name,birth_date,gender\n";
    bad_data_file << ",John,Doe,1980-01-15,M\n";  // Missing patient_id
    bad_data_file << "P002,Jane,Smith,invalid-date,F\n";  // Invalid date
    bad_data_file << "P003,Maria,Garcia,1990-03-10,X\n";  // Invalid gender
    bad_data_file << "P004,David,Chen,1985-07-25,M\n";   // Valid record
    bad_data_file.close();

    // Configure job with strict validation
    std::unordered_map<std::string, std::any> job_config = {
        {"job_name", std::string("Data Quality Test")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "bad_patients.csv").string()},
        {"target_table", std::string("person")},
        {"error_handling", std::unordered_map<std::string, std::any>{
            {"mode", std::string("continue_on_error")},
            {"max_error_percentage", 75.0},
            {"log_errors", true}
        }},
        {"validation", std::unordered_map<std::string, std::any>{
            {"strict_mode", true},
            {"required_fields", std::vector<std::string>{"patient_id", "gender", "birth_date"}},
            {"validate_dates", true},
            {"validate_vocabularies", true}
        }}
    };

    auto job_id = etl_service_->submit_job(job_config);
    EXPECT_FALSE(job_id.empty());

    // Wait for completion
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && (*status == "completed" || *status == "failed");
    }, 60000);

    EXPECT_TRUE(completed);

    // Should complete with warnings (not fail completely)
    auto final_status = etl_service_->get_job_status(job_id);
    EXPECT_TRUE(*final_status == "completed" || *final_status == "completed_with_warnings");

    // Verify only valid records were loaded
    auto person_count = count_rows("person");
    EXPECT_EQ(person_count, 1); // Only P004 should be valid

    // Verify error reporting
    auto job_metrics = etl_service_->get_job_metrics(job_id);
    EXPECT_TRUE(job_metrics.has_value());
    EXPECT_TRUE(job_metrics->contains("validation_errors"));
    EXPECT_TRUE(job_metrics->contains("records_rejected"));
    
    auto records_rejected = std::any_cast<int>(job_metrics->at("records_rejected"));
    EXPECT_EQ(records_rejected, 3); // 3 invalid records
}

// Test performance with larger datasets
TEST_F(E2EIntegrationTestUpdated, TestPerformanceWithLargeDataset) {
    // Generate larger test dataset
    const int num_records = 1000;
    std::ofstream large_file(test_data_dir_ / "large_patients.csv");
    large_file << "patient_id,first_name,last_name,birth_date,gender,race,ethnicity\n";
    
    for (int i = 1; i <= num_records; ++i) {
        large_file << "P" << std::setfill('0') << std::setw(6) << i << ","
                  << "First" << i << ","
                  << "Last" << i << ","
                  << "198" << (i % 10) << "-" << std::setfill('0') << std::setw(2) << ((i % 12) + 1) << "-"
                  << std::setfill('0') << std::setw(2) << ((i % 28) + 1) << ","
                  << (i % 2 == 0 ? "M" : "F") << ","
                  << "White,Not Hispanic\n";
    }
    large_file.close();

    // Configure performance-optimized job
    std::unordered_map<std::string, std::any> job_config = {
        {"job_name", std::string("Performance Test")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "large_patients.csv").string()},
        {"target_table", std::string("person")},
        {"performance", std::unordered_map<std::string, std::any>{
            {"batch_size", 100},
            {"parallel_workers", 3},
            {"enable_bulk_insert", true},
            {"commit_frequency", 500}
        }},
        {"monitoring", std::unordered_map<std::string, std::any>{
            {"enable_progress_tracking", true},
            {"log_performance_metrics", true}
        }}
    };

    auto start_time = std::chrono::high_resolution_clock::now();
    auto job_id = etl_service_->submit_job(job_config);
    
    // Wait for completion
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && (*status == "completed" || *status == "failed");
    }, 120000); // 2 minute timeout for large dataset

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    EXPECT_TRUE(completed);
    
    auto final_status = etl_service_->get_job_status(job_id);
    EXPECT_EQ(*final_status, "completed");

    // Verify all records were loaded
    auto person_count = count_rows("person");
    EXPECT_EQ(person_count, num_records);

    // Verify performance metrics
    auto job_metrics = etl_service_->get_job_metrics(job_id);
    EXPECT_TRUE(job_metrics.has_value());
    EXPECT_TRUE(job_metrics->contains("records_per_second"));
    
    auto records_per_second = std::any_cast<double>(job_metrics->at("records_per_second"));
    EXPECT_GT(records_per_second, 10.0); // Should process at least 10 records per second

    logger_->info("Performance test completed in {} ms, {} records/sec", 
                 duration.count(), records_per_second);
}

// Test rollback and recovery scenarios
TEST_F(E2EIntegrationTestUpdated, TestRollbackAndRecovery) {
    // First, load some initial data
    std::unordered_map<std::string, std::any> initial_job = {
        {"job_name", std::string("Initial Load")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "patients.csv").string()},
        {"target_table", std::string("person")},
        {"enable_rollback", true}
    };

    auto initial_job_id = etl_service_->submit_job(initial_job);
    wait_for_condition([this, &initial_job_id]() {
        auto status = etl_service_->get_job_status(initial_job_id);
        return status && *status == "completed";
    }, 60000);

    auto initial_count = count_rows("person");
    EXPECT_EQ(initial_count, 5);

    // Now try a job that will fail partway through
    std::ofstream partial_fail_file(test_data_dir_ / "partial_fail_patients.csv");
    partial_fail_file << "patient_id,first_name,last_name,birth_date,gender\n";
    partial_fail_file << "P006,Alice,Brown,1985-07-20,F\n";  // Valid
    partial_fail_file << "P007,Bob,Wilson,1990-11-15,M\n";   // Valid
    partial_fail_file << "INVALID_ID_THAT_WILL_CAUSE_CONSTRAINT_VIOLATION,Charlie,Davis,1988-03-05,M\n";  // Will fail
    partial_fail_file.close();

    std::unordered_map<std::string, std::any> failing_job = {
        {"job_name", std::string("Failing Job with Rollback")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "partial_fail_patients.csv").string()},
        {"target_table", std::string("person")},
        {"enable_rollback", true},
        {"transaction_mode", std::string("all_or_nothing")}
    };

    auto failing_job_id = etl_service_->submit_job(failing_job);
    
    // Wait for it to fail
    bool completed = wait_for_condition([this, &failing_job_id]() {
        auto status = etl_service_->get_job_status(failing_job_id);
        return status && *status == "failed";
    }, 60000);

    EXPECT_TRUE(completed);

    // Verify rollback occurred - count should be same as before
    auto final_count = count_rows("person");
    EXPECT_EQ(final_count, initial_count); // No new records should be added due to rollback

    // Verify rollback information is available
    auto rollback_info = etl_service_->get_rollback_info(failing_job_id);
    EXPECT_TRUE(rollback_info.has_value());
    EXPECT_TRUE(rollback_info->contains("rollback_performed"));
    EXPECT_TRUE(std::any_cast<bool>(rollback_info->at("rollback_performed")));
}

} // namespace omop::test::integration