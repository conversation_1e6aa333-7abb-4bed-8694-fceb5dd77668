// tests/integration/transform/test_advanced_transformation_integration.cpp
// Tests complex transformation scenarios including chains, custom transformations, and edge cases

#include <gtest/gtest.h>
#include <memory>
#include <random>
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "transform/field_transformations.h"
#include "transform/custom_transformations.h"
#include "transform/string_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/date_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "core/pipeline.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"

namespace omop::test {

class AdvancedTransformationTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize logging
        common::LoggingConfig::initialize_default();
        logger_ = common::Logger::get("advanced-transform-test");

        // Initialize vocabulary service
        auto vocab_connection = createTestConnection();
        transform::VocabularyServiceManager::initialize(
            std::move(vocab_connection), 10000);

        // Register all transformation types
        registerTransformations();

        // Load test vocabularies
        loadTestVocabularies();
    }

    void TearDown() override {
        transform::VocabularyServiceManager::reset();
        DatabaseFixture::TearDown();
    }

    void registerTransformations() {
        auto& registry = transform::TransformationRegistry::instance();

        // Register standard transformations
        registry.register_transformation("direct",
            []() { return std::make_unique<transform::DirectTransformation>(); });

        registry.register_transformation("date_transform",
            []() { return std::make_unique<transform::DateTransformation>(); });

        registry.register_transformation("numeric_transform",
            []() { return std::make_unique<transform::NumericTransformation>(); });

        registry.register_transformation("string_concatenation",
            []() { return std::make_unique<transform::StringConcatenationTransformation>(); });

        registry.register_transformation("conditional",
            []() { return std::make_unique<transform::ConditionalTransformation>(); });

        registry.register_transformation("vocabulary_mapping",
            []() { return std::make_unique<transform::VocabularyTransformation>(
                transform::VocabularyServiceManager::instance()); });

        // Register advanced transformations
        registry.register_transformation("advanced_numeric",
            []() { return std::make_unique<transform::AdvancedNumericTransformation>(); });

        registry.register_transformation("string_manipulation",
            []() { return std::make_unique<transform::StringManipulationTransformation>(); });

        registry.register_transformation("string_pattern_extraction",
            []() { return std::make_unique<transform::StringPatternExtractionTransformation>(); });

        registry.register_transformation("date_calculation",
            []() { return std::make_unique<transform::DateCalculationTransformation>(); });

        registry.register_transformation("date_range_validation",
            []() { return std::make_unique<transform::DateRangeValidationTransformation>(); });

        registry.register_transformation("advanced_conditional",
            []() { return std::make_unique<transform::AdvancedConditionalTransformation>(); });

        registry.register_transformation("lookup_table",
            []() { return std::make_unique<transform::LookupTableTransformation>(); });

        registry.register_transformation("concept_hierarchy",
            []() { return std::make_unique<transform::ConceptHierarchyTransformation>(); });

        registry.register_transformation("domain_mapping",
            []() { return std::make_unique<transform::DomainMappingTransformation>(); });

        // Register custom transformations
        transform::CustomTransformationFactory::register_custom_transformations();
    }

    void loadTestVocabularies() {
        auto& vocab_service = transform::VocabularyServiceManager::instance();

        // Add test concepts
        std::vector<transform::Concept> concepts = {
            {8507, "MALE", "Gender", "Gender", "Gender", "M"},
            {8532, "FEMALE", "Gender", "Gender", "Gender", "F"},
            {320128, "Essential hypertension", "Condition", "SNOMED", "Clinical Finding", "59621000"},
            {4329847, "Myocardial infarction", "Condition", "SNOMED", "Clinical Finding", "22298006"},
            {1112807, "Aspirin 81 MG", "Drug", "RxNorm", "Clinical Drug", "243670"},
            {40213227, "Lisinopril 10 MG", "Drug", "RxNorm", "Clinical Drug", "29046"}
        };

        // Add vocabulary mappings
        std::vector<transform::VocabularyMapping> mappings = {
            {"M", "Gender", 8507, "OMOP", 1.0f, "exact"},
            {"Male", "Gender", 8507, "OMOP", 1.0f, "exact"},
            {"F", "Gender", 8532, "OMOP", 1.0f, "exact"},
            {"Female", "Gender", 8532, "OMOP", 1.0f, "exact"},
            {"I10", "ICD10CM", 320128, "OMOP", 0.95f, "mapped"},
            {"410.9", "ICD9CM", 4329847, "OMOP", 0.9f, "mapped"},
            {"ASPIRIN", "DrugName", 1112807, "OMOP", 0.8f, "fuzzy"},
            {"LISINOPRIL", "DrugName", 40213227, "OMOP", 0.85f, "fuzzy"}
        };

        for (const auto& mapping : mappings) {
            vocab_service.add_mapping(mapping);
        }
    }

protected:
    std::shared_ptr<common::Logger> logger_;
};

// Test complex transformation chains
TEST_F(AdvancedTransformationTest, ComplexTransformationChains) {
    // Create test data with complex scenarios
    core::RecordBatch input_batch;

    // Record 1: Full name to be split and formatted
    core::Record record1;
    record1.setField("patient_name", std::string("john SMITH-jones"));
    record1.setField("birth_date", std::string("1980-05-15"));
    record1.setField("gender_code", std::string("m"));
    record1.setField("weight_lbs", 180.5);
    record1.setField("height_inches", 72.0);
    input_batch.addRecord(record1);

    // Create transformation chain
    transform::TransformationChain chain;

    // Step 1: Extract and format name components
    auto name_transform = std::make_unique<transform::StringManipulationTransformation>();
    name_transform->configure(YAML::Load(R"(
operation: TitleCase
)"));

    // Step 2: Convert weight to kg
    auto weight_transform = std::make_unique<transform::AdvancedNumericTransformation>();
    weight_transform->configure(YAML::Load(R"(
operation: UnitConversion
from_unit: lbs
to_unit: kg
round_to_decimal: 2
)"));

    // Step 3: Calculate BMI
    class BMICalculationTransformation : public transform::ComplexTransformation {
    public:
        transform::TransformationResult transform_detailed(
            const std::any& input,
            core::ProcessingContext& context) override {

            transform::TransformationResult result;

            try {
                // Extract weight and height from context
                auto weight_kg = context.get_data("weight_kg");
                auto height_inches = context.get_data("height_inches");

                if (!weight_kg || !height_inches) {
                    result.set_error("Missing weight or height data");
                    return result;
                }

                double weight = std::any_cast<double>(*weight_kg);
                double height = std::any_cast<double>(*height_inches);

                // Convert height to meters
                double height_m = height * 0.0254;

                // Calculate BMI
                double bmi = weight / (height_m * height_m);

                result.value = std::round(bmi * 10) / 10.0;
                result.metadata["formula"] = "weight(kg) / height(m)^2";

            } catch (const std::exception& e) {
                result.set_error(fmt::format("BMI calculation failed: {}", e.what()));
            }

            return result;
        }

        bool validate_input(const std::any& input) const override {
            return true; // Uses context data
        }

        std::string get_type() const override { return "bmi_calculation"; }

        void configure(const YAML::Node& params) override {}
    };

    // Apply transformation chain
    core::ProcessingContext context;

    for (const auto& record : input_batch) {
        // Transform name
        auto name_result = name_transform->transform(
            record.getField("patient_name"), context);

        EXPECT_EQ("John Smith-Jones", std::any_cast<std::string>(name_result));

        // Transform weight
        auto weight_result = std::any_cast<transform::TransformationResult>(
            std::any_cast<transform::AdvancedNumericTransformation*>(
                weight_transform.get())->transform_detailed(
                    record.getField("weight_lbs"), context));

        EXPECT_TRUE(weight_result.is_success());
        double weight_kg = std::any_cast<double>(weight_result.value);
        EXPECT_NEAR(81.65, weight_kg, 0.01);

        // Store for BMI calculation
        context.set_data("weight_kg", weight_kg);
        context.set_data("height_inches", record.getField("height_inches"));

        // Calculate BMI
        auto bmi_transform = std::make_unique<BMICalculationTransformation>();
        auto bmi_result = bmi_transform->transform_detailed(
            std::any{}, context);

        EXPECT_TRUE(bmi_result.is_success());
        double bmi = std::any_cast<double>(bmi_result.value);
        EXPECT_NEAR(25.0, bmi, 0.1);
    }
}

// Test conditional transformations with complex rules
TEST_F(AdvancedTransformationTest, AdvancedConditionalTransformations) {
    // Create test data
    std::vector<core::Record> test_records;

    // Different patient scenarios
    std::vector<std::tuple<int, std::string, int, std::string>> patients = {
        {25, "M", 0, "HEALTHY"},      // Young male, healthy
        {70, "F", 3, "HIGH_RISK"},    // Elderly female, high risk
        {45, "M", 1, "MODERATE"},     // Middle-aged male, moderate
        {10, "F", 0, "PEDIATRIC"},    // Child
        {90, "M", 5, "CRITICAL"}      // Very elderly, critical
    };

    for (const auto& [age, gender, conditions, expected] : patients) {
        core::Record record;
        record.setField("age", age);
        record.setField("gender", std::string(gender));
        record.setField("condition_count", conditions);
        test_records.push_back(record);
    }

    // Create advanced conditional transformation
    auto conditional = std::make_unique<transform::AdvancedConditionalTransformation>();
    conditional->configure(YAML::Load(R"(
rules:
  - description: "Pediatric patients"
    conditions:
      - field: age
        operator: less_than
        value: 18
    then_action:
      type: SetValue
      value: "PEDIATRIC"

  - description: "High risk elderly"
    conditions:
      - field: age
        operator: greater_than_or_equal
        value: 65
      - field: condition_count
        operator: greater_than
        value: 2
    then_action:
      type: SetValue
      value: "HIGH_RISK"

  - description: "Healthy adults"
    conditions:
      - field: age
        operator: between
        value: [18, 64]
      - field: condition_count
        operator: equals
        value: 0
    then_action:
      type: SetValue
      value: "HEALTHY"

  - description: "Critical patients"
    conditions:
      - field: condition_count
        operator: greater_than_or_equal
        value: 5
    then_action:
      type: SetValue
      value: "CRITICAL"

default_action:
  type: SetValue
  value: "MODERATE"
)"));

    // Test transformations
    core::ProcessingContext context;

    for (size_t i = 0; i < test_records.size(); ++i) {
        auto result = conditional->transform_detailed(
            test_records[i], context);

        EXPECT_TRUE(result.is_success());

        std::string risk_category = std::any_cast<std::string>(result.value);
        std::string expected = std::get<3>(patients[i]);

        EXPECT_EQ(expected, risk_category)
            << "Patient " << i << " miscategorized";
    }
}

// Test string pattern extraction and manipulation
TEST_F(AdvancedTransformationTest, StringPatternExtractionManipulation) {
    // Test data with various string patterns
    std::vector<std::pair<std::string, std::string>> test_cases = {
        {"<EMAIL>", "example.com"},
        {"(555) 123-4567", "**********"},
        {"Patient ID: PAT-2023-001", "PAT-2023-001"},
        {"DOB: 05/15/1980 (Age: 43)", "05/15/1980"},
        {"MRN#123456", "123456"},
        {"  SMITH, JOHN   MD  ", "SMITH, JOHN"}
    };

    // Create pattern extractors for different scenarios
    std::vector<std::unique_ptr<transform::StringPatternExtractionTransformation>> extractors;

    // Email domain extractor
    auto email_extractor = std::make_unique<transform::StringPatternExtractionTransformation>();
    email_extractor->configure(YAML::Load(R"(
pattern_type: Custom
custom_pattern: "@([\\w.-]+)"
capture_group: 1
)"));
    extractors.push_back(std::move(email_extractor));

    // Phone number extractor
    auto phone_extractor = std::make_unique<transform::StringPatternExtractionTransformation>();
    phone_extractor->configure(YAML::Load(R"(
pattern_type: Phone
)"));
    extractors.push_back(std::move(phone_extractor));

    // Patient ID extractor
    auto id_extractor = std::make_unique<transform::StringPatternExtractionTransformation>();
    id_extractor->configure(YAML::Load(R"(
pattern_type: Custom
custom_pattern: "PAT-\\d{4}-\\d{3}"
)"));
    extractors.push_back(std::move(id_extractor));

    // Date extractor
    auto date_extractor = std::make_unique<transform::StringPatternExtractionTransformation>();
    date_extractor->configure(YAML::Load(R"(
pattern_type: Date
)"));
    extractors.push_back(std::move(date_extractor));

    // MRN extractor
    auto mrn_extractor = std::make_unique<transform::StringPatternExtractionTransformation>();
    mrn_extractor->configure(YAML::Load(R"(
pattern_type: Custom
custom_pattern: "MRN#(\\d+)"
capture_group: 1
)"));
    extractors.push_back(std::move(mrn_extractor));

    // Name cleaner
    auto name_cleaner = std::make_unique<transform::StringManipulationTransformation>();
    name_cleaner->configure(YAML::Load(R"(
operation: Trim
)"));

    // Test extractions
    core::ProcessingContext context;

    for (size_t i = 0; i < test_cases.size(); ++i) {
        const auto& [input, expected] = test_cases[i];

        transform::TransformationResult result;

        if (i == 5) {
            // Special case for name cleaning
            result = std::any_cast<transform::TransformationResult>(
                std::any_cast<transform::StringManipulationTransformation*>(
                    name_cleaner.get())->transform_detailed(input, context));

            // Additional processing to remove suffix
            std::string cleaned = std::any_cast<std::string>(result.value);
            size_t md_pos = cleaned.find(" MD");
            if (md_pos != std::string::npos) {
                cleaned = cleaned.substr(0, md_pos);
            }
            result.value = cleaned;
        } else if (i < extractors.size()) {
            result = extractors[i]->transform_detailed(input, context);
        }

        EXPECT_TRUE(result.is_success())
            << "Failed to extract from: " << input;

        if (result.is_success() && i < test_cases.size()) {
            std::string extracted = std::any_cast<std::string>(result.value);
            EXPECT_EQ(expected, extracted)
                << "Extraction mismatch for: " << input;
        }
    }
}

// Test date calculations and validations
TEST_F(AdvancedTransformationTest, DateCalculationValidation) {
    // Create date calculation transformer
    auto age_calc = std::make_unique<transform::DateCalculationTransformation>();
    age_calc->configure(YAML::Load(R"(
operation: Age
reference_date: "2023-12-31"
)"));

    auto date_diff = std::make_unique<transform::DateCalculationTransformation>();
    date_diff->configure(YAML::Load(R"(
operation: DateDiff
diff_unit: days
)"));

    auto date_validator = std::make_unique<transform::DateRangeValidationTransformation>();
    date_validator->configure(YAML::Load(R"(
min_date: "1900-01-01"
max_date: "2023-12-31"
reject_future_dates: true
set_future_to_today: true
)"));

    // Test age calculation
    core::ProcessingContext context;

    std::vector<std::pair<std::string, int>> age_tests = {
        {"1980-05-15", 43},
        {"2000-01-01", 23},
        {"2010-12-31", 13},
        {"1950-06-30", 73}
    };

    for (const auto& [birth_date, expected_age] : age_tests) {
        auto result = age_calc->transform_detailed(birth_date, context);
        EXPECT_TRUE(result.is_success());

        int calculated_age = std::any_cast<int>(result.value);
        EXPECT_EQ(expected_age, calculated_age)
            << "Age calculation failed for: " << birth_date;
    }

    // Test date difference calculation
    context.set_data("start_date", std::string("2023-01-15"));
    context.set_data("end_date", std::string("2023-03-20"));

    auto diff_result = date_diff->transform_detailed(
        std::string("2023-01-15"), context);

    // Note: This would need the transformer to access both dates from context
    // For now, we'll test date validation

    // Test date validation
    std::vector<std::pair<std::string, bool>> validation_tests = {
        {"2023-06-15", true},   // Valid date
        {"2025-01-01", false},  // Future date
        {"1899-12-31", false},  // Before min date
        {"invalid", false}      // Invalid format
    };

    for (const auto& [date_str, should_pass] : validation_tests) {
        auto val_result = date_validator->transform_detailed(date_str, context);

        if (should_pass) {
            EXPECT_TRUE(val_result.is_success())
                << "Valid date rejected: " << date_str;
        } else {
            EXPECT_TRUE(!val_result.is_success() || !val_result.warnings.empty())
                << "Invalid date accepted: " << date_str;
        }
    }
}

// Test vocabulary transformations with concept hierarchies
TEST_F(AdvancedTransformationTest, VocabularyConceptHierarchyTransformations) {
    auto& vocab_service = transform::VocabularyServiceManager::instance();

    // Add concept hierarchy for testing
    // Cardiovascular disease hierarchy
    std::vector<std::pair<int, int>> concept_hierarchy = {
        {4329847, 316866},  // MI -> Ischemic heart disease
        {316866, 134057},   // Ischemic heart disease -> Heart disease
        {134057, 4008678}   // Heart disease -> Cardiovascular disease
    };

    // Simulate concept ancestor relationships
    class MockVocabularyService : public transform::VocabularyService {
    public:
        using VocabularyService::VocabularyService;

        std::vector<int> get_ancestors(int concept_id, int max_levels) override {
            std::vector<int> ancestors;

            if (concept_id == 4329847) {  // MI
                ancestors = {316866, 134057, 4008678};
            } else if (concept_id == 316866) {  // Ischemic heart disease
                ancestors = {134057, 4008678};
            } else if (concept_id == 134057) {  // Heart disease
                ancestors = {4008678};
            }

            if (max_levels > 0 && ancestors.size() > max_levels) {
                ancestors.resize(max_levels);
            }

            return ancestors;
        }
    };

    // Create concept hierarchy transformer
    auto hierarchy_transform = std::make_unique<transform::ConceptHierarchyTransformation>();
    hierarchy_transform->configure(YAML::Load(R"(
direction: ToAncestor
ancestor_level: 2
select_strategy: first
)"));

    // Create domain mapping transformer
    auto domain_transform = std::make_unique<transform::DomainMappingTransformation>();
    domain_transform->configure(YAML::Load(R"(
source_vocabulary: ICD10CM
target_domain: Condition
default_concept_id: 0
)"));

    // Test transformations
    core::ProcessingContext context;

    // Test concept hierarchy navigation
    auto hierarchy_result = hierarchy_transform->transform_detailed(
        4329847, context);  // MI concept

    EXPECT_TRUE(hierarchy_result.is_success());
    int ancestor_concept = std::any_cast<int>(hierarchy_result.value);
    EXPECT_EQ(134057, ancestor_concept);  // Should get heart disease (level 2)

    // Test domain mapping
    auto domain_result = domain_transform->transform_detailed(
        std::string("I21.9"), context);  // ICD10 code for MI

    EXPECT_TRUE(domain_result.is_success());
    int mapped_concept = std::any_cast<int>(domain_result.value);
    // Would check against actual mapping
}

// Test custom transformation with complex logic
TEST_F(AdvancedTransformationTest, CustomTransformationLogic) {
    // Create custom transformation for lab value interpretation
    class LabValueInterpretationTransform : public transform::ComplexTransformation {
    public:
        transform::TransformationResult transform_detailed(
            const std::any& input,
            core::ProcessingContext& context) override {

            transform::TransformationResult result;

            try {
                // Get lab test info from context
                auto test_name = context.get_data("test_name");
                auto test_unit = context.get_data("test_unit");

                if (!test_name) {
                    result.set_error("Missing test name");
                    return result;
                }

                double value = std::any_cast<double>(input);
                std::string test = std::any_cast<std::string>(*test_name);

                // Interpret based on test type
                std::string interpretation;

                if (test == "GLUCOSE") {
                    if (value < 70) {
                        interpretation = "LOW";
                        result.add_warning("Hypoglycemia detected");
                    } else if (value > 125) {
                        interpretation = "HIGH";
                        result.add_warning("Hyperglycemia detected");
                    } else {
                        interpretation = "NORMAL";
                    }
                } else if (test == "HEMOGLOBIN") {
                    // Gender-specific ranges
                    auto gender = context.get_data("gender");
                    std::string gender_str = gender ?
                        std::any_cast<std::string>(*gender) : "U";

                    if (gender_str == "M") {
                        if (value < 13.5) interpretation = "LOW";
                        else if (value > 17.5) interpretation = "HIGH";
                        else interpretation = "NORMAL";
                    } else if (gender_str == "F") {
                        if (value < 12.0) interpretation = "LOW";
                        else if (value > 15.5) interpretation = "HIGH";
                        else interpretation = "NORMAL";
                    } else {
                        // Use wider range for unknown gender
                        if (value < 12.0) interpretation = "LOW";
                        else if (value > 17.5) interpretation = "HIGH";
                        else interpretation = "NORMAL";
                    }
                }

                result.value = interpretation;
                result.metadata["original_value"] = value;
                result.metadata["test_name"] = test;

            } catch (const std::exception& e) {
                result.set_error(fmt::format("Lab interpretation failed: {}",
                                           e.what()));
            }

            return result;
        }

        bool validate_input(const std::any& input) const override {
            try {
                std::any_cast<double>(input);
                return true;
            } catch (...) {
                return false;
            }
        }

        std::string get_type() const override {
            return "lab_value_interpretation";
        }

        void configure(const YAML::Node& params) override {
            // Could configure reference ranges here
        }
    };

    // Test the custom transformation
    auto lab_transform = std::make_unique<LabValueInterpretationTransform>();
    core::ProcessingContext context;

    // Test glucose values
    std::vector<std::tuple<std::string, double, std::string, std::string>> test_cases = {
        {"GLUCOSE", 65.0, "M", "LOW"},
        {"GLUCOSE", 95.0, "M", "NORMAL"},
        {"GLUCOSE", 140.0, "M", "HIGH"},
        {"HEMOGLOBIN", 11.5, "F", "LOW"},
        {"HEMOGLOBIN", 13.5, "F", "NORMAL"},
        {"HEMOGLOBIN", 16.0, "M", "NORMAL"},
        {"HEMOGLOBIN", 18.0, "M", "HIGH"}
    };

    for (const auto& [test_name, value, gender, expected] : test_cases) {
        context.set_data("test_name", test_name);
        context.set_data("gender", gender);

        auto result = lab_transform->transform_detailed(value, context);

        EXPECT_TRUE(result.is_success())
            << "Failed for " << test_name << " = " << value;

        if (result.is_success()) {
            std::string interpretation = std::any_cast<std::string>(result.value);
            EXPECT_EQ(expected, interpretation)
                << "Wrong interpretation for " << test_name
                << " = " << value << " (gender: " << gender << ")";
        }
    }
}

// Test transformation performance and caching
TEST_F(AdvancedTransformationTest, TransformationPerformanceCaching) {
    // Create transformation cache
    transform::TransformationCache cache(1000);

    // Create expensive transformation
    class ExpensiveTransformation : public transform::ComplexTransformation {
    public:
        transform::TransformationResult transform_detailed(
            const std::any& input,
            core::ProcessingContext& context) override {

            transform::TransformationResult result;

            // Simulate expensive computation
            std::this_thread::sleep_for(std::chrono::milliseconds(10));

            transform_count_++;

            std::string input_str = std::any_cast<std::string>(input);
            result.value = fmt::format("TRANSFORMED_{}", input_str);

            return result;
        }

        bool validate_input(const std::any& input) const override {
            return input.type() == typeid(std::string);
        }

        std::string get_type() const override { return "expensive"; }
        void configure(const YAML::Node&) override {}

        std::atomic<int> transform_count_{0};
    };

    auto expensive_transform = std::make_unique<ExpensiveTransformation>();
    auto transform_ptr = expensive_transform.get();

    // Create cached wrapper
    class CachedTransformation : public transform::ComplexTransformation {
    public:
        CachedTransformation(std::unique_ptr<transform::ComplexTransformation> inner,
                           transform::TransformationCache& cache)
            : inner_(std::move(inner)), cache_(cache) {}

        transform::TransformationResult transform_detailed(
            const std::any& input,
            core::ProcessingContext& context) override {

            // Create cache key
            std::string key = fmt::format("{}:{}",
                inner_->get_type(),
                std::any_cast<std::string>(input));

            // Check cache
            auto cached = cache_.get(key);
            if (cached) {
                cache_hits_++;
                return std::any_cast<transform::TransformationResult>(*cached);
            }

            // Compute result
            auto result = inner_->transform_detailed(input, context);

            // Cache result
            cache_.put(key, result);
            cache_misses_++;

            return result;
        }

        bool validate_input(const std::any& input) const override {
            return inner_->validate_input(input);
        }

        std::string get_type() const override {
            return fmt::format("cached_{}", inner_->get_type());
        }

        void configure(const YAML::Node& params) override {
            inner_->configure(params);
        }

        std::atomic<int> cache_hits_{0};
        std::atomic<int> cache_misses_{0};

    private:
        std::unique_ptr<transform::ComplexTransformation> inner_;
        transform::TransformationCache& cache_;
    };

    auto cached_transform = std::make_unique<CachedTransformation>(
        std::move(expensive_transform), cache);

    // Test with repeated values
    core::ProcessingContext context;
    auto start = std::chrono::steady_clock::now();

    // Process many records with repeated values
    const int num_iterations = 1000;
    const int unique_values = 50;

    std::mt19937 rng(42);
    std::uniform_int_distribution<int> dist(0, unique_values - 1);

    for (int i = 0; i < num_iterations; ++i) {
        std::string input = fmt::format("VALUE_{}", dist(rng));
        auto result = cached_transform->transform_detailed(input, context);

        EXPECT_TRUE(result.is_success());
    }

    auto duration = std::chrono::steady_clock::now() - start;
    auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        duration).count();

    // Verify caching worked
    EXPECT_EQ(unique_values, transform_ptr->transform_count_.load());
    EXPECT_EQ(unique_values, cached_transform->cache_misses_.load());
    EXPECT_EQ(num_iterations - unique_values,
              cached_transform->cache_hits_.load());

    // Performance should be much better with caching
    logger_->info("Cached transformation performance: {} ms for {} iterations",
                 duration_ms, num_iterations);

    // Without caching, would take ~10ms * 1000 = 10 seconds
    // With caching, should take ~10ms * 50 = 500ms
    EXPECT_LT(duration_ms, 2000); // Allow some overhead

    // Check cache statistics
    auto cache_stats = cache.get_stats();
    EXPECT_EQ(unique_values, cache_stats.size);
    EXPECT_GT(cache_stats.hit_rate, 0.9); // Should have >90% hit rate
}

// Test error handling and recovery in transformations
TEST_F(AdvancedTransformationTest, TransformationErrorHandlingRecovery) {
    // Create transformation that can fail
    class UnreliableTransformation : public transform::ComplexTransformation {
    public:
        transform::TransformationResult transform_detailed(
            const std::any& input,
            core::ProcessingContext& context) override {

            transform::TransformationResult result;

            try {
                std::string input_str = std::any_cast<std::string>(input);

                // Fail for specific inputs
                if (input_str.find("ERROR") != std::string::npos) {
                    throw std::runtime_error("Simulated transformation error");
                }

                if (input_str.find("WARN") != std::string::npos) {
                    result.add_warning("Potential issue detected");
                }

                // Transform normally
                result.value = fmt::format("PROCESSED_{}", input_str);

            } catch (const std::bad_any_cast& e) {
                result.set_error("Invalid input type");
            } catch (const std::exception& e) {
                result.set_error(e.what());
            }

            return result;
        }

        bool validate_input(const std::any& input) const override {
            return input.type() == typeid(std::string);
        }

        std::string get_type() const override { return "unreliable"; }
        void configure(const YAML::Node&) override {}
    };

    // Create batch field transformer with error handling
    transform::BatchFieldTransformer batch_transformer;
    batch_transformer.set_copy_unmapped_fields(true);

    // Add field mappings
    batch_transformer.add_mapping({
        .source_field = "input_value",
        .target_field = "output_value",
        .transformation_type = "unreliable",
        .is_required = false
    });

    // Register transformation
    auto& registry = transform::TransformationRegistry::instance();
    registry.register_transformation("unreliable",
        []() { return std::make_unique<UnreliableTransformation>(); });

    // Test with various inputs
    std::vector<std::pair<std::string, bool>> test_inputs = {
        {"NORMAL_VALUE", true},
        {"ERROR_CASE", false},
        {"WARN_CASE", true},
        {"ANOTHER_NORMAL", true},
        {"ANOTHER_ERROR", false}
    };

    core::ProcessingContext context;
    int success_count = 0;
    int error_count = 0;

    for (const auto& [input, should_succeed] : test_inputs) {
        core::Record input_record;
        input_record.setField("input_value", input);
        input_record.setField("other_field", std::string("preserved"));

        auto output_record = batch_transformer.transform(input_record, context);

        if (output_record.hasField("output_value")) {
            success_count++;

            if (should_succeed) {
                std::string output = std::any_cast<std::string>(
                    output_record.getField("output_value"));
                EXPECT_EQ(fmt::format("PROCESSED_{}", input), output);
            }
        } else {
            error_count++;
            EXPECT_FALSE(should_succeed)
                << "Transformation should have succeeded for: " << input;
        }

        // Verify unmapped fields are preserved
        EXPECT_TRUE(output_record.hasField("other_field"));
        EXPECT_EQ("preserved",
                 std::any_cast<std::string>(output_record.getField("other_field")));
    }

    EXPECT_EQ(3, success_count);
    EXPECT_EQ(2, error_count);
}

// Test transformation metrics collection
TEST_F(AdvancedTransformationTest, TransformationMetricsCollection) {
    transform::TransformationMetrics metrics;

    // Simulate transformation executions
    std::vector<std::tuple<std::string, std::string, int, bool>> executions = {
        {"person_id", "direct", 5, true},
        {"gender", "vocabulary_mapping", 15, true},
        {"birth_date", "date_transform", 8, true},
        {"invalid_field", "numeric_transform", 3, false},
        {"gender", "vocabulary_mapping", 12, true},
        {"person_id", "direct", 4, true}
    };

    for (const auto& [field, type, duration_ms, success] : executions) {
        metrics.record_execution(
            field, type,
            std::chrono::milliseconds(duration_ms),
            success);
    }

    // Check field statistics
    auto person_stats = metrics.get_field_stats("person_id");
    EXPECT_EQ(2, person_stats.total_count);
    EXPECT_EQ(2, person_stats.success_count);
    EXPECT_NEAR(4.5, person_stats.average_duration(), 0.5);

    auto gender_stats = metrics.get_field_stats("gender");
    EXPECT_EQ(2, gender_stats.total_count);
    EXPECT_EQ(1.0, gender_stats.success_rate());

    // Check transformation type statistics
    auto vocab_stats = metrics.get_transformation_stats("vocabulary_mapping");
    EXPECT_EQ(2, vocab_stats.total_count);
    EXPECT_NEAR(13.5, vocab_stats.average_duration(), 0.5);

    // Check all field names
    auto field_names = metrics.get_field_names();
    EXPECT_EQ(4, field_names.size());

    // Reset and verify
    metrics.reset();
    auto after_reset = metrics.get_field_stats("person_id");
    EXPECT_EQ(0, after_reset.total_count);
}

} // namespace omop::test