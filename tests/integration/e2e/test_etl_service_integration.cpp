// Integration tests for ETL service
#include <gtest/gtest.h>
#include "etl/etl_service.h"
#include "etl/etl_service_client.h"
#include "test_helpers/integration_test_environment.h"
#include "test_helpers/mock_service_registry.h"
#include <grpcpp/grpcpp.h>
#include <thread>
#include <future>

namespace omop::test {

class ETLServiceIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger_ = common::Logger::get("test-etl-service");

        // Setup test environment
        env_ = std::make_unique<IntegrationTestEnvironment>();

        try {
            env_->setup();
            startETLService();
        } catch (const std::exception& e) {
            GTEST_SKIP() << "Test environment not available: " << e.what();
        }
    }

    void TearDown() override {
        stopETLService();
        if (env_) {
            env_->teardown();
        }
    }

    void startETLService() {
        // Configure service
        etl::ServiceConfig config;
        config.port = 50051;
        config.num_worker_threads = 4;
        config.enable_tls = false;
        config.enable_health_check = true;
        config.enable_metrics = true;
        config.max_concurrent_jobs = 10;

        // Create and start service
        service_ = std::make_unique<etl::ETLService>(config);

        // Configure with test databases
        etl::ETLConfiguration etl_config;
        etl_config.source_connection = env_->get_source_connection_config();
        etl_config.target_connection = env_->get_target_connection_config();
        etl_config.vocabulary_schema = "vocabulary";
        etl_config.target_schema = "test_cdm";

        service_->configure(etl_config);

        // Start service in background thread
        service_thread_ = std::thread([this]() {
            service_->start();
        });

        // Wait for service to be ready
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // Create client
        auto channel = grpc::CreateChannel("localhost:50051",
                                         grpc::InsecureChannelCredentials());
        client_ = std::make_unique<etl::ETLServiceClient>(channel);

        // Wait for service to be fully ready
        waitForServiceReady();
    }

    void stopETLService() {
        if (service_) {
            service_->shutdown();
        }
        if (service_thread_.joinable()) {
            service_thread_.join();
        }
    }

    void waitForServiceReady() {
        int max_attempts = 20;
        for (int i = 0; i < max_attempts; ++i) {
            if (client_->check_health()) {
                return;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        FAIL() << "Service failed to become ready";
    }

    std::unique_ptr<IntegrationTestEnvironment> env_;
    std::unique_ptr<etl::ETLService> service_;
    std::unique_ptr<etl::ETLServiceClient> client_;
    std::thread service_thread_;
    std::shared_ptr<common::Logger> logger_;
};

// Test basic job submission and execution
TEST_F(ETLServiceIntegrationTest, BasicJobSubmissionAndExecution) {
    // Generate test data
    env_->generate_source_data(100);

    // Create job request
    etl::JobRequest request;
    request.job_name = "test_job_basic";
    request.job_type = etl::JobType::FullLoad;

    // Add table mappings
    etl::TableMapping person_mapping;
    person_mapping.source_table = "patients";
    person_mapping.target_table = "person";
    person_mapping.mapping_config = R"({
        "transformations": [
            {
                "source_column": "patient_id",
                "target_column": "person_id",
                "type": "direct"
            },
            {
                "source_column": "gender",
                "target_column": "gender_concept_id",
                "type": "vocabulary_mapping",
                "vocabulary": "Gender"
            }
        ]
    })";
    request.table_mappings.push_back(person_mapping);

    // Submit job
    auto job_response = client_->submit_job(request);

    ASSERT_TRUE(job_response.success);
    ASSERT_FALSE(job_response.job_id.empty());

    logger_->info("Submitted job with ID: {}", job_response.job_id);

    // Monitor job progress
    etl::JobStatus final_status = etl::JobStatus::Unknown;
    int max_wait_seconds = 30;

    for (int i = 0; i < max_wait_seconds; ++i) {
        auto status_response = client_->get_job_status(job_response.job_id);

        ASSERT_TRUE(status_response.success);

        logger_->info("Job {} status: {}, progress: {}%",
                     job_response.job_id,
                     etl::job_status_to_string(status_response.status),
                     status_response.progress_percent);

        if (status_response.status == etl::JobStatus::Completed ||
            status_response.status == etl::JobStatus::Failed) {
            final_status = status_response.status;
            break;
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    EXPECT_EQ(final_status, etl::JobStatus::Completed);

    // Get job results
    auto results = client_->get_job_results(job_response.job_id);

    ASSERT_TRUE(results.success);
    EXPECT_GT(results.records_processed, 0);
    EXPECT_EQ(results.records_loaded, results.records_processed);
    EXPECT_EQ(results.error_count, 0);

    // Verify data in target
    auto conn = env_->get_target_connection();
    auto count = conn->execute_query("SELECT COUNT(*) FROM test_cdm.person");
    ASSERT_TRUE(count->next());
    EXPECT_EQ(std::any_cast<int64_t>(count->get_value(0)), 100);
}

// Test concurrent job execution
TEST_F(ETLServiceIntegrationTest, ConcurrentJobExecution) {
    // Generate test data for multiple tables
    env_->generate_source_data(200);

    // Create multiple job requests
    std::vector<etl::JobRequest> requests;

    // Job 1: Person table
    etl::JobRequest person_job;
    person_job.job_name = "concurrent_person";
    person_job.job_type = etl::JobType::FullLoad;

    etl::TableMapping person_mapping;
    person_mapping.source_table = "patients";
    person_mapping.target_table = "person";
    person_mapping.mapping_config = env_->get_person_mapping_config();
    person_job.table_mappings.push_back(person_mapping);
    requests.push_back(person_job);

    // Job 2: Visit table
    etl::JobRequest visit_job;
    visit_job.job_name = "concurrent_visit";
    visit_job.job_type = etl::JobType::FullLoad;

    etl::TableMapping visit_mapping;
    visit_mapping.source_table = "encounters";
    visit_mapping.target_table = "visit_occurrence";
    visit_mapping.mapping_config = env_->get_visit_mapping_config();
    visit_job.table_mappings.push_back(visit_mapping);
    requests.push_back(visit_job);

    // Job 3: Condition table
    etl::JobRequest condition_job;
    condition_job.job_name = "concurrent_condition";
    condition_job.job_type = etl::JobType::FullLoad;

    etl::TableMapping condition_mapping;
    condition_mapping.source_table = "diagnoses";
    condition_mapping.target_table = "condition_occurrence";
    condition_mapping.mapping_config = env_->get_condition_mapping_config();
    condition_job.table_mappings.push_back(condition_mapping);
    requests.push_back(condition_job);

    // Submit all jobs concurrently
    std::vector<std::future<etl::JobSubmitResponse>> futures;

    for (const auto& request : requests) {
        futures.push_back(std::async(std::launch::async,
            [this, request]() {
                return client_->submit_job(request);
            }));
    }

    // Collect job IDs
    std::vector<std::string> job_ids;
    for (auto& future : futures) {
        auto response = future.get();
        ASSERT_TRUE(response.success);
        job_ids.push_back(response.job_id);
    }

    logger_->info("Submitted {} concurrent jobs", job_ids.size());

    // Monitor all jobs until completion
    bool all_completed = false;
    int max_wait_seconds = 60;

    for (int i = 0; i < max_wait_seconds && !all_completed; ++i) {
        all_completed = true;

        for (const auto& job_id : job_ids) {
            auto status = client_->get_job_status(job_id);

            if (status.status != etl::JobStatus::Completed &&
                status.status != etl::JobStatus::Failed) {
                all_completed = false;
            }
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    EXPECT_TRUE(all_completed) << "Not all jobs completed within timeout";

    // Verify all jobs succeeded
    for (const auto& job_id : job_ids) {
        auto results = client_->get_job_results(job_id);
        EXPECT_TRUE(results.success);
        EXPECT_GT(results.records_processed, 0);
        EXPECT_EQ(results.error_count, 0);
    }
}

// Test job cancellation
TEST_F(ETLServiceIntegrationTest, JobCancellation) {
    // Generate large dataset to ensure job runs long enough
    env_->generate_source_data(10000);

    // Create job with slow processing
    etl::JobRequest request;
    request.job_name = "test_cancellation";
    request.job_type = etl::JobType::FullLoad;
    request.options["processing_delay_ms"] = "10"; // Artificial delay per record

    etl::TableMapping mapping;
    mapping.source_table = "patients";
    mapping.target_table = "person";
    mapping.mapping_config = env_->get_person_mapping_config();
    request.table_mappings.push_back(mapping);

    // Submit job
    auto submit_response = client_->submit_job(request);
    ASSERT_TRUE(submit_response.success);

    // Wait for job to start processing
    std::this_thread::sleep_for(std::chrono::seconds(1));

    // Cancel job
    auto cancel_response = client_->cancel_job(submit_response.job_id);
    EXPECT_TRUE(cancel_response.success);

    // Wait for cancellation to take effect
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Verify job was cancelled
    auto status = client_->get_job_status(submit_response.job_id);
    EXPECT_EQ(status.status, etl::JobStatus::Cancelled);

    // Verify partial results
    auto results = client_->get_job_results(submit_response.job_id);
    EXPECT_TRUE(results.success);
    EXPECT_GT(results.records_processed, 0);
    EXPECT_LT(results.records_processed, 10000); // Should not have processed all
}

// Test incremental loading
TEST_F(ETLServiceIntegrationTest, IncrementalLoading) {
    // Initial full load
    env_->generate_source_data(100);

    etl::JobRequest initial_request;
    initial_request.job_name = "initial_load";
    initial_request.job_type = etl::JobType::FullLoad;

    etl::TableMapping mapping;
    mapping.source_table = "patients";
    mapping.target_table = "person";
    mapping.mapping_config = env_->get_person_mapping_config();
    initial_request.table_mappings.push_back(mapping);

    auto initial_response = client_->submit_job(initial_request);
    ASSERT_TRUE(initial_response.success);

    // Wait for completion
    waitForJobCompletion(initial_response.job_id);

    // Add more data with timestamps
    env_->add_incremental_source_data(50);

    // Incremental load
    etl::JobRequest incremental_request;
    incremental_request.job_name = "incremental_load";
    incremental_request.job_type = etl::JobType::IncrementalLoad;
    incremental_request.options["last_load_timestamp"] =
        std::to_string(std::chrono::system_clock::now().time_since_epoch().count());

    incremental_request.table_mappings.push_back(mapping);

    auto incremental_response = client_->submit_job(incremental_request);
    ASSERT_TRUE(incremental_response.success);

    waitForJobCompletion(incremental_response.job_id);

    // Verify results
    auto results = client_->get_job_results(incremental_response.job_id);
    EXPECT_EQ(results.records_processed, 50);

    // Verify total records
    auto conn = env_->get_target_connection();
    auto count = conn->execute_query("SELECT COUNT(*) FROM test_cdm.person");
    ASSERT_TRUE(count->next());
    EXPECT_EQ(std::any_cast<int64_t>(count->get_value(0)), 150);
}

// Test job scheduling
TEST_F(ETLServiceIntegrationTest, JobScheduling) {
    // Schedule a job to run in the future
    etl::JobRequest request;
    request.job_name = "scheduled_job";
    request.job_type = etl::JobType::FullLoad;
    request.schedule_type = etl::ScheduleType::Once;

    // Schedule to run 2 seconds from now
    auto run_time = std::chrono::system_clock::now() + std::chrono::seconds(2);
    request.scheduled_time = std::chrono::system_clock::to_time_t(run_time);

    etl::TableMapping mapping;
    mapping.source_table = "patients";
    mapping.target_table = "person";
    mapping.mapping_config = env_->get_person_mapping_config();
    request.table_mappings.push_back(mapping);

    // Generate test data
    env_->generate_source_data(50);

    // Submit scheduled job
    auto response = client_->submit_job(request);
    ASSERT_TRUE(response.success);

    // Verify job is in scheduled state
    auto initial_status = client_->get_job_status(response.job_id);
    EXPECT_EQ(initial_status.status, etl::JobStatus::Scheduled);

    // Wait for scheduled time
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Job should now be running or completed
    auto running_status = client_->get_job_status(response.job_id);
    EXPECT_TRUE(running_status.status == etl::JobStatus::Running ||
                running_status.status == etl::JobStatus::Completed);

    // Wait for completion
    waitForJobCompletion(response.job_id);

    // Verify execution
    auto results = client_->get_job_results(response.job_id);
    EXPECT_EQ(results.records_processed, 50);
}

// Test error handling and recovery
TEST_F(ETLServiceIntegrationTest, ErrorHandlingAndRecovery) {
    // Generate data with known issues
    env_->generate_source_data_with_errors(100);

    // Create job with error handling enabled
    etl::JobRequest request;
    request.job_name = "error_handling_test";
    request.job_type = etl::JobType::FullLoad;
    request.options["continue_on_error"] = "true";
    request.options["max_error_rate"] = "0.1"; // Allow up to 10% errors

    etl::TableMapping mapping;
    mapping.source_table = "patients";
    mapping.target_table = "person";
    mapping.mapping_config = env_->get_person_mapping_config();
    request.table_mappings.push_back(mapping);

    // Submit job
    auto response = client_->submit_job(request);
    ASSERT_TRUE(response.success);

    waitForJobCompletion(response.job_id);

    // Get results
    auto results = client_->get_job_results(response.job_id);

    EXPECT_TRUE(results.success);
    EXPECT_GT(results.records_processed, 0);
    EXPECT_GT(results.error_count, 0);
    EXPECT_LT(results.error_rate, 0.1);

    // Get error details
    auto error_report = client_->get_job_errors(response.job_id);

    ASSERT_TRUE(error_report.success);
    EXPECT_GT(error_report.errors.size(), 0);

    for (const auto& error : error_report.errors) {
        logger_->info("Error in record {}: {}",
                     error.record_id, error.error_message);
    }
}

// Test service health monitoring
TEST_F(ETLServiceIntegrationTest, ServiceHealthMonitoring) {
    // Check basic health
    auto health = client_->check_health();
    EXPECT_TRUE(health);

    // Get detailed health status
    auto health_status = client_->get_health_status();

    EXPECT_TRUE(health_status.is_healthy);
    EXPECT_EQ(health_status.service_name, "ETLService");
    EXPECT_FALSE(health_status.version.empty());

    // Check component health
    EXPECT_GT(health_status.components.size(), 0);

    for (const auto& [component, status] : health_status.components) {
        logger_->info("Component {} health: {}",
                     component, status ? "healthy" : "unhealthy");
    }

    // Submit multiple jobs to stress the service
    std::vector<std::string> job_ids;
    for (int i = 0; i < 5; ++i) {
        etl::JobRequest request;
        request.job_name = "health_test_" + std::to_string(i);
        request.job_type = etl::JobType::FullLoad;

        etl::TableMapping mapping;
        mapping.source_table = "patients";
        mapping.target_table = "person";
        request.table_mappings.push_back(mapping);

        auto response = client_->submit_job(request);
        if (response.success) {
            job_ids.push_back(response.job_id);
        }
    }

    // Check health under load
    auto loaded_health = client_->get_health_status();
    EXPECT_TRUE(loaded_health.is_healthy);
    EXPECT_GT(loaded_health.active_jobs, 0);
    EXPECT_LE(loaded_health.active_jobs, loaded_health.max_jobs);
}

// Test job retry mechanism
TEST_F(ETLServiceIntegrationTest, JobRetryMechanism) {
    // Create a job that will fail initially
    env_->setup_failing_source_connection();

    etl::JobRequest request;
    request.job_name = "retry_test";
    request.job_type = etl::JobType::FullLoad;
    request.retry_policy.enabled = true;
    request.retry_policy.max_retries = 3;
    request.retry_policy.initial_delay_seconds = 1;
    request.retry_policy.backoff_multiplier = 2.0;

    etl::TableMapping mapping;
    mapping.source_table = "patients";
    mapping.target_table = "person";
    request.table_mappings.push_back(mapping);

    // Submit job
    auto response = client_->submit_job(request);
    ASSERT_TRUE(response.success);

    // After 2 seconds, fix the connection
    std::thread fix_thread([this]() {
        std::this_thread::sleep_for(std::chrono::seconds(2));
        env_->fix_source_connection();
        env_->generate_source_data(50);
    });

    // Wait for job completion
    waitForJobCompletion(response.job_id, 30); // Extended timeout for retries

    fix_thread.join();

    // Get results
    auto results = client_->get_job_results(response.job_id);
    auto status = client_->get_job_status(response.job_id);

    EXPECT_EQ(status.status, etl::JobStatus::Completed);
    EXPECT_GT(status.retry_count, 0);
    EXPECT_EQ(results.records_processed, 50);
}

// Test service metrics collection
TEST_F(ETLServiceIntegrationTest, ServiceMetricsCollection) {
    // Enable metrics endpoint
    auto metrics = client_->get_metrics();

    ASSERT_TRUE(metrics.success);

    // Check initial metrics
    auto initial_jobs_total = metrics.counters["jobs_submitted_total"];
    auto initial_records_total = metrics.counters["records_processed_total"];

    // Run a job
    env_->generate_source_data(100);

    etl::JobRequest request;
    request.job_name = "metrics_test";
    request.job_type = etl::JobType::FullLoad;

    etl::TableMapping mapping;
    mapping.source_table = "patients";
    mapping.target_table = "person";
    mapping.mapping_config = env_->get_person_mapping_config();
    request.table_mappings.push_back(mapping);

    auto response = client_->submit_job(request);
    ASSERT_TRUE(response.success);

    waitForJobCompletion(response.job_id);

    // Get updated metrics
    auto updated_metrics = client_->get_metrics();

    EXPECT_GT(updated_metrics.counters["jobs_submitted_total"], initial_jobs_total);
    EXPECT_GT(updated_metrics.counters["records_processed_total"], initial_records_total);
    EXPECT_GT(updated_metrics.histograms["job_duration_seconds"].count, 0);
    EXPECT_GT(updated_metrics.gauges["active_jobs"], 0);

    // Log some key metrics
    logger_->info("Service metrics:");
    logger_->info("  - Jobs submitted: {}",
                 updated_metrics.counters["jobs_submitted_total"]);
    logger_->info("  - Records processed: {}",
                 updated_metrics.counters["records_processed_total"]);
    logger_->info("  - Average job duration: {:.2f}s",
                 updated_metrics.histograms["job_duration_seconds"].mean);
}

private:
    void waitForJobCompletion(const std::string& job_id, int timeout_seconds = 30) {
        for (int i = 0; i < timeout_seconds; ++i) {
            auto status = client_->get_job_status(job_id);

            if (status.status == etl::JobStatus::Completed ||
                status.status == etl::JobStatus::Failed ||
                status.status == etl::JobStatus::Cancelled) {
                return;
            }

            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        FAIL() << "Job " << job_id << " did not complete within timeout";
    }
};

} // namespace omop::test