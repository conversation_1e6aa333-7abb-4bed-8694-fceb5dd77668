// tests/integration/e2e/test_disaster_recovery_e2e.cpp
// Tests disaster recovery scenarios including backup, restore, and failover
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include <filesystem>

namespace omop::e2e::test {

class DisasterRecoveryTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: source_db

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm

            backup:
                enabled: true
                path: /tmp/omop_backup
                retention_days: 7
                compress: true

            recovery:
                checkpoint_interval_seconds: 60
                checkpoint_path: /tmp/omop_checkpoints
        )");

        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);
        etl_service_ = std::make_shared<ETLService>(config_, pipeline_manager_);

        // Create backup directories
        std::filesystem::create_directories("/tmp/omop_backup");
        std::filesystem::create_directories("/tmp/omop_checkpoints");
    }

    void TearDown() override {
        // Clean up backup files
        std::filesystem::remove_all("/tmp/omop_backup");
        std::filesystem::remove_all("/tmp/omop_checkpoints");
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    DatabaseFixture db_fixture_;
};

// Tests checkpoint creation and recovery
TEST_F(DisasterRecoveryTest, CheckpointRecovery) {
    // Create a long-running job
    ETLJobRequest request;
    request.name = "Checkpoint Test";
    request.source_table = "large_patients_table";
    request.target_table = "person";
    request.pipeline_config.batch_size = 100;
    request.pipeline_config.checkpoint_interval = std::chrono::seconds(1);

    auto job_id = etl_service_->create_job(request);

    // Let it run for a bit to create checkpoints
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Simulate failure by cancelling job
    etl_service_->cancel_job(job_id);

    // Verify checkpoint was created
    std::filesystem::path checkpoint_path = "/tmp/omop_checkpoints";
    bool checkpoint_exists = false;

    for (const auto& entry : std::filesystem::directory_iterator(checkpoint_path)) {
        if (entry.path().string().find(job_id) != std::string::npos) {
            checkpoint_exists = true;
            break;
        }
    }

    EXPECT_TRUE(checkpoint_exists);

    // Create new job from checkpoint
    request.name = "Recovery Test";
    auto recovery_job_id = etl_service_->create_job(request);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(5));

    auto result = etl_service_->get_job_result(recovery_job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->status, core::JobStatus::Completed);
}

// Tests backup and restore functionality
TEST_F(DisasterRecoveryTest, BackupAndRestore) {
    // Run initial ETL to populate data
    auto initial_jobs = etl_service_->run_all_tables(false);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(10));

    // Get record counts before backup
    auto person_count_before = db_fixture_.executeScalar<int>(
        "SELECT COUNT(*) FROM cdm.person");
    auto visit_count_before = db_fixture_.executeScalar<int>(
        "SELECT COUNT(*) FROM cdm.visit_occurrence");

    // Trigger backup
    BackupManager backup_mgr(config_);
    auto backup_id = backup_mgr.createBackup("full_backup");

    ASSERT_FALSE(backup_id.empty());

    // Simulate data loss
    db_fixture_.execute("TRUNCATE TABLE cdm.person CASCADE");
    db_fixture_.execute("TRUNCATE TABLE cdm.visit_occurrence CASCADE");

    // Verify data is gone
    EXPECT_EQ(db_fixture_.executeScalar<int>("SELECT COUNT(*) FROM cdm.person"), 0);

    // Restore from backup
    bool restored = backup_mgr.restoreBackup(backup_id);
    EXPECT_TRUE(restored);

    // Verify data is restored
    auto person_count_after = db_fixture_.executeScalar<int>(
        "SELECT COUNT(*) FROM cdm.person");
    auto visit_count_after = db_fixture_.executeScalar<int>(
        "SELECT COUNT(*) FROM cdm.visit_occurrence");

    EXPECT_EQ(person_count_after, person_count_before);
    EXPECT_EQ(visit_count_after, visit_count_before);
}

// Tests failover to secondary database
TEST_F(DisasterRecoveryTest, DatabaseFailover) {
    // Configure with primary and secondary databases
    config_->load_config_from_string(R"(
        target_db:
            primary:
                type: postgresql
                host: primary.db.local
                port: 5432
                database: omop_cdm
            secondary:
                type: postgresql
                host: secondary.db.local
                port: 5432
                database: omop_cdm_replica
            failover:
                enabled: true
                health_check_interval_seconds: 10
                max_retry_attempts: 3
    )");

    // Start job on primary
    ETLJobRequest request;
    request.name = "Failover Test";
    request.source_table = "patients";
    request.target_table = "person";

    auto job_id = etl_service_->create_job(request);

    // Simulate primary database failure
    db_fixture_.simulateConnectionFailure("primary.db.local");

    // Wait for failover to occur
    std::this_thread::sleep_for(std::chrono::seconds(15));

    // Verify job continued on secondary
    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());

    // Job should complete successfully despite primary failure
    EXPECT_EQ(result->status, core::JobStatus::Completed);
    EXPECT_TRUE(result->metrics.contains("failover_occurred"));
    EXPECT_TRUE(std::any_cast<bool>(result->metrics["failover_occurred"]));
}

// Tests incremental backup and point-in-time recovery
TEST_F(DisasterRecoveryTest, IncrementalBackupAndPITR) {
    BackupManager backup_mgr(config_);

    // Create initial full backup
    auto full_backup_id = backup_mgr.createBackup("full_backup", BackupType::Full);

    // Run some ETL jobs
    etl_service_->run_all_tables(false);
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Record timestamp for PITR
    auto pitr_timestamp = std::chrono::system_clock::now();

    // Create incremental backup
    auto incr_backup_id = backup_mgr.createBackup("incr_backup", BackupType::Incremental);

    // Run more ETL jobs
    etl_service_->run_all_tables(false);
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Simulate data corruption
    db_fixture_.execute("UPDATE cdm.person SET year_of_birth = 9999");

    // Restore to point in time
    bool restored = backup_mgr.restoreToPointInTime(pitr_timestamp);
    EXPECT_TRUE(restored);

    // Verify data is restored to correct state
    auto corrupted_count = db_fixture_.executeScalar<int>(
        "SELECT COUNT(*) FROM cdm.person WHERE year_of_birth = 9999");
    EXPECT_EQ(corrupted_count, 0);
}

} // namespace omop::e2e::test
