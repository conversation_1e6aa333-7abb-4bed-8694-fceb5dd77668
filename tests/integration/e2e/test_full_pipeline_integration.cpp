// End-to-end integration test for full ETL pipeline
#include <gtest/gtest.h>
#include "pipeline/etl_pipeline.h"
#include "test_helpers/integration_test_environment.h"
#include "test_helpers/test_data_generator.h"
#include "common/configuration.h"
#include <yaml-cpp/yaml.h>

namespace omop::test {

class FullPipelineIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger_ = common::Logger::get("test-full-pipeline");

        // Setup test environment
        env_ = std::make_unique<IntegrationTestEnvironment>();

        try {
            env_->setup();
            createTestConfiguration();
        } catch (const std::exception& e) {
            GTEST_SKIP() << "Test environment not available: " << e.what();
        }
    }

    void TearDown() override {
        if (env_) {
            env_->teardown();
        }
    }

    void createTestConfiguration() {
        // Create comprehensive pipeline configuration
        YAML::Node config;

        // Source database configuration
        config["source"]["type"] = "postgresql";
        config["source"]["connection"]["host"] = "localhost";
        config["source"]["connection"]["port"] = 5432;
        config["source"]["connection"]["database"] = "source_db";
        config["source"]["connection"]["username"] = "test_user";
        config["source"]["connection"]["password"] = "test_pass";

        // Target database configuration
        config["target"]["type"] = "postgresql";
        config["target"]["connection"]["host"] = "localhost";
        config["target"]["connection"]["port"] = 5432;
        config["target"]["connection"]["database"] = "omop_test_db";
        config["target"]["connection"]["username"] = "test_user";
        config["target"]["connection"]["password"] = "test_pass";
        config["target"]["schema"] = "test_cdm";

        // Vocabulary configuration
        config["vocabulary"]["schema"] = "vocabulary";
        config["vocabulary"]["cache_size"] = 10000;

        // Pipeline settings
        config["pipeline"]["batch_size"] = 1000;
        config["pipeline"]["parallel_workers"] = 4;
        config["pipeline"]["commit_interval"] = 5000;
        config["pipeline"]["error_threshold"] = 0.05;

        // Table mappings
        config["mappings"] = createTableMappings();

        // Save configuration
        config_path_ = env_->get_temp_dir() / "pipeline_config.yaml";
        std::ofstream config_file(config_path_);
        config_file << config;
        config_file.close();
    }

    YAML::Node createTableMappings() {
        YAML::Node mappings;

        // Person mapping
        YAML::Node person_mapping;
        person_mapping["source_table"] = "patients";
        person_mapping["target_table"] = "person";
        person_mapping["transformations"] = createPersonTransformations();
        mappings.push_back(person_mapping);

        // Visit mapping
        YAML::Node visit_mapping;
        visit_mapping["source_table"] = "encounters";
        visit_mapping["target_table"] = "visit_occurrence";
        visit_mapping["transformations"] = createVisitTransformations();
        mappings.push_back(visit_mapping);

        // Condition mapping
        YAML::Node condition_mapping;
        condition_mapping["source_table"] = "diagnoses";
        condition_mapping["target_table"] = "condition_occurrence";
        condition_mapping["transformations"] = createConditionTransformations();
        mappings.push_back(condition_mapping);

        return mappings;
    }

    YAML::Node createPersonTransformations() {
        YAML::Node transforms;

        // Direct mappings
        transforms.push_back(createTransform("patient_id", "person_id", "direct"));
        transforms.push_back(createTransform("birth_date", "birth_datetime", "date_transform"));

        // Vocabulary mappings
        auto gender_transform = createTransform("gender", "gender_concept_id", "vocabulary_mapping");
        gender_transform["parameters"]["vocabulary"] = "Gender";
        transforms.push_back(gender_transform);

        auto race_transform = createTransform("race", "race_concept_id", "vocabulary_mapping");
        race_transform["parameters"]["vocabulary"] = "Race";
        transforms.push_back(race_transform);

        auto ethnicity_transform = createTransform("ethnicity", "ethnicity_concept_id", "vocabulary_mapping");
        ethnicity_transform["parameters"]["vocabulary"] = "Ethnicity";
        transforms.push_back(ethnicity_transform);

        // Derived fields
        auto year_transform = createTransform("birth_date", "year_of_birth", "date_calculation");
        year_transform["parameters"]["extract"] = "year";
        transforms.push_back(year_transform);

        return transforms;
    }

    YAML::Node createVisitTransformations() {
        YAML::Node transforms;

        transforms.push_back(createTransform("encounter_id", "visit_occurrence_id", "direct"));
        transforms.push_back(createTransform("patient_id", "person_id", "direct"));

        auto visit_concept = createTransform("encounter_type", "visit_concept_id", "vocabulary_mapping");
        visit_concept["parameters"]["vocabulary"] = "Visit";
        transforms.push_back(visit_concept);

        transforms.push_back(createTransform("start_datetime", "visit_start_datetime", "date_transform"));
        transforms.push_back(createTransform("end_datetime", "visit_end_datetime", "date_transform"));

        return transforms;
    }

    YAML::Node createConditionTransformations() {
        YAML::Node transforms;

        transforms.push_back(createTransform("diagnosis_id", "condition_occurrence_id", "direct"));
        transforms.push_back(createTransform("patient_id", "person_id", "direct"));

        auto condition_concept = createTransform("icd10_code", "condition_concept_id", "vocabulary_mapping");
        condition_concept["parameters"]["vocabulary"] = "ICD10CM";
        condition_concept["parameters"]["target_vocabulary"] = "SNOMED";
        transforms.push_back(condition_concept);

        transforms.push_back(createTransform("diagnosis_date", "condition_start_date", "date_transform"));

        return transforms;
    }

    YAML::Node createTransform(const std::string& source, const std::string& target,
                              const std::string& type) {
        YAML::Node transform;
        transform["source_column"] = source;
        transform["target_column"] = target;
        transform["type"] = type;
        return transform;
    }

    std::unique_ptr<IntegrationTestEnvironment> env_;
    std::filesystem::path config_path_;
    std::shared_ptr<common::Logger> logger_;
};

// Test full ETL pipeline execution
TEST_F(FullPipelineIntegrationTest, FullPipelineExecution) {
    // Create and configure pipeline
    pipeline::ETLPipeline pipeline;

    pipeline::PipelineConfig config;
    config.config_file = config_path_.string();
    config.log_level = "info";
    config.validate_only = false;

    // Initialize pipeline
    ASSERT_TRUE(pipeline.initialize(config));

    // Generate test data in source database
    env_->generate_source_data(1000); // 1000 patients

    // Run pipeline
    auto start = std::chrono::high_resolution_clock::now();

    pipeline::PipelineResult result = pipeline.run();

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end - start);

    // Verify results
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.status, pipeline::PipelineStatus::Completed);

    logger_->info("Pipeline execution completed:");
    logger_->info("  - Duration: {} seconds", duration.count());
    logger_->info("  - Records processed: {}", result.records_processed);
    logger_->info("  - Records loaded: {}", result.records_loaded);
    logger_->info("  - Errors: {}", result.error_count);

    // Verify data in OMOP tables
    auto conn = env_->get_target_connection();

    // Check person table
    auto person_count = conn->execute_query(
        "SELECT COUNT(*) FROM test_cdm.person");
    ASSERT_TRUE(person_count->next());
    EXPECT_GE(std::any_cast<int64_t>(person_count->get_value(0)), 900);

    // Check visit_occurrence table
    auto visit_count = conn->execute_query(
        "SELECT COUNT(*) FROM test_cdm.visit_occurrence");
    ASSERT_TRUE(visit_count->next());
    EXPECT_GT(std::any_cast<int64_t>(visit_count->get_value(0)), 0);

    // Check condition_occurrence table
    auto condition_count = conn->execute_query(
        "SELECT COUNT(*) FROM test_cdm.condition_occurrence");
    ASSERT_TRUE(condition_count->next());
    EXPECT_GT(std::any_cast<int64_t>(condition_count->get_value(0)), 0);
}

// Test pipeline with data quality checks
TEST_F(FullPipelineIntegrationTest, PipelineWithDataQualityChecks) {
    // Add data quality configuration
    YAML::Node config = YAML::LoadFile(config_path_.string());

    config["data_quality"]["enabled"] = true;
    config["data_quality"]["checks"] = YAML::Load(R"(
        - type: completeness
          tables: [person, visit_occurrence]
          threshold: 0.95
        - type: uniqueness
          table: person
          column: person_id
        - type: referential_integrity
          from_table: visit_occurrence
          from_column: person_id
          to_table: person
          to_column: person_id
        - type: value_range
          table: person
          column: year_of_birth
          min: 1900
          max: 2024
    )");

    // Save updated configuration
    std::ofstream config_file(config_path_);
    config_file << config;
    config_file.close();

    // Create pipeline with quality checks
    pipeline::ETLPipeline pipeline;

    pipeline::PipelineConfig pipeline_config;
    pipeline_config.config_file = config_path_.string();
    pipeline_config.enable_quality_checks = true;

    ASSERT_TRUE(pipeline.initialize(pipeline_config));

    // Generate test data with some quality issues
    env_->generate_source_data_with_quality_issues(500);

    // Run pipeline
    auto result = pipeline.run();

    // Check quality report
    EXPECT_TRUE(result.success);
    EXPECT_TRUE(result.quality_report.has_value());

    auto& quality_report = *result.quality_report;

    logger_->info("Data quality results:");
    for (const auto& check : quality_report.checks) {
        logger_->info("  - {}: {} (score: {:.2f})",
                     check.name,
                     check.passed ? "PASSED" : "FAILED",
                     check.score);
    }

    // Verify specific quality checks
    auto completeness_check = std::find_if(
        quality_report.checks.begin(),
        quality_report.checks.end(),
        [](const auto& check) { return check.type == "completeness"; });

    ASSERT_NE(completeness_check, quality_report.checks.end());
    EXPECT_GE(completeness_check->score, 0.90);
}

// Test pipeline recovery and restart
TEST_F(FullPipelineIntegrationTest, PipelineRecoveryAndRestart) {
    // Create pipeline with checkpointing enabled
    YAML::Node config = YAML::LoadFile(config_path_.string());
    config["pipeline"]["enable_checkpointing"] = true;
    config["pipeline"]["checkpoint_interval"] = 100;
    config["pipeline"]["checkpoint_dir"] = (env_->get_temp_dir() / "checkpoints").string();

    std::ofstream config_file(config_path_);
    config_file << config;
    config_file.close();

    // Generate test data
    env_->generate_source_data(1000);

    // Create pipeline that will be interrupted
    pipeline::ETLPipeline pipeline1;

    pipeline::PipelineConfig pipeline_config;
    pipeline_config.config_file = config_path_.string();

    ASSERT_TRUE(pipeline1.initialize(pipeline_config));

    // Start pipeline in separate thread
    std::atomic<bool> interrupt_flag(false);
    std::thread pipeline_thread([&pipeline1, &interrupt_flag]() {
        // Simulate interruption after processing some records
        std::thread interrupt_thread([&interrupt_flag]() {
            std::this_thread::sleep_for(std::chrono::seconds(2));
            interrupt_flag = true;
        });

        pipeline1.run_with_interruption(&interrupt_flag);
        interrupt_thread.join();
    });

    pipeline_thread.join();

    // Get checkpoint info
    auto checkpoint_info = pipeline1.get_last_checkpoint();
    ASSERT_TRUE(checkpoint_info.has_value());

    logger_->info("Pipeline interrupted at checkpoint:");
    logger_->info("  - Records processed: {}", checkpoint_info->records_processed);
    logger_->info("  - Last table: {}", checkpoint_info->last_table);
    logger_->info("  - Last record ID: {}", checkpoint_info->last_record_id);

    // Create new pipeline to resume from checkpoint
    pipeline::ETLPipeline pipeline2;

    pipeline_config.resume_from_checkpoint = true;
    pipeline_config.checkpoint_id = checkpoint_info->checkpoint_id;

    ASSERT_TRUE(pipeline2.initialize(pipeline_config));

    // Resume pipeline
    auto result = pipeline2.run();

    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.status, pipeline::PipelineStatus::Completed);

    // Verify all data was processed
    auto conn = env_->get_target_connection();
    auto count = conn->execute_query("SELECT COUNT(*) FROM test_cdm.person");
    ASSERT_TRUE(count->next());

    // Should have processed all records despite interruption
    EXPECT_GE(std::any_cast<int64_t>(count->get_value(0)), 950);
}

// Test pipeline monitoring and metrics
TEST_F(FullPipelineIntegrationTest, PipelineMonitoringAndMetrics) {
    // Enable detailed monitoring
    YAML::Node config = YAML::LoadFile(config_path_.string());
    config["monitoring"]["enabled"] = true;
    config["monitoring"]["metrics_interval"] = 100;
    config["monitoring"]["export_metrics"] = true;
    config["monitoring"]["metrics_file"] = (env_->get_temp_dir() / "metrics.json").string();

    std::ofstream config_file(config_path_);
    config_file << config;
    config_file.close();

    // Create pipeline with monitoring
    pipeline::ETLPipeline pipeline;

    pipeline::PipelineConfig pipeline_config;
    pipeline_config.config_file = config_path_.string();

    ASSERT_TRUE(pipeline.initialize(pipeline_config));

    // Set up metrics collector
    std::vector<pipeline::PipelineMetrics> collected_metrics;
    std::mutex metrics_mutex;

    pipeline.set_metrics_callback(
        [&collected_metrics, &metrics_mutex](const pipeline::PipelineMetrics& metrics) {
            std::lock_guard<std::mutex> lock(metrics_mutex);
            collected_metrics.push_back(metrics);
        });

    // Generate test data
    env_->generate_source_data(2000);

    // Run pipeline
    auto result = pipeline.run();

    EXPECT_TRUE(result.success);

    // Analyze collected metrics
    {
        std::lock_guard<std::mutex> lock(metrics_mutex);

        EXPECT_GT(collected_metrics.size(), 0);

        logger_->info("Collected {} metric snapshots", collected_metrics.size());

        // Check throughput over time
        double max_throughput = 0.0;
        double avg_throughput = 0.0;

        for (const auto& metrics : collected_metrics) {
            max_throughput = std::max(max_throughput, metrics.records_per_second);
            avg_throughput += metrics.records_per_second;
        }

        avg_throughput /= collected_metrics.size();

        logger_->info("Pipeline performance:");
        logger_->info("  - Average throughput: {:.0f} records/second", avg_throughput);
        logger_->info("  - Peak throughput: {:.0f} records/second", max_throughput);

        // Check final metrics
        const auto& final_metrics = collected_metrics.back();
        logger_->info("  - Total duration: {:.1f} seconds", final_metrics.elapsed_seconds);
        logger_->info("  - Memory usage: {:.1f} MB", final_metrics.memory_usage_mb);
        logger_->info("  - CPU usage: {:.1f}%", final_metrics.cpu_usage_percent);
    }

    // Verify metrics were exported
    EXPECT_TRUE(std::filesystem::exists(env_->get_temp_dir() / "metrics.json"));
}

// Test pipeline with multiple data sources
TEST_F(FullPipelineIntegrationTest, PipelineWithMultipleDataSources) {
    // Configure multiple source databases
    YAML::Node config = YAML::LoadFile(config_path_.string());

    // Add second source
    config["sources"]["primary"] = config["source"];
    config["sources"]["secondary"]["type"] = "postgresql";
    config["sources"]["secondary"]["connection"]["host"] = "localhost";
    config["sources"]["secondary"]["connection"]["port"] = 5433;
    config["sources"]["secondary"]["connection"]["database"] = "source_db2";
    config["sources"]["secondary"]["connection"]["username"] = "test_user";
    config["sources"]["secondary"]["connection"]["password"] = "test_pass";

    // Update mappings to use different sources
    config["mappings"][0]["source"] = "primary";  // patients from primary
    config["mappings"][1]["source"] = "secondary"; // encounters from secondary
    config["mappings"][2]["source"] = "secondary"; // diagnoses from secondary

    // Add data merge configuration
    config["merge"]["enabled"] = true;
    config["merge"]["key_mappings"] = YAML::Load(R"(
        - primary_key: patient_id
          secondary_key: person_id
          mapping_table: staging.patient_mapping
    )");

    std::ofstream config_file(config_path_);
    config_file << config;
    config_file.close();

    // Setup secondary database
    env_->setup_secondary_database();

    // Generate data in both sources
    env_->generate_source_data(500);          // Primary
    env_->generate_secondary_data(500);       // Secondary with matching IDs

    // Create and run pipeline
    pipeline::ETLPipeline pipeline;

    pipeline::PipelineConfig pipeline_config;
    pipeline_config.config_file = config_path_.string();

    ASSERT_TRUE(pipeline.initialize(pipeline_config));

    auto result = pipeline.run();

    EXPECT_TRUE(result.success);

    // Verify merged data
    auto conn = env_->get_target_connection();

    // Check that visits are linked to persons
    auto orphan_visits = conn->execute_query(R"(
        SELECT COUNT(*)
        FROM test_cdm.visit_occurrence v
        WHERE NOT EXISTS (
            SELECT 1 FROM test_cdm.person p
            WHERE p.person_id = v.person_id
        )
    )");

    ASSERT_TRUE(orphan_visits->next());
    EXPECT_EQ(std::any_cast<int64_t>(orphan_visits->get_value(0)), 0)
        << "Found orphan visits without matching persons";
}

} // namespace omop::test