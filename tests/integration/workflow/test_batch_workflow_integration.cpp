// Integration test for batch processing and workflow orchestration in the ETL pipeline
#include <gtest/gtest.h>
#include "core/job_scheduler.h"
#include "service/etl_service.h"
#include "load/batch_loader.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "transform/transformation_engine.h"
#include "test_helpers/database_fixture.h"
#include <filesystem>
#include <future>
#include <random>

namespace omop::test::integration {

/**
 * @brief Batch processing and workflow orchestration integration test
 *
 * Tests complex batch processing scenarios, job scheduling, and workflow dependencies.
 */
class BatchWorkflowIntegrationTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize services
        config_manager_ = std::make_shared<common::ConfigurationManager>();
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        job_manager_ = std::make_shared<core::JobManager>(config_manager_, logger_);

        // Start job manager
        job_manager_->start();

        // Initialize scheduler
        scheduler_ = std::make_shared<core::JobScheduler>(job_manager_);
        scheduler_->start();

        // Create test data
        createBatchTestData();
    }

    void TearDown() override {
        scheduler_->stop();
        job_manager_->stop();
        cleanupTestData();
        DatabaseFixture::TearDown();
    }

    void createBatchTestData() {
        test_data_dir_ = std::filesystem::temp_directory_path() / "batch_workflow_test";
        std::filesystem::create_directories(test_data_dir_);

        createLargeBatchFiles();
        createDependentDataFiles();
        createStreamingDataFile();
    }

    void cleanupTestData() {
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }
    }

    void createLargeBatchFiles() {
        // Create multiple batch files to test batch processing
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> age_dist(18, 90);
        std::uniform_int_distribution<> concept_dist(1000, 9999);

        for (int batch = 1; batch <= 5; ++batch) {
            std::ofstream file(test_data_dir_ / std::format("batch_{}.csv", batch));
            file << "person_id,birth_date,gender,race,measurement_date,measurement_concept_id,value_as_number,unit_concept_id\n";

            for (int i = 1; i <= 2000; ++i) {
                int person_id = (batch - 1) * 2000 + i;
                int age = age_dist(gen);
                int birth_year = 2024 - age;

                file << person_id << ","
                     << birth_year << "-01-01,"
                     << (i % 2 == 0 ? "M" : "F") << ","
                     << "White,"
                     << "2024-01-15,"
                     << concept_dist(gen) << ","
                     << (50.0 + (i % 100)) << ","
                     << "8713\n";
            }
            file.close();
        }
    }

    void createDependentDataFiles() {
        // Create files with dependencies for workflow testing

        // Master patient file
        std::ofstream master_file(test_data_dir_ / "patients_master.csv");
        master_file << "person_id,birth_date,gender,active\n";
        for (int i = 1; i <= 100; ++i) {
            master_file << i << ",1970-01-01,M,Y\n";
        }
        master_file.close();

        // Dependent visits file
        std::ofstream visits_file(test_data_dir_ / "visits_dependent.csv");
        visits_file << "visit_id,person_id,visit_date,visit_type\n";
        for (int i = 1; i <= 100; ++i) {
            visits_file << "V" << i << "," << i << ",2024-01-15,Outpatient\n";
        }
        visits_file.close();

        // Dependent procedures file
        std::ofstream proc_file(test_data_dir_ / "procedures_dependent.csv");
        proc_file << "procedure_id,visit_id,procedure_date,procedure_code\n";
        for (int i = 1; i <= 100; ++i) {
            proc_file << "P" << i << ",V" << i << ",2024-01-15,99213\n";
        }
        proc_file.close();
    }

    void createStreamingDataFile() {
        // Create a large file for streaming tests
        std::ofstream file(test_data_dir_ / "streaming_data.jsonl");

        for (int i = 1; i <= 50000; ++i) {
            nlohmann::json record = {
                {"patient_id", std::format("P{:06d}", i)},
                {"timestamp", "2024-01-15T10:00:00"},
                {"vital_signs", {
                    {"heart_rate", 60 + (i % 40)},
                    {"blood_pressure", {
                        {"systolic", 110 + (i % 30)},
                        {"diastolic", 70 + (i % 20)}
                    }},
                    {"temperature", 36.5 + (i % 10) * 0.1}
                }}
            };
            file << record.dump() << "\n";
        }
        file.close();
    }

protected:
    std::filesystem::path test_data_dir_;
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<core::JobManager> job_manager_;
    std::shared_ptr<core::JobScheduler> scheduler_;
};

// Test batch processing with multiple files
TEST_F(BatchWorkflowIntegrationTest, TestMultiFileBatchProcessing) {
    // Configure batch loader
    load::BatchLoaderOptions batch_options;
    batch_options.batch_size = 500;
    batch_options.max_batches_in_memory = 5;
    batch_options.enable_compression = true;
    batch_options.parallel_processing = true;
    batch_options.worker_threads = 4;

    // Create job configurations for each batch file
    std::vector<core::JobConfig> job_configs;

    for (int batch = 1; batch <= 5; ++batch) {
        core::JobConfig config;
        config.job_id = std::format("batch_job_{}", batch);
        config.job_name = std::format("Batch {} Processing", batch);
        config.priority = core::JobPriority::NORMAL;
        config.enable_checkpointing = true;
        config.checkpoint_interval = 1000;

        config.parameters["input_file"] = (test_data_dir_ / std::format("batch_{}.csv", batch)).string();
        config.parameters["target_table"] = "measurement";
        config.parameters["batch_size"] = "500";

        job_configs.push_back(config);
    }

    // Submit jobs
    std::vector<std::string> job_ids;
    for (const auto& config : job_configs) {
        auto job_id = job_manager_->submitJob(config);
        job_ids.push_back(job_id);
    }

    // Wait for all jobs to complete
    bool all_completed = false;
    auto timeout = std::chrono::steady_clock::now() + std::chrono::seconds(60);

    while (!all_completed && std::chrono::steady_clock::now() < timeout) {
        all_completed = true;

        for (const auto& job_id : job_ids) {
            auto job = job_manager_->getJob(job_id);
            if (job && job->getStatus() != core::JobStatus::Completed &&
                job->getStatus() != core::JobStatus::Failed) {
                all_completed = false;
                break;
            }
        }

        if (!all_completed) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    EXPECT_TRUE(all_completed);

    // Verify results
    size_t total_processed = 0;
    size_t total_failed = 0;

    for (const auto& job_id : job_ids) {
        auto job = job_manager_->getJob(job_id);
        ASSERT_NE(job, nullptr);

        auto stats = job->getStatistics();
        total_processed += stats.successful_records;
        total_failed += stats.failed_records;

        EXPECT_EQ(job->getStatus(), core::JobStatus::Completed);
        EXPECT_GT(stats.successful_records, 0);
        EXPECT_EQ(stats.failed_records, 0);
    }

    EXPECT_EQ(total_processed, 10000); // 5 files * 2000 records each
}

// Test workflow with job dependencies
TEST_F(BatchWorkflowIntegrationTest, TestJobDependencies) {
    // Create job schedule with dependencies
    core::JobSchedule master_schedule;
    master_schedule.schedule_id = "master_patient_load";
    master_schedule.job_config_id = "patient_config";
    master_schedule.trigger_type = core::TriggerType::MANUAL;

    auto master_schedule_id = scheduler_->addSchedule(master_schedule);

    // Create dependent job schedules
    core::JobSchedule visits_schedule;
    visits_schedule.schedule_id = "visits_load";
    visits_schedule.job_config_id = "visits_config";
    visits_schedule.trigger_type = core::TriggerType::DEPENDENCY;
    visits_schedule.dependencies.push_back("master_patient_load");

    auto visits_schedule_id = scheduler_->addSchedule(visits_schedule);

    core::JobSchedule procedures_schedule;
    procedures_schedule.schedule_id = "procedures_load";
    procedures_schedule.job_config_id = "procedures_config";
    procedures_schedule.trigger_type = core::TriggerType::DEPENDENCY;
    procedures_schedule.dependencies.push_back("visits_load");

    auto procedures_schedule_id = scheduler_->addSchedule(procedures_schedule);

    // Create job configurations
    core::JobConfig patient_config;
    patient_config.job_id = "patient_job";
    patient_config.job_name = "Patient Master Load";
    patient_config.parameters["input_file"] = (test_data_dir_ / "patients_master.csv").string();

    core::JobConfig visits_config;
    visits_config.job_id = "visits_job";
    visits_config.job_name = "Visits Load";
    visits_config.parameters["input_file"] = (test_data_dir_ / "visits_dependent.csv").string();

    core::JobConfig procedures_config;
    procedures_config.job_id = "procedures_job";
    procedures_config.job_name = "Procedures Load";
    procedures_config.parameters["input_file"] = (test_data_dir_ / "procedures_dependent.csv").string();

    // Submit master job to trigger the workflow
    auto master_job_id = scheduler_->triggerSchedule(master_schedule_id);
    ASSERT_TRUE(master_job_id.has_value());

    // Wait for workflow completion
    std::this_thread::sleep_for(std::chrono::seconds(10));

    // Verify all jobs completed in order
    auto all_jobs = job_manager_->getAllJobs();

    // Find jobs by name
    std::shared_ptr<core::Job> patient_job, visits_job, procedures_job;

    for (const auto& job : all_jobs) {
        if (job->getName() == "Patient Master Load") patient_job = job;
        else if (job->getName() == "Visits Load") visits_job = job;
        else if (job->getName() == "Procedures Load") procedures_job = job;
    }

    ASSERT_NE(patient_job, nullptr);
    ASSERT_NE(visits_job, nullptr);
    ASSERT_NE(procedures_job, nullptr);

    // Verify execution order
    EXPECT_LT(patient_job->getEndTime(), visits_job->getStartTime());
    EXPECT_LT(visits_job->getEndTime(), procedures_job->getStartTime());

    // Verify all completed successfully
    EXPECT_EQ(patient_job->getStatus(), core::JobStatus::Completed);
    EXPECT_EQ(visits_job->getStatus(), core::JobStatus::Completed);
    EXPECT_EQ(procedures_job->getStatus(), core::JobStatus::Completed);
}

// Test streaming data processing
TEST_F(BatchWorkflowIntegrationTest, TestStreamingDataProcessing) {
    // Configure streaming pipeline
    core::PipelineConfig config;
    config.batch_size = 1000;
    config.max_parallel_batches = 8;
    config.queue_size = 5000;
    config.commit_interval = 5000;

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Set up streaming JSON extractor
    auto extractor = std::make_unique<extract::JsonLinesExtractor>();
    std::unordered_map<std::string, std::any> extract_config;
    extract_config["filepath"] = (test_data_dir_ / "streaming_data.jsonl").string();

    core::ProcessingContext context;
    extractor->initialize(extract_config, context);
    pipeline->set_extractor(std::move(extractor));

    // Set up transformer with custom logic for vital signs
    class VitalSignsTransformer : public core::ITransformer {
    public:
        void initialize(const std::unordered_map<std::string, std::any>& config,
                       core::ProcessingContext& context) override {}

        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            core::Record output;

            // Extract patient ID
            auto patient_id = record.getFieldOptional("patient_id");
            if (patient_id.has_value()) {
                output.setField("person_id", patient_id.value());
            }

            // Extract vital signs
            auto vital_signs = record.getFieldOptional("vital_signs");
            if (vital_signs.has_value() && vital_signs->type() == typeid(nlohmann::json)) {
                auto vitals = std::any_cast<nlohmann::json>(*vital_signs);

                if (vitals.contains("heart_rate")) {
                    output.setField("heart_rate", vitals["heart_rate"].get<int>());
                }

                if (vitals.contains("blood_pressure")) {
                    auto bp = vitals["blood_pressure"];
                    output.setField("systolic_bp", bp["systolic"].get<int>());
                    output.setField("diastolic_bp", bp["diastolic"].get<int>());
                }

                if (vitals.contains("temperature")) {
                    output.setField("temperature_c", vitals["temperature"].get<double>());
                }
            }

            return output;
        }

        core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                        core::ProcessingContext& context) override {
            core::RecordBatch output(batch.size());

            for (const auto& record : batch) {
                auto transformed = transform(record, context);
                if (transformed.has_value()) {
                    output.addRecord(std::move(*transformed));
                }
            }

            return output;
        }

        std::string get_type() const override { return "vital_signs"; }

        omop::common::ValidationResult validate(const core::Record& record) const override {
            omop::common::ValidationResult result;

            auto patient_id = record.getFieldOptional("patient_id");
            if (!patient_id.has_value()) {
                result.add_error("patient_id", "Missing patient ID", "required");
            }

            return result;
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            return {};
        }
    };

    pipeline->set_transformer(std::make_unique<VitalSignsTransformer>());

    // Set up batch loader
    auto loader = load::BatchLoaderFactory::create("csv", load::BatchLoaderOptions{});
    std::unordered_map<std::string, std::any> loader_config;
    loader_config["output_file"] = (test_data_dir_ / "vital_signs_output.csv").string();
    loader->initialize(loader_config, context);
    pipeline->set_loader(std::move(loader));

    // Track progress
    std::atomic<size_t> records_processed{0};
    pipeline->set_progress_callback([&records_processed](const core::JobInfo& info) {
        records_processed = info.processed_records;
    });

    // Start pipeline
    auto start_time = std::chrono::steady_clock::now();
    auto job = pipeline->start("streaming_test");
    auto result = job.get();
    auto end_time = std::chrono::steady_clock::now();

    // Verify results
    EXPECT_EQ(result.status, core::JobStatus::Completed);
    EXPECT_EQ(result.processed_records, 50000);
    EXPECT_EQ(result.error_records, 0);

    // Calculate throughput
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double records_per_second = (result.processed_records * 1000.0) / duration.count();

    // Should process at least 5000 records/second for streaming
    EXPECT_GT(records_per_second, 5000);

    // Verify output file exists and has correct size
    auto output_file = test_data_dir_ / "vital_signs_output.csv";
    EXPECT_TRUE(std::filesystem::exists(output_file));
}

// Test batch recovery and checkpointing
TEST_F(BatchWorkflowIntegrationTest, TestBatchRecoveryAndCheckpointing) {
    // Create job with checkpointing enabled
    core::JobConfig config;
    config.job_id = "checkpoint_test_job";
    config.job_name = "Checkpoint Test";
    config.enable_checkpointing = true;
    config.checkpoint_interval = 500; // Checkpoint every 500 records
    config.parameters["input_file"] = (test_data_dir_ / "batch_1.csv").string();

    // Submit job
    auto job_id = job_manager_->submitJob(config);
    auto job = job_manager_->getJob(job_id);
    ASSERT_NE(job, nullptr);

    // Wait for job to start processing
    while (job->getStatus() != core::JobStatus::Running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    // Let it process some records
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Simulate failure by canceling the job
    job_manager_->cancelJob(job_id);

    // Wait for cancellation
    while (job->getStatus() != core::JobStatus::Cancelled) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    auto partial_stats = job->getStatistics();
    EXPECT_GT(partial_stats.successful_records, 0);
    EXPECT_LT(partial_stats.successful_records, 2000); // Less than total

    // Verify checkpoint was saved
    EXPECT_TRUE(job->saveCheckpoint());

    // Create new job to resume from checkpoint
    core::JobConfig resume_config = config;
    resume_config.job_id = "checkpoint_resume_job";
    resume_config.job_name = "Checkpoint Resume";

    auto resume_job_id = job_manager_->submitJob(resume_config);
    auto resume_job = job_manager_->getJob(resume_job_id);
    ASSERT_NE(resume_job, nullptr);

    // Load checkpoint
    EXPECT_TRUE(resume_job->loadCheckpoint());

    // Wait for completion
    while (resume_job->getStatus() != core::JobStatus::Completed &&
           resume_job->getStatus() != core::JobStatus::Failed) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    auto final_stats = resume_job->getStatistics();
    EXPECT_EQ(resume_job->getStatus(), core::JobStatus::Completed);

    // Total records processed should equal the file size
    EXPECT_EQ(partial_stats.successful_records + final_stats.successful_records, 2000);
}

// Test concurrent batch processing
TEST_F(BatchWorkflowIntegrationTest, TestConcurrentBatchProcessing) {
    // Set max concurrent jobs
    job_manager_->setMaxConcurrentJobs(3);

    // Submit multiple jobs
    std::vector<std::string> job_ids;

    for (int i = 1; i <= 5; ++i) {
        core::JobConfig config;
        config.job_id = std::format("concurrent_job_{}", i);
        config.job_name = std::format("Concurrent Job {}", i);
        config.priority = static_cast<core::JobPriority>(i % 3); // Varying priorities
        config.parameters["input_file"] = (test_data_dir_ / std::format("batch_{}.csv", i)).string();

        auto job_id = job_manager_->submitJob(config);
        job_ids.push_back(job_id);
    }

    // Monitor concurrent execution
    std::map<std::string, std::chrono::system_clock::time_point> start_times;
    std::map<std::string, std::chrono::system_clock::time_point> end_times;

    // Track job execution
    bool all_completed = false;
    while (!all_completed) {
        all_completed = true;
        size_t running_count = 0;

        for (const auto& job_id : job_ids) {
            auto job = job_manager_->getJob(job_id);
            if (job) {
                auto status = job->getStatus();

                if (status == core::JobStatus::Running) {
                    running_count++;
                    if (start_times.find(job_id) == start_times.end()) {
                        start_times[job_id] = job->getStartTime();
                    }
                } else if (status == core::JobStatus::Completed ||
                          status == core::JobStatus::Failed) {
                    if (end_times.find(job_id) == end_times.end()) {
                        end_times[job_id] = job->getEndTime();
                    }
                } else {
                    all_completed = false;
                }
            }
        }

        // Verify concurrent execution limit
        EXPECT_LE(running_count, 3);

        if (!all_completed) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }

    // Verify all jobs completed
    for (const auto& job_id : job_ids) {
        auto job = job_manager_->getJob(job_id);
        ASSERT_NE(job, nullptr);
        EXPECT_EQ(job->getStatus(), core::JobStatus::Completed);
    }

    // Verify priority-based execution
    // Higher priority jobs should generally start earlier
    auto high_priority_job = job_manager_->getJob("concurrent_job_3"); // CRITICAL priority
    auto low_priority_job = job_manager_->getJob("concurrent_job_1");  // LOW priority

    if (high_priority_job && low_priority_job) {
        // High priority should start before or at the same time as low priority
        EXPECT_LE(high_priority_job->getStartTime(), low_priority_job->getStartTime());
    }
}

// Test batch error handling and recovery
TEST_F(BatchWorkflowIntegrationTest, TestBatchErrorHandlingAndRecovery) {
    // Create a file with some invalid records
    std::ofstream error_file(test_data_dir_ / "error_test.csv");
    error_file << "person_id,value,status\n";
    error_file << "1,100,valid\n";
    error_file << "invalid_id,200,valid\n"; // Invalid person_id
    error_file << "3,invalid_value,valid\n"; // Invalid numeric value
    error_file << "4,400,valid\n";
    error_file << "5,500,error\n"; // Business rule violation
    error_file.close();

    // Configure job with retry logic
    core::JobConfig config;
    config.job_id = "error_handling_job";
    config.job_name = "Error Handling Test";
    config.max_retries = 2;
    config.retry_delay = std::chrono::seconds(1);
    config.parameters["input_file"] = (test_data_dir_ / "error_test.csv").string();
    config.parameters["stop_on_error"] = "false";
    config.parameters["error_threshold"] = "0.5"; // Allow up to 50% errors

    // Submit job
    auto job_id = job_manager_->submitJob(config);

    // Wait for completion
    auto job = job_manager_->getJob(job_id);
    while (job && job->getStatus() != core::JobStatus::Completed &&
           job->getStatus() != core::JobStatus::Failed) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Verify partial success
    ASSERT_NE(job, nullptr);
    EXPECT_EQ(job->getStatus(), core::JobStatus::Completed); // Should complete despite errors

    auto stats = job->getStatistics();
    EXPECT_EQ(stats.total_records_processed, 5);
    EXPECT_EQ(stats.successful_records, 2); // Only valid records
    EXPECT_EQ(stats.failed_records, 3); // Invalid records

    // Test retry for failed job
    core::JobConfig retry_config = config;
    retry_config.job_id = "retry_test_job";
    retry_config.parameters["error_threshold"] = "0.1"; // Strict threshold

    auto retry_job_id = job_manager_->submitJob(retry_config);
    auto retry_job = job_manager_->getJob(retry_job_id);

    // Wait for initial failure
    while (retry_job && retry_job->getStatus() == core::JobStatus::Running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    ASSERT_NE(retry_job, nullptr);

    // Job should fail due to high error rate
    if (retry_job->getStatus() == core::JobStatus::Failed && retry_job->canRetry()) {
        // Retry the job
        EXPECT_TRUE(job_manager_->retryJob(retry_job_id));

        // Wait for retry completion
        while (retry_job->getStatus() == core::JobStatus::Running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // Verify retry count increased
        EXPECT_GT(retry_job->getRetryCount(), 0);
    }
}

// Test scheduled batch workflows
TEST_F(BatchWorkflowIntegrationTest, TestScheduledBatchWorkflows) {
    // Create ETL service
    auto etl_service = std::make_shared<service::ETLService>(config_manager_, pipeline_manager_);
    auto etl_scheduler = std::make_unique<service::ETLScheduler>(etl_service);

    etl_scheduler->start();

    // Schedule immediate job
    service::ETLJobRequest immediate_job;
    immediate_job.name = "Immediate Batch Job";
    immediate_job.source_table = "batch_1.csv";
    immediate_job.target_table = "measurement";
    immediate_job.extractor_config["filepath"] = (test_data_dir_ / "batch_1.csv").string();

    service::ETLScheduler::Schedule immediate_schedule;
    immediate_schedule.type = service::ETLScheduler::ScheduleType::Once;
    immediate_schedule.start_time = std::chrono::system_clock::now();

    etl_scheduler->schedule_job("immediate_job", immediate_job, immediate_schedule);

    // Schedule future job
    service::ETLJobRequest future_job;
    future_job.name = "Future Batch Job";
    future_job.source_table = "batch_2.csv";
    future_job.target_table = "measurement";
    future_job.extractor_config["filepath"] = (test_data_dir_ / "batch_2.csv").string();

    service::ETLScheduler::Schedule future_schedule;
    future_schedule.type = service::ETLScheduler::ScheduleType::Once;
    future_schedule.start_time = std::chrono::system_clock::now() + std::chrono::seconds(2);

    etl_scheduler->schedule_job("future_job", future_job, future_schedule);

    // Wait for jobs to execute
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Verify scheduled jobs
    auto scheduled_jobs = etl_scheduler->get_scheduled_jobs();
    EXPECT_GE(scheduled_jobs.size(), 2);

    // Stop scheduler
    etl_scheduler->stop();

    // Verify job results
    auto immediate_result = etl_service->get_job_result("immediate_job");
    EXPECT_TRUE(immediate_result.has_value());
    if (immediate_result.has_value()) {
        EXPECT_EQ(immediate_result->status, core::JobStatus::Completed);
    }

    auto future_result = etl_service->get_job_result("future_job");
    EXPECT_TRUE(future_result.has_value());
    if (future_result.has_value()) {
        EXPECT_EQ(future_result->status, core::JobStatus::Completed);
    }
}

} // namespace omop::test::integration