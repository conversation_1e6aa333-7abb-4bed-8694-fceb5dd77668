// tests/integration/core/test_interfaces_integration.cpp
// Tests core interfaces including ProcessingContext, component factories and encoding functionality

#include <gtest/gtest.h>
#include "core/interfaces.h"
#include "core/component_factory.h"
#include "core/encoding.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <memory>
#include <sstream>
#include <thread>
#include <chrono>
#include <iomanip>
#include <locale>
#include <iostream>

namespace omop::core::test {

class InterfacesIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for date/time formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::exception&) {
            // Fallback if UK locale not available
            std::locale::global(std::locale("C"));
        }
    }

    void TearDown() override {
        // Reset to default locale
        std::locale::global(std::locale("C"));
    }
};

// Tests ProcessingContext creation and basic stage/job ID management operations
TEST_F(InterfacesIntegrationTest, ProcessingContextBasicOperations) {
    ProcessingContext context;
    
    // Test stage management
    EXPECT_EQ(context.current_stage(), ProcessingContext::Stage::Extract);
    
    context.set_stage(ProcessingContext::Stage::Transform);
    EXPECT_EQ(context.current_stage(), ProcessingContext::Stage::Transform);
    
    // Test job ID management
    context.set_job_id("test-job-001");
    EXPECT_EQ(context.job_id(), "test-job-001");
}

// Tests ProcessingContext with UK currency formatting and NHS-specific data handling
TEST_F(InterfacesIntegrationTest, ProcessingContextUKCurrencyData) {
    ProcessingContext context;
    
    // Set UK healthcare context
    context.set_job_id("uk-nhs-job-001");
    context.set_stage(ProcessingContext::Stage::Transform);
    
    // Store UK currency data
    context.set_data("prescription_cost", 15.50); // £15.50
    context.set_data("currency_symbol", std::string("£"));
    context.set_data("nhs_number", std::string("************"));
    
    // Retrieve and verify UK healthcare data
    auto cost = context.get_data("prescription_cost");
    auto currency = context.get_data("currency_symbol");
    auto nhs_num = context.get_data("nhs_number");
    
    EXPECT_TRUE(cost.has_value());
    EXPECT_TRUE(currency.has_value());
    EXPECT_TRUE(nhs_num.has_value());
    
    // Verify UK currency formatting
    if (cost && currency) {
        auto cost_val = std::any_cast<double>(*cost);
        auto currency_sym = std::any_cast<std::string>(*currency);
        EXPECT_DOUBLE_EQ(cost_val, 15.50);
        EXPECT_EQ(currency_sym, "£");
    }
}

// Tests ProcessingContext with UK date/time formatting and regional standards
TEST_F(InterfacesIntegrationTest, ProcessingContextUKDateTimeHandling) {
    ProcessingContext context;
    
    // Set UK locale-specific time data
    auto uk_time = std::chrono::system_clock::now();
    context.set_data("appointment_time", uk_time);
    context.set_data("date_format", std::string("DD/MM/YYYY"));
    context.set_data("time_format", std::string("HH:MM"));
    context.set_data("timezone", std::string("GMT"));
    
    // Test elapsed time calculation
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    auto elapsed = context.elapsed_time();
    EXPECT_GT(elapsed.count(), 0.0);
    
    // Verify UK date format storage
    auto date_fmt = context.get_data("date_format");
    EXPECT_TRUE(date_fmt.has_value());
    if (date_fmt) {
        EXPECT_EQ(std::any_cast<std::string>(*date_fmt), "DD/MM/YYYY");
    }
}

// Tests ProcessingContext error tracking with UK healthcare quality thresholds
TEST_F(InterfacesIntegrationTest, ProcessingContextErrorTracking) {
    ProcessingContext context;
    
    // Set error threshold
    context.set_error_threshold(0.05); // 5% error threshold
    
    // Add exactly 10 processed and 1 error (10% error rate)
    context.increment_processed(10);
    context.increment_errors(1);
    
    EXPECT_EQ(context.processed_count(), 10);
    EXPECT_EQ(context.error_count(), 1);
    
    // 1/10 = 0.1 = 10% which is > 5%, so should be true
    // But let's check manually first
    double rate = static_cast<double>(context.error_count()) / context.processed_count();
    EXPECT_GT(rate, 0.05); // Verify our math: 0.1 > 0.05
    
    // Now test the actual method
    EXPECT_TRUE(context.is_error_threshold_exceeded());
}

// Tests component factory initialization and registration of UK healthcare components
TEST_F(InterfacesIntegrationTest, ComponentFactoryInitialization) {
    // Initialize factories
    initialize_component_factories();
    
    auto& extractor_factory = get_extractor_factory();
    auto& transformer_factory = get_transformer_factory();
    auto& loader_factory = get_loader_factory();
    
    // Verify factories are initialized
    EXPECT_GT(extractor_factory.registered_count(), 0);
    EXPECT_GT(transformer_factory.registered_count(), 0);
    EXPECT_GT(loader_factory.registered_count(), 0);
    
    // Check for placeholder components
    EXPECT_TRUE(extractor_factory.is_registered("placeholder"));
    EXPECT_TRUE(transformer_factory.is_registered("placeholder"));
}

// Tests component creation through factory methods for UK OMOP components
TEST_F(InterfacesIntegrationTest, ComponentCreation) {
    initialize_component_factories();
    
    std::unordered_map<std::string, std::any> config;
    
    // Create placeholder components
    auto extractor = create_extractor("placeholder", config);
    auto transformer = create_transformer("placeholder", config);
    
    ASSERT_NE(extractor, nullptr);
    ASSERT_NE(transformer, nullptr);
    
    EXPECT_EQ(extractor->get_type(), "placeholder");
    EXPECT_EQ(transformer->get_type(), "placeholder");
}

// Tests concurrent access to ProcessingContext with UK NHS data processing
TEST_F(InterfacesIntegrationTest, ProcessingContextConcurrentAccess) {
    ProcessingContext context;
    context.set_job_id("uk-concurrent-job");
    
    const size_t num_threads = 10;
    const size_t operations_per_thread = 100;
    std::vector<std::thread> threads;
    
    // Launch UK NHS data processing threads
    for (size_t i = 0; i < num_threads; ++i) {
        threads.emplace_back([&context, operations_per_thread, i]() {
            for (size_t j = 0; j < operations_per_thread; ++j) {
                // Simulate UK NHS record processing
                context.increment_processed();
                if (j % 10 == 0) {
                    context.increment_errors();
                }
                
                // Store UK-specific data
                std::string key = "thread_" + std::to_string(i) + "_data_" + std::to_string(j);
                context.set_data(key, static_cast<int>(i * 1000 + j));
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(context.processed_count(), num_threads * operations_per_thread);
    EXPECT_EQ(context.error_count(), num_threads * (operations_per_thread / 10));
}

// Tests text encoder normalization with UK-specific characters and medical terminology
TEST_F(InterfacesIntegrationTest, TextEncoderNormalizationUK) {
    auto encoder = create_text_encoder();
    encoder->initialize(get_default_encoding_config());
    
    // Test text with UK-specific characters that need normalization
    std::string uk_text = "The café in Münich costs £15.50";
    
    // Test normalization
    std::string normalized = encoder->normalize_text(uk_text, "NFC");
    EXPECT_FALSE(normalized.empty());
    
    // Test cleaning invalid characters
    std::string text_with_invalid = "Valid text\xFF\xFEInvalid bytes";
    std::string cleaned = encoder->clean_invalid_chars(text_with_invalid, '?');
    
    EXPECT_NE(cleaned.find("Valid text"), std::string::npos);
    EXPECT_EQ(cleaned.find('\xFF'), std::string::npos);
}

// Tests ProcessingContext with comprehensive UK localization (NHS numbers, postcodes, currency, temperature)
TEST_F(InterfacesIntegrationTest, ProcessingContextUKLocalization) {
    ProcessingContext context;
    
    // Set UK healthcare context
    context.set_job_id("uk-nhs-job-001");
    context.set_stage(ProcessingContext::Stage::Transform);
    
    // Store comprehensive UK localized data
    context.set_data("amount_gbp", 15.50); // £15.50 prescription cost
    context.set_data("currency_symbol", std::string("£"));
    context.set_data("postal_code", std::string("SW1A 1AA")); // UK postcode format
    context.set_data("temperature_celsius", 36.7); // Body temperature in Celsius
    context.set_data("nhs_number", std::string("************")); // Valid NHS number format
    context.set_data("phone_number", std::string("+44 20 7946 0958")); // UK phone format
    
    // Retrieve and verify all UK localized data
    auto amount = context.get_data("amount_gbp");
    auto currency = context.get_data("currency_symbol");
    auto postcode = context.get_data("postal_code");
    auto temperature = context.get_data("temperature_celsius");
    auto nhs_num = context.get_data("nhs_number");
    auto phone = context.get_data("phone_number");
    
    // Verify all data exists
    EXPECT_TRUE(amount.has_value());
    EXPECT_TRUE(currency.has_value());
    EXPECT_TRUE(postcode.has_value());
    EXPECT_TRUE(temperature.has_value());
    EXPECT_TRUE(nhs_num.has_value());
    EXPECT_TRUE(phone.has_value());
    
    // Verify UK currency formatting
    if (amount && currency) {
        auto amount_val = std::any_cast<double>(*amount);
        auto currency_sym = std::any_cast<std::string>(*currency);
        EXPECT_DOUBLE_EQ(amount_val, 15.50);
        EXPECT_EQ(currency_sym, "£");
    }
    
    // Verify UK postcode format
    if (postcode) {
        auto postcode_val = std::any_cast<std::string>(*postcode);
        EXPECT_EQ(postcode_val, "SW1A 1AA");
        // Verify UK postcode pattern (letter-number pattern)
        EXPECT_TRUE(postcode_val.size() >= 6 && postcode_val.size() <= 8);
    }
    
    // Verify temperature in Celsius (UK standard)
    if (temperature) {
        auto temp_val = std::any_cast<double>(*temperature);
        EXPECT_DOUBLE_EQ(temp_val, 36.7);
        EXPECT_GE(temp_val, 35.0); // Normal body temp range in Celsius
        EXPECT_LE(temp_val, 42.0);
    }
    
    // Verify NHS number format
    if (nhs_num) {
        auto nhs_val = std::any_cast<std::string>(*nhs_num);
        EXPECT_EQ(nhs_val, "************");
        // Check NHS number format (10 digits with spaces)
        std::string nhs_digits_only = nhs_val;
        nhs_digits_only.erase(std::remove(nhs_digits_only.begin(), nhs_digits_only.end(), ' '), nhs_digits_only.end());
        EXPECT_EQ(nhs_digits_only.length(), 10);
    }
}

// Tests text encoder with UK-specific character encoding and medical terminology
TEST_F(InterfacesIntegrationTest, TextEncoderUKEncoding) {
    auto encoder = create_text_encoder();
    encoder->initialize(get_default_encoding_config());
    
    // Test UK medical terminology with special characters
    std::string uk_medical_text = "Patient's GP in Röntgen clinic - prescription cost £25.75";
    
    // Test UTF-8 encoding preservation for UK characters
    EXPECT_FALSE(uk_medical_text.empty());
    EXPECT_NE(uk_medical_text.find("£"), std::string::npos);
    EXPECT_NE(uk_medical_text.find("ö"), std::string::npos);
    
    // Test text normalization for UK healthcare data
    std::string normalized = encoder->normalize_text(uk_medical_text, "NFC");
    EXPECT_FALSE(normalized.empty());
    EXPECT_NE(normalized.find("£"), std::string::npos);
    
    // Test UK postcode encoding
    std::string postcode_text = "Patient address: London SW1A 1AA, UK";
    std::string normalized_postcode = encoder->normalize_text(postcode_text, "NFC");
    EXPECT_NE(normalized_postcode.find("SW1A 1AA"), std::string::npos);
    
    // Test cleaning with UK currency symbols
    std::string text_with_invalid = "Valid prescription £15.50\xFF\xFEInvalid bytes";
    std::string cleaned = encoder->clean_invalid_chars(text_with_invalid, '?');
    
    EXPECT_NE(cleaned.find("£15.50"), std::string::npos);
    EXPECT_EQ(cleaned.find('\xFF'), std::string::npos);
    EXPECT_EQ(cleaned.find('\xFE'), std::string::npos);
}

// Tests component factory error handling for unknown UK healthcare component types
TEST_F(InterfacesIntegrationTest, ComponentFactoryErrorHandling) {
    initialize_component_factories();
    
    std::unordered_map<std::string, std::any> config;
    
    // Test creating non-existent component types
    EXPECT_THROW(create_extractor("nonexistent", config), common::ConfigurationException);
    EXPECT_THROW(create_transformer("invalid_type", config), common::ConfigurationException);
    EXPECT_THROW(create_loader("missing_type", config), common::ConfigurationException);
}

// Tests validation result with UK-specific validation rules for NHS data
TEST_F(InterfacesIntegrationTest, ValidationResultUKRules) {
    omop::common::ValidationResult result;
    
    // Test UK postcode validation
    result.add_error("postcode", "Invalid UK postcode format", "postcode_format");
    result.add_warning("phone", "Phone number should be in UK format (+44)", "phone_format");
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 1);
    EXPECT_EQ(result.warnings().size(), 1);
    
    auto errors = result.errors();
    EXPECT_EQ(errors.size(), 1);
    EXPECT_EQ(errors[0].field_name, "postcode");
    EXPECT_EQ(errors[0].rule_name, "postcode_format");
}

} // namespace omop::core::test