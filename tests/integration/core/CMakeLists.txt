# Core integration tests
set(CORE_INTEGRATION_TEST_SOURCES
    test_job_manager_integration.cpp
    test_job_scheduler_integration.cpp
    test_pipeline_integration.cpp
    test_record_batch_integration.cpp
    test_interfaces_integration.cpp
    test_component_factory_integration.cpp
    test_uk_localized_pipeline_integration.cpp
)

add_executable(core_integration_tests ${CORE_INTEGRATION_TEST_SOURCES})

target_link_libraries(core_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(core_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME core_integration_tests
    COMMAND core_integration_tests
)

set_tests_properties(core_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;core"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)