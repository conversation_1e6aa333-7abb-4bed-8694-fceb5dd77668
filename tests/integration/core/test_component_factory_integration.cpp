// tests/integration/core/test_component_factory_integration.cpp
// Tests component factory registration, creation and lifecycle management with comprehensive coverage

#include <gtest/gtest.h>
#include "core/component_factory.h"
#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <memory>
#include <thread>
#include <chrono>

namespace omop::core::test {

// Mock implementations for testing
class TestExtractor : public IExtractor {
public:
    void initialize([[maybe_unused]] const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] ProcessingContext& context) override {}
    RecordBatch extract_batch([[maybe_unused]] size_t batch_size, [[maybe_unused]] ProcessingContext& context) override {
        return RecordBatch();
    }
    bool has_more_data() const override { return false; }
    std::string get_type() const override { return "test_extractor"; }
    void finalize([[maybe_unused]] ProcessingContext& context) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override { return {}; }
};

class TestTransformer : public ITransformer {
public:
    void initialize([[maybe_unused]] const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] ProcessingContext& context) override {}
    std::optional<Record> transform(const Record& record, [[maybe_unused]] ProcessingContext& context) override {
        return record;
    }
    RecordBatch transform_batch(const RecordBatch& batch, [[maybe_unused]] ProcessingContext& context) override {
        return batch;
    }
    std::string get_type() const override { return "test_transformer"; }
    omop::common::ValidationResult validate([[maybe_unused]] const Record& record) const override {
        return omop::common::ValidationResult();
    }
    std::unordered_map<std::string, std::any> get_statistics() const override { return {}; }
};

class TestLoader : public ILoader {
public:
    void initialize([[maybe_unused]] const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] ProcessingContext& context) override {}
    bool load([[maybe_unused]] const Record& record, [[maybe_unused]] ProcessingContext& context) override { return true; }
    size_t load_batch(const RecordBatch& batch, [[maybe_unused]] ProcessingContext& context) override {
        return batch.size();
    }
    void commit([[maybe_unused]] ProcessingContext& context) override {}
    void rollback([[maybe_unused]] ProcessingContext& context) override {}
    std::string get_type() const override { return "test_loader"; }
    void finalize([[maybe_unused]] ProcessingContext& context) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override { return {}; }
};

class ComponentFactoryIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize component factories
        initialize_component_factories();
    }

    void TearDown() override {
        // Cleanup if needed
    }
};

// Test factory initialization produces working factories
TEST_F(ComponentFactoryIntegrationTest, FactoryInitialization) {
    auto& extractor_factory = get_extractor_factory();
    auto& transformer_factory = get_transformer_factory();
    [[maybe_unused]] auto& loader_factory = get_loader_factory();
    
    // Verify factories have placeholder components registered
    EXPECT_GT(extractor_factory.registered_count(), 0);
    EXPECT_GT(transformer_factory.registered_count(), 0);
    EXPECT_TRUE(extractor_factory.is_registered("placeholder"));
    EXPECT_TRUE(transformer_factory.is_registered("placeholder"));
}

// Test custom component registration and creation
TEST_F(ComponentFactoryIntegrationTest, CustomComponentRegistration) {
    // Register custom components
    register_extractor_type("test_extractor", []() {
        return std::make_unique<TestExtractor>();
    });
    
    register_transformer_type("test_transformer", []() {
        return std::make_unique<TestTransformer>();
    });
    
    register_loader_type("test_loader", []() {
        return std::make_unique<TestLoader>();
    });
    
    // Verify registration
    auto extractor_types = get_registered_extractor_types();
    auto transformer_types = get_registered_transformer_types();
    auto loader_types = get_registered_loader_types();
    
    EXPECT_NE(std::find(extractor_types.begin(), extractor_types.end(), "test_extractor"), 
              extractor_types.end());
    EXPECT_NE(std::find(transformer_types.begin(), transformer_types.end(), "test_transformer"), 
              transformer_types.end());
    EXPECT_NE(std::find(loader_types.begin(), loader_types.end(), "test_loader"), 
              loader_types.end());
}

// Test component creation through factory functions
TEST_F(ComponentFactoryIntegrationTest, ComponentCreation) {
    // Register test components first
    register_extractor_type("test_extractor", []() {
        return std::make_unique<TestExtractor>();
    });
    
    register_transformer_type("test_transformer", []() {
        return std::make_unique<TestTransformer>();
    });
    
    register_loader_type("test_loader", []() {
        return std::make_unique<TestLoader>();
    });
    
    std::unordered_map<std::string, std::any> config;
    
    // Create components
    auto extractor = create_extractor("test_extractor", config);
    auto transformer = create_transformer("test_transformer", config);
    auto loader = create_loader("test_loader", config);
    
    ASSERT_NE(extractor, nullptr);
    ASSERT_NE(transformer, nullptr);
    ASSERT_NE(loader, nullptr);
    
    EXPECT_EQ(extractor->get_type(), "test_extractor");
    EXPECT_EQ(transformer->get_type(), "test_transformer");
    EXPECT_EQ(loader->get_type(), "test_loader");
}

// Test factory error handling for unknown component types
TEST_F(ComponentFactoryIntegrationTest, FactoryErrorHandling) {
    std::unordered_map<std::string, std::any> config;
    
    // Test creation of unknown component types
    EXPECT_THROW(create_extractor("unknown_extractor", config), 
                 common::ConfigurationException);
    EXPECT_THROW(create_transformer("unknown_transformer", config), 
                 common::ConfigurationException);
    EXPECT_THROW(create_loader("unknown_loader", config), 
                 common::ConfigurationException);
}

// Test concurrent component creation from factories
TEST_F(ComponentFactoryIntegrationTest, ConcurrentComponentCreation) {
    // Register test component
    register_extractor_type("concurrent_extractor", []() {
        return std::make_unique<TestExtractor>();
    });
    
    const size_t num_threads = 10;
    const size_t components_per_thread = 100;
    std::atomic<size_t> created_count{0};
    std::atomic<bool> error_occurred{false};
    
    std::vector<std::thread> threads;
    
    for (size_t t = 0; t < num_threads; ++t) {
        threads.emplace_back([&created_count, &error_occurred, components_per_thread]() {
            try {
                for (size_t i = 0; i < components_per_thread; ++i) {
                    std::unordered_map<std::string, std::any> config;
                    auto extractor = create_extractor("concurrent_extractor", config);
                    if (extractor) {
                        created_count++;
                    }
                }
            } catch (const std::exception&) {
                error_occurred = true;
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_FALSE(error_occurred);
    EXPECT_EQ(created_count, num_threads * components_per_thread);
}

// Test factory registration with duplicate types
TEST_F(ComponentFactoryIntegrationTest, DuplicateTypeRegistration) {
    // Register a component type
    register_extractor_type("duplicate_test", []() {
        return std::make_unique<TestExtractor>();
    });
    
    // Register the same type again (should overwrite)
    register_extractor_type("duplicate_test", []() {
        return std::make_unique<TestExtractor>();
    });
    
    // Should still be able to create the component
    std::unordered_map<std::string, std::any> config;
    auto extractor = create_extractor("duplicate_test", config);
    EXPECT_NE(extractor, nullptr);
}

// Test component factory with complex configuration
TEST_F(ComponentFactoryIntegrationTest, ComplexConfigurationHandling) {
    // Register configurable component
    register_transformer_type("configurable_transformer", []() {
        return std::make_unique<TestTransformer>();
    });
    
    // Create complex configuration
    std::unordered_map<std::string, std::any> config;
    config["batch_size"] = size_t(1000);
    config["enable_validation"] = true;
    config["error_threshold"] = 0.05;
    config["output_table"] = std::string("person");
    
    // Create component with configuration
    auto transformer = create_transformer("configurable_transformer", config);
    ASSERT_NE(transformer, nullptr);
    
    // Test initialization with config
    ProcessingContext context;
    EXPECT_NO_THROW(transformer->initialize(config, context));
}

// Test factory component lifecycle management
TEST_F(ComponentFactoryIntegrationTest, ComponentLifecycleManagement) {
    register_loader_type("lifecycle_loader", []() {
        return std::make_unique<TestLoader>();
    });
    
    std::unordered_map<std::string, std::any> config;
    auto loader = create_loader("lifecycle_loader", config);
    ASSERT_NE(loader, nullptr);
    
    ProcessingContext context;
    
    // Test full lifecycle
    EXPECT_NO_THROW(loader->initialize(config, context));
    
    // Test operations
    Record test_record;
    test_record.setField("id", 123);
    EXPECT_TRUE(loader->load(test_record, context));
    
    RecordBatch test_batch;
    test_batch.addRecord(test_record);
    EXPECT_GT(loader->load_batch(test_batch, context), 0);
    
    EXPECT_NO_THROW(loader->commit(context));
    EXPECT_NO_THROW(loader->finalize(context));
}

// Test factory performance with large numbers of components
TEST_F(ComponentFactoryIntegrationTest, FactoryPerformance) {
    register_extractor_type("performance_extractor", []() {
        return std::make_unique<TestExtractor>();
    });
    
    const size_t num_components = 10000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::vector<std::unique_ptr<IExtractor>> extractors;
    extractors.reserve(num_components);
    
    for (size_t i = 0; i < num_components; ++i) {
        std::unordered_map<std::string, std::any> config;
        extractors.push_back(create_extractor("performance_extractor", config));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "Created " << num_components << " components in " 
              << duration.count() << " ms" << std::endl;
    std::cout << "Rate: " << (num_components * 1000.0 / duration.count()) 
              << " components/second" << std::endl;
    
    EXPECT_EQ(extractors.size(), num_components);
    
    // Verify all components are functional
    for (const auto& extractor : extractors) {
        EXPECT_NE(extractor, nullptr);
        EXPECT_EQ(extractor->get_type(), "test_extractor"); // Use the actual type being created
    }
}

// Test factory with empty and null configurations
TEST_F(ComponentFactoryIntegrationTest, EmptyConfigurationHandling) {
    register_transformer_type("empty_config_transformer", []() {
        return std::make_unique<TestTransformer>();
    });
    
    // Test with empty configuration
    std::unordered_map<std::string, std::any> empty_config;
    auto transformer = create_transformer("empty_config_transformer", empty_config);
    ASSERT_NE(transformer, nullptr);
    
    ProcessingContext context;
    EXPECT_NO_THROW(transformer->initialize(empty_config, context));
}

// Test factory registration validation
TEST_F(ComponentFactoryIntegrationTest, RegistrationValidation) {
    auto& factory = get_extractor_factory();
    
    size_t initial_count = factory.registered_count();
    
    // Register new type
    register_extractor_type("validation_extractor", []() {
        return std::make_unique<TestExtractor>();
    });
    
    EXPECT_EQ(factory.registered_count(), initial_count + 1);
    EXPECT_TRUE(factory.is_registered("validation_extractor"));
    
    auto registered_types = get_registered_extractor_types();
    EXPECT_NE(std::find(registered_types.begin(), registered_types.end(), "validation_extractor"),
              registered_types.end());
}

} // namespace omop::core::test