#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <chrono>

#include "gtest/gtest.h"
#include "gmock/gmock.h"

#include "common/configuration.h"
#include "core/interfaces.h"
#include "etl/etl_service.h"
#include "security/auth_manager.h"
#include "monitoring/metrics_collector.h"

namespace omop::test {

/**
 * @brief Test environment configuration
 */
struct TestEnvironmentConfig {
    std::string test_data_path{"test_data"};
    std::string temp_dir_path{"temp"};
    std::string database_url{"sqlite://test.db"};
    bool use_in_memory_db{true};
    bool enable_logging{false};
    bool enable_metrics{false};
    bool enable_security{false};
    std::chrono::seconds test_timeout{30};
    size_t max_test_records{1000};
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Test database configuration
 */
struct TestDatabaseConfig {
    std::string connection_string;
    std::string schema_name{"test_omop"};
    bool auto_create_schema{true};
    bool auto_cleanup{true};
    std::vector<std::string> test_tables;
    std::vector<std::string> setup_scripts;
    std::vector<std::string> cleanup_scripts;
};

/**
 * @brief Integration test environment
 * 
 * This class provides a comprehensive test environment for integration tests,
 * including database setup, service mocking, and test data management.
 */
class IntegrationTestEnvironment : public ::testing::Environment {
public:
    IntegrationTestEnvironment() = default;
    explicit IntegrationTestEnvironment(const TestEnvironmentConfig& config);
    ~IntegrationTestEnvironment() override = default;

    /**
     * @brief Set up test environment
     */
    void SetUp() override;

    /**
     * @brief Tear down test environment
     */
    void TearDown() override;

    /**
     * @brief Get test environment configuration
     * @return TestEnvironmentConfig Current configuration
     */
    const TestEnvironmentConfig& get_config() const { return config_; }

    /**
     * @brief Update test environment configuration
     * @param config New configuration
     */
    void update_config(const TestEnvironmentConfig& config) { config_ = config; }

    /**
     * @brief Get test database configuration
     * @return TestDatabaseConfig Database configuration
     */
    TestDatabaseConfig get_database_config() const;

    /**
     * @brief Create test database connection
     * @return std::string Database connection string
     */
    std::string create_test_database();

    /**
     * @brief Cleanup test database
     * @param connection_string Database connection string
     */
    void cleanup_test_database(const std::string& connection_string = "");

    /**
     * @brief Create temporary directory
     * @return std::string Temporary directory path
     */
    std::string create_temp_directory();

    /**
     * @brief Cleanup temporary directory
     * @param temp_path Temporary directory path
     */
    void cleanup_temp_directory(const std::string& temp_path = "");

    /**
     * @brief Get test data file path
     * @param filename Test data filename
     * @return std::string Full path to test data file
     */
    std::string get_test_data_path(const std::string& filename) const;

    /**
     * @brief Load test configuration
     * @param config_name Configuration name
     * @return omop::common::PipelineConfig Pipeline configuration
     */
    omop::common::PipelineConfig load_test_config(const std::string& config_name) const;

    /**
     * @brief Create mock ETL service
     * @return std::unique_ptr<omop::etl::IETLService> Mock ETL service
     */
    std::unique_ptr<omop::etl::IETLService> create_mock_etl_service();

    /**
     * @brief Create mock authentication manager
     * @return std::unique_ptr<omop::security::IAuthManager> Mock auth manager
     */
    std::unique_ptr<omop::security::IAuthManager> create_mock_auth_manager();

    /**
     * @brief Create mock metrics collector
     * @return std::unique_ptr<omop::monitoring::IMetricsCollector> Mock metrics collector
     */
    std::unique_ptr<omop::monitoring::IMetricsCollector> create_mock_metrics_collector();

    /**
     * @brief Create test processing context
     * @param job_id Job identifier
     * @return omop::core::ProcessingContext Test processing context
     */
    omop::core::ProcessingContext create_test_context(const std::string& job_id = "test_job");

    /**
     * @brief Wait for condition with timeout
     * @param condition Condition function
     * @param timeout Timeout duration
     * @param check_interval Check interval
     * @return bool True if condition met within timeout
     */
    bool wait_for_condition(
        std::function<bool()> condition,
        std::chrono::seconds timeout = std::chrono::seconds(10),
        std::chrono::milliseconds check_interval = std::chrono::milliseconds(100));

    /**
     * @brief Generate unique test identifier
     * @return std::string Unique test ID
     */
    std::string generate_test_id() const;

    /**
     * @brief Get test statistics
     * @return std::unordered_map<std::string, std::any> Test statistics
     */
    std::unordered_map<std::string, std::any> get_test_statistics() const;

    /**
     * @brief Reset test environment
     */
    void reset();

private:
    TestEnvironmentConfig config_;
    std::vector<std::string> temp_directories_;
    std::vector<std::string> test_databases_;
    std::unordered_map<std::string, std::any> test_statistics_;

    void setup_logging();
    void setup_database();
    void setup_test_data();
    void cleanup_resources();
};

/**
 * @brief Base class for integration tests
 */
class IntegrationTestBase : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;

    /**
     * @brief Get test environment
     * @return IntegrationTestEnvironment& Test environment reference
     */
    IntegrationTestEnvironment& get_environment() { return *environment_; }

    /**
     * @brief Create test database for this test
     * @return std::string Database connection string
     */
    std::string create_test_database();

    /**
     * @brief Create temporary directory for this test
     * @return std::string Temporary directory path
     */
    std::string create_temp_directory();

    /**
     * @brief Get test data file path
     * @param filename Test data filename
     * @return std::string Full path to test data file
     */
    std::string get_test_data_path(const std::string& filename) const;

    /**
     * @brief Load test configuration
     * @param config_name Configuration name
     * @return omop::common::PipelineConfig Pipeline configuration
     */
    omop::common::PipelineConfig load_test_config(const std::string& config_name) const;

    /**
     * @brief Create test processing context
     * @return omop::core::ProcessingContext Test processing context
     */
    omop::core::ProcessingContext create_test_context();

    /**
     * @brief Assert condition within timeout
     * @param condition Condition function
     * @param timeout Timeout duration
     * @param message Error message
     */
    void assert_eventually(
        std::function<bool()> condition,
        std::chrono::seconds timeout = std::chrono::seconds(5),
        const std::string& message = "Condition not met within timeout");

private:
    static IntegrationTestEnvironment* environment_;
    std::string test_database_;
    std::string temp_directory_;
};

/**
 * @brief Test fixture for ETL pipeline tests
 */
class ETLPipelineTestFixture : public IntegrationTestBase {
protected:
    void SetUp() override;
    void TearDown() override;

    /**
     * @brief Create test ETL service
     * @return std::unique_ptr<omop::etl::IETLService> ETL service
     */
    std::unique_ptr<omop::etl::IETLService> create_etl_service();

    /**
     * @brief Create test pipeline configuration
     * @param extractor_type Extractor type
     * @param loader_type Loader type
     * @return omop::common::PipelineConfig Pipeline configuration
     */
    omop::common::PipelineConfig create_pipeline_config(
        const std::string& extractor_type = "csv",
        const std::string& loader_type = "database");

    /**
     * @brief Submit test job
     * @param job_name Job name
     * @param config Pipeline configuration
     * @return std::string Job ID
     */
    std::string submit_test_job(
        const std::string& job_name,
        const omop::common::PipelineConfig& config);

    /**
     * @brief Wait for job completion
     * @param job_id Job ID
     * @param timeout Timeout duration
     * @return omop::etl::JobStatus Final job status
     */
    omop::etl::JobStatus wait_for_job_completion(
        const std::string& job_id,
        std::chrono::seconds timeout = std::chrono::seconds(30));

private:
    std::unique_ptr<omop::etl::IETLService> etl_service_;
};

/**
 * @brief Test fixture for security tests
 */
class SecurityTestFixture : public IntegrationTestBase {
protected:
    void SetUp() override;
    void TearDown() override;

    /**
     * @brief Create test authentication manager
     * @return std::unique_ptr<omop::security::IAuthManager> Auth manager
     */
    std::unique_ptr<omop::security::IAuthManager> create_auth_manager();

    /**
     * @brief Create test user
     * @param username Username
     * @param password Password
     * @param roles User roles
     * @return std::string User ID
     */
    std::string create_test_user(
        const std::string& username,
        const std::string& password,
        const std::vector<std::string>& roles = {});

    /**
     * @brief Authenticate test user
     * @param username Username
     * @param password Password
     * @return std::string Authentication token
     */
    std::string authenticate_test_user(
        const std::string& username,
        const std::string& password);

private:
    std::unique_ptr<omop::security::IAuthManager> auth_manager_;
    std::vector<std::string> test_users_;
};

/**
 * @brief Create global test environment
 * @param config Test environment configuration
 * @return IntegrationTestEnvironment* Test environment instance
 */
IntegrationTestEnvironment* create_test_environment(
    const TestEnvironmentConfig& config = TestEnvironmentConfig{});

/**
 * @brief Get default test environment configuration
 * @return TestEnvironmentConfig Default configuration
 */
TestEnvironmentConfig get_default_test_config();

/**
 * @brief Initialize test logging
 * @param log_level Log level
 */
void init_test_logging(const std::string& log_level = "INFO");

/**
 * @brief Test helper macros
 */
#define ASSERT_EVENTUALLY(condition, timeout, message) \
    assert_eventually([&]() { return (condition); }, (timeout), (message))

#define EXPECT_EVENTUALLY(condition, timeout) \
    EXPECT_TRUE(get_environment().wait_for_condition([&]() { return (condition); }, (timeout)))

} // namespace omop::test