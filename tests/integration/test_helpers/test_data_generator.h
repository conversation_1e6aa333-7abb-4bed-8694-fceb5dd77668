#include <string>
#pragma once

#include "core/record.h"
#include "cdm/omop_tables.h"
#include "extract/database_connector.h"
#include <random>
#include <chrono>
#include <vector>
#include <unordered_map>
#include <nlohmann/json.hpp>
#include <filesystem>
#include <any>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <set>
#include <atomic>
#include <format>

namespace omop::test {

// Define TestRecord as a map-like structure for test data
using TestRecord = std::unordered_map<std::string, std::string>;

struct TestCondition {
    std::string code;
    int concept_id;
    int source_concept_id;
};

struct TestMeasurement {
    std::string name;
    int concept_id;
    double value;
    int unit_concept_id;
    std::string unit;
    bool has_range;
    double range_low;
    double range_high;
    int source_concept_id;
};

struct TestDrug {
    std::string name;
    int concept_id;
    int source_concept_id;
};

struct TestLocation {
    std::string city;
    std::string state;
    std::string zip;
};

/**
 * @brief Test data generator for OMOP CDM records
 *
 * Generates realistic test data for integration testing of the ETL pipeline.
 */
class TestDataGenerator {
public:
    TestDataGenerator(unsigned int seed = std::random_device{}());
    std::vector<omop::core::Record> generate_patient_records(size_t count);
    std::vector<omop::core::Record> generate_condition_records(size_t count, const std::vector<int64_t>& patient_ids);
    std::vector<omop::core::Record> generate_condition_records(size_t count);
    std::vector<omop::core::Record> generate_measurement_records(size_t count, const std::vector<int64_t>& patient_ids);
    std::vector<omop::core::Record> generate_measurement_records(size_t count);
    std::vector<omop::core::Record> generate_drug_exposure_records(size_t count, const std::vector<int64_t>& patient_ids);
    std::vector<omop::core::Record> generate_procedure_records(size_t count, const std::vector<int64_t>& patient_ids);
    std::vector<omop::core::Record> generate_procedure_records(size_t count);
    std::vector<omop::core::Record> generate_visit_records(size_t count, const std::vector<int64_t>& patient_ids);
    std::vector<omop::core::Record> generate_visit_records(size_t count);
    std::vector<omop::core::Record> generate_observation_records(size_t count, const std::vector<int64_t>& patient_ids);
    std::vector<omop::core::Record> generate_observation_records(size_t count);
    void write_csv_file(const std::string& filename, const std::vector<omop::core::Record>& records, const std::vector<std::string>& columns);
    void write_json_file(const std::string& filename, const std::vector<omop::core::Record>& records);
    std::chrono::system_clock::time_point generate_date_in_range(std::chrono::system_clock::time_point start, std::chrono::system_clock::time_point end);
    std::pair<std::string, int32_t> generate_race();
    std::pair<std::string, int32_t> generate_ethnicity();
    TestLocation generate_location();
    std::string format_csv_value(const std::any& value);
    std::string format_json_value(const std::any& value);
    TestCondition generate_condition();
    TestMeasurement generate_measurement();
    TestDrug generate_drug();
    std::string random_name();
    std::string random_diagnosis_code();
    std::chrono::system_clock::time_point random_date_after(std::chrono::system_clock::time_point start, int min_days, int max_days);

    /**
     * @brief Generate random person record
     * @param person_id Person ID (0 for auto-generated)
     * @return Person record
     */
    omop::cdm::Person generate_person(int64_t person_id = 0) {
        if (person_id == 0) {
            person_id = next_person_id_++;
        }

        omop::cdm::Person person;
        person.person_id = person_id;
        person.gender_concept_id = random_gender_concept();
        person.year_of_birth = random_year_of_birth();
        person.month_of_birth = random_int(1, 12);
        person.day_of_birth = random_int(1, 28);
        person.race_concept_id = random_race_concept();
        person.ethnicity_concept_id = random_ethnicity_concept();
        person.person_source_value = "P" + std::to_string(person_id);
        person.gender_source_value = person.gender_concept_id == 8507 ? "M" : "F";

        return person;
    }

    /**
     * @brief Generate observation period for person
     * @param person_id Person ID
     * @param period_id Period ID (0 for auto-generated)
     * @return Observation period record
     */
    omop::cdm::ObservationPeriod generate_observation_period(
        int64_t person_id,
        int64_t period_id = 0) {
        if (period_id == 0) {
            period_id = next_observation_period_id_++;
        }

        omop::cdm::ObservationPeriod period;
        period.observation_period_id = period_id;
        period.person_id = person_id;

        // Generate random period between 2010 and 2023
        auto start_date = random_date(2010, 2020);
        auto end_date = random_date_after(start_date, 365, 3650); // 1-10 years

        period.observation_period_start_date = start_date;
        period.observation_period_end_date = end_date;
        period.period_type_concept_id = 44814724; // Period inferred from EHR

        return period;
    }

    /**
     * @brief Generate generic record with random data
     * @param num_fields Number of fields
     * @return Generic record
     */
    omop::core::Record generate_record(size_t num_fields = 10) {
        omop::core::Record record;

        for (size_t i = 0; i < num_fields; ++i) {
            std::string field_name = "field_" + std::to_string(i);

            // Generate random field types
            switch (random_int(0, 4)) {
                case 0: // Integer
                    record.setField(field_name, random_int(0, 1000));
                    break;
                case 1: // Double
                    record.setField(field_name, random_double(0.0, 100.0));
                    break;
                case 2: // String
                    record.setField(field_name, random_string(10));
                    break;
                case 3: // Date
                    record.setField(field_name, random_date(2020, 2023));
                    break;
                case 4: // Boolean
                    record.setField(field_name, random_bool());
                    break;
            }
        }

        // Set metadata
        record.getMetadataMutable().source_table = "test_source";
        record.getMetadataMutable().target_table = "test_target";
        record.getMetadataMutable().source_row_number = next_row_number_++;
        record.getMetadataMutable().extraction_time = std::chrono::system_clock::now();

        return record;
    }

    /**
     * @brief Generate batch of records
     * @param batch_size Number of records
     * @param fields_per_record Fields per record
     * @return Record batch
     */
    omop::core::RecordBatch generate_record_batch(size_t batch_size,
                                          size_t fields_per_record = 10) {
        omop::core::RecordBatch batch;
        batch.reserve(batch_size);

        for (size_t i = 0; i < batch_size; ++i) {
            batch.addRecord(generate_record(fields_per_record));
        }

        return batch;
    }

    /**
     * @brief Generate CSV data
     * @param num_rows Number of rows
     * @param columns Column definitions (name -> type)
     * @return CSV content as string
     */
    std::string generate_csv_data(
        size_t num_rows,
        const std::vector<std::pair<std::string, std::string>>& columns) {
        std::stringstream csv;

        // Header
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) csv << ",";
            csv << columns[i].first;
        }
        csv << "\n";

        // Data rows
        for (size_t row = 0; row < num_rows; ++row) {
            for (size_t col = 0; col < columns.size(); ++col) {
                if (col > 0) csv << ",";

                const auto& type = columns[col].second;
                if (type == "int") {
                    csv << random_int(1, 1000);
                } else if (type == "double") {
                    csv << std::fixed << std::setprecision(2)
                        << random_double(0.0, 100.0);
                } else if (type == "date") {
                    csv << format_date(random_date(2020, 2023));
                } else if (type == "bool") {
                    csv << (random_bool() ? "true" : "false");
                } else { // string
                    csv << "\"" << random_string(8) << "\"";
                }
            }
            csv << "\n";
        }

        return csv.str();
    }

    /**
     * @brief Generate JSON data
     * @param num_records Number of records
     * @return JSON content as string
     */
    std::string generate_json_data(size_t num_records) {
        nlohmann::json data = nlohmann::json::array();

        for (size_t i = 0; i < num_records; ++i) {
            nlohmann::json record;
            record["id"] = i + 1;
            record["name"] = random_name();
            record["age"] = random_int(18, 90);
            record["gender"] = random_bool() ? "M" : "F";
            record["date_of_birth"] = format_date(random_date(1930, 2005));
            record["is_active"] = random_bool();
            record["score"] = random_double(0.0, 100.0);

            data.push_back(record);
        }

        return data.dump(2);
    }

    /**
     * @brief Generate vocabulary mapping data
     * @param num_mappings Number of mappings
     * @return Mapping data
     */
    std::vector<std::pair<std::string, int>> generate_vocabulary_mappings(
        size_t num_mappings) {
        std::vector<std::pair<std::string, int>> mappings;

        for (size_t i = 0; i < num_mappings; ++i) {
            std::string source_value = random_diagnosis_code();
            int concept_id = random_int(1000000, 9999999);
            mappings.emplace_back(source_value, concept_id);
        }

        return mappings;
    }

    /**
     * @brief Reset generator with new seed
     * @param seed New seed value
     */
    void reset(unsigned int seed) {
        seed_ = seed;
        rng_.seed(seed);
        next_person_id_ = 1;
        next_observation_period_id_ = 1;
        next_row_number_ = 1;
    }

    int random_int(int min, int max) {
        std::uniform_int_distribution<int> dist(min, max);
        return dist(rng_);
    }

    double random_double(double min, double max) {
        std::uniform_real_distribution<double> dist(min, max);
        return dist(rng_);
    }

    bool random_bool() {
        std::uniform_int_distribution<int> dist(0, 1);
        return dist(rng_) == 1;
    }

    std::string random_string(size_t length) {
        const std::string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        std::string result;
        result.reserve(length);

        for (size_t i = 0; i < length; ++i) {
            result += chars[random_int(0, chars.size() - 1)];
        }

        return result;
    }

    std::chrono::system_clock::time_point random_date(int year_min, int year_max) {
        std::uniform_int_distribution<int> year_dist(year_min, year_max);
        std::uniform_int_distribution<int> month_dist(1, 12);
        std::uniform_int_distribution<int> day_dist(1, 28);

        int year = year_dist(rng_);
        int month = month_dist(rng_);
        int day = day_dist(rng_);

        std::tm tm = {};
        tm.tm_year = year - 1900;
        tm.tm_mon = month - 1;
        tm.tm_mday = day;
        tm.tm_hour = 12;
        tm.tm_min = 0;
        tm.tm_sec = 0;

        return std::chrono::system_clock::from_time_t(std::mktime(&tm));
    }

    std::string format_date(const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm* tm = std::localtime(&time_t);

        std::stringstream ss;
        ss << std::put_time(tm, "%Y-%m-%d");
        return ss.str();
    }

    int random_year_of_birth() {
        return random_int(1930, 2005);
    }

    int32_t random_gender_concept() {
        return random_bool() ? 8507 : 8532; // Male : Female
    }

    int32_t random_race_concept() {
        std::vector<int32_t> race_concepts = {
            8527, // White
            8516, // Black or African American
            8515, // Asian
            38003572, // Native Hawaiian or Other Pacific Islander
            8522, // American Indian or Alaska Native
            0 // Unknown
        };
        return race_concepts[random_int(0, race_concepts.size() - 1)];
    }

    int32_t random_ethnicity_concept() {
        std::vector<int32_t> ethnicity_concepts = {
            38003563, // Hispanic or Latino
            38003564, // Not Hispanic or Latino
            0 // Unknown
        };
        return ethnicity_concepts[random_int(0, ethnicity_concepts.size() - 1)];
    }

    /**
     * @brief Generate data with specific error conditions
     */
    std::vector<TestRecord> generate_error_test_data(size_t count = 100) {
        std::vector<TestRecord> records;
        std::random_device rd;
        std::mt19937 gen(rd());

        for (size_t i = 0; i < count; ++i) {
            TestRecord record;

            // Generate various error conditions
            switch (i % 10) {
                case 0: // Missing required field
                    record["patient_id"] = "";
                    break;

                case 1: // Invalid date format
                    record["birth_date"] = "not-a-date";
                    break;

                case 2: // Out of range value
                    record["age"] = std::to_string(-5);
                    break;

                case 3: // Invalid concept code
                    record["gender"] = "Unknown";
                    break;

                case 4: // Duplicate key
                    record["patient_id"] = "1001"; // Always same ID
                    break;

                case 5: // Null in non-nullable field
                    record["year_of_birth"] = "";
                    break;

                case 6: // Invalid foreign key
                    record["visit_id"] = "999999999";
                    break;

                case 7: // Data type mismatch
                    record["person_id"] = "ABC123";
                    break;

                case 8: // Constraint violation
                    record["death_date"] = "1900-01-01";
                    record["birth_date"] = "2000-01-01";
                    break;

                default: // Valid record
                    record["patient_id"] = std::to_string(2000 + i);
                    record["birth_date"] = generate_random_date(1920, 2020);
                    record["gender"] = (i % 2 == 0) ? "M" : "F";
                    break;
            }

            records.push_back(record);
        }

        return records;
    }

    /**
     * @brief Generate hierarchical condition data
     */
    std::vector<TestRecord> generate_hierarchical_conditions(
        const std::vector<TestRecord>& visits,
        size_t conditions_per_visit = 3) {

        std::vector<TestRecord> conditions;
        std::random_device rd;
        std::mt19937 gen(rd());

        // ICD10 hierarchy examples
        std::vector<std::pair<std::string, std::vector<std::string>>> icd_hierarchy = {
            {"I10", {"I10.0", "I10.1", "I10.9"}},  // Hypertension
            {"E11", {"E11.0", "E11.1", "E11.2", "E11.3", "E11.9"}},  // Diabetes
            {"J44", {"J44.0", "J44.1", "J44.9"}},  // COPD
            {"N18", {"N18.1", "N18.2", "N18.3", "N18.4", "N18.5"}}   // CKD
        };

        std::uniform_int_distribution<> hierarchy_dist(0, icd_hierarchy.size() - 1);

        for (const auto& visit : visits) {
            for (size_t i = 0; i < conditions_per_visit; ++i) {
                TestRecord condition;

                auto& hierarchy = icd_hierarchy[hierarchy_dist(gen)];
                std::uniform_int_distribution<> code_dist(0, hierarchy.second.size() - 1);

                condition["condition_id"] = generate_id();
                condition["patient_id"] = visit.at("patient_id");
                condition["visit_id"] = visit.at("visit_id");
                condition["condition_date"] = visit.at("visit_date");
                condition["icd10_code"] = hierarchy.second[code_dist(gen)];
                condition["parent_code"] = hierarchy.first;
                condition["condition_type"] = (i == 0) ? "Primary" : "Secondary";

                conditions.push_back(condition);
            }
        }

        return conditions;
    }

    /**
     * @brief Generate time-series measurement data
     */
    std::vector<TestRecord> generate_time_series_measurements(
        const std::vector<TestRecord>& patients,
        const std::string& measurement_type,
        size_t measurements_per_patient = 12) {

        std::vector<TestRecord> measurements;
        std::random_device rd;
        std::mt19937 gen(rd());

        for (const auto& patient : patients) {
            double baseline_value = 0.0;
            std::string loinc_code;

            // Set baseline and LOINC based on measurement type
            if (measurement_type == "glucose") {
                baseline_value = 100.0;
                loinc_code = "2339-0";
                std::normal_distribution<> value_dist(baseline_value, 20.0);
            } else if (measurement_type == "blood_pressure") {
                baseline_value = 120.0;
                loinc_code = "8480-6";
            } else if (measurement_type == "cholesterol") {
                baseline_value = 200.0;
                loinc_code = "2093-3";
            }

            std::normal_distribution<> value_dist(baseline_value, baseline_value * 0.1);

            // Generate time series with trend
            auto base_date = parse_date(patient.at("birth_date"));

            for (size_t i = 0; i < measurements_per_patient; ++i) {
                TestRecord measurement;

                // Add time progression
                auto measurement_date = base_date;
                measurement_date += std::chrono::days(365 * 40 + i * 30); // Start at age 40

                // Add trend and noise
                double trend = i * 0.5; // Slight upward trend
                double seasonal = std::sin(i * 3.14159 / 6) * 5.0; // Seasonal variation
                double value = value_dist(gen) + trend + seasonal;

                measurement["measurement_id"] = generate_id();
                measurement["patient_id"] = patient.at("patient_id");
                measurement["measurement_date"] = format_date(measurement_date);
                measurement["lab_code"] = loinc_code;
                measurement["value"] = std::to_string(value);
                measurement["unit"] = (measurement_type == "glucose") ? "mg/dL" : "mg/dL";
                measurement["range_low"] = std::to_string(baseline_value * 0.8);
                measurement["range_high"] = std::to_string(baseline_value * 1.2);

                measurements.push_back(measurement);
            }
        }

        return measurements;
    }

    /**
     * @brief Generate realistic drug exposure patterns
     */
    std::vector<TestRecord> generate_drug_exposures_with_patterns(
        const std::vector<TestRecord>& visits,
        [[maybe_unused]] size_t drugs_per_visit_avg = 2) {

        std::vector<TestRecord> exposures;
        std::random_device rd;
        std::mt19937 gen(rd());

        // Common drug combinations
        std::vector<std::vector<std::string>> drug_combinations = {
            {"metformin", "lisinopril", "atorvastatin"},  // Diabetes combo
            {"aspirin", "metoprolol", "atorvastatin"},    // Cardiac combo
            {"omeprazole", "metformin"},                  // GERD + Diabetes
            {"levothyroxine"},                            // Thyroid only
            {"albuterol", "fluticasone"}                  // Asthma combo
        };

        std::uniform_int_distribution<> combo_dist(0, drug_combinations.size() - 1);
        std::uniform_int_distribution<> duration_dist(30, 365);
        std::uniform_real_distribution<> adherence_dist(0.7, 1.0);

        for (const auto& visit : visits) {
            auto& combo = drug_combinations[combo_dist(gen)];

            for (const auto& drug_name : combo) {
                TestRecord exposure;

                auto start_date = parse_date(visit.at("visit_date"));
                int duration_days = duration_dist(gen);
                auto end_date = start_date + std::chrono::days(duration_days);

                exposure["drug_exposure_id"] = generate_id();
                exposure["patient_id"] = visit.at("patient_id");
                exposure["visit_id"] = visit.at("visit_id");
                exposure["drug_name"] = drug_name;
                exposure["drug_start_date"] = format_date(start_date);
                exposure["drug_end_date"] = format_date(end_date);
                exposure["days_supply"] = std::to_string(duration_days);
                exposure["quantity"] = std::to_string(duration_days * adherence_dist(gen));
                exposure["sig"] = "Take 1 tablet by mouth daily";

                exposures.push_back(exposure);
            }
        }

        return exposures;
    }

private:
    void initialize_data_pools();

    std::mt19937 rng_;
    unsigned int seed_;
    int64_t next_person_id_ = 1;
    int64_t next_observation_period_id_ = 1;
    int64_t next_row_number_ = 1;

    // Data pools
    std::vector<std::string> first_names_;
    std::vector<std::string> last_names_;
    std::vector<std::string> diagnosis_codes_;
    std::vector<std::string> drug_names_;

    int64_t start_patient_id_ = 1000;
    int64_t start_condition_id_ = 2000;
    int64_t start_measurement_id_ = 3000;
    int64_t start_procedure_id_ = 4000;
    int64_t start_visit_id_ = 5000;
    int64_t start_observation_id_ = 6000;

    // Helper methods for extended test data generation
    std::string generate_id() {
        static std::atomic<int64_t> id_counter{10000};
        return std::to_string(id_counter.fetch_add(1));
    }

    std::string generate_random_date(int start_year, int end_year) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> year_dist(start_year, end_year);
        std::uniform_int_distribution<> month_dist(1, 12);
        std::uniform_int_distribution<> day_dist(1, 28);

        return std::to_string(year_dist(gen)) + "-" +
               std::format("{:02d}", month_dist(gen)) + "-" +
               std::format("{:02d}", day_dist(gen));
    }

    std::chrono::system_clock::time_point parse_date(const std::string& date_str) {
        std::tm tm = {};
        std::istringstream ss(date_str);
        ss >> std::get_time(&tm, "%Y-%m-%d");
        return std::chrono::system_clock::from_time_t(std::mktime(&tm));
    }
};

/**
 * @brief Performance test data generator
 *
 * Generates large volumes of data for performance testing.
 */
class PerformanceTestDataGenerator : public TestDataGenerator {
public:
    PerformanceTestDataGenerator(unsigned int seed = std::random_device{}())
        : TestDataGenerator(seed) {}

    /**
     * @brief Generate large CSV file
     * @param path Output file path
     * @param num_rows Number of rows
     * @param columns Column definitions
     */
    void generate_large_csv_file(
        const std::filesystem::path& path,
        size_t num_rows,
        const std::vector<std::pair<std::string, std::string>>& columns) {
        std::ofstream file(path);

        // Write header
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) file << ",";
            file << columns[i].first;
        }
        file << "\n";

        // Write data in chunks
        const size_t chunk_size = 10000;
        for (size_t row = 0; row < num_rows; row += chunk_size) {
            size_t rows_in_chunk = std::min(chunk_size, num_rows - row);
            std::string chunk_data = generate_csv_chunk(rows_in_chunk, columns);
            file << chunk_data;
        }

        file.close();
    }

    /**
     * @brief Generate database test data
     * @param connection Database connection
     * @param num_persons Number of person records
     */
    void generate_database_test_data(
        extract::IDatabaseConnection* connection,
        size_t num_persons) {
        // Begin transaction
        connection->begin_transaction();

        try {
            // Generate persons
            for (size_t i = 0; i < num_persons; ++i) {
                auto person = generate_person();
                insert_person(connection, person);

                // Generate observation periods
                int num_periods = random_int(1, 3);
                for (int j = 0; j < num_periods; ++j) {
                    auto period = generate_observation_period(person.person_id);
                    insert_observation_period(connection, period);
                }
            }

            connection->commit();
        } catch (...) {
            connection->rollback();
            throw;
        }
    }

private:
    std::string generate_csv_chunk(
        size_t num_rows,
        const std::vector<std::pair<std::string, std::string>>& columns) {
        std::stringstream chunk;

        for (size_t row = 0; row < num_rows; ++row) {
            for (size_t col = 0; col < columns.size(); ++col) {
                if (col > 0) chunk << ",";

                const auto& type = columns[col].second;
                if (type == "int") {
                    chunk << random_int(1, 1000);
                } else if (type == "double") {
                    chunk << std::fixed << std::setprecision(2)
                          << random_double(0.0, 100.0);
                } else if (type == "date") {
                    chunk << format_date(random_date(2020, 2023));
                } else {
                    chunk << random_string(8);
                }
            }
            chunk << "\n";
        }

        return chunk.str();
    }

    void insert_person(extract::IDatabaseConnection* connection,
                      const omop::cdm::Person& person) {
        std::string sql = person.to_insert_sql();
        connection->execute_update(sql);
    }

    void insert_observation_period(extract::IDatabaseConnection* connection,
                                  const omop::cdm::ObservationPeriod& period) {
        std::string sql = period.to_insert_sql();
        connection->execute_update(sql);
    }
};

} // namespace omop::test