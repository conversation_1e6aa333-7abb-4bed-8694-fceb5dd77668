#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <random>
#include <chrono>
#include <optional>

#include "core/record.h"
#include "cdm/omop_tables.h"

namespace omop::test {

/**
 * @brief Data generation strategy enumeration
 */
enum class GenerationStrategy {
    Random,
    Sequential,
    Pattern,
    RealData,
    Synthetic
};

/**
 * @brief Field generation configuration
 */
struct FieldGenerationConfig {
    std::string field_name;
    std::string data_type;
    bool nullable{false};
    std::any min_value;
    std::any max_value;
    std::vector<std::any> possible_values;
    std::string pattern;
    std::function<std::any()> custom_generator;
    double null_probability{0.0};
};

/**
 * @brief Table generation configuration
 */
struct TableGenerationConfig {
    std::string table_name;
    size_t record_count{100};
    GenerationStrategy strategy{GenerationStrategy::Random};
    std::vector<FieldGenerationConfig> field_configs;
    std::unordered_map<std::string, std::any> constraints;
    uint32_t seed{0}; // 0 = random seed
};

/**
 * @brief Data generator configuration
 */
struct DataGeneratorConfig {
    std::string output_format{"csv"};
    std::string output_path{"test_data"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
    bool include_headers{true};
    std::string field_delimiter{","};
    std::string record_delimiter{"\n"};
    std::string null_value{"NULL"};
    uint32_t global_seed{0}; // 0 = random seed
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Test data generator interface
 * 
 * This interface defines the contract for test data generators that create
 * synthetic data for testing ETL pipelines.
 */
class IDataGenerator {
public:
    virtual ~IDataGenerator() = default;

    /**
     * @brief Initialize data generator
     * @param config Generator configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const DataGeneratorConfig& config) = 0;

    /**
     * @brief Generate records for table
     * @param table_config Table generation configuration
     * @return std::vector<omop::core::Record> Generated records
     */
    virtual std::vector<omop::core::Record> generate_records(
        const TableGenerationConfig& table_config) = 0;

    /**
     * @brief Generate CSV data
     * @param table_config Table generation configuration
     * @return std::string CSV data
     */
    virtual std::string generate_csv(const TableGenerationConfig& table_config) = 0;

    /**
     * @brief Generate JSON data
     * @param table_config Table generation configuration
     * @return std::string JSON data
     */
    virtual std::string generate_json(const TableGenerationConfig& table_config) = 0;

    /**
     * @brief Save generated data to file
     * @param table_config Table generation configuration
     * @param filename Output filename
     * @return bool True if save successful
     */
    virtual bool save_to_file(
        const TableGenerationConfig& table_config,
        const std::string& filename) = 0;

    /**
     * @brief Generate OMOP CDM person data
     * @param count Number of persons to generate
     * @return std::vector<omop::core::Record> Person records
     */
    virtual std::vector<omop::core::Record> generate_person_data(size_t count) = 0;

    /**
     * @brief Generate OMOP CDM visit occurrence data
     * @param person_count Number of persons
     * @param visits_per_person Average visits per person
     * @return std::vector<omop::core::Record> Visit occurrence records
     */
    virtual std::vector<omop::core::Record> generate_visit_occurrence_data(
        size_t person_count, double visits_per_person = 2.0) = 0;

    /**
     * @brief Generate OMOP CDM condition occurrence data
     * @param visit_count Number of visits
     * @param conditions_per_visit Average conditions per visit
     * @return std::vector<omop::core::Record> Condition occurrence records
     */
    virtual std::vector<omop::core::Record> generate_condition_occurrence_data(
        size_t visit_count, double conditions_per_visit = 1.5) = 0;

    /**
     * @brief Generate OMOP CDM drug exposure data
     * @param person_count Number of persons
     * @param exposures_per_person Average drug exposures per person
     * @return std::vector<omop::core::Record> Drug exposure records
     */
    virtual std::vector<omop::core::Record> generate_drug_exposure_data(
        size_t person_count, double exposures_per_person = 3.0) = 0;

    /**
     * @brief Generate OMOP CDM measurement data
     * @param visit_count Number of visits
     * @param measurements_per_visit Average measurements per visit
     * @return std::vector<omop::core::Record> Measurement records
     */
    virtual std::vector<omop::core::Record> generate_measurement_data(
        size_t visit_count, double measurements_per_visit = 2.5) = 0;

    /**
     * @brief Generate linked dataset (foreign key relationships)
     * @param table_configs Table configurations with relationships
     * @return std::unordered_map<std::string, std::vector<omop::core::Record>> Generated data by table
     */
    virtual std::unordered_map<std::string, std::vector<omop::core::Record>> 
    generate_linked_dataset(const std::vector<TableGenerationConfig>& table_configs) = 0;

    /**
     * @brief Get generator statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Reset generator state
     */
    virtual void reset() = 0;
};

/**
 * @brief Default data generator implementation
 */
class DataGenerator : public IDataGenerator {
public:
    DataGenerator() = default;
    ~DataGenerator() override = default;

    bool initialize(const DataGeneratorConfig& config) override;

    std::vector<omop::core::Record> generate_records(
        const TableGenerationConfig& table_config) override;

    std::string generate_csv(const TableGenerationConfig& table_config) override;
    std::string generate_json(const TableGenerationConfig& table_config) override;

    bool save_to_file(
        const TableGenerationConfig& table_config,
        const std::string& filename) override;

    std::vector<omop::core::Record> generate_person_data(size_t count) override;

    std::vector<omop::core::Record> generate_visit_occurrence_data(
        size_t person_count, double visits_per_person = 2.0) override;

    std::vector<omop::core::Record> generate_condition_occurrence_data(
        size_t visit_count, double conditions_per_visit = 1.5) override;

    std::vector<omop::core::Record> generate_drug_exposure_data(
        size_t person_count, double exposures_per_person = 3.0) override;

    std::vector<omop::core::Record> generate_measurement_data(
        size_t visit_count, double measurements_per_visit = 2.5) override;

    std::unordered_map<std::string, std::vector<omop::core::Record>> 
    generate_linked_dataset(const std::vector<TableGenerationConfig>& table_configs) override;

    std::unordered_map<std::string, std::any> get_statistics() override;
    void reset() override;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief Field value generators
 */
class FieldGenerators {
public:
    static std::function<std::any()> integer_generator(int min_val, int max_val);
    static std::function<std::any()> double_generator(double min_val, double max_val);
    static std::function<std::any()> string_generator(size_t min_length, size_t max_length);
    static std::function<std::any()> date_generator(
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date);
    static std::function<std::any()> boolean_generator(double true_probability = 0.5);
    static std::function<std::any()> enum_generator(const std::vector<std::string>& values);
    static std::function<std::any()> email_generator();
    static std::function<std::any()> phone_generator();
    static std::function<std::any()> uuid_generator();
    static std::function<std::any()> concept_id_generator();
    static std::function<std::any()> person_id_generator(size_t max_persons);
    static std::function<std::any()> visit_id_generator(size_t max_visits);
};

/**
 * @brief OMOP CDM data templates
 */
class OMOPDataTemplates {
public:
    static TableGenerationConfig person_table_config(size_t count = 100);
    static TableGenerationConfig visit_occurrence_table_config(size_t count = 200);
    static TableGenerationConfig condition_occurrence_table_config(size_t count = 300);
    static TableGenerationConfig drug_exposure_table_config(size_t count = 400);
    static TableGenerationConfig measurement_table_config(size_t count = 500);
    static TableGenerationConfig observation_table_config(size_t count = 200);
    static TableGenerationConfig death_table_config(size_t count = 10);
    static TableGenerationConfig location_table_config(size_t count = 50);
    static TableGenerationConfig care_site_table_config(size_t count = 25);
    static TableGenerationConfig provider_table_config(size_t count = 30);

    static std::vector<TableGenerationConfig> minimal_omop_dataset(size_t person_count = 100);
    static std::vector<TableGenerationConfig> full_omop_dataset(size_t person_count = 1000);
};

/**
 * @brief Test data utilities
 */
class TestDataUtils {
public:
    /**
     * @brief Create temporary CSV file
     * @param records Records to write
     * @param field_names Field names
     * @return std::string Temporary file path
     */
    static std::string create_temp_csv_file(
        const std::vector<omop::core::Record>& records,
        const std::vector<std::string>& field_names);

    /**
     * @brief Create temporary JSON file
     * @param records Records to write
     * @return std::string Temporary file path
     */
    static std::string create_temp_json_file(
        const std::vector<omop::core::Record>& records);

    /**
     * @brief Validate OMOP CDM data
     * @param table_name OMOP table name
     * @param records Records to validate
     * @return std::vector<std::string> Validation errors (empty if valid)
     */
    static std::vector<std::string> validate_omop_data(
        const std::string& table_name,
        const std::vector<omop::core::Record>& records);

    /**
     * @brief Compare record sets
     * @param expected Expected records
     * @param actual Actual records
     * @param tolerance Tolerance for numeric comparisons
     * @return bool True if record sets match
     */
    static bool compare_record_sets(
        const std::vector<omop::core::Record>& expected,
        const std::vector<omop::core::Record>& actual,
        double tolerance = 1e-6);

    /**
     * @brief Generate deterministic test data
     * @param seed Random seed
     * @param config Table configuration
     * @return std::vector<omop::core::Record> Deterministic records
     */
    static std::vector<omop::core::Record> generate_deterministic_data(
        uint32_t seed,
        const TableGenerationConfig& config);
};

/**
 * @brief Create data generator instance
 * @return std::unique_ptr<IDataGenerator> Data generator instance
 */
std::unique_ptr<IDataGenerator> create_data_generator();

/**
 * @brief Get default data generator configuration
 * @return DataGeneratorConfig Default configuration
 */
DataGeneratorConfig get_default_data_generator_config();

/**
 * @brief Helper macros for test data generation
 */
#define GENERATE_TEST_PERSONS(count) \
    omop::test::OMOPDataTemplates::person_table_config(count)

#define GENERATE_TEST_VISITS(count) \
    omop::test::OMOPDataTemplates::visit_occurrence_table_config(count)

#define GENERATE_MINIMAL_OMOP_DATASET(person_count) \
    omop::test::OMOPDataTemplates::minimal_omop_dataset(person_count)

#define CREATE_TEMP_CSV(records, fields) \
    omop::test::TestDataUtils::create_temp_csv_file(records, fields)

} // namespace omop::test