#pragma once

#include <string>
#include <gtest/gtest.h>
#include <memory>
#include <filesystem>
#include <fstream>
#include <chrono>
#include "common/configuration.h"
#include "common/logging.h"
#include "integration_test_base.h"
#include "database_connection_factory.h"
#include "extract/database_connector.h"
#include "cdm/omop_tables.h"
#include <vector>

namespace omop::test {

// Use the factory's database type and configuration
using DatabaseType = DatabaseConnectionFactory::DatabaseType;
using DatabaseConfig = DatabaseConnectionFactory::ConnectionConfig;

/**
 * @brief Database fixture for integration tests
 *
 * Provides database setup and teardown for tests that need database connectivity.
 */
class DatabaseFixture : public IntegrationTestBase {
public:
    DatabaseFixture();
    virtual ~DatabaseFixture();

    void setup(const DatabaseConfig& config);
    void teardown();
    void execute_sql_file(const std::string& file_path);
    void create_omop_tables(const std::vector<std::string>& tables);
    bool verify_table_exists(const std::string& table_name, const std::string& schema);
    size_t get_row_count(const std::string& table_name, const std::string& schema);
    void clear_table(const std::string& table_name, const std::string& schema);

    /**
     * @brief Create database connection for use by loaders
     * @return Database connection
     */
    std::unique_ptr<extract::IDatabaseConnection> create_connection() {
        return DatabaseConnectionFactory::createConnection(DatabaseType::PostgreSQL, config_);
    }

    /**
     * @brief Create clinical database connection
     * @return Database connection
     */
    std::unique_ptr<extract::IDatabaseConnection> create_clinical_connection() {
        return DatabaseConnectionFactory::createClinicalConnection();
    }

    /**
     * @brief Create OMOP database connection
     * @return Database connection
     */
    std::unique_ptr<extract::IDatabaseConnection> create_omop_connection() {
        return DatabaseConnectionFactory::createOmopConnection();
    }

    /**
     * @brief Create MySQL database connection
     * @return Database connection
     */
    std::unique_ptr<extract::IDatabaseConnection> create_mysql_connection() {
        return DatabaseConnectionFactory::createMySQLConnection();
    }

    /**
     * @brief Clean up test database
     */
    void cleanup_test_database() {
        // This is handled by the destructor and TearDown
        // but provided for compatibility with test expectations
    }

    /**
     * @brief Setup test database for integration tests
     */
    void setup_test_database() {
        // Initialize logger
        logger_ = std::make_shared<common::Logger>("DatabaseFixture");

        // Use default OMOP configuration for tests
        config_ = DatabaseConnectionFactory::getOmopConfig();

        // Create and connect to test database
        connection_ = create_connection();

        if (!connection_ || !connection_->is_connected()) {
            throw std::runtime_error("Failed to connect to test database");
        }

        logger_->debug("Connected to test database successfully");
    }

    /**
     * @brief Execute SQL query and return result set
     * @param sql SQL query to execute
     * @return Result set
     */
    std::unique_ptr<extract::IResultSet> execute_query(const std::string& sql) {
        return connection_->execute_query(sql);
    }

    std::unique_ptr<extract::IDatabaseConnection> get_connection();
    void create_test_schemas();
    void drop_test_schemas();
    void cleanup_test_data();
    std::vector<std::string> split_sql_statements(const std::string& sql);

    // Concrete implementation of TestBody for GoogleTest framework
    void TestBody() override {}

protected:
    std::string get_env_or_default(const std::string& env_var,
                                   const std::string& default_value) {
        const char* value = std::getenv(env_var.c_str());
        return value ? std::string(value) : default_value;
    }
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Set up test database connection
        setup_test_database();

        // Create test schema
        create_test_schema();
    }

    void TearDown() override {
        // Drop test schema
        if (cleanup_database_) {
            drop_test_schema();
        }

        // Disconnect
        if (connection_ && connection_->is_connected()) {
            connection_->disconnect();
        }

        IntegrationTestBase::TearDown();
    }

    /**
     * @brief Get database connection
     * @return Database connection pointer
     */
    extract::IDatabaseConnection* get_connection_ptr() {
        return connection_.get();
    }

    /**
     * @brief Execute SQL file
     * @param sql_file SQL file path
     */
    void execute_sql_file(const std::filesystem::path& sql_file) {
        auto sql_content = read_file(sql_file);
        execute_sql(sql_content);
    }

    /**
     * @brief Execute SQL statement
     * @param sql SQL statement
     */
    void execute_sql(const std::string& sql) {
        if (!connection_) {
            throw std::runtime_error("Database connection not initialized. Call setup_test_database() first.");
        }
        connection_->execute_update(sql);
    }

    /**
     * @brief Count rows in table
     * @param table_name Table name
     * @param schema Schema name
     * @return Row count
     */
    size_t count_rows(const std::string& table_name,
                     const std::string& schema = "") {
        std::string query = "SELECT COUNT(*) FROM ";
        if (!schema.empty()) {
            query += schema + ".";
        }
        query += table_name;

        auto result = execute_query(query);
        if (result->next()) {
            return std::any_cast<int64_t>(result->get_value(0));
        }
        return 0;
    }

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name
     * @return True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") {
        return connection_->table_exists(table_name,
            schema.empty() ? test_schema_ : schema);
    }

    /**
     * @brief Load test data SQL
     * @param data_file SQL file with test data
     */
    void load_test_data(const std::string& data_file);

    /**
     * @brief Create OMOP CDM tables
     */
    void create_omop_tables() {
        // Create core OMOP tables for testing
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS person (
                person_id BIGINT PRIMARY KEY,
                gender_concept_id INTEGER NOT NULL,
                year_of_birth INTEGER NOT NULL,
                month_of_birth INTEGER,
                day_of_birth INTEGER,
                birth_datetime TIMESTAMP,
                race_concept_id INTEGER NOT NULL,
                ethnicity_concept_id INTEGER NOT NULL,
                location_id INTEGER,
                provider_id INTEGER,
                care_site_id INTEGER,
                person_source_value VARCHAR(50),
                gender_source_value VARCHAR(50),
                gender_source_concept_id INTEGER,
                race_source_value VARCHAR(50),
                race_source_concept_id INTEGER,
                ethnicity_source_value VARCHAR(50),
                ethnicity_source_concept_id INTEGER
            );

            CREATE TABLE IF NOT EXISTS observation_period (
                observation_period_id BIGINT PRIMARY KEY,
                person_id BIGINT NOT NULL,
                observation_period_start_date DATE NOT NULL,
                observation_period_end_date DATE NOT NULL,
                period_type_concept_id INTEGER NOT NULL
            );

            CREATE TABLE IF NOT EXISTS concept (
                concept_id INTEGER PRIMARY KEY,
                concept_name VARCHAR(255) NOT NULL,
                domain_id VARCHAR(20) NOT NULL,
                vocabulary_id VARCHAR(20) NOT NULL,
                concept_class_id VARCHAR(20) NOT NULL,
                standard_concept VARCHAR(1),
                concept_code VARCHAR(50) NOT NULL,
                valid_start_date DATE NOT NULL,
                valid_end_date DATE NOT NULL,
                invalid_reason VARCHAR(1)
            );
        )");
    }

protected:
    /**
     * @brief Get test database connection parameters
     * @return Connection parameters
     */
    virtual extract::IDatabaseConnection::ConnectionParams get_test_db_params() {
        auto factory_params = DatabaseConnectionFactory::configToParams(config_);
        return factory_params;
    }

    // Additional member variables from cpp file
    DatabaseConfig config_;
    std::string test_source_schema_ = "test_source";
    std::string test_cdm_schema_ = "test_cdm";
    std::shared_ptr<common::Logger> logger_;
    std::string test_schema_;

private:
    void create_test_schema() {
        // Create the cdm schema if it doesn't exist
        test_schema_ = "cdm";
        
        try {
            execute_sql("CREATE SCHEMA IF NOT EXISTS " + test_schema_);
            logger_->info("Created CDM schema: {}", test_schema_);
        } catch (const std::exception& e) {
            logger_->warn("Schema might already exist: {}", e.what());
        }
        
        // Set search path to include cdm and test schemas
        execute_sql("SET search_path TO cdm, test_source, test_cdm, public");

        logger_->info("Using CDM schema: {}", test_schema_);
    }

    void drop_test_schema() {
        if (!test_schema_.empty()) {
            execute_sql("DROP SCHEMA IF EXISTS " + test_schema_ + " CASCADE");
            logger_->info("Dropped test schema: {}", test_schema_);
        }
    }

    std::unique_ptr<extract::IDatabaseConnection> connection_;
    bool cleanup_database_ = true;
};

/**
 * @brief Multi-database fixture for testing across different database types
 */
class MultiDatabaseFixture : public DatabaseFixture {
protected:
    struct DatabaseInfo {
        std::string type;
        std::unique_ptr<extract::IDatabaseConnection> connection;
        std::string schema;
    };
    std::vector<DatabaseInfo> databases_;
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Set up multiple database connections
        setup_all_databases();
    }

    void TearDown() override {
        // Clean up all databases
        for (auto& db : databases_) {
            if (db.connection && db.connection->is_connected()) {
                if (!db.schema.empty()) {
                    db.connection->execute_update(
                        "DROP SCHEMA IF EXISTS " + db.schema + " CASCADE");
                }
                db.connection->disconnect();
            }
        }

        IntegrationTestBase::TearDown();
    }

    /**
     * @brief Run test on all configured databases
     * @param test_func Test function to run
     */
    void run_on_all_databases(
        std::function<void(const std::string&, extract::IDatabaseConnection*)> test_func) {
        for (auto& db : databases_) {
            if (db.connection && db.connection->is_connected()) {
                SCOPED_TRACE("Database: " + db.type);
                test_func(db.type, db.connection.get());
            }
        }
    }

private:
    void setup_all_databases() {
        // PostgreSQL
        if (should_test_postgres()) {
            setup_postgres_database();
        }

        // MySQL
        if (should_test_mysql()) {
            setup_mysql_database();
        }

        // Add more database types as needed
    }

    void setup_postgres_database() {
#ifdef OMOP_HAS_POSTGRESQL
        DatabaseInfo db_info;
        db_info.type = "postgresql";
        db_info.connection = std::make_unique<extract::PostgreSQLConnection>();

        try {
            db_info.connection->connect(get_test_db_params());
            db_info.schema = create_test_schema_for_db(db_info.connection.get());
            databases_.push_back(std::move(db_info));
        } catch (const std::exception& e) {
            logger_->warn("Failed to setup PostgreSQL: {}", e.what());
        }
#else
        logger_->warn("PostgreSQL support not compiled in, skipping PostgreSQL tests");
#endif
    }

    void setup_mysql_database() {
        // Similar setup for MySQL when implemented
    }

    bool should_test_postgres() {
        return get_env_or_default("TEST_POSTGRES", "true") == "true";
    }

    bool should_test_mysql() {
        return get_env_or_default("TEST_MYSQL", "false") == "true";
    }

    std::string create_test_schema_for_db(extract::IDatabaseConnection* conn) {
        // Use a simple counter instead of timestamp to avoid stoull issues
        static int schema_counter = 0;
        auto schema = "test_schema_" + std::to_string(++schema_counter);
        conn->execute_update("CREATE SCHEMA IF NOT EXISTS " + schema);
        return schema;
    }
};

} // namespace omop::test