// Database fixture implementation for integration tests
#include "database_fixture.h"
#include "database_connection_factory.h"
#include <fstream>
#include <sstream>
#include <regex>

namespace omop::test {

using namespace omop::extract;

DatabaseFixture::DatabaseFixture()
    : logger_(common::Logger::get("test-database-fixture")) {
}

DatabaseFixture::~DatabaseFixture() {
    try {
        teardown();
    } catch (const std::exception& e) {
        logger_->error("Error during database fixture cleanup: {}", e.what());
    }
}

void DatabaseFixture::setup(const DatabaseConfig& config) {
    config_ = config;

    // Create database connection using factory
    try {
        connection_ = DatabaseConnectionFactory::createConnection(DatabaseType::PostgreSQL, config);
        logger_->info("Connected to test database: {}@{}:{}/{}",
            config.username, config.host, config.port, config.database);
    } catch (const std::exception& e) {
        logger_->error("Failed to connect to test database: {}", e.what());
        throw;
    }

    // Create test schemas
    create_test_schemas();
}

void DatabaseFixture::teardown() {
    if (connection_ && connection_->is_connected()) {
        try {
            cleanup_test_data();
            drop_test_schemas();
            connection_->disconnect();
            logger_->info("Test database cleanup completed");
        } catch (const std::exception& e) {
            logger_->error("Error during teardown: {}", e.what());
        }
    }
}

void DatabaseFixture::execute_sql_file(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open SQL file: " + file_path);
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string sql_content = buffer.str();

    // Split SQL statements by semicolon (simple approach)
    std::vector<std::string> statements = split_sql_statements(sql_content);

    for (const auto& statement : statements) {
        if (!statement.empty() && statement.find_first_not_of(" \t\n\r") != std::string::npos) {
            try {
                connection_->execute_update(statement);
                logger_->debug("Executed SQL statement from file: {}", file_path);
            } catch (const std::exception& e) {
                logger_->error("Failed to execute SQL statement: {}", e.what());
                throw;
            }
        }
    }
}

void DatabaseFixture::load_test_data(const std::string& data_file) {
    execute_sql_file(data_file);
    logger_->info("Loaded test data from: {}", data_file);
}

void DatabaseFixture::create_omop_tables(const std::vector<std::string>& tables) {
    for (const auto& table : tables) {
        try {
            // Check if table already exists
            if (!connection_->table_exists(table, test_cdm_schema_)) {
                std::string create_sql = cdm::OmopSchema::get_create_table_sql(table, test_cdm_schema_);
                connection_->execute_update(create_sql);
                logger_->debug("Created OMOP table: {}.{}", test_cdm_schema_, table);
            } else {
                logger_->debug("OMOP table already exists: {}.{}", test_cdm_schema_, table);
            }
        } catch (const std::exception& e) {
            logger_->warn("Failed to create or check OMOP table {}: {}", table, e.what());
        }
    }

    // Create indexes
    for (const auto& table : tables) {
        auto index_statements = cdm::OmopSchema::get_table_indexes(table, test_cdm_schema_);
        for (const auto& index_sql : index_statements) {
            try {
                connection_->execute_update(index_sql);
            } catch (const std::exception& e) {
                logger_->warn("Failed to create index: {}", e.what());
            }
        }
    }
}

bool DatabaseFixture::verify_table_exists(const std::string& table_name,
                                        const std::string& schema) {
    return connection_->table_exists(table_name, schema);
}

size_t DatabaseFixture::get_row_count(const std::string& table_name,
                                     const std::string& schema) {
    std::string qualified_table = schema.empty() ? table_name : schema + "." + table_name;
    std::string query = "SELECT COUNT(*) FROM " + qualified_table;

    auto result = connection_->execute_query(query);
    if (result->next()) {
        auto count_value = result->get_value(0);
        if (count_value.has_value()) {
            try {
                return std::any_cast<int64_t>(count_value);
            } catch (const std::bad_any_cast&) {
                return std::any_cast<int>(count_value);
            }
        }
    }
    return 0;
}

void DatabaseFixture::clear_table(const std::string& table_name,
                                 const std::string& schema) {
    std::string qualified_table = schema.empty() ? table_name : schema + "." + table_name;
    connection_->execute_update("TRUNCATE TABLE " + qualified_table + " CASCADE");
    logger_->debug("Cleared table: {}", qualified_table);
}

std::unique_ptr<IDatabaseConnection> DatabaseFixture::get_connection() {
    // Create a new connection using the factory
    return DatabaseConnectionFactory::createConnection(DatabaseType::PostgreSQL, config_);
}

void DatabaseFixture::create_test_schemas() {
    // Create test source schema
    try {
        connection_->execute_update("CREATE SCHEMA IF NOT EXISTS " + test_source_schema_);
        logger_->debug("Created test source schema: {}", test_source_schema_);
    } catch (const std::exception& e) {
        logger_->warn("Schema might already exist: {}", e.what());
    }

    // Create test CDM schema
    try {
        connection_->execute_update("CREATE SCHEMA IF NOT EXISTS " + test_cdm_schema_);
        logger_->debug("Created test CDM schema: {}", test_cdm_schema_);
    } catch (const std::exception& e) {
        logger_->warn("Schema might already exist: {}", e.what());
    }
}

void DatabaseFixture::drop_test_schemas() {
    // Drop test schemas
    try {
        connection_->execute_update("DROP SCHEMA IF EXISTS " + test_source_schema_ + " CASCADE");
        connection_->execute_update("DROP SCHEMA IF EXISTS " + test_cdm_schema_ + " CASCADE");
        logger_->debug("Dropped test schemas");
    } catch (const std::exception& e) {
        logger_->warn("Failed to drop test schemas: {}", e.what());
    }
}

void DatabaseFixture::cleanup_test_data() {
    // Get all tables in test schemas and truncate them
    std::vector<std::string> schemas = {test_source_schema_, test_cdm_schema_};

    for (const auto& schema : schemas) {
        // Use PostgreSQL query format (works for most databases)
        std::string query = "SELECT table_name FROM information_schema.tables "
                           "WHERE table_schema = '" + schema + "' AND table_type = 'BASE TABLE'";

        try {
            auto result = connection_->execute_query(query);
            std::vector<std::string> tables;

            while (result->next()) {
                auto table_name = result->get_value(0);
                if (table_name.has_value()) {
                    tables.push_back(std::any_cast<std::string>(table_name));
                }
            }

            for (const auto& table : tables) {
                clear_table(table, schema);
            }
        } catch (const std::exception& e) {
            logger_->warn("Failed to cleanup tables in schema {}: {}", schema, e.what());
        }
    }
}

std::vector<std::string> DatabaseFixture::split_sql_statements(const std::string& sql) {
    std::vector<std::string> statements;
    std::string current_statement;
    bool in_string = false;
    char string_delimiter = '\0';

    for (size_t i = 0; i < sql.length(); ++i) {
        char c = sql[i];

        // Handle string literals
        if ((c == '\'' || c == '"') && (i == 0 || sql[i-1] != '\\')) {
            if (!in_string) {
                in_string = true;
                string_delimiter = c;
            } else if (c == string_delimiter) {
                in_string = false;
            }
        }

        current_statement += c;

        // Check for statement terminator
        if (c == ';' && !in_string) {
            // Remove trailing whitespace
            current_statement.erase(current_statement.find_last_not_of(" \t\n\r") + 1);
            if (!current_statement.empty() && current_statement != ";") {
                statements.push_back(current_statement);
            }
            current_statement.clear();
        }
    }

    // Add last statement if any
    current_statement.erase(current_statement.find_last_not_of(" \t\n\r") + 1);
    if (!current_statement.empty() && current_statement != ";") {
        statements.push_back(current_statement);
    }

    return statements;
}

} // namespace omop::test