#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <mutex>

#include "gtest/gtest.h"
#include "gmock/gmock.h"

#include "etl/etl_service.h"
#include "security/auth_manager.h"
#include "security/authorization.h"
#include "security/audit_logger.h"
#include "monitoring/metrics_collector.h"

namespace omop::test {

/**
 * @brief Service registry configuration
 */
struct ServiceRegistryConfig {
    bool auto_create_mocks{true};
    bool strict_mocking{false};
    std::unordered_map<std::string, std::any> service_configs;
};

/**
 * @brief Mock service registry interface
 * 
 * This interface provides a centralized registry for mock services
 * used in integration tests.
 */
class IServiceRegistry {
public:
    virtual ~IServiceRegistry() = default;

    /**
     * @brief Register service instance
     * @param service_name Service name
     * @param service Service instance
     */
    virtual void register_service(const std::string& service_name, std::shared_ptr<void> service) = 0;

    /**
     * @brief Get service instance
     * @param service_name Service name
     * @return std::shared_ptr<void> Service instance
     */
    virtual std::shared_ptr<void> get_service(const std::string& service_name) = 0;

    /**
     * @brief Check if service is registered
     * @param service_name Service name
     * @return bool True if service is registered
     */
    virtual bool has_service(const std::string& service_name) const = 0;

    /**
     * @brief Unregister service
     * @param service_name Service name
     * @return bool True if service was unregistered
     */
    virtual bool unregister_service(const std::string& service_name) = 0;

    /**
     * @brief Clear all services
     */
    virtual void clear() = 0;

    /**
     * @brief Get all registered service names
     * @return std::vector<std::string> Service names
     */
    virtual std::vector<std::string> get_service_names() const = 0;
};

/**
 * @brief Mock ETL service
 */
class MockETLService : public omop::etl::IETLService {
public:
    MOCK_METHOD(bool, initialize, (const omop::etl::ServiceConfig& config), (override));
    MOCK_METHOD(bool, start, (), (override));
    MOCK_METHOD(bool, stop, (), (override));
    MOCK_METHOD(omop::etl::ServiceStatus, get_status, (), (const, override));
    
    MOCK_METHOD(std::string, submit_job, 
                (const std::string& job_name, const omop::common::PipelineConfig& pipeline_config), 
                (override));
    
    MOCK_METHOD(bool, cancel_job, (const std::string& job_id), (override));
    MOCK_METHOD(std::optional<omop::etl::JobInfo>, get_job_info, (const std::string& job_id), (override));
    MOCK_METHOD(std::vector<omop::etl::JobInfo>, get_all_jobs, (), (override));
    MOCK_METHOD(std::vector<omop::etl::JobInfo>, get_running_jobs, (), (override));
    
    MOCK_METHOD(omop::etl::JobStatus, wait_for_job, 
                (const std::string& job_id, std::chrono::seconds timeout), 
                (override));
    
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (override));
    MOCK_METHOD(void, register_job_callback, (std::function<void(const omop::etl::JobInfo&)> callback), (override));
    MOCK_METHOD(bool, is_healthy, (), (const, override));
    MOCK_METHOD(omop::etl::ServiceConfig, get_config, (), (const, override));
    MOCK_METHOD(bool, update_config, (const omop::etl::ServiceConfig& config), (override));
};

/**
 * @brief Mock authentication manager
 */
class MockAuthManager : public omop::security::IAuthManager {
public:
    MOCK_METHOD(bool, initialize, (const omop::security::AuthConfig& config), (override));
    
    MOCK_METHOD(std::pair<omop::security::AuthResult, std::optional<omop::security::AuthToken>>, 
                authenticate, (const omop::security::AuthCredentials& credentials), (override));
    
    MOCK_METHOD(std::pair<omop::security::AuthResult, std::optional<omop::security::UserInfo>>, 
                validate_token, (const std::string& token), (override));
    
    MOCK_METHOD(std::pair<omop::security::AuthResult, std::optional<omop::security::AuthToken>>, 
                refresh_token, (const std::string& refresh_token), (override));
    
    MOCK_METHOD(bool, revoke_token, (const std::string& token), (override));
    MOCK_METHOD(std::optional<omop::security::UserInfo>, get_user_info, (const std::string& user_id), (override));
    MOCK_METHOD(bool, create_user, (const omop::security::UserInfo& user_info, const std::string& password), (override));
    MOCK_METHOD(bool, update_user, (const omop::security::UserInfo& user_info), (override));
    MOCK_METHOD(bool, delete_user, (const std::string& user_id), (override));
    MOCK_METHOD(bool, lock_user, (const std::string& user_id), (override));
    MOCK_METHOD(bool, unlock_user, (const std::string& user_id), (override));
    
    MOCK_METHOD(bool, change_password, 
                (const std::string& user_id, const std::string& old_password, const std::string& new_password), 
                (override));
    
    MOCK_METHOD(bool, reset_password, (const std::string& user_id, const std::string& new_password), (override));
    MOCK_METHOD(std::vector<std::string>, get_active_sessions, (const std::string& user_id), (override));
    MOCK_METHOD(bool, terminate_session, (const std::string& user_id, const std::string& session_token), (override));
    MOCK_METHOD(bool, terminate_all_sessions, (const std::string& user_id), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (override));
    MOCK_METHOD(omop::security::AuthConfig, get_config, (), (const, override));
    MOCK_METHOD(bool, update_config, (const omop::security::AuthConfig& config), (override));
};

/**
 * @brief Mock authorization manager
 */
class MockAuthorizationManager : public omop::security::IAuthorizationManager {
public:
    MOCK_METHOD(bool, initialize, (const omop::security::AuthzConfig& config), (override));
    MOCK_METHOD(omop::security::AuthzResponse, authorize, (const omop::security::AuthzRequest& request), (override));
    MOCK_METHOD(bool, is_authorized, (const std::string& subject, const std::string& resource, omop::security::Action action), (override));
    MOCK_METHOD(std::vector<omop::security::Permission>, get_permissions, (const std::string& subject), (override));
    MOCK_METHOD(std::vector<omop::security::Role>, get_roles, (const std::string& subject), (override));
    MOCK_METHOD(bool, create_permission, (const omop::security::Permission& permission), (override));
    MOCK_METHOD(bool, update_permission, (const omop::security::Permission& permission), (override));
    MOCK_METHOD(bool, delete_permission, (const std::string& permission_id), (override));
    MOCK_METHOD(std::optional<omop::security::Permission>, get_permission, (const std::string& permission_id), (override));
    MOCK_METHOD(std::vector<omop::security::Permission>, get_all_permissions, (), (override));
    MOCK_METHOD(bool, create_role, (const omop::security::Role& role), (override));
    MOCK_METHOD(bool, update_role, (const omop::security::Role& role), (override));
    MOCK_METHOD(bool, delete_role, (const std::string& role_id), (override));
    MOCK_METHOD(std::optional<omop::security::Role>, get_role, (const std::string& role_id), (override));
    MOCK_METHOD(std::vector<omop::security::Role>, get_all_roles, (), (override));
    MOCK_METHOD(bool, assign_role, (const std::string& subject, const std::string& role_id), (override));
    MOCK_METHOD(bool, remove_role, (const std::string& subject, const std::string& role_id), (override));
    MOCK_METHOD(bool, grant_permission, (const std::string& subject, const std::string& permission_id), (override));
    MOCK_METHOD(bool, revoke_permission, (const std::string& subject, const std::string& permission_id), (override));
    MOCK_METHOD(bool, create_policy, (const omop::security::Policy& policy), (override));
    MOCK_METHOD(bool, update_policy, (const omop::security::Policy& policy), (override));
    MOCK_METHOD(bool, delete_policy, (const std::string& policy_id), (override));
    MOCK_METHOD(std::optional<omop::security::Policy>, get_policy, (const std::string& policy_id), (override));
    MOCK_METHOD(std::vector<omop::security::Policy>, get_all_policies, (), (override));
    MOCK_METHOD(bool, evaluate_policy, (const std::string& expression, const std::unordered_map<std::string, std::any>& context), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (override));
    MOCK_METHOD(omop::security::AuthzConfig, get_config, (), (const, override));
    MOCK_METHOD(bool, update_config, (const omop::security::AuthzConfig& config), (override));
};

/**
 * @brief Mock audit logger
 */
class MockAuditLogger : public omop::security::IAuditLogger {
public:
    MOCK_METHOD(bool, initialize, (const omop::security::AuditConfig& config), (override));
    MOCK_METHOD(bool, log_event, (const omop::security::AuditEvent& event), (override));
    
    MOCK_METHOD(bool, log_authentication, 
                (const std::string& subject, omop::security::AuditOutcome outcome, const std::string& source_ip, 
                 const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(bool, log_authorization, 
                (const std::string& subject, const std::string& resource, const std::string& action, 
                 omop::security::AuditOutcome outcome, const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(bool, log_data_access, 
                (const std::string& subject, const std::string& resource, const std::string& action, 
                 omop::security::AuditOutcome outcome, const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(bool, log_data_modification, 
                (const std::string& subject, const std::string& resource, const std::string& action, 
                 omop::security::AuditOutcome outcome, const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(bool, log_system_access, 
                (const std::string& subject, const std::string& action, omop::security::AuditOutcome outcome, 
                 const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(bool, log_config_change, 
                (const std::string& subject, const std::string& resource, const std::string& action, 
                 omop::security::AuditOutcome outcome, const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(bool, log_security_violation, 
                (const std::string& subject, const std::string& violation_type, const std::string& description, 
                 omop::security::AuditSeverity severity, const std::unordered_map<std::string, std::any>& additional_data), 
                (override));
    
    MOCK_METHOD(std::vector<omop::security::AuditEvent>, query_events, (const omop::security::AuditQuery& query), (override));
    MOCK_METHOD(size_t, get_event_count, (const omop::security::AuditQuery& query), (override));
    MOCK_METHOD(std::optional<omop::security::AuditEvent>, get_event, (const std::string& event_id), (override));
    MOCK_METHOD(size_t, delete_old_events, (const std::chrono::system_clock::time_point& older_than), (override));
    MOCK_METHOD(size_t, archive_old_events, (const std::chrono::system_clock::time_point& older_than, const std::string& archive_path), (override));
    MOCK_METHOD(bool, flush, (), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (override));
    MOCK_METHOD(omop::security::AuditConfig, get_config, (), (const, override));
    MOCK_METHOD(bool, update_config, (const omop::security::AuditConfig& config), (override));
};

/**
 * @brief Mock metrics collector
 */
class MockMetricsCollector : public omop::monitoring::IMetricsCollector {
public:
    MOCK_METHOD(bool, initialize, (const omop::monitoring::MetricsConfig& config), (override));
    MOCK_METHOD(bool, register_metric, (const omop::monitoring::MetricDefinition& definition), (override));
    
    MOCK_METHOD(bool, increment_counter, 
                (const std::string& name, double value, const std::unordered_map<std::string, std::string>& labels), 
                (override));
    
    MOCK_METHOD(bool, set_gauge, 
                (const std::string& name, double value, const std::unordered_map<std::string, std::string>& labels), 
                (override));
    
    MOCK_METHOD(bool, observe_histogram, 
                (const std::string& name, double value, const std::unordered_map<std::string, std::string>& labels), 
                (override));
    
    MOCK_METHOD(bool, observe_summary, 
                (const std::string& name, double value, const std::unordered_map<std::string, std::string>& labels), 
                (override));
    
    MOCK_METHOD(std::string, start_timer, 
                (const std::string& name, const std::unordered_map<std::string, std::string>& labels), 
                (override));
    
    MOCK_METHOD(std::optional<double>, stop_timer, (const std::string& timer_id), (override));
    MOCK_METHOD(std::optional<omop::monitoring::Metric>, get_metric, (const std::string& name), (override));
    MOCK_METHOD(std::vector<omop::monitoring::Metric>, query_metrics, (const omop::monitoring::MetricsQuery& query), (override));
    MOCK_METHOD(std::vector<std::string>, get_metric_names, (), (override));
    MOCK_METHOD(std::string, export_metrics, (const std::string& format), (override));
    MOCK_METHOD(bool, clear_metrics, (const std::string& name), (override));
    MOCK_METHOD(size_t, delete_old_data, (const std::chrono::system_clock::time_point& older_than), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (override));
    MOCK_METHOD(omop::monitoring::MetricsConfig, get_config, (), (const, override));
    MOCK_METHOD(bool, update_config, (const omop::monitoring::MetricsConfig& config), (override));
};

/**
 * @brief Mock service registry implementation
 */
class MockServiceRegistry : public IServiceRegistry {
public:
    MockServiceRegistry() = default;
    explicit MockServiceRegistry(const ServiceRegistryConfig& config);
    ~MockServiceRegistry() override = default;

    void register_service(const std::string& service_name, std::shared_ptr<void> service) override;
    std::shared_ptr<void> get_service(const std::string& service_name) override;
    bool has_service(const std::string& service_name) const override;
    bool unregister_service(const std::string& service_name) override;
    void clear() override;
    std::vector<std::string> get_service_names() const override;

    /**
     * @brief Get ETL service mock
     * @return std::shared_ptr<MockETLService> ETL service mock
     */
    std::shared_ptr<MockETLService> get_etl_service();

    /**
     * @brief Get authentication manager mock
     * @return std::shared_ptr<MockAuthManager> Auth manager mock
     */
    std::shared_ptr<MockAuthManager> get_auth_manager();

    /**
     * @brief Get authorization manager mock
     * @return std::shared_ptr<MockAuthorizationManager> Authorization manager mock
     */
    std::shared_ptr<MockAuthorizationManager> get_authorization_manager();

    /**
     * @brief Get audit logger mock
     * @return std::shared_ptr<MockAuditLogger> Audit logger mock
     */
    std::shared_ptr<MockAuditLogger> get_audit_logger();

    /**
     * @brief Get metrics collector mock
     * @return std::shared_ptr<MockMetricsCollector> Metrics collector mock
     */
    std::shared_ptr<MockMetricsCollector> get_metrics_collector();

    /**
     * @brief Setup default mock behaviors
     */
    void setup_default_behaviors();

    /**
     * @brief Reset all mocks
     */
    void reset_all_mocks();

    /**
     * @brief Verify all mock expectations
     * @return bool True if all expectations met
     */
    bool verify_all_expectations();

private:
    ServiceRegistryConfig config_;
    mutable std::mutex registry_mutex_;
    std::unordered_map<std::string, std::shared_ptr<void>> services_;

    void create_default_mocks();
    template<typename T>
    std::shared_ptr<T> get_or_create_service(const std::string& service_name);
};

/**
 * @brief Create mock service registry
 * @param config Registry configuration
 * @return std::unique_ptr<IServiceRegistry> Service registry instance
 */
std::unique_ptr<IServiceRegistry> create_mock_service_registry(
    const ServiceRegistryConfig& config = ServiceRegistryConfig{});

/**
 * @brief Get global mock service registry
 * @return IServiceRegistry& Global registry instance
 */
IServiceRegistry& get_global_mock_registry();

/**
 * @brief Reset global mock service registry
 */
void reset_global_mock_registry();

/**
 * @brief Test helper for scoped mock setup
 */
class ScopedMockSetup {
public:
    explicit ScopedMockSetup(IServiceRegistry& registry);
    ~ScopedMockSetup();

    /**
     * @brief Setup ETL service mock with default behaviors
     */
    void setup_etl_service_mock();

    /**
     * @brief Setup authentication manager mock with default behaviors
     */
    void setup_auth_manager_mock();

    /**
     * @brief Setup authorization manager mock with default behaviors
     */
    void setup_authorization_manager_mock();

    /**
     * @brief Setup audit logger mock with default behaviors
     */
    void setup_audit_logger_mock();

    /**
     * @brief Setup metrics collector mock with default behaviors
     */
    void setup_metrics_collector_mock();

private:
    IServiceRegistry& registry_;
    std::vector<std::string> registered_services_;
};

/**
 * @brief Mock helper macros
 */
#define MOCK_SERVICE_REGISTRY() omop::test::get_global_mock_registry()
#define GET_MOCK_ETL_SERVICE() std::static_pointer_cast<omop::test::MockETLService>(MOCK_SERVICE_REGISTRY().get_service("etl_service"))
#define GET_MOCK_AUTH_MANAGER() std::static_pointer_cast<omop::test::MockAuthManager>(MOCK_SERVICE_REGISTRY().get_service("auth_manager"))
#define GET_MOCK_AUTHORIZATION_MANAGER() std::static_pointer_cast<omop::test::MockAuthorizationManager>(MOCK_SERVICE_REGISTRY().get_service("authorization_manager"))
#define GET_MOCK_AUDIT_LOGGER() std::static_pointer_cast<omop::test::MockAuditLogger>(MOCK_SERVICE_REGISTRY().get_service("audit_logger"))
#define GET_MOCK_METRICS_COLLECTOR() std::static_pointer_cast<omop::test::MockMetricsCollector>(MOCK_SERVICE_REGISTRY().get_service("metrics_collector"))

} // namespace omop::test