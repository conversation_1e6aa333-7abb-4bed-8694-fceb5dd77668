#pragma once

#include <gtest/gtest.h>
#include <memory>
#include <string>
#include <filesystem>
#include <fstream>
#include <chrono>
#include "common/configuration.h"
#include "common/logging.h"

namespace omop::test {

/**
 * @brief Base class for all integration tests
 *
 * Provides common setup, teardown, and utility functions for integration testing.
 */
class IntegrationTestBase : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize logging
        common::LoggingConfig::initialize_default();
        logger_ = common::Logger::get("integration_test");

        // Create test output directory
        test_output_dir_ = std::filesystem::path(TEST_OUTPUT_DIR) / test_name_;
        std::filesystem::create_directories(test_output_dir_);

        // Set up configuration
        config_ = std::make_shared<common::ConfigurationManager>();

        // Record start time
        start_time_ = std::chrono::steady_clock::now();
    }

    void TearDown() override {
        // Log test duration
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time_);

        logger_->info("Test {} completed in {} ms",
                     test_name_, duration.count());

        // Clean up test output if specified
        if (cleanup_after_test_) {
            std::filesystem::remove_all(test_output_dir_);
        }
    }

    /**
     * @brief Get path to test data file
     * @param relative_path Path relative to test data directory
     * @return std::filesystem::path Full path to test data file
     */
    std::filesystem::path get_test_data_path(const std::string& relative_path) {
        return std::filesystem::path(TEST_DATA_DIR) / relative_path;
    }

    /**
     * @brief Get path for test output file
     * @param filename Output filename
     * @return std::filesystem::path Full path for output file
     */
    std::filesystem::path get_test_output_path(const std::string& filename) {
        return test_output_dir_ / filename;
    }

    /**
     * @brief Load test configuration from YAML file
     * @param config_file Configuration file name
     */
    void load_test_config(const std::string& config_file) {
        auto config_path = get_test_data_path("yaml") / config_file;
        config_->load_config(config_path.string());
    }

    /**
     * @brief Create temporary test file with content
     * @param filename File name
     * @param content File content
     * @return std::filesystem::path Path to created file
     */
    std::filesystem::path create_temp_file(const std::string& filename,
                                          const std::string& content) {
        auto path = test_output_dir_ / filename;
        std::ofstream file(path);
        file << content;
        file.close();
        return path;
    }

    /**
     * @brief Read file content
     * @param path File path
     * @return std::string File content
     */
    std::string read_file(const std::filesystem::path& path) {
        std::ifstream file(path);
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }

    /**
     * @brief Wait for condition with timeout
     * @param condition Condition function
     * @param timeout_ms Timeout in milliseconds
     * @return bool True if condition met within timeout
     */
    bool wait_for_condition(std::function<bool()> condition,
                           int timeout_ms = 5000) {
        auto start = std::chrono::steady_clock::now();
        while (!condition()) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - start);
            if (elapsed.count() > timeout_ms) {
                return false;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        return true;
    }

    /**
     * @brief Assert file exists
     * @param path File path
     */
    void assert_file_exists(const std::filesystem::path& path) {
        ASSERT_TRUE(std::filesystem::exists(path))
            << "File does not exist: " << path;
    }

    /**
     * @brief Assert file contains text
     * @param path File path
     * @param text Text to find
     */
    void assert_file_contains(const std::filesystem::path& path,
                             const std::string& text) {
        auto content = read_file(path);
        ASSERT_TRUE(content.find(text) != std::string::npos)
            << "File " << path << " does not contain: " << text;
    }

protected:
    std::shared_ptr<common::Logger> logger_;
    std::shared_ptr<common::ConfigurationManager> config_;
    std::filesystem::path test_output_dir_;
    std::string test_name_ = ::testing::UnitTest::GetInstance()
        ->current_test_info()->name();
    bool cleanup_after_test_ = true;
    std::chrono::steady_clock::time_point start_time_;

    /**
     * @brief Get test vocabulary path
     * @return std::string Path to test vocabulary files
     */
    std::string getTestVocabularyPath() {
        // Return the path to test vocabulary data
        // This should point to vocabulary files needed for concept mapping
        std::string vocab_path = std::string(TEST_DATA_DIR) + "/vocabulary";
        
        // Create vocabulary directory if it doesn't exist
        std::filesystem::create_directories(vocab_path);
        
        return vocab_path;
    }
};

/**
 * @brief Parameterized test base for data-driven tests
 */
template<typename T>
class ParameterizedIntegrationTest : public IntegrationTestBase,
                                    public ::testing::WithParamInterface<T> {
protected:
    T get_test_param() const { return this->GetParam(); }
};

} // namespace omop::test