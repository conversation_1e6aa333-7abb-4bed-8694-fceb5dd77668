// test_helpers/transform_test_fixture.h
// Base test fixture for transformation tests

#pragma once

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "extract/database_connector.h"
#include <memory>
#include <vector>
#include <cstdlib>

namespace omop::test {

/**
 * Base test fixture for transformation tests with common setup/teardown
 */
class TransformTestFixture : public ::testing::Test {
protected:
    struct DatabaseConfig {
        std::string host;
        int port;
        std::string database;
        std::string username;
        std::string password;
        std::string schema = "test_cdm";
    };

    void SetUp() override {
        // Initialize logger
        logger_ = common::Logger::get("transform-test");

        // Load database configuration from environment
        db_config_ = load_database_config();

        // Set up database connection
        if (!skip_database_tests()) {
            try {
                setup_database();
            } catch (const std::exception& e) {
                logger_->warn("Database setup failed: {}. Skipping database tests.", e.what());
                db_available_ = false;
            }
        }

        // Initialize configuration manager
        config_manager_ = std::make_shared<common::ConfigurationManager>();

        // Track registered transformations for cleanup
        registered_transformations_.clear();
    }

    void TearDown() override {
        // Clean up registered transformations
        cleanup_transformations();

        // Clean up vocabulary service
        if (vocabulary_service_) {
            vocabulary_service_->clear_cache();
        }

        // Clean up database
        if (db_connection_ && db_available_) {
            try {
                cleanup_database();
            } catch (const std::exception& e) {
                logger_->error("Database cleanup failed: {}", e.what());
            }
        }
    }

    // Database configuration from environment
    DatabaseConfig load_database_config() {
        DatabaseConfig config;
        config.host = std::getenv("TEST_DB_HOST") ? std::getenv("TEST_DB_HOST") : "localhost";
        
        const char* port_str = std::getenv("TEST_DB_PORT");
        config.port = port_str ? std::atoi(port_str) : 5432;
        
        config.database = std::getenv("TEST_DB_NAME") ? std::getenv("TEST_DB_NAME") : "omop_test_db";
        config.username = std::getenv("TEST_DB_USER") ? std::getenv("TEST_DB_USER") : "test_user";
        config.password = std::getenv("TEST_DB_PASS") ? std::getenv("TEST_DB_PASS") : "test_pass";
        
        const char* schema = std::getenv("TEST_DB_SCHEMA");
        if (schema) {
            config.schema = schema;
        }
        
        return config;
    }

    // Check if database tests should be skipped
    bool skip_database_tests() {
        const char* skip = std::getenv("SKIP_DB_TESTS");
        return skip && std::string(skip) == "true";
    }

    // Set up database connection and tables
    void setup_database() {
        // Create database connection
        auto conn_params = std::make_unique<extract::IDatabaseConnection::ConnectionParams>();
        conn_params->host = db_config_.host;
        conn_params->port = db_config_.port;
        conn_params->database = db_config_.database;
        conn_params->username = db_config_.username;
        conn_params->password = db_config_.password;

        // For testing, we'll use a mock connection if real DB is not available
        // In production, use appropriate database connector
        db_connection_ = create_test_connection(std::move(conn_params));
        
        if (db_connection_) {
            db_available_ = true;
            create_test_schema();
        }
    }

    // Clean up database
    void cleanup_database() {
        if (db_connection_ && db_available_) {
            try {
                db_connection_->execute_update("DROP SCHEMA IF EXISTS " + db_config_.schema + " CASCADE");
            } catch (const std::exception& e) {
                logger_->warn("Failed to drop test schema: {}", e.what());
            }
        }
    }

    // Create test schema
    void create_test_schema() {
        if (!db_connection_) return;

        try {
            db_connection_->execute_update("CREATE SCHEMA IF NOT EXISTS " + db_config_.schema);
            
            // Create basic vocabulary tables for testing
            std::string concept_table = R"(
                CREATE TABLE IF NOT EXISTS )" + db_config_.schema + R"(.concept (
                    concept_id INTEGER PRIMARY KEY,
                    concept_name VARCHAR(255),
                    domain_id VARCHAR(20),
                    vocabulary_id VARCHAR(20),
                    concept_class_id VARCHAR(20),
                    standard_concept VARCHAR(1),
                    concept_code VARCHAR(50),
                    valid_start_date DATE,
                    valid_end_date DATE
                )
            )";
            db_connection_->execute_update(concept_table);
            
        } catch (const std::exception& e) {
            logger_->error("Failed to create test schema: {}", e.what());
            throw;
        }
    }

    // Create test database connection
    std::unique_ptr<extract::IDatabaseConnection> create_test_connection(
        std::unique_ptr<extract::IDatabaseConnection::ConnectionParams> params) {
        // In real implementation, create appropriate database connection
        // For now, return nullptr if database is not available
        return nullptr;
    }

    // Initialize vocabulary service with test data
    void initialize_vocabulary_service() {
        if (!db_connection_ || !db_available_) {
            logger_->warn("Database not available, skipping vocabulary service initialization");
            return;
        }

        vocabulary_service_ = std::make_unique<transform::VocabularyService>(
            create_test_connection(nullptr));
        vocabulary_service_->initialize(1000); // Small cache for testing
    }

    // Register a transformation and track it for cleanup
    void register_transformation(const std::string& type_name,
                                std::function<std::unique_ptr<transform::FieldTransformation>()> factory) {
        transform::TransformationRegistry::instance().register_transformation(type_name, factory);
        registered_transformations_.push_back(type_name);
    }

    // Clean up registered transformations
    void cleanup_transformations() {
        // Note: This requires enhancement to TransformationRegistry
        // For now, we track but cannot unregister
        for (const auto& type : registered_transformations_) {
            logger_->debug("Would unregister transformation: {}", type);
        }
        registered_transformations_.clear();
    }

    // Create test configuration
    void create_test_configuration(const std::string& yaml_content) {
        config_manager_->load_config_from_string(yaml_content);
    }

    // Utility function to create test records
    core::Record create_test_record(const std::unordered_map<std::string, std::any>& fields) {
        core::Record record;
        for (const auto& [name, value] : fields) {
            record.setField(name, value);
        }
        return record;
    }

    // Verify transformation result
    void verify_field_value(const core::Record& record,
                           const std::string& field_name,
                           const std::any& expected_value) {
        ASSERT_TRUE(record.hasField(field_name)) 
            << "Field '" << field_name << "' not found in record";
        
        auto actual = record.getField(field_name);
        
        // Compare based on type
        if (expected_value.type() == actual.type()) {
            if (expected_value.type() == typeid(int)) {
                EXPECT_EQ(std::any_cast<int>(actual), std::any_cast<int>(expected_value));
            } else if (expected_value.type() == typeid(double)) {
                EXPECT_DOUBLE_EQ(std::any_cast<double>(actual), std::any_cast<double>(expected_value));
            } else if (expected_value.type() == typeid(std::string)) {
                EXPECT_EQ(std::any_cast<std::string>(actual), std::any_cast<std::string>(expected_value));
            }
            // Add more type comparisons as needed
        } else {
            FAIL() << "Type mismatch for field '" << field_name << "'";
        }
    }

protected:
    // Common test resources
    std::shared_ptr<common::Logger> logger_;
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::unique_ptr<extract::IDatabaseConnection> db_connection_;
    std::unique_ptr<transform::VocabularyService> vocabulary_service_;
    
    // Configuration
    DatabaseConfig db_config_;
    bool db_available_ = false;
    
    // Tracking for cleanup
    std::vector<std::string> registered_transformations_;
};

/**
 * Performance test fixture with timing utilities
 */
class TransformPerformanceTestFixture : public TransformTestFixture {
protected:
    struct PerformanceMetrics {
        double min_time_ms = std::numeric_limits<double>::max();
        double max_time_ms = 0.0;
        double avg_time_ms = 0.0;
        double total_time_ms = 0.0;
        size_t sample_count = 0;
        double throughput_per_sec = 0.0;
    };

    void SetUp() override {
        TransformTestFixture::SetUp();
        
        // Load performance thresholds from environment
        const char* threshold_str = std::getenv("PERF_THRESHOLD_MS");
        if (threshold_str) {
            perf_threshold_ms_ = std::stod(threshold_str);
        }
    }

    // Measure transformation performance
    template<typename Func>
    PerformanceMetrics measure_performance(Func func, size_t iterations) {
        PerformanceMetrics metrics;
        
        // Warm up
        for (size_t i = 0; i < std::min(iterations / 10, size_t(10)); ++i) {
            func();
        }
        
        // Measure
        for (size_t i = 0; i < iterations; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            func();
            auto end = std::chrono::high_resolution_clock::now();
            
            double duration_ms = std::chrono::duration<double, std::milli>(end - start).count();
            
            metrics.min_time_ms = std::min(metrics.min_time_ms, duration_ms);
            metrics.max_time_ms = std::max(metrics.max_time_ms, duration_ms);
            metrics.total_time_ms += duration_ms;
            metrics.sample_count++;
        }
        
        metrics.avg_time_ms = metrics.total_time_ms / metrics.sample_count;
        metrics.throughput_per_sec = (metrics.sample_count * 1000.0) / metrics.total_time_ms;
        
        return metrics;
    }

    // Check if performance meets threshold
    void assert_performance_threshold(const PerformanceMetrics& metrics,
                                     const std::string& operation_name) {
        logger_->info("Performance metrics for '{}':", operation_name);
        logger_->info("  Min: {:.2f} ms", metrics.min_time_ms);
        logger_->info("  Max: {:.2f} ms", metrics.max_time_ms);
        logger_->info("  Avg: {:.2f} ms", metrics.avg_time_ms);
        logger_->info("  Throughput: {:.0f} ops/sec", metrics.throughput_per_sec);
        
        EXPECT_LT(metrics.avg_time_ms, perf_threshold_ms_)
            << "Average time for '" << operation_name 
            << "' exceeds threshold of " << perf_threshold_ms_ << " ms";
    }

protected:
    double perf_threshold_ms_ = 100.0; // Default threshold
};

} // namespace omop::test