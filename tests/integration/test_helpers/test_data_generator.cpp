// Test data generator implementation
#include "test_data_generator.h"
#include <random>
#include <algorithm>
#include <iomanip>
#include <sstream>

namespace omop::test {

TestDataGenerator::TestDataGenerator(unsigned int seed)
    : rng_(seed), seed_(seed) {
    initialize_data_pools();
}

std::vector<core::Record> TestDataGenerator::generate_patient_records(size_t count) {
    std::vector<core::Record> records;
    records.reserve(count);

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate patient ID
        int64_t patient_id = start_patient_id_ + i;
        record.setField("patient_id", patient_id);
        record.setField("person_id", patient_id);

        // Generate birth date (age 20-80)
        auto birth_date = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(80 * 365 * 24),
            std::chrono::system_clock::now() - std::chrono::hours(20 * 365 * 24)
        );
        record.setField("birth_date", birth_date);
        record.setField("birth_datetime", birth_date);

        // Extract year, month, day
        auto time_t = std::chrono::system_clock::to_time_t(birth_date);
        std::tm* tm = std::localtime(&time_t);
        record.setField("year_of_birth", tm->tm_year + 1900);
        record.setField("month_of_birth", tm->tm_mon + 1);
        record.setField("day_of_birth", tm->tm_mday);

        // Generate gender
        std::string gender = (i % 2 == 0) ? "M" : "F";
        record.setField("gender", gender);
        record.setField("gender_concept_id", gender == "M" ? 8507 : 8532);
        record.setField("gender_source_value", gender);

        // Generate race
        auto race = generate_race();
        record.setField("race", race.first);
        record.setField("race_concept_id", race.second);
        record.setField("race_source_value", race.first);

        // Generate ethnicity
        auto ethnicity = generate_ethnicity();
        record.setField("ethnicity", ethnicity.first);
        record.setField("ethnicity_concept_id", ethnicity.second);
        record.setField("ethnicity_source_value", ethnicity.first);

        // Generate location
        auto location = generate_location();
        record.setField("city", location.city);
        record.setField("state", location.state);
        record.setField("zip", location.zip);
        record.setField("location_id", static_cast<int32_t>(i + 1000));

        // Set provider and care site
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));
        record.setField("care_site_id", static_cast<int32_t>(3000 + (i % 5)));

        records.push_back(record);
    }

    return records;
}

std::vector<core::Record> TestDataGenerator::generate_condition_records(
    size_t count, const std::vector<int64_t>& patient_ids) {

    std::vector<core::Record> records;
    records.reserve(count);

    std::uniform_int_distribution<size_t> patient_dist(0, patient_ids.size() - 1);

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate condition occurrence ID
        int64_t condition_id = start_condition_id_ + i;
        record.setField("condition_occurrence_id", condition_id);

        // Select random patient
        int64_t patient_id = patient_ids[patient_dist(rng_)];
        record.setField("person_id", patient_id);

        // Generate condition
        auto condition = generate_condition();
        record.setField("condition_source_value", condition.code);
        record.setField("condition_concept_id", condition.concept_id);
        record.setField("condition_source_concept_id", condition.source_concept_id);

        // Generate dates
        auto start_date = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(365 * 24),
            std::chrono::system_clock::now()
        );
        record.setField("condition_start_date", start_date);
        record.setField("condition_start_datetime", start_date);

        // Type concept
        record.setField("condition_type_concept_id", 32817); // EHR

        // Provider and visit
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));
        record.setField("visit_occurrence_id", static_cast<int64_t>(4000 + i));

        records.push_back(record);
    }

    return records;
}

std::vector<core::Record> TestDataGenerator::generate_condition_records(size_t count) {
    // Generate patient IDs first
    std::vector<int64_t> patient_ids;
    for (size_t i = 0; i < count; ++i) {
        patient_ids.push_back(start_patient_id_ + i);
    }
    return generate_condition_records(count, patient_ids);
}

std::vector<core::Record> TestDataGenerator::generate_measurement_records(
    size_t count, const std::vector<int64_t>& patient_ids) {

    std::vector<core::Record> records;
    records.reserve(count);

    std::uniform_int_distribution<size_t> patient_dist(0, patient_ids.size() - 1);

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate measurement ID
        int64_t measurement_id = start_measurement_id_ + i;
        record.setField("measurement_id", measurement_id);

        // Select random patient
        int64_t patient_id = patient_ids[patient_dist(rng_)];
        record.setField("person_id", patient_id);

        // Generate measurement
        auto measurement = generate_measurement();
        record.setField("measurement_source_value", measurement.name);
        record.setField("measurement_concept_id", measurement.concept_id);
        record.setField("value_as_number", measurement.value);
        record.setField("unit_concept_id", measurement.unit_concept_id);
        record.setField("unit_source_value", measurement.unit);

        // Set ranges
        if (measurement.has_range) {
            record.setField("range_low", measurement.range_low);
            record.setField("range_high", measurement.range_high);
        }

        // Generate date
        auto measurement_date = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(180 * 24),
            std::chrono::system_clock::now()
        );
        record.setField("measurement_date", measurement_date);
        record.setField("measurement_datetime", measurement_date);

        // Type concept
        record.setField("measurement_type_concept_id", 44818702); // Lab result

        // Provider and visit
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));
        record.setField("visit_occurrence_id", static_cast<int64_t>(4000 + (i % 100)));

        records.push_back(record);
    }

    return records;
}

std::vector<core::Record> TestDataGenerator::generate_measurement_records(size_t count) {
    // Generate patient IDs first
    std::vector<int64_t> patient_ids;
    for (size_t i = 0; i < count; ++i) {
        patient_ids.push_back(start_patient_id_ + i);
    }
    return generate_measurement_records(count, patient_ids);
}

std::vector<core::Record> TestDataGenerator::generate_drug_exposure_records(
    size_t count, const std::vector<int64_t>& patient_ids) {

    std::vector<core::Record> records;
    records.reserve(count);

    std::uniform_int_distribution<size_t> patient_dist(0, patient_ids.size() - 1);

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate drug exposure ID
        int64_t drug_exposure_id = 10000 + i;
        record.setField("drug_exposure_id", drug_exposure_id);

        // Select random patient
        int64_t patient_id = patient_ids[patient_dist(rng_)];
        record.setField("person_id", patient_id);

        // Generate drug
        auto drug = generate_drug();
        record.setField("drug_source_value", drug.name);
        record.setField("drug_concept_id", drug.concept_id);
        record.setField("drug_source_concept_id", drug.source_concept_id);

        // Generate dates
        auto start_date = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(365 * 24),
            std::chrono::system_clock::now()
        );
        record.setField("drug_exposure_start_date", start_date);
        record.setField("drug_exposure_start_datetime", start_date);

        // Calculate end date based on days supply
        std::uniform_int_distribution<int> days_dist(7, 90);
        int days_supply = days_dist(rng_);
        auto end_date = start_date + std::chrono::hours(days_supply * 24);
        record.setField("drug_exposure_end_date", end_date);
        record.setField("drug_exposure_end_datetime", end_date);
        record.setField("days_supply", days_supply);

        // Quantity and refills
        std::uniform_real_distribution<double> quantity_dist(30.0, 180.0);
        record.setField("quantity", quantity_dist(rng_));

        std::uniform_int_distribution<int> refill_dist(0, 5);
        record.setField("refills", refill_dist(rng_));

        // Type concept
        record.setField("drug_type_concept_id", 38000177); // Prescription written

        // Route
        record.setField("route_concept_id", 4132161); // Oral
        record.setField("route_source_value", "Oral");

        // Provider and visit
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));
        record.setField("visit_occurrence_id", static_cast<int64_t>(4000 + (i % 100)));

        records.push_back(record);
    }

    return records;
}

void TestDataGenerator::write_csv_file(const std::string& filename,
                                      const std::vector<core::Record>& records,
                                      const std::vector<std::string>& columns) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file for writing: " + filename);
    }

    // Write header
    for (size_t i = 0; i < columns.size(); ++i) {
        file << columns[i];
        if (i < columns.size() - 1) file << ",";
    }
    file << "\n";

    // Write records
    for (const auto& record : records) {
        for (size_t i = 0; i < columns.size(); ++i) {
            auto field = record.getFieldOptional(columns[i]);
            if (field.has_value()) {
                file << format_csv_value(field.value());
            }
            if (i < columns.size() - 1) file << ",";
        }
        file << "\n";
    }

    file.close();
}

void TestDataGenerator::write_json_file(const std::string& filename,
                                       const std::vector<core::Record>& records) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file for writing: " + filename);
    }

    file << "{\n  \"records\": [\n";

    for (size_t i = 0; i < records.size(); ++i) {
        file << "    {\n";

        auto field_names = records[i].getFieldNames();
        for (size_t j = 0; j < field_names.size(); ++j) {
            auto field = records[i].getFieldOptional(field_names[j]);
            if (field.has_value()) {
                file << "      \"" << field_names[j] << "\": ";
                file << format_json_value(field.value());
                if (j < field_names.size() - 1) file << ",";
                file << "\n";
            }
        }

        file << "    }";
        if (i < records.size() - 1) file << ",";
        file << "\n";
    }

    file << "  ]\n}\n";
    file.close();
}

std::chrono::system_clock::time_point TestDataGenerator::generate_date_in_range(
    std::chrono::system_clock::time_point start,
    std::chrono::system_clock::time_point end) {

    auto start_time = start.time_since_epoch().count();
    auto end_time = end.time_since_epoch().count();

    std::uniform_int_distribution<decltype(start_time)> dist(start_time, end_time);
    auto random_time = dist(rng_);

    return std::chrono::system_clock::time_point(
        std::chrono::system_clock::duration(random_time));
}

omop::test::TestCondition TestDataGenerator::generate_condition() {
    std::vector<TestCondition> conditions = {
        {"I10", 320128, 45542411},      // Essential hypertension
        {"E11.9", 201826, 45553863},    // Type 2 diabetes
        {"J45.909", 317009, 45591547},  // Asthma
        {"K21.9", 318800, 45534058},    // GERD
        {"M79.3", 4344500, 45601968},   // Myalgia
        {"F41.1", 436073, 45533050}     // Generalized anxiety disorder
    };

    std::uniform_int_distribution<size_t> dist(0, conditions.size() - 1);
    return conditions[dist(rng_)];
}

omop::test::TestMeasurement TestDataGenerator::generate_measurement() {
    std::vector<TestMeasurement> measurements = {
        {"Systolic Blood Pressure", 3004249, 0.0, 8876, "mmHg", true, 90.0, 140.0, 0},
        {"Diastolic Blood Pressure", 3012888, 0.0, 8876, "mmHg", true, 60.0, 90.0, 0},
        {"Body Temperature", 3020891, 0.0, 586323, "Cel", true, 36.0, 37.5, 0},
        {"Heart Rate", 3027018, 0.0, 8541, "beats/min", true, 60.0, 100.0, 0},
        {"Hemoglobin A1c", 3004410, 0.0, 8554, "%", true, 4.0, 6.5, 0},
        {"Total Cholesterol", 3027114, 0.0, 8840, "mg/dL", true, 0.0, 200.0, 0}
    };

    std::uniform_int_distribution<size_t> dist(0, measurements.size() - 1);
    auto measurement = measurements[dist(rng_)];

    // Generate realistic value
    std::normal_distribution<double> value_dist(
        (measurement.range_low + measurement.range_high) / 2.0,
        (measurement.range_high - measurement.range_low) / 6.0
    );
    measurement.value = value_dist(rng_);

    // Clamp to reasonable range
    measurement.value = std::max(measurement.range_low * 0.8,
                                std::min(measurement.range_high * 1.2, measurement.value));

    return measurement;
}

omop::test::TestDrug TestDataGenerator::generate_drug() {
    std::vector<TestDrug> drugs = {
        {"metformin 500mg", 1503297, 45775515},
        {"lisinopril 10mg", 1308216, 45775372},
        {"albuterol inhaler", 1196539, 45775520},
        {"omeprazole 20mg", 911735, 45775527},
        {"ibuprofen 600mg", 1177480, 45775206},
        {"sertraline 50mg", 722031, 45774935}
    };

    std::uniform_int_distribution<size_t> dist(0, drugs.size() - 1);
    return drugs[dist(rng_)];
}

std::pair<std::string, int32_t> TestDataGenerator::generate_race() {
    std::vector<std::pair<std::string, int32_t>> races = {
        {"White", 8527},
        {"Black or African American", 8516},
        {"Asian", 8515},
        {"American Indian or Alaska Native", 8657},
        {"Native Hawaiian or Other Pacific Islander", 8557}
    };

    // Weighted distribution
    std::discrete_distribution<size_t> dist({60, 20, 10, 5, 5});
    return races[dist(rng_)];
}

std::pair<std::string, int32_t> TestDataGenerator::generate_ethnicity() {
    std::uniform_real_distribution<double> dist(0.0, 1.0);
    if (dist(rng_) < 0.18) { // ~18% Hispanic
        return {"Hispanic or Latino", 38003563};
    } else {
        return {"Not Hispanic or Latino", 38003564};
    }
}

omop::test::TestLocation TestDataGenerator::generate_location() {
    std::vector<TestLocation> locations = {
        {"Boston", "MA", "02134"},
        {"Cambridge", "MA", "02139"},
        {"Somerville", "MA", "02144"},
        {"Brookline", "MA", "02446"},
        {"Newton", "MA", "02458"},
        {"Quincy", "MA", "02169"},
        {"Waltham", "MA", "02451"},
        {"Medford", "MA", "02155"}
    };

    std::uniform_int_distribution<size_t> dist(0, locations.size() - 1);
    return locations[dist(rng_)];
}

std::string TestDataGenerator::format_csv_value(const std::any& value) {
    if (!value.has_value()) {
        return "";
    }

    try {
        // Try different types
        if (value.type() == typeid(std::string)) {
            std::string str = std::any_cast<std::string>(value);
            // Quote if contains comma or quote
            if (str.find(',') != std::string::npos || str.find('"') != std::string::npos) {
                // Escape quotes
                size_t pos = 0;
                while ((pos = str.find('"', pos)) != std::string::npos) {
                    str.replace(pos, 1, "\"\"");
                    pos += 2;
                }
                return "\"" + str + "\"";
            }
            return str;
        } else if (value.type() == typeid(int)) {
            return std::to_string(std::any_cast<int>(value));
        } else if (value.type() == typeid(int32_t)) {
            return std::to_string(std::any_cast<int32_t>(value));
        } else if (value.type() == typeid(int64_t)) {
            return std::to_string(std::any_cast<int64_t>(value));
        } else if (value.type() == typeid(double)) {
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(2) << std::any_cast<double>(value);
            return oss.str();
        } else if (value.type() == typeid(bool)) {
            return std::any_cast<bool>(value) ? "true" : "false";
        } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
            auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
            auto time_t = std::chrono::system_clock::to_time_t(tp);
            std::tm* tm = std::localtime(&time_t);
            std::ostringstream oss;
            oss << std::put_time(tm, "%Y-%m-%d %H:%M:%S");
            return oss.str();
        }
    } catch (const std::bad_any_cast&) {
        // Ignore and return empty
    }

    return "";
}

std::string TestDataGenerator::format_json_value(const std::any& value) {
    if (!value.has_value()) {
        return "null";
    }

    try {
        if (value.type() == typeid(std::string)) {
            std::string str = std::any_cast<std::string>(value);
            // Escape special characters
            std::string escaped;
            for (char c : str) {
                switch (c) {
                    case '"': escaped += "\\\""; break;
                    case '\\': escaped += "\\\\"; break;
                    case '\b': escaped += "\\b"; break;
                    case '\f': escaped += "\\f"; break;
                    case '\n': escaped += "\\n"; break;
                    case '\r': escaped += "\\r"; break;
                    case '\t': escaped += "\\t"; break;
                    default: escaped += c; break;
                }
            }
            return "\"" + escaped + "\"";
        } else if (value.type() == typeid(int) ||
                   value.type() == typeid(int32_t) ||
                   value.type() == typeid(int64_t)) {
            return format_csv_value(value); // Reuse numeric formatting
        } else if (value.type() == typeid(double)) {
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(6) << std::any_cast<double>(value);
            return oss.str();
        } else if (value.type() == typeid(bool)) {
            return std::any_cast<bool>(value) ? "true" : "false";
        } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
            auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
            auto time_t = std::chrono::system_clock::to_time_t(tp);
            std::tm* tm = std::localtime(&time_t);
            std::ostringstream oss;
            oss << "\"" << std::put_time(tm, "%Y-%m-%dT%H:%M:%S") << "\"";
            return oss.str();
        }
    } catch (const std::bad_any_cast&) {
        // Ignore and return null
    }

    return "null";
}

void TestDataGenerator::initialize_data_pools() {
    // Initialize data pools for generating realistic test data
    // This method sets up various pools of data that can be randomly selected from

    // First names pool
    first_names_ = {
        "John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "James", "Maria",
        "Christopher", "Jennifer", "Daniel", "Linda", "Matthew", "Barbara", "Anthony", "Elizabeth",
        "Mark", "Michelle", "Christopher", "Lisa", "Daniel", "Amanda", "David", "Stephanie",
        "James", "Nicole", "Robert", "Jessica"
    };

    // Last names pool
    last_names_ = {
        "Smith", "Doe", "Johnson", "Wilson", "Brown", "Davis", "Miller", "Garcia", "Rodriguez", "Martinez",
        "Anderson", "Taylor", "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson",
        "Garcia", "Clark", "Robinson", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "King"
    };

    // Diagnosis codes pool
    diagnosis_codes_ = {
        "I10", "E11.9", "I25.10", "E78.5", "I50.9", "J44.9", "N18.9", "E87.5",
        "I63.9", "I48.91", "I47.1", "I42.9", "I35.1", "I34.0", "I31.9", "I27.9",
        "I26.99", "I25.5", "I25.2", "I25.1", "I21.9", "I20.9", "I16.9", "I15.9",
        "I13.2", "I12.9", "I11.9", "I10.9", "I09.9", "I08.9"
    };

    // Drug names pool
    drug_names_ = {
        "Lisinopril", "Metformin", "Atorvastatin", "Amlodipine", "Metoprolol", "Losartan", "Simvastatin",
        "Hydrochlorothiazide", "Furosemide", "Warfarin", "Aspirin", "Ibuprofen", "Acetaminophen",
        "Omeprazole", "Pantoprazole", "Lansoprazole", "Esomeprazole", "Ranitidine", "Famotidine",
        "Cimetidine", "Diltiazem", "Verapamil", "Nifedipine", "Amlodipine", "Felodipine"
    };
}

std::chrono::system_clock::time_point TestDataGenerator::random_date_after(
    std::chrono::system_clock::time_point start_date,
    int min_days, int max_days) {

    // Convert to time_t for easier manipulation
    auto start_time_t = std::chrono::system_clock::to_time_t(start_date);

    // Add random number of days
    std::uniform_int_distribution<int> days_dist(min_days, max_days);
    int days_to_add = days_dist(rng_);

    // Add days (86400 seconds per day)
    start_time_t += (days_to_add * 86400);

    // Convert back to time_point
    return std::chrono::system_clock::from_time_t(start_time_t);
}

std::vector<core::Record> TestDataGenerator::generate_procedure_records(size_t count, const std::vector<int64_t>& patient_ids) {
    std::vector<core::Record> records;
    records.reserve(count);

    std::uniform_int_distribution<size_t> patient_dist(0, patient_ids.size() - 1);
    std::uniform_int_distribution<int> procedure_dist(1000, 9999);

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate procedure occurrence ID
        int64_t procedure_id = start_procedure_id_ + i;
        record.setField("procedure_occurrence_id", procedure_id);

        // Select random patient
        int64_t patient_id = patient_ids[patient_dist(rng_)];
        record.setField("person_id", patient_id);

        // Generate procedure
        int procedure_concept_id = procedure_dist(rng_);
        record.setField("procedure_concept_id", procedure_concept_id);
        record.setField("procedure_source_value", "PROC_" + std::to_string(procedure_concept_id));
        record.setField("procedure_source_concept_id", procedure_concept_id);

        // Generate visit
        record.setField("visit_occurrence_id", static_cast<int64_t>(4000 + (i % 100)));

        // Generate dates
        auto procedure_date = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(365 * 24),
            std::chrono::system_clock::now()
        );
        record.setField("procedure_date", procedure_date);
        record.setField("procedure_datetime", procedure_date);

        // Set other fields
        record.setField("procedure_type_concept_id", 38000275); // EHR record patient status
        record.setField("modifier_concept_id", 0);
        record.setField("quantity", 1);
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));

        records.push_back(record);
    }

    return records;
}

std::vector<core::Record> TestDataGenerator::generate_procedure_records(size_t count) {
    // Generate patient IDs first
    std::vector<int64_t> patient_ids;
    for (size_t i = 0; i < count; ++i) {
        patient_ids.push_back(start_patient_id_ + i);
    }
    return generate_procedure_records(count, patient_ids);
}

std::vector<core::Record> TestDataGenerator::generate_visit_records(size_t count, const std::vector<int64_t>& patient_ids) {
    std::vector<core::Record> records;
    records.reserve(count);

    std::uniform_int_distribution<size_t> patient_dist(0, patient_ids.size() - 1);
    std::uniform_int_distribution<int> visit_type_dist(9201, 9203); // Inpatient, Outpatient, ER

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate visit occurrence ID
        int64_t visit_id = start_visit_id_ + i;
        record.setField("visit_occurrence_id", visit_id);

        // Select random patient
        int64_t patient_id = patient_ids[patient_dist(rng_)];
        record.setField("person_id", patient_id);

        // Generate visit type
        int visit_concept_id = visit_type_dist(rng_);
        record.setField("visit_concept_id", visit_concept_id);
        record.setField("visit_source_value", "VISIT_" + std::to_string(visit_id));

        // Generate dates
        auto visit_start = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(365 * 24),
            std::chrono::system_clock::now()
        );
        auto visit_end = random_date_after(visit_start, 1, 30);

        record.setField("visit_start_date", visit_start);
        record.setField("visit_start_datetime", visit_start);
        record.setField("visit_end_date", visit_end);
        record.setField("visit_end_datetime", visit_end);

        // Set other fields
        record.setField("visit_type_concept_id", 44818517); // Visit derived from EHR record
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));
        record.setField("care_site_id", static_cast<int32_t>(3000 + (i % 5)));

        records.push_back(record);
    }

    return records;
}

std::vector<core::Record> TestDataGenerator::generate_visit_records(size_t count) {
    // Generate patient IDs first
    std::vector<int64_t> patient_ids;
    for (size_t i = 0; i < count; ++i) {
        patient_ids.push_back(start_patient_id_ + i);
    }
    return generate_visit_records(count, patient_ids);
}

std::vector<core::Record> TestDataGenerator::generate_observation_records(size_t count, const std::vector<int64_t>& patient_ids) {
    std::vector<core::Record> records;
    records.reserve(count);

    std::uniform_int_distribution<size_t> patient_dist(0, patient_ids.size() - 1);
    std::uniform_int_distribution<int> observation_dist(3000, 3999);

    for (size_t i = 0; i < count; ++i) {
        core::Record record;

        // Generate observation ID
        int64_t observation_id = start_observation_id_ + i;
        record.setField("observation_id", observation_id);

        // Select random patient
        int64_t patient_id = patient_ids[patient_dist(rng_)];
        record.setField("person_id", patient_id);

        // Generate observation
        int observation_concept_id = observation_dist(rng_);
        record.setField("observation_concept_id", observation_concept_id);
        record.setField("observation_source_value", "OBS_" + std::to_string(observation_concept_id));
        record.setField("observation_source_concept_id", observation_concept_id);

        // Generate value
        std::uniform_real_distribution<double> value_dist(0.0, 100.0);
        double value = value_dist(rng_);
        record.setField("value_as_number", value);
        record.setField("value_as_string", std::to_string(value));

        // Generate dates
        auto observation_date = generate_date_in_range(
            std::chrono::system_clock::now() - std::chrono::hours(365 * 24),
            std::chrono::system_clock::now()
        );
        record.setField("observation_date", observation_date);
        record.setField("observation_datetime", observation_date);

        // Set other fields
        record.setField("observation_type_concept_id", 38000280); // Observation recorded from EHR
        record.setField("value_as_concept_id", 0);
        record.setField("qualifier_concept_id", 0);
        record.setField("unit_concept_id", 0);
        record.setField("provider_id", static_cast<int32_t>(2000 + (i % 10)));
        record.setField("visit_occurrence_id", static_cast<int64_t>(4000 + (i % 100)));

        records.push_back(record);
    }

    return records;
}

std::vector<core::Record> TestDataGenerator::generate_observation_records(size_t count) {
    // Generate patient IDs first
    std::vector<int64_t> patient_ids;
    for (size_t i = 0; i < count; ++i) {
        patient_ids.push_back(start_patient_id_ + i);
    }
    return generate_observation_records(count, patient_ids);
}

} // namespace omop::test