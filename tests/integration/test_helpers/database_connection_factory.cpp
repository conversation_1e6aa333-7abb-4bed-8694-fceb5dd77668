#include "database_connection_factory.h"
#include <cstdlib>
#include <stdexcept>

namespace omop::test {

std::shared_ptr<common::Logger> DatabaseConnectionFactory::logger_ = 
    common::Logger::get("database-connection-factory");

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createClinicalConnection() {
    auto config = getClinicalConfig();
    return createPostgreSQLConnection(config);
}

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createOmopConnection() {
    auto config = getOmopConfig();
    return createPostgreSQLConnection(config);
}

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createMySQLConnection() {
    auto config = getMySQLConfig();
    return createMySQLConnectionImpl(config);
}

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createTestConnection() {
    auto config = getTestConfig();
    return createPostgreSQLConnection(config);
}

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createConnection(
    DatabaseType type, const ConnectionConfig& config) {
    switch (type) {
        case DatabaseType::PostgreSQL:
            return createPostgreSQLConnection(config);
        case DatabaseType::MySQL:
            return createMySQLConnectionImpl(config);
        default:
            throw std::invalid_argument("Unsupported database type");
    }
}

DatabaseConnectionFactory::ConnectionConfig DatabaseConnectionFactory::getClinicalConfig() {
    return {
        .host = getEnvOrDefault("POSTGRES_HOST", "clinical-db"),
        .port = std::stoi(getEnvOrDefault("POSTGRES_PORT", "5432")),
        .database = getEnvOrDefault("POSTGRES_DB", "clinical_db"),
        .username = getEnvOrDefault("POSTGRES_USER", "clinical_user"),
        .password = getEnvOrDefault("POSTGRES_PASSWORD", "clinical_pass"),
        .connect_timeout = 30,
        .query_timeout = 60,
        .additional_options = {}
    };
}

DatabaseConnectionFactory::ConnectionConfig DatabaseConnectionFactory::getOmopConfig() {
    return {
        .host = getEnvOrDefault("OMOP_HOST", "omop-cdm-db"),
        .port = std::stoi(getEnvOrDefault("OMOP_PORT", "5432")),
        .database = getEnvOrDefault("OMOP_DB", "omop_cdm"),
        .username = getEnvOrDefault("OMOP_USER", "omop_user"),
        .password = getEnvOrDefault("OMOP_PASSWORD", "omop_pass"),
        .connect_timeout = 30,
        .query_timeout = 60,
        .additional_options = {}
    };
}

DatabaseConnectionFactory::ConnectionConfig DatabaseConnectionFactory::getMySQLConfig() {
    return {
        .host = getEnvOrDefault("MYSQL_HOST", "mysql"),
        .port = std::stoi(getEnvOrDefault("MYSQL_PORT", "3306")),
        .database = getEnvOrDefault("MYSQL_DATABASE", "test_mysql_db"),
        .username = getEnvOrDefault("MYSQL_USER", "mysql_user"),
        .password = getEnvOrDefault("MYSQL_PASSWORD", "mysql_pass"),
        .connect_timeout = 30,
        .query_timeout = 60,
        .additional_options = {}
    };
}

DatabaseConnectionFactory::ConnectionConfig DatabaseConnectionFactory::getTestConfig() {
    return {
        .host = getEnvOrDefault("TEST_DB_HOST", "clinical-db"),
        .port = std::stoi(getEnvOrDefault("TEST_DB_PORT", "5432")),
        .database = getEnvOrDefault("TEST_DB_NAME", "clinical_db"),
        .username = getEnvOrDefault("TEST_DB_USER", "clinical_user"),
        .password = getEnvOrDefault("TEST_DB_PASSWORD", "clinical_pass"),
        .connect_timeout = 30,
        .query_timeout = 60,
        .additional_options = {}
    };
}

bool DatabaseConnectionFactory::testConnection(DatabaseType type) {
    try {
        auto connection = createConnection(type, 
            type == DatabaseType::MySQL ? getMySQLConfig() : getClinicalConfig());
        return connection && connection->is_connected();
    } catch (const std::exception& e) {
        logger_->warn("Database connection test failed: {}", e.what());
        return false;
    }
}

std::string DatabaseConnectionFactory::getEnvOrDefault(const std::string& env_var, 
                                                      const std::string& default_value) {
    const char* value = std::getenv(env_var.c_str());
    return value ? std::string(value) : default_value;
}

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createPostgreSQLConnection(
    const ConnectionConfig& config) {
#ifdef OMOP_HAS_POSTGRESQL
    auto connection = std::make_unique<extract::PostgreSQLConnection>();
    auto params = configToParams(config);
    
    // Remove query_timeout from connection parameters for PostgreSQL
    // It should be set after connection via set_query_timeout method
    params.options.erase("query_timeout");
    
    try {
        connection->connect(params);
        
        // Set query timeout AFTER connection is established
        if (config.query_timeout > 0) {
            connection->set_query_timeout(config.query_timeout);
        }
        
        logger_->debug("Connected to PostgreSQL database: {}@{}:{}/{}", 
                     config.username, config.host, config.port, config.database);
        return connection;
    } catch (const std::exception& e) {
        throw common::DatabaseException(
            "Failed to connect to PostgreSQL database: " + std::string(e.what()),
            "postgresql", 0);
    }
#else
    throw std::runtime_error("PostgreSQL support not compiled in");
#endif
}

std::unique_ptr<extract::IDatabaseConnection> DatabaseConnectionFactory::createMySQLConnectionImpl(
    const ConnectionConfig& config) {
#ifdef OMOP_HAS_MYSQL
    auto connection = std::make_unique<extract::MySQLConnection>();
    auto params = configToParams(config);
    
    try {
        connection->connect(params);
        logger_->debug("Connected to MySQL database: {}@{}:{}/{}", 
                     config.username, config.host, config.port, config.database);
        return std::move(connection);
    } catch (const std::exception& e) {
        throw common::DatabaseException(
            "Failed to connect to MySQL database: " + std::string(e.what()),
            "mysql", 0);
    }
#else
    // Fallback to PostgreSQL for testing if MySQL not available
    logger_->warn("MySQL support not compiled in, falling back to PostgreSQL");
    return createPostgreSQLConnection(config);
#endif
}

extract::IDatabaseConnection::ConnectionParams DatabaseConnectionFactory::configToParams(
    const ConnectionConfig& config) {
    extract::IDatabaseConnection::ConnectionParams params;
    params.host = config.host;
    params.port = config.port;
    params.database = config.database;
    params.username = config.username;
    params.password = config.password;
    
    // Add timeout options
    params.options["connect_timeout"] = std::to_string(config.connect_timeout);
    params.options["query_timeout"] = std::to_string(config.query_timeout);
    
    // Add any additional options
    for (const auto& [key, value] : config.additional_options) {
        params.options[key] = value;
    }
    
    return params;
}

// TestDatabaseConnection implementation
TestDatabaseConnection::TestDatabaseConnection(DatabaseConnectionFactory::DatabaseType type)
    : logger_(common::Logger::get("test-database-connection")) {
    connection_ = DatabaseConnectionFactory::createConnection(type, 
        type == DatabaseConnectionFactory::DatabaseType::MySQL ? 
        DatabaseConnectionFactory::getMySQLConfig() : 
        DatabaseConnectionFactory::getClinicalConfig());
}

TestDatabaseConnection::TestDatabaseConnection(DatabaseConnectionFactory::DatabaseType type, 
                                             const DatabaseConnectionFactory::ConnectionConfig& config)
    : logger_(common::Logger::get("test-database-connection")) {
    connection_ = DatabaseConnectionFactory::createConnection(type, config);
}

TestDatabaseConnection::~TestDatabaseConnection() {
    if (connection_ && connection_->is_connected()) {
        try {
            connection_->disconnect();
        } catch (const std::exception& e) {
            logger_->warn("Error disconnecting from database: {}", e.what());
        }
    }
}

bool TestDatabaseConnection::isConnected() const {
    return connection_ && connection_->is_connected();
}

size_t TestDatabaseConnection::executeQuery(const std::string& sql) {
    if (!connection_) {
        throw std::runtime_error("Database connection not initialized");
    }
    
    auto result = connection_->execute_query(sql);
    size_t count = 0;
    while (result->next()) {
        count++;
    }
    return count;
}

size_t TestDatabaseConnection::executeUpdate(const std::string& sql) {
    if (!connection_) {
        throw std::runtime_error("Database connection not initialized");
    }
    
    return connection_->execute_update(sql);
}

bool TestDatabaseConnection::tableExists(const std::string& table_name, const std::string& schema) {
    if (!connection_) {
        return false;
    }
    
    return connection_->table_exists(table_name, schema);
}

size_t TestDatabaseConnection::getRowCount(const std::string& table_name, const std::string& schema) {
    if (!connection_) {
        return 0;
    }
    
    std::string qualified_table = schema.empty() ? table_name : schema + "." + table_name;
    std::string query = "SELECT COUNT(*) FROM " + qualified_table;
    
    auto result = connection_->execute_query(query);
    if (result->next()) {
        auto count_value = result->get_value(0);
        try {
            return std::any_cast<int64_t>(count_value);
        } catch (const std::bad_any_cast&) {
            try {
                return std::any_cast<int>(count_value);
            } catch (const std::bad_any_cast&) {
                return std::any_cast<long>(count_value);
            }
        }
    }
    return 0;
}

void TestDatabaseConnection::clearTable(const std::string& table_name, const std::string& schema) {
    if (!connection_) {
        throw std::runtime_error("Database connection not initialized");
    }
    
    std::string qualified_table = schema.empty() ? table_name : schema + "." + table_name;
    connection_->execute_update("TRUNCATE TABLE " + qualified_table + " CASCADE");
    logger_->debug("Cleared table: {}", qualified_table);
}

} // namespace omop::test
