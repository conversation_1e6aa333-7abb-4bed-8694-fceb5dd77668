// UK-specific test utilities and data generators
#pragma once

#include <string>
#include <vector>
#include <random>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace omop::test::uk {

class UKTestDataGenerator {
public:
    UKTestDataGenerator(unsigned int seed = 42) : rng_(seed) {}
    
    // Generate UK postcode
    std::string generatePostcode() {
        static const std::vector<std::string> areas = {
            "SW", "W", "EC", "N", "E", "SE", "NW", "WC",  // London
            "B", "M", "L", "G", "EH", "CF", "BT",          // Major cities
            "OX", "CB", "BA", "BS", "BH", "BR", "RG"       // Other areas
        };
        
        std::uniform_int_distribution<> area_dist(0, areas.size() - 1);
        std::uniform_int_distribution<> num_dist(1, 99);
        std::uniform_int_distribution<> letter_dist(0, 25);
        
        std::string postcode = areas[area_dist(rng_)];
        postcode += std::to_string(num_dist(rng_));
        if (rng_() % 2 == 0) {
            postcode += static_cast<char>('A' + letter_dist(rng_));
        }
        postcode += " ";
        postcode += std::to_string(rng_() % 10);
        postcode += static_cast<char>('A' + letter_dist(rng_));
        postcode += static_cast<char>('A' + letter_dist(rng_));
        
        return postcode;
    }
    
    // Generate UK phone number
    std::string generatePhoneNumber(bool formatted = true) {
        static const std::vector<std::string> area_codes = {
            "020", "0121", "0131", "0141", "0151", "0161",  // Major cities
            "01223", "01865", "01273", "01225"              // Other areas
        };
        
        std::uniform_int_distribution<> area_dist(0, area_codes.size() - 1);
        std::uniform_int_distribution<> digit_dist(0, 9);
        
        std::string phone = area_codes[area_dist(rng_)];
        
        // Generate remaining digits
        int remaining_digits = 11 - phone.length();
        std::string number_part;
        for (int i = 0; i < remaining_digits; ++i) {
            number_part += std::to_string(digit_dist(rng_));
        }
        
        if (formatted) {
            if (phone.length() == 3) {  // London format
                return phone + " " + number_part.substr(0, 4) + " " + number_part.substr(4);
            } else {  // Other formats
                return phone + " " + number_part;
            }
        }
        
        return phone + number_part;
    }
    
    // Generate NHS number with valid checksum
    std::string generateNHSNumber(bool formatted = true) {
        std::uniform_int_distribution<> digit_dist(0, 9);
        
        // Generate first 9 digits
        std::vector<int> digits;
        for (int i = 0; i < 9; ++i) {
            digits.push_back(digit_dist(rng_));
        }
        
        // Calculate checksum
        int checksum = 0;
        for (int i = 0; i < 9; ++i) {
            checksum += digits[i] * (10 - i);
        }
        
        int check_digit = 11 - (checksum % 11);
        if (check_digit == 11) check_digit = 0;
        if (check_digit == 10) {
            // Invalid, regenerate
            return generateNHSNumber(formatted);
        }
        
        digits.push_back(check_digit);
        
        // Format result
        std::string nhs_number;
        for (int digit : digits) {
            nhs_number += std::to_string(digit);
        }
        
        if (formatted) {
            return nhs_number.substr(0, 3) + " " + 
                   nhs_number.substr(3, 3) + " " + 
                   nhs_number.substr(6);
        }
        
        return nhs_number;
    }
    
    // Generate UK date string
    std::string generateUKDate(int year_min = 1920, int year_max = 2023) {
        std::uniform_int_distribution<> year_dist(year_min, year_max);
        std::uniform_int_distribution<> month_dist(1, 12);
        std::uniform_int_distribution<> day_dist(1, 28);  // Safe for all months
        
        int day = day_dist(rng_);
        int month = month_dist(rng_);
        int year = year_dist(rng_);
        
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(2) << day << "/"
            << std::setfill('0') << std::setw(2) << month << "/"
            << year;
        
        return oss.str();
    }
    
    // Generate UK currency amount
    std::string generateCurrencyAmount(double min = 0.01, double max = 10000.00) {
        std::uniform_real_distribution<> amount_dist(min, max);
        double amount = amount_dist(rng_);
        
        std::ostringstream oss;
        oss << "£" << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }
    
    // Generate temperature in Celsius
    double generateTemperatureCelsius(double min = 35.0, double max = 42.0) {
        std::uniform_real_distribution<> temp_dist(min, max);
        return std::round(temp_dist(rng_) * 10) / 10;  // Round to 1 decimal place
    }
    
private:
    std::mt19937 rng_;
};

} // namespace omop::test::uk 