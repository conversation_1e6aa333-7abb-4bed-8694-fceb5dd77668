// Updated API integration test for current OMOP ETL implementation
#include <gtest/gtest.h>
// #include "app/api/api_service.h" // TODO: Fix include path or implement API service
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <nlohmann/json.hpp>
#include <future>
#include <thread>
#include <chrono>

namespace omop::test::integration {

/**
 * @brief Updated API integration test class that matches current implementation
 */
class APIIntegrationTestUpdated : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize configuration manager
        config_manager_ = std::make_shared<common::ConfigurationManager>();
        
        // Load test configuration
        load_test_api_config();

        // Initialize pipeline manager first
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        
        // Initialize ETL service
        etl_service_ = std::make_shared<service::ETLService>(config_manager_, pipeline_manager_);

        // Create test data directory
        test_data_dir_ = std::filesystem::temp_directory_path() / "api_integration_test";
        std::filesystem::create_directories(test_data_dir_);

        // Create sample test data
        create_test_data_files();
    }

    void TearDown() override {
        // Cleanup test data
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }

        DatabaseFixture::TearDown();
    }

    void load_test_api_config() {
        // Configure in-memory for testing
        std::string test_config = R"(
            api:
                host: "localhost"
                port: 8080
                max_connections: 100
                enable_cors: true
                
            database:
                source:
                    type: "postgresql"
                    host: "localhost"
                    port: 5432
                    database: "test_source"
                    username: "test_user"
                    password: "test_pass"
                    
                target:
                    type: "postgresql" 
                    host: "localhost"
                    port: 5432
                    database: "test_omop"
                    username: "test_user"
                    password: "test_pass"
                    
            etl:
                batch_size: 100
                max_workers: 2
                error_threshold: 0.05
        )";
        
        config_manager_->load_config_from_string(test_config);
    }

    void create_test_data_files() {
        // Create CSV test data
        std::ofstream csv_file(test_data_dir_ / "test_patients.csv");
        csv_file << "patient_id,first_name,last_name,birth_date,gender\n";
        csv_file << "1,John,Doe,1980-01-01,M\n";
        csv_file << "2,Jane,Smith,1975-05-15,F\n";
        csv_file << "3,Bob,Johnson,1990-03-10,M\n";
        csv_file.close();

        // Create JSON test data  
        nlohmann::json json_data = {
            {"patients", {
                {{"id", 1}, {"name", "Alice Brown"}, {"dob", "1985-07-20"}, {"gender", "F"}},
                {{"id", 2}, {"name", "Charlie Davis"}, {"dob", "1978-12-05"}, {"gender", "M"}}
            }}
        };
        
        std::ofstream json_file(test_data_dir_ / "test_patients.json");
        json_file << json_data.dump(2);
        json_file.close();
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
    std::filesystem::path test_data_dir_;
};

// Test ETL service initialization and basic functionality
TEST_F(APIIntegrationTestUpdated, TestETLServiceInitialization) {
    // Test that ETL service initializes correctly
    ASSERT_NE(etl_service_, nullptr);
    
    // Test configuration loading
    auto config = etl_service_->get_configuration();
    ASSERT_NE(config, nullptr);
    
    // Verify configuration values
    EXPECT_EQ(config->get_value<int>("etl.batch_size"), 100);
    EXPECT_EQ(config->get_value<int>("etl.max_workers"), 2);
    EXPECT_DOUBLE_EQ(config->get_value<double>("etl.error_threshold"), 0.05);
}

// Test job creation and management through ETL service
TEST_F(APIIntegrationTestUpdated, TestJobManagement) {
    // Create a job request
    std::unordered_map<std::string, std::any> job_config = {
        {"name", std::string("Test CSV Import Job")},
        {"description", std::string("Import test patient data from CSV")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "test_patients.csv").string()},
        {"target_table", std::string("person")},
        {"mapping_config", std::string("patient_to_person_mapping")}
    };

    // Submit job to ETL service
    auto job_id = etl_service_->submit_job(job_config);
    EXPECT_FALSE(job_id.empty());

    // Check job status
    auto status = etl_service_->get_job_status(job_id);
    EXPECT_TRUE(status.has_value());
    
    // Verify job details
    auto job_info = etl_service_->get_job_info(job_id);
    EXPECT_TRUE(job_info.has_value());
    EXPECT_EQ(std::any_cast<std::string>(job_info->at("name")), "Test CSV Import Job");
}

// Test data extraction functionality
TEST_F(APIIntegrationTestUpdated, TestDataExtraction) {
    // Configure CSV extractor
    std::unordered_map<std::string, std::any> extractor_config = {
        {"type", std::string("csv")},
        {"file_path", (test_data_dir_ / "test_patients.csv").string()},
        {"has_header", true},
        {"delimiter", std::string(",")}
    };

    // Test extraction through service
    auto extraction_result = etl_service_->test_extraction(extractor_config);
    EXPECT_TRUE(extraction_result.success);
    EXPECT_GT(extraction_result.record_count, 0);
    EXPECT_EQ(extraction_result.record_count, 3); // We created 3 test records
}

// Test JSON data extraction
TEST_F(APIIntegrationTestUpdated, TestJSONExtraction) {
    // Configure JSON extractor
    std::unordered_map<std::string, std::any> extractor_config = {
        {"type", std::string("json")},
        {"file_path", (test_data_dir_ / "test_patients.json").string()},
        {"json_path", std::string("$.patients[*]")}
    };

    // Test JSON extraction
    auto extraction_result = etl_service_->test_extraction(extractor_config);
    EXPECT_TRUE(extraction_result.success);
    EXPECT_GT(extraction_result.record_count, 0);
}

// Test transformation pipeline
TEST_F(APIIntegrationTestUpdated, TestTransformationPipeline) {
    // Create transformation configuration
    std::unordered_map<std::string, std::any> transform_config = {
        {"source_field", std::string("patient_id")},
        {"target_field", std::string("person_id")}, 
        {"transformation_type", std::string("direct_mapping")},
        {"validation_rules", std::vector<std::string>{"not_null", "numeric"}}
    };

    // Test transformation through service
    std::unordered_map<std::string, std::any> test_record = {
        {"patient_id", 123},
        {"first_name", std::string("Test")},
        {"last_name", std::string("Patient")}
    };

    auto transform_result = etl_service_->test_transformation(transform_config, test_record);
    EXPECT_TRUE(transform_result.success);
    EXPECT_TRUE(transform_result.transformed_record.contains("person_id"));
}

// Test data loading functionality
TEST_F(APIIntegrationTestUpdated, TestDataLoading) {
    // Create test records for loading
    std::vector<std::unordered_map<std::string, std::any>> test_records = {
        {
            {"person_id", 1},
            {"gender_concept_id", 8507}, // Male
            {"year_of_birth", 1980},
            {"race_concept_id", 8527}, // White
            {"ethnicity_concept_id", 38003564} // Not Hispanic
        },
        {
            {"person_id", 2},
            {"gender_concept_id", 8532}, // Female
            {"year_of_birth", 1975},
            {"race_concept_id", 8527}, // White
            {"ethnicity_concept_id", 38003564} // Not Hispanic
        }
    };

    // Configure loader
    std::unordered_map<std::string, std::any> loader_config = {
        {"type", std::string("database")},
        {"target_table", std::string("person")},
        {"batch_size", 10},
        {"upsert_mode", true}
    };

    // Test loading through service
    auto load_result = etl_service_->test_loading(loader_config, test_records);
    EXPECT_TRUE(load_result.success);
    EXPECT_EQ(load_result.records_loaded, 2);
}

// Test end-to-end ETL pipeline
TEST_F(APIIntegrationTestUpdated, TestEndToEndPipeline) {
    // Configure complete ETL pipeline
    std::unordered_map<std::string, std::any> pipeline_config = {
        {"job_name", std::string("E2E Test Pipeline")},
        {"extractor", std::unordered_map<std::string, std::any>{
            {"type", std::string("csv")},
            {"file_path", (test_data_dir_ / "test_patients.csv").string()},
            {"has_header", true}
        }},
        {"transformer", std::unordered_map<std::string, std::any>{
            {"mappings", std::vector<std::unordered_map<std::string, std::any>>{
                {{"source", std::string("patient_id")}, {"target", std::string("person_id")}, {"type", std::string("direct")}},
                {{"source", std::string("gender")}, {"target", std::string("gender_concept_id")}, {"type", std::string("vocabulary_lookup")}}
            }}
        }},
        {"loader", std::unordered_map<std::string, std::any>{
            {"type", std::string("database")},
            {"target_table", std::string("person")}
        }}
    };

    // Submit and execute pipeline
    auto job_id = etl_service_->submit_job(pipeline_config);
    EXPECT_FALSE(job_id.empty());

    // Wait for job completion (with timeout)
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && (std::any_cast<std::string>(*status) == "completed" || 
                         std::any_cast<std::string>(*status) == "failed");
    }, 30000); // 30 second timeout

    EXPECT_TRUE(completed);

    // Verify job completed successfully
    auto final_status = etl_service_->get_job_status(job_id);
    EXPECT_TRUE(final_status.has_value());
    EXPECT_EQ(std::any_cast<std::string>(*final_status), "completed");
}

// Test error handling and recovery
TEST_F(APIIntegrationTestUpdated, TestErrorHandling) {
    // Test with invalid file path
    std::unordered_map<std::string, std::any> invalid_config = {
        {"name", std::string("Invalid File Test")},
        {"source_type", std::string("csv")},
        {"source_path", std::string("/nonexistent/file.csv")},
        {"target_table", std::string("person")}
    };

    // This should fail gracefully
    auto job_id = etl_service_->submit_job(invalid_config);
    
    // Wait for job to fail
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && std::any_cast<std::string>(*status) == "failed";
    }, 10000);

    EXPECT_TRUE(completed);

    // Verify error information is available
    auto job_info = etl_service_->get_job_info(job_id);
    EXPECT_TRUE(job_info.has_value());
    EXPECT_TRUE(job_info->contains("error_message"));
}

// Test concurrent job execution
TEST_F(APIIntegrationTestUpdated, TestConcurrentJobs) {
    std::vector<std::string> job_ids;
    const int num_jobs = 3;

    // Submit multiple jobs concurrently
    for (int i = 0; i < num_jobs; ++i) {
        std::unordered_map<std::string, std::any> job_config = {
            {"name", std::string("Concurrent Job ") + std::to_string(i)},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "test_patients.csv").string()},
            {"target_table", std::string("person")},
            {"batch_size", 1} // Small batch size to make jobs take longer
        };

        auto job_id = etl_service_->submit_job(job_config);
        EXPECT_FALSE(job_id.empty());
        job_ids.push_back(job_id);
    }

    // Wait for all jobs to complete
    for (const auto& job_id : job_ids) {
        bool completed = wait_for_condition([this, &job_id]() {
            auto status = etl_service_->get_job_status(job_id);
            return status && (std::any_cast<std::string>(*status) == "completed" || 
                             std::any_cast<std::string>(*status) == "failed");
        }, 60000); // 1 minute timeout

        EXPECT_TRUE(completed);
    }

    // Verify all jobs completed successfully
    for (const auto& job_id : job_ids) {
        auto status = etl_service_->get_job_status(job_id);
        EXPECT_TRUE(status.has_value());
        // Job should either complete successfully or fail gracefully
        std::string status_str = std::any_cast<std::string>(*status);
        EXPECT_TRUE(status_str == "completed" || status_str == "failed");
    }
}

// Test job cancellation
TEST_F(APIIntegrationTestUpdated, TestJobCancellation) {
    // Submit a long-running job
    std::unordered_map<std::string, std::any> job_config = {
        {"name", std::string("Cancellable Job")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "test_patients.csv").string()},
        {"target_table", std::string("person")},
        {"batch_size", 1}, // Very small batch to make it run longer
        {"delay_between_batches", 1000} // 1 second delay between batches
    };

    auto job_id = etl_service_->submit_job(job_config);
    EXPECT_FALSE(job_id.empty());

    // Wait a bit for job to start
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Cancel the job
    bool cancelled = etl_service_->cancel_job(job_id);
    EXPECT_TRUE(cancelled);

    // Verify job is cancelled
    auto status = etl_service_->get_job_status(job_id);
    EXPECT_TRUE(status.has_value());
    EXPECT_EQ(std::any_cast<std::string>(*status), "cancelled");
}

// Test metrics and monitoring
TEST_F(APIIntegrationTestUpdated, TestMetricsCollection) {
    // Submit a job to generate metrics
    std::unordered_map<std::string, std::any> job_config = {
        {"name", std::string("Metrics Test Job")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "test_patients.csv").string()},
        {"target_table", std::string("person")}
    };

    auto job_id = etl_service_->submit_job(job_config);
    
    // Wait for job completion
    wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && std::any_cast<std::string>(*status) == "completed";
    }, 30000);

    // Get job metrics
    auto metrics = etl_service_->get_job_metrics(job_id);
    EXPECT_TRUE(metrics.has_value());
    
    // Verify metrics contain expected fields
    EXPECT_TRUE(metrics->contains("records_processed"));
    EXPECT_TRUE(metrics->contains("processing_time"));
    EXPECT_TRUE(metrics->contains("throughput"));
}

} // namespace omop::test::integration