# Enhanced Edge Cases Test Configuration
# Comprehensive edge case testing for OMOP ETL pipeline

# Database connections for edge case testing
source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_clinical_data_edge
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    connect_timeout: 30
    application_name: omop_etl_edge_test

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_omop_cdm_edge
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    schema: test_cdm_edge
    application_name: omop_etl_edge_test

# ETL settings for edge case testing
etl_settings:
  batch_size: 50  # Smaller for edge case testing
  parallel_workers: 1  # Single thread for easier debugging
  validation_mode: strict
  error_threshold: 0.10  # Allow 10% error rate for edge cases
  checkpoint_enabled: true
  checkpoint_interval: 30
  log_level: debug
  enable_profiling: true
  enable_monitoring: true
  max_retry_attempts: 5
  retry_delay_seconds: 2
  continue_on_error: true  # Continue to find all edge cases
  enable_data_quality_checks: true

# Edge case scenarios
edge_case_scenarios:

  # Data quality edge cases
  data_quality_edge_cases:
    
    # Null and empty values
    null_empty_values:
      description: "Test handling of null and empty values"
      test_data:
        - person_id: null
          birth_date: ""
          gender: "NULL"
          race: null
          ethnicity: ""
          
        - person_id: 999999
          birth_date: "1900-01-01"
          gender: ""
          race: "   "
          ethnicity: null
          
        - person_id: 999998
          birth_date: null
          gender: "UNKNOWN"
          race: ""
          ethnicity: "NULL"
      
      expected_behavior:
        null_person_id: "skip_record"
        empty_birth_date: "use_default_date"
        invalid_gender: "map_to_unknown"
        empty_race: "map_to_unknown"
        null_ethnicity: "map_to_unknown"

    # Invalid date formats
    invalid_date_formats:
      description: "Test handling of invalid date formats"
      test_data:
        - birth_date: "2023-13-45"  # Invalid month and day
        - birth_date: "2023/02/30"  # Invalid day for February
        - birth_date: "2023-02-29"  # Valid leap year
        - birth_date: "2024-02-29"  # Valid leap year
        - birth_date: "2023-02-29"  # Invalid leap year
        - birth_date: "13/25/2023"  # Invalid month
        - birth_date: "2023-00-15"  # Invalid month
        - birth_date: "2023-12-00"  # Invalid day
        - birth_date: "not-a-date"  # Completely invalid
        - birth_date: "2023-12-32"  # Invalid day
        
      expected_behavior:
        invalid_dates: "log_error_and_skip"
        leap_year_validation: "strict"
        date_range_validation: "1900-01-01 to current_date"

    # Special characters and encoding
    special_characters:
      description: "Test handling of special characters and encoding issues"
      test_data:
        - name: "José María O'Connor-Smith"
        - name: "李小明"
        - name: "محمد أحمد"
        - name: "Иван Петров"
        - name: "François-Xavier"
        - name: "Björk Guðmundsdóttir"
        - name: "José María O'Connor-Smith & Co."
        - name: "Test\nNewline"
        - name: "Test\tTab"
        - name: "Test\r\nCRLF"
        
      expected_behavior:
        unicode_support: "full"
        special_chars: "preserve"
        control_chars: "clean"
        encoding: "UTF-8"

    # Extremely long values
    long_values:
      description: "Test handling of extremely long field values"
      test_data:
        - name: "A" * 1000  # 1000 character name
        - notes: "B" * 10000  # 10000 character notes
        - description: "C" * 50000  # 50000 character description
        - address: "D" * 2000  # 2000 character address
        
      expected_behavior:
        truncate_long_values: true
        max_name_length: 255
        max_notes_length: 10000
        max_description_length: 50000
        max_address_length: 2000

    # Numeric edge cases
    numeric_edge_cases:
      description: "Test handling of numeric edge cases"
      test_data:
        - age: -5  # Negative age
        - age: 0   # Zero age
        - age: 150 # Very old
        - age: 200 # Impossible age
        - weight: -10.5  # Negative weight
        - weight: 0.0    # Zero weight
        - weight: 999.9  # Very high weight
        - height: -50    # Negative height
        - height: 0      # Zero height
        - height: 300    # Very tall
        
      expected_behavior:
        negative_values: "log_warning_and_skip"
        zero_values: "allow_if_valid"
        extreme_values: "validate_against_ranges"
        impossible_values: "log_error_and_skip"

  # Business logic edge cases
  business_logic_edge_cases:
    
    # Temporal relationships
    temporal_relationships:
      description: "Test temporal relationship edge cases"
      test_data:
        - birth_date: "2023-01-01"
          death_date: "2022-12-31"  # Death before birth
          
        - birth_date: "2023-01-01"
          death_date: "2023-01-01"  # Death on birth date
          
        - visit_start: "2023-01-15"
          visit_end: "2023-01-10"   # Visit ends before start
          
        - condition_start: "2023-01-01"
          condition_end: "2023-01-01"  # Same day condition
          
        - drug_start: "2023-01-01"
          drug_end: "2023-12-31"    # Year-long drug exposure
          
        - procedure_date: "2023-01-01"
          visit_start: "2023-01-15" # Procedure before visit
          
      expected_behavior:
        death_before_birth: "log_error_and_skip"
        same_day_events: "allow"
        visit_end_before_start: "log_error_and_skip"
        long_exposures: "validate_reasonableness"
        procedure_before_visit: "log_warning"

    # Referential integrity edge cases
    referential_integrity:
      description: "Test referential integrity edge cases"
      test_data:
        - person_id: 999999  # Non-existent person
          visit_occurrence_id: 1
          
        - person_id: 1
          visit_occurrence_id: 999999  # Non-existent visit
          
        - person_id: 1
          provider_id: 999999  # Non-existent provider
          
        - person_id: 1
          care_site_id: 999999  # Non-existent care site
          
        - person_id: 1
          location_id: 999999  # Non-existent location
          
      expected_behavior:
        orphan_records: "log_error_and_skip"
        missing_references: "log_error_and_skip"
        circular_references: "detect_and_log"
        self_references: "validate_and_log"

    # Vocabulary mapping edge cases
    vocabulary_mapping:
      description: "Test vocabulary mapping edge cases"
      test_data:
        - gender: "MALE"      # Exact match
        - gender: "male"      # Case insensitive
        - gender: "M"         # Abbreviation
        - gender: "1"         # Numeric code
        - gender: "Male"      # Title case
        - gender: "MALE "     # Trailing space
        - gender: " MALE"     # Leading space
        - gender: "MALE\t"    # Tab character
        - gender: "MALE\n"    # Newline character
        - gender: "MALE\r\n"  # CRLF
        - gender: "MALE&"     # Special character
        - gender: "MALE;"     # Semicolon
        - gender: "MALE,"     # Comma
        - gender: "MALE."     # Period
        - gender: "MALE!"     # Exclamation
        - gender: "MALE?"     # Question mark
        - gender: "MALE@"     # At symbol
        - gender: "MALE#"     # Hash
        - gender: "MALE$"     # Dollar
        - gender: "MALE%"     # Percent
        - gender: "MALE^"     # Caret
        - gender: "MALE*"     # Asterisk
        - gender: "MALE("     # Parenthesis
        - gender: "MALE)"     # Parenthesis
        - gender: "MALE-"     # Hyphen
        - gender: "MALE_"     # Underscore
        - gender: "MALE="     # Equals
        - gender: "MALE+"     # Plus
        - gender: "MALE["     # Bracket
        - gender: "MALE]"     # Bracket
        - gender: "MALE{"     # Brace
        - gender: "MALE}"     # Brace
        - gender: "MALE|"     # Pipe
        - gender: "MALE\\"    # Backslash
        - gender: "MALE/"     # Forward slash
        - gender: "MALE<"     # Less than
        - gender: "MALE>"     # Greater than
        - gender: "MALE~"     # Tilde
        - gender: "MALE`"     # Backtick
        
      expected_behavior:
        exact_matches: "map_correctly"
        case_insensitive: "map_correctly"
        abbreviations: "map_correctly"
        numeric_codes: "map_correctly"
        whitespace: "trim_and_map"
        special_chars: "clean_and_map"
        unknown_values: "map_to_unknown"

  # Performance edge cases
  performance_edge_cases:
    
    # Large data volumes
    large_data_volumes:
      description: "Test handling of large data volumes"
      test_data:
        - single_large_record: 1000000  # 1MB single record
        - many_small_records: 100000    # 100K small records
        - mixed_record_sizes: true      # Mix of large and small
        - memory_pressure: true         # Simulate memory pressure
        
      expected_behavior:
        memory_management: "streaming_processing"
        batch_optimization: "adaptive_batch_sizes"
        timeout_handling: "graceful_timeout"
        resource_cleanup: "automatic_cleanup"

    # Slow processing
    slow_processing:
      description: "Test handling of slow processing scenarios"
      test_data:
        - complex_transformations: true
        - slow_database_queries: true
        - network_latency: 1000  # 1 second latency
        - cpu_intensive_operations: true
        
      expected_behavior:
        timeout_handling: "configurable_timeouts"
        progress_tracking: "detailed_progress"
        cancellation: "graceful_cancellation"
        resource_monitoring: "continuous_monitoring"

  # Error handling edge cases
  error_handling_edge_cases:
    
    # Database connection issues
    database_connection_issues:
      description: "Test handling of database connection issues"
      test_data:
        - connection_timeout: true
        - connection_drop: true
        - slow_connection: true
        - connection_pool_exhaustion: true
        - database_unavailable: true
        
      expected_behavior:
        retry_logic: "exponential_backoff"
        circuit_breaker: "implemented"
        fallback_mechanisms: "available"
        error_reporting: "detailed"

    # Data corruption
    data_corruption:
      description: "Test handling of data corruption scenarios"
      test_data:
        - corrupted_records: true
        - malformed_json: true
        - invalid_xml: true
        - encoding_issues: true
        - checksum_mismatch: true
        
      expected_behavior:
        corruption_detection: "checksum_validation"
        quarantine_mechanism: "implemented"
        recovery_attempts: "limited"
        error_logging: "detailed"

    # Resource exhaustion
    resource_exhaustion:
      description: "Test handling of resource exhaustion"
      test_data:
        - memory_exhaustion: true
        - disk_space_exhaustion: true
        - cpu_exhaustion: true
        - network_bandwidth_exhaustion: true
        - file_descriptor_exhaustion: true
        
      expected_behavior:
        resource_monitoring: "continuous"
        graceful_degradation: "implemented"
        resource_cleanup: "automatic"
        alerting: "immediate"

# Enhanced validation rules for edge cases
enhanced_validation_rules:
  
  # Person table validations
  person_validations:
    - field: person_id
      rule: "positive_integer"
      error_message: "Person ID must be a positive integer"
      
    - field: birth_date
      rule: "date_range"
      min_date: "1900-01-01"
      max_date: "current_date"
      error_message: "Birth date must be between 1900 and current date"
      
    - field: death_date
      rule: "date_after_birth"
      birth_field: "birth_date"
      error_message: "Death date must be after birth date"
      
    - field: gender_concept_id
      rule: "valid_concept"
      vocabulary: "Gender"
      error_message: "Gender must be a valid concept ID"

  # Visit table validations
  visit_validations:
    - field: visit_start_date
      rule: "not_null"
      error_message: "Visit start date cannot be null"
      
    - field: visit_end_date
      rule: "date_after_start"
      start_field: "visit_start_date"
      error_message: "Visit end date must be after start date"
      
    - field: visit_concept_id
      rule: "valid_concept"
      vocabulary: "Visit"
      error_message: "Visit concept must be valid"

  # Clinical event validations
  clinical_validations:
    - field: condition_start_date
      rule: "not_null"
      error_message: "Condition start date cannot be null"
      
    - field: drug_exposure_start_date
      rule: "not_null"
      error_message: "Drug exposure start date cannot be null"
      
    - field: procedure_date
      rule: "not_null"
      error_message: "Procedure date cannot be null"
      
    - field: measurement_date
      rule: "not_null"
      error_message: "Measurement date cannot be null"

# Error handling configuration for edge cases
error_handling:
  max_error_count: 1000  # Higher for edge case testing
  error_threshold: 0.10  # 10% error rate allowed
  continue_on_error: true
  quarantine_invalid_records: true
  detailed_error_reporting: true
  error_categorization: true
  
  error_categories:
    - category: "data_quality"
      description: "Data quality issues"
      severity: "warning"
      
    - category: "business_logic"
      description: "Business logic violations"
      severity: "error"
      
    - category: "system_error"
      description: "System-level errors"
      severity: "critical"
      
    - category: "performance"
      description: "Performance issues"
      severity: "warning"

# Reporting configuration for edge cases
reporting:
  generate_edge_case_report: true
  report_format: "html"
  include_error_details: true
  include_recommendations: true
  
  report_sections:
    - "edge_case_summary"
    - "error_analysis"
    - "data_quality_issues"
    - "business_logic_violations"
    - "system_errors"
    - "performance_issues"
    - "recommendations"
    
  export_formats:
    - "html"
    - "json"
    - "csv" 