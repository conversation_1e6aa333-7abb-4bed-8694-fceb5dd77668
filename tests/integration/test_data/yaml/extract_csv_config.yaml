# CSV Extractor Integration Test Configuration
# Used by CSV extractor integration tests

csv_extractor:
  # Basic CSV parsing options
  delimiter: ","
  quote_char: '"'
  escape_char: "\\"
  has_header: true
  encoding: "UTF-8"
  
  # Data processing options
  skip_empty_lines: true
  skip_lines: 0
  max_lines: 0  # 0 = unlimited
  max_records: 0  # 0 = unlimited
  
  # Type inference options
  infer_types: true
  type_inference_sample_size: 100
  
  # UK Localization settings
  locale: "en_GB"
  date_format: "%d/%m/%Y"
  datetime_format: "%d/%m/%Y %H:%M:%S"
  currency_symbol: "£"
  decimal_separator: "."
  thousands_separator: ","
  
  # Column specifications for testing
  test_columns:
    - name: "patient_id"
      type: "integer"
      nullable: false
    - name: "nhs_number"
      type: "string"
      nullable: false
      validation: "uk_nhs_number"
    - name: "first_name"
      type: "string"
      nullable: false
    - name: "last_name"
      type: "string"
      nullable: false
    - name: "birth_date"
      type: "date"
      format: "%d/%m/%Y"
      nullable: false
    - name: "postcode"
      type: "string"
      validation: "uk_postcode"
      nullable: true
    - name: "amount"
      type: "currency"
      currency: "GBP"
      nullable: true
    - name: "temperature"
      type: "decimal"
      unit: "celsius"
      nullable: true
    - name: "gender"
      type: "string"
      values: ["M", "F", "O", "U"]
      nullable: false

# Test scenarios configuration
test_scenarios:
  basic_extraction:
    description: "Basic CSV extraction with type inference"
    file_pattern: "patients.csv"
    expected_records: 10
    
  quoted_fields:
    description: "CSV with quoted fields containing special characters"
    delimiter: ","
    quote_char: '"'
    escape_char: "\\"
    
  custom_delimiter:
    description: "Pipe-delimited CSV file"
    delimiter: "|"
    
  no_header:
    description: "CSV without header row"
    has_header: false
    column_names: ["id", "name", "value"]
    
  uk_regional:
    description: "UK regional format testing"
    locale: "en_GB"
    validate_nhs_numbers: true
    validate_postcodes: true
    date_format: "%d/%m/%Y"
    
  large_file:
    description: "Large file performance testing"
    batch_size: 1000
    max_records: 10000
    
  column_selection:
    description: "Column filtering test"
    columns: ["patient_id", "first_name", "last_name"]
    
# Performance thresholds
performance:
  max_processing_time_seconds: 60
  max_memory_usage_mb: 512
  min_records_per_second: 1000
  
# Validation rules
validation:
  uk_nhs_number:
    pattern: "^[0-9]{10}$"
    checksum: true
    
  uk_postcode:
    pattern: "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$"
    
  date_formats:
    - "%d/%m/%Y"
    - "%d-%m-%Y"
    - "%Y-%m-%d" 