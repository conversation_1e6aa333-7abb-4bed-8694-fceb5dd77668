# Extract Library Integration Test Master Configuration
# This file orchestrates all extract library integration tests

extract_integration_tests:
  # Configuration file references
  configurations:
    csv_extractor: "extract_csv_config.yaml"
    json_extractor: "extract_json_config.yaml"
    database_extractor: "extract_database_config.yaml"
    multi_source_extractor: "extract_multi_source_config.yaml"
    uk_localization: "uk_localization_config.yaml"
    
  # Test execution settings
  execution:
    parallel_test_execution: true
    max_concurrent_tests: 4
    test_timeout_seconds: 300
    fail_fast: false
    retry_failed_tests: true
    max_retries: 2
    
  # Test data paths
  test_data_paths:
    csv_data: "tests/integration/test_data/csv/"
    json_data: "tests/integration/test_data/json/"
    sql_data: "tests/integration/test_data/sql/"
    yaml_configs: "tests/integration/test_data/yaml/"
    
  # Test categories and priorities
  test_categories:
    unit_functionality:
      priority: 1
      tests:
        - "CsvExtractorIntegrationTest.*"
        - "JsonExtractorIntegrationTest.*"
        - "DatabaseExtractorIntegrationTest.*"
      description: "Basic extractor functionality tests"
      
    multi_source:
      priority: 2
      tests:
        - "MultiSourceExtractionIntegrationTest.*"
      description: "Multi-source integration tests"
      
    uk_localization:
      priority: 3
      tests:
        - "UKLocalizedExtractIntegrationTest.*"
      description: "UK healthcare localization tests"
      
    performance:
      priority: 4
      tests:
        - "*Performance*"
        - "*LargeFile*"
        - "*Streaming*"
      description: "Performance and scalability tests"
      
    error_handling:
      priority: 5
      tests:
        - "*Error*"
        - "*Exception*" 
        - "*Failure*"
      description: "Error handling and resilience tests"

# Test environment configuration
test_environment:
  # Docker configuration
  docker:
    use_docker: true
    compose_file: "docker-compose.test.yml"
    services: ["omop-etl-dev"]
    
  # Database setup
  databases:
    test_postgresql:
      enabled: false  # Optional
      image: "postgres:13"
      environment:
        POSTGRES_DB: "test_omop_db"
        POSTGRES_USER: "test_user"
        POSTGRES_PASSWORD: "test_password"
        
    test_mysql:
      enabled: false  # Optional
      image: "mysql:8.0"
      environment:
        MYSQL_DATABASE: "test_omop_db"
        MYSQL_USER: "test_user"
        MYSQL_PASSWORD: "test_password"
        MYSQL_ROOT_PASSWORD: "root_password"
        
  # File system setup
  filesystem:
    temp_directory: "/tmp"
    cleanup_after_tests: true
    preserve_test_data: false
    
# Reporting and output
reporting:
  # Test results output
  output_formats:
    - "junit_xml"
    - "json"
    - "console"
    
  output_files:
    junit_xml: "extract_integration_test_results.xml"
    json: "extract_integration_test_results.json"
    coverage: "extract_integration_coverage.html"
    
  # Performance metrics
  collect_performance_metrics: true
  performance_baseline_file: "extract_performance_baseline.json"
  
  # Detailed logging
  logging:
    level: "INFO"
    file: "extract_integration_tests.log"
    include_timestamps: true
    include_thread_info: true

# Quality gates and thresholds
quality_gates:
  # Test success thresholds
  minimum_pass_rate: 0.80  # 80% of tests must pass
  critical_test_failures: 0  # No critical test failures allowed
  
  # Performance thresholds
  max_test_execution_time_seconds: 300
  max_memory_usage_mb: 2048
  min_extraction_rate_records_per_second: 100
  
  # Coverage requirements
  minimum_code_coverage: 0.70  # 70% code coverage
  minimum_line_coverage: 0.75   # 75% line coverage
  
# UK Healthcare compliance checks
uk_healthcare_compliance:
  # Data protection validation
  gdpr_compliance_checks: true
  data_minimization_validation: true
  
  # NHS data standards
  nhs_number_validation: true
  postcode_validation: true
  date_format_compliance: true
  
  # Clinical coding standards
  snomed_ct_validation: false  # Optional
  icd10_validation: false      # Optional
  read_codes_validation: false # Optional

# Integration with CI/CD
cicd_integration:
  # GitHub Actions
  github_actions:
    workflow_file: ".github/workflows/extract-integration-tests.yml"
    trigger_on: ["push", "pull_request"]
    
  # Jenkins
  jenkins:
    pipeline_file: "Jenkinsfile.extract-tests"
    
  # Build artifacts
  artifacts:
    - "extract_integration_test_results.*"
    - "extract_integration_coverage.*"
    - "extract_integration_tests.log"
    - "extract_performance_metrics.json"

# Test data management
test_data_management:
  # Data generation
  generate_synthetic_data: true
  synthetic_data_size: "medium"  # small, medium, large
  
  # Data refresh
  refresh_test_data_before_run: false
  archive_test_results: true
  retention_period_days: 30
  
  # Sensitive data handling
  anonymize_test_data: true
  encrypt_sensitive_files: false  # For testing environment
  
# Notifications and alerts
notifications:
  # Test completion notifications
  on_test_completion:
    enabled: true
    channels: ["console"]  # email, slack, teams, console
    
  # Failure notifications
  on_test_failure:
    enabled: true
    channels: ["console"]
    include_logs: true
    
  # Performance degradation alerts
  on_performance_degradation:
    enabled: true
    threshold_percent: 20  # 20% performance degradation

# Custom test hooks
custom_hooks:
  # Pre-test setup
  before_all_tests:
    - "scripts/setup-test-environment.sh"
    - "scripts/prepare-test-data.sh"
    
  # Post-test cleanup
  after_all_tests:
    - "scripts/cleanup-test-environment.sh"
    - "scripts/archive-test-results.sh"
    
  # Per-test hooks
  before_each_test:
    - "scripts/reset-test-state.sh"
    
  after_each_test:
    - "scripts/collect-test-metrics.sh"

# Advanced configuration
advanced:
  # Memory management
  memory_profiling: false
  leak_detection: false
  
  # Threading and concurrency
  thread_safety_testing: true
  deadlock_detection: false
  
  # Network and I/O
  network_simulation: false
  io_fault_injection: false
  
  # Security testing
  sql_injection_testing: true
  input_validation_testing: true 