# OMOP ETL Pipeline Configuration - Comprehensive E2E Test Version
# This configuration file defines the mappings from source data to OMOP CDM tables
# Enhanced for comprehensive end-to-end testing with all major OMOP tables

# Database connections
source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_clinical_data
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    connect_timeout: 30
    application_name: omop_etl_e2e_test

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_omop_cdm
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    schema: test_cdm
    application_name: omop_etl_e2e_test

# ETL settings
etl_settings:
  batch_size: 100  # Smaller for testing
  parallel_workers: 2
  validation_mode: strict  # strict, warning, or skip
  error_threshold: 0.05    # Allow 5% error rate for testing
  checkpoint_enabled: true
  checkpoint_interval: 60   # seconds - shorter for testing
  log_level: debug
  enable_profiling: true
  enable_monitoring: true
  max_retry_attempts: 3
  retry_delay_seconds: 5
  continue_on_error: false
  enable_data_quality_checks: true

# Vocabulary mappings - Enhanced with more comprehensive mappings
vocabulary_mappings:
  Gender:
    "Male": 8507
    "M": 8507
    "MALE": 8507
    "1": 8507
    "Female": 8532
    "F": 8532
    "FEMALE": 8532
    "2": 8532
    "Unknown": 0
    "U": 0
    "NULL": 0
    "": 0

  Race:
    "White": 8527
    "Caucasian": 8527
    "WHITE": 8527
    "Black or African American": 8516
    "Black": 8516
    "African American": 8516
    "BLACK": 8516
    "Asian": 8515
    "ASIAN": 8515
    "American Indian or Alaska Native": 8657
    "Native American": 8657
    "Native Hawaiian or Other Pacific Islander": 8557
    "Pacific Islander": 8557
    "Mixed": 8522
    "Other": 8522
    "Unknown": 0
    "NULL": 0
    "": 0

  Ethnicity:
    "Hispanic or Latino": 38003563
    "Hispanic": 38003563
    "Latino": 38003563
    "HISPANIC": 38003563
    "Not Hispanic or Latino": 38003564
    "Non-Hispanic": 38003564
    "Not Hispanic": 38003564
    "NON-HISPANIC": 38003564
    "Unknown": 0
    "NULL": 0
    "": 0

  VisitType:
    "Inpatient": 9201
    "IP": 9201
    "INPATIENT": 9201
    "Outpatient": 9202
    "OP": 9202
    "OUTPATIENT": 9202
    "Emergency": 9203
    "ER": 9203
    "ED": 9203
    "EMERGENCY": 9203
    "Urgent Care": 9202
    "Home Health": 581476
    "Telehealth": 5083

  ProcedureType:
    "PRIMARY": 44786630  # Primary Procedure
    "SECONDARY": 44786631  # Secondary Procedure
    "SURGICAL": 44786630
    "DIAGNOSTIC": 44786631
    "THERAPEUTIC": 44786630

  MeasurementType:
    "LAB": 44818702  # Lab result
    "VITAL": 44818701  # From physical examination
    "LABORATORY": 44818702
    "VITALS": 44818701
    "IMAGING": 44818702

  ObservationType:
    "CLINICAL": 38000280  # Observation recorded from EHR
    "PATIENT": 38000281  # Patient reported
    "SURVEY": 38000282  # Survey
    "DERIVED": 38000283  # Derived value

  DrugType:
    "PRESCRIPTION": 38000177  # Prescription written
    "DISPENSED": 38000175  # Prescription dispensed
    "ADMINISTERED": 38000180  # Inpatient administration
    "OVER_THE_COUNTER": 38000179  # Over the counter

  DeathType:
    "PRIMARY": 38003566  # Primary cause of death
    "CONTRIBUTING": 38003567  # Contributing cause of death
    "UNDERLYING": 38003568  # Underlying cause of death

# Table mappings - Comprehensive coverage of all major OMOP tables
table_mappings:
  # Person table mapping
  person:
    source_table: patients
    target_table: person
    pre_process_sql: |
      -- Clean up birth dates and validate data
      UPDATE patients
      SET birth_date = NULL
      WHERE birth_date < '1900-01-01' OR birth_date > CURRENT_DATE;
      
      -- Standardize gender values
      UPDATE patients
      SET gender = UPPER(TRIM(gender))
      WHERE gender IS NOT NULL;
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true
        default_time: "00:00:00"

      - source_column: birth_date
        target_column: year_of_birth
        type: custom
        function: extract_year

      - source_column: birth_date
        target_column: month_of_birth
        type: custom
        function: extract_month

      - source_column: birth_date
        target_column: day_of_birth
        type: custom
        function: extract_day

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        default_value: 0

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default_value: 0

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default_value: 0

      - source_column: patient_id
        target_column: person_source_value
        type: direct

      - source_column: gender
        target_column: gender_source_value
        type: direct

      - source_column: race
        target_column: race_source_value
        type: direct

      - source_column: ethnicity
        target_column: ethnicity_source_value
        type: direct

      - source_column: location_id
        target_column: location_id
        type: direct

    filters:
      - field: birth_date
        operator: not_null
      - field: gender
        operator: not_null
      - field: patient_id
        operator: not_null

    validations:
      - field: year_of_birth
        rule: range
        min: 1900
        max: 2024
      - field: gender_concept_id
        rule: not_zero
      - field: person_id
        rule: unique
      - field: person_id
        rule: positive_integer

  # Observation Period mapping
  observation_period:
    source_table: patient_enrollment
    target_table: observation_period
    transformations:
      - source_column: enrollment_id
        target_column: observation_period_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: enrollment_start_date
        target_column: observation_period_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: enrollment_end_date
        target_column: observation_period_end_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: enrollment_type
        target_column: period_type_concept_id
        type: conditional
        conditions:
          - if: enrollment_type == "INSURANCE"
            then: 44814724  # Period inferred from insurance enrollment
          - if: enrollment_type == "EHR"
            then: 44814725  # Period inferred from EHR enrollment
          - else: 44814724

    filters:
      - field: patient_id
        operator: exists
        reference_table: person
        reference_field: person_id

    validations:
      - field: observation_period_start_date
        rule: not_null
      - field: observation_period_end_date
        rule: greater_than_or_equal
        compare_field: observation_period_start_date

  # Visit Occurrence mapping
  visit_occurrence:
    source_table: encounters
    target_table: visit_occurrence
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: encounter_type
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: VisitType
        default_value: 0

      - source_column: admission_date
        target_column: visit_start_date
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: discharge_date
        target_column: visit_end_date
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: admission_date
        target_column: visit_start_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: discharge_date
        target_column: visit_end_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: visit_type_source
        target_column: visit_type_concept_id
        type: conditional
        conditions:
          - if: visit_type_source == "EHR"
            then: 32817  # EHR encounter
          - if: visit_type_source == "CLAIM"
            then: 32810  # Claim
          - else: 0

      - source_column: encounter_id
        target_column: visit_source_value
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: department_id
        target_column: care_site_id
        type: direct

      - source_column: admission_source
        target_column: admitting_source_concept_id
        type: conditional
        conditions:
          - if: admission_source == "EMERGENCY"
            then: 8870  # Emergency Room
          - if: admission_source == "REFERRAL"
            then: 8844  # Physician Referral
          - else: 0

      - source_column: discharge_disposition
        target_column: discharge_to_concept_id
        type: conditional
        conditions:
          - if: discharge_disposition == "HOME"
            then: 8536  # Home
          - if: discharge_disposition == "TRANSFER"
            then: 8717  # Transfer
          - else: 0

    filters:
      - field: admission_date
        operator: not_null
      - field: patient_id
        operator: exists
        reference_table: person
        reference_field: person_id

    validations:
      - field: visit_start_date
        rule: date_range
        min: "1900-01-01"
        max: "2099-12-31"
      - field: visit_end_date
        rule: greater_than_or_equal
        compare_field: visit_start_date
      - field: visit_occurrence_id
        rule: unique

  # Condition Occurrence mapping
  condition_occurrence:
    source_table: diagnoses
    target_table: condition_occurrence
    transformations:
      - source_column: diagnosis_id
        target_column: condition_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: icd_code
        target_column: condition_concept_id
        type: vocabulary_mapping
        vocabulary: ICD10CM
        target_vocabulary: SNOMED
        mapping_type: source_to_standard

      - source_column: diagnosis_date
        target_column: condition_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: diagnosis_date
        target_column: condition_start_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true

      - source_column: resolution_date
        target_column: condition_end_date
        type: date_transform
        format: "%Y-%m-%d"
        allow_null: true

      - source_column: resolution_date
        target_column: condition_end_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true
        allow_null: true

      - source_column: diagnosis_type
        target_column: condition_type_concept_id
        type: conditional
        conditions:
          - if: diagnosis_type == "PRIMARY"
            then: 32902  # Primary diagnosis
          - if: diagnosis_type == "SECONDARY"
            then: 32908  # Secondary diagnosis
          - if: diagnosis_type == "ADMISSION"
            then: 32901  # Admission diagnosis
          - else: 32899  # Other diagnosis

      - source_column: condition_status
        target_column: condition_status_concept_id
        type: conditional
        conditions:
          - if: condition_status == "CONFIRMED"
            then: 32893  # Confirmed
          - if: condition_status == "PROVISIONAL"
            then: 32894  # Provisional
          - else: 0

      - source_column: icd_code
        target_column: condition_source_value
        type: direct

      - source_column: icd_code
        target_column: condition_source_concept_id
        type: vocabulary_mapping
        vocabulary: ICD10CM

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

    validations:
      - field: condition_concept_id
        rule: not_zero
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id
      - field: condition_start_date
        rule: not_null

  # Drug Exposure mapping
  drug_exposure:
    source_table: medications
    target_table: drug_exposure
    transformations:
      - source_column: medication_id
        target_column: drug_exposure_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: drug_code
        target_column: drug_concept_id
        type: vocabulary_mapping
        vocabulary: RxNorm
        mapping_type: ingredient_level

      - source_column: start_date
        target_column: drug_exposure_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_columns: [start_date, duration_days]
        target_column: drug_exposure_end_date
        type: date_calculation
        calculation: start_date + duration_days

      - source_column: start_date
        target_column: drug_exposure_start_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true

      - source_columns: [start_date, duration_days]
        target_column: drug_exposure_end_datetime
        type: date_calculation
        calculation: start_date + duration_days
        add_time: true

      - source_column: prescription_type
        target_column: drug_type_concept_id
        type: vocabulary_mapping
        vocabulary: DrugType
        default_value: 0

      - source_column: quantity
        target_column: quantity
        type: numeric_transform
        operation: round
        precision: 2

      - source_column: days_supply
        target_column: days_supply
        type: direct

      - source_column: refills
        target_column: refills
        type: direct

      - source_column: sig_text
        target_column: sig
        type: string_transform
        max_length: 500

      - source_column: route
        target_column: route_concept_id
        type: vocabulary_mapping
        vocabulary: Route

      - source_column: drug_name
        target_column: drug_source_value
        type: direct

      - source_column: route
        target_column: route_source_value
        type: direct

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: dose_value
        target_column: effective_drug_dose
        type: numeric_transform

      - source_column: dose_unit
        target_column: dose_unit_concept_id
        type: vocabulary_mapping
        vocabulary: Unit

    validations:
      - field: drug_concept_id
        rule: not_zero
      - field: drug_exposure_start_date
        rule: not_null
      - field: quantity
        rule: positive_number
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id

  # Procedure Occurrence mapping
  procedure_occurrence:
    source_table: procedures
    target_table: procedure_occurrence
    transformations:
      - source_column: procedure_id
        target_column: procedure_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: procedure_code
        target_column: procedure_concept_id
        type: vocabulary_mapping
        vocabulary: CPT4
        target_vocabulary: SNOMED
        mapping_type: source_to_standard

      - source_column: procedure_date
        target_column: procedure_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: procedure_date
        target_column: procedure_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: procedure_type
        target_column: procedure_type_concept_id
        type: vocabulary_mapping
        vocabulary: ProcedureType
        default_value: 0

      - source_column: modifier_code
        target_column: modifier_concept_id
        type: vocabulary_mapping
        vocabulary: CPT4
        allow_null: true

      - source_column: quantity
        target_column: quantity
        type: direct

      - source_column: procedure_code
        target_column: procedure_source_value
        type: direct

      - source_column: procedure_code
        target_column: procedure_source_concept_id
        type: vocabulary_mapping
        vocabulary: CPT4

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

    validations:
      - field: procedure_concept_id
        rule: not_zero
      - field: procedure_date
        rule: not_null
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id

  # Measurement mapping
  measurement:
    source_table: lab_results
    target_table: measurement
    transformations:
      - source_column: lab_id
        target_column: measurement_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: loinc_code
        target_column: measurement_concept_id
        type: vocabulary_mapping
        vocabulary: LOINC

      - source_column: result_date
        target_column: measurement_date
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: result_date
        target_column: measurement_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: result_type
        target_column: measurement_type_concept_id
        type: vocabulary_mapping
        vocabulary: MeasurementType
        default_value: 0

      - source_column: operator
        target_column: operator_concept_id
        type: vocabulary_mapping
        vocabulary: MeasurementOperator

      - source_column: numeric_result
        target_column: value_as_number
        type: numeric_transform

      - source_column: text_result
        target_column: value_as_concept_id
        type: conditional
        conditions:
          - if: text_result == "POSITIVE"
            then: 45884084  # Positive
          - if: text_result == "NEGATIVE"
            then: 45878583  # Negative
          - if: text_result == "ABNORMAL"
            then: 45878745  # Abnormal
          - if: text_result == "NORMAL"
            then: 45884153  # Normal
          - else: 0

      - source_column: unit
        target_column: unit_concept_id
        type: vocabulary_mapping
        vocabulary: Unit

      - source_column: ref_range_low
        target_column: range_low
        type: numeric_transform

      - source_column: ref_range_high
        target_column: range_high
        type: numeric_transform

      - source_column: lab_name
        target_column: measurement_source_value
        type: direct

      - source_column: unit
        target_column: unit_source_value
        type: direct

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

    validations:
      - field: measurement_concept_id
        rule: not_zero
      - field: value_as_number
        rule: reasonable_range
        context: measurement_concept_id
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id

  # Observation mapping
  observation:
    source_table: clinical_observations
    target_table: observation
    transformations:
      - source_column: observation_id
        target_column: observation_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: observation_code
        target_column: observation_concept_id
        type: vocabulary_mapping
        vocabulary: SNOMED

      - source_column: observation_date
        target_column: observation_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: observation_date
        target_column: observation_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: observation_type
        target_column: observation_type_concept_id
        type: vocabulary_mapping
        vocabulary: ObservationType
        default_value: 0

      - source_column: value_as_number
        target_column: value_as_number
        type: numeric_transform

      - source_column: value_as_string
        target_column: value_as_string
        type: string_transform
        max_length: 60

      - source_column: value_as_concept
        target_column: value_as_concept_id
        type: vocabulary_mapping
        vocabulary: SNOMED

      - source_column: unit
        target_column: unit_concept_id
        type: vocabulary_mapping
        vocabulary: Unit

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: observation_code
        target_column: observation_source_value
        type: direct

      - source_column: observation_code
        target_column: observation_source_concept_id
        type: vocabulary_mapping
        vocabulary: SNOMED

      - source_column: unit
        target_column: unit_source_value
        type: direct

    validations:
      - field: observation_concept_id
        rule: not_zero
      - field: observation_date
        rule: not_null
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id

  # Death mapping
  death:
    source_table: deaths
    target_table: death
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: death_date
        target_column: death_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: death_date
        target_column: death_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: death_type
        target_column: death_type_concept_id
        type: vocabulary_mapping
        vocabulary: DeathType
        default_value: 0

      - source_column: cause_of_death_code
        target_column: cause_concept_id
        type: vocabulary_mapping
        vocabulary: ICD10CM
        target_vocabulary: SNOMED
        mapping_type: source_to_standard

      - source_column: cause_of_death_code
        target_column: cause_source_value
        type: direct

      - source_column: cause_of_death_code
        target_column: cause_source_concept_id
        type: vocabulary_mapping
        vocabulary: ICD10CM

    validations:
      - field: death_date
        rule: not_null
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id
      - field: person_id
        rule: unique

  # Note mapping
  note:
    source_table: clinical_notes
    target_table: note
    transformations:
      - source_column: note_id
        target_column: note_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: note_date
        target_column: note_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: note_date
        target_column: note_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: note_type
        target_column: note_type_concept_id
        type: conditional
        conditions:
          - if: note_type == "PROGRESS_NOTE"
            then: 44814637  # Progress note
          - if: note_type == "DISCHARGE_SUMMARY"
            then: 44814638  # Discharge summary
          - if: note_type == "NURSING_NOTE"
            then: 44814639  # Nursing note
          - else: 0

      - source_column: note_class
        target_column: note_class_concept_id
        type: conditional
        conditions:
          - if: note_class == "CLINICAL"
            then: 44814640  # Clinical note
          - if: note_class == "ADMINISTRATIVE"
            then: 44814641  # Administrative note
          - else: 0

      - source_column: note_title
        target_column: note_title
        type: string_transform
        max_length: 250

      - source_column: note_text
        target_column: note_text
        type: string_transform
        max_length: 5000

      - source_column: encoding_type
        target_column: encoding_concept_id
        type: conditional
        conditions:
          - if: encoding_type == "UTF8"
            then: 32678  # UTF-8
          - else: 0

      - source_column: language_code
        target_column: language_concept_id
        type: conditional
        conditions:
          - if: language_code == "en"
            then: 4180186  # English
          - if: language_code == "es"
            then: 4182511  # Spanish
          - else: 0

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: note_source_value
        target_column: note_source_value
        type: direct

    validations:
      - field: note_date
        rule: not_null
      - field: note_text
        rule: not_null
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id

# Custom transformation functions
custom_functions:
  extract_year:
    type: sql
    expression: "EXTRACT(YEAR FROM {field})"

  extract_month:
    type: sql
    expression: "EXTRACT(MONTH FROM {field})"

  extract_day:
    type: sql
    expression: "EXTRACT(DAY FROM {field})"

  calculate_age:
    type: sql
    expression: "EXTRACT(YEAR FROM AGE({current_date}, {birth_date}))"

  standardize_gender:
    type: sql
    expression: "UPPER(TRIM({field}))"

  clean_icd_code:
    type: sql
    expression: "REGEXP_REPLACE({field}, '[^A-Z0-9.]', '', 'g')"

# Data quality checks - Comprehensive validation suite
quality_checks:
  - name: person_count_check
    description: Verify person count matches source
    query: |
      SELECT COUNT(*) as target_count,
             (SELECT COUNT(*) FROM patients WHERE birth_date IS NOT NULL AND gender IS NOT NULL) as source_count
      FROM test_cdm.person
    expected: target_count = source_count
    severity: error

  - name: orphan_visits_check
    description: Check for visits without valid person
    query: |
      SELECT COUNT(*) as orphan_count
      FROM test_cdm.visit_occurrence v
      LEFT JOIN test_cdm.person p ON v.person_id = p.person_id
      WHERE p.person_id IS NULL
    expected: orphan_count = 0
    severity: error

  - name: date_consistency_check
    description: Verify dates are logically consistent
    query: |
      SELECT COUNT(*) as invalid_dates
      FROM test_cdm.visit_occurrence
      WHERE visit_start_date > visit_end_date
    expected: invalid_dates = 0
    severity: error

  - name: birth_date_validation
    description: Verify birth dates are reasonable
    query: |
      SELECT COUNT(*) as invalid_birth_dates
      FROM test_cdm.person
      WHERE year_of_birth < 1900 OR year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE)
    expected: invalid_birth_dates = 0
    severity: warning

  - name: condition_concept_validation
    description: Verify condition concepts are valid
    query: |
      SELECT COUNT(*) as invalid_concepts
      FROM test_cdm.condition_occurrence
      WHERE condition_concept_id = 0
    expected: invalid_concepts <= 5  # Allow some unmapped concepts
    severity: warning

  - name: drug_exposure_validation
    description: Verify drug exposures have valid dates and quantities
    query: |
      SELECT COUNT(*) as invalid_drugs
      FROM test_cdm.drug_exposure
      WHERE drug_exposure_start_date > drug_exposure_end_date
         OR quantity <= 0
         OR days_supply <= 0
    expected: invalid_drugs = 0
    severity: error

  - name: measurement_range_validation
    description: Verify measurements are within reasonable ranges
    query: |
      SELECT COUNT(*) as out_of_range_measurements
      FROM test_cdm.measurement
      WHERE (range_low IS NOT NULL AND value_as_number < range_low * 0.1)
         OR (range_high IS NOT NULL AND value_as_number > range_high * 10)
    expected: out_of_range_measurements <= 10  # Allow some outliers
    severity: warning

  - name: referential_integrity_check
    description: Verify foreign key relationships
    query: |
      SELECT
        (SELECT COUNT(*) FROM test_cdm.condition_occurrence co
         LEFT JOIN test_cdm.person p ON co.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.drug_exposure de
         LEFT JOIN test_cdm.person p ON de.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.procedure_occurrence po
         LEFT JOIN test_cdm.person p ON po.person_id = p.person_id
         WHERE p.person_id IS NULL) as orphan_records
    expected: orphan_records = 0
    severity: error

  - name: duplicate_records_check
    description: Check for duplicate records in key tables
    query: |
      SELECT
        (SELECT COUNT(*) - COUNT(DISTINCT person_id) FROM test_cdm.person) +
        (SELECT COUNT(*) - COUNT(DISTINCT visit_occurrence_id) FROM test_cdm.visit_occurrence) +
        (SELECT COUNT(*) - COUNT(DISTINCT condition_occurrence_id) FROM test_cdm.condition_occurrence) as duplicate_count
    expected: duplicate_count = 0
    severity: error

  - name: vocabulary_coverage_check
    description: Check vocabulary mapping coverage
    query: |
      SELECT
        ROUND(
          (SELECT COUNT(*) FROM test_cdm.condition_occurrence WHERE condition_concept_id > 0) * 100.0 /
          NULLIF((SELECT COUNT(*) FROM test_cdm.condition_occurrence), 0), 2
        ) as condition_mapping_percentage
    expected: condition_mapping_percentage >= 80.0
    severity: warning

# Error handling configuration
error_handling:
  max_error_count: 100
  error_log_file: "etl_errors.log"
  continue_on_validation_error: false
  email_on_failure: false
  retry_failed_batches: true
  quarantine_invalid_records: true

# Performance monitoring
performance_monitoring:
  enable_metrics: true
  metrics_interval_seconds: 30
  log_slow_queries: true
  slow_query_threshold_ms: 5000
  enable_profiling: true
  profile_memory_usage: true
