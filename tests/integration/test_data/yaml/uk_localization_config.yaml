# UK Healthcare Localization Configuration
# Used by UK-specific integration tests

uk_localization:
  # Regional settings
  regional:
    locale: "en_GB"
    timezone: "Europe/London"
    country_code: "GB"
    language: "en"
    currency: "GBP"
    
  # Date and time formatting
  datetime:
    date_format: "%d/%m/%Y"
    datetime_format: "%d/%m/%Y %H:%M:%S"
    time_format: "%H:%M:%S"
    preferred_formats:
      - "%d/%m/%Y"        # 15/01/2023
      - "%d-%m-%Y"        # 15-01-2023  
      - "%d.%m.%Y"        # 15.01.2023
      - "%d %B %Y"        # 15 January 2023
      - "%d %b %Y"        # 15 Jan 2023
    iso_fallback: true
    
  # Number and currency formatting
  numeric:
    decimal_separator: "."
    thousands_separator: ","
    currency_symbol: "£"
    currency_position: "before"  # £1,234.56
    decimal_places: 2
    negative_format: "-£1,234.56"
    
  # Temperature and measurements
  measurements:
    temperature_unit: "celsius"
    temperature_symbol: "°C"
    distance_unit: "metric"     # metres, kilometres
    weight_unit: "metric"       # kilograms, grams
    height_unit: "mixed"        # feet/inches or metres
    
  # UK Healthcare specific identifiers
  healthcare_identifiers:
    nhs_number:
      enabled: true
      format: "#### ### ###"
      validation: true
      checksum_validation: true
      pattern: "^[0-9]{10}$"
      
    chi_number:  # Scotland
      enabled: true
      format: "## ## ## ####"
      pattern: "^[0-9]{10}$"
      
    hc_number:   # Northern Ireland
      enabled: true
      format: "### ### ###"
      pattern: "^[0-9]{9}$"
      
  # UK Postal codes
  postal_codes:
    validation: true
    normalization: true
    formats:
      - "A9 9AA"     # M1 1AA
      - "A99 9AA"    # M60 1NW
      - "AA9 9AA"    # SW1A 1AA
      - "AA99 9AA"   # W1A 1AA
      - "A9A 9AA"    # W1P 1HQ
      - "AA9A 9AA"   # EC1A 1BB
    pattern: "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$"
    examples:
      - "SW1A 1AA"   # Buckingham Palace
      - "EC1A 1BB"   # Bank of England
      - "M1 1AA"     # Manchester
      - "B33 8TH"    # Birmingham
      - "W1A 0AX"    # BBC Broadcasting House
      
  # UK Phone numbers
  phone_numbers:
    validation: true
    formats:
      - "01### ######"    # Geographic
      - "02# #### ####"   # Geographic (London, etc.)
      - "03## ### ####"   # Non-geographic
      - "07### ######"    # Mobile
      - "0800 ### ####"   # Freephone
      - "0845 ### ####"   # Local rate
    international_prefix: "+44"
    
# Healthcare data standards
healthcare_standards:
  # SNOMED CT (UK Extension)
  snomed_ct:
    enabled: true
    edition: "uk_extension"
    version: "********"
    
  # dm+d (Dictionary of medicines and devices)
  dmd:
    enabled: true
    version: "current"
    
  # Read Codes (legacy support)
  read_codes:
    enabled: true
    version: "v3"
    
  # ICD-10 (UK specific codes)
  icd10:
    enabled: true
    edition: "uk"
    
# Test data generation rules
test_data:
  patient_data:
    # UK-specific patient names
    first_names:
      male: ["James", "Robert", "John", "Michael", "David", "William", "Richard", "Charles", "Joseph", "Thomas"]
      female: ["Mary", "Patricia", "Jennifer", "Linda", "Elizabeth", "Barbara", "Susan", "Jessica", "Sarah", "Karen"]
      
    surnames: ["Smith", "Jones", "Taylor", "Brown", "Williams", "Wilson", "Johnson", "Davies", "Robinson", "Wright"]
    
    # UK address components
    address_components:
      street_names: ["High Street", "Church Lane", "Victoria Road", "King's Road", "Queen's Avenue"]
      cities: ["London", "Manchester", "Birmingham", "Glasgow", "Liverpool", "Leeds", "Sheffield", "Bristol"]
      counties: ["Essex", "Kent", "Surrey", "Yorkshire", "Lancashire", "Devon", "Cornwall", "Norfolk"]
      
    # Sample NHS numbers (test only - not real)
    sample_nhs_numbers:
      - "**********"
      - "**********"
      - "**********"
      - "**********"
      - "**********"
      
    # Sample postcodes
    sample_postcodes:
      - "SW1A 1AA"
      - "EC1A 1BB"
      - "M1 1AA"
      - "B33 8TH"
      - "W1A 0AX"
      - "NW1 2DB"
      - "SE1 9RT"
      - "LS1 4AP"
      
# Validation rules
validation_rules:
  nhs_number:
    description: "NHS Number validation using Modulus 11 check"
    algorithm: "modulus_11"
    steps:
      - "Multiply digits 1-9 by weights 10,9,8,7,6,5,4,3,2"
      - "Sum the products"
      - "Divide sum by 11 and get remainder"
      - "Subtract remainder from 11 to get check digit"
      - "If result is 11, check digit is 0"
      - "If result is 10, NHS number is invalid"
      
  postcode:
    description: "UK Postcode format validation"
    rules:
      - "First part: 1-2 letters + 1-2 digits + optional letter"
      - "Second part: 1 digit + 2 letters"
      - "Space between parts"
      - "All letters uppercase"
      - "No excluded letter combinations"
      
  chi_number:
    description: "CHI Number validation (Scotland)"
    format: "DDMMYY-NNNS"
    validation: "date_birth_plus_sequence"
    
# GDPR and data protection
data_protection:
  gdpr_compliance: true
  data_minimization: true
  consent_tracking: false  # For test data
  anonymization:
    enabled: false  # For testing
    methods: ["pseudonymization", "aggregation", "masking"]
    
  audit_requirements:
    log_access: true
    log_modifications: true
    retention_period_days: 2555  # 7 years
    
# NHS organisational data
nhs_organizations:
  structure:
    - "NHS England"
    - "NHS Scotland" 
    - "NHS Wales"
    - "Health and Social Care Northern Ireland"
    
  trusts:
    england:
      - "Imperial College Healthcare NHS Trust"
      - "Guy's and St Thomas' NHS Foundation Trust"
      - "The Royal Marsden NHS Foundation Trust"
      
  ccgs:  # Clinical Commissioning Groups (historical)
    - "NHS Central London CCG"
    - "NHS North West London CCG"
    - "NHS South East London CCG"
    
  icbs:  # Integrated Care Boards (current)
    - "NHS North West London ICB"
    - "NHS South East London ICB"
    - "NHS North Central London ICB"
    
# Professional registration
professional_codes:
  gmc:  # General Medical Council
    format: "[0-9]{7}"
    description: "GMC Reference Number"
    
  nmc:  # Nursing and Midwifery Council  
    format: "[0-9]{2}[A-Z][0-9]{4}[A-Z]"
    description: "NMC PIN"
    
  gdc:  # General Dental Council
    format: "[0-9]{5,6}"
    description: "GDC Number" 