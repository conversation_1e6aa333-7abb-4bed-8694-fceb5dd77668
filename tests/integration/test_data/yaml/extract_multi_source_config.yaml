# Multi-Source Extraction Integration Test Configuration
# Used by multi-source extraction integration tests

multi_source_extractor:
  # Parallel processing configuration
  parallel_processing:
    enabled: true
    thread_count: 4
    max_concurrent_extractors: 8
    preserve_order: false
    load_balancing: "round_robin"  # Options: round_robin, least_loaded, priority
    
  # Coordination and synchronization
  coordination:
    batch_synchronization: true
    shared_memory_buffer_size_mb: 256
    inter_extractor_communication: true
    progress_reporting_interval_ms: 1000
    
  # Error handling and resilience
  error_handling:
    failure_tolerance: "continue"  # Options: fail_fast, continue, retry
    max_failed_extractors: 2
    retry_failed_sources: true
    retry_delay_ms: 5000
    max_retry_attempts: 3
    
  # Performance optimization
  performance:
    memory_pool_size_mb: 512
    io_optimization: true
    prefetch_enabled: true
    cache_extracted_data: false
    compression_enabled: false

# Source definitions for testing
test_sources:
  csv_patients:
    type: "csv"
    filepath: "tests/integration/test_data/csv/patients.csv"
    config:
      delimiter: ","
      has_header: true
      encoding: "UTF-8"
    priority: 1
    
  json_patients:
    type: "json"
    filepath: "tests/integration/test_data/json/patient_records.json"
    config:
      root_path: ""
      flatten_nested: true
    priority: 2
    
  jsonl_encounters:
    type: "jsonl"
    filepath: "tests/integration/test_data/json/clinical_data.jsonl"
    config:
      streaming: true
      buffer_size: 8192
    priority: 3
    
  database_observations:
    type: "database"
    connection: "test_db"
    config:
      table: "test_observations"
      columns: ["observation_id", "patient_id", "observation_date", "value_as_number"]
    priority: 4

# Test scenarios configuration
test_scenarios:
  parallel_extraction:
    description: "Parallel extraction from multiple sources simultaneously"
    sources: ["csv_patients", "json_patients", "jsonl_encounters"]
    expected_total_records: 15
    thread_count: 3
    
  sequential_extraction:
    description: "Sequential extraction from multiple sources for comparison"
    sources: ["csv_patients", "json_patients", "jsonl_encounters"]
    parallel_processing: false
    expected_total_records: 15
    
  error_handling_multi_source:
    description: "Error handling in multi-source scenarios"
    sources: ["csv_patients", "json_patients", "invalid_source"]
    failure_tolerance: "continue"
    expected_successful_sources: 2
    
  performance_comparison:
    description: "Performance comparison between sequential and parallel extraction"
    sources: ["csv_patients", "json_patients", "jsonl_encounters"]
    measure_performance: true
    benchmark_iterations: 3
    
  cross_format_consistency:
    description: "Cross-format data consistency validation"
    sources: ["csv_patients", "json_patients"]
    validate_consistency: true
    consistency_fields: ["patient_id", "first_name", "last_name", "age", "gender"]
    
  load_balancing:
    description: "Load balancing across multiple extraction threads"
    sources: ["csv_patients", "json_patients", "jsonl_encounters", "database_observations"]
    thread_count: 2
    load_balancing_strategy: "least_loaded"
    
  priority_based_extraction:
    description: "Priority-based extraction ordering"
    sources: ["csv_patients", "json_patients", "jsonl_encounters"]
    respect_priority: true
    expected_order: ["csv_patients", "json_patients", "jsonl_encounters"]
    
  memory_management:
    description: "Memory management during multi-source extraction"
    sources: ["csv_patients", "json_patients", "jsonl_encounters"]
    memory_limit_mb: 128
    track_memory_usage: true
    
  streaming_coordination:
    description: "Coordination of streaming and batch extractors"
    sources: ["csv_patients", "jsonl_encounters"]
    mixed_processing_modes: true
    synchronization_points: 3
    
  fault_tolerance:
    description: "Fault tolerance with source failures"
    sources: ["csv_patients", "failing_source", "json_patients"]
    failure_tolerance: "continue"
    expected_failures: 1
    recovery_mechanism: "skip_and_continue"

# Data quality and validation
data_quality:
  cross_source_validation:
    enabled: true
    duplicate_detection: true
    consistency_checks: true
    schema_validation: false
    
  field_mapping:
    patient_id: ["id", "patient_id", "patientId"]
    first_name: ["first_name", "firstName", "fname"]
    last_name: ["last_name", "lastName", "lname"]
    birth_date: ["birth_date", "birthDate", "dob"]
    gender: ["gender", "sex"]
    
  data_normalization:
    enabled: true
    case_normalization: "preserve"
    date_format_standardization: true
    numeric_precision: 2
    
# UK Healthcare specific settings
uk_healthcare:
  localization:
    locale: "en_GB"
    timezone: "Europe/London"
    date_format: "%d/%m/%Y"
    currency: "GBP"
    
  validation:
    nhs_number_validation: true
    postcode_validation: true
    uk_date_format_preference: true
    
  data_protection:
    gdpr_compliance: true
    data_anonymization: false
    audit_trail: true

# Performance monitoring
monitoring:
  metrics_collection:
    enabled: true
    collection_interval_ms: 5000
    detailed_timing: true
    memory_tracking: true
    
  thresholds:
    max_extraction_time_seconds: 300
    max_memory_usage_mb: 1024
    min_throughput_records_per_second: 100
    max_error_rate_percent: 5
    
  alerts:
    performance_degradation: true
    memory_threshold_exceeded: true
    error_rate_exceeded: true
    source_failure: true

# Integration patterns
integration_patterns:
  fan_out:
    description: "One source distributed to multiple processors"
    enabled: false
    
  fan_in:
    description: "Multiple sources merged into single output"
    enabled: true
    merge_strategy: "interleave"
    
  pipeline:
    description: "Sequential processing pipeline"
    enabled: false
    stages: ["extract", "validate", "transform"]
    
  scatter_gather:
    description: "Parallel processing with result aggregation"
    enabled: true
    aggregation_strategy: "concat"

# Testing framework integration
testing:
  mock_sources:
    enabled: true
    generate_test_data: true
    simulate_failures: true
    
  benchmarking:
    enabled: true
    baseline_comparison: true
    performance_regression_detection: true
    
  stress_testing:
    max_concurrent_sources: 10
    sustained_load_duration_seconds: 60
    memory_pressure_testing: true 