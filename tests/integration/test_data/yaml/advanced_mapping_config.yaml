# Advanced mapping configuration with complex transformations
mappings:
  person_complex:
    source_table: test_data_complex_scenarios.csv
    target_table: person
    pre_process_sql: |
      SELECT * FROM source
      WHERE patient_id IS NOT NULL
      AND LENGTH(patient_id) > 0

    filters: |
      birth_date IS NOT NULL OR
      (birth_date IS NULL AND notes LIKE '%Missing birth date%')

    validations:
      - field: birth_date
        type: date_range
        min: "1900-01-01"
        max: "2024-12-31"
        allow_null: true

      - field: gender
        type: in_list
        values: ["M", "F", "1", "2", "X"]

      - field: death_date
        type: custom
        rule: "death_date IS NULL OR death_date > birth_date"

    transformations:
      - source_columns: [patient_id]
        target_column: person_id
        type: direct

      - source_columns: [birth_date]
        target_column: birth_datetime
        type: date_transform
        parameters:
          input_formats:
            - "%Y-%m-%d"
            - "%m/%d/%Y"
            - "%d-%m-%Y"
          output_format: "%Y-%m-%d %H:%M:%S"
          default_time: "00:00:00"
          timezone: "UTC"

      - source_columns: [birth_date]
        target_column: year_of_birth
        type: date_calculation
        parameters:
          extract: year

      - source_columns: [birth_date]
        target_column: month_of_birth
        type: date_calculation
        parameters:
          extract: month
          allow_null: true

      - source_columns: [gender]
        target_column: gender_concept_id
        type: conditional
        parameters:
          conditions:
            - field: gender
              operator: equals
              value: "M"
              then_value: 8507
            - field: gender
              operator: equals
              value: "1"
              then_value: 8507
            - field: gender
              operator: equals
              value: "F"
              then_value: 8532
            - field: gender
              operator: equals
              value: "2"
              then_value: 8532
            - field: gender
              operator: equals
              value: "X"
              then_value: 0
          default_value: 0

      - source_columns: [race, ethnicity]
        target_column: race_source_value
        type: string_concatenation
        parameters:
          separator: " - "
          skip_empty: true

      - source_columns: [death_date, birth_date]
        target_column: death_datetime
        type: custom
        parameters:
          function: validate_death_date
          validation: "death_date > birth_date"

  measurement_complex:
    source_table: lab_results_complex.csv
    target_table: measurement

    transformations:
      - source_columns: [result_value, unit]
        target_column: value_as_number
        type: numeric_transform
        parameters:
          unit_conversion:
            mg/dL_to_mmol/L: 0.0555  # For glucose
            g/dL_to_g/L: 10.0        # For proteins

      - source_columns: [result_value]
        target_column: value_as_concept_id
        type: conditional
        parameters:
          conditions:
            - field: result_value
              operator: equals
              value: "Positive"
              then_value: 45877994  # Positive finding
            - field: result_value
              operator: equals
              value: "Negative"
              then_value: 45878245  # Negative finding