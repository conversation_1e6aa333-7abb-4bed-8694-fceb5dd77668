# Integration Test Configuration Files

This directory contains YAML configuration files for various integration test scenarios across the OMOP CDM ETL pipeline components.

## File Organization

### 📊 Extract Library Configurations
These configurations are specifically for testing the extract library components:

| File | Purpose | Test Scope |
|------|---------|------------|
| `extract_csv_config.yaml` | CSV extractor configuration | CSV parsing, UK localization, column selection |
| `extract_json_config.yaml` | JSON/JSONL extractor configuration | JSON parsing, nested objects, streaming |
| `extract_database_config.yaml` | Database extractor configuration | SQL queries, connections, transactions |
| `extract_multi_source_config.yaml` | Multi-source extraction configuration | Parallel processing, coordination |
| `uk_localization_config.yaml` | UK healthcare localization | NHS numbers, postcodes, GDPR compliance |
| `extract_integration_master_config.yaml` | Master extract test orchestration | All extract library tests |

### ⚙️ Core Pipeline Configurations  
These configurations are for testing the core library's ETL pipeline functionality:

| File | Purpose | Test Scope |
|------|---------|------------|
| `test_config.yaml` | Comprehensive pipeline integration test | Full ETL pipeline with all stages |
| `fullpipeline.yaml` | Complete ETL mapping configuration | Database-to-database transformation |
| `quick_config.yaml` | Fast execution performance test | High-throughput, minimal delays |
| `slow_config.yaml` | Long-running pipeline test | Checkpointing, slow processing |
| `failing_config.yaml` | Failure scenario testing | Error handling, recovery |
| `pauseable_config.yaml` | Pauseable job testing | Job control, resume capability |
| `checkpoint_config.yaml` | Checkpointing functionality | State persistence, recovery |
| `stats_config.yaml` | Statistics and monitoring | Metrics collection, reporting |
| `priority_test_config.yaml` | Priority-based processing | Job scheduling, prioritization |
| `priority_config.yaml` | Basic priority configuration | Simple priority settings |

### 🗂️ Data Mapping Configurations
Legacy configurations for data transformation mapping:

| File | Purpose | Test Scope |
|------|---------|------------|
| `mapping_config.yaml` | Standard data mapping rules | Field transformations, vocabulary mapping |
| `advanced_mapping_config.yaml` | Complex mapping scenarios | Advanced transformations, complex rules |

### 🚀 Enhanced Test Configurations (NEW)
Advanced configurations for comprehensive testing scenarios:

| File | Purpose | Test Scope |
|------|---------|------------|
| `enhanced_comprehensive_e2e_config.yaml` | Enhanced E2E with ALL OMOP tables | Complete OMOP CDM coverage including missing tables |
| `enhanced_performance_test_config.yaml` | Advanced performance testing | Load, stress, and benchmark testing |
| `enhanced_edge_cases_config.yaml` | Comprehensive edge case testing | Data quality, business logic, and error scenarios |

## Configuration Categories

### 🎯 **Performance Testing**
- `quick_config.yaml` - High-speed processing (batch_size: 1000, minimal delays)
- `slow_config.yaml` - Long-running jobs (batch_size: 100, extended delays)
- `stats_config.yaml` - Performance monitoring and metrics collection

### 🔧 **Reliability Testing**
- `failing_config.yaml` - Failure injection and error handling
- `checkpoint_config.yaml` - State persistence and recovery
- `pauseable_config.yaml` - Job pause/resume functionality

### 📋 **Functional Testing**
- `test_config.yaml` - Complete end-to-end pipeline testing
- `fullpipeline.yaml` - Full database transformation workflow
- `priority_test_config.yaml` - Job prioritization and scheduling

### 🌍 **Localization Testing**
- `uk_localization_config.yaml` - UK healthcare standards (NHS, GDPR)
- Extract library configs with UK regional settings

### 🚀 **Enhanced Testing (NEW)**
- `enhanced_comprehensive_e2e_config.yaml` - Complete OMOP CDM coverage with all tables
- `enhanced_performance_test_config.yaml` - Advanced performance and load testing
- `enhanced_edge_cases_config.yaml` - Comprehensive edge case and error scenario testing

## Usage Examples

### Extract Library Tests
```bash
# Run CSV extraction tests
./extract_integration_tests --config=tests/integration/test_data/yaml/extract_csv_config.yaml

# Run multi-source extraction
./extract_integration_tests --config=tests/integration/test_data/yaml/extract_multi_source_config.yaml

# Run all extract tests
./extract_integration_tests --config=tests/integration/test_data/yaml/extract_integration_master_config.yaml
```

### Core Pipeline Tests
```bash
# Run quick performance test
./pipeline_integration_tests --config=tests/integration/test_data/yaml/quick_config.yaml

# Run failure scenario test
./pipeline_integration_tests --config=tests/integration/test_data/yaml/failing_config.yaml

# Run comprehensive pipeline test
./pipeline_integration_tests --config=tests/integration/test_data/yaml/test_config.yaml

# Run enhanced comprehensive E2E test
./pipeline_integration_tests --config=tests/integration/test_data/yaml/enhanced_comprehensive_e2e_config.yaml

# Run enhanced performance test
./pipeline_integration_tests --config=tests/integration/test_data/yaml/enhanced_performance_test_config.yaml

# Run enhanced edge cases test
./pipeline_integration_tests --config=tests/integration/test_data/yaml/enhanced_edge_cases_config.yaml
```

## Test Environment Requirements

### For Extract Library Tests
- Docker environment with `omop-etl-dev` container
- Test CSV and JSON files in `tests/integration/test_data/`
- Optional: PostgreSQL/MySQL for database tests

### For Core Pipeline Tests  
- PostgreSQL database for full pipeline tests
- Test data loaded in source database
- Checkpoint directory: `/tmp/omop-etl/checkpoints`
- Sufficient disk space for large batch processing

## Configuration File Structure

### Common Elements
All configuration files follow these patterns:

```yaml
# Pipeline/Component configuration
pipeline:
  batch_size: 1000
  error_threshold: 0.01
  enable_checkpointing: true
  
# Monitoring and logging
monitoring:
  metrics:
    enabled: true
  logging:
    level: "INFO"
    
# UK Healthcare compliance (where applicable)
uk_healthcare:
  nhs_number_validation: true
  postcode_validation: true
  gdpr_compliance: true
```

### Extract Library Specific
```yaml
# Extractor type and settings
csv_extractor:
  delimiter: ","
  has_header: true
  locale: "en_GB"
  
# Test scenarios
test_scenarios:
  basic_extraction:
    description: "Basic functionality test"
    expected_records: 10
```

### Core Pipeline Specific
```yaml
# Pipeline stages
stages:
  - name: "extract"
    type: "extractor"
    config:
      type: "csv"
      file_path: "test_data.csv"
      
  - name: "transform" 
    type: "transformer"
    config:
      type: "identity"
      
  - name: "load"
    type: "loader"
    config:
      type: "database"
```

## Maintenance Notes

### Adding New Configurations
1. Follow the naming convention: `{component}_{purpose}_config.yaml`
2. Include comprehensive documentation within the file
3. Add entry to this README with purpose and scope
4. Update master configuration files if applicable

### Enhanced Test Data Files
The following new test data files support the enhanced configurations:

| File | Purpose | Tables Supported |
|------|---------|------------------|
| `enhanced_test_data_locations.csv` | Location table test data | location |
| `enhanced_test_data_care_sites.csv` | Care site table test data | care_site |
| `enhanced_test_data_providers.csv` | Provider table test data | provider |
| `enhanced_test_data_visit_details.csv` | Visit detail table test data | visit_detail |
| `enhanced_test_data_device_exposures.csv` | Device exposure table test data | device_exposure |
| `enhanced_test_data_costs.csv` | Cost table test data | cost |
| `enhanced_test_data_payer_plan_periods.csv` | Payer plan period table test data | payer_plan_period |

### Updating Existing Configurations
1. Maintain backward compatibility where possible
2. Update version numbers in configuration metadata
3. Test changes against existing integration tests
4. Update documentation to reflect changes

## Integration with CI/CD

These configurations are used by:
- GitHub Actions workflows (`.github/workflows/`)
- Docker Compose test environments (`docker-compose.test.yml`)
- CMake test targets (`CMakeLists.txt`)
- Jenkins pipelines (if configured)

## Troubleshooting

### Common Issues
1. **File path errors**: Ensure relative paths are correct from project root
2. **Database connections**: Check database availability and credentials
3. **Permission errors**: Verify write access to checkpoint directories
4. **Memory issues**: Adjust batch sizes for available system memory

### Debug Configuration
For debugging failed tests:
1. Enable verbose logging in configuration
2. Reduce batch sizes to isolate issues
3. Use single-threaded processing
4. Check specific error patterns in logs

---

**Last Updated**: January 2025  
**Maintained By**: OMOP CDM ETL Team  
**Review Schedule**: Quarterly updates recommended 