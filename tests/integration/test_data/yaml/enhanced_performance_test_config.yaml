# Enhanced Performance Test Configuration
# Comprehensive performance testing for OMOP ETL pipeline

# Database connections for performance testing
source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_clinical_data_perf
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    connect_timeout: 60
    application_name: omop_etl_perf_test
    pool_size: 20
    max_overflow: 10

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_omop_cdm_perf
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    schema: test_cdm_perf
    application_name: omop_etl_perf_test
    pool_size: 20
    max_overflow: 10

# Performance test scenarios
performance_scenarios:
  
  # High-volume data processing
  high_volume:
    description: "Process large volumes of data efficiently"
    batch_size: 10000
    parallel_workers: 8
    expected_throughput: 50000  # records per minute
    max_memory_usage: 4096      # MB
    timeout_minutes: 60
    
    data_volume:
      patients: 100000
      encounters: 500000
      conditions: 1000000
      drugs: 800000
      procedures: 300000
      measurements: 2000000
      observations: 500000
    
    performance_targets:
      extraction_rate: 100000   # records/minute
      transformation_rate: 80000 # records/minute
      loading_rate: 60000       # records/minute
      total_processing_time: 30  # minutes

  # Concurrent processing
  concurrent_processing:
    description: "Test concurrent ETL jobs"
    concurrent_jobs: 5
    batch_size: 5000
    parallel_workers: 4
    expected_throughput: 25000  # records per minute per job
    max_memory_usage: 2048      # MB per job
    timeout_minutes: 45
    
    job_configurations:
      - job_name: "person_etl"
        priority: 1
        tables: ["person", "observation_period"]
        
      - job_name: "visit_etl"
        priority: 2
        tables: ["visit_occurrence", "visit_detail"]
        
      - job_name: "clinical_etl"
        priority: 3
        tables: ["condition_occurrence", "drug_exposure", "procedure_occurrence"]
        
      - job_name: "measurement_etl"
        priority: 4
        tables: ["measurement", "observation"]
        
      - job_name: "supporting_etl"
        priority: 5
        tables: ["death", "note", "device_exposure"]

  # Memory optimization
  memory_optimization:
    description: "Test memory-efficient processing"
    batch_size: 1000
    parallel_workers: 2
    max_memory_usage: 512       # MB
    enable_streaming: true
    enable_compression: true
    timeout_minutes: 90
    
    optimization_settings:
      use_temp_tables: true
      enable_indexing: false    # During load
      enable_constraints: false # During load
      cleanup_interval: 10000   # records
      
    expected_metrics:
      memory_usage_peak: 400    # MB
      disk_usage: 1024          # MB
      processing_time: 120      # minutes

  # Network performance
  network_performance:
    description: "Test network-bound scenarios"
    batch_size: 2000
    parallel_workers: 6
    network_latency: 100        # ms
    bandwidth_limit: 100        # Mbps
    timeout_minutes: 75
    
    network_settings:
      connection_pooling: true
      connection_reuse: true
      compression: true
      retry_on_failure: true
      circuit_breaker: true
      
    expected_metrics:
      network_throughput: 50    # Mbps
      connection_errors: 0.01   # error rate
      retry_count: 5            # max retries

  # CPU-intensive processing
  cpu_intensive:
    description: "Test CPU-intensive transformations"
    batch_size: 500
    parallel_workers: 16
    cpu_utilization_target: 80  # percent
    timeout_minutes: 60
    
    transformation_complexity:
      enable_complex_joins: true
      enable_aggregations: true
      enable_window_functions: true
      enable_custom_functions: true
      
    expected_metrics:
      cpu_utilization: 75       # percent
      processing_time: 45       # minutes
      transformation_errors: 0.001 # error rate

  # I/O performance
  io_performance:
    description: "Test I/O-bound scenarios"
    batch_size: 5000
    parallel_workers: 4
    io_operations_per_second: 1000
    timeout_minutes: 60
    
    io_settings:
      use_bulk_operations: true
      enable_buffering: true
      buffer_size: 8192         # KB
      prefetch_size: 4096       # KB
      
    expected_metrics:
      read_throughput: 100      # MB/s
      write_throughput: 50      # MB/s
      io_wait_time: 10          # percent

# Performance monitoring configuration
performance_monitoring:
  enable_real_time_monitoring: true
  metrics_collection_interval: 10  # seconds
  enable_profiling: true
  enable_tracing: true
  
  metrics_to_track:
    - cpu_usage
    - memory_usage
    - disk_io
    - network_io
    - database_connections
    - query_execution_time
    - batch_processing_time
    - error_rates
    - throughput_rates
    
  alerting:
    cpu_threshold: 90           # percent
    memory_threshold: 85        # percent
    disk_threshold: 80          # percent
    error_rate_threshold: 0.05  # 5%
    timeout_threshold: 300      # seconds

# Load testing configuration
load_testing:
  ramp_up_time: 300             # seconds
  steady_state_time: 1800       # seconds (30 minutes)
  ramp_down_time: 300           # seconds
  
  load_patterns:
    - pattern: "constant"
      description: "Constant load"
      users: 10
      duration: 600
      
    - pattern: "spike"
      description: "Sudden spike in load"
      users: 50
      duration: 300
      
    - pattern: "gradual"
      description: "Gradual increase"
      users_start: 5
      users_end: 25
      duration: 900
      
    - pattern: "burst"
      description: "Burst traffic"
      users: 100
      duration: 60
      frequency: 300

# Stress testing configuration
stress_testing:
  max_concurrent_users: 100
  max_data_volume: 10000000     # 10M records
  max_processing_time: 7200     # 2 hours
  
  stress_scenarios:
    - scenario: "memory_pressure"
      description: "Test under memory pressure"
      memory_limit: 1024        # MB
      expected_behavior: "graceful_degradation"
      
    - scenario: "cpu_pressure"
      description: "Test under CPU pressure"
      cpu_limit: 50             # percent
      expected_behavior: "slower_processing"
      
    - scenario: "network_pressure"
      description: "Test under network pressure"
      bandwidth_limit: 10       # Mbps
      expected_behavior: "timeout_handling"
      
    - scenario: "disk_pressure"
      description: "Test under disk pressure"
      disk_space_limit: 1024    # MB
      expected_behavior: "error_handling"

# Benchmarking configuration
benchmarking:
  baseline_configuration:
    batch_size: 1000
    parallel_workers: 4
    memory_limit: 2048          # MB
    
  benchmark_scenarios:
    - name: "baseline"
      description: "Baseline performance"
      configuration: "baseline"
      
    - name: "optimized"
      description: "Optimized configuration"
      batch_size: 5000
      parallel_workers: 8
      memory_limit: 4096
      
    - name: "minimal"
      description: "Minimal resource usage"
      batch_size: 500
      parallel_workers: 2
      memory_limit: 1024
      
  success_criteria:
    - metric: "throughput"
      baseline: 10000           # records/minute
      target: 20000             # records/minute
      minimum: 5000             # records/minute
      
    - metric: "memory_usage"
      baseline: 1024            # MB
      target: 2048              # MB
      maximum: 4096             # MB
      
    - metric: "processing_time"
      baseline: 60              # minutes
      target: 30                # minutes
      maximum: 120              # minutes

# Error handling for performance tests
error_handling:
  max_error_count: 1000
  error_threshold: 0.05         # 5%
  continue_on_error: true
  retry_failed_batches: true
  max_retry_attempts: 3
  retry_delay_seconds: 10
  
  performance_error_handling:
    timeout_handling: "abort_job"
    memory_error_handling: "reduce_batch_size"
    network_error_handling: "retry_with_backoff"
    disk_error_handling: "cleanup_and_retry"

# Reporting configuration
reporting:
  generate_performance_report: true
  report_format: "html"
  include_charts: true
  include_recommendations: true
  
  report_sections:
    - "executive_summary"
    - "performance_metrics"
    - "resource_utilization"
    - "bottleneck_analysis"
    - "optimization_recommendations"
    - "comparison_with_baseline"
    
  export_formats:
    - "html"
    - "pdf"
    - "json"
    - "csv" 