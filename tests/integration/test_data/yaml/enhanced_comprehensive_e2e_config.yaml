# Enhanced OMOP ETL Pipeline Configuration - Comprehensive E2E Test Version
# This configuration file defines the mappings from source data to OMOP CDM tables
# Enhanced for comprehensive end-to-end testing with ALL OMOP tables including missing ones

# Database connections
source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_clinical_data
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    connect_timeout: 30
    application_name: omop_etl_e2e_test

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_omop_cdm
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    schema: test_cdm
    application_name: omop_etl_e2e_test

# ETL settings
etl_settings:
  batch_size: 100  # Smaller for testing
  parallel_workers: 2
  validation_mode: strict  # strict, warning, or skip
  error_threshold: 0.05    # Allow 5% error rate for testing
  checkpoint_enabled: true
  checkpoint_interval: 60   # seconds - shorter for testing
  log_level: debug
  enable_profiling: true
  enable_monitoring: true
  max_retry_attempts: 3
  retry_delay_seconds: 5
  continue_on_error: false
  enable_data_quality_checks: true

# Enhanced vocabulary mappings with additional concepts
vocabulary_mappings:
  Gender:
    "Male": 8507
    "M": 8507
    "MALE": 8507
    "1": 8507
    "Female": 8532
    "F": 8532
    "FEMALE": 8532
    "2": 8532
    "Unknown": 0
    "U": 0
    "NULL": 0
    "": 0

  Race:
    "White": 8527
    "Caucasian": 8527
    "WHITE": 8527
    "Black or African American": 8516
    "Black": 8516
    "African American": 8516
    "BLACK": 8516
    "Asian": 8515
    "ASIAN": 8515
    "American Indian or Alaska Native": 8657
    "Native American": 8657
    "Native Hawaiian or Other Pacific Islander": 8557
    "Pacific Islander": 8557
    "Mixed": 8522
    "Other": 8522
    "Unknown": 0
    "NULL": 0
    "": 0

  Ethnicity:
    "Hispanic or Latino": 38003563
    "Hispanic": 38003563
    "Latino": 38003563
    "HISPANIC": 38003563
    "Not Hispanic or Latino": 38003564
    "Non-Hispanic": 38003564
    "Not Hispanic": 38003564
    "NON-HISPANIC": 38003564
    "Unknown": 0
    "NULL": 0
    "": 0

  VisitType:
    "Inpatient": 9201
    "IP": 9201
    "INPATIENT": 9201
    "Outpatient": 9202
    "OP": 9202
    "OUTPATIENT": 9202
    "Emergency": 9203
    "ER": 9203
    "EMERGENCY": 9203
    "Long-term care": 42898160
    "LTC": 42898160
    "Unknown": 0

  ProcedureType:
    "Surgery": 38000275
    "SURGERY": 38000275
    "Diagnostic": 38000267
    "DIAGNOSTIC": 38000267
    "Therapeutic": 38000268
    "THERAPEUTIC": 38000268
    "Unknown": 0

  MeasurementType:
    "Laboratory": 38000280
    "LAB": 38000280
    "Vital Signs": 38000281
    "VITAL": 38000281
    "Physical Exam": 38000282
    "PHYSICAL": 38000282
    "Unknown": 0

  ObservationType:
    "Clinical": 38000283
    "CLINICAL": 38000283
    "Survey": 38000284
    "SURVEY": 38000284
    "Unknown": 0

  DrugType:
    "Prescription": 38000177
    "PRESCRIPTION": 38000177
    "Over-the-counter": 38000178
    "OTC": 38000178
    "Unknown": 0

  DeathType:
    "Natural": 38003569
    "NATURAL": 38003569
    "Accident": 38003570
    "ACCIDENT": 38003570
    "Suicide": 38003571
    "SUICIDE": 38003571
    "Homicide": 38003572
    "HOMICIDE": 38003572
    "Unknown": 0

  DeviceType:
    "Implant": 38000285
    "IMPLANT": 38000285
    "External": 38000286
    "EXTERNAL": 38000286
    "Unknown": 0

  CostType:
    "Medical": 38000287
    "MEDICAL": 38000287
    "Pharmacy": 38000288
    "PHARMACY": 38000288
    "Unknown": 0

# Enhanced table mappings - Comprehensive coverage of ALL OMOP tables
table_mappings:
  # Person table mapping (existing)
  person:
    source_table: patients
    target_table: person
    pre_process_sql: |
      -- Clean up birth dates and validate data
      UPDATE patients
      SET birth_date = NULL
      WHERE birth_date < '1900-01-01' OR birth_date > CURRENT_DATE;
      
      -- Standardize gender values
      UPDATE patients
      SET gender = UPPER(TRIM(gender))
      WHERE gender IS NOT NULL;
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true
        default_time: "00:00:00"

      - source_column: birth_date
        target_column: year_of_birth
        type: custom
        function: extract_year

      - source_column: birth_date
        target_column: month_of_birth
        type: custom
        function: extract_month

      - source_column: birth_date
        target_column: day_of_birth
        type: custom
        function: extract_day

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        default_value: 0

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default_value: 0

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default_value: 0

      - source_column: patient_id
        target_column: person_source_value
        type: direct

      - source_column: gender
        target_column: gender_source_value
        type: direct

      - source_column: race
        target_column: race_source_value
        type: direct

      - source_column: ethnicity
        target_column: ethnicity_source_value
        type: direct

      - source_column: location_id
        target_column: location_id
        type: direct

    filters:
      - field: birth_date
        operator: not_null
      - field: gender
        operator: not_null
      - field: patient_id
        operator: not_null

    validations:
      - field: year_of_birth
        rule: range
        min: 1900
        max: 2024
      - field: gender_concept_id
        rule: not_zero
      - field: person_id
        rule: unique
      - field: person_id
        rule: positive_integer

  # NEW: Location table mapping
  location:
    source_table: locations
    target_table: location
    transformations:
      - source_column: location_id
        target_column: location_id
        type: direct

      - source_column: address_1
        target_column: address_1
        type: direct

      - source_column: address_2
        target_column: address_2
        type: direct

      - source_column: city
        target_column: city
        type: direct

      - source_column: state
        target_column: state
        type: direct

      - source_column: zip
        target_column: zip
        type: direct

      - source_column: county
        target_column: county
        type: direct

      - source_column: country
        target_column: country
        type: direct

      - source_column: location_source_value
        target_column: location_source_value
        type: direct

    validations:
      - field: location_id
        rule: unique
      - field: location_id
        rule: positive_integer

  # NEW: Care Site table mapping
  care_site:
    source_table: care_sites
    target_table: care_site
    transformations:
      - source_column: care_site_id
        target_column: care_site_id
        type: direct

      - source_column: care_site_name
        target_column: care_site_name
        type: direct

      - source_column: place_of_service_concept_id
        target_column: place_of_service_concept_id
        type: direct

      - source_column: location_id
        target_column: location_id
        type: direct

      - source_column: care_site_source_value
        target_column: care_site_source_value
        type: direct

      - source_column: place_of_service_source_value
        target_column: place_of_service_source_value
        type: direct

    validations:
      - field: care_site_id
        rule: unique
      - field: care_site_id
        rule: positive_integer

  # NEW: Provider table mapping
  provider:
    source_table: providers
    target_table: provider
    transformations:
      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: provider_name
        target_column: provider_name
        type: direct

      - source_column: npi
        target_column: npi
        type: direct

      - source_column: dea
        target_column: dea
        type: direct

      - source_column: specialty_concept_id
        target_column: specialty_concept_id
        type: direct

      - source_column: care_site_id
        target_column: care_site_id
        type: direct

      - source_column: year_of_birth
        target_column: year_of_birth
        type: direct

      - source_column: gender_concept_id
        target_column: gender_concept_id
        type: direct

      - source_column: provider_source_value
        target_column: provider_source_value
        type: direct

      - source_column: specialty_source_value
        target_column: specialty_source_value
        type: direct

      - source_column: specialty_source_concept_id
        target_column: specialty_source_concept_id
        type: direct

      - source_column: gender_source_value
        target_column: gender_source_value
        type: direct

      - source_column: gender_source_concept_id
        target_column: gender_source_concept_id
        type: direct

    validations:
      - field: provider_id
        rule: unique
      - field: provider_id
        rule: positive_integer

  # NEW: Visit Detail table mapping
  visit_detail:
    source_table: visit_details
    target_table: visit_detail
    transformations:
      - source_column: visit_detail_id
        target_column: visit_detail_id
        type: direct

      - source_column: person_id
        target_column: person_id
        type: direct

      - source_column: visit_detail_concept_id
        target_column: visit_detail_concept_id
        type: direct

      - source_column: visit_detail_start_date
        target_column: visit_detail_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: visit_detail_start_datetime
        target_column: visit_detail_start_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: visit_detail_end_date
        target_column: visit_detail_end_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: visit_detail_end_datetime
        target_column: visit_detail_end_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: visit_detail_type_concept_id
        target_column: visit_detail_type_concept_id
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: care_site_id
        target_column: care_site_id
        type: direct

      - source_column: visit_detail_source_value
        target_column: visit_detail_source_value
        type: direct

      - source_column: visit_detail_source_concept_id
        target_column: visit_detail_source_concept_id
        type: direct

      - source_column: admitted_from_concept_id
        target_column: admitted_from_concept_id
        type: direct

      - source_column: admitted_from_source_value
        target_column: admitted_from_source_value
        type: direct

      - source_column: discharged_to_concept_id
        target_column: discharged_to_concept_id
        type: direct

      - source_column: discharged_to_source_value
        target_column: discharged_to_source_value
        type: direct

      - source_column: preceding_visit_detail_id
        target_column: preceding_visit_detail_id
        type: direct

      - source_column: visit_detail_parent_id
        target_column: visit_detail_parent_id
        type: direct

      - source_column: visit_occurrence_id
        target_column: visit_occurrence_id
        type: direct

    validations:
      - field: visit_detail_id
        rule: unique
      - field: visit_detail_id
        rule: positive_integer
      - field: visit_detail_start_date
        rule: not_null
      - field: visit_detail_end_date
        rule: date_after_start
        start_field: visit_detail_start_date

  # NEW: Device Exposure table mapping
  device_exposure:
    source_table: device_exposures
    target_table: device_exposure
    transformations:
      - source_column: device_exposure_id
        target_column: device_exposure_id
        type: direct

      - source_column: person_id
        target_column: person_id
        type: direct

      - source_column: device_concept_id
        target_column: device_concept_id
        type: direct

      - source_column: device_exposure_start_date
        target_column: device_exposure_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: device_exposure_start_datetime
        target_column: device_exposure_start_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: device_exposure_end_date
        target_column: device_exposure_end_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: device_exposure_end_datetime
        target_column: device_exposure_end_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: device_type_concept_id
        target_column: device_type_concept_id
        type: vocabulary_mapping
        vocabulary: DeviceType
        default_value: 0

      - source_column: unique_device_id
        target_column: unique_device_id
        type: direct

      - source_column: production_id
        target_column: production_id
        type: direct

      - source_column: quantity
        target_column: quantity
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: visit_occurrence_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: visit_detail_id
        target_column: visit_detail_id
        type: direct

      - source_column: device_source_value
        target_column: device_source_value
        type: direct

      - source_column: device_source_concept_id
        target_column: device_source_concept_id
        type: direct

      - source_column: unit_concept_id
        target_column: unit_concept_id
        type: direct

      - source_column: unit_source_value
        target_column: unit_source_value
        type: direct

      - source_column: unit_source_concept_id
        target_column: unit_source_concept_id
        type: direct

    validations:
      - field: device_exposure_id
        rule: unique
      - field: device_exposure_id
        rule: positive_integer
      - field: device_exposure_start_date
        rule: not_null

  # NEW: Cost table mapping
  cost:
    source_table: costs
    target_table: cost
    transformations:
      - source_column: cost_id
        target_column: cost_id
        type: direct

      - source_column: cost_event_id
        target_column: cost_event_id
        type: direct

      - source_column: cost_domain_id
        target_column: cost_domain_id
        type: direct

      - source_column: cost_type_concept_id
        target_column: cost_type_concept_id
        type: vocabulary_mapping
        vocabulary: CostType
        default_value: 0

      - source_column: currency_concept_id
        target_column: currency_concept_id
        type: direct

      - source_column: total_charge
        target_column: total_charge
        type: direct

      - source_column: total_cost
        target_column: total_cost
        type: direct

      - source_column: total_paid
        target_column: total_paid
        type: direct

      - source_column: paid_by_payer
        target_column: paid_by_payer
        type: direct

      - source_column: paid_by_patient
        target_column: paid_by_patient
        type: direct

      - source_column: paid_patient_copay
        target_column: paid_patient_copay
        type: direct

      - source_column: paid_patient_coinsurance
        target_column: paid_patient_coinsurance
        type: direct

      - source_column: paid_patient_deductible
        target_column: paid_patient_deductible
        type: direct

      - source_column: paid_by_primary
        target_column: paid_by_primary
        type: direct

      - source_column: paid_ingredient_cost
        target_column: paid_ingredient_cost
        type: direct

      - source_column: paid_dispensing_fee
        target_column: paid_dispensing_fee
        type: direct

      - source_column: payer_plan_period_id
        target_column: payer_plan_period_id
        type: direct

      - source_column: amount_allowed
        target_column: amount_allowed
        type: direct

      - source_column: revenue_code_concept_id
        target_column: revenue_code_concept_id
        type: direct

      - source_column: revenue_code_source_value
        target_column: revenue_code_source_value
        type: direct

      - source_column: drg_concept_id
        target_column: drg_concept_id
        type: direct

      - source_column: drg_source_value
        target_column: drg_source_value
        type: direct

    validations:
      - field: cost_id
        rule: unique
      - field: cost_id
        rule: positive_integer

  # NEW: Payer Plan Period table mapping
  payer_plan_period:
    source_table: payer_plan_periods
    target_table: payer_plan_period
    transformations:
      - source_column: payer_plan_period_id
        target_column: payer_plan_period_id
        type: direct

      - source_column: person_id
        target_column: person_id
        type: direct

      - source_column: payer_plan_period_start_date
        target_column: payer_plan_period_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: payer_plan_period_end_date
        target_column: payer_plan_period_end_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: payer_concept_id
        target_column: payer_concept_id
        type: direct

      - source_column: payer_source_value
        target_column: payer_source_value
        type: direct

      - source_column: payer_source_concept_id
        target_column: payer_source_concept_id
        type: direct

      - source_column: plan_concept_id
        target_column: plan_concept_id
        type: direct

      - source_column: plan_source_value
        target_column: plan_source_value
        type: direct

      - source_column: plan_source_concept_id
        target_column: plan_source_concept_id
        type: direct

      - source_column: sponsor_concept_id
        target_column: sponsor_concept_id
        type: direct

      - source_column: sponsor_source_value
        target_column: sponsor_source_value
        type: direct

      - source_column: sponsor_source_concept_id
        target_column: sponsor_source_concept_id
        type: direct

      - source_column: family_source_value
        target_column: family_source_value
        type: direct

      - source_column: stop_reason_concept_id
        target_column: stop_reason_concept_id
        type: direct

      - source_column: stop_reason_source_value
        target_column: stop_reason_source_value
        type: direct

      - source_column: stop_reason_source_concept_id
        target_column: stop_reason_source_concept_id
        type: direct

    validations:
      - field: payer_plan_period_id
        rule: unique
      - field: payer_plan_period_id
        rule: positive_integer
      - field: payer_plan_period_start_date
        rule: not_null
      - field: payer_plan_period_end_date
        rule: date_after_start
        start_field: payer_plan_period_start_date

# Enhanced data quality checks
data_quality_checks:
  - name: enhanced_visit_validation
    description: Enhanced visit date validation with more comprehensive checks
    query: |
      SELECT COUNT(*) as invalid_visits
      FROM test_cdm.visit_occurrence
      WHERE visit_start_date > visit_end_date
         OR visit_start_date IS NULL
         OR visit_end_date IS NULL
         OR visit_start_date < '1900-01-01'
         OR visit_end_date > CURRENT_DATE + INTERVAL '1 year'
    expected: invalid_visits = 0
    severity: error

  - name: enhanced_person_validation
    description: Enhanced person data validation
    query: |
      SELECT COUNT(*) as invalid_persons
      FROM test_cdm.person
      WHERE year_of_birth < 1900 
         OR year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE)
         OR month_of_birth < 1 
         OR month_of_birth > 12
         OR day_of_birth < 1 
         OR day_of_birth > 31
         OR person_id IS NULL
    expected: invalid_persons = 0
    severity: error

  - name: enhanced_condition_validation
    description: Enhanced condition occurrence validation
    query: |
      SELECT COUNT(*) as invalid_conditions
      FROM test_cdm.condition_occurrence
      WHERE condition_start_date > condition_end_date
         OR condition_start_date IS NULL
         OR condition_concept_id IS NULL
         OR person_id IS NULL
    expected: invalid_conditions = 0
    severity: error

  - name: enhanced_drug_validation
    description: Enhanced drug exposure validation
    query: |
      SELECT COUNT(*) as invalid_drugs
      FROM test_cdm.drug_exposure
      WHERE drug_exposure_start_date > drug_exposure_end_date
         OR drug_exposure_start_date IS NULL
         OR drug_concept_id IS NULL
         OR person_id IS NULL
         OR quantity <= 0
         OR days_supply <= 0
    expected: invalid_drugs = 0
    severity: error

  - name: enhanced_measurement_validation
    description: Enhanced measurement validation
    query: |
      SELECT COUNT(*) as invalid_measurements
      FROM test_cdm.measurement
      WHERE measurement_date IS NULL
         OR measurement_concept_id IS NULL
         OR person_id IS NULL
         OR (range_low IS NOT NULL AND range_high IS NOT NULL AND range_low > range_high)
    expected: invalid_measurements = 0
    severity: error

  - name: enhanced_procedure_validation
    description: Enhanced procedure occurrence validation
    query: |
      SELECT COUNT(*) as invalid_procedures
      FROM test_cdm.procedure_occurrence
      WHERE procedure_date IS NULL
         OR procedure_concept_id IS NULL
         OR person_id IS NULL
         OR procedure_date < '1900-01-01'
         OR procedure_date > CURRENT_DATE + INTERVAL '1 year'
    expected: invalid_procedures = 0
    severity: error

  - name: enhanced_observation_validation
    description: Enhanced observation validation
    query: |
      SELECT COUNT(*) as invalid_observations
      FROM test_cdm.observation
      WHERE observation_date IS NULL
         OR observation_concept_id IS NULL
         OR person_id IS NULL
         OR observation_date < '1900-01-01'
         OR observation_date > CURRENT_DATE + INTERVAL '1 year'
    expected: invalid_observations = 0
    severity: error

  - name: enhanced_death_validation
    description: Enhanced death validation
    query: |
      SELECT COUNT(*) as invalid_deaths
      FROM test_cdm.death
      WHERE death_date IS NULL
         OR person_id IS NULL
         OR death_date < '1900-01-01'
         OR death_date > CURRENT_DATE
    expected: invalid_deaths = 0
    severity: error

  - name: enhanced_note_validation
    description: Enhanced note validation
    query: |
      SELECT COUNT(*) as invalid_notes
      FROM test_cdm.note
      WHERE note_date IS NULL
         OR note_type_concept_id IS NULL
         OR person_id IS NULL
         OR note_text IS NULL
         OR LENGTH(note_text) = 0
    expected: invalid_notes = 0
    severity: error

  - name: enhanced_visit_detail_validation
    description: Enhanced visit detail validation
    query: |
      SELECT COUNT(*) as invalid_visit_details
      FROM test_cdm.visit_detail
      WHERE visit_detail_start_date > visit_detail_end_date
         OR visit_detail_start_date IS NULL
         OR visit_detail_concept_id IS NULL
         OR person_id IS NULL
    expected: invalid_visit_details = 0
    severity: error

  - name: enhanced_device_validation
    description: Enhanced device exposure validation
    query: |
      SELECT COUNT(*) as invalid_devices
      FROM test_cdm.device_exposure
      WHERE device_exposure_start_date > device_exposure_end_date
         OR device_exposure_start_date IS NULL
         OR device_concept_id IS NULL
         OR person_id IS NULL
    expected: invalid_devices = 0
    severity: error

  - name: enhanced_cost_validation
    description: Enhanced cost validation
    query: |
      SELECT COUNT(*) as invalid_costs
      FROM test_cdm.cost
      WHERE cost_id IS NULL
         OR cost_event_id IS NULL
         OR cost_domain_id IS NULL
         OR cost_type_concept_id IS NULL
    expected: invalid_costs = 0
    severity: error

  - name: enhanced_payer_plan_validation
    description: Enhanced payer plan period validation
    query: |
      SELECT COUNT(*) as invalid_payer_plans
      FROM test_cdm.payer_plan_period
      WHERE payer_plan_period_start_date > payer_plan_period_end_date
         OR payer_plan_period_start_date IS NULL
         OR person_id IS NULL
    expected: invalid_payer_plans = 0
    severity: error

  - name: enhanced_referential_integrity
    description: Enhanced referential integrity checks for all tables
    query: |
      SELECT
        (SELECT COUNT(*) FROM test_cdm.condition_occurrence co
         LEFT JOIN test_cdm.person p ON co.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.drug_exposure de
         LEFT JOIN test_cdm.person p ON de.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.procedure_occurrence po
         LEFT JOIN test_cdm.person p ON po.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.measurement m
         LEFT JOIN test_cdm.person p ON m.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.observation o
         LEFT JOIN test_cdm.person p ON o.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.death d
         LEFT JOIN test_cdm.person p ON d.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.note n
         LEFT JOIN test_cdm.person p ON n.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.visit_detail vd
         LEFT JOIN test_cdm.person p ON vd.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.device_exposure de
         LEFT JOIN test_cdm.person p ON de.person_id = p.person_id
         WHERE p.person_id IS NULL) +
        (SELECT COUNT(*) FROM test_cdm.payer_plan_period ppp
         LEFT JOIN test_cdm.person p ON ppp.person_id = p.person_id
         WHERE p.person_id IS NULL) as orphan_records
    expected: orphan_records = 0
    severity: error

  - name: enhanced_duplicate_check
    description: Enhanced duplicate record checks for all tables
    query: |
      SELECT
        (SELECT COUNT(*) - COUNT(DISTINCT person_id) FROM test_cdm.person) +
        (SELECT COUNT(*) - COUNT(DISTINCT visit_occurrence_id) FROM test_cdm.visit_occurrence) +
        (SELECT COUNT(*) - COUNT(DISTINCT condition_occurrence_id) FROM test_cdm.condition_occurrence) +
        (SELECT COUNT(*) - COUNT(DISTINCT drug_exposure_id) FROM test_cdm.drug_exposure) +
        (SELECT COUNT(*) - COUNT(DISTINCT procedure_occurrence_id) FROM test_cdm.procedure_occurrence) +
        (SELECT COUNT(*) - COUNT(DISTINCT measurement_id) FROM test_cdm.measurement) +
        (SELECT COUNT(*) - COUNT(DISTINCT observation_id) FROM test_cdm.observation) +
        (SELECT COUNT(*) - COUNT(DISTINCT note_id) FROM test_cdm.note) +
        (SELECT COUNT(*) - COUNT(DISTINCT visit_detail_id) FROM test_cdm.visit_detail) +
        (SELECT COUNT(*) - COUNT(DISTINCT device_exposure_id) FROM test_cdm.device_exposure) +
        (SELECT COUNT(*) - COUNT(DISTINCT cost_id) FROM test_cdm.cost) +
        (SELECT COUNT(*) - COUNT(DISTINCT payer_plan_period_id) FROM test_cdm.payer_plan_period) as duplicate_count
    expected: duplicate_count = 0
    severity: error

  - name: enhanced_vocabulary_coverage
    description: Enhanced vocabulary mapping coverage check
    query: |
      SELECT
        ROUND(
          (SELECT COUNT(*) FROM test_cdm.condition_occurrence WHERE condition_concept_id > 0) * 100.0 /
          NULLIF((SELECT COUNT(*) FROM test_cdm.condition_occurrence), 0), 2
        ) as condition_mapping_percentage,
        ROUND(
          (SELECT COUNT(*) FROM test_cdm.drug_exposure WHERE drug_concept_id > 0) * 100.0 /
          NULLIF((SELECT COUNT(*) FROM test_cdm.drug_exposure), 0), 2
        ) as drug_mapping_percentage,
        ROUND(
          (SELECT COUNT(*) FROM test_cdm.procedure_occurrence WHERE procedure_concept_id > 0) * 100.0 /
          NULLIF((SELECT COUNT(*) FROM test_cdm.procedure_occurrence), 0), 2
        ) as procedure_mapping_percentage,
        ROUND(
          (SELECT COUNT(*) FROM test_cdm.measurement WHERE measurement_concept_id > 0) * 100.0 /
          NULLIF((SELECT COUNT(*) FROM test_cdm.measurement), 0), 2
        ) as measurement_mapping_percentage
    expected: 
      condition_mapping_percentage >= 80.0
      drug_mapping_percentage >= 80.0
      procedure_mapping_percentage >= 80.0
      measurement_mapping_percentage >= 80.0
    severity: warning

# Enhanced error handling configuration
error_handling:
  max_error_count: 100
  error_log_file: "etl_errors.log"
  continue_on_validation_error: false
  email_on_failure: false
  retry_failed_batches: true
  quarantine_invalid_records: true
  detailed_error_reporting: true
  error_categorization: true

# Enhanced performance monitoring
performance_monitoring:
  enable_metrics: true
  metrics_interval_seconds: 30
  log_slow_queries: true
  slow_query_threshold_ms: 5000
  enable_profiling: true
  profile_memory_usage: true
  track_batch_performance: true
  monitor_disk_usage: true
  alert_on_performance_degradation: true

# Custom functions (existing)
custom_functions:
  extract_year:
    sql: "EXTRACT(YEAR FROM ?)"
  extract_month:
    sql: "EXTRACT(MONTH FROM ?)"
  extract_day:
    sql: "EXTRACT(DAY FROM ?)"
  calculate_age:
    sql: "EXTRACT(YEAR FROM AGE(?, CURRENT_DATE))"
  standardize_gender:
    sql: "UPPER(TRIM(?))"
  clean_icd_code:
    sql: "UPPER(TRIM(REGEXP_REPLACE(?, '[^A-Z0-9.]', '', 'g')))" 