# JSON Extractor Integration Test Configuration
# Used by JSON extractor integration tests

json_extractor:
  # JSON parsing options
  parse_strict: false
  allow_comments: false
  allow_trailing_commas: false
  
  # Data processing options
  root_path: ""  # JSONPath to data array (e.g., "data.patients")
  flatten_nested: true
  max_depth: 10
  array_delimiter: "_"
  
  # Date parsing options
  parse_dates: true
  date_formats:
    - "%Y-%m-%dT%H:%M:%SZ"    # ISO 8601 with Z
    - "%Y-%m-%dT%H:%M:%S"     # ISO 8601 without Z
    - "%d/%m/%Y"              # UK date format
    - "%Y-%m-%d"              # Standard date format
    
  # UK Localization settings
  locale: "en_GB"
  currency_symbol: "£"
  temperature_unit: "celsius"
  
  # Type coercion rules
  type_coercion:
    strings_to_numbers: true
    strings_to_booleans: true
    strings_to_dates: true
    null_handling: "preserve"  # Options: preserve, skip, default
    
  # Nested object handling
  nested_objects:
    flatten_strategy: "dot_notation"  # Options: dot_notation, underscore, custom
    max_nesting_level: 5
    preserve_arrays: false
    array_index_format: "{key}_{index}"

# Test scenarios configuration
test_scenarios:
  basic_array:
    description: "Basic JSON array extraction"
    file_pattern: "patients.json"
    expected_records: 3
    root_path: ""
    
  nested_objects:
    description: "JSON with nested objects requiring flattening"
    flatten_nested: true
    array_delimiter: "_"
    
  root_path_navigation:
    description: "JSON with root path navigation"
    root_path: "data.users"
    expected_records: 3
    
  json_lines:
    description: "JSON Lines (JSONL) format extraction"
    format: "jsonl"
    expected_records: 3
    
  arrays_in_objects:
    description: "JSON with arrays within objects"
    flatten_nested: true
    array_delimiter: ","
    preserve_arrays: false
    
  date_parsing:
    description: "JSON with various date formats"
    parse_dates: true
    expected_date_fields: ["created_at", "updated_at", "birth_date"]
    
  large_file:
    description: "Large JSON file performance testing"
    batch_size: 1000
    max_records: 10000
    streaming: true
    
  streaming_extraction:
    description: "Streaming JSON extraction for very large files"
    streaming: true
    buffer_size: 8192
    
  uk_healthcare_data:
    description: "UK healthcare JSON data with NHS numbers and postcodes"
    locale: "en_GB"
    validate_nhs_numbers: true
    validate_postcodes: true
    
  null_value_handling:
    description: "JSON with null and missing values"
    null_handling: "preserve"
    missing_field_default: null
    
  mixed_type_arrays:
    description: "JSON arrays with mixed data types"
    type_coercion:
      strings_to_numbers: true
      preserve_type_info: true
      
  malformed_json_error:
    description: "Error handling for malformed JSON"
    expect_error: true
    error_type: "parse_error"
    
  empty_json_handling:
    description: "Handling of empty JSON files and arrays"
    allow_empty: true
    expected_records: 0

# JSON Schema validation (optional)
schema_validation:
  enabled: false
  schema_file: "patient_schema.json"
  strict_validation: false
  
# Performance thresholds
performance:
  max_processing_time_seconds: 120
  max_memory_usage_mb: 1024
  min_records_per_second: 500
  streaming_threshold_mb: 100
  
# UK Healthcare specific validation
uk_healthcare:
  nhs_number:
    pattern: "^[0-9]{10}$"
    validate_checksum: true
    
  postcode:
    pattern: "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$"
    normalize: true
    
  date_formats:
    preferred: "%d/%m/%Y"
    accepted:
      - "%d/%m/%Y"
      - "%d-%m-%Y"
      - "%Y-%m-%d"
      - "%Y-%m-%dT%H:%M:%SZ"
      
# Error handling configuration
error_handling:
  on_parse_error: "skip_record"  # Options: skip_record, fail_fast, continue
  on_validation_error: "log_and_continue"
  max_errors: 100
  error_sampling_rate: 0.1  # Log 10% of errors
  
# Streaming configuration
streaming:
  enabled: false
  chunk_size: 1000
  buffer_size: 8192
  parallel_processing: true
  thread_count: 4 