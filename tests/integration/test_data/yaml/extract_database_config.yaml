# Database Extractor Integration Test Configuration
# Used by database extractor integration tests

database_extractor:
  # Connection configuration
  connections:
    test_db:
      type: "mock"
      host: "localhost"
      port: 5432
      database: "test_omop_db"
      schema: "public"
      username: "test_user"
      password: "test_password"
      
    postgresql:
      type: "postgresql"
      host: "${POSTGRES_TEST_HOST:-localhost}"
      port: "${POSTGRES_TEST_PORT:-5432}"
      database: "${POSTGRES_TEST_DB:-test_db}"
      username: "${POSTGRES_TEST_USER:-postgres}"
      password: "${POSTGRES_TEST_PASSWORD:-postgres}"
      
    mysql:
      type: "mysql"
      host: "${MYSQL_TEST_HOST:-localhost}"
      port: "${MYSQL_TEST_PORT:-3306}"
      database: "${MYSQL_TEST_DB:-test_db}"
      username: "${MYSQL_TEST_USER:-root}"
      password: "${MYSQL_TEST_PASSWORD:-}"
      
  # Connection pool settings
  connection_pool:
    min_connections: 2
    max_connections: 10
    connection_timeout_ms: 5000
    idle_timeout_ms: 300000
    max_lifetime_ms: 1800000
    
  # Query execution settings
  query_execution:
    default_timeout_seconds: 30
    max_retry_attempts: 3
    retry_delay_ms: 1000
    batch_size: 1000
    streaming_threshold: 10000
    
  # Transaction settings
  transactions:
    isolation_level: "READ_COMMITTED"
    lock_timeout_seconds: 30
    auto_commit: false
    
  # UK Localization settings
  locale: "en_GB"
  timezone: "Europe/London"
  date_format: "DD/MM/YYYY"
  datetime_format: "DD/MM/YYYY HH24:MI:SS"

# Test scenarios configuration
test_scenarios:
  basic_table_extraction:
    description: "Basic table extraction with standard SQL query"
    table: "test_patients"
    columns: ["patient_id", "first_name", "last_name", "birth_date", "gender"]
    expected_records: 10
    
  custom_sql_query:
    description: "Custom SQL query execution for complex data extraction"
    query: |
      SELECT p.patient_id, p.first_name, p.last_name, o.observation_date 
      FROM patients p 
      JOIN observations o ON p.patient_id = o.patient_id 
      WHERE p.birth_date > '1980-01-01'
    expected_records: 5
    
  pagination_handling:
    description: "Paginated extraction for large database tables"
    table: "test_patients"
    batch_size: 5
    offset_column: "patient_id"
    order_by: "patient_id ASC"
    expected_batches: 2
    
  filtered_extraction:
    description: "Extraction with WHERE clause filtering"
    table: "test_patients"
    filter: "age > 30"
    columns: ["patient_id", "first_name", "age"]
    
  join_extraction:
    description: "JOIN operations across multiple related tables"
    query: |
      SELECT p.patient_id, p.first_name, p.last_name, 
             v.visit_date, v.visit_type
      FROM test_patients p
      JOIN test_visits v ON p.patient_id = v.patient_id
      WHERE v.visit_date >= '2023-01-01'
      ORDER BY p.patient_id, v.visit_date
      
  prepared_statement:
    description: "Prepared statement extraction with parameter binding"
    query: "SELECT * FROM test_patients WHERE patient_id = ? AND gender = ?"
    parameters: [1, "M"]
    
  column_selection:
    description: "Column selection and ordering in database extraction"
    table: "test_patients"
    columns: ["patient_id", "last_name", "first_name"]
    order_by: "last_name ASC, first_name ASC"
    
  connection_error_handling:
    description: "Database connection error handling and recovery"
    connection: "invalid_connection"
    expect_error: true
    
  transaction_handling:
    description: "Transaction handling with rollback and commit"
    use_transaction: true
    operations:
      - type: "insert"
        table: "test_patients"
        data: {"patient_id": 999, "first_name": "Test", "last_name": "User"}
      - type: "rollback"
        
  concurrent_extraction:
    description: "Concurrent extraction from multiple threads"
    thread_count: 4
    extractors_per_thread: 4
    table: "test_patients"
    
  uk_date_range_filtering:
    description: "Date range filtering with UK date formats"
    table: "test_patients"
    where_clause: "birth_date BETWEEN '1990-01-01' AND '2000-12-31'"
    date_format: "DD/MM/YYYY"
    locale: "en_GB"
    
  sql_injection_prevention:
    description: "SQL injection prevention with sanitized inputs"
    table: "test_patients'; DROP TABLE patients; --"
    where_clause: "1=1; DROP TABLE test_patients; --"
    expect_error: true
    
  null_value_handling:
    description: "NULL value handling in database results"
    query: |
      SELECT patient_id, first_name, middle_name, last_name 
      FROM test_patients 
      WHERE middle_name IS NULL OR middle_name IS NOT NULL
    null_value_replacement: ""
    
  large_result_sets:
    description: "Large result set handling with memory management"
    table: "large_test_table"
    batch_size: 1000
    stream_results: true
    max_iterations: 10

# Database schema for testing
test_schema:
  tables:
    test_patients:
      columns:
        patient_id: "INTEGER PRIMARY KEY"
        first_name: "VARCHAR(50) NOT NULL"
        last_name: "VARCHAR(50) NOT NULL"
        birth_date: "DATE"
        gender: "CHAR(1)"
        nhs_number: "VARCHAR(10)"
        postcode: "VARCHAR(10)"
        
    test_visits:
      columns:
        visit_id: "INTEGER PRIMARY KEY"
        patient_id: "INTEGER REFERENCES test_patients(patient_id)"
        visit_date: "DATE"
        visit_type: "VARCHAR(50)"
        
    test_observations:
      columns:
        observation_id: "INTEGER PRIMARY KEY"
        patient_id: "INTEGER REFERENCES test_patients(patient_id)"
        observation_date: "DATE"
        observation_concept_id: "INTEGER"
        value_as_number: "DECIMAL(10,2)"

# Performance thresholds
performance:
  max_connection_time_ms: 5000
  max_query_time_seconds: 60
  max_memory_usage_mb: 512
  min_records_per_second: 500
  
# UK Healthcare specific settings
uk_healthcare:
  nhs_number_validation: true
  postcode_validation: true
  date_preference: "DD/MM/YYYY"
  time_zone: "Europe/London"
  
# Error handling
error_handling:
  on_connection_error: "retry"
  on_query_error: "log_and_continue"
  on_timeout: "cancel_and_retry"
  max_retries: 3
  retry_backoff_ms: 1000
  
# Security settings
security:
  sql_injection_protection: true
  query_whitelist_enabled: false
  parameter_validation: true
  connection_encryption: false 