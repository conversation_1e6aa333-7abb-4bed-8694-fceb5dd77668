# Integration Test CSV Data Files

This directory contains CSV test data files for various integration test scenarios across the OMOP CDM ETL pipeline components.

## File Organization

### 👥 Patient Data Files
Core patient demographics and identity information:

| File | Purpose | Test Scope |
|------|---------|------------|
| `uk_patients_test.csv` | UK-localized patient records | NHS numbers, UK postcodes, phone formats, currency |
| `patients.csv` | Standard patient demographics | Basic patient information, demographics |

### 🏥 Clinical Data Files
Healthcare episode and clinical event data:

| File | Purpose | Test Scope |
|------|---------|------------|
| `conditions.csv` | Medical conditions and diagnoses | ICD-10 codes, condition occurrences |
| `medications.csv` | Drug prescriptions and exposures | Drug codes, dosages, administration |
| `test_data_drug_exposures.csv` | Comprehensive drug exposure records | Complex drug scenarios, interactions |
| `test_data_observations.csv` | Clinical observations and measurements | Lab results, vital signs, assessments |
| `test_data_procedures.csv` | Medical procedures and interventions | CPT codes, procedure occurrences |
| `test_data_visits.csv` | Healthcare visits and encounters | Visit types, dates, duration |

### 🏢 Healthcare Infrastructure Files  
Organizational and location data:

| File | Purpose | Test Scope |
|------|---------|------------|
| `test_data_care_sites.csv` | Healthcare facilities and departments | Hospital units, clinic locations |
| `test_data_providers.csv` | Healthcare providers and practitioners | Physician data, specialties, credentials |
| `test_data_locations.csv` | Geographic and address information | Location hierarchies, address formats |

### 📚 Reference Data Files
Vocabulary and mapping information:

| File | Purpose | Test Scope |
|------|---------|------------|
| `test_data_vocabulary_mappings.csv` | Medical terminology mappings | ICD-10, SNOMED, RxNorm mappings |

### 🧪 Test Scenario Files
Specialized test cases and edge conditions:

| File | Purpose | Test Scope |
|------|---------|------------|
| `test_data_complex_scenarios.csv` | Complex data scenarios | Multi-condition patients, edge cases |
| `test_data_edge_cases.csv` | Data quality and validation testing | Null values, format variations, anomalies |

## Data Categories

### 🎯 **Demographics Testing**
- `uk_patients_test.csv` - UK healthcare standards (NHS numbers, postcodes)
- `patients.csv` - Standard demographic patterns
- `test_data_locations.csv` - Geographic data validation

### 🔧 **Clinical Testing**
- `conditions.csv` - Basic condition mapping
- `medications.csv` - Drug exposure scenarios
- `test_data_observations.csv` - Clinical measurements
- `test_data_procedures.csv` - Procedural data

### 📋 **Infrastructure Testing**
- `test_data_care_sites.csv` - Healthcare facility mapping
- `test_data_providers.csv` - Provider information validation
- `test_data_visits.csv` - Visit encounter patterns

### 🌍 **Localization Testing**
- `uk_patients_test.csv` - UK regional standards compliance
- NHS number validation (10-digit format)
- UK postcode patterns (e.g., SW1A 1AA, EC1A 1BB)
- UK phone number formats (020, 0161, 0113, 0121 prefixes)
- British currency formatting (£1,234.56)
- Celsius temperature units (20.5°C)
- DD/MM/YYYY date format

## Usage Examples

### Extract Library Tests
```bash
# Test CSV extractor with UK patient data
./extract_integration_tests --input=tests/integration/test_data/csv/uk_patients_test.csv --config=extract_csv_config.yaml

# Test complex data scenarios
./extract_integration_tests --input=tests/integration/test_data/csv/test_data_complex_scenarios.csv

# Test edge case handling
./extract_integration_tests --input=tests/integration/test_data/csv/test_data_edge_cases.csv
```

### Multi-Source Pipeline Tests
```bash
# Test complete patient workflow
./pipeline_integration_tests --patients=tests/integration/test_data/csv/patients.csv \
                            --conditions=tests/integration/test_data/csv/conditions.csv \
                            --medications=tests/integration/test_data/csv/medications.csv

# Test healthcare infrastructure
./pipeline_integration_tests --care_sites=tests/integration/test_data/csv/test_data_care_sites.csv \
                            --providers=tests/integration/test_data/csv/test_data_providers.csv
```

## File Format Standards

### Common CSV Properties
All CSV files follow these standards:

```csv
# Standard format
field1,field2,field3,field4
value1,value2,value3,value4

# Character encoding: UTF-8
# Delimiter: comma (,)
# Header row: included
# Quote character: double quote (")
# Escape character: backslash (\)
```

### UK Healthcare Specific Formats

#### NHS Numbers
```csv
nhs_number
**********    # 10-digit format
**********    # Valid checksum
```

#### UK Postcodes
```csv
postcode
SW1A 1AA      # Central London format
EC1A 1BB      # City of London format
M1 1AA        # Manchester format
B33 8TH       # Birmingham format
```

#### UK Phone Numbers
```csv
phone
020 7946 0958 # London landline
0161 123 4567 # Manchester landline
0113 987 6543 # Leeds landline
0121 555 0123 # Birmingham landline
```

#### British Currency
```csv
amount
£1234.56      # Pound symbol prefix
£2,500.00     # Thousands separator
£999.99       # Standard decimal format
```

#### Date Format (DD/MM/YYYY)
```csv
birth_date
15/01/1985    # 15th January 1985
28/02/1990    # 28th February 1990
31/12/1975    # 31st December 1975
```

## Test Environment Requirements

### For CSV Extraction Tests
- File system access to test data directory
- UTF-8 encoding support
- CSV parsing libraries (e.g., libcsv, csv-parser)
- Memory capacity for largest test files (~2KB typical)

### For UK Localization Tests  
- UK locale support (en_GB)
- NHS number validation algorithms
- UK postcode validation patterns
- British currency formatting libraries
- Celsius temperature unit handling

## Quality Assurance

### Data Validation
Each CSV file includes:

1. **Header Validation**: Consistent column naming
2. **Data Types**: Appropriate type validation
3. **Format Compliance**: UK standards adherence
4. **Completeness**: Required fields populated
5. **Referential Integrity**: Cross-file consistency

### Test Coverage Matrix

| Test Scenario | Patient Data | Clinical Data | Infrastructure | Reference Data |
|---------------|--------------|---------------|----------------|----------------|
| Basic Extraction | ✅ patients.csv | ✅ conditions.csv | ✅ care_sites.csv | ✅ vocabulary.csv |
| UK Localization | ✅ uk_patients.csv | ✅ observations.csv | ✅ providers.csv | ✅ mappings.csv |
| Edge Cases | ✅ edge_cases.csv | ✅ procedures.csv | ✅ locations.csv | ✅ complex.csv |
| Performance | ✅ All files | ✅ drug_exposure.csv | ✅ visits.csv | ✅ All reference |

## Integration with Test Suites

### Extract Library Integration Tests
These CSV files are used by:
- `test_csv_extractor_integration.cpp`
- `test_multi_source_extraction_integration.cpp`
- `extractor_integration_test.cpp`

### Pipeline Integration Tests
Referenced in:
- Core pipeline tests for full ETL workflows
- Transform library tests for data conversion
- Load library tests for database insertion

### Configuration Integration
CSV files are referenced in:
- `extract_csv_config.yaml` - CSV extractor configurations
- `extract_multi_source_config.yaml` - Multi-file processing
- `uk_localization_config.yaml` - UK healthcare compliance

## Maintenance Notes

### Adding New CSV Files
1. Follow naming convention: `test_data_{purpose}_{domain}.csv`
2. Include header row with descriptive column names
3. Use UTF-8 encoding
4. Add entry to this README with purpose and scope
5. Update relevant test configuration files

### Updating Existing Files
1. Maintain backward compatibility with existing tests
2. Preserve column order and naming
3. Validate data format consistency
4. Test changes against integration test suite
5. Update documentation to reflect changes

### UK Healthcare Compliance
When adding UK-specific data:
1. Use valid NHS number format (10 digits)
2. Include proper UK postcode patterns
3. Follow British date format (DD/MM/YYYY)
4. Use British currency symbols and formatting
5. Include UK phone number patterns

## Troubleshooting

### Common Issues
1. **Encoding errors**: Ensure UTF-8 encoding for all files
2. **Format mismatches**: Verify CSV delimiter and quote settings
3. **NHS number validation**: Check 10-digit format and checksum
4. **Postcode validation**: Verify UK postcode pattern compliance
5. **Date parsing**: Ensure DD/MM/YYYY format consistency

### Debug Data
For debugging failed tests:
1. Use `test_data_edge_cases.csv` for boundary conditions
2. Check `test_data_complex_scenarios.csv` for multi-factor issues
3. Validate against `uk_patients_test.csv` for localization
4. Cross-reference with vocabulary mapping files

---

**Last Updated**: January 2025  
**Maintained By**: OMOP CDM ETL Team  
**Review Schedule**: Quarterly updates recommended 