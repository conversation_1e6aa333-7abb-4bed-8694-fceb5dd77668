# Integration Test JSON Data Files

This directory contains JSON test data files for various integration test scenarios across the OMOP CDM ETL pipeline components.

## File Organization

### 👥 Patient Record Files
Complex patient data with nested structures:

| File | Purpose | Test Scope |
|------|---------|------------|
| `uk_patient_records.json` | UK-localized comprehensive patient data | NHS numbers, nested clinical data, UK formats |
| `patient_records.json` | Standard patient records with basic structure | Demographics, simple clinical data |

### 🏥 Clinical Data Files
Healthcare data with complex relationships:

| File | Purpose | Test Scope |
|------|---------|------------|
| `clinical_data.json` | Multi-domain clinical information | Conditions, procedures, observations in JSON |

### 📚 Reference Data Files
Vocabulary and mapping information in structured format:

| File | Purpose | Test Scope |
|------|---------|------------|
| `vocabulary_mappings.json` | Medical terminology and code mappings | Hierarchical vocabulary structures, nested mappings |

## Data Categories

### 🎯 **Complex Structure Testing**
- `uk_patient_records.json` - Nested patient data with UK localization
- `patient_records.json` - Standard hierarchical patient information
- `clinical_data.json` - Multi-level clinical data structures

### 🔧 **JSON Format Testing**
- Array structures for patient collections
- Nested objects for complex clinical relationships
- Mixed data types (strings, numbers, booleans, dates)
- UTF-8 encoding with special characters

### 📋 **Healthcare Data Modeling**
- Patient demographics with nested address information
- Clinical events with timestamps and relationships
- Medical terminology with hierarchical mappings
- Multi-language support and localization

### 🌍 **UK Localization Testing**
- NHS number validation in JSON format
- UK address structures with nested postcode data
- British phone number arrays and formats
- UK healthcare terminology and codes
- GDPR compliance data structures

## Usage Examples

### Extract Library Tests
```bash
# Test JSON extractor with UK patient data
./extract_integration_tests --input=tests/integration/test_data/json/uk_patient_records.json --config=extract_json_config.yaml

# Test vocabulary mapping extraction
./extract_integration_tests --input=tests/integration/test_data/json/vocabulary_mappings.json

# Test clinical data streaming
./extract_integration_tests --input=tests/integration/test_data/json/clinical_data.json --stream=true
```

### Multi-Format Pipeline Tests
```bash
# Test JSON to OMOP transformation
./pipeline_integration_tests --json_input=tests/integration/test_data/json/patient_records.json \
                            --output_format=omop_cdm

# Test UK healthcare JSON processing
./pipeline_integration_tests --json_input=tests/integration/test_data/json/uk_patient_records.json \
                            --localization=uk_healthcare
```

### Streaming and Large File Tests
```bash
# Test JSON Lines streaming
./extract_integration_tests --input=tests/integration/test_data/json/clinical_data.json \
                          --mode=streaming --format=jsonl

# Test memory-efficient processing
./extract_integration_tests --input=tests/integration/test_data/json/uk_patient_records.json \
                          --memory_limit=100MB
```

## File Format Standards

### JSON Structure Patterns

#### Patient Records Format
```json
[
  {
    "patient_id": 1001,
    "nhs_number": "**********",
    "demographics": {
      "first_name": "James",
      "last_name": "Smith",
      "birth_date": "15/01/1985",
      "gender": "M"
    },
    "address": {
      "postcode": "SW1A 1AA",
      "street": "10 Downing Street",
      "city": "London",
      "country": "United Kingdom"
    },
    "contact": {
      "phone": ["020 7946 0958", "07700 900123"],
      "email": "<EMAIL>"
    },
    "clinical_data": {
      "conditions": [
        {
          "condition_code": "I10",
          "description": "Essential hypertension",
          "diagnosis_date": "01/03/2020"
        }
      ],
      "medications": [
        {
          "drug_code": "123456",
          "drug_name": "Amlodipine",
          "dosage": "5mg",
          "frequency": "daily"
        }
      ]
    }
  }
]
```

#### Vocabulary Mappings Format
```json
{
  "vocabularies": {
    "ICD10": {
      "version": "2021",
      "mappings": {
        "I10": {
          "description": "Essential hypertension",
          "category": "Cardiovascular",
          "snomed_mapping": "38341003"
        }
      }
    },
    "SNOMED_CT": {
      "version": "UK_Edition_20210317",
      "concepts": {
        "38341003": {
          "preferred_term": "Hypertensive disorder",
          "synonyms": ["High blood pressure", "Elevated blood pressure"]
        }
      }
    }
  }
}
```

### UK Healthcare Specific JSON Structures

#### NHS Number Validation
```json
{
  "patient_id": 1001,
  "nhs_number": "**********",
  "nhs_number_status": "verified",
  "verification_date": "2023-01-15"
}
```

#### UK Address Structure
```json
{
  "address": {
    "line1": "10 Downing Street",
    "line2": "Westminster",
    "city": "London",
    "postcode": "SW1A 1AA",
    "country": "United Kingdom",
    "uprn": "10033544614"
  }
}
```

#### UK Phone Numbers
```json
{
  "contact": {
    "landline": "020 7946 0958",
    "mobile": "07700 900123",
    "formatted_numbers": [
      {
        "number": "+44 20 7946 0958",
        "type": "landline",
        "area": "London"
      },
      {
        "number": "+44 7700 900123", 
        "type": "mobile",
        "network": "UK"
      }
    ]
  }
}
```

## Test Environment Requirements

### For JSON Extraction Tests
- JSON parsing libraries (e.g., nlohmann/json, RapidJSON)
- UTF-8 encoding support with Unicode characters
- Streaming JSON parser for large files
- Memory management for nested structures

### For UK Localization Tests  
- UK locale support (en_GB) with JSON data
- NHS number validation within JSON structures
- UK postcode validation for address objects
- British date format parsing (DD/MM/YYYY in strings)
- UK phone number pattern matching

### For Performance Tests
- Large JSON file handling (up to 100MB+)
- Streaming JSON processing capabilities
- Memory-efficient parsing algorithms
- Concurrent JSON processing support

## Quality Assurance

### Data Validation
Each JSON file includes:

1. **Schema Validation**: Well-formed JSON structure
2. **Data Types**: Correct type usage (string, number, boolean)
3. **Nested Structure**: Proper object hierarchy
4. **UTF-8 Compliance**: International character support
5. **UK Standards**: NHS numbers, postcodes, phone formats

### JSON Schema Compliance

#### Patient Record Schema
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["patient_id", "demographics"],
  "properties": {
    "patient_id": {"type": "integer"},
    "nhs_number": {"type": "string", "pattern": "^[0-9]{10}$"},
    "demographics": {
      "type": "object",
      "required": ["first_name", "last_name", "birth_date"],
      "properties": {
        "birth_date": {"type": "string", "pattern": "^[0-9]{2}/[0-9]{2}/[0-9]{4}$"}
      }
    }
  }
}
```

### Test Coverage Matrix

| Test Scenario | Patient Records | Clinical Data | Vocabulary | Reference Data |
|---------------|----------------|---------------|------------|----------------|
| Basic JSON Parsing | ✅ patient_records.json | ✅ clinical_data.json | ✅ vocabulary.json | ✅ All files |
| UK Localization | ✅ uk_patient_records.json | ✅ Nested clinical | ✅ UK terminologies | ✅ UK mappings |
| Nested Structures | ✅ Complex patient data | ✅ Multi-level clinical | ✅ Hierarchical vocab | ✅ All nested |
| Streaming Processing | ✅ Large patient arrays | ✅ Event streams | ✅ Large vocabularies | ✅ All files |

## Integration with Test Suites

### Extract Library Integration Tests
These JSON files are used by:
- `test_json_extractor_integration.cpp`
- `test_multi_source_extraction_integration.cpp`
- `test_streaming_json_integration.cpp`

### Pipeline Integration Tests
Referenced in:
- JSON to OMOP CDM transformation tests
- Multi-format input pipeline tests  
- UK healthcare compliance tests

### Configuration Integration
JSON files are referenced in:
- `extract_json_config.yaml` - JSON extractor configurations
- `extract_multi_source_config.yaml` - Multi-format processing
- `uk_localization_config.yaml` - UK healthcare JSON processing

## Advanced JSON Features

### Streaming Support
```json
// JSON Lines format for streaming
{"patient_id": 1001, "name": "James Smith", "nhs_number": "**********"}
{"patient_id": 1002, "name": "Mary Jones", "nhs_number": "**********"}
{"patient_id": 1003, "name": "Robert Taylor", "nhs_number": "**********"}
```

### Complex Nested Relationships
```json
{
  "patient_id": 1001,
  "episodes": [
    {
      "episode_id": "E001",
      "visits": [
        {
          "visit_id": "V001",
          "procedures": [
            {
              "procedure_code": "12345",
              "observations": [
                {"type": "vital_signs", "value": "120/80 mmHg"}
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### Multi-Language Support
```json
{
  "condition": {
    "code": "I10",
    "descriptions": {
      "en_GB": "Essential hypertension",
      "en_US": "Essential hypertension", 
      "cy_GB": "Gorbwysedd hanfodol"
    },
    "locale_specific": {
      "uk": {
        "nhs_code": "G20..00",
        "read_code": "G20"
      }
    }
  }
}
```

## Maintenance Notes

### Adding New JSON Files
1. Follow naming convention: `{domain}_{purpose}.json`
2. Validate JSON syntax and schema compliance
3. Use UTF-8 encoding for international characters
4. Include UK healthcare standards where applicable
5. Add entry to this README with purpose and scope

### Updating Existing Files
1. Maintain backward compatibility with JSON schema
2. Preserve object structure and required fields
3. Validate nested data consistency
4. Test changes against JSON parser tests
5. Update documentation to reflect schema changes

### Performance Considerations
1. Large files should support streaming processing
2. Nested structures should not exceed reasonable depth (< 10 levels)
3. Array sizes should be manageable for memory constraints
4. Include performance benchmarks for large JSON files

## Troubleshooting

### Common Issues
1. **JSON Syntax Errors**: Validate JSON format with linters
2. **UTF-8 Encoding**: Check international character rendering
3. **Memory Issues**: Use streaming parsers for large files
4. **Nested Depth**: Limit object nesting for parser compatibility
5. **Schema Validation**: Ensure required fields are present

### Debug Strategies
For debugging failed JSON tests:
1. Use JSON validators to check syntax
2. Test streaming vs. full parsing modes
3. Validate NHS numbers and UK data formats
4. Check nested object access patterns
5. Monitor memory usage with large files

### Performance Optimization
1. **Streaming Processing**: Use SAX-style parsers for large files
2. **Memory Management**: Implement object pooling for nested structures
3. **Parallel Processing**: Parse multiple JSON files concurrently
4. **Compression**: Consider compressed JSON for large datasets

---

**Last Updated**: January 2025  
**Maintained By**: OMOP CDM ETL Team  
**Review Schedule**: Quarterly updates recommended 