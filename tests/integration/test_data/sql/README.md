# Integration Test SQL Data Files

This directory contains SQL test data files for various integration test scenarios across the OMOP CDM ETL pipeline components.

## File Organization

### 🗄️ Schema Definition Files
Database structure and table creation scripts:

| File | Purpose | Test Scope |
|------|---------|------------|
| `test_schema.sql` | Test database schema creation | Source and CDM table structures, constraints |

### 📊 Test Data Files
Sample data insertion and test setup scripts:

| File | Purpose | Test Scope |
|------|---------|------------|
| `test_data.sql` | Test data population scripts | Sample records, reference data, test scenarios |

## Data Categories

### 🎯 **Schema Testing**
- `test_schema.sql` - Database structure validation
- Table creation and constraint testing
- Index definition and performance testing
- Foreign key relationship validation

### 🔧 **Data Population Testing**
- `test_data.sql` - Sample data insertion
- Reference data setup
- Test scenario data preparation
- Data quality validation records

### 📋 **Database Integration Testing**
- Multi-schema database setup
- Cross-database ETL pipeline testing
- OMOP CDM schema compliance
- Performance benchmarking data

### 🌍 **OMOP CDM Compliance Testing**
- Standard OMOP table structures
- OMOP vocabulary integration
- CDM version compatibility
- Constraint and relationship validation

## Usage Examples

### Schema Setup
```bash
# Create test schemas and tables
psql -h localhost -U test_user -d test_db -f tests/integration/test_data/sql/test_schema.sql

# MySQL version
mysql -h localhost -u test_user -p test_db < tests/integration/test_data/sql/test_schema.sql

# SQLite version
sqlite3 test.db < tests/integration/test_data/sql/test_schema.sql
```

### Test Data Population
```bash
# Load test data
psql -h localhost -U test_user -d test_db -f tests/integration/test_data/sql/test_data.sql

# Load with transaction handling
psql -h localhost -U test_user -d test_db -c "BEGIN; \i tests/integration/test_data/sql/test_data.sql; COMMIT;"

# Load with error handling
psql -h localhost -U test_user -d test_db -v ON_ERROR_STOP=1 -f tests/integration/test_data/sql/test_data.sql
```

### Database Integration Tests
```bash
# Run database extractor tests
./database_integration_tests --schema_file=tests/integration/test_data/sql/test_schema.sql \
                           --data_file=tests/integration/test_data/sql/test_data.sql

# Run ETL pipeline with database source
./pipeline_integration_tests --source_schema=tests/integration/test_data/sql/test_schema.sql \
                            --source_data=tests/integration/test_data/sql/test_data.sql \
                            --target_schema=omop_cdm
```

## File Format Standards

### SQL Script Structure

#### Schema Definition Format
```sql
-- test_schema.sql structure
-- ===========================

-- Schema creation
CREATE SCHEMA IF NOT EXISTS test_source;
CREATE SCHEMA IF NOT EXISTS test_cdm;

-- Source tables (healthcare data input)
CREATE TABLE IF NOT EXISTS test_source.patients_test (
    patient_id INTEGER PRIMARY KEY,
    birth_date DATE NOT NULL,
    gender VARCHAR(10),
    race VARCHAR(50),
    ethnicity VARCHAR(50),
    nhs_number VARCHAR(10),
    postcode VARCHAR(10)
);

-- OMOP CDM tables (output format)
CREATE TABLE IF NOT EXISTS test_cdm.person (
    person_id INTEGER PRIMARY KEY,
    gender_concept_id INTEGER NOT NULL,
    year_of_birth INTEGER NOT NULL,
    month_of_birth INTEGER,
    day_of_birth INTEGER,
    birth_datetime TIMESTAMP,
    race_concept_id INTEGER NOT NULL,
    ethnicity_concept_id INTEGER NOT NULL
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_patients_test_birth_date 
ON test_source.patients_test(birth_date);

-- Constraints
ALTER TABLE test_source.patients_test 
ADD CONSTRAINT chk_gender CHECK (gender IN ('M', 'F', 'U'));
```

#### Data Population Format
```sql
-- test_data.sql structure
-- ========================

-- Clear existing test data
TRUNCATE TABLE test_source.patients_test CASCADE;
TRUNCATE TABLE test_source.conditions_test CASCADE;

-- Insert test patients
INSERT INTO test_source.patients_test 
(patient_id, birth_date, gender, race, ethnicity, nhs_number, postcode) 
VALUES 
(1001, '1985-01-15', 'M', 'White', 'Not Hispanic', '**********', 'SW1A 1AA'),
(1002, '1990-02-28', 'F', 'Asian', 'Not Hispanic', '**********', 'EC1A 1BB'),
(1003, '1975-12-31', 'M', 'Black', 'Hispanic', '**********', 'M1 1AA');

-- Insert test conditions
INSERT INTO test_source.conditions_test 
(condition_id, patient_id, condition_code, condition_date) 
VALUES 
(2001, 1001, 'I10', '2020-03-01'),
(2002, 1002, 'E11', '2019-06-15'),
(2003, 1003, 'J44', '2021-01-20');

-- Insert reference data
INSERT INTO test_source.vocabulary_test 
(vocabulary_id, concept_code, concept_name, domain_id) 
VALUES 
('ICD10CM', 'I10', 'Essential hypertension', 'Condition'),
('ICD10CM', 'E11', 'Type 2 diabetes mellitus', 'Condition'),
('ICD10CM', 'J44', 'Other chronic obstructive pulmonary disease', 'Condition');
```

### Database-Specific Features

#### PostgreSQL Features
```sql
-- Use PostgreSQL-specific features
CREATE SCHEMA IF NOT EXISTS test_source;

-- UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- JSONB support for complex data
CREATE TABLE test_source.clinical_notes (
    note_id SERIAL PRIMARY KEY,
    patient_id INTEGER,
    note_content JSONB,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Full-text search
CREATE INDEX idx_notes_fulltext ON test_source.clinical_notes 
USING gin(to_tsvector('english', note_content));
```

#### MySQL Features
```sql
-- MySQL-specific syntax
CREATE DATABASE IF NOT EXISTS test_source 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Auto-increment and timestamp features
CREATE TABLE test_source.patients_test (
    patient_id INT AUTO_INCREMENT PRIMARY KEY,
    birth_date DATE NOT NULL,
    created_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;
```

#### SQLite Features
```sql
-- SQLite compatibility
PRAGMA foreign_keys = ON;

-- Simple table creation
CREATE TABLE patients_test (
    patient_id INTEGER PRIMARY KEY AUTOINCREMENT,
    birth_date TEXT NOT NULL,
    gender TEXT CHECK (gender IN ('M', 'F', 'U'))
);
```

## Test Environment Requirements

### For Database Integration Tests
- PostgreSQL 12+ (primary database)
- MySQL 8.0+ (secondary database for compatibility)
- SQLite 3.30+ (lightweight testing)
- Database driver libraries (libpq, mysql-connector, sqlite3)

### For OMOP CDM Tests  
- OMOP CDM vocabulary tables
- Sufficient database permissions for schema creation
- Test database isolation from production
- Transaction support for rollback testing

### For Performance Tests
- Database with sufficient storage (>1GB for large tests)
- Index optimization capabilities
- Query execution plan analysis tools
- Connection pooling support

## Quality Assurance

### SQL Script Validation
Each SQL file includes:

1. **Syntax Validation**: Database-specific SQL compliance
2. **Schema Consistency**: Proper table relationships
3. **Data Integrity**: Constraint and validation rules
4. **Performance**: Appropriate indexing strategies
5. **OMOP Compliance**: CDM standard adherence

### Database Compatibility Matrix

| Feature | PostgreSQL | MySQL | SQLite | SQL Server |
|---------|------------|-------|--------|------------|
| Schema Creation | ✅ Full Support | ✅ Full Support | ⚠️ Limited | ✅ Full Support |
| Foreign Keys | ✅ Yes | ✅ Yes | ✅ Yes | ✅ Yes |
| Transactions | ✅ ACID | ✅ ACID | ✅ ACID | ✅ ACID |
| JSON Support | ✅ JSONB | ✅ JSON | ✅ JSON1 | ✅ JSON |
| Full-Text Search | ✅ Built-in | ✅ Built-in | ✅ FTS5 | ✅ Built-in |
| UUID Support | ✅ Extension | ✅ Built-in | ⚠️ Manual | ✅ Built-in |

### Test Coverage Matrix

| Test Scenario | Schema Setup | Data Population | OMOP Compliance | Performance |
|---------------|--------------|-----------------|-----------------|-------------|
| Basic Database Setup | ✅ test_schema.sql | ✅ test_data.sql | ✅ CDM tables | ✅ Indexes |
| Multi-Schema Testing | ✅ Source + CDM | ✅ Cross-schema | ✅ Relationships | ✅ Constraints |
| Data Type Testing | ✅ All SQL types | ✅ Sample data | ✅ OMOP types | ✅ Type performance |
| Constraint Testing | ✅ All constraints | ✅ Validation data | ✅ CDM rules | ✅ Constraint perf |

## Integration with Test Suites

### Database Integration Tests
These SQL files are used by:
- `test_database_extractor_integration.cpp`
- `test_database_loader_integration.cpp`
- `test_omop_tables_integration.cpp`

### ETL Pipeline Tests
Referenced in:
- Full ETL workflow tests
- Database-to-database transformation
- OMOP CDM compliance validation

### Configuration Integration
SQL files are referenced in:
- `extract_database_config.yaml` - Database connection configs
- `test_config.yaml` - Full pipeline database tests
- Database-specific configuration files

## Advanced SQL Features

### Transaction Management
```sql
-- Transaction testing patterns
BEGIN;

-- Insert test data
INSERT INTO test_source.patients_test VALUES (...);

-- Test rollback scenarios
SAVEPOINT test_point;
INSERT INTO test_source.invalid_data VALUES (...);
ROLLBACK TO test_point;

-- Commit successful operations
COMMIT;
```

### Error Handling
```sql
-- Error condition testing
DO $$
BEGIN
    -- Attempt operation that may fail
    INSERT INTO test_source.patients_test 
    VALUES (1001, 'invalid_date', 'X');  -- Should fail constraint
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'Constraint violation handled correctly';
END $$;
```

### Performance Testing Data
```sql
-- Generate large datasets for performance testing
INSERT INTO test_source.patients_test 
SELECT 
    generate_series(1, 100000) as patient_id,
    '1980-01-01'::date + (random() * 365 * 40)::int as birth_date,
    CASE WHEN random() < 0.5 THEN 'M' ELSE 'F' END as gender;
```

## Maintenance Notes

### Adding New SQL Files
1. Follow naming convention: `{purpose}_{database_type}.sql`
2. Include database compatibility comments
3. Add transaction management where appropriate
4. Include rollback/cleanup procedures
5. Add entry to this README with purpose and scope

### Updating Existing Files
1. Maintain backward compatibility with existing schemas
2. Use `IF NOT EXISTS` clauses for creation statements
3. Include migration scripts for schema changes
4. Test changes against all supported databases
5. Update documentation to reflect schema changes

### OMOP CDM Compliance
When updating OMOP-related SQL:
1. Follow latest OMOP CDM specifications
2. Include proper vocabulary table references
3. Maintain concept_id relationships
4. Include data quality constraints
5. Test against OMOP data quality dashboard

## Troubleshooting

### Common Issues
1. **Permission Errors**: Ensure database user has CREATE/DROP privileges
2. **Schema Conflicts**: Check for existing schema/table names
3. **Data Type Mismatches**: Verify column type compatibility
4. **Constraint Violations**: Check referential integrity requirements
5. **Character Encoding**: Ensure UTF-8 support for international data

### Debug Procedures
For debugging failed SQL tests:
1. Run SQL scripts manually to identify syntax errors
2. Check database logs for constraint violations
3. Verify database connectivity and permissions
4. Test with smaller datasets to isolate issues
5. Use `EXPLAIN PLAN` for performance issues

### Performance Optimization
1. **Index Analysis**: Use database query planners
2. **Statistics Updates**: Ensure table statistics are current
3. **Connection Pooling**: Configure appropriate pool sizes
4. **Memory Settings**: Optimize database memory configuration

---

**Last Updated**: January 2025  
**Maintained By**: OMOP CDM ETL Team  
**Review Schedule**: Quarterly updates recommended 