// Integration test for validation framework

#include <gtest/gtest.h>
#include "integration_test_base.h"
#include "database_fixture.h"
#include "common/validation.h"
#include "common/utilities.h"
#include "core/interfaces.h"
#include "transform/transformation_engine.h"
#include "test_helpers/test_data_generator.h"
#include <nlohmann/json.hpp>
#include <fstream>
#include <sstream>

using namespace omop::test;
using namespace omop::common;
using namespace omop::core;

class ValidationIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Initialize validation engine
        engine_ = std::make_unique<BasicValidationEngine>(logger_);

        // Initialize data generator
        generator_ = std::make_unique<TestDataGenerator>(42);
    }

    std::unique_ptr<BasicValidationEngine> engine_;
    std::unique_ptr<TestDataGenerator> generator_;
};

TEST_F(ValidationIntegrationTest, BasicValidationRules) {
    // Test basic validation rules

    // Add validation rules
    engine_->addRule(std::make_unique<NotNullRule>("person_id"));
    engine_->addRule(std::make_unique<NotNullRule>("birth_date"));

    // Create test record with valid data
    std::unordered_map<std::string, std::any> valid_record;
    valid_record["person_id"] = int64_t(12345);
    valid_record["birth_date"] = std::chrono::system_clock::now();
    valid_record["name"] = std::string("John Doe");

    omop::common::ValidationResult result = engine_->validateRecord(valid_record);
    EXPECT_TRUE(result.is_valid());
    EXPECT_TRUE(result.errors().empty());

    // Test with missing required field
    std::unordered_map<std::string, std::any> invalid_record;
    invalid_record["name"] = std::string("Jane Doe");
    // person_id and birth_date are missing

    result = engine_->validateRecord(invalid_record);
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.errors().size(), 2);
}

TEST_F(ValidationIntegrationTest, NumericRangeValidation) {
    // Test numeric range validation

    engine_->addRule(std::make_unique<NumericRangeRule<int>>(
        "year_of_birth", 1900, 2024));

    engine_->addRule(std::make_unique<NumericRangeRule<double>>(
        "weight_kg", 0.5, 500.0));

    // Valid values
    std::unordered_map<std::string, std::any> record;
    record["year_of_birth"] = 1985;
    record["weight_kg"] = 75.5;

    omop::common::ValidationResult result = engine_->validateRecord(record);
    EXPECT_TRUE(result.is_valid());

    // Invalid year (future)
    record["year_of_birth"] = 2050;
    result = engine_->validateRecord(record);
    EXPECT_FALSE(result.is_valid());

    // Invalid weight (negative)
    record["year_of_birth"] = 1985;
    record["weight_kg"] = -10.0;
    result = engine_->validateRecord(record);
    EXPECT_FALSE(result.is_valid());
}

TEST_F(ValidationIntegrationTest, DateRangeValidation) {
    // Test date range validation

    auto min_date = std::chrono::system_clock::now() - std::chrono::hours(24 * 365 * 100);
    auto max_date = std::chrono::system_clock::now();

    engine_->addRule(std::make_unique<DateRangeRule>(
        "birth_date", min_date, max_date));

    // Valid date
    std::unordered_map<std::string, std::any> record;
    record["birth_date"] = std::chrono::system_clock::now() - std::chrono::hours(24 * 365 * 30);

    omop::common::ValidationResult result = engine_->validateRecord(record);
    EXPECT_TRUE(result.is_valid());

    // Future date (invalid)
    record["birth_date"] = std::chrono::system_clock::now() + std::chrono::hours(24 * 365);
    result = engine_->validateRecord(record);
    EXPECT_FALSE(result.is_valid());
}

TEST_F(ValidationIntegrationTest, InListValidation) {
    // Test in-list validation

    std::vector<std::string> valid_genders = {"M", "F", "U"};
    engine_->addRule(std::make_unique<InListRule<std::string>>(
        "gender", valid_genders));

    std::vector<int> valid_concepts = {8507, 8532, 0};
    engine_->addRule(std::make_unique<InListRule<int>>(
        "gender_concept_id", valid_concepts));

    // Valid values
    std::unordered_map<std::string, std::any> record;
    record["gender"] = std::string("M");
    record["gender_concept_id"] = 8507;

    omop::common::ValidationResult result = engine_->validateRecord(record);
    EXPECT_TRUE(result.is_valid());

    // Invalid gender
    record["gender"] = std::string("X");
    result = engine_->validateRecord(record);
    EXPECT_FALSE(result.is_valid());
}

TEST_F(ValidationIntegrationTest, RegexValidation) {
    // Test regex pattern validation

    // Email validation
    engine_->addRule(std::make_unique<RegexRule>(
        "email", R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)"));

    // Phone number validation
    engine_->addRule(std::make_unique<RegexRule>(
        "phone", R"(^\+?1?\d{10,14}$)"));

    // Postal code validation
    engine_->addRule(std::make_unique<RegexRule>(
        "postal_code", R"(^\d{5}(-\d{4})?$)"));

    // Valid data
    std::unordered_map<std::string, std::any> record;
    record["email"] = std::string("<EMAIL>");
    record["phone"] = std::string("+14155551234");
    record["postal_code"] = std::string("12345-6789");

    omop::common::ValidationResult result = engine_->validateRecord(record);
    EXPECT_TRUE(result.is_valid());

    // Invalid email
    record["email"] = std::string("invalid.email");
    result = engine_->validateRecord(record);
    EXPECT_FALSE(result.is_valid());
}

TEST_F(ValidationIntegrationTest, CustomValidationRules) {
    // Test custom validation rules

    // Custom rule: birth date must be before death date
    auto date_order_rule = std::make_unique<CustomRule>(
        "death_date",
        [](const std::any& value, const std::unordered_map<std::string, std::any>& record) {
            if (!value.has_value()) return true; // Null is valid

            auto death_date_it = record.find("death_date");
            auto birth_date_it = record.find("birth_date");

            if (death_date_it == record.end() || birth_date_it == record.end()) {
                return true; // Can't validate without both dates
            }

            try {
                auto death = std::any_cast<std::chrono::system_clock::time_point>(
                    death_date_it->second);
                auto birth = std::any_cast<std::chrono::system_clock::time_point>(
                    birth_date_it->second);

                return death > birth;
            } catch (...) {
                return false;
            }
        },
        "Death date must be after birth date"
    );

    engine_->addRule(std::move(date_order_rule));

    // Valid record
    std::unordered_map<std::string, std::any> record;
    auto birth = std::chrono::system_clock::now() - std::chrono::hours(24 * 365 * 70);
    auto death = std::chrono::system_clock::now() - std::chrono::hours(24 * 365);

    record["birth_date"] = birth;
    record["death_date"] = death;

    omop::common::ValidationResult result = engine_->validateRecord(record);
    EXPECT_TRUE(result.is_valid());

    // Invalid record (death before birth)
    record["death_date"] = birth - std::chrono::hours(24 * 365);
    result = engine_->validateRecord(record);
    EXPECT_FALSE(result.is_valid());
}

TEST_F(ValidationIntegrationTest, BatchValidation) {
    // Test batch validation performance

    // Set up rules
    engine_->addRule(std::make_unique<NotNullRule>("person_id"));
    engine_->addRule(std::make_unique<NumericRangeRule<int>>(
        "year_of_birth", 1900, 2024));
    engine_->addRule(std::make_unique<InListRule<std::string>>(
        "gender", std::vector<std::string>{"M", "F", "U"}));

    // Generate test batch
    const size_t batch_size = 10000;
    std::vector<std::unordered_map<std::string, std::any>> batch;

    for (size_t i = 0; i < batch_size; ++i) {
        std::unordered_map<std::string, std::any> record;
        record["person_id"] = int64_t(i + 1);
        record["year_of_birth"] = 1980;
        record["gender"] = std::string("M");

        // Introduce some invalid records
        if (i % 100 == 0) {
            record["year_of_birth"] = 2050; // Invalid year
        }
        if (i % 200 == 0) {
            record.erase("person_id"); // Missing required field
        }

        batch.push_back(record);
    }

    // Validate batch
    auto start = std::chrono::high_resolution_clock::now();
    omop::common::ValidationResult result = engine_->validateBatch(batch);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end - start);

    logger_->info("Validated {} records in {} ms", batch_size, duration.count());

    // Verify results
    EXPECT_GT(result.errors().size(), 0);

    // Performance check
    double records_per_second = (batch_size * 1000.0) / duration.count();
    EXPECT_GT(records_per_second, 10000); // Should validate >10k records/sec
}

TEST_F(ValidationIntegrationTest, ValidationWithYAMLConfig) {
    // Test creating validation engine from YAML configuration

    std::string validation_config = R"(
validations:
  - field: person_id
    type: not_null

  - field: year_of_birth
    type: range
    min: 1900
    max: 2024

  - field: gender_concept_id
    type: in_list
    values: [8507, 8532, 0]

  - field: email
    type: regex
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

  - field: weight_kg
    type: range
    min: 0.5
    max: 500.0
)";

    auto engine = ValidationRuleFactory::createEngineFromYaml(validation_config);
    ASSERT_NE(nullptr, engine);

    // Test with valid record
    std::unordered_map<std::string, std::any> record;
    record["person_id"] = int64_t(1001);
    record["year_of_birth"] = 1985;
    record["gender_concept_id"] = 8507;
    record["email"] = std::string("<EMAIL>");
    record["weight_kg"] = 75.5;

    omop::common::ValidationResult result = engine->validateRecord(record);
    EXPECT_TRUE(result.is_valid());
}

TEST_F(ValidationIntegrationTest, ValidationUtilities) {
    // Test validation utility functions

    // Email validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_email("<EMAIL>"));
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_email("<EMAIL>"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_email("invalid.email"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_email("@example.com"));

    // Phone number validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_phone("1234567890", "US"));
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_phone("+14155551234", "US"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_phone("123", "US"));

    // Postal code validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_postal_code("12345", "US"));
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_postal_code("12345-6789", "US"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_postal_code("ABCDE", "US"));

    // UUID validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_uuid("550e8400-e29b-41d4-a716-************"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_uuid("not-a-uuid"));

    // URL validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_url("https://www.example.com"));
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_url("http://example.com:8080/path"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_url("not a url"));

    // JSON validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_json("{\"key\": \"value\"}"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_json("invalid json"));

    // SQL identifier validation
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_sql_identifier("table_name"));
    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_sql_identifier("column123"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_sql_identifier("table-name"));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_sql_identifier("123table"));

    // String sanitization
    std::string dangerous = "<script>alert('XSS')</script>";
    std::string safe = omop::common::ValidationUtils::sanitize_string(dangerous);
    EXPECT_NE(dangerous, safe);
    EXPECT_FALSE(safe.find("<script>") != std::string::npos);
}

TEST_F(ValidationIntegrationTest, TypeSpecificValidation) {
    // Test validation with specific data types

    engine_->setValidationTypeEnabled(ValidationType::NOT_NULL, true);
    engine_->setValidationTypeEnabled(ValidationType::NUMERIC_RANGE, true);

    // Add type-specific rules
    engine_->addRule(std::make_unique<NotNullRule>("integer_field"));
    engine_->addRule(std::make_unique<NumericRangeRule<int>>(
        "integer_field", 0, 100));

    // Test with different types
    std::unordered_map<std::string, std::any> record;

    // Integer validation
    record["integer_field"] = 50;
    omop::common::ValidationResult result = engine_->validateRecord(record);
    EXPECT_TRUE(result.is_valid());

    // String that looks like integer
    record["integer_field"] = std::string("50");
    result = engine_->validateRecord(record);
    // Should fail type validation
    EXPECT_FALSE(result.is_valid());

    // Double value for integer field
    record["integer_field"] = 50.5;
    result = engine_->validateRecord(record);
    // May pass or fail depending on implementation
}

TEST_F(ValidationIntegrationTest, ConcurrentValidation) {
    // Test thread-safe validation

    // Set up validation rules
    engine_->addRule(std::make_unique<NotNullRule>("id"));
    engine_->addRule(std::make_unique<NumericRangeRule<int>>("value", 0, 1000));

    const int num_threads = 8;
    const int records_per_thread = 1000;
    std::vector<std::thread> threads;
    std::atomic<size_t> total_validated{0};
    std::atomic<size_t> total_failed{0};

    auto validate_func = [this, &total_validated, &total_failed](int thread_id) {
        for (int i = 0; i < records_per_thread; ++i) {
            std::unordered_map<std::string, std::any> record;
            record["id"] = thread_id * 10000 + i;
            record["value"] = generator_->random_int(0, 1100); // Some will fail

            omop::common::ValidationResult result = engine_->validateRecord(record);
            total_validated++;
            if (result.errors().size() > 0) {
                total_failed++;
            }
        }
    };

    // Launch threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(validate_func, i);
    }

    // Wait for completion
    for (auto& thread : threads) {
        thread.join();
    }

    // Verify results
    EXPECT_EQ(num_threads * records_per_thread, total_validated.load());
    EXPECT_GT(total_failed.load(), 0); // Some should have failed range validation
}

TEST_F(ValidationIntegrationTest, ComplexBusinessRules) {
    // Test complex business rule validation

    // Rule: Person must have at least one observation period
    // Rule: Observation period must not overlap
    // Rule: Death date must be within an observation period

    // This would require cross-record validation
    class CrossRecordValidator {
    public:
        omop::common::ValidationResult validate(
            const std::vector<std::unordered_map<std::string, std::any>>& persons,
            const std::vector<std::unordered_map<std::string, std::any>>& periods) {

            omop::common::ValidationResult result;

            // Check each person has at least one period
            for (const auto& person : persons) {
                auto person_id = std::any_cast<int64_t>(person.at("person_id"));

                bool has_period = false;
                for (const auto& period : periods) {
                    if (std::any_cast<int64_t>(period.at("person_id")) == person_id) {
                        has_period = true;
                        break;
                    }
                }

                if (!has_period) {
                    result.add_error("observation_period", "Person " + std::to_string(person_id) + " has no observation period", "cross_record");
                }
            }

            return result;
        }
    };

    // Test cross-record validation
    std::vector<std::unordered_map<std::string, std::any>> persons;
    std::vector<std::unordered_map<std::string, std::any>> periods;

    // Add persons
    for (int i = 1; i <= 5; ++i) {
        std::unordered_map<std::string, std::any> person;
        person["person_id"] = int64_t(i);
        persons.push_back(person);
    }

    // Add periods for only some persons
    for (int i = 1; i <= 3; ++i) {
        std::unordered_map<std::string, std::any> period;
        period["observation_period_id"] = int64_t(i);
        period["person_id"] = int64_t(i);
        periods.push_back(period);
    }

    CrossRecordValidator validator;
    omop::common::ValidationResult result = validator.validate(persons, periods);

    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(2, result.errors().size()); // Persons 4 and 5 have no periods
}