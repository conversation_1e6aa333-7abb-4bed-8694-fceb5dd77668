// Integration test for logging system

#include <gtest/gtest.h>
#include "integration_test_base.h"
#include "database_fixture.h"
#include "common/logging.h"
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <nlohmann/json.hpp>
#include <thread>
#include <regex>

using namespace omop::test;
using namespace omop::common;

class LoggingIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Initialize logging for tests
        LoggingConfig::initialize_default();

        // Create test log file path
        log_file_path_ = get_test_output_path("test.log");
        json_log_file_path_ = get_test_output_path("test.json.log");
    }

    void TearDown() override {
        // Ensure all logs are flushed
        LoggingConfig::flush_all();

        IntegrationTestBase::TearDown();
    }

    std::filesystem::path log_file_path_;
    std::filesystem::path json_log_file_path_;
};

TEST_F(LoggingIntegrationTest, BasicLoggingFunctionality) {
    // Test basic logging operations

    auto logger = Logger::get("test-logger");

    // Log messages at different levels
    logger->trace("This is a trace message");
    logger->debug("Debug message with value: {}", 42);
    logger->info("Information message");
    logger->warn("Warning: {} items remaining", 10);
    logger->error("Error occurred: {}", "test error");
    logger->critical("Critical error!");

    // Flush logs
    LoggingConfig::flush_all();

    // Basic test - just verify logger works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, StructuredLogging) {
    // Test structured logging with context

    auto logger = Logger::get("structured-logger");
    logger->set_job_id("job-123");
    logger->set_component("test-component");

    // Log with structured context
    std::unordered_map<std::string, std::any> context;
    context["user_id"] = 12345;
    context["operation"] = "data_load";
    context["table"] = "person";
    context["record_count"] = 1000;

    logger->log_structured(LogLevel::Info, "Processing batch", context);

    // Log operation
    logger->log_operation("extract_data", "completed", {
        {"duration_ms", 1500},
        {"records_processed", 5000},
        {"errors", 0}
    });

    // Log metrics
    logger->log_metrics({
        {"cpu_usage", 75.5},
        {"memory_mb", 2048.0},
        {"disk_io_mbps", 150.0}
    });

    LoggingConfig::flush_all();

    // Basic test - just verify structured logging works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, DatabaseLogging) {
    // Test database logging functionality
    
    // Create a mock database connection using a dummy struct
    struct DummyConnection {};
    auto mock_connection = std::make_shared<DummyConnection>();
    
    // Cast to std::shared_ptr<void> for DatabaseLogSink
    std::shared_ptr<void> connection_void = std::static_pointer_cast<void>(mock_connection);
    
    // Create database log sink
    auto db_sink = std::make_shared<DatabaseLogSink>(connection_void, "test_logs");
    
    // Add sink to logger
    auto logger = Logger::get("db-test-logger");
    logger->add_sink(db_sink);
    
    // Log some test messages
    logger->info("Database log test message 1");
    logger->warn("Database log test message 2");
    logger->error("Database log test message 3");
    
    // Flush logs
    LoggingConfig::flush_all();
    
    // Basic test - verify that database logging works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, PerformanceLogging) {
    // Test performance logging functionality

    auto logger = Logger::get("perf-logger");
    auto perf_logger = std::make_unique<PerformanceLogger>(logger);

    // Test scoped timer
    {
        auto timer = perf_logger->scoped_timer("data_processing");

        // Simulate work
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        timer.set_record_count(1000);
    }

    // Log throughput
    perf_logger->log_throughput("processing", 5000.0);

    // Log resource usage
    perf_logger->log_resource_usage(45.5, 1024.0, 256.0);

    // Basic test - just verify performance logging works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, AuditLogging) {
    // Test audit logging functionality with UK localization

    auto logger = Logger::get("audit-logger");
    auto audit_logger = std::make_unique<AuditLogger>(logger);

    // Log data access
    audit_logger->log_data_access("person", "read", 1000, "test_user");

    // Log configuration change
    audit_logger->log_config_change("batch_size", "1000", "5000", "admin_user");

    // Log security event
    audit_logger->log_security_event("unauthorized_access", {
        {"ip_address", "*************"},
        {"resource", "protected_table"},
        {"action", "denied"}
    });

    // Basic test - just verify audit logging works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, ConcurrentLogging) {
    // Test concurrent logging from multiple threads

    auto logger = Logger::get("concurrent-logger");

    const int num_threads = 4;
    std::vector<std::thread> threads;

    auto log_func = [&logger](int thread_id) {
        for (int i = 0; i < 100; ++i) {
            logger->info("Thread {} message {}", thread_id, i);
        }
    };

    // Start threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(log_func, i);
    }

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    // Basic test - just verify concurrent logging works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, LogRotation) {
    // Test log rotation functionality
    
    // Create test log file path
    auto test_log_path = get_test_output_path("test_rotation.log");
    
    // Create file sink with small max size to trigger rotation
    auto file_sink = std::make_shared<FileSink>(test_log_path.string(), 1024, 3); // 1KB max, 3 backup files
    
    // Add sink to logger
    auto logger = Logger::get("rotation-test-logger");
    logger->add_sink(file_sink);
    
    // Write enough data to trigger rotation
    std::string large_message(200, 'X'); // 200 character message
    for (int i = 0; i < 10; ++i) {
        logger->info("Rotation test message {}: {}", i, large_message);
    }
    
    // Flush logs
    LoggingConfig::flush_all();
    
    // Verify that the log file exists
    EXPECT_TRUE(std::filesystem::exists(test_log_path));
    
    // Verify that rotation files were created (at least one backup should exist)
    auto backup_path = test_log_path.string() + ".1";
    EXPECT_TRUE(std::filesystem::exists(backup_path));
    
    // Clean up test files
    std::filesystem::remove(test_log_path);
    std::filesystem::remove(backup_path);
}

TEST_F(LoggingIntegrationTest, LogLevelFiltering) {
    // Test log level filtering

    auto logger = Logger::get("level-test-logger");

    // Set to debug level
    logger->set_level(LogLevel::Debug);

    // All messages should be logged
    logger->trace("Trace message");
    logger->debug("Debug message");
    logger->info("Info message");
    logger->warn("Warning message");
    logger->error("Error message");
    logger->critical("Critical message");

    // Set to warning level
    logger->set_level(LogLevel::Warning);

    // Only warning and above should be logged
    logger->trace("Trace message (should not appear)");
    logger->debug("Debug message (should not appear)");
    logger->info("Info message (should not appear)");
    logger->warn("Warning message (should appear)");
    logger->error("Error message (should appear)");
    logger->critical("Critical message (should appear)");

    // Basic test - just verify level filtering works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, CustomFormatter) {
    // Test custom formatter

    class CustomFormatter : public ILogFormatter {
    public:
        std::string format(const LogEntry& entry) override {
            std::string level_str;
            switch (entry.level) {
                case LogLevel::Trace: level_str = "TRACE"; break;
                case LogLevel::Debug: level_str = "DEBUG"; break;
                case LogLevel::Info: level_str = "INFO"; break;
                case LogLevel::Warning: level_str = "WARN"; break;
                case LogLevel::Error: level_str = "ERROR"; break;
                case LogLevel::Critical: level_str = "CRITICAL"; break;
                default: level_str = "UNKNOWN"; break;
            }

            return fmt::format("[CUSTOM] {} - {}: {}",
                              entry.logger_name,
                              level_str,
                              entry.message);
        }
    };

    auto logger = Logger::get("custom-formatter-logger");

    // Note: Custom formatter would need to be set on a sink
    // For now, just test that the formatter can be created
    auto formatter = std::make_unique<CustomFormatter>();
    EXPECT_TRUE(formatter != nullptr);

    // Basic test - just verify custom formatter works without errors
    EXPECT_TRUE(true);
}

TEST_F(LoggingIntegrationTest, GlobalLoggingConfiguration) {
    // Test global logging configuration

    // Initialize with default configuration
    LoggingConfig::initialize_default();

    // Set global level
    LoggingConfig::set_global_level(LogLevel::Info);

    // Create a new logger
    auto logger = Logger::get("global-config-test");

    // Test logging
    logger->info("Global config test message");
    logger->debug("Debug message (should not appear)");

    // Flush all loggers
    LoggingConfig::flush_all();

    // Basic test - just verify global configuration works without errors
    EXPECT_TRUE(true);
}