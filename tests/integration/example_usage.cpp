#include <iostream>
#include <memory>
#include "common/configuration.h"
#include "common/logging.h"
#include "core/pipeline.h"
#include "extract/database_connector.h"
#include "extract/csv_extractor.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"

using namespace omop;

/**
 * Example 1: Simple database-to-database ETL
 */
void example_database_etl() {
    // Initialize logging
    common::LoggingConfig::initialize_default();
    auto logger = common::Logger::get("example");

    try {
        // Load configuration
        auto config = std::make_shared<common::ConfigurationManager>();
        config->load_config("config/etl/config.yaml");

        // Create pipeline builder
        auto pipeline = core::PipelineBuilder()
            .with_config({
                .batch_size = 1000,
                .max_parallel_batches = 4,
                .error_threshold = 0.01
            })
            // Set up database extractor
            .with_extractor("database", {
                {"table_name", std::string("patients")},
                {"columns", std::vector<std::string>{"patient_id", "birth_date", "gender", "race"}}
            })
            // Set up transformer for person table
            .with_transformer_for_table("person")
            // Set up database loader
            .with_loader("omop_database", {
                {"table_name", std::string("person")},
                {"batch_size", size_t(5000)}
            })
            // Add progress callback
            .with_progress_callback([](const core::JobInfo& info) {
                std::cout << "\rProgress: " << info.processed_records
                         << "/" << info.total_records
                         << " (" << info.progress() << "%)" << std::flush;
            })
            // Add error callback
            .with_error_callback([logger](const std::string& msg, const std::exception& e) {
                logger->error("Pipeline error: {} - {}", msg, e.what());
            })
            .build();

        // Start the pipeline
        auto future = pipeline->start("person_etl_001");

        // Wait for completion
        auto job_info = future.get();

        std::cout << "\nETL completed:" << std::endl;
        std::cout << "  Processed: " << job_info.processed_records << std::endl;
        std::cout << "  Errors: " << job_info.error_records << std::endl;
        std::cout << "  Duration: " << job_info.duration().count() << "s" << std::endl;

    } catch (const std::exception& e) {
        logger->error("ETL failed: {}", e.what());
    }
}

/**
 * Example 2: CSV file ETL with custom transformation
 */
void example_csv_etl() {
    auto logger = common::Logger::get("example");

    try {
        // Create CSV extractor
        auto extractor = std::make_unique<extract::CsvExtractor>();
        extractor->initialize({
            {"filepath", std::string("data/patients.csv")},
            {"delimiter", char(',')},
            {"has_header", true}
        }, core::ProcessingContext{});

        // Create custom transformation engine
        auto transformer = std::make_unique<transform::TransformationEngine>();

        // Register custom transformation
        transformer->register_transformation("normalize_gender",
            []() -> std::unique_ptr<transform::FieldTransformation> {
                class GenderNormalizer : public transform::FieldTransformation {
                public:
                    std::any transform(const std::any& input, core::ProcessingContext&) override {
                        if (input.type() == typeid(std::string)) {
                            std::string value = std::any_cast<std::string>(input);
                            std::transform(value.begin(), value.end(), value.begin(), ::toupper);
                            if (value == "M" || value == "MALE") return std::string("Male");
                            if (value == "F" || value == "FEMALE") return std::string("Female");
                        }
                        return std::string("Unknown");
                    }

                    bool validate_input(const std::any& input) const override {
                        return input.has_value();
                    }

                    std::string get_type() const override { return "normalize_gender"; }
                    void configure(const YAML::Node&) override {}
                };
                return std::make_unique<GenderNormalizer>();
            });

        // Create file loader (for testing/debugging)
        class DebugLoader : public core::ILoader {
        public:
            void initialize(const std::unordered_map<std::string, std::any>&,
                           core::ProcessingContext&) override {}

            bool load(const core::Record& record, core::ProcessingContext& context) override {
                // Just print the record
                std::cout << "Record: ";
                for (const auto& field : record.get_field_names()) {
                    auto value = record.get_field(field);
                    if (value) {
                        std::cout << field << "=";
                        if (value->type() == typeid(std::string)) {
                            std::cout << std::any_cast<std::string>(*value);
                        } else if (value->type() == typeid(int64_t)) {
                            std::cout << std::any_cast<int64_t>(*value);
                        }
                        std::cout << " ";
                    }
                }
                std::cout << std::endl;
                context.increment_processed();
                return true;
            }

            size_t load_batch(const core::RecordBatch& batch,
                            core::ProcessingContext& context) override {
                size_t count = 0;
                for (const auto& record : batch) {
                    if (load(record, context)) count++;
                }
                return count;
            }

            void commit(core::ProcessingContext&) override {}
            void rollback(core::ProcessingContext&) override {}
            std::string get_type() const override { return "debug"; }
            void finalize(core::ProcessingContext&) override {}

            std::unordered_map<std::string, std::any> get_statistics() const override {
                return {};
            }
        };

        // Create pipeline
        auto pipeline = std::make_unique<core::ETLPipeline>();
        pipeline->set_extractor(std::move(extractor));
        pipeline->set_transformer(std::move(transformer));
        pipeline->set_loader(std::make_unique<DebugLoader>());

        // Run pipeline
        auto future = pipeline->start("csv_debug_001");
        auto result = future.get();

        logger->info("CSV ETL completed: {} records processed", result.processed_records);

    } catch (const std::exception& e) {
        logger->error("CSV ETL failed: {}", e.what());
    }
}

/**
 * Example 3: Using the Pipeline Manager for multiple jobs
 */
void example_pipeline_manager() {
    auto logger = common::Logger::get("example");

    try {
        // Create pipeline manager
        auto manager = std::make_shared<core::PipelineManager>(4); // Max 4 concurrent jobs

        // Submit multiple jobs
        std::vector<std::string> tables = {"person", "visit_occurrence", "condition_occurrence"};
        std::vector<std::string> job_ids;

        for (const auto& table : tables) {
            auto pipeline = core::PipelineBuilder()
                .with_config_file("config/etl/config.yaml")
                .with_extractor("database", {{"table_name", table}})
                .with_transformer_for_table(table)
                .with_loader("omop_database", {{"table_name", table}})
                .build();

            std::string job_id = manager->submit_job(
                table + "_etl",
                std::move(pipeline)
            );

            job_ids.push_back(job_id);
            logger->info("Submitted job: {} for table: {}", job_id, table);
        }

        // Monitor jobs
        while (true) {
            bool all_complete = true;

            for (const auto& job_id : job_ids) {
                auto status = manager->get_job_status(job_id);
                if (status && *status == core::JobStatus::Running) {
                    all_complete = false;

                    auto info = manager->get_job_info(job_id);
                    if (info) {
                        logger->info("Job {}: {} records processed",
                                   job_id, info->processed_records);
                    }
                }
            }

            if (all_complete) break;

            std::this_thread::sleep_for(std::chrono::seconds(5));
        }

        // Print results
        for (const auto& job_id : job_ids) {
            auto info = manager->get_job_info(job_id);
            if (info) {
                logger->info("Job {} completed: {} records, {} errors, {}s",
                           job_id, info->processed_records, info->error_records,
                           info->duration().count());
            }
        }

    } catch (const std::exception& e) {
        logger->error("Pipeline manager failed: {}", e.what());
    }
}

/**
 * Example 4: Custom component implementation
 */
class CustomVocabularyTransformer : public core::ITransformer {
private:
    std::unordered_map<std::string, int> gender_map_ = {
        {"Male", 8507}, {"M", 8507},
        {"Female", 8532}, {"F", 8532},
        {"Unknown", 0}
    };

public:
    void initialize(const std::unordered_map<std::string, std::any>&,
                   core::ProcessingContext&) override {
        // Load custom vocabulary mappings
    }

    std::optional<core::Record> transform(const core::Record& record,
                                         core::ProcessingContext& context) override {
        core::Record transformed;

        // Copy and transform fields
        for (const auto& field : record.get_field_names()) {
            auto value = record.get_field(field);
            if (!value) continue;

            if (field == "gender" && value->type() == typeid(std::string)) {
                // Apply vocabulary mapping
                std::string gender = std::any_cast<std::string>(*value);
                auto it = gender_map_.find(gender);
                int concept_id = (it != gender_map_.end()) ? it->second : 0;

                transformed.set_field("gender_concept_id", concept_id);
                transformed.set_field("gender_source_value", gender);
            } else {
                // Direct copy
                transformed.set_field(field, *value);
            }
        }

        context.increment_processed();
        return transformed;
    }

    core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                     core::ProcessingContext& context) override {
        core::RecordBatch transformed;

        for (const auto& record : batch) {
            auto result = transform(record, context);
            if (result) {
                transformed.add_record(std::move(*result));
            }
        }

        return transformed;
    }

    std::string get_type() const override { return "custom_vocabulary"; }

            omop::common::ValidationResult validate(const core::Record&) const override {
            return omop::common::ValidationResult{};
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {};
    }
};

int main() {
    std::cout << "OMOP ETL Pipeline Examples\n" << std::endl;

    std::cout << "1. Running database-to-database ETL..." << std::endl;
    example_database_etl();

    std::cout << "\n2. Running CSV file ETL..." << std::endl;
    example_csv_etl();

    std::cout << "\n3. Running pipeline manager example..." << std::endl;
    example_pipeline_manager();

    return 0;
}