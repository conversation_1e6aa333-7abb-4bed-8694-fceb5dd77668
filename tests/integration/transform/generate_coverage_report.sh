#!/bin/bash
# Generate test coverage report for transform library integration tests

# Ensure UK locale
export LC_ALL=en_GB.UTF-8
export LANG=en_GB.UTF-8

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "Transform Library Integration Test Coverage Report"
echo "================================================="
echo ""
echo "Generated on: $(date '+%d/%m/%Y %H:%M:%S')"  # UK date format
echo ""

# Function to check test coverage for a header file
check_coverage() {
    local header_file=$1
    local test_files=$2
    
    echo "Checking coverage for: $header_file"
    
    # Extract class names and important functions
    classes=$(grep -E "^class [A-Za-z_]+" "$header_file" | awk '{print $2}' | sed 's/[:{].*//')
    
    if [ -z "$classes" ]; then
        echo -e "${YELLOW}  No classes found${NC}"
    else
        for class in $classes; do
            # Check if class is tested
            if grep -q "$class" $test_files 2>/dev/null; then
                echo -e "${GREEN}  ✓ $class${NC}"
            else
                echo -e "${RED}  ✗ $class${NC}"
            fi
        done
    fi
    echo ""
}

# List of header files to check
HEADERS=(
    "src/lib/transform/validation_engine.h"
    "src/lib/transform/vocabulary_service.h"
    "src/lib/transform/transformations.h"
    "src/lib/transform/transformation_registry_improvements.h"
    "src/lib/transform/field_transformations.h"
    "src/lib/transform/transformation_engine.h"
    "src/lib/transform/custom_transformations.h"
    "src/lib/transform/numeric_transformations.h"
    "src/lib/transform/vocabulary_transformations.h"
    "src/lib/transform/date_transformations.h"
    "src/lib/transform/conditional_transformations.h"
    "src/lib/transform/string_transformations.h"
    "src/lib/transform/transformation_result.h"
)

# Test files
TEST_FILES="tests/integration/transform/test_*.cpp"

# Check each header
for header in "${HEADERS[@]}"; do
    if [ -f "$header" ]; then
        check_coverage "$header" "$TEST_FILES"
    else
        echo -e "${RED}Header not found: $header${NC}"
        echo ""
    fi
done

# Check UK localization compliance
echo "UK Localization Compliance Check"
echo "================================"

echo -n "Date formats (DD/MM/YYYY): "
uk_dates=$(grep -E "(DD/MM/YYYY|%d/%m/%Y)" $TEST_FILES | wc -l)
us_dates=$(grep -E "(MM/DD/YYYY|%m/%d/%Y)" $TEST_FILES | wc -l)
if [ $us_dates -eq 0 ]; then
    echo -e "${GREEN}✓ All dates use UK format ($uk_dates instances)${NC}"
else
    echo -e "${RED}✗ Found $us_dates US date formats${NC}"
fi

echo -n "Currency symbols (£): "
pound_count=$(grep -F "£" $TEST_FILES | wc -l)
echo -e "${GREEN}✓ Found $pound_count instances of UK currency${NC}"

echo ""
echo "Test coverage report complete." 