// Integration tests for field transformations
#include <gtest/gtest.h>
#include "transform/field_transformations.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "test_helpers/transform_test_fixture.h"
#include "test_helpers/test_data_generator.h"
#include "core/interfaces.h"
#include "common/validation.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#include "test_helpers/database_connection_factory.h"

namespace omop::test {

class FieldTransformationsIntegrationTest : public omop::test::TransformTestFixture {
protected:
    std::unique_ptr<omop::transform::VocabularyService> vocab_service_;
    std::unique_ptr<omop::transform::TransformationEngine> engine_;
    std::shared_ptr<omop::common::Logger> logger_;

    void SetUp() override {
        TransformTestFixture::SetUp();
        logger_ = omop::common::Logger::get("field-transform-test");
        
        // Initialize transformation engine
        engine_ = std::make_unique<omop::transform::TransformationEngine>();
        
    }
    
    std::unique_ptr<extract::IDatabaseConnection> create_omop_db_connection() {
        // Use the centralized database connection factory
        try {
            return DatabaseConnectionFactory::createOmopConnection();
        } catch (const std::exception& e) {
            logger_->warn("Exception connecting to OMOP database: {}", e.what());
            return nullptr;
        }
    }

    void TearDown() override {
        TransformTestFixture::TearDown();
    }

    void setupVocabularyService() {
        // Initialize vocabulary service with test data
        // Note: This would need a real database connection in practice
        // vocab_service_ = std::make_unique<omop::transform::VocabularyService>(std::move(connection));
        // vocab_service_->initialize();
    }
};

// Tests fundamental field transformations including type conversion and date parsing
TEST_F(FieldTransformationsIntegrationTest, BasicFieldTransformations) {
    // Create transformations
    auto direct_transform = std::make_unique<omop::transform::DirectTransformation>();
    auto date_transform = std::make_unique<omop::transform::DateTransformation>();
    auto numeric_transform = std::make_unique<omop::transform::NumericTransformation>();
    
    // Configure date transformation
    YAML::Node date_config;
    date_config["input_format"] = "%d/%m/%Y"; // UK date format
    date_config["output_format"] = "%Y-%m-%d";
    date_transform->configure(date_config);
    
    // Configure numeric transformation
    YAML::Node numeric_config;
    numeric_config["precision"] = 0;
    numeric_transform->configure(numeric_config);

    // Create test record
    core::Record input_record;
    input_record.setField("patient_id", int64_t(12345));
    input_record.setField("age_string", std::string("45"));
    input_record.setField("birth_date_string", std::string("15/06/1979")); // UK date format
    input_record.setField("name_full", std::string("  John Doe  "));

    // Create processing context
    omop::core::ProcessingContext context;

    // Apply transformations
    auto person_id = direct_transform->transform(input_record.getField("patient_id"), context);
    auto age_numeric = numeric_transform->transform(input_record.getField("age_string"), context);
    auto birth_date = date_transform->transform(input_record.getField("birth_date_string"), context);
    auto name_direct = direct_transform->transform(input_record.getField("name_full"), context);

    // Verify transformations
    EXPECT_EQ(std::any_cast<int64_t>(person_id), 12345);
    EXPECT_NO_THROW(std::any_cast<double>(age_numeric));
    EXPECT_NO_THROW(std::any_cast<std::string>(birth_date));
    EXPECT_EQ(std::any_cast<std::string>(name_direct), "  John Doe  ");
}

// Test vocabulary-based transformations for field mapping to OMOP concepts
TEST_F(FieldTransformationsIntegrationTest, VocabularyTransformations) {
    // Initialize vocabulary service if not available
    if (!vocab_service_) {
        // Create real OMOP database connection for testing
        auto omop_connection = create_omop_db_connection();
        if (omop_connection) {
            vocab_service_ = std::make_unique<transform::VocabularyService>(std::move(omop_connection));
            ASSERT_NE(vocab_service_, nullptr) << "Failed to create vocabulary service for field transformations";

            // Initialize the vocabulary service with default cache size
            vocab_service_->initialize(1000);
        } else {
            GTEST_SKIP() << "OMOP database connection not available for vocabulary transformations test";
        }
    }

    // Create vocabulary transformation
    auto vocab_transform = std::make_unique<omop::transform::VocabularyTransformation>(*vocab_service_);
    
    // Configure vocabulary mapping
    YAML::Node config;
    config["vocabulary_name"] = "Gender";
    config["source_vocabulary"] = "Custom";
    config["target_vocabulary"] = "OMOP";
    config["default_concept_id"] = 0;
    config["case_sensitive"] = false;
    
    vocab_transform->configure(config);

    // Create processing context
    omop::core::ProcessingContext context;

    // Test gender mapping
    std::string gender_value = "Male";
    auto gender_result = vocab_transform->transform(gender_value, context);
    
    // The result should be a concept ID (integer)
    EXPECT_NO_THROW(std::any_cast<int>(gender_result));
    
    logger_->info("Gender transformation completed");
}

// Tests transformation engine batch processing with performance metrics
TEST_F(FieldTransformationsIntegrationTest, TransformationEngineProcessing) {
    // Initialize transformation engine
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("person");
    config["enable_validation"] = true;
    
    omop::core::ProcessingContext context;
    engine_->initialize(config, context);

    // Create test records
    std::vector<omop::core::Record> records;
    for (int i = 0; i < 10; ++i) {
        omop::core::Record record;
        record.setField("person_id", int64_t(i + 1));
        record.setField("year_of_birth", int32_t(1980 + i));
        record.setField("gender_source_value", std::string(i % 2 == 0 ? "M" : "F"));
        records.push_back(record);
    }

    // Create record batch
    omop::core::RecordBatch batch;
    for (const auto& record : records) {
        batch.addRecord(record);
    }

    // Execute transformation
    auto start = std::chrono::high_resolution_clock::now();
    auto result_batch = engine_->transform_batch(batch, context);
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    // Verify results
    EXPECT_EQ(result_batch.size(), records.size());

    logger_->info("Transformation engine results:");
    logger_->info("  - Records processed: {}", result_batch.size());
    logger_->info("  - Duration: {} ms", duration.count());
    
    // Get statistics
    auto stats = engine_->get_statistics();
    logger_->info("  - Records transformed: {}", 
                 std::any_cast<size_t>(stats["records_transformed"]));
}

// Test string concatenation transformations combining multiple field values
TEST_F(FieldTransformationsIntegrationTest, StringConcatenationTransformation) {
    // Create string concatenation transformation
    auto concat_transform = std::make_unique<omop::transform::StringConcatenationTransformation>();
    
    // Configure transformation
    YAML::Node config;
    config["separator"] = " ";
    config["skip_empty"] = true;
    config["prefix"] = "";
    config["suffix"] = "";
    concat_transform->configure(config);
    
    // Set source fields
    concat_transform->set_source_fields({"first_name", "last_name"});

    // Create test record
    omop::core::Record record;
    record.setField("first_name", std::string("John"));
    record.setField("last_name", std::string("Doe"));
    
    // Create processing context
    omop::core::ProcessingContext context;
    
    // Transform using multiple values
    std::unordered_map<std::string, std::any> values;
    values["first_name"] = std::string("John");
    values["last_name"] = std::string("Doe");
    
    auto result = concat_transform->transform_multiple(values, context);
    
    // Verify result
    EXPECT_EQ(std::any_cast<std::string>(result), "John Doe");
    
    logger_->info("String concatenation result: {}", std::any_cast<std::string>(result));
}

// Test conditional field transformations based on rule evaluation
TEST_F(FieldTransformationsIntegrationTest, ConditionalTransformation) {
    // Create conditional transformation
    auto conditional_transform = std::make_unique<omop::transform::ConditionalTransformation>();
    
    // Configure transformation with age-based conditions
    YAML::Node config;
    config["conditions"][0]["field"] = "age";
    config["conditions"][0]["operator"] = "<";
    config["conditions"][0]["value"] = 18;
    config["conditions"][0]["then_value"] = "Child";
    config["conditions"][1]["field"] = "age";
    config["conditions"][1]["operator"] = "<";
    config["conditions"][1]["value"] = 65;
    config["conditions"][1]["then_value"] = "Adult";
    config["default_value"] = "Elderly";
    
    conditional_transform->configure(config);
    
    // Create processing context
    omop::core::ProcessingContext context;
    
    // Test with different ages
    std::vector<std::pair<int, std::string>> test_cases = {
        {15, "Child"},
        {30, "Adult"},
        {70, "Elderly"}
    };
    
    for (const auto& [age, expected] : test_cases) {
        auto result = conditional_transform->transform(age, context);
        
        // Note: The actual implementation may vary, this is a simplified test
        EXPECT_NO_THROW(std::any_cast<std::string>(result));
        
        logger_->info("Age {} -> Category: {}", age, std::any_cast<std::string>(result));
    }
}

// Test numeric field transformations including unit conversions and calculations
TEST_F(FieldTransformationsIntegrationTest, NumericTransformation) {
    // Create numeric transformation
    auto numeric_transform = std::make_unique<omop::transform::NumericTransformation>();
    
    // Configure for multiplication
    YAML::Node config;
    config["operation"] = "multiply";
    config["operand"] = 2.5;
    config["precision"] = 2;
    
    numeric_transform->configure(config);
    
    // Create processing context
    omop::core::ProcessingContext context;
    
    // Test various numeric inputs
    std::vector<double> test_values = {10.0, 20.5, 0.0, -5.0};
    
    for (double value : test_values) {
        auto result = numeric_transform->transform(value, context);
        
        // Verify transformation
        EXPECT_NO_THROW(std::any_cast<double>(result));
        
        double transformed_value = std::any_cast<double>(result);
        logger_->info("Value {} -> Transformed: {}", value, transformed_value);
    }
    
    // Test with safe transformation
    auto safe_result = numeric_transform->transform_safe(15.0, context);
    EXPECT_TRUE(safe_result.is_success());
    EXPECT_NO_THROW(std::any_cast<double>(safe_result.value));
}

// Test UK-specific date format transformations including DD/MM/YYYY parsing
TEST_F(FieldTransformationsIntegrationTest, DateTransformationUKFormat) {
    // Create date transformation
    auto date_transform = std::make_unique<omop::transform::DateTransformation>();
    
    // Configure for UK date format parsing
    YAML::Node config;
    config["format"] = "%d/%m/%Y";  // UK format DD/MM/YYYY
    config["output_format"] = "%Y-%m-%d";  // ISO format for OMOP
    config["add_time"] = false;
    
    date_transform->configure(config);
    
    // Create processing context
    omop::core::ProcessingContext context;
    
    // Test various UK date formats
    std::vector<std::pair<std::string, std::string>> uk_date_tests = {
        {"15/06/1979", "Valid UK birthdate"},
        {"01/01/2000", "Y2K date"},
        {"29/02/2024", "Leap year date"},
        {"31/12/1999", "Last day of millennium"}
    };
    
    for (const auto& [date_str, description] : uk_date_tests) {
        try {
            auto result = date_transform->transform(date_str, context);
            EXPECT_TRUE(result.has_value()) << "Date transformation failed for: " << date_str;
            
            if (result.type() == typeid(std::string)) {
                std::string iso_date = std::any_cast<std::string>(result);
                EXPECT_FALSE(iso_date.empty()) << "Empty result for date: " << date_str;
                // Verify ISO format (YYYY-MM-DD)
                EXPECT_EQ(iso_date.length(), 10) << "Invalid ISO date length for: " << date_str;
                EXPECT_EQ(iso_date[4], '-') << "Missing first dash in ISO date: " << iso_date;
                EXPECT_EQ(iso_date[7], '-') << "Missing second dash in ISO date: " << iso_date;
                
                logger_->info("UK date transformation: {} -> {} ({})", date_str, iso_date, description);
            }
        } catch (const std::exception& e) {
            FAIL() << "Exception transforming UK date " << date_str << ": " << e.what();
        }
    }
}

} // namespace omop::test