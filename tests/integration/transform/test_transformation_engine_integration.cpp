/**
 * @file test_transformation_engine_integration.cpp
 * @brief Integration tests for TransformationEngine
 */

#include <gtest/gtest.h>
#include "test_helpers/transform_test_fixture.h"
#include "test_helpers/test_data_generator.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/configuration.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#include "test_helpers/database_connection_factory.h"
#include <yaml-cpp/yaml.h>

namespace omop::test {

class TransformationEngineIntegrationTest : public TransformTestFixture {
protected:
    void SetUp() override {
        TransformTestFixture::SetUp();
        
        // Initialize vocabulary service if database is available
        if (db_available_) {
            initialize_vocabulary_service();
        }
        
        // Create transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
    }
    
    void configure_person_transformation() {
        std::string config_yaml = R"(
            tables:
              person:
                source_table: patient_data
                target_table: person
                transformations:
                  - source_column: patient_id
                    target_column: person_id
                    type: direct
                  - source_column: gender
                    target_column: gender_concept_id
                    type: vocabulary_mapping
                    parameters:
                      vocabulary_name: CUSTOM_GENDER
                      default_concept_id: 0
                  - source_column: birth_date
                    target_column: year_of_birth
                    type: date_transform
                    parameters:
                      input_format: "%Y-%m-%d"
                      output_format: "%Y"
                  - source_column: race
                    target_column: race_concept_id
                    type: vocabulary_mapping
                    parameters:
                      vocabulary_name: CUSTOM_RACE
                  - source_column: ethnicity
                    target_column: ethnicity_concept_id
                    type: vocabulary_mapping
                    parameters:
                      vocabulary_name: CUSTOM_ETHNICITY
                filters:
                  - field: age
                    operator: ">="
                    value: 18
                validations:
                  - field: person_id
                    type: required
                  - field: gender_concept_id
                    type: required
                  - field: year_of_birth
                    type: range
                    min: 1900
                    max: 2023
        )";
        
        config_manager_->load_config_from_string(config_yaml);
    }
    
    std::unique_ptr<extract::IDatabaseConnection> create_omop_db_connection() {
        // Use the centralized database connection factory
        try {
            return DatabaseConnectionFactory::createOmopConnection();
        } catch (const std::exception& e) {
            // Log warning but continue - some tests may not need database
            auto logger = common::Logger::get("transformation-engine-test");
            logger->warn("Exception connecting to OMOP database: {}", e.what());
            return nullptr;
        }
    }
    
    std::unique_ptr<transform::VocabularyService> create_transformation_engine_vocabulary_service() {
        // Create real vocabulary service with OMOP database connection
        auto omop_connection = create_omop_db_connection();
        if (!omop_connection) {
            return nullptr;
        }
        auto vocab_service = std::make_unique<transform::VocabularyService>(std::move(omop_connection));
        vocab_service->initialize(1000);
        return vocab_service;
    }
    
    std::unique_ptr<transform::TransformationEngine> engine_;
};

TEST_F(TransformationEngineIntegrationTest, BasicPersonTransformation) {
    configure_person_transformation();
    
    // Initialize engine
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    
    core::ProcessingContext context;
    engine_->initialize(config, context);
    
    // Create test record
    auto input_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"race", std::string("White")},
        {"ethnicity", std::string("Not Hispanic")},
        {"age", 38}
    });
    
    // Transform record
    auto result = engine_->transform(input_record, context);
    
    ASSERT_TRUE(result.has_value());
    
    const auto& transformed = result.value();
    EXPECT_TRUE(transformed.hasField("person_id"));
    EXPECT_EQ(std::any_cast<int>(transformed.getField("person_id")), 12345);
    
    EXPECT_TRUE(transformed.hasField("year_of_birth"));
    EXPECT_EQ(std::any_cast<std::string>(transformed.getField("year_of_birth")), "1985");
}

TEST_F(TransformationEngineIntegrationTest, BatchTransformation) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Create batch of test records
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(100);
    
    core::RecordBatch input_batch;
    for (const auto& record : records) {
        input_batch.addRecord(record);
    }
    
    // Transform batch
    auto result_batch = engine_->transform_batch(input_batch, context);
    
    // Verify results
    EXPECT_GT(result_batch.size(), 0);
    EXPECT_LE(result_batch.size(), input_batch.size()); // Some may be filtered out
    
    // Check first record
    if (!result_batch.isEmpty()) {
        const auto& first = result_batch.getRecord(0);
        EXPECT_TRUE(first.hasField("person_id"));
        EXPECT_TRUE(first.hasField("gender_concept_id"));
        EXPECT_TRUE(first.hasField("year_of_birth"));
    }
}

TEST_F(TransformationEngineIntegrationTest, ValidationRules) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test with invalid record (missing required fields)
    auto invalid_record = create_test_record({
        {"patient_id", 12345},
        // Missing gender
        {"birth_date", std::string("1850-01-01")}, // Too old
        {"age", 173}
    });
    
    auto validation_result = engine_->validate(invalid_record);
    EXPECT_FALSE(validation_result.is_valid());
    EXPECT_GT(validation_result.error_count(), 0);
    
    // Test with valid record
    auto valid_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Female")},
        {"birth_date", std::string("1990-03-20")},
        {"race", std::string("Asian")},
        {"ethnicity", std::string("Hispanic")},
        {"age", 33}
    });
    
    validation_result = engine_->validate(valid_record);
    EXPECT_TRUE(validation_result.is_valid());
}

TEST_F(TransformationEngineIntegrationTest, FilteringRules) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Create records with different ages
    core::RecordBatch input_batch;
    
    // Adult record
    auto adult_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    input_batch.addRecord(adult_record);
    
    // Minor record
    auto minor_record = create_test_record({
        {"patient_id", 12346},
        {"gender", std::string("Female")},
        {"birth_date", std::string("2010-03-20")},
        {"age", 13}
    });
    input_batch.addRecord(minor_record);
    
    // Transform batch
    auto result_batch = engine_->transform_batch(input_batch, context);
    
    // Only adult should pass through filter
    EXPECT_EQ(result_batch.size(), 1);
    if (!result_batch.isEmpty()) {
        const auto& result = result_batch.getRecord(0);
        EXPECT_EQ(std::any_cast<int>(result.getField("person_id")), 12345);
    }
}

// Test vocabulary mapping integration within the transformation engine
TEST_F(TransformationEngineIntegrationTest, VocabularyMapping) {
    // Initialize vocabulary service for transformation engine testing
    if (!vocabulary_service_) {
        vocabulary_service_ = create_transformation_engine_vocabulary_service();
        ASSERT_NE(vocabulary_service_, nullptr) << "Failed to create vocabulary service for transformation engine";
    }
    
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Create test record
    auto input_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"race", std::string("White")},
        {"ethnicity", std::string("Not Hispanic")},
        {"age", 38}
    });
    
    // Transform record
    auto result = engine_->transform(input_record, context);
    
    ASSERT_TRUE(result.has_value());
    
    const auto& transformed = result.value();
    EXPECT_TRUE(transformed.hasField("gender_concept_id"));
    EXPECT_TRUE(transformed.hasField("race_concept_id"));
    EXPECT_TRUE(transformed.hasField("ethnicity_concept_id"));
    
    // Verify concept IDs are valid
    int gender_concept_id = std::any_cast<int>(transformed.getField("gender_concept_id"));
    EXPECT_GT(gender_concept_id, 0);
}

TEST_F(TransformationEngineIntegrationTest, ErrorHandling) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test with malformed record
    auto malformed_record = create_test_record({
        {"patient_id", std::string("not_a_number")}, // Wrong type
        {"gender", std::string("Male")},
        {"birth_date", std::string("invalid_date")}, // Invalid date
        {"age", 38}
    });
    
    auto result = engine_->transform(malformed_record, context);
    
    // Should handle errors gracefully
    if (result.has_value()) {
        // Some transformations might succeed
        const auto& transformed = result.value();
        EXPECT_TRUE(transformed.hasField("gender_concept_id"));
    }
}

TEST_F(TransformationEngineIntegrationTest, PerformanceTest) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Generate large batch of test records
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(10000);
    
    core::RecordBatch input_batch;
    for (const auto& record : records) {
        input_batch.addRecord(record);
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    auto result_batch = engine_->transform_batch(input_batch, context);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_GT(result_batch.size(), 0) << "Should transform some records";
    EXPECT_LT(duration.count(), 10000) << "Should complete within 10 seconds";
    
    std::cout << "Transformed " << result_batch.size() << " records in " 
              << duration.count() << "ms" << std::endl;
}

TEST_F(TransformationEngineIntegrationTest, ConfigurationReloading) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test with initial configuration
    auto record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    // Load new configuration
    std::string new_config_yaml = R"(
        tables:
          person:
            source_table: patient_data
            target_table: person
            transformations:
              - source_column: patient_id
                target_column: person_id
                type: direct
              - source_column: gender
                target_column: gender_concept_id
                type: vocabulary_mapping
                parameters:
                  vocabulary_name: CUSTOM_GENDER
                  default_concept_id: 0
            filters:
              - field: age
                operator: ">="
                value: 21  # Changed from 18
    )";
    
    config_manager_->load_config_from_string(new_config_yaml);
    engine_->initialize(config, context);
    
    // Test with new configuration
    result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value()); // Should still pass (age 38 >= 21)
    
    // Test with record that would be filtered out by new rules
    auto young_record = create_test_record({
        {"patient_id", 12346},
        {"gender", std::string("Female")},
        {"birth_date", std::string("2005-03-20")},
        {"age", 18} // Would pass old filter but fail new filter
    });
    
    auto young_result = engine_->transform(young_record, context);
    // This record should be filtered out by the new age requirement
}

TEST_F(TransformationEngineIntegrationTest, MemoryManagement) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test memory usage with large batches
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(50000);
    
    core::RecordBatch input_batch;
    for (const auto& record : records) {
        input_batch.addRecord(record);
    }
    
    // Process in chunks to avoid memory issues
    const size_t chunk_size = 1000;
    size_t total_processed = 0;
    
    for (size_t i = 0; i < input_batch.size(); i += chunk_size) {
        core::RecordBatch chunk;
        for (size_t j = i; j < std::min(i + chunk_size, input_batch.size()); ++j) {
            chunk.addRecord(input_batch.getRecord(j));
        }
        
        auto result_chunk = engine_->transform_batch(chunk, context);
        total_processed += result_chunk.size();
    }
    
    EXPECT_GT(total_processed, 0) << "Should process some records";
    EXPECT_LE(total_processed, records.size()) << "Should not exceed input size";
}

} // namespace omop::test