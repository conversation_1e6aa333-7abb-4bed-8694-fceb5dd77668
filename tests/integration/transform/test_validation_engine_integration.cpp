/**
 * @file test_validation_engine_integration.cpp
 * @brief Integration tests for ValidationEngine
 */

#include <gtest/gtest.h>
#include "test_helpers/transform_test_fixture.h"
#include "test_helpers/test_data_generator.h"
#include "transform/validation_engine.h"
#include "core/record.h"
#include <yaml-cpp/yaml.h>

namespace omop::test {

class ValidationEngineIntegrationTest : public TransformTestFixture {
protected:
    void SetUp() override {
        TransformTestFixture::SetUp();
        
        // Create validation engine
        validation_engine_ = transform::create_validation_engine();
        
        // Register custom validators
        register_custom_validators();
    }
    
    void register_custom_validators() {
        // Register NHS number validator
        validation_engine_->register_validator("nhs_number", []() {
            class NHSNumberValidator : public transform::IFieldValidator {
                omop::common::ValidationResult validate(const std::any& value,
                                                       core::ProcessingContext& context) override {
                    omop::common::ValidationResult result;
                    
                    if (!value.has_value()) {
                        result.add_error(field_name_, "NHS number is required", "nhs_number");
                        return result;
                    }
                    
                    try {
                        auto nhs_str = std::any_cast<std::string>(value);
                        
                        // Remove spaces
                        nhs_str.erase(std::remove(nhs_str.begin(), nhs_str.end(), ' '), 
                                     nhs_str.end());
                        
                        // Check length
                        if (nhs_str.length() != 10) {
                            result.add_error(field_name_, 
                                           "NHS number must be 10 digits", 
                                           "nhs_number");
                            return result;
                        }
                        
                        // Validate checksum
                        int checksum = 0;
                        for (int i = 0; i < 9; ++i) {
                            if (!std::isdigit(nhs_str[i])) {
                                result.add_error(field_name_, 
                                               "NHS number must contain only digits", 
                                               "nhs_number");
                                return result;
                            }
                            checksum += (nhs_str[i] - '0') * (10 - i);
                        }
                        
                        int check_digit = 11 - (checksum % 11);
                        if (check_digit == 11) check_digit = 0;
                        if (check_digit == 10) {
                            result.add_error(field_name_, 
                                           "Invalid NHS number checksum", 
                                           "nhs_number");
                            return result;
                        }
                        
                        if ((nhs_str[9] - '0') != check_digit) {
                            result.add_error(field_name_, 
                                           "Invalid NHS number checksum", 
                                           "nhs_number");
                        }
                        
                    } catch (const std::bad_any_cast&) {
                        result.add_error(field_name_, 
                                       "NHS number must be a string", 
                                       "nhs_number");
                    }
                    
                    return result;
                }
                
                std::string get_type() const override { return "nhs_number"; }
                void configure(const YAML::Node& params) override {}
                const std::string& get_field_name() const override { return field_name_; }
                void set_field_name(const std::string& name) override { field_name_ = name; }
                
            private:
                std::string field_name_;
            };
            
            return std::make_unique<NHSNumberValidator>();
        });
        
        // Register date consistency validator
        validation_engine_->register_validator("date_consistency", []() {
            class DateConsistencyValidator : public transform::IFieldValidator {
                omop::common::ValidationResult validate(const std::any& value,
                                                       core::ProcessingContext& context) override {
                    omop::common::ValidationResult result;
                    
                    // This validator checks consistency between multiple date fields
                    // It needs access to the full record, which we'll handle through context
                    
                    return result;
                }
                
                std::string get_type() const override { return "date_consistency"; }
                void configure(const YAML::Node& params) override {
                    if (params["start_date_field"]) {
                        start_date_field_ = params["start_date_field"].as<std::string>();
                    }
                    if (params["end_date_field"]) {
                        end_date_field_ = params["end_date_field"].as<std::string>();
                    }
                }
                const std::string& get_field_name() const override { return field_name_; }
                void set_field_name(const std::string& name) override { field_name_ = name; }
                
            private:
                std::string field_name_;
                std::string start_date_field_;
                std::string end_date_field_;
            };
            
            return std::make_unique<DateConsistencyValidator>();
        });
    }
    
    std::unique_ptr<transform::IValidationEngine> validation_engine_;
};

TEST_F(ValidationEngineIntegrationTest, BasicFieldValidation) {
    YAML::Node validation_config;
    validation_config["validations"]["person_id"]["type"] = "required";
    validation_config["validations"]["person_id"]["message"] = "Person ID is required";
    
    validation_config["validations"]["year_of_birth"]["type"] = "range";
    validation_config["validations"]["year_of_birth"]["min"] = 1900;
    validation_config["validations"]["year_of_birth"]["max"] = 2023;
    validation_config["validations"]["year_of_birth"]["message"] = "Year of birth must be between 1900 and 2023";
    
    validation_config["validations"]["nhs_number"]["type"] = "nhs_number";
    validation_config["validations"]["nhs_number"]["message"] = "Invalid NHS number format";
    
    validation_engine_->load_configuration(validation_config);
    
    core::ProcessingContext context;
    
    // Test valid record
    auto valid_record = create_test_record({
        {"person_id", 12345},
        {"year_of_birth", 1985},
        {"nhs_number", std::string("**********")}
    });
    
    auto result = validation_engine_->validate_record(valid_record, context);
    EXPECT_TRUE(result.is_valid()) << "Valid record should pass validation";
    
    // Test invalid record
    auto invalid_record = create_test_record({
        {"person_id", 0}, // Invalid person ID
        {"year_of_birth", 1850}, // Too old
        {"nhs_number", std::string("123456789")} // Invalid NHS number
    });
    
    result = validation_engine_->validate_record(invalid_record, context);
    EXPECT_FALSE(result.is_valid()) << "Invalid record should fail validation";
    EXPECT_GT(result.error_count(), 0) << "Should have validation errors";
}

TEST_F(ValidationEngineIntegrationTest, CustomValidatorIntegration) {
    YAML::Node validation_config;
    validation_config["validations"]["nhs_number"]["type"] = "nhs_number";
    
    validation_engine_->load_configuration(validation_config);
    
    core::ProcessingContext context;
    
    // Test valid NHS number
    auto valid_record = create_test_record({
        {"nhs_number", std::string("**********")}
    });
    
    auto result = validation_engine_->validate_record(valid_record, context);
    EXPECT_TRUE(result.is_valid()) << "Valid NHS number should pass validation";
    
    // Test invalid NHS number
    auto invalid_record = create_test_record({
        {"nhs_number", std::string("123456789")} // Wrong length
    });
    
    result = validation_engine_->validate_record(invalid_record, context);
    EXPECT_FALSE(result.is_valid()) << "Invalid NHS number should fail validation";
}

TEST_F(ValidationEngineIntegrationTest, CrossFieldValidation) {
    YAML::Node validation_config;
    validation_config["validations"]["admission_date"]["type"] = "required";
    validation_config["validations"]["discharge_date"]["type"] = "required";
    validation_config["validations"]["date_consistency"]["type"] = "date_consistency";
    validation_config["validations"]["date_consistency"]["start_date_field"] = "admission_date";
    validation_config["validations"]["date_consistency"]["end_date_field"] = "discharge_date";
    
    validation_engine_->load_configuration(validation_config);
    
    core::ProcessingContext context;
    
    // Test consistent dates
    auto valid_record = create_test_record({
        {"admission_date", std::string("2023-01-15")},
        {"discharge_date", std::string("2023-01-20")}
    });
    
    auto result = validation_engine_->validate_record(valid_record, context);
    EXPECT_TRUE(result.is_valid()) << "Consistent dates should pass validation";
    
    // Test inconsistent dates
    auto invalid_record = create_test_record({
        {"admission_date", std::string("2023-01-20")},
        {"discharge_date", std::string("2023-01-15")} // Discharge before admission
    });
    
    result = validation_engine_->validate_record(invalid_record, context);
    // Note: This test depends on the implementation of date_consistency validator
}

TEST_F(ValidationEngineIntegrationTest, BatchValidation) {
    YAML::Node validation_config;
    validation_config["validations"]["person_id"]["type"] = "required";
    validation_config["validations"]["year_of_birth"]["type"] = "range";
    validation_config["validations"]["year_of_birth"]["min"] = 1900;
    validation_config["validations"]["year_of_birth"]["max"] = 2023;
    
    validation_engine_->load_configuration(validation_config);
    
    core::ProcessingContext context;
    
    // Create batch of records
    std::vector<core::Record> records;
    
    // Valid record
    records.push_back(create_test_record({
        {"person_id", 12345},
        {"year_of_birth", 1985}
    }));
    
    // Invalid record
    records.push_back(create_test_record({
        {"person_id", 0}, // Invalid
        {"year_of_birth", 1850} // Too old
    }));
    
    // Valid record
    records.push_back(create_test_record({
        {"person_id", 67890},
        {"year_of_birth", 1990}
    }));
    
    auto results = validation_engine_->validate_batch(records, context);
    
    EXPECT_EQ(results.size(), 3) << "Should return results for all records";
    EXPECT_TRUE(results[0].is_valid()) << "First record should be valid";
    EXPECT_FALSE(results[1].is_valid()) << "Second record should be invalid";
    EXPECT_TRUE(results[2].is_valid()) << "Third record should be valid";
}

TEST_F(ValidationEngineIntegrationTest, PerformanceTest) {
    YAML::Node validation_config;
    validation_config["validations"]["person_id"]["type"] = "required";
    validation_config["validations"]["year_of_birth"]["type"] = "range";
    validation_config["validations"]["year_of_birth"]["min"] = 1900;
    validation_config["validations"]["year_of_birth"]["max"] = 2023;
    
    validation_engine_->load_configuration(validation_config);
    
    core::ProcessingContext context;
    
    // Generate large batch of test records
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(10000);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    auto results = validation_engine_->validate_batch(records, context);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(results.size(), 10000) << "Should validate all records";
    EXPECT_LT(duration.count(), 5000) << "Validation should complete within 5 seconds";
    
    std::cout << "Validated " << records.size() << " records in " 
              << duration.count() << "ms" << std::endl;
}

TEST_F(ValidationEngineIntegrationTest, ErrorReporting) {
    YAML::Node validation_config;
    validation_config["validations"]["person_id"]["type"] = "required";
    validation_config["validations"]["person_id"]["message"] = "Person ID is required";
    
    validation_config["validations"]["year_of_birth"]["type"] = "range";
    validation_config["validations"]["year_of_birth"]["min"] = 1900;
    validation_config["validations"]["year_of_birth"]["max"] = 2023;
    validation_config["validations"]["year_of_birth"]["message"] = "Year of birth must be between 1900 and 2023";
    
    validation_engine_->load_configuration(validation_config);
    
    core::ProcessingContext context;
    
    auto invalid_record = create_test_record({
        {"year_of_birth", 1850} // Missing person_id, invalid year
    });
    
    auto result = validation_engine_->validate_record(invalid_record, context);
    
    EXPECT_FALSE(result.is_valid()) << "Record should be invalid";
    EXPECT_EQ(result.error_count(), 2) << "Should have 2 validation errors";
    
    // Check error messages
    bool found_person_id_error = false;
    bool found_year_error = false;
    
    for (const auto& error : result.errors()) {
        if (error.field_name == "person_id") {
            found_person_id_error = true;
            EXPECT_EQ(error.error_message, "Person ID is required");
        } else if (error.field_name == "year_of_birth") {
            found_year_error = true;
            EXPECT_EQ(error.error_message, "Year of birth must be between 1900 and 2023");
        }
    }
    
    EXPECT_TRUE(found_person_id_error) << "Should have person_id error";
    EXPECT_TRUE(found_year_error) << "Should have year_of_birth error";
}

TEST_F(ValidationEngineIntegrationTest, ConfigurationReloading) {
    YAML::Node initial_config;
    initial_config["validations"]["person_id"]["type"] = "required";
    
    validation_engine_->load_configuration(initial_config);
    
    core::ProcessingContext context;
    
    // Test with initial config
    auto record = create_test_record({
        {"person_id", 12345}
    });
    
    auto result = validation_engine_->validate_record(record, context);
    EXPECT_TRUE(result.is_valid()) << "Should pass with initial config";
    
    // Load new configuration
    YAML::Node new_config;
    new_config["validations"]["person_id"]["type"] = "range";
    new_config["validations"]["person_id"]["min"] = 10000;
    new_config["validations"]["person_id"]["max"] = 99999;
    
    validation_engine_->load_configuration(new_config);
    
    // Test with new config
    result = validation_engine_->validate_record(record, context);
    EXPECT_TRUE(result.is_valid()) << "Should pass with new config";
    
    // Test with invalid value for new config
    auto invalid_record = create_test_record({
        {"person_id", 999} // Too small for new range
    });
    
    result = validation_engine_->validate_record(invalid_record, context);
    EXPECT_FALSE(result.is_valid()) << "Should fail with new config";
}

} // namespace omop::test 