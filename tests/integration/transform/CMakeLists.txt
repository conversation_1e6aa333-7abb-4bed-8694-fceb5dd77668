# Transform integration tests
set(TRANSFORM_INTEGRATION_TEST_SOURCES
    test_complex_transformations_integration.cpp
    test_custom_transformations_integration.cpp
    test_custom_transformations_extended_integration.cpp
    test_field_transformations_integration.cpp
    test_transformation_engine_integration.cpp
    test_vocabulary_service_integration.cpp
    test_validation_engine_integration.cpp
    test_transformation_registry_integration.cpp
    test_transformation_cache_metrics_integration.cpp
    test_edge_cases_integration.cpp
    test_uk_localization_integration.cpp
)

add_executable(transform_integration_tests ${TRANSFORM_INTEGRATION_TEST_SOURCES})

target_link_libraries(transform_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(transform_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

# Link additional libraries for custom transformations
target_link_libraries(transform_integration_tests
    PRIVATE
        dl  # For plugin loading
)

add_test(
    NAME transform_integration_tests
    COMMAND transform_integration_tests
)

set_tests_properties(transform_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;transform"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)