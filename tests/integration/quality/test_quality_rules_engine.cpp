// tests/integration/quality/test_quality_rules_engine.cpp
// Quality rules engine evaluation tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <functional>
#include "core/record.h"
#include "common/validation.h"
#include "test_helpers/test_data_generator.h"
#include <unordered_set>
#include <string>
#include "common/utilities.h"

namespace omop::quality::test {
using namespace std;
using namespace omop::core;

class QualityRulesEngineTest : public ::testing::Test {
protected:
    class QualityRule {
    public:
        enum class Severity {
            Info,
            Warning,
            Error,
            Critical
        };

        struct RuleResult {
            bool passed{true};
            Severity severity{Severity::Info};
            std::string rule_name;
            std::string description;
            std::vector<std::string> violations;
            std::unordered_map<std::string, double> metrics;
        };

        QualityRule(const std::string& name, Severity severity)
            : name_(name), severity_(severity) {}

        virtual ~QualityRule() = default;

        virtual RuleResult Evaluate(const std::vector<core::Record>& records) = 0;

        const std::string& GetName() const { return name_; }
        Severity GetSeverity() const { return severity_; }

    protected:
        std::string name_;
        Severity severity_;
    };

    class CompletenessRule : public QualityRule {
    public:
        CompletenessRule(const std::string& field_name, double threshold)
            : QualityRule("Completeness_" + field_name, Severity::Warning),
              field_name_(field_name), threshold_(threshold) {}

        RuleResult Evaluate(const std::vector<core::Record>& records) override {
            RuleResult result;
            result.rule_name = name_;

            if (records.empty()) {
                result.passed = true;
                return result;
            }

            size_t non_null_count = 0;
            for (const auto& record : records) {
                auto value = record.getFieldOptional(field_name_);
                if (value.has_value() && value->has_value()) {
                    non_null_count++;
                }
            }

            double completeness = static_cast<double>(non_null_count) / records.size();
            result.metrics["completeness"] = completeness;

            if (completeness < threshold_) {
                result.passed = false;
                result.severity = severity_;
                result.violations.push_back(
                    "Field '" + field_name_ + "' completeness " +
                    std::to_string(completeness * 100) + "% is below threshold " +
                    std::to_string(threshold_ * 100) + "%"
                );
            }

            result.description = "Completeness check for field '" + field_name_ + "'";
            return result;
        }

    private:
        std::string field_name_;
        double threshold_;
    };

    class UniquenessRule : public QualityRule {
    public:
        UniquenessRule(const std::string& field_name, bool must_be_unique = true)
            : QualityRule("Uniqueness_" + field_name,
                         must_be_unique ? Severity::Error : Severity::Warning),
              field_name_(field_name), must_be_unique_(must_be_unique) {}

        RuleResult Evaluate(const std::vector<core::Record>& records) override {
            RuleResult result;
            result.rule_name = name_;

            std::unordered_map<std::string, size_t> value_counts;
            size_t total_values = 0;

            for (const auto& record : records) {
                auto value = record.getFieldOptional(field_name_);
                if (value.has_value() && value->has_value()) {
                    std::string str_value = omop::common::any_to_string(*value);
                    value_counts[str_value]++;
                    total_values++;
                }
            }

            size_t unique_values = value_counts.size();
            double uniqueness = total_values > 0 ?
                static_cast<double>(unique_values) / total_values : 1.0;

            result.metrics["uniqueness"] = uniqueness;
            result.metrics["unique_count"] = static_cast<double>(unique_values);
            result.metrics["total_count"] = static_cast<double>(total_values);

            if (must_be_unique_ && uniqueness < 1.0) {
                result.passed = false;
                result.severity = severity_;

                // Find duplicates
                for (const auto& [value, count] : value_counts) {
                    if (count > 1) {
                        result.violations.push_back(
                            "Value '" + value + "' appears " +
                            std::to_string(count) + " times"
                        );
                    }
                }
            }

            result.description = "Uniqueness check for field '" + field_name_ + "'";
            return result;
        }

    private:
        std::string field_name_;
        bool must_be_unique_;
    };

    class ReferentialIntegrityRule : public QualityRule {
    public:
        ReferentialIntegrityRule(const std::string& field_name,
                                const std::string& reference_field,
                                const std::vector<core::Record>& reference_records)
            : QualityRule("ReferentialIntegrity_" + field_name, Severity::Error),
              field_name_(field_name), reference_field_(reference_field) {

            // Build reference set
            for (const auto& record : reference_records) {
                auto value = record.getFieldOptional(reference_field);
                if (value.has_value() && value->has_value()) {
                    reference_values_.insert(omop::common::any_to_string(*value));
                }
            }
        }

        RuleResult Evaluate(const std::vector<core::Record>& records) override {
            RuleResult result;
            result.rule_name = name_;

            size_t total_references = 0;
            size_t valid_references = 0;
            std::unordered_map<std::string, size_t> invalid_refs;

            for (const auto& record : records) {
                auto value = record.getFieldOptional(field_name_);
                if (value.has_value() && value->has_value()) {
                    std::string str_value = omop::common::any_to_string(*value);
                    total_references++;

                    if (reference_values_.find(str_value) != reference_values_.end()) {
                        valid_references++;
                    } else {
                        invalid_refs[str_value]++;
                    }
                }
            }

            double integrity = total_references > 0 ?
                static_cast<double>(valid_references) / total_references : 1.0;

            result.metrics["referential_integrity"] = integrity;
            result.metrics["valid_references"] = static_cast<double>(valid_references);
            result.metrics["invalid_references"] =
                static_cast<double>(total_references - valid_references);

            if (!invalid_refs.empty()) {
                result.passed = false;
                result.severity = severity_;

                for (const auto& [ref, count] : invalid_refs) {
                    result.violations.push_back(
                        "Invalid reference '" + ref + "' found " +
                        std::to_string(count) + " times"
                    );
                }
            }

            result.description = "Referential integrity check for field '" +
                               field_name_ + "' against '" + reference_field_ + "'";
            return result;
        }

    private:
        std::string field_name_;
        std::string reference_field_;
        std::unordered_set<std::string> reference_values_;
    };

    class DataTypeConsistencyRule : public QualityRule {
    public:
        DataTypeConsistencyRule(const std::string& field_name,
                               const std::string& expected_type)
            : QualityRule("DataType_" + field_name, Severity::Error),
              field_name_(field_name), expected_type_(expected_type) {}

        RuleResult Evaluate(const std::vector<core::Record>& records) override {
            RuleResult result;
            result.rule_name = name_;

            std::unordered_map<std::string, size_t> type_counts;
            size_t total_values = 0;

            for (const auto& record : records) {
                auto value = record.getFieldOptional(field_name_);
                if (value.has_value() && value->has_value()) {
                    std::string type_name = GetTypeName(*value);
                    type_counts[type_name]++;
                    total_values++;
                }
            }

            // Check if expected type is dominant
            size_t expected_count = type_counts[expected_type_];
            double consistency = total_values > 0 ?
                static_cast<double>(expected_count) / total_values : 0.0;

            result.metrics["type_consistency"] = consistency;

            if (consistency < 1.0 && total_values > 0) {
                result.passed = false;
                result.severity = severity_;

                for (const auto& [type, count] : type_counts) {
                    if (type != expected_type_) {
                        result.violations.push_back(
                            "Found " + std::to_string(count) + " values of type '" +
                            type + "' instead of expected '" + expected_type_ + "'"
                        );
                    }
                }
            }

            result.description = "Data type consistency check for field '" +
                               field_name_ + "'";
            return result;
        }

    private:
        std::string field_name_;
        std::string expected_type_;

        std::string GetTypeName(const std::any& value) {
            if (value.type() == typeid(int)) return "int";
            if (value.type() == typeid(int64_t)) return "int64";
            if (value.type() == typeid(double)) return "double";
            if (value.type() == typeid(float)) return "float";
            if (value.type() == typeid(std::string)) return "string";
            if (value.type() == typeid(bool)) return "bool";
            if (value.type() == typeid(std::chrono::system_clock::time_point)) return "datetime";
            return "unknown";
        }
    };

    class QualityRulesEngine {
    public:
        struct EngineResult {
            bool all_passed{true};
            size_t total_rules{0};
            size_t passed_rules{0};
            size_t failed_rules{0};
            std::vector<QualityRule::RuleResult> rule_results;
            std::unordered_map<std::string, double> aggregate_metrics;
        };

        void AddRule(std::unique_ptr<QualityRule> rule) {
            rules_.push_back(std::move(rule));
        }

        EngineResult EvaluateRules(const std::vector<core::Record>& records) {
            EngineResult result;
            result.total_rules = rules_.size();

            for (const auto& rule : rules_) {
                auto rule_result = rule->Evaluate(records);

                if (rule_result.passed) {
                    result.passed_rules++;
                } else {
                    result.failed_rules++;
                    result.all_passed = false;
                }

                result.rule_results.push_back(rule_result);

                // Aggregate metrics
                for (const auto& [metric, value] : rule_result.metrics) {
                    std::string full_metric_name = rule->GetName() + "." + metric;
                    result.aggregate_metrics[full_metric_name] = value;
                }
            }

            // Calculate overall quality score
            result.aggregate_metrics["overall_quality_score"] =
                result.total_rules > 0 ?
                static_cast<double>(result.passed_rules) / result.total_rules : 1.0;

            return result;
        }

        void ClearRules() {
            rules_.clear();
        }

    private:
        std::vector<std::unique_ptr<QualityRule>> rules_;
    };

protected:
    std::unique_ptr<omop::test::TestDataGenerator> generator_;
    std::unique_ptr<QualityRulesEngine> engine_;

    void SetUp() override {
        generator_ = std::make_unique<omop::test::TestDataGenerator>();
        engine_ = std::make_unique<QualityRulesEngine>();
    }
};

TEST_F(QualityRulesEngineTest, CompletenessRules) {
    // Generate test data with controlled completeness
    std::vector<core::Record> records;

    for (int i = 0; i < 100; ++i) {
        core::Record record;
        record.setField("id", i);

        // 90% complete field
        if (i % 10 != 0) {
            record.setField("name", "Patient " + std::to_string(i));
        }

        // 70% complete field
        if (i % 10 < 7) {
            record.setField("email", "patient" + std::to_string(i) + "@example.com");
        }

        // 100% complete field
        record.setField("created_date", std::chrono::system_clock::now());

        records.push_back(record);
    }

    // Add completeness rules
    engine_->AddRule(std::make_unique<CompletenessRule>("name", 0.95));     // Will fail
    engine_->AddRule(std::make_unique<CompletenessRule>("email", 0.65));    // Will pass
    engine_->AddRule(std::make_unique<CompletenessRule>("created_date", 1.0)); // Will pass

    // Evaluate rules
    auto result = engine_->EvaluateRules(records);

    // Verify results
    EXPECT_FALSE(result.all_passed) << "Not all rules should pass";
    EXPECT_EQ(result.total_rules, 3);
    EXPECT_EQ(result.passed_rules, 2);
    EXPECT_EQ(result.failed_rules, 1);

    // Check specific rule results
    EXPECT_NEAR(result.aggregate_metrics["Completeness_name.completeness"], 0.9, 0.01);
    EXPECT_NEAR(result.aggregate_metrics["Completeness_email.completeness"], 0.7, 0.01);
    EXPECT_DOUBLE_EQ(result.aggregate_metrics["Completeness_created_date.completeness"], 1.0);
}

TEST_F(QualityRulesEngineTest, UniquenessRules) {
    // Generate test data with duplicates
    std::vector<core::Record> records;

    for (int i = 0; i < 100; ++i) {
        core::Record record;
        record.setField("id", i); // Unique
        record.setField("email", "user" + std::to_string(i % 95) + "@example.com"); // 5 duplicates
        record.setField("category", "Category" + std::to_string(i % 10)); // 10 unique values
        records.push_back(record);
    }

    // Add uniqueness rules
    engine_->AddRule(std::make_unique<UniquenessRule>("id", true));      // Must be unique
    engine_->AddRule(std::make_unique<UniquenessRule>("email", true));   // Will fail
    engine_->AddRule(std::make_unique<UniquenessRule>("category", false)); // Warning only

    // Evaluate rules
    auto result = engine_->EvaluateRules(records);

    // Verify results
    EXPECT_FALSE(result.all_passed);
    EXPECT_EQ(result.failed_rules, 1); // Only email should fail

    // Check uniqueness metrics
    EXPECT_DOUBLE_EQ(result.aggregate_metrics["Uniqueness_id.uniqueness"], 1.0);
    EXPECT_LT(result.aggregate_metrics["Uniqueness_email.uniqueness"], 1.0);
    EXPECT_DOUBLE_EQ(result.aggregate_metrics["Uniqueness_category.uniqueness"], 0.1);
}

TEST_F(QualityRulesEngineTest, ReferentialIntegrityRules) {
    // Create reference data (valid person IDs)
    std::vector<core::Record> persons;
    for (int i = 1; i <= 50; ++i) {
        core::Record person;
        person.setField("person_id", int64_t(i));
        persons.push_back(person);
    }

    // Create data with references
    std::vector<core::Record> conditions;
    for (int i = 0; i < 100; ++i) {
        core::Record condition;
        condition.setField("condition_id", i);

        // Most references are valid (1-50), but some are invalid (51-55)
        int64_t person_id = (i < 90) ? int64_t(1 + i % 50) : int64_t(51 + i % 5);
        condition.setField("person_id", person_id);

        conditions.push_back(condition);
    }

    // Add referential integrity rule
    engine_->AddRule(std::make_unique<ReferentialIntegrityRule>(
        "person_id", "person_id", persons));

    // Evaluate rules
    auto result = engine_->EvaluateRules(conditions);

    // Verify results
    EXPECT_FALSE(result.all_passed);
    EXPECT_EQ(result.failed_rules, 1);

    // Check integrity metrics
    auto integrity = result.aggregate_metrics["ReferentialIntegrity_person_id.referential_integrity"];
    EXPECT_NEAR(integrity, 0.9, 0.01); // 90% valid references
}

TEST_F(QualityRulesEngineTest, DataTypeConsistencyRules) {
    // Create data with mixed types
    std::vector<core::Record> records;

    for (int i = 0; i < 100; ++i) {
        core::Record record;

        // Mostly integers, but some strings
        if (i < 95) {
            record.setField("numeric_field", i);
        } else {
            record.setField("numeric_field", "not_a_number_" + std::to_string(i));
        }

        // Consistent string field
        record.setField("string_field", "value_" + std::to_string(i));

        records.push_back(record);
    }

    // Add data type consistency rules
    engine_->AddRule(std::make_unique<DataTypeConsistencyRule>("numeric_field", "int"));
    engine_->AddRule(std::make_unique<DataTypeConsistencyRule>("string_field", "string"));

    // Evaluate rules
    auto result = engine_->EvaluateRules(records);

    // Verify results
    EXPECT_FALSE(result.all_passed);
    EXPECT_EQ(result.passed_rules, 1); // Only string_field should pass
    EXPECT_EQ(result.failed_rules, 1); // numeric_field should fail

    // Check consistency metrics
    EXPECT_NEAR(result.aggregate_metrics["DataType_numeric_field.type_consistency"], 0.95, 0.01);
    EXPECT_DOUBLE_EQ(result.aggregate_metrics["DataType_string_field.type_consistency"], 1.0);
}

TEST_F(QualityRulesEngineTest, CustomBusinessRules) {
    // Create custom business rule
    class AgeConsistencyRule : public QualityRule {
    public:
        AgeConsistencyRule()
            : QualityRule("AgeConsistency", Severity::Error) {}

        RuleResult Evaluate(const std::vector<core::Record>& records) override {
            RuleResult result;
            result.rule_name = name_;

            for (const auto& record : records) {
                auto birth_date_opt = record.getFieldOptional("birth_date");
                auto death_date_opt = record.getFieldOptional("death_date");

                if (birth_date_opt.has_value() && birth_date_opt->has_value() &&
                    death_date_opt.has_value() && death_date_opt->has_value()) {

                    try {
                        auto birth = std::any_cast<std::chrono::system_clock::time_point>(*birth_date_opt);
                        auto death = std::any_cast<std::chrono::system_clock::time_point>(*death_date_opt);

                        if (death < birth) {
                            result.passed = false;
                            result.severity = severity_;
                            result.violations.push_back("Death date before birth date");
                        }

                        auto age_duration = death - birth;
                        auto age_days = std::chrono::duration_cast<std::chrono::days>(age_duration).count();
                        auto age_years = age_days / 365.25;

                        if (age_years > 150) {
                            result.passed = false;
                            result.severity = Severity::Warning;
                            result.violations.push_back(
                                "Unrealistic age: " + std::to_string(static_cast<int>(age_years)) + " years");
                        }
                    } catch (...) {
                        // Type conversion error
                    }
                }
            }

            result.description = "Age consistency check between birth and death dates";
            return result;
        }
    };

    // Generate test data
    std::vector<core::Record> records;
    auto now = std::chrono::system_clock::now();

    // Valid record
    core::Record valid_record;
    valid_record.setField("birth_date", now - std::chrono::hours(24 * 365 * 80)); // 80 years ago
    valid_record.setField("death_date", now - std::chrono::hours(24 * 30)); // 30 days ago
    records.push_back(valid_record);

    // Invalid: death before birth
    core::Record invalid_record1;
    invalid_record1.setField("birth_date", now - std::chrono::hours(24 * 365 * 50));
    invalid_record1.setField("death_date", now - std::chrono::hours(24 * 365 * 60)); // 10 years before birth
    records.push_back(invalid_record1);

    // Invalid: unrealistic age
    core::Record invalid_record2;
    invalid_record2.setField("birth_date", now - std::chrono::hours(24 * 365 * 200)); // 200 years ago
    invalid_record2.setField("death_date", now);
    records.push_back(invalid_record2);

    // Add custom rule
    engine_->AddRule(std::make_unique<AgeConsistencyRule>());

    // Evaluate
    auto result = engine_->EvaluateRules(records);

    // Verify
    EXPECT_FALSE(result.all_passed);
    EXPECT_EQ(result.rule_results[0].violations.size(), 2); // Two violations
}

TEST_F(QualityRulesEngineTest, RulePrioritization) {
    // Test rule execution with different severities
    std::vector<core::Record> records = generator_->generate_patient_records(100);

    // Add rules with different severities
    engine_->AddRule(std::make_unique<CompletenessRule>("optional_field", 0.5)); // Warning

    // Make a critical rule that will fail
    class CriticalDataRule : public QualityRule {
    public:
        CriticalDataRule() : QualityRule("CriticalCheck", Severity::Critical) {}

        RuleResult Evaluate(const std::vector<core::Record>& records) override {
            RuleResult result;
            result.rule_name = name_;
            result.passed = false; // Always fail for testing
            result.severity = severity_;
            result.violations.push_back("Critical data quality issue detected");
            return result;
        }
    };

    engine_->AddRule(std::make_unique<CriticalDataRule>());

    // Evaluate
    auto result = engine_->EvaluateRules(records);

    // Check for critical failures
    bool has_critical_failure = false;
    for (const auto& rule_result : result.rule_results) {
        if (!rule_result.passed && rule_result.severity == QualityRule::Severity::Critical) {
            has_critical_failure = true;
            break;
        }
    }

    EXPECT_TRUE(has_critical_failure) << "Should detect critical quality issues";
}

TEST_F(QualityRulesEngineTest, RulePerformance) {
    // Test rule engine performance with large datasets
    const size_t record_count = 100000;
    auto records = generator_->generate_patient_records(record_count);

    // Add multiple rules
    engine_->AddRule(std::make_unique<CompletenessRule>("person_id", 1.0));
    engine_->AddRule(std::make_unique<UniquenessRule>("person_id", true));
    engine_->AddRule(std::make_unique<DataTypeConsistencyRule>("birth_date", "datetime"));

    // Measure evaluation time
    auto start = std::chrono::high_resolution_clock::now();
    auto result = engine_->EvaluateRules(records);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    // Performance assertions
    EXPECT_LT(duration.count(), 5000) << "Rule evaluation should complete within 5 seconds for 100k records";

    // Verify results
    EXPECT_EQ(result.total_rules, 3);
    EXPECT_GT(result.aggregate_metrics["overall_quality_score"], 0.0);

    std::cout << "Evaluated " << record_count << " records with " << result.total_rules
              << " rules in " << duration.count() << " milliseconds" << std::endl;
}
}