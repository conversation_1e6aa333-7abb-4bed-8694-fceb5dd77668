# tests/integration/monitoring/CMakeLists.txt
set(MONITORING_TEST_SOURCES
    test_metrics_collection.cpp
    test_alerting_integration.cpp
    test_logging_integration.cpp
    test_tracing_integration.cpp
)

add_executable(monitoring_integration_tests ${MONITORING_TEST_SOURCES})

target_link_libraries(monitoring_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(monitoring_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME monitoring_integration_tests
    COMMAND monitoring_integration_tests
)

set_tests_properties(monitoring_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;monitoring"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)