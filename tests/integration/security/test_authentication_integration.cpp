// Tests authentication mechanisms across the ETL pipeline
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "security/auth_manager.h"
#include "security/audit_logger.h"
#include "common/utilities.h"
#include "common/configuration.h"
#include <yaml-cpp/yaml.h>
#include <thread>
#include <chrono>

using namespace std::chrono_literals;

namespace omop::security::test {

class AuthenticationIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize authentication configuration
        YAML::Node auth_yaml;
        auth_yaml["authentication"]["enabled"] = true;
        auth_yaml["authentication"]["type"] = "jwt";
        auth_yaml["authentication"]["secret_key"] = "test_secret_key_12345678";
        auth_yaml["authentication"]["token_expiry_minutes"] = 60;
        auth_yaml["authentication"]["refresh_token_expiry_days"] = 7;
        
        auth_yaml["ldap"]["enabled"] = false;
        auth_yaml["ldap"]["server"] = "ldap://localhost:389";
        auth_yaml["ldap"]["base_dn"] = "dc=example,dc=com";
        
        auth_yaml["oauth2"]["enabled"] = true;
        auth_yaml["oauth2"]["provider"] = "keycloak";
        auth_yaml["oauth2"]["client_id"] = "omop_etl";
        auth_yaml["oauth2"]["client_secret"] = "secret123";
        auth_yaml["oauth2"]["auth_url"] = "https://auth.example.com/auth";
        auth_yaml["oauth2"]["token_url"] = "https://auth.example.com/token";

        auth_manager_ = std::make_unique<AuthManager>();
        audit_logger_ = std::make_unique<AuditLogger>();
        
        // Convert YAML to AuthConfig
        AuthConfig config;
        config.enabled = auth_yaml["authentication"]["enabled"].as<bool>();
        config.token_lifetime = std::chrono::seconds(
            auth_yaml["authentication"]["token_expiry_minutes"].as<int>() * 60);
        config.refresh_token_lifetime = std::chrono::seconds(
            auth_yaml["authentication"]["refresh_token_expiry_days"].as<int>() * 86400);
        
        auth_manager_->initialize(config);
        
        // Initialize audit logger
        AuditConfig audit_config = get_default_audit_config();
        audit_config.log_file_path = "test_audit.log";
        audit_logger_->initialize(audit_config);
    }

    void TearDown() override {
        // Cleanup
        std::filesystem::remove("test_audit.log");
    }

    std::unique_ptr<AuthManager> auth_manager_;
    std::unique_ptr<AuditLogger> audit_logger_;
};

// Tests JWT token generation and validation
TEST_F(AuthenticationIntegrationTest, JWTTokenAuthentication) {
    // Create user credentials
    AuthCredentials creds;
    creds.username = "test_user";
    creds.password = "TestPass123!";

    // Create user first
    UserInfo user;
    user.user_id = "test_user";
    user.username = "test_user";
    user.email = "<EMAIL>";
    user.roles = {"user"};
    user.status = UserStatus::Active;
    user.created_at = std::chrono::system_clock::now();
    
    EXPECT_TRUE(auth_manager_->create_user(user, creds.password));

    // Authenticate user
    auto auth_result = auth_manager_->authenticate(creds);
    ASSERT_EQ(auth_result.first, AuthResult::Success);
    ASSERT_TRUE(auth_result.second.has_value());
    
    auto token = auth_result.second.value();
    ASSERT_FALSE(token.token.empty());
    ASSERT_EQ(token.token_type, "Bearer");
    ASSERT_EQ(token.subject, "test_user");
    ASSERT_EQ(token.issuer, "omop-etl");

    // Validate access token
    auto validation = auth_manager_->validate_token(token.token);
    EXPECT_EQ(validation.first, AuthResult::Success);
    EXPECT_TRUE(validation.second.has_value());
    
    auto validated_user = validation.second.value();
    EXPECT_EQ(validated_user.user_id, "test_user");
    EXPECT_EQ(validated_user.username, "test_user");
}

// Tests token refresh functionality
TEST_F(AuthenticationIntegrationTest, TokenRefresh) {
    // Create user first
    UserInfo user;
    user.user_id = "test_user";
    user.username = "test_user";
    user.email = "<EMAIL>";
    user.roles = {"user"};
    user.status = UserStatus::Active;
    user.created_at = std::chrono::system_clock::now();
    
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));

    // Initial authentication
    AuthCredentials creds;
    creds.username = "test_user";
    creds.password = "TestPass123!";

    auto initial_auth = auth_manager_->authenticate(creds);
    ASSERT_EQ(initial_auth.first, AuthResult::Success);
    ASSERT_TRUE(initial_auth.second.has_value());

    auto original_token = initial_auth.second.value();

    // Wait a bit to ensure new token will have different timestamp
    std::this_thread::sleep_for(2s);

    // Refresh token
    auto refresh_result = auth_manager_->refresh_token(original_token.token);
    ASSERT_EQ(refresh_result.first, AuthResult::Success);
    ASSERT_TRUE(refresh_result.second.has_value());

    auto new_token = refresh_result.second.value();
    ASSERT_NE(new_token.token, original_token.token);
    ASSERT_EQ(new_token.subject, original_token.subject);
    ASSERT_EQ(new_token.issuer, original_token.issuer);

    // Verify new token is valid
    auto validation = auth_manager_->validate_token(new_token.token);
    EXPECT_EQ(validation.first, AuthResult::Success);
    EXPECT_TRUE(validation.second.has_value());

    // Verify old token is revoked
    auto old_validation = auth_manager_->validate_token(original_token.token);
    EXPECT_EQ(old_validation.first, AuthResult::TokenRevoked);
    EXPECT_FALSE(old_validation.second.has_value());
}

// Tests user management operations
TEST_F(AuthenticationIntegrationTest, UserManagement) {
    // Create user
    UserInfo user;
    user.user_id = "test_user";
    user.username = "test_user";
    user.email = "<EMAIL>";
    user.roles = {"user"};
    user.status = UserStatus::Active;
    user.created_at = std::chrono::system_clock::now();
    
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));

    // Get user info
    auto user_info = auth_manager_->get_user_info("test_user");
    EXPECT_TRUE(user_info.has_value());
    EXPECT_EQ(user_info->user_id, "test_user");
    EXPECT_EQ(user_info->username, "test_user");

    // Update user
    user.email = "<EMAIL>";
    user.roles = {"admin", "user"};
    EXPECT_TRUE(auth_manager_->update_user(user));

    // Verify update
    auto updated_info = auth_manager_->get_user_info("test_user");
    EXPECT_TRUE(updated_info.has_value());
    EXPECT_EQ(updated_info->email, "<EMAIL>");
    EXPECT_EQ(updated_info->roles.size(), 2);

    // Lock user
    EXPECT_TRUE(auth_manager_->lock_user("test_user"));

    // Try to authenticate with locked user
    AuthCredentials creds;
    creds.username = "test_user";
    creds.password = "TestPass123!";
    
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::AccountLocked);

    // Unlock user
    EXPECT_TRUE(auth_manager_->unlock_user("test_user"));

    // Should be able to authenticate again
    auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
}

// Tests session management
TEST_F(AuthenticationIntegrationTest, SessionManagement) {
    // Create user
    UserInfo user;
    user.user_id = "test_user";
    user.username = "test_user";
    user.email = "<EMAIL>";
    user.roles = {"user"};
    user.status = UserStatus::Active;
    user.created_at = std::chrono::system_clock::now();
    
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));

    // Create multiple sessions
    AuthCredentials creds;
    creds.username = "test_user";
    creds.password = "TestPass123!";
    
    auto auth_result1 = auth_manager_->authenticate(creds);
    auto auth_result2 = auth_manager_->authenticate(creds);
    auto auth_result3 = auth_manager_->authenticate(creds);

    EXPECT_EQ(auth_result1.first, AuthResult::Success);
    EXPECT_EQ(auth_result2.first, AuthResult::Success);
    EXPECT_EQ(auth_result3.first, AuthResult::Success);

    // Get active sessions
    auto sessions = auth_manager_->get_active_sessions("test_user");
    EXPECT_EQ(sessions.size(), 3);

    // Terminate specific session
    EXPECT_TRUE(auth_manager_->terminate_session("test_user", auth_result1.second->token));

    // Verify session was terminated
    auto validation_result = auth_manager_->validate_token(auth_result1.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);

    // Other sessions should still be valid
    validation_result = auth_manager_->validate_token(auth_result2.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::Success);

    // Terminate all sessions
    EXPECT_TRUE(auth_manager_->terminate_all_sessions("test_user"));

    // All sessions should be terminated
    validation_result = auth_manager_->validate_token(auth_result2.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);

    validation_result = auth_manager_->validate_token(auth_result3.second->token);
    EXPECT_EQ(validation_result.first, AuthResult::TokenRevoked);
}

// Tests audit logging integration
TEST_F(AuthenticationIntegrationTest, AuditLoggingIntegration) {
    // Create user
    UserInfo user;
    user.user_id = "test_user";
    user.username = "test_user";
    user.email = "<EMAIL>";
    user.roles = {"user"};
    user.status = UserStatus::Active;
    user.created_at = std::chrono::system_clock::now();
    
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));

    // Authenticate user (should trigger audit log)
    AuthCredentials creds;
    creds.username = "test_user";
    creds.password = "TestPass123!";
    
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);

    // Wait for audit log to be written
    std::this_thread::sleep_for(2s);

    // Check audit log file exists and contains authentication event
    EXPECT_TRUE(std::filesystem::exists("test_audit.log"));
    
    std::ifstream log_file("test_audit.log");
    std::string line;
    bool found_auth_event = false;
    
    while (std::getline(log_file, line)) {
        if (line.find("Authentication") != std::string::npos && 
            line.find("test_user") != std::string::npos) {
            found_auth_event = true;
            break;
        }
    }
    
    EXPECT_TRUE(found_auth_event);
}

// Tests password management
TEST_F(AuthenticationIntegrationTest, PasswordManagement) {
    // Create user
    UserInfo user;
    user.user_id = "test_user";
    user.username = "test_user";
    user.email = "<EMAIL>";
    user.roles = {"user"};
    user.status = UserStatus::Active;
    user.created_at = std::chrono::system_clock::now();
    
    EXPECT_TRUE(auth_manager_->create_user(user, "TestPass123!"));

    // Change password
    EXPECT_TRUE(auth_manager_->change_password("test_user", "TestPass123!", "NewPass456!"));

    // Should be able to authenticate with new password
    AuthCredentials new_creds;
    new_creds.username = "test_user";
    new_creds.password = "NewPass456!";
    
    auto auth_result = auth_manager_->authenticate(new_creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);

    // Should not be able to authenticate with old password
    AuthCredentials old_creds;
    old_creds.username = "test_user";
    old_creds.password = "TestPass123!";
    
    auth_result = auth_manager_->authenticate(old_creds);
    EXPECT_EQ(auth_result.first, AuthResult::InvalidCredentials);

    // Reset password
    EXPECT_TRUE(auth_manager_->reset_password("test_user", "ResetPass789!"));

    // Should be able to authenticate with reset password
    AuthCredentials reset_creds;
    reset_creds.username = "test_user";
    reset_creds.password = "ResetPass789!";
    
    auth_result = auth_manager_->authenticate(reset_creds);
    EXPECT_EQ(auth_result.first, AuthResult::Success);
}

// Tests error handling
TEST_F(AuthenticationIntegrationTest, ErrorHandling) {
    // Try to authenticate with non-existent user
    AuthCredentials creds;
    creds.username = "nonexistent";
    creds.password = "password";
    
    auto auth_result = auth_manager_->authenticate(creds);
    EXPECT_EQ(auth_result.first, AuthResult::InvalidCredentials);

    // Try to validate invalid token
    auto validation_result = auth_manager_->validate_token("invalid_token");
    EXPECT_EQ(validation_result.first, AuthResult::InvalidToken);

    // Try to refresh invalid token
    auto refresh_result = auth_manager_->refresh_token("invalid_token");
    EXPECT_EQ(refresh_result.first, AuthResult::InvalidToken);

    // Try to get non-existent user info
    auto user_info = auth_manager_->get_user_info("nonexistent");
    EXPECT_FALSE(user_info.has_value());
}

} // namespace omop::security::test