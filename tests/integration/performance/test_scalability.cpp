// tests/performance/test_scalability.cpp
// Horizontal and vertical scaling tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <vector>
#include <thread>
#include <numeric>
#include "core/pipeline.h"
#include "load/database_loader.h"
#include "test_helpers/test_data_generator.h"

namespace omop::performance {

class ScalabilityTest : public ::testing::Test {
protected:
    struct ScalabilityMetrics {
        size_t data_size;
        size_t num_workers;
        double throughput;
        double latency_p95;
        double cpu_efficiency;
        double memory_per_record;
        double speedup;
    };

    ScalabilityMetrics MeasureScalability(
        size_t data_size,
        size_t num_workers,
        size_t batch_size = 5000) {

        ScalabilityMetrics metrics;
        metrics.data_size = data_size;
        metrics.num_workers = num_workers;

        // Generate test data
        test::TestDataGenerator generator;
        auto records = generator.generateConditionRecords(data_size);

        // Configure pipeline
        core::PipelineConfig config;
        config.batch_size = batch_size;
        config.max_parallel_batches = num_workers;

        // Measure performance
        auto start_time = std::chrono::high_resolution_clock::now();
        size_t initial_memory = getCurrentMemoryUsageMB();

        // Process data
        size_t processed = ProcessRecords(records, config);

        auto end_time = std::chrono::high_resolution_clock::now();
        size_t final_memory = getCurrentMemoryUsageMB();

        // Calculate metrics
        auto duration = std::chrono::duration<double>(end_time - start_time).count();
        metrics.throughput = processed / duration;
        metrics.memory_per_record = static_cast<double>(final_memory - initial_memory) * 1024 * 1024 / processed;

        return metrics;
    }

    size_t ProcessRecords(const std::vector<core::Record>& records,
                         const core::PipelineConfig& config) {
        // Simulate processing
        std::atomic<size_t> processed{0};
        const size_t chunk_size = config.batch_size;

        std::vector<std::thread> workers;
        for (size_t w = 0; w < config.max_parallel_batches; ++w) {
            workers.emplace_back([&, w]() {
                size_t start = w * (records.size() / config.max_parallel_batches);
                size_t end = (w + 1) * (records.size() / config.max_parallel_batches);

                for (size_t i = start; i < end; i += chunk_size) {
                    size_t batch_end = std::min(i + chunk_size, end);

                    // Simulate processing batch
                    std::this_thread::sleep_for(std::chrono::microseconds(100));
                    processed += (batch_end - i);
                }
            });
        }

        for (auto& worker : workers) {
            worker.join();
        }

        return processed;
    }

    size_t getCurrentMemoryUsageMB() {
        // Simplified memory measurement
        return 100; // Placeholder
    }

    void PlotScalabilityResults(const std::vector<ScalabilityMetrics>& results) {
        std::cout << "\nScalability Results:\n";
        std::cout << "Workers\tData Size\tThroughput\tSpeedup\tEfficiency\n";

        double baseline_throughput = results[0].throughput;

        for (const auto& metric : results) {
            double speedup = metric.throughput / baseline_throughput;
            double efficiency = speedup / metric.num_workers;

            std::cout << metric.num_workers << "\t"
                      << metric.data_size << "\t"
                      << std::fixed << std::setprecision(2)
                      << metric.throughput << "\t"
                      << speedup << "x\t"
                      << efficiency * 100 << "%\n";
        }
    }
};

TEST_F(ScalabilityTest, HorizontalScaling) {
    // Test scaling with increasing number of workers
    const size_t data_size = 1000000;
    const std::vector<size_t> worker_counts = {1, 2, 4, 8, 16};

    std::vector<ScalabilityMetrics> results;

    for (size_t workers : worker_counts) {
        auto metrics = MeasureScalability(data_size, workers);
        results.push_back(metrics);
    }

    PlotScalabilityResults(results);

    // Verify scaling efficiency
    double baseline = results[0].throughput;

    for (size_t i = 1; i < results.size(); ++i) {
        double actual_speedup = results[i].throughput / baseline;
        double ideal_speedup = static_cast<double>(worker_counts[i]);
        double efficiency = actual_speedup / ideal_speedup;

        // Expect at least 50% efficiency up to 8 workers
        if (worker_counts[i] <= 8) {
            EXPECT_GT(efficiency, 0.5)
                << "Poor scaling efficiency with " << worker_counts[i] << " workers";
        }
    }
}

TEST_F(ScalabilityTest, VerticalScaling) {
    // Test scaling with increasing data sizes
    const std::vector<size_t> data_sizes = {10000, 100000, 500000, 1000000};
    const size_t num_workers = 4;

    std::vector<ScalabilityMetrics> results;

    for (size_t data_size : data_sizes) {
        auto metrics = MeasureScalability(data_size, num_workers);
        results.push_back(metrics);
    }

    // Verify throughput remains stable
    double avg_throughput = 0;
    for (const auto& metric : results) {
        avg_throughput += metric.throughput;
    }
    avg_throughput /= results.size();

    for (const auto& metric : results) {
        double deviation = std::abs(metric.throughput - avg_throughput) / avg_throughput;

        // Throughput should not deviate more than 20% with data size
        EXPECT_LT(deviation, 0.2)
            << "Throughput instability detected for data size " << metric.data_size;
    }
}

TEST_F(ScalabilityTest, WeakScalingTest) {
    // Weak scaling: increase workers and data proportionally
    const size_t base_data_per_worker = 100000;
    const std::vector<size_t> worker_counts = {1, 2, 4, 8};

    std::vector<double> efficiencies;

    for (size_t workers : worker_counts) {
        size_t data_size = base_data_per_worker * workers;
        auto metrics = MeasureScalability(data_size, workers);

        // Calculate time per unit of work
        double time_per_unit = (1.0 / metrics.throughput) * workers;
        efficiencies.push_back(time_per_unit);
    }

    // In ideal weak scaling, time per unit should remain constant
    double baseline_time = efficiencies[0];

    for (size_t i = 1; i < efficiencies.size(); ++i) {
        double scaling_factor = efficiencies[i] / baseline_time;

        // Allow 30% degradation in weak scaling
        EXPECT_LT(scaling_factor, 1.3)
            << "Weak scaling degradation with " << worker_counts[i] << " workers";
    }
}

TEST_F(ScalabilityTest, StrongScalingTest) {
    // Strong scaling: fixed data size, increasing workers
    const size_t fixed_data_size = 1000000;
    const std::vector<size_t> worker_counts = {1, 2, 4, 8, 16};

    std::vector<ScalabilityMetrics> results;

    for (size_t workers : worker_counts) {
        auto metrics = MeasureScalability(fixed_data_size, workers);
        metrics.speedup = metrics.throughput / results[0].throughput;
        results.push_back(metrics);
    }

    // Analyze Amdahl's Law behavior
    for (size_t i = 1; i < results.size(); ++i) {
        double actual_speedup = results[i].speedup;
        double ideal_speedup = static_cast<double>(worker_counts[i]);

        // Estimate parallel fraction using Amdahl's Law
        // S = 1 / ((1-P) + P/N) where S=speedup, P=parallel fraction, N=workers
        double parallel_fraction = (actual_speedup - 1) / (ideal_speedup - 1);

        std::cout << "Workers: " << worker_counts[i]
                  << ", Speedup: " << actual_speedup
                  << ", Parallel fraction: " << parallel_fraction << "\n";

        // Expect at least 70% of work to be parallelizable
        EXPECT_GT(parallel_fraction, 0.7)
            << "Low parallel fraction indicates poor parallelization";
    }
}

TEST_F(ScalabilityTest, BatchSizeOptimization) {
    // Find optimal batch size for different data sizes
    const size_t num_workers = 4;
    const std::vector<size_t> batch_sizes = {100, 500, 1000, 5000, 10000, 50000};
    const std::vector<size_t> data_sizes = {10000, 100000, 1000000};

    for (size_t data_size : data_sizes) {
        std::cout << "\nOptimal batch size for " << data_size << " records:\n";

        double best_throughput = 0;
        size_t optimal_batch_size = 0;

        for (size_t batch_size : batch_sizes) {
            if (batch_size > data_size / num_workers) continue;

            auto metrics = MeasureScalability(data_size, num_workers, batch_size);

            std::cout << "Batch size: " << batch_size
                      << ", Throughput: " << metrics.throughput << "\n";

            if (metrics.throughput > best_throughput) {
                best_throughput = metrics.throughput;
                optimal_batch_size = batch_size;
            }
        }

        std::cout << "Optimal batch size: " << optimal_batch_size
                  << " with throughput: " << best_throughput << "\n";

        // Verify optimal batch size is reasonable
        EXPECT_GE(optimal_batch_size, 1000)
            << "Optimal batch size too small";
        EXPECT_LE(optimal_batch_size, 50000)
            << "Optimal batch size too large";
    }
}

TEST_F(ScalabilityTest, NetworkBottleneckSimulation) {
    // Simulate network bottlenecks in distributed processing
    const size_t data_size = 100000;
    const std::vector<size_t> network_delays_us = {0, 100, 500, 1000, 5000};
    const size_t num_workers = 4;

    std::vector<double> throughputs;

    for (size_t delay : network_delays_us) {
        // Inject network delay simulation
        auto start = std::chrono::high_resolution_clock::now();

        // Process with simulated network delay
        size_t processed = 0;
        std::vector<std::thread> workers;

        for (size_t w = 0; w < num_workers; ++w) {
            workers.emplace_back([&]() {
                for (size_t i = 0; i < data_size / num_workers; ++i) {
                    // Simulate processing
                    std::this_thread::sleep_for(std::chrono::microseconds(10));

                    // Simulate network communication
                    if (delay > 0) {
                        std::this_thread::sleep_for(std::chrono::microseconds(delay));
                    }

                    processed++;
                }
            });
        }

        for (auto& worker : workers) {
            worker.join();
        }

        auto end = std::chrono::high_resolution_clock::now();
        double duration = std::chrono::duration<double>(end - start).count();
        double throughput = processed / duration;
        throughputs.push_back(throughput);

        std::cout << "Network delay: " << delay << " μs, "
                  << "Throughput: " << throughput << " records/s\n";
    }

    // Verify throughput degradation with network delay
    for (size_t i = 1; i < throughputs.size(); ++i) {
        double degradation = (throughputs[0] - throughputs[i]) / throughputs[0];

        // Higher delays should cause more degradation
        if (network_delays_us[i] >= 1000) {
            EXPECT_GT(degradation, 0.2)
                << "Expected significant degradation with high network delay";
        }
    }
}

} // namespace omop::performance