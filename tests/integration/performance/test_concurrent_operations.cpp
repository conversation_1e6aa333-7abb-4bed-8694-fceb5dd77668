// tests/performance/test_concurrent_operations.cpp
// Concurrency stress tests for multi-threaded operations

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <atomic>
#include <thread>
#include <random>
#include <barrier>
#include "core/job_manager.h"
#include "core/pipeline.h"
#include "test_helpers/test_data_generator.h"

namespace omop::performance {

class ConcurrentOperationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        logger_ = std::make_shared<common::Logger>("concurrent_test");
        job_manager_ = std::make_unique<core::JobManager>(config_, logger_);
        job_manager_->start();
    }

    void TearDown() override {
        if (job_manager_) {
            job_manager_->stop();
        }
    }

    void RunConcurrentJobs(size_t num_jobs, size_t records_per_job) {
        std::vector<std::string> job_ids;
        std::atomic<size_t> completed_jobs{0};
        std::atomic<size_t> failed_jobs{0};

        // Submit jobs concurrently
        std::vector<std::thread> submitters;

        for (size_t i = 0; i < num_jobs; ++i) {
            submitters.emplace_back([&, i]() {
                core::JobConfig config;
                config.job_name = "concurrent_job_" + std::to_string(i);
                config.pipeline_config_path = "test_config.yaml";
                config.priority = static_cast<core::JobPriority>(i % 4);

                try {
                    auto job_id = job_manager_->submitJob(config);
                    job_ids.push_back(job_id);
                } catch (const std::exception& e) {
                    failed_jobs++;
                }
            });
        }

        for (auto& thread : submitters) {
            thread.join();
        }

        // Wait for all jobs to complete
        auto start_time = std::chrono::steady_clock::now();
        const auto timeout = std::chrono::minutes(5);

        while (completed_jobs < num_jobs &&
               std::chrono::steady_clock::now() - start_time < timeout) {

            for (const auto& job_id : job_ids) {
                auto job = job_manager_->getJob(job_id);
                if (job && (job->getStatus() == core::JobStatus::Completed ||
                           job->getStatus() == core::JobStatus::Failed)) {
                    completed_jobs++;
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // Verify all jobs completed
        EXPECT_EQ(completed_jobs, num_jobs)
            << "Not all jobs completed within timeout";
        EXPECT_EQ(failed_jobs, 0)
            << "Some jobs failed during concurrent execution";
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<common::Logger> logger_;
    std::unique_ptr<core::JobManager> job_manager_;
};

TEST_F(ConcurrentOperationsTest, ConcurrentJobSubmission) {
    // Test concurrent job submission
    const size_t num_jobs = 100;
    const size_t num_threads = 10;

    std::atomic<size_t> submitted_jobs{0};
    std::vector<std::thread> threads;
    std::vector<std::string> all_job_ids;
    std::mutex job_ids_mutex;

    // Create barrier for synchronized start
    std::barrier sync_point(num_threads);

    for (size_t t = 0; t < num_threads; ++t) {
        threads.emplace_back([&, t]() {
            sync_point.arrive_and_wait(); // Synchronize thread start

            for (size_t i = 0; i < num_jobs / num_threads; ++i) {
                core::JobConfig config;
                config.job_name = "thread_" + std::to_string(t) + "_job_" + std::to_string(i);
                config.pipeline_config_path = "test_config.yaml";

                auto job_id = job_manager_->submitJob(config);

                {
                    std::lock_guard<std::mutex> lock(job_ids_mutex);
                    all_job_ids.push_back(job_id);
                }

                submitted_jobs++;
            }
        });
    }

    for (auto& thread : threads) {
        thread.join();
    }

    // Verify all jobs were submitted
    EXPECT_EQ(submitted_jobs, num_jobs);
    EXPECT_EQ(all_job_ids.size(), num_jobs);

    // Verify no duplicate job IDs
    std::set<std::string> unique_ids(all_job_ids.begin(), all_job_ids.end());
    EXPECT_EQ(unique_ids.size(), all_job_ids.size())
        << "Duplicate job IDs detected";
}

TEST_F(ConcurrentOperationsTest, ConcurrentPipelineExecution) {
    // Test multiple pipelines running concurrently
    const size_t num_pipelines = 8;
    const size_t records_per_pipeline = 10000;

    std::vector<std::thread> pipeline_threads;
    std::atomic<size_t> successful_pipelines{0};
    std::atomic<size_t> total_records_processed{0};

    for (size_t i = 0; i < num_pipelines; ++i) {
        pipeline_threads.emplace_back([&, i]() {
            try {
                // Create independent pipeline
                core::PipelineConfig config;
                config.batch_size = 1000;
                auto pipeline = std::make_unique<core::ETLPipeline>(config);

                // Generate test data
                test::TestDataGenerator generator;
                auto records = generator.generatePatientRecords(records_per_pipeline);

                // Process records
                size_t processed = 0;
                for (const auto& record : records) {
                    core::RecordBatch batch;
                    batch.addRecord(record);
                    // Simulate processing
                    processed++;
                }

                total_records_processed += processed;
                successful_pipelines++;

            } catch (const std::exception& e) {
                // Log error but don't fail test
                std::cerr << "Pipeline " << i << " failed: " << e.what() << std::endl;
            }
        });
    }

    for (auto& thread : pipeline_threads) {
        thread.join();
    }

    // Verify concurrent execution success
    EXPECT_EQ(successful_pipelines, num_pipelines)
        << "Some pipelines failed during concurrent execution";
    EXPECT_EQ(total_records_processed, num_pipelines * records_per_pipeline)
        << "Not all records were processed";
}

TEST_F(ConcurrentOperationsTest, ResourceContentionTest) {
    // Test behavior under resource contention
    const size_t num_workers = 16;
    const size_t operations_per_worker = 1000;

    // Shared resources
    std::mutex shared_mutex;
    std::atomic<size_t> shared_counter{0};
    std::vector<size_t> access_times;
    std::mutex times_mutex;

    std::vector<std::thread> workers;

    auto start_time = std::chrono::high_resolution_clock::now();

    for (size_t w = 0; w < num_workers; ++w) {
        workers.emplace_back([&]() {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> work_dist(1, 10);

            for (size_t i = 0; i < operations_per_worker; ++i) {
                // Simulate work
                std::this_thread::sleep_for(std::chrono::microseconds(work_dist(gen)));

                // Access shared resource
                auto access_start = std::chrono::high_resolution_clock::now();
                {
                    std::lock_guard<std::mutex> lock(shared_mutex);
                    shared_counter++;

                    // Simulate critical section work
                    std::this_thread::sleep_for(std::chrono::microseconds(5));
                }
                auto access_end = std::chrono::high_resolution_clock::now();

                // Record access time
                auto access_duration = std::chrono::duration_cast<std::chrono::microseconds>(
                    access_end - access_start).count();

                {
                    std::lock_guard<std::mutex> lock(times_mutex);
                    access_times.push_back(access_duration);
                }
            }
        });
    }

    for (auto& worker : workers) {
        worker.join();
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count();

    // Analyze contention metrics
    EXPECT_EQ(shared_counter, num_workers * operations_per_worker)
        << "Lost updates detected";

    // Calculate access time statistics
    std::sort(access_times.begin(), access_times.end());
    auto p50 = access_times[access_times.size() * 0.50];
    auto p95 = access_times[access_times.size() * 0.95];
    auto p99 = access_times[access_times.size() * 0.99];

    std::cout << "Resource contention metrics:\n"
              << "Total duration: " << total_duration << " ms\n"
              << "Access time P50: " << p50 << " μs\n"
              << "Access time P95: " << p95 << " μs\n"
              << "Access time P99: " << p99 << " μs\n";

    // Verify reasonable contention levels
    EXPECT_LT(p95, 1000) << "Excessive contention detected";
}

TEST_F(ConcurrentOperationsTest, DeadlockDetection) {
    // Test for potential deadlocks in concurrent operations
    const size_t num_threads = 4;
    const size_t iterations = 1000;

    std::mutex mutex_a, mutex_b, mutex_c;
    std::atomic<size_t> completed_iterations{0};
    std::atomic<bool> deadlock_detected{false};

    std::vector<std::thread> threads;

    // Thread 1: A -> B -> C
    threads.emplace_back([&]() {
        for (size_t i = 0; i < iterations && !deadlock_detected; ++i) {
            std::unique_lock<std::mutex> lock_a(mutex_a, std::defer_lock);
            std::unique_lock<std::mutex> lock_b(mutex_b, std::defer_lock);
            std::unique_lock<std::mutex> lock_c(mutex_c, std::defer_lock);

            if (!std::try_lock(lock_a, lock_b, lock_c)) {
                continue; // Retry
            }

            // Critical section
            std::this_thread::sleep_for(std::chrono::microseconds(1));
            completed_iterations++;
        }
    });

    // Thread 2: B -> C -> A
    threads.emplace_back([&]() {
        for (size_t i = 0; i < iterations && !deadlock_detected; ++i) {
            std::unique_lock<std::mutex> lock_b(mutex_b, std::defer_lock);
            std::unique_lock<std::mutex> lock_c(mutex_c, std::defer_lock);
            std::unique_lock<std::mutex> lock_a(mutex_a, std::defer_lock);

            if (!std::try_lock(lock_b, lock_c, lock_a)) {
                continue; // Retry
            }

            // Critical section
            std::this_thread::sleep_for(std::chrono::microseconds(1));
            completed_iterations++;
        }
    });

    // Thread 3: C -> A -> B
    threads.emplace_back([&]() {
        for (size_t i = 0; i < iterations && !deadlock_detected; ++i) {
            std::unique_lock<std::mutex> lock_c(mutex_c, std::defer_lock);
            std::unique_lock<std::mutex> lock_a(mutex_a, std::defer_lock);
            std::unique_lock<std::mutex> lock_b(mutex_b, std::defer_lock);

            if (!std::try_lock(lock_c, lock_a, lock_b)) {
                continue; // Retry
            }

            // Critical section
            std::this_thread::sleep_for(std::chrono::microseconds(1));
            completed_iterations++;
        }
    });

    // Watchdog thread
    threads.emplace_back([&]() {
        auto start = std::chrono::steady_clock::now();
        const auto timeout = std::chrono::seconds(10);

        while (!deadlock_detected &&
               completed_iterations < num_threads * iterations) {

            if (std::chrono::steady_clock::now() - start > timeout) {
                deadlock_detected = true;
                break;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });

    for (auto& thread : threads) {
        thread.join();
    }

    EXPECT_FALSE(deadlock_detected)
        << "Potential deadlock detected in concurrent operations";
    EXPECT_EQ(completed_iterations, (num_threads - 1) * iterations)
        << "Not all iterations completed";
}

} // namespace omop::performance