// tests/performance/test_memory_usage.cpp
// Memory consumption patterns and leak detection tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <vector>
#include <thread>
#include "core/pipeline.h"
#include "test_helpers/test_data_generator.h"

namespace omop::performance {

class MemoryUsageTest : public ::testing::Test {
protected:
    void SetUp() override {
        initial_memory_ = getCurrentMemoryUsage();
    }

    void TearDown() override {
        // Force garbage collection if possible
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    struct MemorySnapshot {
        size_t heap_size;
        size_t heap_used;
        size_t rss; // Resident Set Size
        std::chrono::steady_clock::time_point timestamp;
    };

    MemorySnapshot getCurrentMemoryUsage() {
        MemorySnapshot snapshot;
        snapshot.timestamp = std::chrono::steady_clock::now();

        #ifdef __linux__
            // Linux-specific memory tracking
            std::ifstream status("/proc/self/status");
            std::string line;
            while (std::getline(status, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label;
                    size_t memory_kb;
                    iss >> label >> memory_kb;
                    snapshot.rss = memory_kb * 1024;
                }
            }
        #endif

        return snapshot;
    }

    void TrackMemoryDuringOperation(
        std::function<void()> operation,
        std::vector<MemorySnapshot>& snapshots,
        std::chrono::milliseconds interval = std::chrono::milliseconds(10)) {

        std::atomic<bool> tracking{true};

        std::thread tracker([&]() {
            while (tracking) {
                snapshots.push_back(getCurrentMemoryUsage());
                std::this_thread::sleep_for(interval);
            }
        });

        operation();

        tracking = false;
        tracker.join();
    }

protected:
    MemorySnapshot initial_memory_;
    std::vector<MemorySnapshot> memory_snapshots_;
};

TEST_F(MemoryUsageTest, RecordBatchMemoryScaling) {
    // Test memory usage scales linearly with batch size
    const std::vector<size_t> batch_sizes = {1000, 5000, 10000, 50000};
    std::vector<size_t> memory_usages;

    for (size_t batch_size : batch_sizes) {
        auto before = getCurrentMemoryUsage();

        // Create batch with test data
        core::RecordBatch batch;
        batch.reserve(batch_size);

        for (size_t i = 0; i < batch_size; ++i) {
            core::Record record;
            record.setField("patient_id", static_cast<int64_t>(i));
            record.setField("name", "Test Patient " + std::to_string(i));
            record.setField("birth_date", std::chrono::system_clock::now());
            record.setField("data", std::string(1000, 'x')); // 1KB per record
            batch.addRecord(std::move(record));
        }

        auto after = getCurrentMemoryUsage();
        size_t memory_used = after.rss - before.rss;
        memory_usages.push_back(memory_used);

        // Calculate bytes per record
        double bytes_per_record = static_cast<double>(memory_used) / batch_size;
        std::cout << "Batch size: " << batch_size
                  << ", Memory used: " << memory_used / (1024 * 1024) << " MB"
                  << ", Bytes per record: " << bytes_per_record << "\n";
    }

    // Verify linear scaling
    for (size_t i = 1; i < memory_usages.size(); ++i) {
        double ratio = static_cast<double>(memory_usages[i]) / memory_usages[0];
        double expected_ratio = static_cast<double>(batch_sizes[i]) / batch_sizes[0];

        // Allow 20% deviation from linear scaling
        EXPECT_NEAR(ratio, expected_ratio, expected_ratio * 0.2)
            << "Memory usage does not scale linearly with batch size";
    }
}

TEST_F(MemoryUsageTest, PipelineMemoryLeakDetection) {
    // Run pipeline multiple times and check for memory leaks
    const size_t iterations = 10;
    const size_t records_per_iteration = 10000;

    std::vector<size_t> iteration_memory;

    for (size_t i = 0; i < iterations; ++i) {
        auto before = getCurrentMemoryUsage();

        // Run a complete pipeline cycle
        core::PipelineConfig config;
        config.batch_size = 1000;
        auto pipeline = std::make_unique<core::ETLPipeline>(config);

        // Process records
        core::RecordBatch batch;
        for (size_t j = 0; j < records_per_iteration; ++j) {
            core::Record record;
            record.setField("id", static_cast<int64_t>(j));
            record.setField("value", std::string(100, 'x'));
            batch.addRecord(std::move(record));
        }

        // Force pipeline destruction
        pipeline.reset();

        // Force cleanup
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        auto after = getCurrentMemoryUsage();
        iteration_memory.push_back(after.rss);
    }

    // Check for memory growth trend
    size_t first_iteration = iteration_memory[0];
    size_t last_iteration = iteration_memory.back();

    // Allow maximum 10% growth over all iterations
    double growth_percentage = (static_cast<double>(last_iteration - first_iteration) / first_iteration) * 100;

    EXPECT_LT(growth_percentage, 10.0)
        << "Potential memory leak detected: " << growth_percentage << "% growth over " << iterations << " iterations";
}

TEST_F(MemoryUsageTest, TransformationCacheMemoryBounds) {
    // Test that transformation caches respect memory limits
    const size_t max_cache_entries = 10000;
    const size_t test_entries = 50000;

    transform::TransformationCache cache(max_cache_entries);

    std::vector<MemorySnapshot> snapshots;

    TrackMemoryDuringOperation([&]() {
        // Add more entries than cache capacity
        for (size_t i = 0; i < test_entries; ++i) {
            std::string key = "key_" + std::to_string(i);
            std::string value = std::string(1000, 'x'); // 1KB per entry
            cache.put(key, value);
        }
    }, snapshots);

    // Find peak memory usage
    size_t peak_memory = 0;
    for (const auto& snapshot : snapshots) {
        peak_memory = std::max(peak_memory, snapshot.rss);
    }

    // Verify cache respected memory bounds
    size_t expected_max_memory = max_cache_entries * 1100; // 1KB data + overhead
    size_t actual_growth = peak_memory - initial_memory_.rss;

    EXPECT_LT(actual_growth, expected_max_memory * 2)
        << "Cache memory usage exceeded expected bounds";

    // Verify cache stats
    auto stats = cache.get_stats();
    EXPECT_LE(stats.size, max_cache_entries)
        << "Cache size exceeded maximum";
}

TEST_F(MemoryUsageTest, ConcurrentMemoryUsage) {
    // Test memory usage under concurrent load
    const size_t num_threads = 8;
    const size_t records_per_thread = 10000;

    std::vector<MemorySnapshot> snapshots;
    std::atomic<size_t> total_records{0};

    TrackMemoryDuringOperation([&]() {
        std::vector<std::thread> threads;

        for (size_t t = 0; t < num_threads; ++t) {
            threads.emplace_back([&, t]() {
                // Each thread processes its own batch
                core::RecordBatch batch;

                for (size_t i = 0; i < records_per_thread; ++i) {
                    core::Record record;
                    record.setField("thread_id", static_cast<int>(t));
                    record.setField("record_id", static_cast<int64_t>(i));
                    record.setField("data", std::string(500, 'x'));
                    batch.addRecord(std::move(record));

                    total_records++;
                }

                // Simulate processing
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            });
        }

        for (auto& thread : threads) {
            thread.join();
        }
    }, snapshots);

    // Analyze memory usage pattern
    size_t peak_memory = 0;
    for (const auto& snapshot : snapshots) {
        peak_memory = std::max(peak_memory, snapshot.rss);
    }

    size_t memory_growth = peak_memory - initial_memory_.rss;
    double mb_per_thread = static_cast<double>(memory_growth) / (1024 * 1024 * num_threads);

    std::cout << "Concurrent memory usage:\n"
              << "Peak memory: " << peak_memory / (1024 * 1024) << " MB\n"
              << "Memory per thread: " << mb_per_thread << " MB\n";

    // Verify reasonable memory usage per thread
    EXPECT_LT(mb_per_thread, 100)
        << "Excessive memory usage per thread";
}

TEST_F(MemoryUsageTest, LargeRecordHandling) {
    // Test memory handling for records with large fields
    const std::vector<size_t> field_sizes = {1024, 10240, 102400, 1048576}; // 1KB to 1MB

    for (size_t field_size : field_sizes) {
        auto before = getCurrentMemoryUsage();

        // Create record with large field
        core::Record record;
        record.setField("id", int64_t(1));
        record.setField("large_field", std::string(field_size, 'x'));

        // Transform the record
        transform::TransformationEngine engine;
        core::ProcessingContext context;

        auto result = engine.transform(record, context);

        auto after = getCurrentMemoryUsage();
        size_t memory_used = after.rss - before.rss;

        // Verify memory usage is proportional to field size
        // Allow 2x overhead for processing
        EXPECT_LT(memory_used, field_size * 3)
            << "Excessive memory usage for field size " << field_size;
    }
}

} // namespace omop::performance