// Integration test for compressed CSV file extraction with various compression formats
#include <gtest/gtest.h>
#include "extract/csv_extractor.h"
#include "core/interfaces.h"
#include "test_helpers/test_data_generator.h"
#include <fstream>
#include <filesystem>
#include <zlib.h>

namespace omop::test {

class CompressedCsvExtractorIntegrationTest : public ::testing::Test {
protected:
    std::filesystem::path test_dir_;
    std::unique_ptr<TestDataGenerator> data_generator_;

    void SetUp() override {
        // Create temporary test directory
        test_dir_ = std::filesystem::temp_directory_path() / "omop_compressed_csv_test";
        std::filesystem::create_directories(test_dir_);

        data_generator_ = std::make_unique<TestDataGenerator>();
    }

    void TearDown() override {
        // Clean up test directory
        std::filesystem::remove_all(test_dir_);
    }

    // Helper to create compressed CSV file
    std::string CreateCompressedCsvFile(const std::string& filename,
                                      extract::CompressedCsvExtractor::CompressionFormat format,
                                      size_t num_records = 100) {
        // First create uncompressed CSV
        std::string csv_path = (test_dir_ / filename).string();
        std::ofstream csv_file(csv_path);

        // Write header
        csv_file << "patient_id,gender,age,diagnosis_code,admission_date\n";

        // Write test data
        for (size_t i = 0; i < num_records; ++i) {
            csv_file << i + 1000 << ","
                    << (i % 2 == 0 ? "M" : "F") << ","
                    << (20 + i % 60) << ","
                    << "ICD-" << (100 + i % 50) << ","
                    << "2024-01-" << std::setfill('0') << std::setw(2) << (1 + i % 28) << "\n";
        }
        csv_file.close();

        // Compress based on format
        std::string compressed_path;
        switch (format) {
            case extract::CompressedCsvExtractor::CompressionFormat::Gzip:
                compressed_path = CompressGzip(csv_path);
                break;
            case extract::CompressedCsvExtractor::CompressionFormat::Zip:
                compressed_path = CompressZip(csv_path);
                break;
            default:
                compressed_path = csv_path;
        }

        // Remove uncompressed file if compressed
        if (compressed_path != csv_path) {
            std::filesystem::remove(csv_path);
        }

        return compressed_path;
    }

    std::string CompressGzip(const std::string& input_path) {
        std::string output_path = input_path + ".gz";

        // Read input file
        std::ifstream input(input_path, std::ios::binary);
        std::string data((std::istreambuf_iterator<char>(input)),
                        std::istreambuf_iterator<char>());
        input.close();

        // Compress using zlib
        z_stream zs;
        memset(&zs, 0, sizeof(zs));

        if (deflateInit2(&zs, Z_DEFAULT_COMPRESSION, Z_DEFLATED,
                        15 + 16, 8, Z_DEFAULT_STRATEGY) != Z_OK) {
            throw std::runtime_error("Failed to initialize gzip compression");
        }

        zs.next_in = reinterpret_cast<Bytef*>(const_cast<char*>(data.data()));
        zs.avail_in = data.size();

        std::vector<char> compressed(data.size() + 1024);
        zs.next_out = reinterpret_cast<Bytef*>(compressed.data());
        zs.avail_out = compressed.size();

        deflate(&zs, Z_FINISH);
        deflateEnd(&zs);

        // Write compressed data
        std::ofstream output(output_path, std::ios::binary);
        output.write(compressed.data(), zs.total_out);
        output.close();

        return output_path;
    }

    std::string CompressZip(const std::string& input_path) {
        // For testing purposes, create a simple zip-like format
        // In production, would use a proper ZIP library
        std::string output_path = input_path + ".zip";
        std::filesystem::copy_file(input_path, output_path);
        return output_path;
    }
};

// Tests automatic compression format detection
TEST_F(CompressedCsvExtractorIntegrationTest, AutoDetectCompressionFormat) {
    // Test various file extensions
    struct TestCase {
        std::string extension;
        extract::CompressedCsvExtractor::CompressionFormat expected_format;
    };

    std::vector<TestCase> test_cases = {
        {".csv.gz", extract::CompressedCsvExtractor::CompressionFormat::Gzip},
        {".csv.zip", extract::CompressedCsvExtractor::CompressionFormat::Zip},
        {".csv", extract::CompressedCsvExtractor::CompressionFormat::None}
    };

    for (const auto& tc : test_cases) {
        std::string filename = "test" + tc.extension;
        std::string filepath = (test_dir_ / filename).string();

        // Create empty file for detection
        std::ofstream(filepath).close();

        auto detected = extract::CompressedCsvExtractor::detect_compression(filepath);
        EXPECT_EQ(detected, tc.expected_format)
            << "Failed to detect format for extension: " << tc.extension;

        std::filesystem::remove(filepath);
    }
}

// Tests extraction from gzip compressed CSV files
TEST_F(CompressedCsvExtractorIntegrationTest, ExtractFromGzipFile) {
    const size_t expected_records = 50;
    std::string compressed_file = CreateCompressedCsvFile(
        "patients.csv",
        extract::CompressedCsvExtractor::CompressionFormat::Gzip,
        expected_records);

    // Configure and initialize extractor
    auto extractor = std::make_unique<extract::CompressedCsvExtractor>();
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = compressed_file;
    config["has_header"] = true;

    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records
    size_t total_extracted = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_extracted += batch.size();

        // Verify batch content
        for (const auto& record : batch) {
            EXPECT_TRUE(record.hasField("patient_id"));
            EXPECT_TRUE(record.hasField("gender"));
            EXPECT_TRUE(record.hasField("age"));
            EXPECT_TRUE(record.hasField("diagnosis_code"));
            EXPECT_TRUE(record.hasField("admission_date"));
        }
    }

    EXPECT_EQ(total_extracted, expected_records);

    // Verify statistics
    auto stats = extractor->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_extracted"]), expected_records);
    EXPECT_EQ(std::any_cast<size_t>(stats["error_count"]), 0);

    extractor->finalize(context);
}

// Tests handling of large compressed files with streaming
TEST_F(CompressedCsvExtractorIntegrationTest, LargeCompressedFileStreaming) {
    const size_t large_record_count = 10000;
    std::string compressed_file = CreateCompressedCsvFile(
        "large_dataset.csv",
        extract::CompressedCsvExtractor::CompressionFormat::Gzip,
        large_record_count);

    auto extractor = std::make_unique<extract::CompressedCsvExtractor>();
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = compressed_file;
    config["has_header"] = true;

    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract in batches and verify memory efficiency
    size_t total_extracted = 0;
    size_t batch_count = 0;
    const size_t batch_size = 1000;

    auto start_time = std::chrono::steady_clock::now();

    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(batch_size, context);
        total_extracted += batch.size();
        batch_count++;

        // Verify batch size (except possibly last batch)
        if (extractor->has_more_data()) {
            EXPECT_EQ(batch.size(), batch_size);
        }
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    EXPECT_EQ(total_extracted, large_record_count);
    EXPECT_GT(batch_count, 1); // Verify streaming worked

    // Log performance metrics
    std::cout << "Extracted " << large_record_count << " records in "
              << duration.count() << "ms" << std::endl;

    extractor->finalize(context);
}

// Tests error handling for corrupted compressed files
TEST_F(CompressedCsvExtractorIntegrationTest, CorruptedCompressedFileHandling) {
    // Create a corrupted gzip file
    std::string corrupted_file = (test_dir_ / "corrupted.csv.gz").string();
    std::ofstream file(corrupted_file, std::ios::binary);
    file << "\x1f\x8b\x08\x00\x00\x00\x00\x00"; // Partial gzip header
    file << "This is not valid compressed data";
    file.close();

    auto extractor = std::make_unique<extract::CompressedCsvExtractor>();
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = corrupted_file;

    core::ProcessingContext context;

    // Should throw exception during initialization or first extraction
    EXPECT_THROW({
        extractor->initialize(config, context);
        if (extractor->has_more_data()) {
            extractor->extract_batch(10, context);
        }
    }, common::ExtractionException);
}

// Tests concurrent extraction from multiple compressed files
TEST_F(CompressedCsvExtractorIntegrationTest, ConcurrentMultiFileExtraction) {
    const int num_files = 5;
    std::vector<std::string> compressed_files;

    // Create multiple compressed files
    for (int i = 0; i < num_files; ++i) {
        std::string filename = "data_" + std::to_string(i) + ".csv";
        compressed_files.push_back(CreateCompressedCsvFile(
            filename,
            extract::CompressedCsvExtractor::CompressionFormat::Gzip,
            100 + i * 50));
    }

    // Extract from all files concurrently
    std::vector<std::future<size_t>> futures;

    for (const auto& filepath : compressed_files) {
        futures.push_back(std::async(std::launch::async, [filepath]() {
            auto extractor = std::make_unique<extract::CompressedCsvExtractor>();
            std::unordered_map<std::string, std::any> config;
            config["filepath"] = filepath;
            config["has_header"] = true;

            core::ProcessingContext context;
            extractor->initialize(config, context);

            size_t count = 0;
            while (extractor->has_more_data()) {
                auto batch = extractor->extract_batch(50, context);
                count += batch.size();
            }

            extractor->finalize(context);
            return count;
        }));
    }

    // Verify results
    size_t expected_total = 0;
    for (int i = 0; i < num_files; ++i) {
        expected_total += 100 + i * 50;
        size_t extracted = futures[i].get();
        EXPECT_EQ(extracted, 100 + i * 50);
    }
}

// Tests memory-mapped file optimization for large compressed files
TEST_F(CompressedCsvExtractorIntegrationTest, MemoryMappedCompressedFile) {
    // Create a moderately large compressed file
    std::string compressed_file = CreateCompressedCsvFile(
        "mmap_test.csv",
        extract::CompressedCsvExtractor::CompressionFormat::Gzip,
        5000);

    auto extractor = std::make_unique<extract::CompressedCsvExtractor>();
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = compressed_file;
    config["has_header"] = true;
    config["use_memory_mapping"] = true; // Enable memory mapping if available

    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Track memory usage pattern
    size_t initial_memory = GetCurrentMemoryUsage();
    size_t peak_memory = initial_memory;
    size_t total_extracted = 0;

    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(500, context);
        total_extracted += batch.size();

        size_t current_memory = GetCurrentMemoryUsage();
        peak_memory = std::max(peak_memory, current_memory);
    }

    EXPECT_EQ(total_extracted, 5000);

    // Memory usage should be relatively constant with memory mapping
    size_t memory_increase = peak_memory - initial_memory;
    std::cout << "Memory increase: " << memory_increase / 1024 << " KB" << std::endl;

    extractor->finalize(context);
}

private:
    // Helper to get current memory usage (platform-specific)
    size_t GetCurrentMemoryUsage() {
        // Simplified implementation - in production would use platform-specific APIs
        return 0;
    }

} // namespace omop::test