// Integration test for platform-specific utilities across different operating systems
#include <gtest/gtest.h>
#ifdef _WIN32
#include "extract/platform/windows_utils.h"
#else
#include "extract/platform/unix_utils.h"
#endif
#include "common/utilities.h"
#include <filesystem>
#include <fstream>
#include <thread>

namespace omop::test {

class PlatformUtilsIntegrationTest : public ::testing::Test {
protected:
    std::filesystem::path test_dir_;

    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_platform_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    void CreateTestFile(const std::string& filename, size_t size_mb) {
        std::ofstream file(test_dir_ / filename, std::ios::binary);
        std::vector<char> buffer(1024 * 1024, 'A');
        for (size_t i = 0; i < size_mb; ++i) {
            file.write(buffer.data(), buffer.size());
        }
        file.close();
    }
};

// Tests cross-platform file operations
TEST_F(PlatformUtilsIntegrationTest, CrossPlatformFileOperations) {
    const std::string test_file = "test_data.bin";
    CreateTestFile(test_file, 5);

    std::string filepath = (test_dir_ / test_file).string();

#ifdef _WIN32
    using namespace omop::extract::platform;

    // Test Windows-specific file operations
    size_t file_size = get_file_size(filepath);
    EXPECT_EQ(file_size, 5 * 1024 * 1024);

    // Test file attributes
    DWORD original_attrs = get_file_attributes(filepath);
    EXPECT_NE(original_attrs, INVALID_FILE_ATTRIBUTES);

    // Set and verify attributes
    bool success = set_file_attributes(filepath, FILE_ATTRIBUTE_READONLY);
    EXPECT_TRUE(success);

    DWORD new_attrs = get_file_attributes(filepath);
    EXPECT_TRUE(new_attrs & FILE_ATTRIBUTE_READONLY);

    // Restore original attributes
    set_file_attributes(filepath, original_attrs);

#else
    using namespace omop::extract::platform;

    // Test Unix-specific file operations
    size_t file_size = get_file_size(filepath);
    EXPECT_EQ(file_size, 5 * 1024 * 1024);

    // Test file permissions
    mode_t original_perms = get_file_permissions(filepath);
    EXPECT_NE(original_perms, 0);

    // Set and verify permissions
    bool success = set_file_permissions(filepath, 0644);
    EXPECT_TRUE(success);

    mode_t new_perms = get_file_permissions(filepath);
    EXPECT_EQ(new_perms & 0777, 0644);

    // Restore original permissions
    set_file_permissions(filepath, original_perms);
#endif
}

// Tests memory-mapped file operations
TEST_F(PlatformUtilsIntegrationTest, MemoryMappedFileOperations) {
    const std::string test_file = "mmap_test.dat";
    const size_t file_size_mb = 10;
    CreateTestFile(test_file, file_size_mb);

    std::string filepath = (test_dir_ / test_file).string();

#ifdef _WIN32
    using namespace omop::extract::platform;

    // Test Windows memory mapping
    auto [file_handle, mapping_handle] = create_file_mapping(filepath);
    EXPECT_TRUE(file_handle.is_valid());
    EXPECT_TRUE(mapping_handle.is_valid());

    void* mapped_view = map_view_of_file(mapping_handle.get());
    EXPECT_NE(mapped_view, nullptr);

    // Read some data
    if (mapped_view) {
        char* data = static_cast<char*>(mapped_view);
        EXPECT_EQ(data[0], 'A');
        EXPECT_EQ(data[1024], 'A');

        bool unmapped = unmap_view_of_file(mapped_view);
        EXPECT_TRUE(unmapped);
    }

#else
    using namespace omop::extract::platform;

    // Test Unix memory mapping
    MemoryMappedFile mmap;
    bool mapped = mmap.map_file(filepath, true);
    EXPECT_TRUE(mapped);
    EXPECT_TRUE(mmap.is_mapped());
    EXPECT_EQ(mmap.size(), file_size_mb * 1024 * 1024);

    // Read some data
    if (mmap.data()) {
        char* data = static_cast<char*>(mmap.data());
        EXPECT_EQ(data[0], 'A');
        EXPECT_EQ(data[1024], 'A');
    }

    mmap.unmap();
    EXPECT_FALSE(mmap.is_mapped());
#endif
}

// Tests high-resolution timing across platforms
TEST_F(PlatformUtilsIntegrationTest, HighResolutionTiming) {
#ifdef _WIN32
    using namespace omop::extract::platform;

    WindowsHighResTimer timer;

    // Perform some work
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 90.0);  // Allow some tolerance
    EXPECT_LE(elapsed_ms, 150.0);

    // Reset and measure again
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    double elapsed_seconds = timer.elapsed_seconds();
    EXPECT_GE(elapsed_seconds, 0.04);
    EXPECT_LE(elapsed_seconds, 0.08);

#else
    using namespace omop::extract::platform;

    UnixHighResTimer timer;

    // Perform some work
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 90.0);  // Allow some tolerance
    EXPECT_LE(elapsed_ms, 150.0);

    // Reset and measure again
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    double elapsed_seconds = timer.elapsed_seconds();
    EXPECT_GE(elapsed_seconds, 0.04);
    EXPECT_LE(elapsed_seconds, 0.08);
#endif
}

// Tests system information retrieval
TEST_F(PlatformUtilsIntegrationTest, SystemInformationRetrieval) {
#ifdef _WIN32
    using namespace omop::extract::platform;

    // Get memory information
    MemoryInfo mem_info = get_memory_info();
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);

    // Get available drives
    std::vector<char> drives = get_available_drives();
    EXPECT_FALSE(drives.empty());

    // At least C: drive should exist on Windows
    bool found_c_drive = false;
    for (char drive : drives) {
        if (drive == 'C') {
            found_c_drive = true;
            break;
        }
    }
    EXPECT_TRUE(found_c_drive);

#else
    using namespace omop::extract::platform;

    // Get memory information
    MemoryInfo mem_info = get_memory_info();
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);

    // Get CPU count
    size_t cpu_count = get_cpu_count();
    EXPECT_GT(cpu_count, 0);
    EXPECT_LE(cpu_count, 1024); // Reasonable upper bound

    // Get mounted filesystems
    std::vector<std::string> filesystems = get_mounted_filesystems();
    EXPECT_FALSE(filesystems.empty());
#endif
}

// Tests temporary file creation
TEST_F(PlatformUtilsIntegrationTest, TemporaryFileCreation) {
#ifdef _WIN32
    using namespace omop::extract::platform;
#else
    using namespace omop::extract::platform;
#endif

    // Get temp directory
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(std::filesystem::exists(temp_dir));
    EXPECT_TRUE(std::filesystem::is_directory(temp_dir));

    // Create temporary files
    std::vector<std::string> temp_files;

    for (int i = 0; i < 5; ++i) {
        std::string temp_file = create_temp_file("omop_test_", ".tmp");
        EXPECT_FALSE(temp_file.empty());
        EXPECT_TRUE(std::filesystem::exists(temp_file));
        temp_files.push_back(temp_file);

        // Write some data
        std::ofstream file(temp_file);
        file << "Test data " << i << std::endl;
        file.close();
    }

    // Verify all files are unique
    std::set<std::string> unique_files(temp_files.begin(), temp_files.end());
    EXPECT_EQ(unique_files.size(), temp_files.size());

    // Clean up
    for (const auto& file : temp_files) {
        std::filesystem::remove(file);
    }
}

// Tests path operations and symbolic links
TEST_F(PlatformUtilsIntegrationTest, PathOperations) {
    std::string test_file = (test_dir_ / "real_file.txt").string();
    std::ofstream(test_file) << "Test content";

#ifdef _WIN32
    using namespace omop::extract::platform;

    // Test network path detection
    bool is_network = is_network_path("\\\\server\\share\\file.txt");
    EXPECT_TRUE(is_network);

    is_network = is_network_path("C:\\local\\file.txt");
    EXPECT_FALSE(is_network);

    // Test UTF-8 conversion
    std::string utf8_str = "Test String αβγ 中文";
    std::wstring wide_str = utf8_to_wide(utf8_str);
    std::string back_to_utf8 = wide_to_utf8(wide_str);
    EXPECT_EQ(utf8_str, back_to_utf8);

#else
    using namespace omop::extract::platform;

    // Test symbolic link operations (if supported)
    std::string link_path = (test_dir_ / "link_to_file.txt").string();

    // Try to create symbolic link (may fail without permissions)
    try {
        std::filesystem::create_symlink(test_file, link_path);

        bool is_link = is_symbolic_link(link_path);
        EXPECT_TRUE(is_link);

        std::string resolved = resolve_symbolic_link(link_path);
        EXPECT_FALSE(resolved.empty());

        std::string real_path = get_real_path(link_path);
        EXPECT_FALSE(real_path.empty());

        std::filesystem::remove(link_path);
    } catch (const std::filesystem::filesystem_error&) {
        // Symbolic link creation may fail without permissions
        GTEST_SKIP() << "Symbolic link creation requires permissions";
    }

    // Test real path resolution
    std::string real_path = get_real_path(test_file);
    EXPECT_FALSE(real_path.empty());
    EXPECT_TRUE(std::filesystem::exists(real_path));
#endif
}

// Tests concurrent file operations
TEST_F(PlatformUtilsIntegrationTest, ConcurrentFileOperations) {
    const int num_threads = 10;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, &success_count]() {
#ifdef _WIN32
            using namespace omop::extract::platform;
#else
            using namespace omop::extract::platform;
#endif

            // Each thread creates and manipulates its own temp file
            std::string temp_file = create_temp_file(
                "concurrent_" + std::to_string(i) + "_", ".tmp");

            if (!temp_file.empty() && std::filesystem::exists(temp_file)) {
                // Write data
                std::ofstream file(temp_file);
                for (int j = 0; j < 100; ++j) {
                    file << "Thread " << i << " Line " << j << "\n";
                }
                file.close();

                // Get file size
                size_t size = get_file_size(temp_file);
                if (size > 0) {
                    success_count++;
                }

                // Clean up
                std::filesystem::remove(temp_file);
            }
        });
    }

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    EXPECT_EQ(success_count.load(), num_threads);
}

// Tests process priority manipulation
TEST_F(PlatformUtilsIntegrationTest, ProcessPriorityManipulation) {
#ifdef _WIN32
    using namespace omop::extract::platform;

    // Test process priority on Windows
    bool success = set_process_priority(NORMAL_PRIORITY_CLASS);
    EXPECT_TRUE(success);

    // Note: Getting current priority requires more complex API calls

#else
    using namespace omop::extract::platform;

    // Get current priority
    int original_priority = get_process_priority();

    // Try to set a slightly lower priority (higher nice value)
    // This may fail without appropriate permissions
    int new_priority = original_priority + 1;
    if (new_priority <= 19) { // Max nice value
        bool success = set_process_priority(new_priority);

        if (success) {
            int current_priority = get_process_priority();
            EXPECT_EQ(current_priority, new_priority);

            // Restore original priority
            set_process_priority(original_priority);
        }
    }
#endif
}

// Tests error handling for invalid operations
TEST_F(PlatformUtilsIntegrationTest, ErrorHandlingInvalidOperations) {
#ifdef _WIN32
    using namespace omop::extract::platform;

    // Test invalid file operations
    size_t size = get_file_size("/invalid/path/that/does/not/exist");
    EXPECT_EQ(size, 0);

    DWORD attrs = get_file_attributes("/invalid/path");
    EXPECT_EQ(attrs, INVALID_FILE_ATTRIBUTES);

    // Test error message retrieval
    std::string error_msg = get_windows_error_message(ERROR_FILE_NOT_FOUND);
    EXPECT_FALSE(error_msg.empty());

#else
    using namespace omop::extract::platform;

    // Test invalid file operations
    size_t size = get_file_size("/invalid/path/that/does/not/exist");
    EXPECT_EQ(size, 0);

    mode_t perms = get_file_permissions("/invalid/path");
    EXPECT_EQ(perms, 0);

    // Test error message retrieval
    std::string error_msg = get_system_error_message(ENOENT);
    EXPECT_FALSE(error_msg.empty());

    // Test invalid memory operations
    void* invalid_addr = reinterpret_cast<void*>(0x1);
    bool locked = lock_memory(invalid_addr, 4096);
    EXPECT_FALSE(locked);
#endif
}

} // namespace omop::test