// Integration test for advanced loader implementations including HTTP, S3, and multi-format loaders
#include <gtest/gtest.h>
#include "load/additional_loaders.h"
#include "load/batch_loader.h"
#include "core/record.h"
#include "test_helpers/test_data_generator.h"
#include <filesystem>
#include <thread>
#include <future>
#include <httplib.h>

namespace omop::test {

class AdvancedLoadersIntegrationTest : public ::testing::Test {
protected:
    std::filesystem::path test_dir_;
    std::unique_ptr<TestDataGenerator> data_generator_;
    std::unique_ptr<httplib::Server> http_server_;
    std::thread server_thread_;
    std::atomic<bool> server_running_{false};
    int server_port_{8088};

    void SetUp() override {
        // Create temporary test directory
        test_dir_ = std::filesystem::temp_directory_path() / "omop_advanced_loaders_test";
        std::filesystem::create_directories(test_dir_);

        data_generator_ = std::make_unique<TestDataGenerator>();

        // Start HTTP test server
        StartHttpTestServer();
    }

    void TearDown() override {
        // Stop HTTP server
        StopHttpTestServer();

        // Clean up test directory
        std::filesystem::remove_all(test_dir_);
    }

    void StartHttpTestServer() {
        http_server_ = std::make_unique<httplib::Server>();

        // Set up test endpoints
        http_server_->Post("/api/load", [this](const httplib::Request& req, httplib::Response& res) {
            // Store received data for verification
            {
                std::lock_guard<std::mutex> lock(server_data_mutex_);
                received_data_.push_back(req.body);
                received_headers_.push_back(req.headers);
            }

            res.set_content("{\"status\":\"success\",\"records_processed\":1}", "application/json");
        });

        http_server_->Get("/health", [](const httplib::Request&, httplib::Response& res) {
            res.set_content("{\"status\":\"healthy\"}", "application/json");
        });

        // Start server in separate thread
        server_thread_ = std::thread([this]() {
            server_running_ = true;
            http_server_->listen("localhost", server_port_);
        });

        // Wait for server to start
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    void StopHttpTestServer() {
        if (http_server_) {
            http_server_->stop();
        }
        if (server_thread_.joinable()) {
            server_thread_.join();
        }
        server_running_ = false;
    }

    core::RecordBatch CreateTestRecordBatch(size_t size) {
        core::RecordBatch batch;
        batch.reserve(size);

        for (size_t i = 0; i < size; ++i) {
            core::Record record;
            record.setField("patient_id", static_cast<int64_t>(1000 + i));
            record.setField("measurement_value", 50.0 + i * 0.5);
            record.setField("measurement_date", std::chrono::system_clock::now());
            record.setField("unit", "mg/dL");
            batch.addRecord(std::move(record));
        }

        return batch;
    }

protected:
    // Server data storage for verification
    std::mutex server_data_mutex_;
    std::vector<std::string> received_data_;
    std::vector<httplib::Headers> received_headers_;
};

// Tests JSON batch loader with various output formats
TEST_F(AdvancedLoadersIntegrationTest, JsonBatchLoaderFormats) {
    // Test array output format
    {
        load::BatchLoaderOptions batch_options;
        batch_options.batch_size = 10;

        load::JsonBatchLoader::JsonOptions json_options;
        json_options.pretty_print = true;
        json_options.array_output = true;
        json_options.include_metadata = true;

        auto loader = std::make_unique<load::JsonBatchLoader>(batch_options, json_options);

        std::unordered_map<std::string, std::any> config;
        config["output_file"] = (test_dir_ / "output_array.json").string();

        core::ProcessingContext context;
        loader->initialize(config, context);

        // Load test data
        auto batch = CreateTestRecordBatch(25);
        size_t loaded = loader->load_batch(batch, context);
        EXPECT_EQ(loaded, 25);

        loader->commit(context);
        loader->finalize(context);

        // Verify output file
        std::ifstream file(config["output_file"].has_value() ?
                          std::any_cast<std::string>(config["output_file"]) : "");
        EXPECT_TRUE(file.good());

        nlohmann::json json_data;
        file >> json_data;
        EXPECT_TRUE(json_data.is_array());
        EXPECT_EQ(json_data.size(), 25);
    }

    // Test NDJSON (newline-delimited JSON) format
    {
        load::BatchLoaderOptions batch_options;
        batch_options.batch_size = 5;

        load::JsonBatchLoader::JsonOptions json_options;
        json_options.pretty_print = false;
        json_options.array_output = false; // NDJSON mode

        auto loader = std::make_unique<load::JsonBatchLoader>(batch_options, json_options);

        std::unordered_map<std::string, std::any> config;
        config["output_file"] = (test_dir_ / "output_ndjson.jsonl").string();

        core::ProcessingContext context;
        loader->initialize(config, context);

        auto batch = CreateTestRecordBatch(15);
        loader->load_batch(batch, context);
        loader->commit(context);
        loader->finalize(context);

        // Verify NDJSON format
        std::ifstream file(std::any_cast<std::string>(config["output_file"]));
        std::string line;
        int line_count = 0;

        while (std::getline(file, line)) {
            nlohmann::json json_line = nlohmann::json::parse(line);
            EXPECT_TRUE(json_line.is_object());
            line_count++;
        }

        EXPECT_EQ(line_count, 15);
    }
}

// Tests HTTP loader with retry logic and authentication
TEST_F(AdvancedLoadersIntegrationTest, HttpLoaderWithRetries) {
    load::HttpLoader::HttpOptions options;
    options.method = "POST";
    options.content_type = "application/json";
    options.timeout_seconds = 5;
    options.retry_count = 3;
    options.retry_delay_ms = 100;
    options.headers["X-API-Key"] = "test-key-123";
    options.auth_type = "apikey";

    auto loader = std::make_unique<load::HttpLoader>(options);

    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = "http://localhost:" + std::to_string(server_port_) + "/api/load";

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Load single record
    core::Record record;
    record.setField("test_id", 1);
    record.setField("value", "test_data");

    bool success = loader->load(record, context);
    EXPECT_TRUE(success);

    // Load batch
    auto batch = CreateTestRecordBatch(10);
    size_t loaded = loader->load_batch(batch, context);
    EXPECT_EQ(loaded, 10);

    loader->commit(context);

    // Verify server received data
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    {
        std::lock_guard<std::mutex> lock(server_data_mutex_);
        EXPECT_GE(received_data_.size(), 2); // At least single record + batch

        // Verify headers
        for (const auto& headers : received_headers_) {
            auto it = headers.find("X-API-Key");
            EXPECT_NE(it, headers.end());
            if (it != headers.end()) {
                EXPECT_EQ(it->second, "test-key-123");
            }
        }
    }

    loader->finalize(context);
}

// Tests multi-format loader with parallel output
TEST_F(AdvancedLoadersIntegrationTest, MultiFormatLoaderParallel) {
    auto multi_loader = std::make_unique<load::MultiFormatLoader>("multi_format_test");

    // Add CSV loader
    {
        load::BatchLoaderOptions csv_options;
        csv_options.batch_size = 50;

        auto csv_loader = std::make_unique<load::CsvBatchLoader>(csv_options);
        multi_loader->add_loader(std::move(csv_loader), 1.0);
    }

    // Add JSON loader
    {
        load::BatchLoaderOptions json_batch_options;
        json_batch_options.batch_size = 50;

        load::JsonBatchLoader::JsonOptions json_options;
        json_options.array_output = true;

        auto json_loader = std::make_unique<load::JsonBatchLoader>(
            json_batch_options, json_options);
        multi_loader->add_loader(std::move(json_loader), 1.0);
    }

    // Configure output files
    std::unordered_map<std::string, std::any> config;
    config["csv_output"] = (test_dir_ / "multi_output.csv").string();
    config["json_output"] = (test_dir_ / "multi_output.json").string();

    core::ProcessingContext context;
    multi_loader->initialize(config, context);

    // Load large batch to test parallel processing
    auto batch = CreateTestRecordBatch(1000);

    auto start_time = std::chrono::steady_clock::now();
    size_t loaded = multi_loader->load_batch(batch, context);
    auto end_time = std::chrono::steady_clock::now();

    EXPECT_EQ(loaded, 1000);

    multi_loader->commit(context);

    // Verify both output files exist
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "multi_output.csv"));
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "multi_output.json"));

    // Check performance improvement from parallel loading
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time);
    std::cout << "Parallel loading of 1000 records took: "
              << duration.count() << "ms" << std::endl;

    multi_loader->finalize(context);
}

// Tests memory-mapped file batch loader
TEST_F(AdvancedLoadersIntegrationTest, MemoryMappedFileBatchLoader) {
    load::BatchLoaderOptions options;
    options.batch_size = 100;

    size_t file_size_hint = 10 * 1024 * 1024; // 10MB hint
    auto mmap_loader = std::make_unique<load::MmapBatchLoader>(options, file_size_hint);

    std::unordered_map<std::string, std::any> config;
    config["output_file"] = (test_dir_ / "mmap_output.dat").string();
    config["format"] = "binary";

    core::ProcessingContext context;
    mmap_loader->initialize(config, context);

    // Load data in multiple batches to test memory mapping efficiency
    const size_t num_batches = 10;
    const size_t records_per_batch = 500;

    for (size_t i = 0; i < num_batches; ++i) {
        auto batch = CreateTestRecordBatch(records_per_batch);
        size_t loaded = mmap_loader->load_batch(batch, context);
        EXPECT_EQ(loaded, records_per_batch);

        // Simulate some processing delay
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    mmap_loader->commit(context);

    // Verify file size is appropriate
    auto file_size = std::filesystem::file_size(test_dir_ / "mmap_output.dat");
    EXPECT_GT(file_size, 0);
    std::cout << "Memory-mapped file size: " << file_size / 1024 << " KB" << std::endl;

    mmap_loader->finalize(context);
}

// Tests S3-compatible loader with multipart upload simulation
TEST_F(AdvancedLoadersIntegrationTest, S3LoaderMultipartUpload) {
    load::S3Loader::S3Options s3_options;
    s3_options.bucket_name = "test-bucket";
    s3_options.key_prefix = "omop/data/";
    s3_options.use_multipart_upload = true;
    s3_options.multipart_threshold = 1024; // 1KB for testing
    s3_options.part_size = 1024;
    s3_options.metadata["project"] = "omop-etl";
    s3_options.metadata["environment"] = "test";

    // For testing, we'll use local file system to simulate S3
    auto s3_loader = std::make_unique<load::S3Loader>(s3_options);

    std::unordered_map<std::string, std::any> config;
    config["endpoint"] = "http://localhost:9000"; // MinIO endpoint for testing
    config["local_test_mode"] = true; // Use local file system for testing
    config["local_test_path"] = test_dir_.string();

    core::ProcessingContext context;
    s3_loader->initialize(config, context);

    // Create large enough data to trigger multipart upload
    auto large_batch = CreateTestRecordBatch(1000);

    size_t loaded = s3_loader->load_batch(large_batch, context);
    EXPECT_EQ(loaded, 1000);

    s3_loader->commit(context);

    // Verify simulated S3 files
    auto s3_test_dir = test_dir_ / "test-bucket" / "omop" / "data";
    if (std::filesystem::exists(s3_test_dir)) {
        int file_count = 0;
        for (const auto& entry : std::filesystem::directory_iterator(s3_test_dir)) {
            if (entry.is_regular_file()) {
                file_count++;
            }
        }
        EXPECT_GT(file_count, 0);
    }

    s3_loader->finalize(context);
}

// Tests batch loader error recovery and rollback
TEST_F(AdvancedLoadersIntegrationTest, BatchLoaderErrorRecovery) {
    load::BatchLoaderOptions options;
    options.batch_size = 10;
    options.enable_compression = true;

    auto csv_loader = std::make_unique<load::CsvBatchLoader>(options);

    // Configure with invalid path to trigger error
    std::unordered_map<std::string, std::any> config;
    config["output_file"] = "/invalid/path/that/does/not/exist/output.csv";

    core::ProcessingContext context;

    // Should handle initialization error gracefully
    EXPECT_THROW(csv_loader->initialize(config, context), common::LoadingException);

    // Test with valid configuration but simulate write error
    config["output_file"] = (test_dir_ / "recovery_test.csv").string();

    auto recovery_loader = std::make_unique<load::CsvBatchLoader>(options);
    recovery_loader->initialize(config, context);

    // Load some data
    auto batch1 = CreateTestRecordBatch(5);
    recovery_loader->load_batch(batch1, context);

    // Simulate error by making file read-only
    std::filesystem::permissions(
        test_dir_ / "recovery_test.csv",
        std::filesystem::perms::owner_read,
        std::filesystem::perm_options::replace);

    // This should fail
    auto batch2 = CreateTestRecordBatch(5);
    size_t loaded = recovery_loader->load_batch(batch2, context);

    // Rollback should work
    recovery_loader->rollback(context);

    // Restore permissions
    std::filesystem::permissions(
        test_dir_ / "recovery_test.csv",
        std::filesystem::perms::owner_all,
        std::filesystem::perm_options::replace);

    recovery_loader->finalize(context);
}

// Tests concurrent batch loading with multiple loaders
TEST_F(AdvancedLoadersIntegrationTest, ConcurrentBatchLoading) {
    const int num_loaders = 4;
    const int records_per_loader = 250;

    std::vector<std::future<size_t>> futures;

    for (int i = 0; i < num_loaders; ++i) {
        futures.push_back(std::async(std::launch::async, [this, i, records_per_loader]() {
            load::BatchLoaderOptions options;
            options.batch_size = 50;
            options.worker_threads = 2;

            auto loader = std::make_unique<load::CsvBatchLoader>(options);

            std::unordered_map<std::string, std::any> config;
            config["output_file"] = (test_dir_ /
                ("concurrent_output_" + std::to_string(i) + ".csv")).string();

            core::ProcessingContext context;
            loader->initialize(config, context);

            auto batch = CreateTestRecordBatch(records_per_loader);
            size_t loaded = loader->load_batch(batch, context);

            loader->commit(context);
            loader->finalize(context);

            return loaded;
        }));
    }

    // Wait for all loaders to complete
    size_t total_loaded = 0;
    for (auto& future : futures) {
        total_loaded += future.get();
    }

    EXPECT_EQ(total_loaded, num_loaders * records_per_loader);

    // Verify all output files
    for (int i = 0; i < num_loaders; ++i) {
        auto file_path = test_dir_ / ("concurrent_output_" + std::to_string(i) + ".csv");
        EXPECT_TRUE(std::filesystem::exists(file_path));

        // Count lines in each file
        std::ifstream file(file_path);
        std::string line;
        int line_count = 0;
        while (std::getline(file, line)) {
            line_count++;
        }

        // Should have header + data lines
        EXPECT_EQ(line_count, records_per_loader + 1);
    }
}

// Tests batch compression effectiveness
TEST_F(AdvancedLoadersIntegrationTest, BatchCompressionEffectiveness) {
    // Create highly compressible data (repeated values)
    core::RecordBatch compressible_batch;
    for (size_t i = 0; i < 1000; ++i) {
        core::Record record;
        record.setField("patient_id", static_cast<int64_t>(1000));
        record.setField("diagnosis", "DIABETES_TYPE_2");
        record.setField("status", "ACTIVE");
        record.setField("value", 100.0);
        compressible_batch.addRecord(std::move(record));
    }

    // Test with compression enabled
    {
        load::BatchLoaderOptions options;
        options.batch_size = 1000;
        options.enable_compression = true;
        options.compression_type = "gzip";

        auto loader = std::make_unique<load::CsvBatchLoader>(options);

        std::unordered_map<std::string, std::any> config;
        config["output_file"] = (test_dir_ / "compressed_output.csv.gz").string();

        core::ProcessingContext context;
        loader->initialize(config, context);

        loader->load_batch(compressible_batch, context);
        loader->commit(context);
        loader->finalize(context);

        auto compressed_size = std::filesystem::file_size(test_dir_ / "compressed_output.csv.gz");
        std::cout << "Compressed size: " << compressed_size << " bytes" << std::endl;
    }

    // Test without compression for comparison
    {
        load::BatchLoaderOptions options;
        options.batch_size = 1000;
        options.enable_compression = false;

        auto loader = std::make_unique<load::CsvBatchLoader>(options);

        std::unordered_map<std::string, std::any> config;
        config["output_file"] = (test_dir_ / "uncompressed_output.csv").string();

        core::ProcessingContext context;
        loader->initialize(config, context);

        loader->load_batch(compressible_batch, context);
        loader->commit(context);
        loader->finalize(context);

        auto uncompressed_size = std::filesystem::file_size(test_dir_ / "uncompressed_output.csv");
        std::cout << "Uncompressed size: " << uncompressed_size << " bytes" << std::endl;

        // Compression should achieve significant reduction for this data
        // Note: Actual compression would require implementing the compression logic
    }
}

} // namespace omop::test