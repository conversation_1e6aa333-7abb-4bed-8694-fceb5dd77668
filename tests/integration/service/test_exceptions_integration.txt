// Integration test for exception handling across ETL pipeline components
#include <gtest/gtest.h>
#include "common/exceptions.h"
#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "transform/transformation_engine.h"
#include <thread>
#include <future>

namespace omop::test {

class ExceptionIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize test environment
    }

    void TearDown() override {
        // Clean up test environment
    }
};

// Tests exception propagation through the ETL pipeline layers
TEST_F(ExceptionIntegrationTest, ExceptionPropagationAcrossLayers) {
    // Test extraction layer exception
    try {
        throw common::ExtractionException("Test extraction error", "csv");
    } catch (const common::ExtractionException& e) {
        EXPECT_STREQ(e.what(), "Extraction error (csv): Test extraction error");
        EXPECT_EQ(e.extractor_type(), "csv");
        EXPECT_STREQ(e.operation().c_str(), "extract");
    }

    // Test transformation layer exception
    try {
        throw common::TransformationException("Test transformation error", "date_transform", "convert");
    } catch (const common::TransformationException& e) {
        EXPECT_STREQ(e.what(), "Transformation error (date_transform): Test transformation error");
        EXPECT_EQ(e.transformation_type(), "date_transform");
        EXPECT_EQ(e.operation(), "convert");
    }

    // Test loading layer exception
    try {
        throw common::LoadingException("Test loading error", "database", "Failed SQL");
    } catch (const common::LoadingException& e) {
        EXPECT_STREQ(e.what(), "Loading error (database): Test loading error");
        EXPECT_EQ(e.loader_type(), "database");
        EXPECT_TRUE(e.sql_query().has_value());
        EXPECT_EQ(e.sql_query().value(), "Failed SQL");
    }
}

// Tests database exception handling with error codes and SQL state
TEST_F(ExceptionIntegrationTest, DatabaseExceptionHandling) {
    // Test basic database exception
    try {
        throw common::DatabaseException("Connection failed", "PostgreSQL", -1);
    } catch (const common::DatabaseException& e) {
        EXPECT_STREQ(e.what(), "Database error (PostgreSQL): Connection failed");
        EXPECT_EQ(e.database_type(), "PostgreSQL");
        EXPECT_EQ(e.error_code(), -1);
        EXPECT_FALSE(e.sql_state().has_value());
    }

    // Test database exception with SQL state
    try {
        common::DatabaseException ex("Constraint violation", "MySQL", 1452);
        ex.set_sql_state("23000");
        throw ex;
    } catch (const common::DatabaseException& e) {
        EXPECT_EQ(e.error_code(), 1452);
        EXPECT_TRUE(e.sql_state().has_value());
        EXPECT_EQ(e.sql_state().value(), "23000");
    }
}

// Tests exception chaining and nested exception handling
TEST_F(ExceptionIntegrationTest, NestedExceptionHandling) {
    try {
        try {
            throw common::ConfigurationException("Invalid YAML format");
        } catch (const common::ConfigurationException& e) {
            // Wrap in transformation exception
            throw common::TransformationException(
                std::string("Configuration error during transformation: ") + e.what(),
                "vocabulary_mapping", "initialize");
        }
    } catch (const common::TransformationException& e) {
        EXPECT_TRUE(std::string(e.what()).find("Configuration error") != std::string::npos);
    }
}

// Tests exception handling in concurrent scenarios
TEST_F(ExceptionIntegrationTest, ConcurrentExceptionHandling) {
    const int num_threads = 10;
    std::vector<std::future<void>> futures;

    for (int i = 0; i < num_threads; ++i) {
        futures.push_back(std::async(std::launch::async, [i]() {
            try {
                if (i % 3 == 0) {
                    throw common::ExtractionException("Concurrent extraction error", "csv");
                } else if (i % 3 == 1) {
                    throw common::TransformationException("Concurrent transform error", "numeric", "calc");
                } else {
                    throw common::LoadingException("Concurrent loading error", "database");
                }
            } catch (const common::ETLException& e) {
                // Verify exception was caught and has proper base class
                EXPECT_TRUE(e.what() != nullptr);
                EXPECT_FALSE(e.stacktrace().empty());
            }
        }));
    }

    // Wait for all threads to complete
    for (auto& future : futures) {
        EXPECT_NO_THROW(future.get());
    }
}

// Tests exception recovery and retry logic
TEST_F(ExceptionIntegrationTest, ExceptionRecoveryPatterns) {
    int retry_count = 0;
    const int max_retries = 3;
    bool success = false;

    while (retry_count < max_retries && !success) {
        try {
            if (retry_count < 2) {
                throw common::DatabaseException("Temporary connection error", "PostgreSQL", -1);
            }
            // Succeed on third attempt
            success = true;
        } catch (const common::DatabaseException& e) {
            retry_count++;
            EXPECT_LT(retry_count, max_retries + 1);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    EXPECT_TRUE(success);
    EXPECT_EQ(retry_count, 2);
}

// Tests custom exception data and context preservation
TEST_F(ExceptionIntegrationTest, ExceptionContextPreservation) {
    core::ProcessingContext context;
    context.set_job_id("test-job-123");
    context.set_stage(core::ProcessingContext::Stage::Transform);

    try {
        // Simulate transformation error with context
        context.increment_processed(100);
        context.increment_errors(5);

        common::TransformationException ex("Field mapping failed", "vocabulary", "map");
        ex.add_context("job_id", context.job_id());
        ex.add_context("processed_count", std::to_string(context.processed_count()));
        ex.add_context("error_count", std::to_string(context.error_count()));

        throw ex;
    } catch (const common::TransformationException& e) {
        auto ctx = e.get_context();
        EXPECT_EQ(ctx["job_id"], "test-job-123");
        EXPECT_EQ(ctx["processed_count"], "100");
        EXPECT_EQ(ctx["error_count"], "5");
    }
}

// Tests exception aggregation for batch operations
TEST_F(ExceptionIntegrationTest, BatchOperationExceptionAggregation) {
    std::vector<std::exception_ptr> exceptions;

    // Simulate batch processing with multiple errors
    for (int i = 0; i < 10; ++i) {
        try {
            if (i % 4 == 0) {
                throw common::ValidationException("Record validation failed", "person");
            } else if (i % 4 == 1) {
                throw common::TransformationException("Date format error", "date_transform", "parse");
            }
        } catch (...) {
            exceptions.push_back(std::current_exception());
        }
    }

    EXPECT_EQ(exceptions.size(), 5); // 3 validation + 2 transformation errors

    // Verify we can rethrow and handle each exception
    int validation_count = 0;
    int transformation_count = 0;

    for (const auto& ex_ptr : exceptions) {
        try {
            std::rethrow_exception(ex_ptr);
        } catch (const common::ValidationException& e) {
            validation_count++;
        } catch (const common::TransformationException& e) {
            transformation_count++;
        }
    }

    EXPECT_EQ(validation_count, 3);
    EXPECT_EQ(transformation_count, 2);
}

} // namespace omop::test