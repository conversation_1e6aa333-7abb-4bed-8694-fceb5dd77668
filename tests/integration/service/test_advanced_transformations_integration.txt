// Integration test for advanced transformation types including custom, conditional, and numeric transformations
#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/numeric_transformations.h"
#include "transform/string_transformations.h"
#include "transform/date_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/custom_transformations.h"
#include "transform/vocabulary_service.h"
#include "extract/database_connector.h"
#include "test_helpers/database_fixture.h"
#include "core/record.h"
#include <yaml-cpp/yaml.h>

namespace omop::test {

class AdvancedTransformationsIntegrationTest : public DatabaseFixture {
protected:
    std::unique_ptr<transform::VocabularyService> vocab_service_;
    std::unique_ptr<transform::TransformationEngine> engine_;

    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize vocabulary service
        auto conn = CreateConnection();
        vocab_service_ = std::make_unique<transform::VocabularyService>(std::move(conn));
        vocab_service_->initialize(1000);

        // Load test vocabulary mappings
        LoadTestVocabularyMappings();

        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();

        // Register custom transformations
        transform::CustomTransformationFactory::register_custom_transformations();
    }

    void LoadTestVocabularyMappings() {
        // Add test mappings for common transformations
        transform::VocabularyMapping mapping;

        // Gender mappings
        mapping = {"M", "Gender", 8507, "OMOP", 1.0f, "standard", std::nullopt};
        vocab_service_->add_mapping(mapping);
        mapping = {"F", "Gender", 8532, "OMOP", 1.0f, "standard", std::nullopt};
        vocab_service_->add_mapping(mapping);

        // Race mappings
        mapping = {"White", "Race", 8527, "OMOP", 1.0f, "standard", std::nullopt};
        vocab_service_->add_mapping(mapping);
        mapping = {"Black", "Race", 8516, "OMOP", 1.0f, "standard", std::nullopt};
        vocab_service_->add_mapping(mapping);

        // Unit mappings
        mapping = {"mg", "Unit", 8576, "OMOP", 1.0f, "standard", std::nullopt};
        vocab_service_->add_mapping(mapping);
        mapping = {"kg", "Unit", 9529, "OMOP", 1.0f, "standard", std::nullopt};
        vocab_service_->add_mapping(mapping);
    }
};

// Tests advanced numeric transformations with unit conversions
TEST_F(AdvancedTransformationsIntegrationTest, NumericUnitConversions) {
    auto transformation = std::make_unique<transform::AdvancedNumericTransformation>();

    YAML::Node config;
    config["operation"] = "UnitConversion";
    config["from_unit"] = "lb";
    config["to_unit"] = "kg";
    config["round_to_decimal"] = 2;

    transformation->configure(config);

    core::ProcessingContext context;

    // Test various weight conversions
    struct TestCase {
        double input_lb;
        double expected_kg;
    };

    std::vector<TestCase> test_cases = {
        {150.0, 68.04},  // Average adult weight
        {0.0, 0.0},      // Zero value
        {2.2046, 1.0},   // Conversion factor test
        {-10.0, -4.54}   // Negative value (for error cases)
    };

    for (const auto& tc : test_cases) {
        auto result = transformation->transform_detailed(tc.input_lb, context);

        if (tc.input_lb >= 0) {
            EXPECT_TRUE(result.is_success());
            double converted = std::any_cast<double>(result.value);
            EXPECT_NEAR(converted, tc.expected_kg, 0.01);
        } else {
            // Negative weights might generate warning
            EXPECT_FALSE(result.warnings.empty());
        }
    }
}

// Tests complex conditional transformations with nested conditions
TEST_F(AdvancedTransformationsIntegrationTest, NestedConditionalTransformations) {
    auto transformation = std::make_unique<transform::AdvancedConditionalTransformation>();

    YAML::Node config;

    // Configure nested rules for BMI categorization
    YAML::Node rule1;
    rule1["conditions"][0]["field"] = "bmi";
    rule1["conditions"][0]["operator"] = "less_than";
    rule1["conditions"][0]["value"] = 18.5;
    rule1["then"]["type"] = "SetValue";
    rule1["then"]["value"] = "Underweight";

    YAML::Node rule2;
    rule2["conditions"][0]["field"] = "bmi";
    rule2["conditions"][0]["operator"] = "between";
    rule2["conditions"][0]["value"]["min"] = 18.5;
    rule2["conditions"][0]["value"]["max"] = 24.9;
    rule2["then"]["type"] = "SetValue";
    rule2["then"]["value"] = "Normal";

    YAML::Node rule3;
    rule3["conditions"][0]["field"] = "bmi";
    rule3["conditions"][0]["operator"] = "between";
    rule3["conditions"][0]["value"]["min"] = 25.0;
    rule3["conditions"][0]["value"]["max"] = 29.9;
    rule3["then"]["type"] = "SetValue";
    rule3["then"]["value"] = "Overweight";

    YAML::Node rule4;
    rule4["conditions"][0]["field"] = "bmi";
    rule4["conditions"][0]["operator"] = "greater_than_or_equal";
    rule4["conditions"][0]["value"] = 30.0;
    rule4["then"]["type"] = "SetValue";
    rule4["then"]["value"] = "Obese";

    config["rules"].push_back(rule1);
    config["rules"].push_back(rule2);
    config["rules"].push_back(rule3);
    config["rules"].push_back(rule4);
    config["default"]["type"] = "SetValue";
    config["default"]["value"] = "Unknown";

    transformation->configure(config);

    core::ProcessingContext context;

    // Test BMI categorization
    struct TestCase {
        double bmi;
        std::string expected_category;
    };

    std::vector<TestCase> test_cases = {
        {17.5, "Underweight"},
        {22.0, "Normal"},
        {27.5, "Overweight"},
        {32.0, "Obese"},
        {-1.0, "Unknown"}  // Invalid BMI
    };

    for (const auto& tc : test_cases) {
        core::Record record;
        record.setField("bmi", tc.bmi);

        auto result = transformation->transform_detailed(record, context);
        EXPECT_TRUE(result.is_success());

        std::string category = std::any_cast<std::string>(result.value);
        EXPECT_EQ(category, tc.expected_category)
            << "Failed for BMI: " << tc.bmi;
    }
}

// Tests string pattern extraction transformations
TEST_F(AdvancedTransformationsIntegrationTest, StringPatternExtraction) {
    auto transformation = std::make_unique<transform::StringPatternExtractionTransformation>();

    // Test extracting various patterns
    struct PatternTest {
        std::string pattern_type;
        std::string input;
        std::string expected;
    };

    std::vector<PatternTest> pattern_tests = {
        {"Email", "Contact: <EMAIL> for info", "<EMAIL>"},
        {"Phone", "Call us at (*************", "(*************"},
        {"PostalCode", "Address: 123 Main St, City, State 12345", "12345"},
        {"Number", "The patient weighs 75.5 kg", "75.5"},
        {"Date", "Admitted on 2024-03-15 at noon", "2024-03-15"}
    };

    core::ProcessingContext context;

    for (const auto& test : pattern_tests) {
        YAML::Node config;
        config["pattern_type"] = test.pattern_type;
        config["capture_group"] = 0;
        config["default_value"] = "NOT_FOUND";

        transformation->configure(config);

        auto result = transformation->transform_detailed(test.input, context);
        EXPECT_TRUE(result.is_success());

        std::string extracted = std::any_cast<std::string>(result.value);
        EXPECT_EQ(extracted, test.expected)
            << "Failed to extract " << test.pattern_type
            << " from: " << test.input;
    }
}

// Tests date calculation transformations
TEST_F(AdvancedTransformationsIntegrationTest, DateCalculationTransformations) {
    auto transformation = std::make_unique<transform::DateCalculationTransformation>();

    // Test age calculation
    YAML::Node age_config;
    age_config["operation"] = "Age";
    age_config["input_format"] = "%Y-%m-%d";
    age_config["reference_date"] = "2024-01-01";

    transformation->configure(age_config);

    core::ProcessingContext context;

    struct AgeTest {
        std::string birth_date;
        int expected_age;
    };

    std::vector<AgeTest> age_tests = {
        {"1980-01-01", 44},
        {"2000-06-15", 23},
        {"2010-12-31", 13},
        {"2023-12-31", 0}  // Less than 1 year
    };

    for (const auto& test : age_tests) {
        auto result = transformation->transform_detailed(test.birth_date, context);
        EXPECT_TRUE(result.is_success());

        int age = std::any_cast<int>(result.value);
        EXPECT_EQ(age, test.expected_age)
            << "Failed for birth date: " << test.birth_date;
    }

    // Test date difference calculation
    auto diff_transformation = std::make_unique<transform::DateCalculationTransformation>();

    YAML::Node diff_config;
    diff_config["operation"] = "DateDiff";
    diff_config["input_format"] = "%Y-%m-%d";
    diff_config["diff_unit"] = "days";
    diff_config["reference_date"] = "2024-01-01";

    diff_transformation->configure(diff_config);

    struct DiffTest {
        std::string date;
        int expected_days;
    };

    std::vector<DiffTest> diff_tests = {
        {"2024-01-10", 9},
        {"2023-12-25", -7},
        {"2024-01-01", 0}
    };

    for (const auto& test : diff_tests) {
        auto result = diff_transformation->transform_detailed(test.date, context);
        EXPECT_TRUE(result.is_success());

        int days = std::any_cast<int>(result.value);
        EXPECT_EQ(days, test.expected_days)
            << "Failed for date: " << test.date;
    }
}

// Tests vocabulary hierarchy transformations
TEST_F(AdvancedTransformationsIntegrationTest, VocabularyHierarchyTransformations) {
    // Create test concept hierarchy
    SetupTestConceptHierarchy();

    auto transformation = std::make_unique<transform::ConceptHierarchyTransformation>();

    YAML::Node config;
    config["direction"] = "ToAncestor";
    config["ancestor_level"] = 1;
    config["default_concept_id"] = 0;

    transformation->configure(config);

    core::ProcessingContext context;

    // Test concept hierarchy navigation
    struct HierarchyTest {
        int input_concept_id;
        int expected_ancestor_id;
    };

    std::vector<HierarchyTest> hierarchy_tests = {
        {1001, 1000},  // Specific drug to drug class
        {2001, 2000},  // Specific condition to condition category
        {3001, 3000}   // Specific procedure to procedure type
    };

    for (const auto& test : hierarchy_tests) {
        auto result = transformation->transform_detailed(test.input_concept_id, context);
        EXPECT_TRUE(result.is_success());

        int ancestor_id = std::any_cast<int>(result.value);
        EXPECT_EQ(ancestor_id, test.expected_ancestor_id)
            << "Failed for concept: " << test.input_concept_id;
    }
}

// Tests custom JavaScript transformations
TEST_F(AdvancedTransformationsIntegrationTest, JavaScriptTransformations) {
    auto transformation = std::make_unique<transform::JavaScriptTransformation>();

    YAML::Node config;
    config["expression"] = "input.toUpperCase() + '_PROCESSED'";
    config["output_type"] = "string";

    transformation->configure(config);

    core::ProcessingContext context;

    // Test string transformation
    auto result = transformation->transform_detailed(std::string("test"), context);
    EXPECT_TRUE(result.is_success());

    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_EQ(output, "TEST_PROCESSED");

    // Test numeric expression
    auto numeric_transform = std::make_unique<transform::JavaScriptTransformation>();

    YAML::Node numeric_config;
    numeric_config["expression"] = "Math.round(input * 1.8 + 32)";  // Celsius to Fahrenheit
    numeric_config["output_type"] = "number";

    numeric_transform->configure(numeric_config);

    auto numeric_result = numeric_transform->transform_detailed(37.0, context);
    EXPECT_TRUE(numeric_result.is_success());

    double fahrenheit = std::any_cast<double>(numeric_result.value);
    EXPECT_NEAR(fahrenheit, 99.0, 0.1);
}

// Tests transformation pipeline with multiple stages
TEST_F(AdvancedTransformationsIntegrationTest, TransformationPipeline) {
    // Create a complex transformation pipeline
    core::ProcessingContext context;

    // Input record representing patient measurement
    core::Record input_record;
    input_record.setField("patient_id", "P12345");
    input_record.setField("weight", "165 lbs");
    input_record.setField("height", "70 inches");
    input_record.setField("measurement_date", "2024-01-15");
    input_record.setField("gender", "M");

    // Stage 1: Extract numeric values
    auto extract_weight = std::make_unique<transform::StringPatternExtractionTransformation>();
    YAML::Node weight_config;
    weight_config["pattern_type"] = "Number";
    extract_weight->configure(weight_config);

    auto weight_result = extract_weight->transform_detailed(
        input_record.getField("weight"), context);
    EXPECT_TRUE(weight_result.is_success());
    double weight_lbs = std::stod(std::any_cast<std::string>(weight_result.value));

    // Stage 2: Convert units
    auto convert_weight = std::make_unique<transform::AdvancedNumericTransformation>();
    YAML::Node convert_config;
    convert_config["operation"] = "UnitConversion";
    convert_config["from_unit"] = "lb";
    convert_config["to_unit"] = "kg";
    convert_weight->configure(convert_config);

    auto weight_kg_result = convert_weight->transform_detailed(weight_lbs, context);
    EXPECT_TRUE(weight_kg_result.is_success());
    double weight_kg = std::any_cast<double>(weight_kg_result.value);

    // Stage 3: Calculate BMI
    double height_inches = 70.0;
    double height_m = height_inches * 0.0254;
    double bmi = weight_kg / (height_m * height_m);

    // Stage 4: Categorize BMI
    auto categorize_bmi = std::make_unique<transform::AdvancedConditionalTransformation>();
    YAML::Node bmi_config;
    // ... (configuration as in previous test)

    // Stage 5: Map gender to concept
    auto vocab_transform = std::make_unique<transform::VocabularyTransformation>(*vocab_service_);
    YAML::Node vocab_config;
    vocab_config["vocabulary_name"] = "Gender";
    vocab_config["default_concept_id"] = 0;
    vocab_transform->configure(vocab_config);

    auto gender_result = vocab_transform->transform_detailed(
        input_record.getField("gender"), context);
    EXPECT_TRUE(gender_result.is_success());
    int gender_concept_id = std::any_cast<int>(gender_result.value);
    EXPECT_EQ(gender_concept_id, 8507); // Male concept ID

    // Verify complete transformation
    EXPECT_GT(weight_kg, 0);
    EXPECT_GT(bmi, 0);
    EXPECT_LT(bmi, 50); // Reasonable BMI range
}

private:
    void SetupTestConceptHierarchy() {
        // This would normally be loaded from the database
        // For testing, we'll use mock data
    }

} // namespace omop::test