# Service integration tests
add_executable(service_integration_tests
    test_etl_service_integration.cpp
    test_service_manager_integration.cpp
    test_end_to_end_pipeline.cpp
)

target_link_libraries(service_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
        Threads::Threads
)

target_include_directories(service_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

# Add to CTest
add_test(
    NAME service_integration_tests
    COMMAND service_integration_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
)

set_tests_properties(service_integration_tests PROPERTIES
    TIMEOUT 300  # 5 minutes timeout for end-to-end tests
    LABELS "integration;service"
)