# Updated Service integration tests CMakeLists.txt
set(SERVICE_INTEGRATION_TEST_SOURCES
    test_service_integration_updated.cpp
)

add_executable(service_integration_tests_updated ${SERVICE_INTEGRATION_TEST_SOURCES})

target_link_libraries(service_integration_tests_updated
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        integration_test_helpers
        gtest
        gtest_main
        gmock
        nlohmann_json::nlohmann_json
        Threads::Threads
)

target_include_directories(service_integration_tests_updated
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
        ${CMAKE_CURRENT_SOURCE_DIR}/../test_helpers
)

# Add compile definitions for test configuration
target_compile_definitions(service_integration_tests_updated
    PRIVATE
        TEST_DATA_DIR="${CMAKE_CURRENT_SOURCE_DIR}/../test_data"
        TEST_OUTPUT_DIR="${CMAKE_CURRENT_BINARY_DIR}/test_output"
        OMOP_TEST_INTEGRATION
        OMOP_SERVICE_TESTS
)

add_test(
    NAME service_integration_tests_updated
    COMMAND service_integration_tests_updated
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

set_tests_properties(service_integration_tests_updated PROPERTIES
    TIMEOUT 900  # 15 minutes for service tests
    LABELS "integration;service;updated"
    ENVIRONMENT "TEST_DATA_DIR=${CMAKE_CURRENT_SOURCE_DIR}/../test_data;TEST_OUTPUT_DIR=${CMAKE_CURRENT_BINARY_DIR}/test_output"
)

# Create test output directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_output)