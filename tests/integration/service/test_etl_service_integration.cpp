// tests/integration/service/test_etl_service_integration.cpp
// Tests the high-level ETL service orchestration functionality
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "core/pipeline.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <thread>
#include <chrono>

namespace omop::service::test {

class ETLServiceIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize configuration
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: source_db
                username: test_user
                password: test_pass

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm
                username: test_user
                password: test_pass

            etl:
                batch_size: 1000
                error_threshold: 0.05
                enable_validation: true

            tables:
                person:
                    source_table: patients
                    target_table: person
                    transformations:
                        - source_column: patient_id
                          target_column: person_id
                          type: direct
                        - source_column: gender
                          target_column: gender_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary: gender
        )");

        // Create pipeline manager
        pipeline_manager_ = std::make_shared<core::PipelineManager>(2);

        // Create ETL service
        etl_service_ = std::make_unique<ETLService>(config_, pipeline_manager_);
    }

    void TearDown() override {
        // Clean up any remaining jobs
        auto jobs = etl_service_->get_all_job_results();
        for (const auto& job : jobs) {
            etl_service_->cancel_job(job.job_id);
        }
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::unique_ptr<ETLService> etl_service_;
};

// Tests basic job creation and execution
TEST_F(ETLServiceIntegrationTest, CreateAndExecuteSimpleJob) {
    ETLJobRequest request;
    request.name = "Test Person ETL";
    request.description = "Test ETL job for person table";
    request.source_table = "patients";
    request.target_table = "person";
    request.extractor_type = "database";
    request.loader_type = "omop_database";

    request.extractor_config["table"] = "patients";
    request.extractor_config["batch_size"] = 100;

    request.loader_config["table"] = "person";
    request.loader_config["schema"] = "cdm";

    // Create job
    auto job_id = etl_service_->create_job(request);
    ASSERT_FALSE(job_id.empty());

    // Wait for job completion
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Get job result
    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->job_id, job_id);
    EXPECT_NE(result->status, core::JobStatus::Failed);
}

// Tests parallel execution of multiple table ETLs
TEST_F(ETLServiceIntegrationTest, ParallelTableExecution) {
    // Run all configured tables
    auto job_map = etl_service_->run_all_tables(true);

    ASSERT_FALSE(job_map.empty());

    // Wait for all jobs to complete
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Check all job results
    for (const auto& [table, job_id] : job_map) {
        auto result = etl_service_->get_job_result(job_id);
        ASSERT_TRUE(result.has_value());
        EXPECT_NE(result->status, core::JobStatus::Failed)
            << "Job for table " << table << " failed";
    }
}

// Tests job cancellation functionality
TEST_F(ETLServiceIntegrationTest, CancelRunningJob) {
    ETLJobRequest request;
    request.name = "Long Running Job";
    request.source_table = "large_table";
    request.target_table = "target_table";
    request.pipeline_config.batch_size = 10; // Small batches to make it run longer

    auto job_id = etl_service_->create_job(request);

    // Wait briefly then cancel
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    bool cancelled = etl_service_->cancel_job(job_id);
    EXPECT_TRUE(cancelled);

    // Verify job was cancelled
    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->status, core::JobStatus::Cancelled);
}

// Tests job pause and resume functionality
TEST_F(ETLServiceIntegrationTest, PauseAndResumeJob) {
    ETLJobRequest request;
    request.name = "Pausable Job";
    request.source_table = "patients";
    request.target_table = "person";

    auto job_id = etl_service_->create_job(request);

    // Wait briefly then pause
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    bool paused = etl_service_->pause_job(job_id);
    EXPECT_TRUE(paused);

    // Verify job is paused
    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->status, core::JobStatus::Paused);

    // Resume job
    bool resumed = etl_service_->resume_job(job_id);
    EXPECT_TRUE(resumed);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Verify job completed
    result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->status, core::JobStatus::Completed);
}

// Tests configuration validation
TEST_F(ETLServiceIntegrationTest, ValidateTableConfiguration) {
    // Valid table
    auto errors = etl_service_->validate_table_config("person");
    EXPECT_TRUE(errors.empty());

    // Invalid table
    errors = etl_service_->validate_table_config("non_existent_table");
    EXPECT_FALSE(errors.empty());
}

// Tests job scheduling functionality
TEST_F(ETLServiceIntegrationTest, ScheduleJobExecution) {
    ETLJobRequest request;
    request.name = "Scheduled Job";
    request.source_table = "patients";
    request.target_table = "person";
    request.scheduled_time = std::chrono::system_clock::now() + std::chrono::seconds(2);

    auto job_id = etl_service_->schedule_job(request);
    ASSERT_FALSE(job_id.empty());

    // Job should not be running yet
    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_NE(result->status, core::JobStatus::Running);

    // Wait for scheduled time
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Job should be running or completed
    result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_TRUE(result->status == core::JobStatus::Running ||
                result->status == core::JobStatus::Completed);
}

// Tests error handling and recovery
TEST_F(ETLServiceIntegrationTest, HandleTransformationErrors) {
    ETLJobRequest request;
    request.name = "Error Handling Test";
    request.source_table = "invalid_data";
    request.target_table = "person";
    request.pipeline_config.stop_on_error = false;
    request.pipeline_config.error_threshold = 0.5; // Allow up to 50% errors

    auto job_id = etl_service_->create_job(request);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(2));

    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());

    // Should complete with some errors
    EXPECT_EQ(result->status, core::JobStatus::Completed);
    EXPECT_GT(result->error_records, 0);
    EXPECT_FALSE(result->errors.empty());
}

// Tests callback functionality
TEST_F(ETLServiceIntegrationTest, JobCompletionCallbacks) {
    bool callback_invoked = false;
    std::string callback_job_id;
    ETLJobResult callback_result;

    etl_service_->set_completion_callback(
        [&](const std::string& job_id, const ETLJobResult& result) {
            callback_invoked = true;
            callback_job_id = job_id;
            callback_result = result;
        });

    ETLJobRequest request;
    request.name = "Callback Test";
    request.source_table = "patients";
    request.target_table = "person";

    auto job_id = etl_service_->create_job(request);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(2));

    EXPECT_TRUE(callback_invoked);
    EXPECT_EQ(callback_job_id, job_id);
    EXPECT_EQ(callback_result.status, core::JobStatus::Completed);
}

// Tests service statistics collection
TEST_F(ETLServiceIntegrationTest, CollectServiceStatistics) {
    // Run a few jobs
    for (int i = 0; i < 3; ++i) {
        ETLJobRequest request;
        request.name = "Stats Test " + std::to_string(i);
        request.source_table = "patients";
        request.target_table = "person";
        etl_service_->create_job(request);
    }

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(3));

    auto stats = etl_service_->get_statistics();

    EXPECT_EQ(std::any_cast<size_t>(stats["total_jobs_created"]), 3);
    EXPECT_GT(std::any_cast<size_t>(stats["total_jobs_completed"]), 0);
}

} // namespace omop::service::test

// tests/integration/service/test_etl_scheduler_integration.cpp
// Tests the ETL scheduler functionality for recurring and scheduled jobs
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include <thread>
#include <chrono>

namespace omop::service::test {

class ETLSchedulerIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: test_db

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm
        )");

        pipeline_manager_ = std::make_shared<core::PipelineManager>(2);
        etl_service_ = std::make_shared<ETLService>(config_, pipeline_manager_);
        scheduler_ = std::make_unique<ETLScheduler>(etl_service_);
    }

    void TearDown() override {
        scheduler_->stop();
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    std::unique_ptr<ETLScheduler> scheduler_;
};

// Tests one-time scheduled job execution
TEST_F(ETLSchedulerIntegrationTest, ScheduleOnceExecution) {
    scheduler_->start();

    ETLJobRequest request;
    request.name = "One-time Job";
    request.source_table = "patients";
    request.target_table = "person";

    ETLScheduler::Schedule schedule;
    schedule.type = ETLScheduler::ScheduleType::Once;
    schedule.start_time = std::chrono::system_clock::now() + std::chrono::seconds(1);

    scheduler_->schedule_job("once_job", request, schedule);

    // Verify job is scheduled
    auto scheduled_jobs = scheduler_->get_scheduled_jobs();
    EXPECT_EQ(scheduled_jobs.size(), 1);

    // Wait for execution
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Verify job was executed
    auto jobs = etl_service_->get_all_job_results();
    EXPECT_FALSE(jobs.empty());
}

// Tests daily recurring job scheduling
TEST_F(ETLSchedulerIntegrationTest, ScheduleDailyRecurringJob) {
    scheduler_->start();

    ETLJobRequest request;
    request.name = "Daily Job";
    request.source_table = "patients";
    request.target_table = "person";

    ETLScheduler::Schedule schedule;
    schedule.type = ETLScheduler::ScheduleType::Daily;
    schedule.start_time = std::chrono::system_clock::now();
    schedule.interval = std::chrono::minutes(1); // For testing, use 1 minute instead of daily

    scheduler_->schedule_job("daily_job", request, schedule);

    // Get next run time
    auto next_run = scheduler_->get_next_run_time("daily_job");
    ASSERT_TRUE(next_run.has_value());

    // Verify next run is approximately 1 minute from now
    auto duration = *next_run - std::chrono::system_clock::now();
    auto minutes = std::chrono::duration_cast<std::chrono::minutes>(duration).count();
    EXPECT_GE(minutes, 0);
    EXPECT_LE(minutes, 2);
}

// Tests cancellation of scheduled jobs
TEST_F(ETLSchedulerIntegrationTest, CancelScheduledJob) {
    scheduler_->start();

    ETLJobRequest request;
    request.name = "Cancellable Job";
    request.source_table = "patients";
    request.target_table = "person";

    ETLScheduler::Schedule schedule;
    schedule.type = ETLScheduler::ScheduleType::Once;
    schedule.start_time = std::chrono::system_clock::now() + std::chrono::seconds(5);

    scheduler_->schedule_job("cancel_job", request, schedule);

    // Cancel before execution
    bool cancelled = scheduler_->cancel_scheduled_job("cancel_job");
    EXPECT_TRUE(cancelled);

    // Verify job is no longer scheduled
    auto scheduled_jobs = scheduler_->get_scheduled_jobs();
    EXPECT_EQ(scheduled_jobs.size(), 0);
}

} // namespace omop::service::test

// tests/integration/service/test_etl_monitor_integration.cpp
// Tests the ETL monitoring and alerting functionality
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include <thread>

namespace omop::service::test {

class ETLMonitorIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: test_db

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm
        )");

        pipeline_manager_ = std::make_shared<core::PipelineManager>(2);
        etl_service_ = std::make_shared<ETLService>(config_, pipeline_manager_);
        monitor_ = std::make_unique<ETLMonitor>(etl_service_);
    }

    void TearDown() override {
        monitor_->stop();
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    std::unique_ptr<ETLMonitor> monitor_;
};

// Tests alert generation for failed jobs
TEST_F(ETLMonitorIntegrationTest, GenerateJobFailureAlert) {
    std::vector<ETLMonitor::Alert> received_alerts;

    monitor_->set_alert_callback([&](const ETLMonitor::Alert& alert) {
        received_alerts.push_back(alert);
    });

    monitor_->start();

    // Create a job that will fail
    ETLJobRequest request;
    request.name = "Failing Job";
    request.source_table = "non_existent_table";
    request.target_table = "person";

    auto job_id = etl_service_->create_job(request);

    // Wait for monitoring to detect failure
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Check alerts were generated
    EXPECT_FALSE(received_alerts.empty());

    bool found_failure_alert = false;
    for (const auto& alert : received_alerts) {
        if (alert.type == ETLMonitor::AlertType::JobFailed &&
            alert.job_id == job_id) {
            found_failure_alert = true;
            break;
        }
    }

    EXPECT_TRUE(found_failure_alert);
}

// Tests high error rate detection
TEST_F(ETLMonitorIntegrationTest, DetectHighErrorRate) {
    monitor_->set_thresholds(0.1, 100.0, 1024); // 10% error threshold
    monitor_->start();

    // Create a job with high error rate
    ETLJobRequest request;
    request.name = "High Error Job";
    request.source_table = "problematic_data";
    request.target_table = "person";
    request.pipeline_config.stop_on_error = false;

    auto job_id = etl_service_->create_job(request);

    // Wait for monitoring
    std::this_thread::sleep_for(std::chrono::seconds(3));

    auto alerts = monitor_->get_alerts();

    bool found_error_rate_alert = false;
    for (const auto& alert : alerts) {
        if (alert.type == ETLMonitor::AlertType::HighErrorRate) {
            found_error_rate_alert = true;
            break;
        }
    }

    EXPECT_TRUE(found_error_rate_alert);
}

// Tests performance monitoring
TEST_F(ETLMonitorIntegrationTest, MonitorJobPerformance) {
    monitor_->set_thresholds(0.05, 1000.0, 1024); // 1000 records/sec threshold
    monitor_->start();

    ETLJobRequest request;
    request.name = "Performance Test";
    request.source_table = "large_table";
    request.target_table = "person";
    request.pipeline_config.batch_size = 100;

    auto job_id = etl_service_->create_job(request);

    // Wait for some processing
    std::this_thread::sleep_for(std::chrono::seconds(2));

    auto metrics = monitor_->get_job_metrics(job_id);

    EXPECT_TRUE(metrics.find("records_per_second") != metrics.end());
    EXPECT_TRUE(metrics.find("average_batch_time") != metrics.end());
    EXPECT_TRUE(metrics.find("memory_usage_mb") != metrics.end());
}

// Tests system metrics collection
TEST_F(ETLMonitorIntegrationTest, CollectSystemMetrics) {
    monitor_->start();

    // Run a few jobs to generate system activity
    for (int i = 0; i < 3; ++i) {
        ETLJobRequest request;
        request.name = "System Load " + std::to_string(i);
        request.source_table = "patients";
        request.target_table = "person";
        etl_service_->create_job(request);
    }

    std::this_thread::sleep_for(std::chrono::seconds(2));

    auto system_metrics = monitor_->get_system_metrics();

    EXPECT_TRUE(system_metrics.find("total_active_jobs") != system_metrics.end());
    EXPECT_TRUE(system_metrics.find("cpu_usage_percent") != system_metrics.end());
    EXPECT_TRUE(system_metrics.find("memory_usage_mb") != system_metrics.end());
    EXPECT_TRUE(system_metrics.find("total_records_processed") != system_metrics.end());
}

} // namespace omop::service::test