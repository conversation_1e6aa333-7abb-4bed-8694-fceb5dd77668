/**
 * Integration test for ServiceManager coordinating multiple ETL services
 */

#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <thread>
#include <chrono>

namespace omop::test::integration {

class ServiceManagerIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Initialize configuration
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            etl:
                batch_size: 100
                parallel_jobs: 2
                error_threshold: 0.05

            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: source_clinical
                username: test_user
                password: test_pass

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm
                username: test_user
                password: test_pass

            mappings:
                person:
                    source_table: patients
                    target_table: person
                    transformations:
                        - source_column: patient_id
                          target_column: person_id
                          type: direct
                        - source_column: gender
                          target_column: gender_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: Gender

                visit_occurrence:
                    source_table: encounters
                    target_table: visit_occurrence
                    transformations:
                        - source_column: encounter_id
                          target_column: visit_occurrence_id
                          type: direct
        )");

        // Initialize pipeline manager
        pipeline_manager_ = std::make_shared<core::PipelineManager>(2);

        // Initialize ETL service
        etl_service_ = std::make_shared<service::ETLService>(config_, pipeline_manager_);
    }

    void TearDown() override {
        pipeline_manager_->shutdown(true);
        IntegrationTestBase::TearDown();
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
};

TEST_F(ServiceManagerIntegrationTest, TestMultipleTableETLConcurrent) {
    // Test concurrent ETL of multiple tables
    auto job_map = etl_service_->run_all_tables(true);

    ASSERT_FALSE(job_map.empty());
    EXPECT_GE(job_map.size(), 2);

    // Wait for all jobs to complete
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Check job results
    for (const auto& [table_name, job_id] : job_map) {
        auto result = etl_service_->get_job_result(job_id);
        ASSERT_TRUE(result.has_value());
        EXPECT_EQ(result->status, core::JobStatus::Completed);
        EXPECT_GT(result->total_records, 0);
        EXPECT_LE(result->error_records, result->total_records * 0.05);
    }
}

TEST_F(ServiceManagerIntegrationTest, TestETLSchedulerIntegration) {
    // Test scheduled ETL execution
    service::ETLScheduler scheduler(etl_service_);
    scheduler.start();

    // Create scheduled job
    service::ETLJobRequest request;
    request.name = "Test Scheduled ETL";
    request.source_table = "patients";
    request.target_table = "person";
    request.scheduled_time = std::chrono::system_clock::now() + std::chrono::seconds(2);

    service::ETLScheduler::Schedule schedule;
    schedule.type = service::ETLScheduler::ScheduleType::Once;
    schedule.start_time = *request.scheduled_time;

    scheduler.schedule_job("test_scheduled_job", request, schedule);

    // Wait for scheduled execution
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Verify job was executed
    auto scheduled_jobs = scheduler.get_scheduled_jobs();
    EXPECT_EQ(scheduled_jobs.size(), 1);

    scheduler.stop();
}

TEST_F(ServiceManagerIntegrationTest, TestETLMonitoringAlerts) {
    // Test monitoring and alerting functionality
    service::ETLMonitor monitor(etl_service_);

    std::vector<service::ETLMonitor::Alert> captured_alerts;
    monitor.set_alert_callback([&captured_alerts](const service::ETLMonitor::Alert& alert) {
        captured_alerts.push_back(alert);
    });

    monitor.set_thresholds(0.01, 50.0, 512); // Strict thresholds for testing
    monitor.start();

    // Create job with high error rate
    service::ETLJobRequest request;
    request.name = "Test High Error Rate";
    request.source_table = "invalid_table";
    request.target_table = "person";

    auto job_id = etl_service_->create_job(request);

    // Wait for monitoring to detect issues
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Check alerts were generated
    EXPECT_GT(captured_alerts.size(), 0);

    bool found_error_alert = false;
    for (const auto& alert : captured_alerts) {
        if (alert.type == service::ETLMonitor::AlertType::JobFailed ||
            alert.type == service::ETLMonitor::AlertType::HighErrorRate) {
            found_error_alert = true;
            break;
        }
    }

    EXPECT_TRUE(found_error_alert);

    monitor.stop();
}

TEST_F(ServiceManagerIntegrationTest, TestServiceRecoveryAfterFailure) {
    // Test service recovery after job failures
    service::ETLJobRequest request;
    request.name = "Test Recovery";
    request.source_table = "patients";
    request.target_table = "person";
    request.pipeline_config.stop_on_error = false;

    // Simulate failure by using invalid configuration
    request.extractor_config["filepath"] = "/nonexistent/file.csv";

    auto job_id = etl_service_->create_job(request);

    // Wait for job to fail
    std::this_thread::sleep_for(std::chrono::seconds(2));

    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->status, core::JobStatus::Failed);

    // Create a valid job after failure
    request.extractor_config.clear();
    request.extractor_config["table"] = "patients";

    auto recovery_job_id = etl_service_->create_job(request);

    // Wait for recovery job
    std::this_thread::sleep_for(std::chrono::seconds(2));

    auto recovery_result = etl_service_->get_job_result(recovery_job_id);
    ASSERT_TRUE(recovery_result.has_value());
    EXPECT_EQ(recovery_result->status, core::JobStatus::Completed);
}

} // namespace omop::test::integration