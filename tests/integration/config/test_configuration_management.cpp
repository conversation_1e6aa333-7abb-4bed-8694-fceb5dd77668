// tests/integration/config/test_configuration_management.cpp
// Tests dynamic configuration updates and management across the ETL pipeline
#include <gtest/gtest.h>
#include "common/configuration.h"
#include "service/etl_service.h"
#include "core/job_manager.h"
#include "core/job_scheduler.h"
#include "core/workflow_engine.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

namespace omop::config::test {

class ConfigurationManagementTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration directory
        config_dir_ = "/tmp/omop_config_test";
        std::filesystem::create_directories(config_dir_);

        // Create initial configuration file
        createConfigFile("main_config.yaml", getBaseConfig());

        config_manager_ = std::make_shared<common::ConfigurationManager>();
        config_watcher_ = std::make_unique<ConfigurationWatcher>(config_manager_);
    }

    void TearDown() override {
        config_watcher_->stop();
        std::filesystem::remove_all(config_dir_);
    }

    void createConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(config_dir_ + "/" + filename);
        file << content;
        file.close();
    }

    std::string getBaseConfig() {
        return R"(
            version: 1.0
            environment: test

            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: source_db
                pool_size: 10

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm
                pool_size: 20

            etl:
                batch_size: 1000
                max_parallel_jobs: 4
                error_threshold: 0.05

            logging:
                level: info
                file: /tmp/omop_etl.log
                max_size_mb: 100
                retention_days: 7
        )";
    }

    std::string config_dir_;
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::unique_ptr<ConfigurationWatcher> config_watcher_;
};

// Tests hot configuration reloading
TEST_F(ConfigurationManagementTest, HotConfigurationReload) {
    // Load initial configuration
    config_manager_->load_config(config_dir_ + "/main_config.yaml");

    // Start configuration watcher
    config_watcher_->start(config_dir_);

    // Verify initial values
    EXPECT_EQ(config_manager_->get_value_or<int>("etl.batch_size", 0), 1000);

    // Update configuration file
    auto updated_config = getBaseConfig();
    std::regex batch_size_regex("batch_size: 1000");
    updated_config = std::regex_replace(updated_config, batch_size_regex, "batch_size: 2000");

    createConfigFile("main_config.yaml", updated_config);

    // Wait for watcher to detect change
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Verify configuration was reloaded
    EXPECT_EQ(config_manager_->get_value_or<int>("etl.batch_size", 0), 2000);
}

// Tests configuration validation
TEST_F(ConfigurationManagementTest, ConfigurationValidation) {
    // Test valid configuration
    config_manager_->load_config(config_dir_ + "/main_config.yaml");

    try {
        config_manager_->validate_config();
        SUCCEED();
    } catch (const common::ConfigurationException& e) {
        FAIL() << "Valid configuration failed validation: " << e.what();
    }

    // Test invalid configuration
    std::string invalid_config = R"(
        source_db:
            type: invalid_db_type
            host:
            port: -1

        etl:
            batch_size: -100
            error_threshold: 2.0
    )";

    createConfigFile("invalid_config.yaml", invalid_config);

    auto invalid_manager = std::make_shared<common::ConfigurationManager>();
    invalid_manager->load_config(config_dir_ + "/invalid_config.yaml");

    EXPECT_THROW(invalid_manager->validate_config(), common::ConfigurationException);
}

// Tests environment-specific configuration
TEST_F(ConfigurationManagementTest, EnvironmentSpecificConfig) {
    // Create environment-specific configs
    createConfigFile("config.base.yaml", getBaseConfig());

    createConfigFile("config.dev.yaml", R"(
        environment: development
        source_db:
            host: dev.db.local
            database: dev_source

        etl:
            batch_size: 100
            max_parallel_jobs: 1

        logging:
            level: debug
    )");

    createConfigFile("config.prod.yaml", R"(
        environment: production
        source_db:
            host: prod.db.local
            database: prod_source
            ssl_mode: require

        etl:
            batch_size: 10000
            max_parallel_jobs: 16

        logging:
            level: warning
    )");

    // Load development configuration
    std::setenv("OMOP_ENV", "dev", 1);
    config_manager_->load_config(config_dir_ + "/config.${OMOP_ENV}.yaml");

    EXPECT_EQ(config_manager_->get_value_or<std::string>("environment", ""), "development");
    EXPECT_EQ(config_manager_->get_value_or<std::string>("source_db.host", ""), "dev.db.local");
    EXPECT_EQ(config_manager_->get_value_or<int>("etl.batch_size", 0), 100);

    // Load production configuration
    std::setenv("OMOP_ENV", "prod", 1);
    auto prod_manager = std::make_shared<common::ConfigurationManager>();
    prod_manager->load_config(config_dir_ + "/config.${OMOP_ENV}.yaml");

    EXPECT_EQ(prod_manager->get_value_or<std::string>("environment", ""), "production");
    EXPECT_EQ(prod_manager->get_value_or<int>("etl.batch_size", 0), 10000);
}

// Tests configuration encryption for sensitive data
TEST_F(ConfigurationManagementTest, SecureConfigurationHandling) {
    ConfigurationEncryptor encryptor("test_key_12345678");

    // Create configuration with encrypted values
    std::string secure_config = R"(
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: db_user
            password: !encrypted |
                ENC[AES256,data:1234567890abcdef,iv:fedcba0987654321]

        api_keys:
            vocabulary_service: !encrypted |
                ENC[AES256,data:abcdef1234567890,iv:1234567890abcdef]
    )";

    createConfigFile("secure_config.yaml", secure_config);

    // Load with decryption
    config_manager_->set_encryptor(&encryptor);
    config_manager_->load_config(config_dir_ + "/secure_config.yaml");

    // Verify encrypted values are decrypted
    auto password = config_manager_->get_value_or<std::string>("source_db.password", "");
    EXPECT_FALSE(password.empty());
    EXPECT_FALSE(password.find("ENC[") != std::string::npos);
}

} // namespace omop::config::test
