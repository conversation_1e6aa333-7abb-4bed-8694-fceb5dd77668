
// tests/integration/config/test_configuration_migration.cpp
// Tests configuration version migration and backward compatibility
#include <gtest/gtest.h>
#include "common/configuration.h"

namespace omop::config::test {

class ConfigurationMigrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        migrator_ = std::make_unique<ConfigurationMigrator>();
        config_dir_ = "/tmp/omop_config_migration";
        std::filesystem::create_directories(config_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(config_dir_);
    }

    std::unique_ptr<ConfigurationMigrator> migrator_;
    std::string config_dir_;
};

// Tests migration from v1 to v2 configuration format
TEST_F(ConfigurationMigrationTest, MigrateV1ToV2) {
    // Create v1 configuration
    std::string v1_config = R"(
        version: 1.0
        database:
            source:
                type: postgresql
                connection_string: postgresql://user:pass@localhost/source_db
            target:
                type: postgresql
                connection_string: postgresql://user:pass@localhost/omop_cdm

        etl_config:
            batch_size: 1000
            threads: 4
    )";

    std::ofstream file(config_dir_ + "/config_v1.yaml");
    file << v1_config;
    file.close();

    // Migrate to v2
    auto migrated_config = migrator_->migrate(config_dir_ + "/config_v1.yaml", "2.0");

    // Verify migration
    EXPECT_EQ(migrated_config->get_value_or<std::string>("version", ""), "2.0");

    // Check structure changes
    EXPECT_TRUE(migrated_config->get_value("source_db.type").has_value());
    EXPECT_TRUE(migrated_config->get_value("target_db.type").has_value());
    EXPECT_FALSE(migrated_config->get_value("database").has_value());

    // Check renamed fields
    EXPECT_EQ(migrated_config->get_value_or<int>("etl.batch_size", 0), 1000);
    EXPECT_EQ(migrated_config->get_value_or<int>("etl.max_parallel_jobs", 0), 4);
}

// Tests backward compatibility
TEST_F(ConfigurationMigrationTest, BackwardCompatibility) {
    // Create modern v2 configuration
    std::string v2_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        etl:
            batch_size: 2000
            max_parallel_jobs: 8
    )";

    // Test compatibility layer for v1 API
    auto config = std::make_shared<common::ConfigurationManager>();
    config->load_config_from_string(v2_config);

    // Enable compatibility mode
    config->enable_compatibility_mode("1.0");

    // Access using old v1 paths
    auto conn_string = config->get_value_or<std::string>(
        "database.source.connection_string", "");
    EXPECT_FALSE(conn_string.empty());

    auto batch_size = config->get_value_or<int>("etl_config.batch_size", 0);
    EXPECT_EQ(batch_size, 2000);
}

} // namespace omop::config::test

// tests/integration/performance/test_load_performance.cpp
// Tests system performance under various load conditions
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/test_data_generator.h"
#include "common/utilities.h"

namespace omop::performance::test {

class LoadPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: perf_test_db
                pool_size: 50

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm_perf
                pool_size: 50

            etl:
                batch_size: 5000
                max_parallel_jobs: 16
                queue_size: 100000
                memory_limit_mb: 4096

            performance:
                enable_profiling: true
                metrics_interval_seconds: 1
                detailed_timing: true
        )");

        pipeline_manager_ = std::make_shared<core::PipelineManager>(16);
        etl_service_ = std::make_shared<service::ETLService>(config_, pipeline_manager_);

        // Generate large test dataset
        generateLargeDataset();
    }

    void generateLargeDataset() {
        // TODO: Implement TestDataGenerator class
        // TestDataGenerator generator;
        // generator.generatePatientData("patients", 100000);
        // generator.generateVisitData("visits", 500000);
        // generator.generateConditionData("conditions", 1000000);
        // generator.generateMeasurementData("measurements", 2000000);
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
};

// Tests throughput under high load
TEST_F(LoadPerformanceTest, HighThroughputProcessing) {
    utils::PerformanceUtils::Timer timer;
    utils::PerformanceUtils::MemoryTracker memory_tracker;

    // Configure for maximum throughput
    ETLJobRequest request;
    request.name = "High Throughput Test";
    request.source_table = "measurements";
    request.target_table = "measurement";
    request.pipeline_config.batch_size = 10000;
    request.pipeline_config.max_parallel_batches = 8;
    request.pipeline_config.validate_records = false; // Disable validation for speed

    auto job_id = etl_service_->create_job(request);

    // Monitor performance metrics
    std::vector<double> throughput_samples;
    std::thread monitor_thread([&]() {
        while (true) {
            auto result = etl_service_->get_job_result(job_id);
            if (!result.has_value() || result->status != core::JobStatus::Running) {
                break;
            }

            double throughput = result->processed_records / timer.elapsed_seconds();
            throughput_samples.push_back(throughput);

            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    });

    // Wait for completion
    while (true) {
        auto result = etl_service_->get_job_result(job_id);
        if (result.has_value() && result->status == core::JobStatus::Completed) {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    monitor_thread.join();

    // Analyze performance
    auto result = etl_service_->get_job_result(job_id).value();
    double avg_throughput = result.total_records / timer.elapsed_seconds();

    EXPECT_GT(avg_throughput, 10000) << "Throughput below 10k records/sec";
    EXPECT_EQ(result.error_records, 0) << "Errors during high throughput test";

    // Check memory usage
    size_t peak_memory = memory_tracker.peak_usage();
    EXPECT_LT(peak_memory, 4096 * 1024 * 1024) << "Memory usage exceeded limit";

    // Calculate percentiles
    std::sort(throughput_samples.begin(), throughput_samples.end());
    if (!throughput_samples.empty()) {
        double p50 = throughput_samples[throughput_samples.size() / 2];
        double p95 = throughput_samples[throughput_samples.size() * 95 / 100];
        double p99 = throughput_samples[throughput_samples.size() * 99 / 100];

        std::cout << "Throughput - P50: " << p50
                  << ", P95: " << p95
                  << ", P99: " << p99 << " records/sec\n";
    }
}

// Tests concurrent job execution performance
TEST_F(LoadPerformanceTest, ConcurrentJobExecution) {
    const int num_concurrent_jobs = 10;
    std::vector<std::string> job_ids;
    std::vector<std::thread> job_threads;

    utils::PerformanceUtils::Timer timer;

    // Start multiple jobs concurrently
    for (int i = 0; i < num_concurrent_jobs; ++i) {
        job_threads.emplace_back([&, i]() {
            ETLJobRequest request;
            request.name = "Concurrent Job " + std::to_string(i);
            request.source_table = (i % 2 == 0) ? "patients" : "visits";
            request.target_table = (i % 2 == 0) ? "person" : "visit_occurrence";
            request.pipeline_config.batch_size = 1000;

            auto job_id = etl_service_->create_job(request);

            std::lock_guard<std::mutex> lock(job_ids_mutex_);
            job_ids.push_back(job_id);
        });
    }

    // Wait for all threads to submit jobs
    for (auto& thread : job_threads) {
        thread.join();
    }

    // Wait for all jobs to complete
    bool all_completed = false;
    while (!all_completed) {
        all_completed = true;
        for (const auto& job_id : job_ids) {
            auto result = etl_service_->get_job_result(job_id);
            if (!result.has_value() ||
                (result->status != core::JobStatus::Completed &&
                 result->status != core::JobStatus::Failed)) {
                all_completed = false;
                break;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    double total_time = timer.elapsed_seconds();

    // Verify all jobs completed successfully
    size_t total_records = 0;
    for (const auto& job_id : job_ids) {
        auto result = etl_service_->get_job_result(job_id).value();
        EXPECT_EQ(result.status, core::JobStatus::Completed);
        total_records += result.total_records;
    }

    double aggregate_throughput = total_records / total_time;
    EXPECT_GT(aggregate_throughput, 20000)
        << "Aggregate throughput below 20k records/sec";

    std::cout << "Concurrent execution - Total records: " << total_records
              << ", Time: " << total_time << "s"
              << ", Aggregate throughput: " << aggregate_throughput << " records/sec\n";
}

// Tests memory usage under different batch sizes
TEST_F(LoadPerformanceTest, MemoryUsageOptimization) {
    std::vector<size_t> batch_sizes = {100, 1000, 5000, 10000, 20000};
    std::map<size_t, utils::PerformanceUtils::MemoryTracker::Stats> memory_stats;

    for (size_t batch_size : batch_sizes) {
        utils::PerformanceUtils::MemoryTracker tracker;

        ETLJobRequest request;
        request.name = "Memory Test BS=" + std::to_string(batch_size);
        request.source_table = "conditions";
        request.target_table = "condition_occurrence";
        request.pipeline_config.batch_size = batch_size;
        request.pipeline_config.max_parallel_batches = 4;

        auto job_id = etl_service_->create_job(request);

        // Monitor memory during execution
        std::thread monitor([&]() {
            while (true) {
                auto result = etl_service_->get_job_result(job_id);
                if (!result.has_value() || result->status != core::JobStatus::Running) {
                    break;
                }
                tracker.update();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        });

        // Wait for completion
        while (true) {
            auto result = etl_service_->get_job_result(job_id);
            if (result.has_value() && result->status == core::JobStatus::Completed) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        monitor.join();

        memory_stats[batch_size] = tracker.get_stats();
    }

    // Verify memory usage scales appropriately
    for (size_t i = 1; i < batch_sizes.size(); ++i) {
        auto prev_size = batch_sizes[i-1];
        auto curr_size = batch_sizes[i];

        auto prev_memory = memory_stats[prev_size].peak_usage_mb;
        auto curr_memory = memory_stats[curr_size].peak_usage_mb;

        // Memory should not scale linearly with batch size
        double memory_ratio = static_cast<double>(curr_memory) / prev_memory;
        double size_ratio = static_cast<double>(curr_size) / prev_size;

        EXPECT_LT(memory_ratio, size_ratio * 0.8)
            << "Memory scaling inefficient between batch sizes "
            << prev_size << " and " << curr_size;
    }
}

private:
    mutable std::mutex job_ids_mutex_;
};

} // namespace omop::performance::test

// tests/integration/quality/test_data_profiling.cpp
// Tests data profiling and quality assessment functionality
#include <gtest/gtest.h>
#include "transform/vocabulary_service.h"
#include "test_helpers/test_data_generator.h"

namespace omop::quality::test {

class DataProfilingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize test database with diverse data patterns
        // TODO: Implement TestDataGenerator class
        // TestDataGenerator generator;
        // generator.generateDiversePatientData("test_patients", 10000, {
        //     {"age_distribution", "normal"},
        //     {"gender_distribution", "balanced"},
        //     {"missing_rate", 0.05},
        //     {"outlier_rate", 0.02}
        // });
    }

    DatabaseFixture db_fixture_;
};

// Tests comprehensive data profiling
TEST_F(DataProfilingTest, DISABLED_ComprehensiveDataProfiling) {
    // TODO: Implement DataProfiler class
    // DataProfiler profiler;
    // auto profile = profiler.profileTable("test_patients", {
    //     "patient_id", "birth_year", "gender", "race", "ethnicity", "zip_code"
    // });
    
    /*
    // Verify basic statistics
    // EXPECT_EQ(profile.row_count, 10000);
    // EXPECT_GT(profile.columns.size(), 0);

    // Check numeric column profiling
    // auto birth_year_profile = profile.getColumnProfile("birth_year");
    // ASSERT_TRUE(birth_year_profile.has_value());

    // EXPECT_TRUE(birth_year_profile->is_numeric);
    // EXPECT_GT(birth_year_profile->numeric_stats.mean, 1950);
    // EXPECT_LT(birth_year_profile->numeric_stats.mean, 2000);
    // EXPECT_GT(birth_year_profile->numeric_stats.std_dev, 0);
    // EXPECT_EQ(birth_year_profile->numeric_stats.null_count,
    //           static_cast<size_t>(10000 * 0.05 * 0.2)); // Approximate

    // Check categorical column profiling
    // auto gender_profile = profile.getColumnProfile("gender");
    // ASSERT_TRUE(gender_profile.has_value());

    // EXPECT_FALSE(gender_profile->is_numeric);
    // EXPECT_EQ(gender_profile->distinct_count, 3); // M, F, NULL
    // EXPECT_TRUE(gender_profile->value_frequencies.contains("M"));
    // EXPECT_TRUE(gender_profile->value_frequencies.contains("F"));

    // Verify balanced distribution
    // double male_ratio = gender_profile->value_frequencies["M"] /
    //                    static_cast<double>(profile.row_count);
    // EXPECT_NEAR(male_ratio, 0.5, 0.1);
    */
}

// Tests pattern detection in data
TEST_F(DataProfilingTest, DISABLED_DataPatternDetection) {
    // TODO: Implement PatternDetector class
    /*
    PatternDetector detector;

    // Test zip code pattern detection
    auto zip_patterns = detector.detectPatterns("test_patients", "zip_code");

    EXPECT_FALSE(zip_patterns.empty());

    bool found_zip_pattern = false;
    for (const auto& pattern : zip_patterns) {
        if (pattern.regex == "\\d{5}" || pattern.regex == "\\d{5}-\\d{4}") {
            found_zip_pattern = true;
            EXPECT_GT(pattern.match_percentage, 0.9);
        }
    }
    EXPECT_TRUE(found_zip_pattern);

    // Test date pattern detection
    // TODO: Implement TestDataGenerator class
    // TestDataGenerator generator;
    // generator.generateTableWithDates("date_test", 1000);

    auto date_patterns = detector.detectPatterns("date_test", "event_date");

    bool found_date_pattern = false;
    for (const auto& pattern : date_patterns) {
        if (pattern.regex.find("\\d{4}-\\d{2}-\\d{2}") != std::string::npos) {
            found_date_pattern = true;
        }
    }
    EXPECT_TRUE(found_date_pattern);
    */
    GTEST_SKIP() << "PatternDetector class not implemented yet";
}

// Tests outlier detection
TEST_F(DataProfilingTest, DISABLED_OutlierDetection) {
    // TODO: Implement OutlierDetector class
    /*
    OutlierDetector detector;

    // Detect outliers in birth year
    auto outliers = detector.detectOutliers("test_patients", "birth_year", {
        {"method", "z_score"},
        {"threshold", 3.0}
    });

    EXPECT_FALSE(outliers.empty());
    EXPECT_NEAR(outliers.size() / 10000.0, 0.02, 0.01); // ~2% outliers

    // Verify outliers are actually extreme values
    for (const auto& outlier : outliers) {
        int birth_year = std::any_cast<int>(outlier.value);
        EXPECT_TRUE(birth_year < 1900 || birth_year > 2020);
    }

    // Test IQR method
    auto iqr_outliers = detector.detectOutliers("test_patients", "birth_year", {
        {"method", "iqr"},
        {"iqr_multiplier", 1.5}
    });

    EXPECT_GE(iqr_outliers.size(), outliers.size());
    */
    GTEST_SKIP() << "OutlierDetector class not implemented yet";
}

// Tests data quality rule evaluation
TEST_F(DataProfilingTest, DISABLED_DataQualityRuleEvaluation) {
    // TODO: Implement DataQualityRuleEngine class
    /*
    DataQualityRuleEngine engine;

    // Define quality rules
    engine.addRule("birth_year_range", {
        {"type", "range"},
        {"column", "birth_year"},
        {"min", 1900},
        {"max", 2023}
    });

    engine.addRule("gender_values", {
        {"type", "in_list"},
        {"column", "gender"},
        {"values", std::vector<std::string>{"M", "F", "O", "U"}}
    });

    engine.addRule("patient_id_unique", {
        {"type", "unique"},
        {"column", "patient_id"}
    });

    engine.addRule("required_fields", {
        {"type", "not_null"},
        {"columns", std::vector<std::string>{"patient_id", "birth_year"}}
    });

    // Evaluate rules
    auto results = engine.evaluateTable("test_patients");

    EXPECT_GE(results.rules_evaluated, 4);
    EXPECT_GT(results.rules_passed, 0);

    // Check specific rule results
    auto birth_year_result = results.getRuleResult("birth_year_range");
    ASSERT_TRUE(birth_year_result.has_value());
    EXPECT_GT(birth_year_result->pass_rate, 0.95);

    auto unique_result = results.getRuleResult("patient_id_unique");
    ASSERT_TRUE(unique_result.has_value());
    EXPECT_EQ(unique_result->pass_rate, 1.0);
    */
    GTEST_SKIP() << "DataQualityRuleEngine class not implemented yet";
}

// Tests data quality scoring
TEST_F(DataProfilingTest, DISABLED_DataQualityScoring) {
    // TODO: Implement DataQualityScorer class
    /*
    DataQualityScorer scorer;

    // Configure scoring dimensions
    scorer.configureDimension("completeness", {
        {"weight", 0.3},
        {"required_fields", std::vector<std::string>{"patient_id", "birth_year", "gender"}}
    });

    scorer.configureDimension("validity", {
        {"weight", 0.3},
        {"rules", std::vector<std::string>{"birth_year_range", "gender_values"}}
    });

    scorer.configureDimension("consistency", {
        {"weight", 0.2},
        {"cross_field_rules", std::vector<std::string>{"birth_death_consistency"}}
    });

    scorer.configureDimension("accuracy", {
        {"weight", 0.2},
        {"reference_checks", std::vector<std::string>{"zip_code_validation"}}
    });

    // Calculate quality scores
    auto scores = scorer.calculateScores("test_patients");

    EXPECT_GT(scores.overall_score, 0.8);
    EXPECT_GT(scores.dimension_scores["completeness"], 0.9);
    EXPECT_GT(scores.dimension_scores["validity"], 0.9);

    // Get detailed quality report
    auto report = scorer.generateReport("test_patients");

    EXPECT_FALSE(report.issues.empty());
    EXPECT_FALSE(report.recommendations.empty());
    */
    GTEST_SKIP() << "DataQualityScorer class not implemented yet";
}

} // namespace omop::quality::test

// tests/integration/workflow/test_job_dependencies.cpp
// Tests job dependency management and complex workflow execution
#include <gtest/gtest.h>
#include "core/job_manager.h"
#include "service/etl_service.h"

namespace omop::workflow::test {

class JobDependencyTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(getTestConfig());

        job_manager_ = std::make_shared<core::JobManager>(config_,
            common::Logger::get("test"));
        scheduler_ = std::make_unique<core::JobScheduler>(job_manager_, core::SchedulingStrategy::PRIORITY);
    }

    std::string getTestConfig() {
        return R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: test_db

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm

            workflow:
                enable_dependencies: true
                max_retry_attempts: 3
                dependency_check_interval_ms: 100
        )";
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::JobManager> job_manager_;
    std::unique_ptr<core::JobScheduler> scheduler_;
};

// Tests simple linear job dependency chain
TEST_F(JobDependencyTest, LinearDependencyChain) {
    scheduler_->start();
    job_manager_->start();

    // Create job chain: A -> B -> C
    core::JobConfig job_a;
    job_a.job_name = "Job A";
    job_a.pipeline_config_path = "config/job_a.yaml";

    core::JobConfig job_b;
    job_b.job_name = "Job B";
    job_b.pipeline_config_path = "config/job_b.yaml";

    core::JobConfig job_c;
    job_c.job_name = "Job C";
    job_c.pipeline_config_path = "config/job_c.yaml";

    auto job_a_id = scheduler_->submitJob(job_a);
    auto job_b_id = scheduler_->submitJob(job_b, core::JobPriority::NORMAL, {job_a_id});
    auto job_c_id = scheduler_->submitJob(job_c, core::JobPriority::NORMAL, {job_b_id});

    // Wait for all jobs to complete using proper polling approach
    auto timeout = std::chrono::steady_clock::now() + std::chrono::milliseconds(30000);
    while (std::chrono::steady_clock::now() < timeout) {
        auto job = job_manager_->getJob(job_c_id);
        if (job && (job->getStatus() == core::JobStatus::Completed || job->getStatus() == core::JobStatus::Failed)) {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Verify execution order
    auto job_a_result = job_manager_->getJob(job_a_id);
    auto job_b_result = job_manager_->getJob(job_b_id);
    auto job_c_result = job_manager_->getJob(job_c_id);

    ASSERT_TRUE(job_a_result);
    ASSERT_TRUE(job_b_result);
    ASSERT_TRUE(job_c_result);

    EXPECT_EQ(job_a_result->getStatus(), core::JobStatus::Completed);
    EXPECT_EQ(job_b_result->getStatus(), core::JobStatus::Completed);
    EXPECT_EQ(job_c_result->getStatus(), core::JobStatus::Completed);

    // Verify timing
    EXPECT_LT(job_a_result->getEndTime(), job_b_result->getStartTime());
    EXPECT_LT(job_b_result->getEndTime(), job_c_result->getStartTime());
}

// Tests complex DAG (Directed Acyclic Graph) dependencies
TEST_F(JobDependencyTest, ComplexDAGDependencies) {
    scheduler_->start();
    job_manager_->start();

    // Create complex DAG:
    //     A
    //    / \
    //   B   C
    //   |\ /|
    //   | X |
    //   |/ \|
    //   D   E
    //    \ /
    //     F

    std::map<std::string, std::string> job_ids;

    // Create jobs
    auto create_job = [&](const std::string& name,
                         const std::vector<std::string>& deps = {}) {
        core::JobConfig config;
        config.job_name = name;
        config.pipeline_config_path = "config/job_" + name + ".yaml";

        std::vector<std::string> dep_ids;
        for (const auto& dep : deps) {
            dep_ids.push_back(job_ids[dep]);
        }

        job_ids[name] = scheduler_->submitJob(config, core::JobPriority::NORMAL, dep_ids);
    };

    create_job("A");
    create_job("B", {"A"});
    create_job("C", {"A"});
    create_job("D", {"B", "C"});
    create_job("E", {"B", "C"});
    create_job("F", {"D", "E"});

    // Wait for final job using proper polling approach  
    auto timeout2 = std::chrono::steady_clock::now() + std::chrono::milliseconds(60000);
    while (std::chrono::steady_clock::now() < timeout2) {
        auto job = job_manager_->getJob(job_ids["F"]);
        if (job && (job->getStatus() == core::JobStatus::Completed || job->getStatus() == core::JobStatus::Failed)) {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Verify all jobs completed
    for (const auto& [name, id] : job_ids) {
        auto job = job_manager_->getJob(id);
        ASSERT_TRUE(job) << "Job " << name << " not found";
        EXPECT_EQ(job->getStatus(), core::JobStatus::Completed)
            << "Job " << name << " not completed";
    }
}

// Tests conditional workflow execution
TEST_F(JobDependencyTest, ConditionalWorkflowExecution) {
    core::WorkflowEngine workflow(job_manager_, scheduler_);

    // Define conditional workflow
    workflow.defineWorkflow("conditional_etl", R"(
        steps:
          - name: check_data_quality
            job: quality_check

          - name: process_clean_data
            job: etl_clean
            when: ${check_data_quality.output.quality_score} > 0.9

          - name: process_dirty_data
            job: etl_with_cleaning
            when: ${check_data_quality.output.quality_score} <= 0.9

          - name: validate_results
            job: validation
            depends_on: [process_clean_data, process_dirty_data]
    )");

    // Execute workflow
    auto workflow_id = workflow.execute("conditional_etl");

    // Wait for completion
    workflow.waitForCompletion(workflow_id, std::chrono::seconds(60));

    // Check execution path
    auto execution = workflow.getExecution(workflow_id);
    ASSERT_TRUE(execution.has_value());

    // Verify only one processing path was taken
    bool clean_executed = execution->stepExecuted("process_clean_data");
    bool dirty_executed = execution->stepExecuted("process_dirty_data");

    EXPECT_TRUE(clean_executed != dirty_executed)
        << "Both or neither processing paths executed";

    // Verify validation ran
    EXPECT_TRUE(execution->stepExecuted("validate_results"));
}

// Tests job failure handling and retry
TEST_F(JobDependencyTest, FailureHandlingAndRetry) {
    scheduler_->start();
    job_manager_->start();

    // Create job that fails first time
    core::JobConfig failing_job;
    failing_job.job_name = "Failing Job";
    failing_job.pipeline_config_path = "config/failing_job.yaml";
    failing_job.max_retries = 2;
    failing_job.retry_delay = std::chrono::seconds(1);

    auto job_id = scheduler_->submitJob(failing_job);

    // Create dependent job
    core::JobConfig dependent_job;
    dependent_job.job_name = "Dependent Job";
    dependent_job.pipeline_config_path = "config/dependent_job.yaml";

    auto dependent_id = scheduler_->submitJob(dependent_job,
        core::JobPriority::NORMAL, {job_id});

    // Wait for retry to complete
    std::this_thread::sleep_for(std::chrono::seconds(5));

    auto job = job_manager_->getJob(job_id);
    ASSERT_TRUE(job);

    // Verify retry occurred
    EXPECT_GT(job->getRetryCount(), 0);

    // If job eventually succeeded, dependent should run
    if (job->getStatus() == core::JobStatus::Completed) {
        auto dependent = job_manager_->getJob(dependent_id);
        ASSERT_TRUE(dependent);
        EXPECT_EQ(dependent->getStatus(), core::JobStatus::Completed);
    }
}

} // namespace omop::workflow::test