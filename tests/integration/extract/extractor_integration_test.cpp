/**
 * @file extractor_integration_test.cpp
 * @brief Generic integration tests for cross-extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains integration tests that test how different extractors
 * work together and interact with the factory system. Specific extractor
 * functionality is tested in the specialized test files:
 * - test_csv_extractor_integration.cpp
 * - test_json_extractor_integration.cpp
 * - test_database_extractor_integration.cpp
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_factory.h"
#include "extract/extract.h"
#include "extract/extract_utils.h"
#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <format>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Invoke;

// Test fixture for generic extractor integration tests
class ExtractorIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        test_dir_ = std::filesystem::temp_directory_path() / "omop_etl_test";
        std::filesystem::create_directories(test_dir_);

        // Initialize extractor factory
        initialize_extractors();
    }

    void TearDown() override {
        // Clean up test files
        if (std::filesystem::exists(test_dir_)) {
            std::filesystem::remove_all(test_dir_);
        }
    }

    // Helper method to create test CSV file
    std::string createTestCsvFile(const std::string& filename,
                                 const std::vector<std::string>& headers,
                                 const std::vector<std::vector<std::string>>& data) {
        std::string filepath = (test_dir_ / filename).string();
        std::ofstream file(filepath);

        // Write headers
        for (size_t i = 0; i < headers.size(); ++i) {
            file << headers[i];
            if (i < headers.size() - 1) file << ",";
        }
        file << "\n";

        // Write data
        for (const auto& row : data) {
            for (size_t i = 0; i < row.size(); ++i) {
                file << row[i];
                if (i < row.size() - 1) file << ",";
            }
            file << "\n";
        }

        file.close();
        return filepath;
    }

    // Helper method to create test JSON file
    std::string createTestJsonFile(const std::string& filename,
                                  const std::string& json_content) {
        std::string filepath = (test_dir_ / filename).string();
        std::ofstream file(filepath);
        file << json_content;
        file.close();
        return filepath;
    }

    // Helper method to create test JSONL file
    std::string createTestJsonlFile(const std::string& filename,
                                   const std::vector<std::string>& json_lines) {
        std::string filepath = (test_dir_ / filename).string();
        std::ofstream file(filepath);
        for (const auto& line : json_lines) {
            file << line << "\n";
        }
        file.close();
        return filepath;
    }

    // Helper function for auto-detection (replaces missing createExtractorAuto)
    std::unique_ptr<omop::core::IExtractor> createExtractorAuto(const std::string& filepath) {
        // Use the extract_utils function
        return omop::extract::create_extractor_auto(filepath);
    }

    std::filesystem::path test_dir_;
    
    // Helper method to detect source type from file extension
    std::string detectSourceType(const std::string& filepath) {
        if (filepath.ends_with(".csv")) return "csv";
        if (filepath.ends_with(".json")) return "json";
        if (filepath.ends_with(".jsonl")) return "jsonl";
        throw omop::common::ConfigurationException(
            std::format("Unknown file type for: '{}'", filepath));
    }
};

// Test factory-based extractor creation for different file types
TEST_F(ExtractorIntegrationTest, FactoryBasedExtractorCreation) {
    // Create test files of different types
    std::vector<std::string> headers = {"id", "name", "age"};
    std::vector<std::vector<std::string>> data = {
        {"1", "John Doe", "30"},
        {"2", "Jane Smith", "25"}
    };

    std::string csv_file = createTestCsvFile("test.csv", headers, data);
    std::string json_file = createTestJsonFile("test.json", R"([
        {"id": 1, "name": "John Doe", "age": 30},
        {"id": 2, "name": "Jane Smith", "age": 25}
    ])");
    std::string jsonl_file = createTestJsonlFile("test.jsonl", {
        R"({"id": 1, "name": "John Doe", "age": 30})",
        R"({"id": 2, "name": "Jane Smith", "age": 25})"
    });

    // Test factory creation for CSV
    auto csv_extractor = ExtractorFactoryRegistry::create("csv");
    EXPECT_NE(csv_extractor, nullptr);

    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = csv_file;
    ProcessingContext context;

    EXPECT_NO_THROW(csv_extractor->initialize(csv_config, context));

    // Test factory creation for JSON
    auto json_extractor = ExtractorFactoryRegistry::create("json");
    EXPECT_NE(json_extractor, nullptr);

    std::unordered_map<std::string, std::any> json_config;
    json_config["filepath"] = json_file;

    EXPECT_NO_THROW(json_extractor->initialize(json_config, context));

    // Test factory creation for JSONL
    auto jsonl_extractor = ExtractorFactoryRegistry::create("jsonl");
    EXPECT_NE(jsonl_extractor, nullptr);

    std::unordered_map<std::string, std::any> jsonl_config;
    jsonl_config["filepath"] = jsonl_file;

    EXPECT_NO_THROW(jsonl_extractor->initialize(jsonl_config, context));
}

// Test auto-detection of extractor type based on file extension
TEST_F(ExtractorIntegrationTest, AutoDetectExtractorType) {
    // Create test files with different extensions
    std::vector<std::string> headers = {"id", "name"};
    std::vector<std::vector<std::string>> data = {
        {"1", "John Doe"},
        {"2", "Jane Smith"}
    };

    std::string csv_file = createTestCsvFile("data.csv", headers, data);
    std::string json_file = createTestJsonFile("data.json", R"([
        {"id": 1, "name": "John Doe"},
        {"id": 2, "name": "Jane Smith"}
    ])");
    std::string jsonl_file = createTestJsonlFile("data.jsonl", {
        R"({"id": 1, "name": "John Doe"})",
        R"({"id": 2, "name": "Jane Smith"})"
    });

    // Test auto-detection for CSV (using our helper instead of undefined function)
    auto csv_extractor = createExtractorAuto(csv_file);
    EXPECT_NE(csv_extractor, nullptr);
    
    ProcessingContext context;
    csv_extractor->initialize({{"filepath", csv_file}}, context);
    EXPECT_EQ(csv_extractor->get_type(), "csv");

    // Test auto-detection for JSON
    auto json_extractor = createExtractorAuto(json_file);
    EXPECT_NE(json_extractor, nullptr);
    json_extractor->initialize({{"filepath", json_file}}, context);
    EXPECT_EQ(json_extractor->get_type(), "json");

    // Test auto-detection for JSONL
    auto jsonl_extractor = createExtractorAuto(jsonl_file);
    EXPECT_NE(jsonl_extractor, nullptr);
    jsonl_extractor->initialize({{"filepath", jsonl_file}}, context);
    EXPECT_EQ(jsonl_extractor->get_type(), "jsonl");
}

// Test cross-extractor data consistency (same data in different formats)
TEST_F(ExtractorIntegrationTest, CrossExtractorDataConsistency) {
    // Create the same data in different formats
    std::vector<std::string> headers = {"id", "name", "age", "city"};
    std::vector<std::vector<std::string>> data = {
        {"1", "John Doe", "30", "London"},
        {"2", "Jane Smith", "25", "Paris"}
    };

    std::string csv_file = createTestCsvFile("consistency.csv", headers, data);
    std::string json_file = createTestJsonFile("consistency.json", R"([
        {"id": 1, "name": "John Doe", "age": 30, "city": "London"},
        {"id": 2, "name": "Jane Smith", "age": 25, "city": "Paris"}
    ])");

    // Extract from CSV
    auto csv_extractor = createExtractorAuto(csv_file);
    ProcessingContext context;
    
    std::unordered_map<std::string, std::any> csv_config{{"filepath", csv_file}};
    csv_extractor->initialize(csv_config, context);

    std::vector<Record> csv_records;
    while (csv_extractor->has_more_data()) {
        auto batch = csv_extractor->extract_batch(10, context);
        for (size_t i = 0; i < batch.size(); ++i) {
            csv_records.push_back(batch.getRecord(i));
        }
    }

    // Extract from JSON
    auto json_extractor = createExtractorAuto(json_file);
    
    std::unordered_map<std::string, std::any> json_config{{"filepath", json_file}};
    json_extractor->initialize(json_config, context);

    std::vector<Record> json_records;
    while (json_extractor->has_more_data()) {
        auto batch = json_extractor->extract_batch(10, context);
        for (size_t i = 0; i < batch.size(); ++i) {
            json_records.push_back(batch.getRecord(i));
        }
    }

    // Verify we got the same number of records
    EXPECT_EQ(csv_records.size(), json_records.size());

    // Verify data consistency
    for (size_t i = 0; i < csv_records.size(); ++i) {
        const auto& csv_record = csv_records[i];
        const auto& json_record = json_records[i];

        // CSV returns strings, JSON returns typed values
        // Convert CSV string to int for comparison
        std::string csv_id_str = std::any_cast<std::string>(csv_record.getField("id"));
        int csv_id = std::stoi(csv_id_str);
        int json_id = std::any_cast<int>(json_record.getField("id"));
        EXPECT_EQ(csv_id, json_id);
        
        // Names should match directly as strings
        EXPECT_EQ(std::any_cast<std::string>(csv_record.getField("name")), 
                  std::any_cast<std::string>(json_record.getField("name")));
        
        // Ages need conversion
        std::string csv_age_str = std::any_cast<std::string>(csv_record.getField("age"));
        int csv_age = std::stoi(csv_age_str);
        int json_age = std::any_cast<int>(json_record.getField("age"));
        EXPECT_EQ(csv_age, json_age);
        
        // Cities should match as strings
        EXPECT_EQ(std::any_cast<std::string>(csv_record.getField("city")), 
                  std::any_cast<std::string>(json_record.getField("city")));
    }
}

// Test factory error handling for unknown extractor types
TEST_F(ExtractorIntegrationTest, FactoryErrorHandling) {
    // Test creation of unknown extractor type
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"),
                 omop::common::ConfigurationException);

    // Test auto-detection with unknown file extension
    std::string unknown_file = (test_dir_ / "data.unknown").string();
    std::ofstream file(unknown_file);
    file << "some data";
    file.close();

    EXPECT_THROW(createExtractorAuto(unknown_file),
                 omop::common::ConfigurationException);
}

// Test extractor statistics consistency across different types
TEST_F(ExtractorIntegrationTest, ExtractorStatisticsConsistency) {
    // Create test files with same number of records
    std::vector<std::string> headers = {"id", "name"};
    std::vector<std::vector<std::string>> data = {
        {"1", "John Doe"},
        {"2", "Jane Smith"},
        {"3", "Bob Johnson"}
    };

    std::string csv_file = createTestCsvFile("stats.csv", headers, data);
    std::string json_file = createTestJsonFile("stats.json", R"([
        {"id": 1, "name": "John Doe"},
        {"id": 2, "name": "Jane Smith"},
        {"id": 3, "name": "Bob Johnson"}
    ])");

    // Extract from CSV and get statistics
    auto csv_extractor = createExtractorAuto(csv_file);
    ProcessingContext context;
    
    std::unordered_map<std::string, std::any> csv_config{{"filepath", csv_file}};
    csv_extractor->initialize(csv_config, context);

    while (csv_extractor->has_more_data()) {
        csv_extractor->extract_batch(10, context);
    }

    auto csv_stats = csv_extractor->get_statistics();

    // Extract from JSON and get statistics
    auto json_extractor = createExtractorAuto(json_file);
    
    std::unordered_map<std::string, std::any> json_config{{"filepath", json_file}};
    json_extractor->initialize(json_config, context);

    while (json_extractor->has_more_data()) {
        json_extractor->extract_batch(10, context);
    }

    auto json_stats = json_extractor->get_statistics();

    // Verify statistics consistency
    // The exact statistics fields depend on implementation
    // Check for common fields that should exist
    if (csv_stats.find("extracted_count") != csv_stats.end() &&
        json_stats.find("extracted_count") != json_stats.end()) {
        EXPECT_EQ(std::any_cast<size_t>(csv_stats["extracted_count"]), 
                  std::any_cast<size_t>(json_stats["extracted_count"]));
        EXPECT_EQ(std::any_cast<size_t>(csv_stats["extracted_count"]), 3);
    }
    
    if (csv_stats.find("error_count") != csv_stats.end()) {
        EXPECT_EQ(std::any_cast<size_t>(csv_stats["error_count"]), 0);
    }
    
    // Verify both extractors provide statistics
    EXPECT_FALSE(csv_stats.empty());
    EXPECT_FALSE(json_stats.empty());
}

// Test extractor initialization with different configuration patterns
TEST_F(ExtractorIntegrationTest, ExtractorConfigurationPatterns) {
    // Create test file
    std::vector<std::string> headers = {"id", "name"};
    std::vector<std::vector<std::string>> data = {
        {"1", "John Doe"},
        {"2", "Jane Smith"}
    };

    std::string csv_file = createTestCsvFile("config.csv", headers, data);

    // Test different configuration patterns
    std::vector<std::unordered_map<std::string, std::any>> configs = {
        {{"filepath", csv_file}},  // Minimal config
        {{"filepath", csv_file}, {"batch_size", size_t(5)}},  // With batch size
        {{"filepath", csv_file}, {"has_header", true}, {"delimiter", ','}},  // With options
    };

    for (const auto& config : configs) {
        auto extractor = ExtractorFactoryRegistry::create("csv");
        ProcessingContext context;

        EXPECT_NO_THROW(extractor->initialize(config, context));

        // Verify extraction works
        auto batch = extractor->extract_batch(10, context);
        EXPECT_EQ(batch.size(), 2);
    }
}

// Test memory-efficient batch processing for large datasets
TEST_F(ExtractorIntegrationTest, LargeDatasetBatchProcessing) {
    // Create a large CSV file for testing batch processing
    std::string filepath = (test_dir_ / "large_dataset.csv").string();
    std::ofstream file(filepath);
    
    // Write header
    file << "id,name,value,timestamp\n";
    
    // Write many records
    for (int i = 1; i <= 10000; ++i) {
        file << i << ",User" << i << "," << (i * 1.5) << ",2023-01-01T00:00:00Z\n";
    }
    file.close();

    auto extractor = createExtractorAuto(filepath);
    ProcessingContext context;
    
    std::unordered_map<std::string, std::any> config{{"filepath", filepath}};
    extractor->initialize(config, context);

    size_t total_records = 0;
    size_t batch_count = 0;
    
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(500, context);  // Process in smaller batches
        total_records += batch.size();
        batch_count++;
        
        // Verify batch size doesn't exceed requested size
        EXPECT_LE(batch.size(), 500);
    }

    EXPECT_EQ(total_records, 10000);
    EXPECT_GT(batch_count, 1);  // Should have processed multiple batches
}

// Test extractor lifecycle management
TEST_F(ExtractorIntegrationTest, ExtractorLifecycleManagement) {
    // Create test file
    std::vector<std::string> headers = {"id", "name"};
    std::vector<std::vector<std::string>> data = {
        {"1", "John Doe"},
        {"2", "Jane Smith"}
    };

    std::string csv_file = createTestCsvFile("lifecycle.csv", headers, data);

    // Test complete lifecycle
    auto extractor = ExtractorFactoryRegistry::create("csv");
    ProcessingContext context;

    // Initialize
    std::unordered_map<std::string, std::any> config{{"filepath", csv_file}};
    EXPECT_NO_THROW(extractor->initialize(config, context));

    // Extract
    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 2);

    // Verify no more data
    EXPECT_FALSE(extractor->has_more_data());

    // Finalize
    EXPECT_NO_THROW(extractor->finalize(context));
}

// Test extractor type registration and discovery
TEST_F(ExtractorIntegrationTest, ExtractorTypeRegistration) {
    // Get registered extractor types
    auto registered_types = ExtractorFactoryRegistry::get_registered_types();

    // Verify essential extractor types are registered
    EXPECT_NE(std::find(registered_types.begin(), registered_types.end(), "csv"),
              registered_types.end());
    EXPECT_NE(std::find(registered_types.begin(), registered_types.end(), "json"),
              registered_types.end());
    EXPECT_NE(std::find(registered_types.begin(), registered_types.end(), "jsonl"),
              registered_types.end());

    // Verify we can create all registered types
    for (const auto& type : registered_types) {
        auto extractor = ExtractorFactoryRegistry::create(type);
        EXPECT_NE(extractor, nullptr);
        EXPECT_EQ(extractor->get_type(), type);
    }
}