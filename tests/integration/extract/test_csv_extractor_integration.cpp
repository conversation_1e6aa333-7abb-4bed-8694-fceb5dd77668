/**
 * @file test_csv_extractor_integration.cpp
 * @brief Integration tests for CSV extractor functionality including UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "extract/csv_extractor.h"
#include "extract/extractor_factory.h"
#include "common/exceptions.h"
#include "common/utilities.h"
#include "core/interfaces.h"
#include "test_helpers/integration_test_base.h"
#include <filesystem>
#include <fstream>
#include <chrono>
#include <locale>
#include <string>
#include <iomanip>
#include <zlib.h>
#include <thread>

namespace omop::extract::test {

namespace fs = std::filesystem;

class CsvExtractorIntegrationTest : public ::testing::Test {
protected:
    void SetUp() {
        // Create temporary directory for test files
        test_dir_ = fs::temp_directory_path() / ("csv_test_" + omop::common::CryptoUtils::generate_uuid());
        fs::create_directories(test_dir_);
        
        // Set UK locale for proper date/time formatting in UK-specific tests
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (...) {
            // Fallback if UK locale not available
            try {
                std::locale::global(std::locale("C.UTF-8"));
            } catch (...) {
                // Use C locale as last resort
            }
        }
    }

    void TearDown() {
        // Clean up test files
        if (fs::exists(test_dir_)) {
            fs::remove_all(test_dir_);
        }
    }

    // Helper to create CSV file
    void createCsvFile(const std::string& filename, const std::vector<std::string>& lines) {
        std::ofstream file(test_dir_ / filename);
        for (const auto& line : lines) {
            file << line << "\n";
        }
        file.close();
    }

    // Helper to create large CSV file
    void createLargeCsvFile(const std::string& filename, size_t num_records) {
        std::ofstream file(test_dir_ / filename);

        // Write header
        file << "id,name,age,salary,department,hire_date,active\n";

        // Write records
        for (size_t i = 0; i < num_records; ++i) {
            file << i << ","
                 << "Employee_" << i << ","
                 << (20 + i % 40) << ","
                 << (50000 + (i * 1000) % 50000) << ","
                 << "Dept_" << (i % 5) << ","
                 << "2020-01-" << std::setfill('0') << std::setw(2) << (1 + i % 28) << ","
                 << (i % 2 == 0 ? "true" : "false") << "\n";
        }
        
        file.close();
    }

    // Creates UK NHS patient data CSV file with British formatting
    std::string createUKPatientCSV() {
        fs::path filepath = test_dir_ / "uk_patients.csv";
        std::ofstream file(filepath);
        
        // Write header with UK terminology
        file << "patient_id,nhs_number,surname,forename,date_of_birth,postcode,telephone,gender,ethnicity\n";
        
        // Write UK localized patient data
        file << "1,**********,Smith,James,15/03/1985,SW1A 1AA,020 7946 0958,Male,White British\n";
        file << "2,**********,Johnson,Emma,22/07/1990,M1 1AA,0161 496 0000,Female,White English\n";
        file << "3,**********,Williams,Oliver,08/11/1978,CF10 3AT,029 2087 7500,Male,Welsh\n";
        file << "4,**********,Brown,Sophie,30/01/1995,EH1 1YZ,0131 496 0000,Female,British Pakistani\n";
        file << "5,**********,Davis,Harry,12/09/1982,B33 8TH,0121 496 0000,Male,British Caribbean\n";
        file << "6,**********,Miller,Charlotte,18/05/1988,G1 1XQ,0141 496 0000,Female,Scottish\n";
        file << "7,**********,Wilson,George,03/12/1975,BT1 5GS,028 9024 0000,Male,Northern Irish\n";
        file << "8,**********,Moore,Amelia,25/04/1992,LS1 1AA,0113 496 0000,Female,British Indian\n";
        
        file.close();
        return filepath.string();
    }

    // Creates UK clinical data CSV with NHS coding
    std::string createUKClinicalCSV() {
        fs::path filepath = test_dir_ / "uk_clinical_data.csv";
        std::ofstream file(filepath);
        
        // Header with UK clinical terminology
        file << "episode_id,patient_id,admission_date,discharge_date,primary_diagnosis,read_code,icd10_code,consultant,ward\n";
        
        // UK clinical data with Read codes and ICD-10
        file << "1,1,15/01/2024,17/01/2024,Essential Hypertension,G20..00,I10,Dr. Smith,Cardiology Ward\n";
        file << "2,2,20/02/2024,20/02/2024,Upper Respiratory Infection,H05..00,J06.9,Dr. Patel,A&E\n";
        file << "3,3,10/03/2024,,Type 2 Diabetes Mellitus,C10E.00,E11.9,Dr. Jones,Diabetes Clinic\n";
        file << "4,4,05/04/2024,08/04/2024,Acute Myocardial Infarction,G30..00,I21.9,Dr. Williams,CCU\n";
        file << "5,5,12/05/2024,12/05/2024,Day Case Surgery,7L1B.00,Z51.1,Mr. Thompson,Day Surgery\n";
        file << "6,6,18/06/2024,,Mental Health Assessment,Eu20.00,F32.9,Dr. Ahmed,Mental Health Unit\n";
        file << "7,7,22/07/2024,,Physiotherapy Session,8HkE.00,Z51.8,Ms. Brown,Physiotherapy\n";
        file << "8,8,30/08/2024,,Antenatal Appointment,62...00,Z34.9,Dr. Wilson,Maternity Unit\n";
        
        file.close();
        return filepath.string();
    }

    fs::path test_dir_;
};

// Tests basic CSV extraction functionality
TEST_F(CsvExtractorIntegrationTest, BasicCsvExtraction) {
    std::vector<std::string> csv_data = {
        "id,name,age,city",
        "1,John Doe,30,New York",
        "2,Jane Smith,25,Los Angeles",
        "3,Bob Johnson,35,Chicago"
    };
    createCsvFile("basic.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "basic.csv").string()}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records
    auto batch = extractor->extract_batch(10, context);

    EXPECT_EQ(batch.size(), 3);
    EXPECT_FALSE(extractor->has_more_data());

    // Verify first record - handle type inference
    const auto& first = batch.getRecord(0);
    // ID is inferred as integer
    EXPECT_EQ(first.getFieldAs<long long>("id"), 1);
    // Name is string
    EXPECT_EQ(first.getFieldAs<std::string>("name"), "John Doe");
    // Age is inferred as integer  
    EXPECT_EQ(first.getFieldAs<long long>("age"), 30);
    // City is string
    EXPECT_EQ(first.getFieldAs<std::string>("city"), "New York");
}

// Tests CSV extraction with custom delimiters (pipe-separated values)
TEST_F(CsvExtractorIntegrationTest, CustomDelimiterCsv) {
    // Create pipe-delimited file
    std::vector<std::string> csv_data = {
        "id|name|email",
        "1|Alice Brown|<EMAIL>",
        "2|Charlie Davis|<EMAIL>"
    };
    createCsvFile("pipe_delimited.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "pipe_delimited.csv").string()},
        {"delimiter", '|'}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 2);
    EXPECT_EQ(batch.getRecord(0).getFieldAs<std::string>("name"), "Alice Brown");
}

// Tests CSV extraction with quoted fields containing special characters
TEST_F(CsvExtractorIntegrationTest, QuotedFieldsCsv) {
    std::vector<std::string> csv_data = {
        "id,name,notes",
        "1,John,\"Contains \"\"special\"\" characters\"",
        "2,Jane,\"Multi-line\nfield content\"",
        "3,Bob,\"Field with, comma\"",
        "4,Alice,Regular field"
    };
    createCsvFile("quoted.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "quoted.csv").string()}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    EXPECT_EQ(batch.size(), 4);
    
    // Test proper handling of quoted fields with escaped quotes
    EXPECT_EQ(batch.getRecord(0).getFieldAs<std::string>("notes"), "Contains \"special\" characters");
    
    // Test multi-line field content (if supported)
    std::string multiLineField = batch.getRecord(1).getFieldAs<std::string>("notes");
    EXPECT_NE(multiLineField.find('\n'), std::string::npos);
    
    // Test field with embedded comma
    EXPECT_EQ(batch.getRecord(2).getFieldAs<std::string>("notes"), "Field with, comma");
    
    // Test regular unquoted field
    EXPECT_EQ(batch.getRecord(3).getFieldAs<std::string>("notes"), "Regular field");
}

// Tests CSV extraction without header row using explicit column names
TEST_F(CsvExtractorIntegrationTest, NoHeaderCsv) {
    std::vector<std::string> csv_data = {
        "1,John Doe,Engineer",
        "2,Jane Smith,Manager",
        "3,Bob Johnson,Analyst"
    };
    createCsvFile("no_header.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "no_header.csv").string()},
        {"has_header", false},
        {"column_names", std::vector<std::string>{"id", "name", "title"}}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    EXPECT_EQ(batch.getRecord(0).getFieldAs<std::string>("title"), "Engineer");
}

// Tests batch extraction with large CSV files to verify memory management
TEST_F(CsvExtractorIntegrationTest, BatchExtraction) {
    createLargeCsvFile("large.csv", 100);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "large.csv").string()},
        {"batch_size", size_t(10)}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);
    
    size_t total_records = 0;
    size_t batch_count = 0;
    
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_records += batch.size();
        batch_count++;
        EXPECT_LE(batch.size(), 10);
    }

    EXPECT_EQ(total_records, 100);
    EXPECT_EQ(batch_count, 10);
}

// Tests CSV extraction with empty fields and null value handling
TEST_F(CsvExtractorIntegrationTest, EmptyFieldsCsv) {
    std::vector<std::string> csv_data = {
        "id,name,score,comment",
        "1,Alice,,Good work",
        "2,,85,",
        "3,Charlie,92,Excellent"
    };
    createCsvFile("empty_fields.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "empty_fields.csv").string()}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Verify handling of empty fields
    const auto& first = batch.getRecord(0);
    EXPECT_EQ(first.getFieldAs<long long>("id"), 1);
    EXPECT_EQ(first.getFieldAs<std::string>("name"), "Alice");
    // Empty score field - should be handled as empty/null
    EXPECT_TRUE(first.hasField("score"));
    EXPECT_EQ(first.getFieldAs<std::string>("comment"), "Good work");

    const auto& second = batch.getRecord(1);
    EXPECT_EQ(second.getFieldAs<long long>("id"), 2);
    // Empty name field
    EXPECT_TRUE(second.hasField("name"));
    EXPECT_EQ(second.getFieldAs<long long>("score"), 85);
}

// Tests multi-file CSV extraction from multiple related files
TEST_F(CsvExtractorIntegrationTest, MultiFileCsvExtraction) {
    // Create multiple CSV files
    for (int i = 1; i <= 3; ++i) {
        std::vector<std::string> csv_data = {
            "id,name,file_num",
            std::to_string(i) + ",File " + std::to_string(i) + " Data," + std::to_string(i)
        };
        createCsvFile("file_" + std::to_string(i) + ".csv", csv_data);
    }

    auto extractor = std::make_unique<MultiFileCsvExtractor>();
    std::vector<std::string> files = {
        (test_dir_ / "file_1.csv").string(),
        (test_dir_ / "file_2.csv").string(),
        (test_dir_ / "file_3.csv").string()
    };

    std::unordered_map<std::string, std::any> config = {
        {"files", files},
        {"skip_headers_after_first", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total_records = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_records += batch.size();
    }

    EXPECT_EQ(total_records, 3); // 1 record per CSV file * 3 files
}

// Tests CSV directory extraction from all CSV files in a directory
TEST_F(CsvExtractorIntegrationTest, CsvDirectoryExtraction) {
    // Create directory with CSV files
    fs::path csv_dir = test_dir_ / "csv_files";
    fs::create_directories(csv_dir);

    for (int i = 1; i <= 3; ++i) {
        std::vector<std::string> csv_data = {
            "id,product,quantity",
            std::to_string(i) + ",Product " + std::to_string(i) + "," + std::to_string(i * 10)
        };
        
        std::ofstream file(csv_dir / ("data_" + std::to_string(i) + ".csv"));
        for (const auto& line : csv_data) {
            file << line << "\n";
        }
        file.close();
    }

    auto extractor = std::make_unique<CsvDirectoryExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"directory", csv_dir.string()},
        {"pattern", ".*\\.csv$"},
        {"recursive", false}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total_records = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_records += batch.size();
    }

    EXPECT_EQ(total_records, 3); // 1 record per CSV file * 3 files
}

// Tests compressed CSV extraction with real gzip compression
TEST_F(CsvExtractorIntegrationTest, CompressedCsvExtraction) {
    // Create CSV content
    std::vector<std::string> csv_data = {
        "id,product,quantity",
        "1,Widget A,100",
        "2,Widget B,200", 
        "3,Widget C,300"
    };
    
    // Create a properly compressed gzip file
    std::string csv_content = "";
    for (const auto& line : csv_data) {
        csv_content += line + "\n";
    }
    
    std::string gz_filepath = (test_dir_ / "compressed.csv.gz").string();
    gzFile gz_file = gzopen(gz_filepath.c_str(), "wb");
    ASSERT_NE(gz_file, nullptr) << "Failed to create gzip file";
    
    int written = gzwrite(gz_file, csv_content.c_str(), csv_content.length());
    EXPECT_EQ(written, csv_content.length());
    gzclose(gz_file);

    auto extractor = std::make_unique<CompressedCsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", gz_filepath}
    };

    omop::core::ProcessingContext context;

    // Should successfully initialize and extract compressed data
    ASSERT_NO_THROW(extractor->initialize(config, context));
    
    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Verify extracted data
    EXPECT_EQ(std::any_cast<std::string>(batch.getRecord(0).getField("id")), "1");
    EXPECT_EQ(std::any_cast<std::string>(batch.getRecord(0).getField("product")), "Widget A");
    EXPECT_EQ(std::any_cast<std::string>(batch.getRecord(0).getField("quantity")), "100");
}

// Tests error handling for missing file scenarios
TEST_F(CsvExtractorIntegrationTest, MissingFileError) {
    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "nonexistent.csv").string()}
    };

    omop::core::ProcessingContext context;
    EXPECT_THROW(extractor->initialize(config, context), omop::common::ExtractionException);
}

// Tests automatic type inference for different data types in CSV columns
TEST_F(CsvExtractorIntegrationTest, TypeInference) {
    std::vector<std::string> csv_data = {
        "int_col,float_col,bool_col,date_col,string_col",
        "123,45.67,true,2023-01-15,Hello World",
        "456,78.90,false,2023-02-20,Test String",
        "789,12.34,TRUE,2023-03-25,Another Value"
    };
    createCsvFile("typed.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "typed.csv").string()}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Verify that type inference worked correctly
    const auto& first = batch.getRecord(0);
    
    // Integer column should be inferred as integer
    EXPECT_NO_THROW({
        auto id_val = first.getFieldAs<long long>("int_col");
        EXPECT_EQ(id_val, 123);
    });
    
    // Float column should be inferred as double
    EXPECT_NO_THROW({
        auto float_val = first.getFieldAs<double>("float_col");
        EXPECT_DOUBLE_EQ(float_val, 45.67);
    });
    
    // Boolean column should be inferred as boolean
    EXPECT_NO_THROW({
        auto bool_val = first.getFieldAs<bool>("bool_col");
        EXPECT_EQ(bool_val, true);
    });
    
    // Date column stays as string (CSV extractor doesn't parse dates by default)
    EXPECT_NO_THROW({
        auto date_str = first.getFieldAs<std::string>("date_col");
        EXPECT_EQ(date_str, "2023-01-15");
    });
}

// Tests CSV extraction with skip lines feature for files with comments or metadata
TEST_F(CsvExtractorIntegrationTest, SkipLines) {
    std::vector<std::string> csv_data = {
        "# This is a comment",
        "# Another comment",
        "id,name,value",
        "1,Item A,100",
        "2,Item B,200"
    };
    createCsvFile("with_comments.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "with_comments.csv").string()},
        {"skip_lines", size_t(2)}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 2);
    EXPECT_EQ(batch.getRecord(0).getFieldAs<std::string>("name"), "Item A");
}

// Tests extraction statistics collection and reporting
TEST_F(CsvExtractorIntegrationTest, ExtractionStatistics) {
    createLargeCsvFile("stats_test.csv", 500);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "stats_test.csv").string()}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all data
    size_t total = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(100, context);
        total += batch.size();
    }

    // Get statistics
    auto stats = extractor->get_statistics();

    // Check for expected statistics with proper error handling
    ASSERT_TRUE(stats.find("extracted_count") != stats.end());
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), 500);
    
    ASSERT_TRUE(stats.find("error_count") != stats.end());
    EXPECT_EQ(std::any_cast<size_t>(stats["error_count"]), 0);
}

// Tests maximum line limit functionality for large file processing
TEST_F(CsvExtractorIntegrationTest, MaxLinesLimit) {
    createLargeCsvFile("unlimited.csv", 1000);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "unlimited.csv").string()},
        {"max_lines", size_t(250)}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(100, context);
        total += batch.size();
    }

    EXPECT_EQ(total, 250);
}

// Tests UTF-8 validation and international character handling
TEST_F(CsvExtractorIntegrationTest, UTF8Validation) {
    std::vector<std::string> csv_data = {
        "id,name,description",
        "1,John Doe,English text",
        "2,José García,Spanish: ñáéíóú",
        "3,Müller,German: äöüß",
        "4,李明,Chinese: 你好世界",
        "5,Иванов,Russian: Привет мир",
        "6,山田太郎,Japanese: こんにちは"
    };
    createCsvFile("utf8.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "utf8.csv").string()},
        {"encoding", std::string("UTF-8")}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 6);
    
    // Verify international characters are preserved
    EXPECT_EQ(batch.getRecord(1).getFieldAs<std::string>("name"), "José García");
    EXPECT_EQ(batch.getRecord(2).getFieldAs<std::string>("name"), "Müller");
}

// Tests escape character handling in CSV fields
TEST_F(CsvExtractorIntegrationTest, EscapeCharacterHandling) {
    std::vector<std::string> csv_data = {
        "id,name,path",
        "1,Regular Name,/simple/path",
        "2,Name\\,with\\,commas,/path/with\\,comma",
        "3,Name\\\"with\\\"quotes,/path/with\\\"quotes"
    };
    createCsvFile("escape_chars.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "escape_chars.csv").string()},
        {"escape_char", '\\'}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Verify escape characters are handled correctly
    EXPECT_EQ(batch.getRecord(1).getFieldAs<std::string>("name"), "Name,with,commas");
    EXPECT_EQ(batch.getRecord(1).getFieldAs<std::string>("path"), "/path/with,comma");
    EXPECT_EQ(batch.getRecord(2).getFieldAs<std::string>("name"), "Name\"with\"quotes");
    EXPECT_EQ(batch.getRecord(2).getFieldAs<std::string>("path"), "/path/with\"quotes");
}

// Tests column selection to extract only specified columns from CSV
TEST_F(CsvExtractorIntegrationTest, ColumnSelection) {
    std::vector<std::string> csv_data = {
        "id,first_name,last_name,email,phone,address",
        "1,John,Doe,<EMAIL>,555-1234,123 Main St",
        "2,Jane,Smith,<EMAIL>,555-5678,456 Oak Ave",
        "3,Bob,Johnson,<EMAIL>,555-9012,789 Pine Rd"
    };
    createCsvFile("full_data.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "full_data.csv").string()},
        {"columns", std::vector<std::string>{"id", "first_name", "email"}}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);

    // Verify only selected columns are present
    const auto& record = batch.getRecord(0);
    EXPECT_TRUE(record.hasField("id"));
    EXPECT_TRUE(record.hasField("first_name"));
    EXPECT_TRUE(record.hasField("email"));
    EXPECT_FALSE(record.hasField("last_name"));
    EXPECT_FALSE(record.hasField("phone"));
    EXPECT_FALSE(record.hasField("address"));
}

// Tests basic CSV extraction with UK patient data
TEST_F(CsvExtractorIntegrationTest, ExtractUKPatientData) {
    std::string filepath = createUKPatientCSV();
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["has_header"] = true;
    config["date_format"] = std::string("%d/%m/%Y");  // UK date format
    
    omop::core::ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(8, batch.size()) << "Should extract all 8 UK patient records";
    
    auto records = batch.getRecords();
    
    // Verify UK specific data
    auto first_record = records[0];
    EXPECT_EQ("Smith", std::any_cast<std::string>(first_record.getField("surname")));
    EXPECT_EQ("James", std::any_cast<std::string>(first_record.getField("forename")));
    EXPECT_EQ("SW1A 1AA", std::any_cast<std::string>(first_record.getField("postcode")));
    EXPECT_EQ("020 7946 0958", std::any_cast<std::string>(first_record.getField("telephone")));
    EXPECT_EQ("White British", std::any_cast<std::string>(first_record.getField("ethnicity")));
}

// Tests extraction of NHS clinical data with Read codes
TEST_F(CsvExtractorIntegrationTest, ExtractNHSClinicalData) {
    std::string filepath = createUKClinicalCSV();
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["has_header"] = true;
    config["date_format"] = std::string("%d/%m/%Y");
    
    omop::core::ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(8, batch.size()) << "Should extract all 8 clinical records";
    
    auto records = batch.getRecords();
    
    // Verify UK clinical coding
    auto first_record = records[0];
    EXPECT_EQ("G20..00", std::any_cast<std::string>(first_record.getField("read_code")));
    EXPECT_EQ("I10", std::any_cast<std::string>(first_record.getField("icd10_code")));
    EXPECT_EQ("Essential Hypertension", std::any_cast<std::string>(first_record.getField("primary_diagnosis")));
    EXPECT_EQ("Cardiology Ward", std::any_cast<std::string>(first_record.getField("ward")));
}

// Tests UK regional date, currency, and format handling
TEST_F(CsvExtractorIntegrationTest, UKRegionalFormats) {
    std::vector<std::string> csv_data = {
        "id,transaction_date,amount,temperature,postcode",
        "1,15/01/2023,£1234.56,20.5°C,SW1A 1AA",
        "2,28/02/2023,£2500.00,15.2°C,EC1A 1BB",
        "3,31/12/2023,£999.99,12.0°C,M1 1AA"
    };
    createCsvFile("uk_formats.csv", csv_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "uk_formats.csv").string()},
        {"date_format", std::string("%d/%m/%Y")},
        {"locale", std::string("en_GB")}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Verify UK format data is preserved
    const auto& first = batch.getRecord(0);
    EXPECT_EQ(first.getFieldAs<std::string>("transaction_date"), "15/01/2023");
    EXPECT_EQ(first.getFieldAs<std::string>("amount"), "£1234.56");
    EXPECT_EQ(first.getFieldAs<std::string>("postcode"), "SW1A 1AA");
}

} // namespace omop::extract::test