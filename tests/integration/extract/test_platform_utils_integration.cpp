// tests/integration/extract/test_platform_utils_integration.cpp
// Tests platform-specific utility functions

#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

#ifdef _WIN32
#include "extract/platform/windows_utils.h"
#else
#include "extract/platform/unix_utils.h"
#endif

namespace omop::extract::platform::test {

namespace fs = std::filesystem;

class PlatformUtilsIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = fs::temp_directory_path() / "platform_test";
        fs::create_directories(test_dir_);
    }

    void TearDown() override {
        if (fs::exists(test_dir_)) {
            fs::remove_all(test_dir_);
        }
    }

    fs::path test_dir_;
};

#ifndef _WIN32
// Unix-specific tests

TEST_F(PlatformUtilsIntegrationTest, UnixMemoryMappedFile) {
    // Create test file
    std::string test_content = "This is a test file for memory mapping on Unix systems.";
    fs::path test_file = test_dir_ / "mmap_test.txt";
    
    std::ofstream out(test_file);
    out << test_content;
    out.close();

    // Test memory mapping
    MemoryMappedFile mmap;
    EXPECT_TRUE(mmap.map_file(test_file.string(), true));
    EXPECT_TRUE(mmap.is_mapped());
    EXPECT_EQ(mmap.size(), test_content.size());

    // Verify content
    std::string mapped_content(static_cast<char*>(mmap.data()), mmap.size());
    EXPECT_EQ(mapped_content, test_content);

    // Test unmap
    mmap.unmap();
    EXPECT_FALSE(mmap.is_mapped());
}

TEST_F(PlatformUtilsIntegrationTest, UnixFileOperations) {
    fs::path test_file = test_dir_ / "file_ops.txt";
    std::ofstream out(test_file);
    out << "Test file content";
    out.close();

    // Test file size
    EXPECT_EQ(get_file_size(test_file.string()), 17);

    // Test file permissions
    mode_t original_perms = get_file_permissions(test_file.string());
    EXPECT_TRUE(set_file_permissions(test_file.string(), 0644));
    EXPECT_EQ(get_file_permissions(test_file.string()), 0644);

    // Test symbolic links
    fs::path link_path = test_dir_ / "test_link";
    fs::create_symlink(test_file, link_path);
    EXPECT_TRUE(is_symbolic_link(link_path.string()));
    
    std::string resolved = resolve_symbolic_link(link_path.string());
    EXPECT_EQ(fs::path(resolved).filename(), "file_ops.txt");
}

TEST_F(PlatformUtilsIntegrationTest, UnixHighResTimer) {
    UnixHighResTimer timer;
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 100.0);
    EXPECT_LE(elapsed_ms, 150.0); // Allow some tolerance

    // Test reset
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 50.0);
    EXPECT_LE(elapsed_ms, 100.0);
}

TEST_F(PlatformUtilsIntegrationTest, UnixMemoryInfo) {
    MemoryInfo mem_info = get_memory_info();
    
    // Basic sanity checks
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
}

TEST_F(PlatformUtilsIntegrationTest, UnixTempFileCreation) {
    std::string temp_file = create_temp_file("test_prefix_", ".tmp");
    
    EXPECT_TRUE(fs::exists(temp_file));
    EXPECT_NE(temp_file.find("test_prefix_"), std::string::npos);
    EXPECT_NE(temp_file.find(".tmp"), std::string::npos);
    
    // Clean up
    fs::remove(temp_file);
}

#else
// Windows-specific tests

TEST_F(PlatformUtilsIntegrationTest, WindowsStringConversion) {
    // Test UTF-8 to wide conversion
    std::string utf8_str = "Hello, 世界! Привет мир!";
    std::wstring wide_str = utf8_to_wide(utf8_str);
    EXPECT_FALSE(wide_str.empty());
    
    // Test round-trip conversion
    std::string converted_back = wide_to_utf8(wide_str);
    EXPECT_EQ(converted_back, utf8_str);
}

TEST_F(PlatformUtilsIntegrationTest, WindowsFileMapping) {
    // Create test file
    std::string test_content = "This is a test file for Windows file mapping.";
    fs::path test_file = test_dir_ / "winmap_test.txt";
    
    std::ofstream out(test_file);
    out << test_content;
    out.close();

    // Test file mapping
    auto [file_handle, mapping_handle] = create_file_mapping(test_file.string());
    EXPECT_TRUE(file_handle.is_valid());
    EXPECT_TRUE(mapping_handle.is_valid());

    void* view = map_view_of_file(mapping_handle.get());
    EXPECT_NE(view, nullptr);

    // Verify content
    std::string mapped_content(static_cast<char*>(view), test_content.size());
    EXPECT_EQ(mapped_content, test_content);

    // Unmap
    EXPECT_TRUE(unmap_view_of_file(view));
}

TEST_F(PlatformUtilsIntegrationTest, WindowsFileOperations) {
    fs::path test_file = test_dir_ / "win_file_ops.txt";
    std::ofstream out(test_file);
    out << "Windows test file";
    out.close();

    // Test file size
    EXPECT_EQ(get_file_size(test_file.string()), 17);

    // Test file attributes
    DWORD original_attrs = get_file_attributes(test_file.string());
    EXPECT_NE(original_attrs, INVALID_FILE_ATTRIBUTES);

    // Set and verify attributes
    EXPECT_TRUE(set_file_attributes(test_file.string(), 
                                  FILE_ATTRIBUTE_NORMAL | FILE_ATTRIBUTE_ARCHIVE));
}

TEST_F(PlatformUtilsIntegrationTest, WindowsHighResTimer) {
    WindowsHighResTimer timer;
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 100.0);
    EXPECT_LE(elapsed_ms, 150.0); // Allow some tolerance

    // Test reset
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 50.0);
    EXPECT_LE(elapsed_ms, 100.0);
}

TEST_F(PlatformUtilsIntegrationTest, WindowsMemoryInfo) {
    MemoryInfo mem_info = get_memory_info();
    
    // Basic sanity checks
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
    EXPECT_GT(mem_info.total_virtual, 0);
}

TEST_F(PlatformUtilsIntegrationTest, WindowsTempFileCreation) {
    std::string temp_file = create_temp_file("test_prefix_", ".tmp");
    
    EXPECT_TRUE(fs::exists(temp_file));
    EXPECT_NE(temp_file.find("test_prefix_"), std::string::npos);
    EXPECT_NE(temp_file.find(".tmp"), std::string::npos);
    
    // Clean up
    fs::remove(temp_file);
}

TEST_F(PlatformUtilsIntegrationTest, WindowsDriveDetection) {
    std::vector<char> drives = get_available_drives();
    
    // Should at least have C: drive on Windows
    EXPECT_FALSE(drives.empty());
    EXPECT_NE(std::find(drives.begin(), drives.end(), 'C'), drives.end());
}

#endif

} // namespace omop::extract::platform::test 