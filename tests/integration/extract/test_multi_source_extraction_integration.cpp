// Integration tests for multi-source data extraction using existing extractors
#include <gtest/gtest.h>
#include "extract/extractor_factory.h"
#include "extract/extract.h"
#include "extract/extract_utils.h"
#include "extract/database_connector.h"
#include "extract/csv_extractor.h"
#include "extract/postgresql_connector.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "common/utilities.h"
#include "test_helpers/database_connection_factory.h"
#include <thread>
#include <future>
#include <filesystem>
#include <fstream>
#include <iomanip>

namespace omop::test {

namespace fs = std::filesystem;

// Simple database fixture for testing
class DatabaseFixture {
public:
    struct DatabaseConfig {
        enum class DatabaseType { PostgreSQL, MySQL, SQLite };
        DatabaseType type;
        std::string host;
        int port;
        std::string database;
        std::string username;
        std::string password;
    };
    
    void setup(const DatabaseConfig& config) {
        // Create a real connection for testing
        connection_ = createRealConnection();
        config_ = config;
    }
    
    void teardown() {
        if (connection_ && connection_->is_connected()) {
            connection_->disconnect();
        }
    }
    
    std::unique_ptr<extract::IDatabaseConnection> get_connection() {
        return createRealConnection();
    }
    
private:
    std::unique_ptr<extract::IDatabaseConnection> createRealConnection() {
        // Attempt to create a real database connection for testing
        // Use environment variables or test database setup
        try {
            auto connection = std::make_unique<extract::PostgreSQLConnection>();
            extract::IDatabaseConnection::ConnectionParams params{
                .host = std::getenv("TEST_DB_HOST") ? std::getenv("TEST_DB_HOST") : "localhost",
                .port = std::getenv("TEST_DB_PORT") ? std::stoi(std::getenv("TEST_DB_PORT")) : 5432,
                .database = std::getenv("TEST_DB_NAME") ? std::getenv("TEST_DB_NAME") : "test_extract_db",
                .username = std::getenv("TEST_DB_USER") ? std::getenv("TEST_DB_USER") : "test_user",
                .password = std::getenv("TEST_DB_PASSWORD") ? std::getenv("TEST_DB_PASSWORD") : "test_password",
                .options = {}
            };
            
            connection->connect(params);
            return connection;
        } catch (const std::exception& e) {
            // Return nullptr if connection fails - tests will be skipped
            return nullptr;
        }
    }
    
    std::unique_ptr<extract::IDatabaseConnection> connection_;
    DatabaseConfig config_;
};

class MultiSourceExtractionIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        test_dir_ = fs::temp_directory_path() / "multi_source_test";
        fs::create_directories(test_dir_);
        
        // Initialize logging
        logger_ = common::Logger::get("omop-multi-source-test");
        
        // Create sample data files
        createTestDataFiles();
    }

    void TearDown() override {
        // Clean up test directory
        if (fs::exists(test_dir_)) {
            fs::remove_all(test_dir_);
        }
    }

    void createTestDataFiles() {
        // Create CSV data
        std::vector<std::string> csv_data = {
            "patient_id,first_name,last_name,age,gender,diagnosis_code",
            "1,John,Doe,35,M,I10",
            "2,Jane,Smith,42,F,E11.9",
            "3,Robert,Johnson,28,M,J45.9"
        };
        
        std::ofstream csv_file(test_dir_ / "patients.csv");
        for (const auto& line : csv_data) {
            csv_file << line << "\n";
        }
        csv_file.close();

        // Create JSON data
        std::string json_data = R"([
            {"patient_id": 1, "first_name": "John", "last_name": "Doe", "age": 35, "gender": "M"},
            {"patient_id": 2, "first_name": "Jane", "last_name": "Smith", "age": 42, "gender": "F"},
            {"patient_id": 3, "first_name": "Robert", "last_name": "Johnson", "age": 28, "gender": "M"}
        ])";
        
        std::ofstream json_file(test_dir_ / "patients.json");
        json_file << json_data;
        json_file.close();

        // Create JSONL data
        std::vector<std::string> jsonl_data = {
            R"({"encounter_id": 1, "patient_id": 1, "visit_type": "emergency", "diagnosis": "hypertension"})",
            R"({"encounter_id": 2, "patient_id": 2, "visit_type": "outpatient", "diagnosis": "diabetes"})",
            R"({"encounter_id": 3, "patient_id": 3, "visit_type": "inpatient", "diagnosis": "asthma"})"
        };
        
        std::ofstream jsonl_file(test_dir_ / "encounters.jsonl");
        for (const auto& line : jsonl_data) {
            jsonl_file << line << "\n";
        }
        jsonl_file.close();
    }

    fs::path test_dir_;
    std::shared_ptr<common::Logger> logger_;
    DatabaseFixture db_fixture_;
};

// Tests parallel extraction from multiple sources
TEST_F(MultiSourceExtractionIntegrationTest, ParallelExtraction) {
    // Use real extractors with proper configuration
    std::vector<std::unique_ptr<core::IExtractor>> extractors;

    // Add CSV extractor
    auto csv_extractor = std::make_unique<omop::extract::CsvExtractor>();
    std::unordered_map<std::string, std::any> csv_config = {
        {"filepath", (test_dir_ / "patients_source1.csv").string()},
        {"has_header", true}
    };
    
    core::ProcessingContext context;
    csv_extractor->initialize(csv_config, context);
    extractors.push_back(std::move(csv_extractor));

    // Add JSON extractor  
    auto json_extractor = std::make_unique<omop::extract::JsonExtractor>();
    std::unordered_map<std::string, std::any> json_config = {
        {"filepath", (test_dir_ / "encounters_source2.json").string()},
        {"json_path", std::string("$.encounters[*]")}
    };
    json_extractor->initialize(json_config, context);
    extractors.push_back(std::move(json_extractor));

    // Add database extractor using real database connection
    try {
        auto connection = DatabaseConnectionFactory::createClinicalConnection();
        auto db_extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));
        std::unordered_map<std::string, std::any> db_config = {
            {"table", std::string("multi_source_test.source_patients")}
        };
        db_extractor->initialize(db_config, context);
        extractors.push_back(std::move(db_extractor));
    } catch (const std::exception& e) {
        logger_->warn("Failed to create database extractor: {}", e.what());
        // Continue without database extractor for this test
    }

    // Extract from all sources in parallel using threads
    std::vector<std::thread> threads;
    std::vector<size_t> results(extractors.size(), 0);
    
    for (size_t i = 0; i < extractors.size(); ++i) {
        threads.emplace_back([&extractors, &results, i]() {
            try {
                core::ProcessingContext thread_context;
                auto batch = extractors[i]->extract_batch(10, thread_context);
                results[i] = batch.size();
            } catch (...) {
                results[i] = 0;
            }
        });
    }
    
    // Wait for all extractions to complete
    for (auto& thread : threads) {
        thread.join();
    }

    // Verify we got records from all sources
    EXPECT_GT(results[0] + results[1] + results[2], 0);
    
    for (size_t i = 0; i < results.size(); ++i) {
        logger_->info("Source {} extracted {} records", i, results[i]);
    }
}

// Tests streaming extraction with callback processing
TEST_F(MultiSourceExtractionIntegrationTest, StreamingExtraction) {
    // Process records as they come with callback
    std::vector<std::pair<size_t, std::string>> batch_info;
    std::mutex batch_mutex;

    auto callback = [&](const core::RecordBatch& batch, const std::string& source) {
        std::lock_guard<std::mutex> lock(batch_mutex);
        batch_info.emplace_back(batch.size(), source);
        logger_->info("Processed batch of {} records from source: {}", batch.size(), source);
    };

    // Extract from CSV with streaming
    {
        omop::extract::CsvExtractor extractor;
        std::unordered_map<std::string, std::any> config = {
            {"filepath", (test_dir_ / "patients_source1.csv").string()},
            {"has_header", true}
        };
        
        core::ProcessingContext context;
        extractor.initialize(config, context);
        
        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(2, context);
            if (batch.size() > 0) {
                callback(batch, "csv_patients");
            }
        }
    }

    // Extract from JSON with streaming
    {
        omop::extract::JsonExtractor extractor;
        std::unordered_map<std::string, std::any> config = {
            {"filepath", (test_dir_ / "encounters_source2.json").string()},
            {"json_path", std::string("$.encounters[*]")}
        };
        
        core::ProcessingContext context;
        extractor.initialize(config, context);
        
        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(2, context);
            if (batch.size() > 0) {
                callback(batch, "json_encounters");
            }
        }
    }

    // Verify we processed batches from both sources
    EXPECT_GT(batch_info.size(), 0);
    
    bool found_csv = false, found_json = false;
    for (const auto& [size, source] : batch_info) {
        if (source == "csv_patients") found_csv = true;
        if (source == "json_encounters") found_json = true;
        EXPECT_GT(size, 0);
    }
    
    EXPECT_TRUE(found_csv);
    EXPECT_TRUE(found_json);
}

// Tests extraction performance with concurrent operations
TEST_F(MultiSourceExtractionIntegrationTest, ConcurrentExtractionPerformance) {
    const size_t num_threads = 3;
    std::vector<std::thread> extraction_threads;
    std::vector<size_t> extraction_counts(num_threads, 0);
    std::mutex results_mutex;

    auto start_time = std::chrono::steady_clock::now();

    // Create multiple extraction threads
    for (size_t i = 0; i < num_threads; ++i) {
        extraction_threads.emplace_back([this, i, &extraction_counts, &results_mutex]() {
            try {
                if (i == 0) {
                    // CSV extraction
                    omop::extract::CsvExtractor extractor;
                    std::unordered_map<std::string, std::any> config = {
                        {"filepath", (test_dir_ / "patients_source1.csv").string()},
                        {"has_header", true}
                    };

                core::ProcessingContext context;
                    extractor.initialize(config, context);

                size_t count = 0;
                    while (extractor.has_more_data()) {
                        auto batch = extractor.extract_batch(10, context);
                        count += batch.size();
                    }

                    std::lock_guard<std::mutex> lock(results_mutex);
                    extraction_counts[i] = count;
                    
                } else if (i == 1) {
                    // JSON extraction
                    omop::extract::JsonExtractor extractor;
                    std::unordered_map<std::string, std::any> config = {
                        {"filepath", (test_dir_ / "encounters_source2.json").string()},
                        {"json_path", std::string("$.encounters[*]")}
                    };
                    
                    core::ProcessingContext context;
                    extractor.initialize(config, context);

                    size_t count = 0;
                    while (extractor.has_more_data()) {
                        auto batch = extractor.extract_batch(10, context);
                    count += batch.size();
                }

                std::lock_guard<std::mutex> lock(results_mutex);
                extraction_counts[i] = count;
                    
                } else {
                    // Database extraction using real database connection
                    auto connection = DatabaseConnectionFactory::createClinicalConnection();
                    omop::extract::DatabaseExtractor extractor(std::move(connection));

                    std::unordered_map<std::string, std::any> config = {
                        {"table", std::string("multi_source_test.source_patients")}
                    };
                    
                    core::ProcessingContext context;
                    extractor.initialize(config, context);

                    size_t count = 0;
                    while (extractor.has_more_data()) {
                        auto batch = extractor.extract_batch(10, context);
                        count += batch.size();
                    }

                    std::lock_guard<std::mutex> lock(results_mutex);
                    extraction_counts[i] = count;
                }

            } catch (const std::exception& e) {
                logger_->error("Thread {} extraction failed: {}", i, e.what());
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : extraction_threads) {
        thread.join();
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // Verify all extractions completed successfully
    for (size_t i = 0; i < num_threads; ++i) {
        EXPECT_GT(extraction_counts[i], 0) << "Thread " << i << " extracted no records";
    }

    logger_->info("Concurrent extraction completed in {}ms", duration.count());
    EXPECT_LT(duration.count(), 10000); // Should complete within 10 seconds
}

// Tests basic database extraction integration
TEST_F(MultiSourceExtractionIntegrationTest, DatabaseExtraction) {
    // Use real database connection from test environment
    auto connection = DatabaseConnectionFactory::createClinicalConnection();
    ASSERT_TRUE(connection->is_connected());

    try {
        auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));
        
        std::unordered_map<std::string, std::any> config = {
            {"table", std::string("multi_source_test.source_patients")},
            {"columns", std::vector<std::string>{"source_patient_id", "external_id", "first_name", "last_name", "source_system"}}
        };
        
        core::ProcessingContext context;
        extractor->initialize(config, context);

        // Extract at least one batch
        auto batch = extractor->extract_batch(10, context);
        EXPECT_GT(batch.size(), 0) << "Should extract data from real database";
        
        // Verify database structure
        if (batch.size() > 0) {
            const auto& record = batch.getRecord(0);
            EXPECT_TRUE(record.hasField("source_patient_id"));
            EXPECT_TRUE(record.hasField("external_id"));
            EXPECT_TRUE(record.hasField("first_name"));
            EXPECT_TRUE(record.hasField("last_name"));
            EXPECT_TRUE(record.hasField("source_system"));
            
            // Verify data types and values
            auto source_system = record.getField("source_system");
            if (source_system.has_value()) {
                std::string system = std::any_cast<std::string>(source_system);
                EXPECT_TRUE(system == "NHS" || system == "GP_SYSTEM" || system == "HOSPITAL");
            }
        }

        // Test pagination
        size_t total_records = batch.size();
        while (extractor->has_more_data() && total_records < 50) {
            auto next_batch = extractor->extract_batch(10, context);
            total_records += next_batch.size();
            if (next_batch.size() == 0) break;
        }

        logger_->info("Database extraction completed: {} total records", total_records);
        EXPECT_GT(total_records, 0);

    } catch (const std::exception& e) {
        FAIL() << "Database extraction test failed: " << e.what();
    }
}

// Tests database extraction integration with real database
TEST_F(MultiSourceExtractionIntegrationTest, DatabaseExtractionAdvanced) {
    // Use real database connection from test environment
    auto connection = DatabaseConnectionFactory::createClinicalConnection();
    ASSERT_TRUE(connection->is_connected());

    try {
        auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));
        
        std::unordered_map<std::string, std::any> config = {
            {"table", std::string("multi_source_test.source_patients")},
            {"columns", std::vector<std::string>{"source_patient_id", "external_id", "first_name", "source_system"}}
        };

        core::ProcessingContext context;
        extractor->initialize(config, context);

        // Extract at least one batch
        auto batch = extractor->extract_batch(10, context);
        EXPECT_GT(batch.size(), 0) << "Should extract data from real database";
        
        // Verify database structure
        if (batch.size() > 0) {
            const auto& record = batch.getRecord(0);
            EXPECT_TRUE(record.hasField("source_patient_id"));
            EXPECT_TRUE(record.hasField("external_id"));
            EXPECT_TRUE(record.hasField("source_system"));
        }

    } catch (const std::exception& e) {
        FAIL() << "Database extraction test failed: " << e.what();
    }
}

// Tests error handling in multi-source scenarios
TEST_F(MultiSourceExtractionIntegrationTest, ErrorHandlingMultiSource) {
    std::vector<std::thread> threads;
    std::vector<bool> results(3, false);

    // Valid CSV extractor
    threads.emplace_back([this, &results]() {
        try {
            omop::extract::CsvExtractor extractor;
            std::unordered_map<std::string, std::any> config = {
                {"filepath", (test_dir_ / "patients_source1.csv").string()},
                {"has_header", true}
            };
            
            core::ProcessingContext context;
            extractor.initialize(config, context);
            
            auto batch = extractor.extract_batch(10, context);
            results[0] = batch.size() > 0;
        } catch (...) {
            results[0] = false;
        }
    });

    // Invalid CSV extractor (non-existent file)
    threads.emplace_back([this, &results]() {
        try {
            omop::extract::CsvExtractor extractor;
            std::unordered_map<std::string, std::any> config = {
                {"filepath", (test_dir_ / "nonexistent.csv").string()},
                {"has_header", true}
            };
            
            core::ProcessingContext context;
            extractor.initialize(config, context); // Should throw
            results[1] = false; // Should not reach here
        } catch (...) {
            results[1] = true; // Expected failure
        }
    });

    // Valid database extractor
    threads.emplace_back([this, &results]() {
        try {
            auto connection = DatabaseConnectionFactory::createClinicalConnection();
            omop::extract::DatabaseExtractor extractor(std::move(connection));
            
            std::unordered_map<std::string, std::any> config = {
                {"table", std::string("multi_source_test.source_patients")}
            };
            
            core::ProcessingContext context;
            extractor.initialize(config, context);
            
            auto batch = extractor.extract_batch(10, context);
            results[2] = batch.size() > 0;
        } catch (...) {
            results[2] = false;
        }
    });

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    // Valid extractors should succeed, invalid should fail gracefully
    EXPECT_TRUE(results[0]) << "Valid CSV extractor should succeed";
    EXPECT_TRUE(results[1]) << "Invalid CSV extractor should fail gracefully";
    EXPECT_TRUE(results[2]) << "Valid database extractor should succeed";
}

// Tests batch extraction with multiple file types
TEST_F(MultiSourceExtractionIntegrationTest, BatchMultiFileExtraction) {
    struct BatchResult {
        std::string source_type;
        size_t total_records = 0;
        size_t batch_count = 0;
    };
    
    std::vector<BatchResult> results;

    // CSV batch extraction
    {
        BatchResult csv_result;
        csv_result.source_type = "CSV";
        
        omop::extract::CsvExtractor extractor;
        std::unordered_map<std::string, std::any> config = {
            {"filepath", (test_dir_ / "patients_source1.csv").string()},
            {"has_header", true}
        };
        
        core::ProcessingContext context;
        extractor.initialize(config, context);

        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(2, context); // Small batch size
            csv_result.total_records += batch.size();
            if (batch.size() > 0) {
                csv_result.batch_count++;

        // Verify batch structure
        for (size_t i = 0; i < batch.size(); ++i) {
            const auto& record = batch.getRecord(i);
            EXPECT_TRUE(record.hasField("patient_id"));
            EXPECT_TRUE(record.hasField("first_name"));
            EXPECT_TRUE(record.hasField("last_name"));
        }
            }
        }
        
        results.push_back(csv_result);
    }

    // JSON batch extraction
    {
        BatchResult json_result;
        json_result.source_type = "JSON";
        
        omop::extract::JsonExtractor extractor;
        std::unordered_map<std::string, std::any> config = {
            {"filepath", (test_dir_ / "encounters_source2.json").string()},
            {"json_path", std::string("$.encounters[*]")}
        };
        
        core::ProcessingContext context;
        extractor.initialize(config, context);

        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(1, context); // Very small batch
            json_result.total_records += batch.size();
            if (batch.size() > 0) {
                json_result.batch_count++;
            }
        }
        
        results.push_back(json_result);
    }

    // Database batch extraction
    {
        BatchResult db_result;
        db_result.source_type = "Database";
        
        // Database extraction using real database connection
        auto connection = DatabaseConnectionFactory::createClinicalConnection();
        omop::extract::DatabaseExtractor extractor(std::move(connection));
        
        std::unordered_map<std::string, std::any> config = {
            {"table", std::string("multi_source_test.source_patients")}
        };

        core::ProcessingContext context;
        extractor.initialize(config, context);

        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(3, context);
            db_result.total_records += batch.size();
            if (batch.size() > 0) {
                db_result.batch_count++;
            }
        }
        
        results.push_back(db_result);
    }

    // Verify all sources were processed
    for (const auto& result : results) {
        EXPECT_GT(result.total_records, 0) << result.source_type << " should extract records";
        EXPECT_GT(result.batch_count, 0) << result.source_type << " should have batches";
        logger_->info("{}: {} records in {} batches", result.source_type, result.total_records, result.batch_count);
    }
}

// Tests factory-based extractor creation
TEST_F(MultiSourceExtractionIntegrationTest, FactoryBasedExtraction) {
    std::vector<std::string> test_files = {
        (test_dir_ / "patients_source1.csv").string(),
        (test_dir_ / "encounters_source2.json").string(),
        (test_dir_ / "lab_results.csv").string()
    };

    std::vector<std::unique_ptr<core::IExtractor>> extractors;

    // Create extractors based on file type
    for (size_t i = 0; i < test_files.size(); ++i) {
        const auto& file = test_files[i];
        try {
            std::unique_ptr<core::IExtractor> extractor;
            
            if (file.ends_with(".csv")) {
                extractor = std::make_unique<omop::extract::CsvExtractor>();
                std::unordered_map<std::string, std::any> config = {
                    {"filepath", file},
                    {"has_header", true}
                };
                
                core::ProcessingContext context;
                extractor->initialize(config, context);
                
            } else if (file.ends_with(".json")) {
                extractor = std::make_unique<omop::extract::JsonExtractor>();
                std::unordered_map<std::string, std::any> config = {
                    {"filepath", file},
                    {"json_path", std::string("$.encounters[*]")}
                };
                
                core::ProcessingContext context;
                extractor->initialize(config, context);
            }
            
            if (extractor) {
            logger_->info("Successfully created extractor for: {}", file);
            extractors.push_back(std::move(extractor));
            }
            
        } catch (const std::exception& e) {
            logger_->error("Failed to create extractor for {}: {}", file, e.what());
        }
    }

    EXPECT_EQ(extractors.size(), test_files.size());

    // Test extraction from all created extractors
    for (size_t i = 0; i < extractors.size(); ++i) {
        auto& extractor = extractors[i];
        core::ProcessingContext context;

        size_t record_count = 0;
        while (extractor->has_more_data()) {
            auto batch = extractor->extract_batch(10, context);
            record_count += batch.size();
        }

        logger_->info("Extractor {} extracted {} records", i, record_count);
        EXPECT_GT(record_count, 0);
    }
}

// Tests error handling and recovery in multi-source scenarios
TEST_F(MultiSourceExtractionIntegrationTest, ErrorHandlingAndRecovery) {
    // Create a mix of valid and invalid sources
    std::vector<std::pair<std::string, bool>> test_sources = {
        {(test_dir_ / "patients_source1.csv").string(), true},      // Valid CSV
        {(test_dir_ / "encounters_source2.json").string(), true},   // Valid JSON
        {(test_dir_ / "nonexistent.csv").string(), false},          // Invalid file
        {(test_dir_ / "lab_results.csv").string(), true}            // Valid CSV
    };

    std::vector<std::thread> threads;
    std::vector<size_t> successful_extractions(test_sources.size(), 0);
    std::vector<bool> expected_success(test_sources.size());

    for (size_t i = 0; i < test_sources.size(); ++i) {
        expected_success[i] = test_sources[i].second;
        
        threads.emplace_back([this, i, &test_sources, &successful_extractions]() {
            const auto& [file, should_succeed] = test_sources[i];
            
            try {
                if (file.ends_with(".csv")) {
                    omop::extract::CsvExtractor extractor;
                    std::unordered_map<std::string, std::any> config = {
                        {"filepath", file},
                        {"has_header", true}
                    };
                    
                    core::ProcessingContext context;
                    extractor.initialize(config, context);
                    
                    auto batch = extractor.extract_batch(10, context);
                    successful_extractions[i] = batch.size();
                    
                } else if (file.ends_with(".json")) {
                    omop::extract::JsonExtractor extractor;
                    std::unordered_map<std::string, std::any> config = {
                        {"filepath", file},
                        {"json_path", std::string("$.encounters[*]")}
                    };
                    
                    core::ProcessingContext context;
                    extractor.initialize(config, context);
                    
                    auto batch = extractor.extract_batch(10, context);
                    successful_extractions[i] = batch.size();
                }
                
            } catch (const std::exception& e) {
                logger_->warn("Expected failure for {}: {}", file, e.what());
                successful_extractions[i] = 0;
            }
        });
    }

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    // Verify results match expectations
    size_t successful_count = 0;
    for (size_t i = 0; i < test_sources.size(); ++i) {
        if (expected_success[i]) {
            EXPECT_GT(successful_extractions[i], 0) << "Expected success for " << test_sources[i].first;
            if (successful_extractions[i] > 0) successful_count++;
        } else {
            EXPECT_EQ(successful_extractions[i], 0) << "Expected failure for " << test_sources[i].first;
        }
    }

    EXPECT_EQ(successful_count, 3); // Should succeed for 3 valid files
}

// Tests performance comparison between sequential and parallel extraction
TEST_F(MultiSourceExtractionIntegrationTest, PerformanceComparison) {
    std::vector<std::string> test_files = {
        (test_dir_ / "patients_source1.csv").string(),
        (test_dir_ / "encounters_source2.json").string(),
        (test_dir_ / "lab_results.csv").string()
    };

    // Sequential extraction
    auto sequential_start = std::chrono::high_resolution_clock::now();
    size_t sequential_count = 0;

    for (const auto& file : test_files) {
        if (file.ends_with(".csv")) {
            omop::extract::CsvExtractor extractor;
            std::unordered_map<std::string, std::any> config = {
                {"filepath", file},
                {"has_header", true}
            };
            
        core::ProcessingContext context;
            extractor.initialize(config, context);

            while (extractor.has_more_data()) {
                auto batch = extractor.extract_batch(10, context);
                sequential_count += batch.size();
            }
            
        } else if (file.ends_with(".json")) {
            omop::extract::JsonExtractor extractor;
            std::unordered_map<std::string, std::any> config = {
                {"filepath", file},
                {"json_path", std::string("$.encounters[*]")}
            };
            
            core::ProcessingContext context;
            extractor.initialize(config, context);

            while (extractor.has_more_data()) {
                auto batch = extractor.extract_batch(10, context);
            sequential_count += batch.size();
            }
        }
    }

    auto sequential_end = std::chrono::high_resolution_clock::now();
    auto sequential_duration = std::chrono::duration_cast<std::chrono::milliseconds>(sequential_end - sequential_start);

    // Parallel extraction
    auto parallel_start = std::chrono::high_resolution_clock::now();
    std::vector<std::thread> threads;
    std::atomic<size_t> parallel_count{0};

    for (const auto& file : test_files) {
        threads.emplace_back([this, file, &parallel_count]() {
            size_t local_count = 0;
            
            if (file.ends_with(".csv")) {
                omop::extract::CsvExtractor extractor;
                std::unordered_map<std::string, std::any> config = {
                    {"filepath", file},
                    {"has_header", true}
                };
                
                core::ProcessingContext context;
                extractor.initialize(config, context);

                while (extractor.has_more_data()) {
                    auto batch = extractor.extract_batch(10, context);
                    local_count += batch.size();
                }
                
            } else if (file.ends_with(".json")) {
                omop::extract::JsonExtractor extractor;
                std::unordered_map<std::string, std::any> config = {
                    {"filepath", file},
                    {"json_path", std::string("$.encounters[*]")}
                };
                
                core::ProcessingContext context;
                extractor.initialize(config, context);

                while (extractor.has_more_data()) {
                    auto batch = extractor.extract_batch(10, context);
                    local_count += batch.size();
                }
            }
            
            parallel_count += local_count;
        });
    }

    for (auto& thread : threads) {
        thread.join();
    }

    auto parallel_end = std::chrono::high_resolution_clock::now();
    auto parallel_duration = std::chrono::duration_cast<std::chrono::milliseconds>(parallel_end - parallel_start);

    // Verify same number of records
    EXPECT_EQ(sequential_count, parallel_count.load());

    logger_->info("Performance comparison:");
    logger_->info("  Sequential: {} records in {} ms", sequential_count, sequential_duration.count());
    logger_->info("  Parallel:   {} records in {} ms", parallel_count.load(), parallel_duration.count());
    
    if (sequential_duration.count() > 0) {
        double speedup = static_cast<double>(sequential_duration.count()) / parallel_duration.count();
        logger_->info("  Speedup:    {:.2f}x", speedup);
    }
}

// Tests cross-format data consistency validation
TEST_F(MultiSourceExtractionIntegrationTest, CrossExtractorDataConsistency) {
    // Extract same conceptual data from different formats
    std::vector<core::Record> csv_records;
    std::vector<core::Record> json_records;

    // Extract from CSV
    {
        omop::extract::CsvExtractor extractor;
        std::unordered_map<std::string, std::any> config = {
            {"filepath", (test_dir_ / "patients_source1.csv").string()},
            {"has_header", true}
        };
        
        core::ProcessingContext context;
        extractor.initialize(config, context);

        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(10, context);
            auto records = batch.getRecords();
            csv_records.insert(csv_records.end(), records.begin(), records.end());
        }
    }

    // Extract from JSON
    {
        omop::extract::JsonExtractor extractor;
        std::unordered_map<std::string, std::any> config = {
            {"filepath", (test_dir_ / "encounters_source2.json").string()},
            {"json_path", std::string("$.encounters[*]")}
        };
        
        core::ProcessingContext context;
        extractor.initialize(config, context);

        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(10, context);
            auto records = batch.getRecords();
            json_records.insert(json_records.end(), records.begin(), records.end());
        }
    }

    // Verify we got records from both sources
    EXPECT_GT(csv_records.size(), 0);
    EXPECT_GT(json_records.size(), 0);

    // Verify data structure consistency for CSV
    for (const auto& record : csv_records) {
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("first_name"));
        EXPECT_TRUE(record.hasField("last_name"));
    }

    // Verify data structure consistency for JSON
    for (const auto& record : json_records) {
        EXPECT_TRUE(record.hasField("encounter_id"));
        EXPECT_TRUE(record.hasField("patient_ref"));
        EXPECT_TRUE(record.hasField("type"));
    }

    logger_->info("Data consistency verified:");
    logger_->info("  CSV records: {}", csv_records.size());
    logger_->info("  JSON records: {}", json_records.size());
}

} // namespace omop::test