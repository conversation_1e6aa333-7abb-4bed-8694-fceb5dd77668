# tests/CMakeLists.txt - Root test configuration coordinator

# Enable testing for this directory and subdirectories
enable_testing()

# Add unit tests subdirectory
add_subdirectory(unit)

# Add integration tests subdirectory if enabled
if(BUILD_INTEGRATION_TESTS)
    add_subdirectory(integration)
endif()

# Create comprehensive test targets

# Main comprehensive test target - runs all tests
add_custom_target(test_all_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
    COMMENT "Running all tests (unit + integration)"
)

# Unit tests only target
add_custom_target(test_unit_only
    COMMAND ${CMAKE_CTEST_COMMAND} -L unit --output-on-failure
    COMMENT "Running unit tests only"
)

# Integration tests only target (if enabled)
if(BUILD_INTEGRATION_TESTS)
    add_custom_target(test_integration_only
        COMMAND ${CMAKE_CTEST_COMMAND} -L integration --output-on-failure
        COMMENT "Running integration tests only"
    )
endif()

# Legacy target for compatibility
add_custom_target(run_all_tests
    DEPENDS test_all_tests
    COMMENT "Legacy compatibility target for all tests"
)

# Test configuration summary
message(STATUS "Test Configuration:")
message(STATUS "  Unit tests: ${BUILD_TESTS}")
message(STATUS "  Integration tests: ${BUILD_INTEGRATION_TESTS}")
message(STATUS "  Coverage reporting: ${ENABLE_COVERAGE}")
message(STATUS "  Memory checking: ${ENABLE_VALGRIND}")
