#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <future>
#include <chrono>

#include "etl_service.h"

namespace omop::etl {

/**
 * @brief ETL service client configuration
 */
struct ClientConfig {
    std::string service_endpoint;
    std::chrono::seconds connection_timeout{30};
    std::chrono::seconds request_timeout{300};
    bool enable_ssl{false};
    std::string ssl_cert_path;
    std::string ssl_key_path;
    std::string ssl_ca_path;
    std::unordered_map<std::string, std::string> headers;
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief ETL service client interface
 * 
 * This interface defines the contract for ETL service clients that can
 * communicate with remote ETL services to submit jobs, monitor status,
 * and retrieve results.
 */
class IETLServiceClient {
public:
    virtual ~IETLServiceClient() = default;

    /**
     * @brief Initialize the ETL service client
     * @param config Client configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const ClientConfig& config) = 0;

    /**
     * @brief Connect to ETL service
     * @return bool True if connection successful
     */
    virtual bool connect() = 0;

    /**
     * @brief Disconnect from ETL service
     * @return bool True if disconnection successful
     */
    virtual bool disconnect() = 0;

    /**
     * @brief Check if client is connected
     * @return bool True if connected
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Get service status
     * @return ServiceStatus Current service status
     */
    virtual ServiceStatus get_service_status() = 0;

    /**
     * @brief Submit ETL job for execution
     * @param job_name Job name
     * @param pipeline_config Pipeline configuration
     * @return std::string Job ID
     */
    virtual std::string submit_job(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) = 0;

    /**
     * @brief Submit ETL job asynchronously
     * @param job_name Job name
     * @param pipeline_config Pipeline configuration
     * @return std::future<std::string> Future containing job ID
     */
    virtual std::future<std::string> submit_job_async(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) = 0;

    /**
     * @brief Cancel running job
     * @param job_id Job ID to cancel
     * @return bool True if job was cancelled
     */
    virtual bool cancel_job(const std::string& job_id) = 0;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if exists
     */
    virtual std::optional<JobInfo> get_job_info(const std::string& job_id) = 0;

    /**
     * @brief Get all job information
     * @return std::vector<JobInfo> List of all jobs
     */
    virtual std::vector<JobInfo> get_all_jobs() = 0;

    /**
     * @brief Get running jobs
     * @return std::vector<JobInfo> List of running jobs
     */
    virtual std::vector<JobInfo> get_running_jobs() = 0;

    /**
     * @brief Wait for job completion
     * @param job_id Job ID to wait for
     * @param timeout Timeout duration
     * @return JobStatus Final job status
     */
    virtual JobStatus wait_for_job(
        const std::string& job_id,
        std::chrono::seconds timeout = std::chrono::seconds(0)) = 0;

    /**
     * @brief Get service statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_service_statistics() = 0;

    /**
     * @brief Get service health status
     * @return bool True if service is healthy
     */
    virtual bool is_service_healthy() = 0;

    /**
     * @brief Get service configuration
     * @return ServiceConfig Current service configuration
     */
    virtual ServiceConfig get_service_config() = 0;

    /**
     * @brief Update service configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_service_config(const ServiceConfig& config) = 0;

    /**
     * @brief Register job event callback
     * @param callback Callback function for job events
     */
    virtual void register_job_callback(
        std::function<void(const JobInfo&)> callback) = 0;

    /**
     * @brief Get client configuration
     * @return ClientConfig Current client configuration
     */
    virtual ClientConfig get_client_config() const = 0;

    /**
     * @brief Update client configuration
     * @param config New client configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_client_config(const ClientConfig& config) = 0;
};

/**
 * @brief HTTP-based ETL service client
 * 
 * Implementation of ETL service client that communicates with
 * ETL services over HTTP/HTTPS using REST API.
 */
class HTTPETLServiceClient : public IETLServiceClient {
public:
    HTTPETLServiceClient() = default;
    ~HTTPETLServiceClient() override = default;

    bool initialize(const ClientConfig& config) override;
    bool connect() override;
    bool disconnect() override;
    bool is_connected() const override;

    ServiceStatus get_service_status() override;

    std::string submit_job(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) override;

    std::future<std::string> submit_job_async(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) override;

    bool cancel_job(const std::string& job_id) override;
    std::optional<JobInfo> get_job_info(const std::string& job_id) override;
    std::vector<JobInfo> get_all_jobs() override;
    std::vector<JobInfo> get_running_jobs() override;

    JobStatus wait_for_job(
        const std::string& job_id,
        std::chrono::seconds timeout = std::chrono::seconds(0)) override;

    std::unordered_map<std::string, std::any> get_service_statistics() override;
    bool is_service_healthy() override;
    ServiceConfig get_service_config() override;
    bool update_service_config(const ServiceConfig& config) override;

    void register_job_callback(
        std::function<void(const JobInfo&)> callback) override;

    ClientConfig get_client_config() const override;
    bool update_client_config(const ClientConfig& config) override;
};

/**
 * @brief gRPC-based ETL service client
 * 
 * Implementation of ETL service client that communicates with
 * ETL services over gRPC protocol.
 */
class GRPCETLServiceClient : public IETLServiceClient {
public:
    GRPCETLServiceClient() = default;
    ~GRPCETLServiceClient() override = default;

    bool initialize(const ClientConfig& config) override;
    bool connect() override;
    bool disconnect() override;
    bool is_connected() const override;

    ServiceStatus get_service_status() override;

    std::string submit_job(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) override;

    std::future<std::string> submit_job_async(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) override;

    bool cancel_job(const std::string& job_id) override;
    std::optional<JobInfo> get_job_info(const std::string& job_id) override;
    std::vector<JobInfo> get_all_jobs() override;
    std::vector<JobInfo> get_running_jobs() override;

    JobStatus wait_for_job(
        const std::string& job_id,
        std::chrono::seconds timeout = std::chrono::seconds(0)) override;

    std::unordered_map<std::string, std::any> get_service_statistics() override;
    bool is_service_healthy() override;
    ServiceConfig get_service_config() override;
    bool update_service_config(const ServiceConfig& config) override;

    void register_job_callback(
        std::function<void(const JobInfo&)> callback) override;

    ClientConfig get_client_config() const override;
    bool update_client_config(const ClientConfig& config) override;
};

/**
 * @brief Create HTTP ETL service client
 * @return std::unique_ptr<IETLServiceClient> HTTP client instance
 */
std::unique_ptr<IETLServiceClient> create_http_etl_client();

/**
 * @brief Create gRPC ETL service client
 * @return std::unique_ptr<IETLServiceClient> gRPC client instance
 */
std::unique_ptr<IETLServiceClient> create_grpc_etl_client();

/**
 * @brief Get default client configuration
 * @return ClientConfig Default configuration
 */
ClientConfig get_default_client_config();

} // namespace omop::etl