#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <future>

#include "common/configuration.h"
#include "core/interfaces.h"
#include "core/pipeline.h"

namespace omop::etl {

/**
 * @brief ETL service status enumeration
 */
enum class ServiceStatus {
    Stopped,
    Starting,
    Running,
    Stopping,
    Failed
};

/**
 * @brief ETL job status enumeration
 */
enum class JobStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled
};

/**
 * @brief ETL job information
 */
struct JobInfo {
    std::string id;
    std::string name;
    JobStatus status;
    size_t processed_records{0};
    size_t failed_records{0};
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point started_at;
    std::chrono::system_clock::time_point completed_at;
    std::string error_message;
    std::unordered_map<std::string, std::any> metadata;
};

/**
 * @brief ETL service configuration
 */
struct ServiceConfig {
    size_t max_concurrent_jobs{4};
    size_t default_batch_size{1000};
    std::chrono::seconds job_timeout{3600};
    std::string storage_path;
    bool enable_monitoring{true};
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief ETL service interface
 * 
 * This interface defines the contract for ETL services that can execute
 * ETL jobs, manage job lifecycle, and provide monitoring capabilities.
 */
class IETLService {
public:
    virtual ~IETLService() = default;

    /**
     * @brief Initialize the ETL service
     * @param config Service configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const ServiceConfig& config) = 0;

    /**
     * @brief Start the ETL service
     * @return bool True if service started successfully
     */
    virtual bool start() = 0;

    /**
     * @brief Stop the ETL service
     * @return bool True if service stopped successfully
     */
    virtual bool stop() = 0;

    /**
     * @brief Get current service status
     * @return ServiceStatus Current status
     */
    virtual ServiceStatus get_status() const = 0;

    /**
     * @brief Submit ETL job for execution
     * @param job_name Job name
     * @param pipeline_config Pipeline configuration
     * @return std::string Job ID
     */
    virtual std::string submit_job(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) = 0;

    /**
     * @brief Cancel running job
     * @param job_id Job ID to cancel
     * @return bool True if job was cancelled
     */
    virtual bool cancel_job(const std::string& job_id) = 0;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if exists
     */
    virtual std::optional<JobInfo> get_job_info(const std::string& job_id) = 0;

    /**
     * @brief Get all job information
     * @return std::vector<JobInfo> List of all jobs
     */
    virtual std::vector<JobInfo> get_all_jobs() = 0;

    /**
     * @brief Get running jobs
     * @return std::vector<JobInfo> List of running jobs
     */
    virtual std::vector<JobInfo> get_running_jobs() = 0;

    /**
     * @brief Wait for job completion
     * @param job_id Job ID to wait for
     * @param timeout Timeout duration
     * @return JobStatus Final job status
     */
    virtual JobStatus wait_for_job(
        const std::string& job_id,
        std::chrono::seconds timeout = std::chrono::seconds(0)) = 0;

    /**
     * @brief Get service statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Register job event callback
     * @param callback Callback function for job events
     */
    virtual void register_job_callback(
        std::function<void(const JobInfo&)> callback) = 0;

    /**
     * @brief Get service health status
     * @return bool True if service is healthy
     */
    virtual bool is_healthy() const = 0;

    /**
     * @brief Get service configuration
     * @return ServiceConfig Current configuration
     */
    virtual ServiceConfig get_config() const = 0;

    /**
     * @brief Update service configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const ServiceConfig& config) = 0;
};

/**
 * @brief ETL service implementation
 * 
 * Default implementation of the ETL service interface that provides
 * job management, pipeline execution, and monitoring capabilities.
 */
class ETLService : public IETLService {
public:
    ETLService() = default;
    ~ETLService() override = default;

    bool initialize(const ServiceConfig& config) override;
    bool start() override;
    bool stop() override;
    ServiceStatus get_status() const override;

    std::string submit_job(
        const std::string& job_name,
        const omop::common::PipelineConfig& pipeline_config) override;

    bool cancel_job(const std::string& job_id) override;
    std::optional<JobInfo> get_job_info(const std::string& job_id) override;
    std::vector<JobInfo> get_all_jobs() override;
    std::vector<JobInfo> get_running_jobs() override;

    JobStatus wait_for_job(
        const std::string& job_id,
        std::chrono::seconds timeout = std::chrono::seconds(0)) override;

    std::unordered_map<std::string, std::any> get_statistics() override;

    void register_job_callback(
        std::function<void(const JobInfo&)> callback) override;

    bool is_healthy() const override;
    ServiceConfig get_config() const override;
    bool update_config(const ServiceConfig& config) override;
};

/**
 * @brief Create ETL service instance
 * @return std::unique_ptr<IETLService> ETL service instance
 */
std::unique_ptr<IETLService> create_etl_service();

/**
 * @brief Get default service configuration
 * @return ServiceConfig Default configuration
 */
ServiceConfig get_default_service_config();

} // namespace omop::etl