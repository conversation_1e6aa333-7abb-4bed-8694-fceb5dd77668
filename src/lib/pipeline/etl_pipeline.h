#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <future>
#include <chrono>

#include "core/interfaces.h"
#include "core/pipeline.h"
#include "common/configuration.h"
#include "common/metrics_collector.h"

namespace omop::pipeline {

/**
 * @brief Pipeline stage enumeration
 */
enum class PipelineStage {
    Extract,
    Transform,
    Load,
    Validate,
    Monitor,
    Cleanup
};

/**
 * @brief Pipeline execution mode
 */
enum class ExecutionMode {
    Sequential,
    Parallel,
    Streaming,
    Batch,
    Hybrid
};

/**
 * @brief Pipeline status enumeration
 */
enum class PipelineStatus {
    Created,
    Initializing,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled,
    Cleanup
};

/**
 * @brief Pipeline execution statistics
 */
struct PipelineExecutionStats {
    std::string pipeline_id;
    PipelineStatus status{PipelineStatus::Created};
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    std::chrono::duration<double> total_duration{0};
    std::unordered_map<PipelineStage, std::chrono::duration<double>> stage_durations;
    size_t total_records_processed{0};
    size_t successful_records{0};
    size_t failed_records{0};
    size_t skipped_records{0};
    std::unordered_map<PipelineStage, size_t> records_per_stage;
    double throughput_records_per_second{0.0};
    size_t memory_peak_bytes{0};
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    std::unordered_map<std::string, std::any> custom_metrics;
};

/**
 * @brief Pipeline configuration
 */
struct ETLPipelineConfig {
    std::string pipeline_id;
    std::string pipeline_name;
    ExecutionMode execution_mode{ExecutionMode::Sequential};
    size_t batch_size{1000};
    size_t max_parallel_stages{4};
    std::chrono::seconds stage_timeout{300};
    std::chrono::seconds pipeline_timeout{3600};
    bool enable_checkpointing{false};
    std::string checkpoint_directory;
    bool enable_retry{true};
    size_t max_retry_attempts{3};
    std::chrono::seconds retry_delay{30};
    bool continue_on_error{false};
    double error_threshold{0.1}; // 10% error threshold
    bool enable_monitoring{true};
    bool enable_profiling{false};
    std::vector<PipelineStage> stages;
    omop::common::PipelineConfig core_config;
    std::unordered_map<std::string, std::any> stage_configs;
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Pipeline stage interface
 */
class IPipelineStage {
public:
    virtual ~IPipelineStage() = default;

    /**
     * @brief Get stage type
     * @return PipelineStage Stage type
     */
    virtual PipelineStage get_stage_type() const = 0;

    /**
     * @brief Initialize stage
     * @param config Stage configuration
     * @param context Processing context
     * @return bool True if initialization successful
     */
    virtual bool initialize(
        const std::unordered_map<std::string, std::any>& config,
        omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Execute stage
     * @param input Input data
     * @param context Processing context
     * @return std::any Output data
     */
    virtual std::any execute(
        const std::any& input,
        omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Finalize stage
     * @param context Processing context
     * @return bool True if finalization successful
     */
    virtual bool finalize(omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Get stage statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;

    /**
     * @brief Check if stage supports parallel execution
     * @return bool True if supports parallel execution
     */
    virtual bool supports_parallel_execution() const = 0;

    /**
     * @brief Check if stage supports streaming
     * @return bool True if supports streaming
     */
    virtual bool supports_streaming() const = 0;
};

/**
 * @brief ETL pipeline interface
 * 
 * This interface defines the contract for ETL pipelines that orchestrate
 * the complete data processing workflow from extraction to loading.
 */
class IETLPipeline {
public:
    virtual ~IETLPipeline() = default;

    /**
     * @brief Initialize pipeline
     * @param config Pipeline configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const ETLPipelineConfig& config) = 0;

    /**
     * @brief Start pipeline execution
     * @return std::string Pipeline execution ID
     */
    virtual std::string start() = 0;

    /**
     * @brief Start pipeline execution asynchronously
     * @return std::future<std::string> Future containing execution ID
     */
    virtual std::future<std::string> start_async() = 0;

    /**
     * @brief Pause pipeline execution
     * @return bool True if paused successfully
     */
    virtual bool pause() = 0;

    /**
     * @brief Resume pipeline execution
     * @return bool True if resumed successfully
     */
    virtual bool resume() = 0;

    /**
     * @brief Stop pipeline execution
     * @param force Force stop without cleanup
     * @return bool True if stopped successfully
     */
    virtual bool stop(bool force = false) = 0;

    /**
     * @brief Wait for pipeline completion
     * @param timeout Timeout duration
     * @return PipelineStatus Final status
     */
    virtual PipelineStatus wait_for_completion(
        std::chrono::seconds timeout = std::chrono::seconds(0)) = 0;

    /**
     * @brief Get pipeline status
     * @return PipelineStatus Current status
     */
    virtual PipelineStatus get_status() const = 0;

    /**
     * @brief Get execution statistics
     * @return PipelineExecutionStats Current statistics
     */
    virtual PipelineExecutionStats get_execution_stats() const = 0;

    /**
     * @brief Add pipeline stage
     * @param stage Pipeline stage
     * @return bool True if stage added successfully
     */
    virtual bool add_stage(std::unique_ptr<IPipelineStage> stage) = 0;

    /**
     * @brief Remove pipeline stage
     * @param stage_type Stage type to remove
     * @return bool True if stage removed successfully
     */
    virtual bool remove_stage(PipelineStage stage_type) = 0;

    /**
     * @brief Get pipeline stages
     * @return std::vector<PipelineStage> List of stages
     */
    virtual std::vector<PipelineStage> get_stages() const = 0;

    /**
     * @brief Set metrics collector
     * @param metrics_collector Metrics collector instance
     */
    virtual void set_metrics_collector(
        std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) = 0;

    /**
     * @brief Register progress callback
     * @param callback Progress callback function
     */
    virtual void register_progress_callback(
        std::function<void(const PipelineExecutionStats&)> callback) = 0;

    /**
     * @brief Register completion callback
     * @param callback Completion callback function
     */
    virtual void register_completion_callback(
        std::function<void(const PipelineExecutionStats&)> callback) = 0;

    /**
     * @brief Register error callback
     * @param callback Error callback function
     */
    virtual void register_error_callback(
        std::function<void(const std::string&, const PipelineExecutionStats&)> callback) = 0;

    /**
     * @brief Create checkpoint
     * @param checkpoint_name Checkpoint name
     * @return bool True if checkpoint created successfully
     */
    virtual bool create_checkpoint(const std::string& checkpoint_name) = 0;

    /**
     * @brief Restore from checkpoint
     * @param checkpoint_name Checkpoint name
     * @return bool True if restored successfully
     */
    virtual bool restore_from_checkpoint(const std::string& checkpoint_name) = 0;

    /**
     * @brief Get pipeline configuration
     * @return ETLPipelineConfig Current configuration
     */
    virtual ETLPipelineConfig get_config() const = 0;

    /**
     * @brief Update pipeline configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const ETLPipelineConfig& config) = 0;
};

/**
 * @brief Default ETL pipeline implementation
 */
class ETLPipeline : public IETLPipeline {
public:
    ETLPipeline() = default;
    ~ETLPipeline() override = default;

    bool initialize(const ETLPipelineConfig& config) override;
    std::string start() override;
    std::future<std::string> start_async() override;
    bool pause() override;
    bool resume() override;
    bool stop(bool force = false) override;

    PipelineStatus wait_for_completion(
        std::chrono::seconds timeout = std::chrono::seconds(0)) override;

    PipelineStatus get_status() const override;
    PipelineExecutionStats get_execution_stats() const override;

    bool add_stage(std::unique_ptr<IPipelineStage> stage) override;
    bool remove_stage(PipelineStage stage_type) override;
    std::vector<PipelineStage> get_stages() const override;

    void set_metrics_collector(
        std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) override;

    void register_progress_callback(
        std::function<void(const PipelineExecutionStats&)> callback) override;

    void register_completion_callback(
        std::function<void(const PipelineExecutionStats&)> callback) override;

    void register_error_callback(
        std::function<void(const std::string&, const PipelineExecutionStats&)> callback) override;

    bool create_checkpoint(const std::string& checkpoint_name) override;
    bool restore_from_checkpoint(const std::string& checkpoint_name) override;

    ETLPipelineConfig get_config() const override;
    bool update_config(const ETLPipelineConfig& config) override;
};

/**
 * @brief Pipeline stage implementations
 */

/**
 * @brief Extract stage
 */
class ExtractStage : public IPipelineStage {
public:
    PipelineStage get_stage_type() const override { return PipelineStage::Extract; }
    bool initialize(const std::unordered_map<std::string, std::any>& config, omop::core::ProcessingContext& context) override;
    std::any execute(const std::any& input, omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return true; }
};

/**
 * @brief Transform stage
 */
class TransformStage : public IPipelineStage {
public:
    PipelineStage get_stage_type() const override { return PipelineStage::Transform; }
    bool initialize(const std::unordered_map<std::string, std::any>& config, omop::core::ProcessingContext& context) override;
    std::any execute(const std::any& input, omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return true; }
};

/**
 * @brief Load stage
 */
class LoadStage : public IPipelineStage {
public:
    PipelineStage get_stage_type() const override { return PipelineStage::Load; }
    bool initialize(const std::unordered_map<std::string, std::any>& config, omop::core::ProcessingContext& context) override;
    std::any execute(const std::any& input, omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return false; }
};

/**
 * @brief Pipeline factory
 */
class ETLPipelineFactory {
public:
    /**
     * @brief Create standard ETL pipeline
     * @param config Pipeline configuration
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_standard_pipeline(
        const ETLPipelineConfig& config);

    /**
     * @brief Create streaming pipeline
     * @param config Pipeline configuration
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_streaming_pipeline(
        const ETLPipelineConfig& config);

    /**
     * @brief Create parallel pipeline
     * @param config Pipeline configuration
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_parallel_pipeline(
        const ETLPipelineConfig& config);

    /**
     * @brief Create custom pipeline
     * @param config Pipeline configuration
     * @param stages Custom stages
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_custom_pipeline(
        const ETLPipelineConfig& config,
        std::vector<std::unique_ptr<IPipelineStage>> stages);
};

/**
 * @brief Pipeline utilities
 */
class PipelineUtils {
public:
    /**
     * @brief Convert pipeline status to string
     * @param status Pipeline status
     * @return std::string Status string
     */
    static std::string status_to_string(PipelineStatus status);

    /**
     * @brief Convert string to pipeline status
     * @param status_str Status string
     * @return PipelineStatus Pipeline status
     */
    static PipelineStatus string_to_status(const std::string& status_str);

    /**
     * @brief Convert stage to string
     * @param stage Pipeline stage
     * @return std::string Stage string
     */
    static std::string stage_to_string(PipelineStage stage);

    /**
     * @brief Convert string to stage
     * @param stage_str Stage string
     * @return PipelineStage Pipeline stage
     */
    static PipelineStage string_to_stage(const std::string& stage_str);

    /**
     * @brief Convert execution mode to string
     * @param mode Execution mode
     * @return std::string Mode string
     */
    static std::string execution_mode_to_string(ExecutionMode mode);

    /**
     * @brief Convert string to execution mode
     * @param mode_str Mode string
     * @return ExecutionMode Execution mode
     */
    static ExecutionMode string_to_execution_mode(const std::string& mode_str);

    /**
     * @brief Validate pipeline configuration
     * @param config Configuration to validate
     * @return std::vector<std::string> Validation errors
     */
    static std::vector<std::string> validate_config(const ETLPipelineConfig& config);

    /**
     * @brief Optimize pipeline configuration
     * @param config Base configuration
     * @param system_info System information
     * @param performance_requirements Performance requirements
     * @return ETLPipelineConfig Optimized configuration
     */
    static ETLPipelineConfig optimize_config(
        const ETLPipelineConfig& config,
        const std::unordered_map<std::string, std::any>& system_info,
        const std::unordered_map<std::string, std::any>& performance_requirements);
};

/**
 * @brief Create ETL pipeline instance
 * @return std::unique_ptr<IETLPipeline> Pipeline instance
 */
std::unique_ptr<IETLPipeline> create_etl_pipeline();

/**
 * @brief Get default pipeline configuration
 * @return ETLPipelineConfig Default configuration
 */
ETLPipelineConfig get_default_pipeline_config();

} // namespace omop::pipeline