# Service library

set(SERVICE_SOURCES
    etl_service.cpp
    # service.cpp # Deprecated
    extract_service.cpp
    transform_service.cpp
    load_service.cpp
    service_orchestrator.cpp
    microservice_interfaces.cpp
)

# add_library(omop_service ${SERVICE_SOURCES})

# target_include_directories(omop_service
#     PUBLIC
#         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
#         $<INSTALL_INTERFACE:include>
#     PRIVATE
#         ${CMAKE_CURRENT_SOURCE_DIR}
# )

# target_link_libraries(omop_service
#     PUBLIC
#         omop_cdm
#         omop_common
#         omop_core
#         omop_extract
#         omop_transform
#         omop_load
#         spdlog::spdlog
#         nlohmann_json::nlohmann_json
# )

# Add gRPC dependencies if available
# find_package(gRPC CONFIG)
# if(gRPC_FOUND)
#     target_link_libraries(omop_service PUBLIC gRPC::grpc++)
# endif()

# Build tests if enabled
if(BUILD_TESTING)
    add_subdirectory(tests)
endif()