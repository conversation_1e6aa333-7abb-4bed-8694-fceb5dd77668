#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <future>
#include <functional>
#include <chrono>
#ifdef OMOP_HAS_GRPC
#include <grpcpp/grpcpp.h>
#endif

namespace omop::service {

/**
 * @brief Service request/response base types
 */
struct ServiceRequest {
    std::string request_id;
    std::string job_id;
    std::unordered_map<std::string, std::any> parameters;
    std::chrono::system_clock::time_point timestamp;
};

struct ServiceResponse {
    std::string request_id;
    bool success{true};
    std::string error_message;
    std::unordered_map<std::string, std::any> data;
    std::chrono::system_clock::time_point timestamp;
};

/**
 * @brief Extract service request/response
 */
struct ExtractRequest : ServiceRequest {
    std::string source_type;
    std::string source_table;
    std::unordered_map<std::string, std::any> extractor_config;
    size_t batch_size{1000};
    size_t offset{0};
};

struct ExtractResponse : ServiceResponse {
    core::RecordBatch records;
    size_t total_records{0};
    bool has_more_data{false};
    std::unordered_map<std::string, std::any> statistics;
};

/**
 * @brief Transform service request/response
 */
struct TransformRequest : ServiceRequest {
    core::RecordBatch input_records;
    std::string target_table;
    std::unordered_map<std::string, std::any> transformation_config;
};

struct TransformResponse : ServiceResponse {
    core::RecordBatch transformed_records;
    size_t successful_transformations{0};
    size_t failed_transformations{0};
    std::vector<std::string> validation_errors;
};

/**
 * @brief Load service request/response
 */
struct LoadRequest : ServiceRequest {
    core::RecordBatch records;
    std::string target_type;
    std::string target_table;
    std::unordered_map<std::string, std::any> loader_config;
    bool commit{true};
};

struct LoadResponse : ServiceResponse {
    size_t records_loaded{0};
    size_t records_failed{0};
    std::unordered_map<std::string, std::any> statistics;
};

/**
 * @brief Service health check
 */
struct HealthCheckRequest {
    std::string service_name;
};

struct HealthCheckResponse {
    bool healthy{true};
    std::string status;
    std::unordered_map<std::string, std::any> metrics;
};

/**
 * @brief Extract service interface
 */
class IExtractService {
public:
    virtual ~IExtractService() = default;

    /**
     * @brief Extract data batch
     * @param request Extract request
     * @return std::future<ExtractResponse> Extract response
     */
    virtual std::future<ExtractResponse> extract(const ExtractRequest& request) = 0;

    /**
     * @brief Initialize extraction source
     * @param request Initialization request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> initialize_source(const ServiceRequest& request) = 0;

    /**
     * @brief Finalize extraction source
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> finalize_source(const ServiceRequest& request) = 0;

    /**
     * @brief Get extraction statistics
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Statistics response
     */
    virtual std::future<ServiceResponse> get_statistics(const std::string& job_id) = 0;

    /**
     * @brief Health check
     * @param request Health check request
     * @return std::future<HealthCheckResponse> Health response
     */
    virtual std::future<HealthCheckResponse> health_check(const HealthCheckRequest& request) = 0;
};

/**
 * @brief Transform service interface
 */
class ITransformService {
public:
    virtual ~ITransformService() = default;

    /**
     * @brief Transform data batch
     * @param request Transform request
     * @return std::future<TransformResponse> Transform response
     */
    virtual std::future<TransformResponse> transform(const TransformRequest& request) = 0;

    /**
     * @brief Validate transformation configuration
     * @param request Validation request
     * @return std::future<ServiceResponse> Validation response
     */
    virtual std::future<ServiceResponse> validate_config(const ServiceRequest& request) = 0;

    /**
     * @brief Load vocabulary mappings
     * @param request Load request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> load_vocabularies(const ServiceRequest& request) = 0;

    /**
     * @brief Get transformation statistics
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Statistics response
     */
    virtual std::future<ServiceResponse> get_statistics(const std::string& job_id) = 0;

    /**
     * @brief Health check
     * @param request Health check request
     * @return std::future<HealthCheckResponse> Health response
     */
    virtual std::future<HealthCheckResponse> health_check(const HealthCheckRequest& request) = 0;
};

/**
 * @brief Load service interface
 */
class ILoadService {
public:
    virtual ~ILoadService() = default;

    /**
     * @brief Load data batch
     * @param request Load request
     * @return std::future<LoadResponse> Load response
     */
    virtual std::future<LoadResponse> load(const LoadRequest& request) = 0;

    /**
     * @brief Initialize loader
     * @param request Initialization request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> initialize_loader(const ServiceRequest& request) = 0;

    /**
     * @brief Finalize loader
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> finalize_loader(const ServiceRequest& request) = 0;

    /**
     * @brief Initialize target
     * @param request Initialization request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> initialize_target(const ServiceRequest& request) = 0;

    /**
     * @brief Finalize target
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> finalize_target(const ServiceRequest& request) = 0;

    /**
     * @brief Commit transaction
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> commit(const std::string& job_id) = 0;

    /**
     * @brief Rollback transaction
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Response
     */
    virtual std::future<ServiceResponse> rollback(const std::string& job_id) = 0;

    /**
     * @brief Get loading statistics
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Statistics response
     */
    virtual std::future<ServiceResponse> get_statistics(const std::string& job_id) = 0;

    /**
     * @brief Health check
     * @param request Health check request
     * @return std::future<HealthCheckResponse> Health response
     */
    virtual std::future<HealthCheckResponse> health_check(const HealthCheckRequest& request) = 0;
};

/**
 * @brief Service discovery interface
 */
class IServiceDiscovery {
public:
    virtual ~IServiceDiscovery() = default;

    /**
     * @brief Register service
     * @param service_name Service name
     * @param endpoint Service endpoint
     * @param metadata Service metadata
     */
    virtual void register_service(const std::string& service_name,
                                const std::string& endpoint,
                                const std::unordered_map<std::string, std::string>& metadata) = 0;

    /**
     * @brief Unregister service
     * @param service_name Service name
     * @param endpoint Service endpoint
     */
    virtual void unregister_service(const std::string& service_name,
                                  const std::string& endpoint) = 0;

    /**
     * @brief Discover service endpoints
     * @param service_name Service name
     * @return std::vector<std::string> Service endpoints
     */
    virtual std::vector<std::string> discover_endpoints(const std::string& service_name) = 0;

    /**
     * @brief Get service metadata
     * @param service_name Service name
     * @param endpoint Service endpoint
     * @return std::unordered_map<std::string, std::string> Metadata
     */
    virtual std::unordered_map<std::string, std::string> get_metadata(
        const std::string& service_name,
        const std::string& endpoint) = 0;

    /**
     * @brief Subscribe to service changes
     * @param service_name Service name
     * @param callback Callback for service changes
     */
    virtual void subscribe(const std::string& service_name,
                         std::function<void(const std::string&, const std::vector<std::string>&)> callback) = 0;
};

/**
 * @brief Service client factory
 */
class ServiceClientFactory {
public:
    /**
     * @brief Create extract service client
     * @param endpoint Service endpoint
     * @return std::unique_ptr<IExtractService> Service client
     */
    static std::unique_ptr<IExtractService> create_extract_client(const std::string& endpoint);

    /**
     * @brief Create transform service client
     * @param endpoint Service endpoint
     * @return std::unique_ptr<ITransformService> Service client
     */
    static std::unique_ptr<ITransformService> create_transform_client(const std::string& endpoint);

    /**
     * @brief Create load service client
     * @param endpoint Service endpoint
     * @return std::unique_ptr<ILoadService> Service client
     */
    static std::unique_ptr<ILoadService> create_load_client(const std::string& endpoint);
};

/**
 * @brief Circuit breaker for service calls
 */
class CircuitBreaker {
public:
    /**
     * @brief Constructor
     * @param failure_threshold Number of failures before opening
     * @param success_threshold Number of successes before closing
     * @param timeout Timeout duration in open state
     */
    CircuitBreaker(size_t failure_threshold = 5,
                   size_t success_threshold = 2,
                   std::chrono::seconds timeout = std::chrono::seconds(60));

    /**
     * @brief Execute function with circuit breaker
     * @tparam T Return type
     * @param func Function to execute
     * @return T Function result
     * @throws std::runtime_error if circuit is open
     */
    template<typename T>
    T execute(std::function<T()> func);

    /**
     * @brief Record success
     */
    void record_success();

    /**
     * @brief Record failure
     */
    void record_failure();

private:
    enum class State { Closed, Open, HalfOpen };
    
    State state_{State::Closed};
    size_t failure_threshold_;
    size_t success_threshold_;
    std::chrono::seconds timeout_;
    size_t failure_count_{0};
    size_t success_count_{0};
    std::chrono::steady_clock::time_point last_failure_time_;
    mutable std::mutex mutex_;
};

} // namespace omop::service
