#pragma once

#include "service/microservice_interfaces.h"
#include "extract/extractor_factory.h"
#include "common/logging.h"
#include <unordered_map>
#include <mutex>
#include <memory>

namespace omop::service {

/**
 * @brief Extract microservice implementation
 *
 * This service handles all data extraction operations independently.
 * It manages extractor instances, handles batch extraction, and provides
 * extraction statistics.
 */
class ExtractService : public IExtractService {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     */
    explicit ExtractService(std::shared_ptr<common::ConfigurationManager> config);

    /**
     * @brief Destructor
     */
    ~ExtractService() override;

    /**
     * @brief Extract data batch
     * @param request Extract request
     * @return std::future<ExtractResponse> Extract response
     */
    std::future<ExtractResponse> extract(const ExtractRequest& request) override;

    /**
     * @brief Initialize extraction source
     * @param request Initialization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> initialize_source(const ServiceRequest& request) override;

    /**
     * @brief Finalize extraction source
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> finalize_source(const ServiceRequest& request) override;

    /**
     * @brief Get extraction statistics
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Statistics response
     */
    std::future<ServiceResponse> get_statistics(const std::string& job_id) override;

    /**
     * @brief Health check
     * @param request Health check request
     * @return std::future<HealthCheckResponse> Health response
     */
    std::future<HealthCheckResponse> health_check(const HealthCheckRequest& request) override;

private:
    /**
     * @brief Extractor session information
     */
    struct ExtractorSession {
        std::unique_ptr<core::IExtractor> extractor;
        core::ProcessingContext context;
        std::chrono::steady_clock::time_point created_at;
        std::chrono::steady_clock::time_point last_accessed;
        size_t total_extracted{0};
        size_t total_errors{0};
        bool initialized{false};
    };

    /**
     * @brief Get or create extractor session
     * @param job_id Job identifier
     * @return ExtractorSession& Session reference
     */
    ExtractorSession& get_or_create_session(const std::string& job_id);

    /**
     * @brief Create extractor instance
     * @param type Extractor type
     * @param config Configuration
     * @return std::unique_ptr<core::IExtractor> Extractor
     */
    std::unique_ptr<core::IExtractor> create_extractor(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Clean up expired sessions
     */
    void cleanup_expired_sessions();

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<common::Logger> logger_;

    // Session management
    std::unordered_map<std::string, ExtractorSession> sessions_;
    mutable std::mutex sessions_mutex_;
    std::chrono::minutes session_timeout_{30};

    // Service metrics
    std::atomic<size_t> total_requests_{0};
    std::atomic<size_t> successful_requests_{0};
    std::atomic<size_t> failed_requests_{0};
    std::chrono::steady_clock::time_point service_start_time_;
};

/**
 * @brief Extract service factory
 */
class ExtractServiceFactory {
public:
    /**
     * @brief Create extract service instance
     * @param config Configuration manager
     * @return std::unique_ptr<IExtractService> Service instance
     */
    static std::unique_ptr<IExtractService> create(
        std::shared_ptr<common::ConfigurationManager> config);
};

} // namespace omop::service
