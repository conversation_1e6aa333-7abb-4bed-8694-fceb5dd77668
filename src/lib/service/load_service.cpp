#include "load_service.h"
#include "load/batch_loader.h"
#include <future>

namespace omop::service {

LoadService::LoadService(std::shared_ptr<common::ConfigurationManager> config)
    : config_(config), logger_(common::Logger::get("omop-load-service")) {
    
    service_start_time_ = std::chrono::steady_clock::now();
    logger_->info("Load service initialized");
}

LoadService::~LoadService() {
    logger_->info("Load service shutting down");
    
    // Ensure all sessions are properly finalized
    std::lock_guard<std::mutex> lock(sessions_mutex_);
    for (auto& [job_id, session] : sessions_) {
        try {
            if (session.in_transaction) {
                session.loader->rollback(session.context);
            }
            session.loader->finalize(session.context);
        } catch (const std::exception& e) {
            logger_->error("Error finalizing session {}: {}", job_id, e.what());
        }
    }
}

std::future<LoadResponse> LoadService::load(const LoadRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        LoadResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        total_requests_++;
        
        try {
            logger_->info("Processing load request for job: {}", request.job_id);
            
            // Get or create session
            auto& session = get_or_create_session(request.job_id);
            
            // Load batch
            size_t loaded = session.loader->load_batch(request.records, session.context);
            size_t failed = request.records.size() - loaded;
            
            // Update statistics
            session.total_loaded += loaded;
            session.total_failed += failed;
            session.uncommitted_count += loaded;
            session.last_accessed = std::chrono::steady_clock::now();
            total_records_loaded_ += loaded;
            
            // Auto-commit if requested
            if (request.commit && session.uncommitted_count > 0) {
                session.loader->commit(session.context);
                session.uncommitted_count = 0;
                session.in_transaction = false;
            } else if (!session.in_transaction && loaded > 0) {
                session.in_transaction = true;
            }
            
            // Prepare response
            response.records_loaded = loaded;
            response.records_failed = failed;
            response.statistics = session.loader->get_statistics();
            response.success = true;
            
            successful_requests_++;
            
        } catch (const std::exception& e) {
            logger_->error("Load request failed: {}", e.what());
            response.success = false;
            response.error_message = e.what();
            failed_requests_++;
        }
        
        return response;
    });
}

std::future<ServiceResponse> LoadService::initialize_loader(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            logger_->info("Initializing loader for job: {}", request.job_id);
            
            // Get loader type from parameters
            auto type_it = request.parameters.find("loader_type");
            if (type_it == request.parameters.end()) {
                throw std::runtime_error("Missing loader_type parameter");
            }
            std::string loader_type = std::any_cast<std::string>(type_it->second);
            
            // Get target table
            auto table_it = request.parameters.find("target_table");
            if (table_it == request.parameters.end()) {
                throw std::runtime_error("Missing target_table parameter");
            }
            std::string target_table = std::any_cast<std::string>(table_it->second);
            
            // Create new session
            auto& session = get_or_create_session(request.job_id);
            session.loader = create_loader(loader_type, request.parameters);
            session.context.set_job_id(request.job_id);
            
            // Initialize loader
            std::unordered_map<std::string, std::any> init_config = request.parameters;
            init_config["target_table"] = target_table;
            session.loader->initialize(init_config, session.context);
            
            response.success = true;
            response.data["session_created"] = true;
            
        } catch (const std::exception& e) {
            logger_->error("Failed to initialize loader: {}", e.what());
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> LoadService::initialize_target(const ServiceRequest& request) {
    // For database loaders, initialization is handled in initialize_loader
    // This method exists for interface compliance and future extensions
    return initialize_loader(request);
}

std::future<ServiceResponse> LoadService::finalize_target(const ServiceRequest& request) {
    // For database loaders, finalization is handled in finalize_loader
    // This method exists for interface compliance and future extensions
    return finalize_loader(request);
}

std::future<ServiceResponse> LoadService::finalize_loader(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            logger_->info("Finalizing loader for job: {}", request.job_id);
            
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(request.job_id);
            if (it != sessions_.end()) {
                // Commit any pending data
                if (it->second.in_transaction) {
                    it->second.loader->commit(it->second.context);
                }
                
                // Finalize loader
                it->second.loader->finalize(it->second.context);
                
                // Store final statistics
                response.data = it->second.loader->get_statistics();
                response.data["total_loaded"] = it->second.total_loaded;
                response.data["total_failed"] = it->second.total_failed;
                
                sessions_.erase(it);
                response.success = true;
                response.data["session_removed"] = true;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            logger_->error("Failed to finalize loader: {}", e.what());
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> LoadService::initialize_target(const ServiceRequest& request) {
    // For database loaders, initialization is handled in initialize_loader
    // This method exists for interface compliance and future extensions
    return initialize_loader(request);
}

std::future<ServiceResponse> LoadService::finalize_target(const ServiceRequest& request) {
    // For database loaders, finalization is handled in finalize_loader
    // This method exists for interface compliance and future extensions
    return finalize_loader(request);
}

std::future<ServiceResponse> LoadService::commit(const std::string& job_id) {
    return std::async(std::launch::async, [this, job_id]() {
        ServiceResponse response;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(job_id);
            if (it != sessions_.end()) {
                it->second.loader->commit(it->second.context);
                it->second.uncommitted_count = 0;
                it->second.in_transaction = false;
                response.success = true;
                response.data["committed"] = true;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            logger_->error("Failed to commit: {}", e.what());
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> LoadService::rollback(const std::string& job_id) {
    return std::async(std::launch::async, [this, job_id]() {
        ServiceResponse response;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(job_id);
            if (it != sessions_.end()) {
                it->second.loader->rollback(it->second.context);
                it->second.uncommitted_count = 0;
                it->second.in_transaction = false;
                response.success = true;
                response.data["rolled_back"] = true;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            logger_->error("Failed to rollback: {}", e.what());
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> LoadService::get_statistics(const std::string& job_id) {
    return std::async(std::launch::async, [this, job_id]() {
        ServiceResponse response;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(job_id);
            if (it != sessions_.end()) {
                response.success = true;
                response.data = it->second.loader->get_statistics();
                response.data["total_loaded"] = it->second.total_loaded;
                response.data["total_failed"] = it->second.total_failed;
                response.data["uncommitted_count"] = it->second.uncommitted_count;
                response.data["in_transaction"] = it->second.in_transaction;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<HealthCheckResponse> LoadService::health_check(const HealthCheckRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        HealthCheckResponse response;
        response.healthy = true;
        response.status = "healthy";
        
        auto uptime = std::chrono::steady_clock::now() - service_start_time_;
        auto uptime_seconds = std::chrono::duration_cast<std::chrono::seconds>(uptime).count();
        
        response.metrics["uptime_seconds"] = uptime_seconds;
        response.metrics["total_requests"] = total_requests_.load();
        response.metrics["successful_requests"] = successful_requests_.load();
        response.metrics["failed_requests"] = failed_requests_.load();
        response.metrics["active_sessions"] = sessions_.size();
        response.metrics["total_records_loaded"] = total_records_loaded_.load();
        
        return response;
    });
}

LoadService::LoaderSession& LoadService::get_or_create_session(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(sessions_mutex_);
    
    auto it = sessions_.find(job_id);
    if (it != sessions_.end()) {
        return it->second;
    }
    
    // Create new session
    LoaderSession session;
    session.created_at = std::chrono::steady_clock::now();
    session.last_accessed = session.created_at;
    
    auto [inserted_it, success] = sessions_.emplace(job_id, std::move(session));
    return inserted_it->second;
}

std::unique_ptr<core::ILoader> LoadService::create_loader(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {
    
    // Register loaders
    load::LoaderFactory::register_loaders();
    
    // Create database connection for database loaders
    if (type == "database" || type == "omop_database") {
        auto db_config = config_->get_target_db();
        extract::IDatabaseConnection::ConnectionParams params;
        params.host = db_config.host();
        params.port = db_config.port();
        params.database = db_config.database();
        params.username = db_config.username();
        params.password = db_config.password();
        
        auto connection = extract::DatabaseConnectionFactory::instance().create(
            db_config.database_type(), params);
        
        load::DatabaseLoaderOptions options;
        if (config.find("batch_size") != config.end()) {
            options.batch_size = std::any_cast<size_t>(config.at("batch_size"));
        }
        
        return load::LoaderFactory::create(type, std::move(connection), options);
    }
    
    // For other loader types
    if (type == "csv_batch") {
        load::BatchLoaderOptions batch_options;
        return std::make_unique<load::CsvBatchLoader>(batch_options);
    }
    
    throw std::runtime_error(std::format("Unknown loader type: {}", type));
}

std::unique_ptr<ILoadService> LoadServiceFactory::create(
    std::shared_ptr<common::ConfigurationManager> config) {
    return std::make_unique<LoadService>(config);
}

} // namespace omop::service
