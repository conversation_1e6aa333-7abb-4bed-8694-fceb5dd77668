#pragma once

#include "service/microservice_interfaces.h"
#include "load/database_loader.h"
#include "common/logging.h"
#include <unordered_map>
#include <mutex>
#include <memory>

namespace omop::service {

/**
 * @brief Load microservice implementation
 *
 * This service handles all data loading operations independently.
 * It manages loader instances, handles batch loading, and provides
 * transaction management.
 */
class LoadService : public ILoadService {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     */
    explicit LoadService(std::shared_ptr<common::ConfigurationManager> config);

    /**
     * @brief Destructor
     */
    ~LoadService() override;

    /**
     * @brief Load data batch
     * @param request Load request
     * @return std::future<LoadResponse> Load response
     */
    std::future<LoadResponse> load(const LoadRequest& request) override;

    /**
     * @brief Initialize loader
     * @param request Initialization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> initialize_loader(const ServiceRequest& request) override;

    /**
     * @brief Finalize loader
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> finalize_loader(const ServiceRequest& request) override;

    /**
     * @brief Initialize target (required by interface)
     * @param request Initialization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> initialize_target(const ServiceRequest& request) override;

    /**
     * @brief Finalize target (required by interface)
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> finalize_target(const ServiceRequest& request) override;

    /**
     * @brief Commit transaction
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> commit(const std::string& job_id) override;

    /**
     * @brief Rollback transaction
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> rollback(const std::string& job_id) override;

    /**
     * @brief Get loading statistics
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Statistics response
     */
    std::future<ServiceResponse> get_statistics(const std::string& job_id) override;

    /**
     * @brief Health check
     * @param request Health check request
     * @return std::future<HealthCheckResponse> Health response
     */
    std::future<HealthCheckResponse> health_check(const HealthCheckRequest& request) override;

private:
    /**
     * @brief Loader session information
     */
    struct LoaderSession {
        std::unique_ptr<core::ILoader> loader;
        core::ProcessingContext context;
        std::chrono::steady_clock::time_point created_at;
        std::chrono::steady_clock::time_point last_accessed;
        size_t total_loaded{0};
        size_t total_failed{0};
        size_t uncommitted_count{0};
        bool in_transaction{false};
    };

    /**
     * @brief Get or create loader session
     * @param job_id Job identifier
     * @return LoaderSession& Session reference
     */
    LoaderSession& get_or_create_session(const std::string& job_id);

    /**
     * @brief Create loader instance
     * @param type Loader type
     * @param config Configuration
     * @return std::unique_ptr<core::ILoader> Loader
     */
    std::unique_ptr<core::ILoader> create_loader(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<common::Logger> logger_;

    // Session management
    std::unordered_map<std::string, LoaderSession> sessions_;
    mutable std::mutex sessions_mutex_;

    // Service metrics
    std::atomic<size_t> total_requests_{0};
    std::atomic<size_t> successful_requests_{0};
    std::atomic<size_t> failed_requests_{0};
    std::atomic<size_t> total_records_loaded_{0};
    std::chrono::steady_clock::time_point service_start_time_;
};

/**
 * @brief Load service factory
 */
class LoadServiceFactory {
public:
    static std::unique_ptr<ILoadService> create(
        std::shared_ptr<common::ConfigurationManager> config);
};

} // namespace omop::service
