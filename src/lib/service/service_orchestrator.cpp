#include "service_orchestrator.h"
#include <future>
#include <chrono>

namespace omop::service {

ServiceOrchestrator::ServiceOrchestrator(
    const OrchestratorConfig& config,
    std::shared_ptr<common::ConfigurationManager> config_manager)
    : config_(config), config_manager_(config_manager),
      logger_(common::Logger::get("omop-orchestrator")) {
    
    // Create service clients
    extract_service_ = ServiceClientFactory::create_extract_client(config.extract_service_endpoint);
    transform_service_ = ServiceClientFactory::create_transform_client(config.transform_service_endpoint);
    load_service_ = ServiceClientFactory::create_load_client(config.load_service_endpoint);
    
    // Create circuit breakers if enabled
    if (config.enable_circuit_breaker) {
        extract_circuit_breaker_ = std::make_unique<CircuitBreaker>(
            config.circuit_breaker_threshold, 2, std::chrono::seconds(60));
        transform_circuit_breaker_ = std::make_unique<CircuitBreaker>(
            config.circuit_breaker_threshold, 2, std::chrono::seconds(60));
        load_circuit_breaker_ = std::make_unique<CircuitBreaker>(
            config.circuit_breaker_threshold, 2, std::chrono::seconds(60));
    }
    
    start_time_ = std::chrono::steady_clock::now();
    logger_->info("Service orchestrator initialized");
}

ServiceOrchestrator::~ServiceOrchestrator() {
    stop();
}

void ServiceOrchestrator::start() {
    if (running_.exchange(true)) {
        return; // Already running
    }
    
    logger_->info("Starting service orchestrator");
    
    // Start worker threads
    for (size_t i = 0; i < config_.max_concurrent_jobs; ++i) {
        worker_threads_.emplace_back([this]() {
            while (running_) {
                std::pair<std::string, std::shared_ptr<JobState>> job_item;
                {
                    std::unique_lock<std::mutex> lock(queue_mutex_);
                    queue_cv_.wait(lock, [this]() { 
                        return !running_ || !job_queue_.empty(); 
                    });
                    
                    if (!running_) break;
                    
                    if (!job_queue_.empty()) {
                        job_item = std::move(job_queue_.front());
                        job_queue_.pop();
                    }
                }
                
                if (!job_item.first.empty()) {
                    execute_workflow(job_item.first, job_item.second);
                }
            }
        });
    }
    
    logger_->info("Service orchestrator started with {} worker threads", config_.max_concurrent_jobs);
}

void ServiceOrchestrator::stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }
    
    logger_->info("Stopping service orchestrator");
    
    // Notify all waiting threads
    queue_cv_.notify_all();
    
    // Wait for all worker threads to finish
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    worker_threads_.clear();
    
    logger_->info("Service orchestrator stopped");
}

std::future<ETLJobResult> ServiceOrchestrator::execute_job(const ETLJobRequest& request) {
    auto job_id = generate_job_id();
    
    // Create job state
    auto state = std::make_shared<JobState>();
    state->request = request;
    state->result.job_id = job_id;
    state->result.status = core::JobStatus::Created;
    state->result.start_time = std::chrono::system_clock::now();
    state->start_time = std::chrono::steady_clock::now();
    
    // Store job state
    {
        std::lock_guard<std::mutex> lock(jobs_mutex_);
        jobs_[job_id] = state;
    }
    
    // Create promise for async result
    auto promise = std::make_shared<std::promise<ETLJobResult>>();
    auto future = promise->get_future();
    
    // Queue job for execution
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        job_queue_.emplace(job_id, state);
    }
    queue_cv_.notify_one();
    
    // Set up completion callback
    state->result.completion_callback = [promise](const ETLJobResult& result) {
        promise->set_value(result);
    };
    
    total_jobs_processed_++;
    logger_->info("Job {} queued for execution", job_id);
    
    return future;
}

std::optional<ETLJobResult> ServiceOrchestrator::get_job_status(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it != jobs_.end()) {
        return it->second->result;
    }
    return std::nullopt;
}

bool ServiceOrchestrator::cancel_job(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it != jobs_.end()) {
        it->second->cancelled = true;
        it->second->result.status = core::JobStatus::Cancelled;
        it->second->result.end_time = std::chrono::system_clock::now();
        logger_->info("Job {} cancelled", job_id);
        return true;
    }
    return false;
}

HealthCheckResponse ServiceOrchestrator::get_health() {
    HealthCheckResponse response;
    response.healthy = running_;
    response.status = running_ ? "Running" : "Stopped";
    
    // Add metrics
    response.metrics["total_jobs_processed"] = total_jobs_processed_.load();
    response.metrics["successful_jobs"] = successful_jobs_.load();
    response.metrics["failed_jobs"] = failed_jobs_.load();
    response.metrics["active_jobs"] = jobs_.size();
    response.metrics["queued_jobs"] = job_queue_.size();
    
    auto uptime = std::chrono::steady_clock::now() - start_time_;
    response.metrics["uptime_seconds"] = std::chrono::duration_cast<std::chrono::seconds>(uptime).count();
    
    return response;
}

void ServiceOrchestrator::execute_workflow(const std::string& job_id, std::shared_ptr<JobState> state) {
    try {
        logger_->info("Starting workflow execution for job {}", job_id);
        
        // Initialize services
        initialize_services(job_id, state);
        
        // Main ETL loop
        bool has_more_data = true;
        while (has_more_data && !state->cancelled) {
            // Extract batch
            ExtractRequest extract_request;
            extract_request.job_id = job_id;
            extract_request.source_type = state->request.source_type;
            extract_request.source_table = state->request.source_table;
            extract_request.batch_size = config_.batch_size;
            extract_request.extractor_config = state->request.extractor_config;
            
            auto extract_future = extract_service_->extract(extract_request);
            auto extract_response = extract_future.get();
            
            if (!extract_response.success) {
                throw std::runtime_error("Extract failed: " + extract_response.error_message);
            }
            
            // Process batch
            if (!process_batch(job_id, state, extract_response)) {
                throw std::runtime_error("Batch processing failed");
            }
            
            has_more_data = extract_response.has_more_data;
        }
        
        // Finalize services
        finalize_services(job_id, state);
        
        // Update job result
        state->result.status = core::JobStatus::Completed;
        state->result.end_time = std::chrono::system_clock::now();
        successful_jobs_++;
        
        logger_->info("Job {} completed successfully", job_id);
        
    } catch (const std::exception& e) {
        logger_->error("Job {} failed: {}", job_id, e.what());
        
        state->result.status = core::JobStatus::Failed;
        state->result.end_time = std::chrono::system_clock::now();
        state->result.errors.push_back(e.what());
        failed_jobs_++;
        
        // Try to finalize services even on failure
        try {
            finalize_services(job_id, state);
        } catch (...) {
            // Ignore finalization errors
        }
    }
    
    // Clean up job state
    {
        std::lock_guard<std::mutex> lock(jobs_mutex_);
        jobs_.erase(job_id);
    }
}

void ServiceOrchestrator::initialize_services(const std::string& job_id, std::shared_ptr<JobState> state) {
    logger_->debug("Initializing services for job {}", job_id);
    
    // Initialize extract service
    ServiceRequest init_request;
    init_request.job_id = job_id;
    auto extract_init_future = extract_service_->initialize_source(init_request);
    auto extract_init_response = extract_init_future.get();
    
    if (!extract_init_response.success) {
        throw std::runtime_error("Failed to initialize extract service: " + extract_init_response.error_message);
    }
    
    // Initialize transform service
    auto transform_init_future = transform_service_->validate_config(init_request);
    auto transform_init_response = transform_init_future.get();
    
    if (!transform_init_response.success) {
        throw std::runtime_error("Failed to validate transform config: " + transform_init_response.error_message);
    }
    
    // Initialize load service
    auto load_init_future = load_service_->initialize_target(init_request);
    auto load_init_response = load_init_future.get();
    
    if (!load_init_response.success) {
        throw std::runtime_error("Failed to initialize load service: " + load_init_response.error_message);
    }
}

void ServiceOrchestrator::finalize_services(const std::string& job_id, std::shared_ptr<JobState> state) {
    logger_->debug("Finalizing services for job {}", job_id);
    
    ServiceRequest finalize_request;
    finalize_request.job_id = job_id;
    
    // Finalize all services
    try {
        extract_service_->finalize_source(finalize_request).get();
        transform_service_->finalize_session(finalize_request).get();
        load_service_->finalize_target(finalize_request).get();
    } catch (const std::exception& e) {
        logger_->warn("Error during service finalization: {}", e.what());
    }
}

bool ServiceOrchestrator::process_batch(const std::string& job_id,
                                       std::shared_ptr<JobState> state,
                                       const ExtractResponse& extract_response) {
    if (extract_response.records.empty()) {
        return true;
    }
    
    // Transform batch
    TransformRequest transform_request;
    transform_request.job_id = job_id;
    transform_request.input_records = extract_response.records;
    transform_request.target_table = state->request.target_table;
    transform_request.transformation_config = state->request.transformation_config;
    
    auto transform_future = transform_service_->transform(transform_request);
    auto transform_response = transform_future.get();
    
    if (!transform_response.success) {
        throw std::runtime_error("Transform failed: " + transform_response.error_message);
    }
    
    // Load batch
    LoadRequest load_request;
    load_request.job_id = job_id;
    load_request.records = transform_response.transformed_records;
    load_request.target_type = state->request.target_type;
    load_request.target_table = state->request.target_table;
    load_request.loader_config = state->request.loader_config;
    
    auto load_future = load_service_->load(load_request);
    auto load_response = load_future.get();
    
    if (!load_response.success) {
        throw std::runtime_error("Load failed: " + load_response.error_message);
    }
    
    // Update statistics
    state->total_records += extract_response.records.size();
    state->processed_records += load_response.records_loaded;
    state->error_records += load_response.records_failed;
    
    state->result.total_records = state->total_records;
    state->result.processed_records = state->processed_records;
    state->result.error_records = state->error_records;
    
    return true;
}

} // namespace omop::service 