#pragma once

#include "service/microservice_interfaces.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include <unordered_map>
#include <mutex>
#include <memory>

namespace omop::service {

/**
 * @brief Transform microservice implementation
 *
 * This service handles all data transformation operations independently.
 * It manages transformation engines, vocabulary lookups, and validation.
 */
class TransformService : public ITransformService {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     */
    explicit TransformService(std::shared_ptr<common::ConfigurationManager> config);

    /**
     * @brief Destructor
     */
    ~TransformService() override;

    /**
     * @brief Transform data batch
     * @param request Transform request
     * @return std::future<TransformResponse> Transform response
     */
    std::future<TransformResponse> transform(const TransformRequest& request) override;

    /**
     * @brief Validate transformation configuration
     * @param request Validation request
     * @return std::future<ServiceResponse> Validation response
     */
    std::future<ServiceResponse> validate_config(const ServiceRequest& request) override;

    /**
     * @brief Load vocabulary mappings
     * @param request Load request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> load_vocabularies(const ServiceRequest& request) override;

    /**
     * @brief Get transformation statistics
     * @param job_id Job identifier
     * @return std::future<ServiceResponse> Statistics response
     */
    std::future<ServiceResponse> get_statistics(const std::string& job_id) override;

    /**
     * @brief Health check
     * @param request Health check request
     * @return std::future<HealthCheckResponse> Health response
     */
    std::future<HealthCheckResponse> health_check(const HealthCheckRequest& request) override;

    /**
     * @brief Finalize transformation session (missing from interface)
     * @param request Finalization request
     * @return std::future<ServiceResponse> Response
     */
    std::future<ServiceResponse> finalize_session(const ServiceRequest& request);

private:
    /**
     * @brief Transformation session information
     */
    struct TransformSession {
        std::unique_ptr<core::ITransformer> transformer;
        core::ProcessingContext context;
        std::chrono::steady_clock::time_point created_at;
        std::chrono::steady_clock::time_point last_accessed;
        size_t total_transformed{0};
        size_t total_errors{0};
        size_t total_validations{0};
        size_t validation_failures{0};
    };

    /**
     * @brief Get or create transformation session
     * @param job_id Job identifier
     * @param target_table Target table name
     * @return TransformSession& Session reference
     */
    TransformSession& get_or_create_session(const std::string& job_id,
                                           const std::string& target_table);

    /**
     * @brief Create transformer instance
     * @param target_table Target table name
     * @return std::unique_ptr<core::ITransformer> Transformer
     */
    std::unique_ptr<core::ITransformer> create_transformer(const std::string& target_table);

    /**
     * @brief Initialize vocabulary service
     */
    void initialize_vocabulary_service();

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<common::Logger> logger_;
    std::unique_ptr<transform::VocabularyService> vocabulary_service_;

    // Session management
    std::unordered_map<std::string, TransformSession> sessions_;
    mutable std::mutex sessions_mutex_;

    // Service metrics
    std::atomic<size_t> total_requests_{0};
    std::atomic<size_t> successful_requests_{0};
    std::atomic<size_t> failed_requests_{0};
    std::chrono::steady_clock::time_point service_start_time_;
};

/**
 * @brief Transform service factory
 */
class TransformServiceFactory {
public:
    static std::unique_ptr<ITransformService> create(
        std::shared_ptr<common::ConfigurationManager> config);
};

} // namespace omop::service
