#pragma once

#include "service/microservice_interfaces.h"
#include "service/etl_service.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <memory>
#include <thread>
#include <atomic>
#include <queue>
#include <condition_variable>

namespace omop::service {

/**
 * @brief Service orchestrator configuration
 */
struct OrchestratorConfig {
    std::string extract_service_endpoint{"localhost:50051"};
    std::string transform_service_endpoint{"localhost:50052"};
    std::string load_service_endpoint{"localhost:50053"};
    size_t max_concurrent_jobs{10};
    size_t batch_size{1000};
    std::chrono::seconds service_timeout{300};
    bool enable_circuit_breaker{true};
    size_t circuit_breaker_threshold{5};
    bool enable_retry{true};
    size_t max_retries{3};
    std::chrono::seconds retry_delay{5};
};

/**
 * @brief ETL Service Orchestrator
 *
 * Coordinates the three microservices (Extract, Transform, Load) to execute
 * complete ETL pipelines. Handles service discovery, circuit breaking,
 * retries, and workflow orchestration.
 */
class ServiceOrchestrator {
public:
    /**
     * @brief Constructor
     * @param config Orchestrator configuration
     * @param config_manager Configuration manager
     */
    ServiceOrchestrator(const OrchestratorConfig& config,
                       std::shared_ptr<common::ConfigurationManager> config_manager);

    /**
     * @brief Destructor
     */
    ~ServiceOrchestrator();

    /**
     * @brief Start the orchestrator
     */
    void start();

    /**
     * @brief Stop the orchestrator
     */
    void stop();

    /**
     * @brief Execute ETL job
     * @param request ETL job request
     * @return std::future<ETLJobResult> Job result
     */
    std::future<ETLJobResult> execute_job(const ETLJobRequest& request);

    /**
     * @brief Get job status
     * @param job_id Job identifier
     * @return std::optional<ETLJobResult> Job result if found
     */
    std::optional<ETLJobResult> get_job_status(const std::string& job_id);

    /**
     * @brief Cancel job
     * @param job_id Job identifier
     * @return bool True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Get orchestrator health
     * @return HealthCheckResponse Health status
     */
    HealthCheckResponse get_health();

private:
    /**
     * @brief Job execution state
     */
    struct JobState {
        ETLJobRequest request;
        ETLJobResult result;
        std::string extract_session_id;
        std::string transform_session_id;
        std::string load_session_id;
        std::atomic<bool> cancelled{false};
        std::chrono::steady_clock::time_point start_time;
        size_t total_records{0};
        size_t processed_records{0};
        size_t error_records{0};
    };

    /**
     * @brief Execute ETL workflow
     * @param job_id Job identifier
     * @param state Job state
     */
    void execute_workflow(const std::string& job_id, std::shared_ptr<JobState> state);

    /**
     * @brief Initialize services for job
     * @param job_id Job identifier
     * @param state Job state
     */
    void initialize_services(const std::string& job_id, std::shared_ptr<JobState> state);

    /**
     * @brief Finalize services for job
     * @param job_id Job identifier
     * @param state Job state
     */
    void finalize_services(const std::string& job_id, std::shared_ptr<JobState> state);

    /**
     * @brief Process batch through ETL pipeline
     * @param job_id Job identifier
     * @param state Job state
     * @param extract_response Extract response
     * @return bool True if successful
     */
    bool process_batch(const std::string& job_id,
                      std::shared_ptr<JobState> state,
                      const ExtractResponse& extract_response);

    /**
     * @brief Generate unique job ID
     * @return std::string Job ID
     */
    std::string generate_job_id();

    OrchestratorConfig config_;
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::shared_ptr<common::Logger> logger_;

    // Service clients
    std::unique_ptr<IExtractService> extract_service_;
    std::unique_ptr<ITransformService> transform_service_;
    std::unique_ptr<ILoadService> load_service_;

    // Circuit breakers
    std::unique_ptr<CircuitBreaker> extract_circuit_breaker_;
    std::unique_ptr<CircuitBreaker> transform_circuit_breaker_;
    std::unique_ptr<CircuitBreaker> load_circuit_breaker_;

    // Job management
    std::unordered_map<std::string, std::shared_ptr<JobState>> jobs_;
    mutable std::mutex jobs_mutex_;

    // Thread pool for job execution
    std::vector<std::thread> worker_threads_;
    std::queue<std::pair<std::string, std::shared_ptr<JobState>>> job_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> running_{false};

    // Metrics
    std::atomic<size_t> total_jobs_processed_{0};
    std::atomic<size_t> successful_jobs_{0};
    std::atomic<size_t> failed_jobs_{0};
    std::chrono::steady_clock::time_point start_time_;
};

} // namespace omop::service 