# OMOP CDM Library Configuration

add_library(omop_cdm STATIC)

set(CDM_SOURCES
    omop_tables.cpp
    table_definitions.cpp
)

set(CDM_HEADERS
    omop_tables.h
    table_definitions.h
)

target_sources(omop_cdm PRIVATE ${CDM_SOURCES})

omop_configure_library(omop_cdm
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_common
    HEADERS
        ${CDM_HEADERS}
)

# SQL Schema Generation
set(SQL_TEMPLATES
    sql/create_tables.sql.in
    sql/create_indexes.sql.in
    sql/create_constraints.sql.in
    sql/create_provider_care_site.sql.in
    sql/create_location.sql.in
)

set(SQL_OUTPUTS
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_tables.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_indexes.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_constraints.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_provider_care_site.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_location.sql
)

file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/sql)

foreach(template ${SQL_TEMPLATES})
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${template})
        get_filename_component(filename ${template} NAME_WE)
        configure_file(
            ${CMAKE_CURRENT_SOURCE_DIR}/${template}
            ${CMAKE_CURRENT_BINARY_DIR}/sql/${filename}.sql
            @ONLY
        )
    endif()
endforeach()

install(FILES ${SQL_OUTPUTS}
    DESTINATION share/omop/sql
    OPTIONAL
)