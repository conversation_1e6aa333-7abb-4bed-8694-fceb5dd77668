/**
 * @file validation.cpp
 * @brief Implementation of data validation framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "validation.h"
#include "exceptions.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cctype>
#include <any>
#include <optional>
#include <typeinfo>
#include <yaml-cpp/yaml.h>
#include <chrono>
#include <nlohmann/json.hpp>

namespace omop::common {

// ValidationResult implementation

// ValidationRule base class implementation
ValidationRule::ValidationRule(const std::string& field_name,
                             ValidationType type,
                             const std::string& error_message)
    : field_name_(field_name), type_(type), error_message_(error_message) {}

std::string ValidationRule::getErrorMessage() const {
    if (!error_message_.empty()) {
        return error_message_;
    }

    // Generate default error message based on validation type
    std::stringstream ss;
    ss << "Field '" << field_name_ << "' failed validation: ";

    switch (type_) {
        case ValidationType::NOT_NULL:
            ss << "value is null";
            break;
        case ValidationType::NOT_EMPTY:
            ss << "value is empty";
            break;
        case ValidationType::UNIQUE:
            ss << "value is not unique";
            break;
        case ValidationType::IN_LIST:
            ss << "value is not in allowed list";
            break;
        case ValidationType::REGEX:
            ss << "value does not match required pattern";
            break;
        case ValidationType::DATE_RANGE:
            ss << "date is outside allowed range";
            break;
        case ValidationType::NUMERIC_RANGE:
            ss << "number is outside allowed range";
            break;
        case ValidationType::GREATER_THAN:
            ss << "value is not greater than required minimum";
            break;
        case ValidationType::LESS_THAN:
            ss << "value is not less than required maximum";
            break;
        case ValidationType::BETWEEN:
            ss << "value is not between required bounds";
            break;
        case ValidationType::BEFORE:
            ss << "date is not before required date";
            break;
        case ValidationType::AFTER:
            ss << "date is not after required date";
            break;
        case ValidationType::LENGTH:
            ss << "string length is invalid";
            break;
        case ValidationType::CUSTOM:
            ss << "custom validation failed";
            break;
        case ValidationType::NOT_ZERO:
            ss << "value is zero";
            break;
        case ValidationType::NOT_FUTURE_DATE:
            ss << "date is in the future";
            break;
        case ValidationType::FOREIGN_KEY:
            ss << "foreign key constraint violation";
            break;
        case ValidationType::COMPOSITE_KEY:
            ss << "composite key validation failed";
            break;
        case ValidationType::CONDITIONAL:
            ss << "conditional validation failed";
            break;
        default:
            ss << "validation failed";
    }

    return ss.str();
}

// NotNullRule implementation
NotNullRule::NotNullRule(const std::string& field_name)
    : ValidationRule(field_name, ValidationType::NOT_NULL) {}

bool NotNullRule::validate(const std::any& value,
                          const std::unordered_map<std::string, std::any>& record) const {
    return value.has_value();
}

// InListRule implementation
template<typename T>
InListRule<T>::InListRule(const std::string& field_name, const std::vector<T>& allowed_values)
    : ValidationRule(field_name, ValidationType::IN_LIST), allowed_values_(allowed_values) {}

template<typename T>
bool InListRule<T>::validate(const std::any& value,
                            const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const T& val = std::any_cast<const T&>(value);
        return std::find(allowed_values_.begin(), allowed_values_.end(), val) != allowed_values_.end();
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// Explicit template instantiations
template class InListRule<int>;
template class InListRule<int64_t>;
template class InListRule<std::string>;
template class InListRule<double>;

// DateRangeRule implementation
DateRangeRule::DateRangeRule(const std::string& field_name,
                           const std::optional<std::chrono::system_clock::time_point>& min_date,
                           const std::optional<std::chrono::system_clock::time_point>& max_date)
    : ValidationRule(field_name, ValidationType::DATE_RANGE),
      min_date_(min_date), max_date_(max_date) {}

bool DateRangeRule::validate(const std::any& value,
                           const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const auto& date = std::any_cast<const std::chrono::system_clock::time_point&>(value);

        if (min_date_ && date < *min_date_) {
            return false;
        }

        if (max_date_ && date > *max_date_) {
            return false;
        }

        return true;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// NumericRangeRule implementation
template<typename T>
NumericRangeRule<T>::NumericRangeRule(const std::string& field_name,
                                     const std::optional<T>& min_value,
                                     const std::optional<T>& max_value)
    : ValidationRule(field_name, ValidationType::NUMERIC_RANGE),
      min_value_(min_value), max_value_(max_value) {}

template<typename T>
bool NumericRangeRule<T>::validate(const std::any& value,
                                  const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        // First try to cast to the exact type T
        const T& val = std::any_cast<const T&>(value);

        if (min_value_ && val < *min_value_) {
            return false;
        }

        if (max_value_ && val > *max_value_) {
            return false;
        }

        return true;
    } catch (const std::bad_any_cast&) {
        // If exact type cast fails, try to convert from other numeric types
        try {
            // Attempt safe numeric conversions
            if constexpr (std::is_same_v<T, double>) {
                if (value.type() == typeid(int)) {
                    int int_val = std::any_cast<int>(value);
                    double val = static_cast<double>(int_val);
                    
                    if (min_value_ && val < *min_value_) {
                        return false;
                    }
                    
                    if (max_value_ && val > *max_value_) {
                        return false;
                    }
                    
                    return true;
                } else if (value.type() == typeid(float)) {
                    float float_val = std::any_cast<float>(value);
                    double val = static_cast<double>(float_val);
                    
                    if (min_value_ && val < *min_value_) {
                        return false;
                    }
                    
                    if (max_value_ && val > *max_value_) {
                        return false;
                    }
                    
                    return true;
                }
            } else if constexpr (std::is_same_v<T, int>) {
                if (value.type() == typeid(double)) {
                    double double_val = std::any_cast<double>(value);
                    
                    // Check if conversion is safe
                    if (double_val != std::floor(double_val) ||
                        double_val < std::numeric_limits<int>::min() ||
                        double_val > std::numeric_limits<int>::max()) {
                        return false;
                    }
                    
                    int val = static_cast<int>(double_val);

                    if (min_value_ && val < *min_value_) {
                        return false;
                    }

                    if (max_value_ && val > *max_value_) {
                        return false;
                    }

                    return true;
                }
            }
        } catch (const std::bad_any_cast&) {
            // Log the type mismatch for debugging
            return false;
        }
        
        // If we reach here, type conversion failed
        return false;
    }
}

// Explicit template instantiations
template class NumericRangeRule<int>;
template class NumericRangeRule<int64_t>;
template class NumericRangeRule<double>;
template class NumericRangeRule<float>;

// RegexRule implementation
RegexRule::RegexRule(const std::string& field_name, const std::string& pattern)
    : ValidationRule(field_name, ValidationType::REGEX), pattern_(pattern) {}

bool RegexRule::validate(const std::any& value,
                        const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const auto& str = std::any_cast<const std::string&>(value);
        return std::regex_match(str, pattern_);
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// CustomRule implementation
CustomRule::CustomRule(const std::string& field_name,
                      ValidationFunction validator,
                      const std::string& error_message)
    : ValidationRule(field_name, ValidationType::CUSTOM, error_message),
      validator_(validator) {}

bool CustomRule::validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const {
    return validator_(value, record);
}

// BasicValidationEngine implementation
BasicValidationEngine::BasicValidationEngine(std::shared_ptr<Logger> logger)
    : logger_(logger ? logger : Logger::get("ValidationEngine")) {
    // Enable all validation types by default
    for (int i = 0; i <= static_cast<int>(ValidationType::CONDITIONAL); ++i) {
        enabled_types_[static_cast<ValidationType>(i)] = true;
    }
}

void BasicValidationEngine::addRule(std::unique_ptr<ValidationRule> rule) {
    rules_.push_back(std::move(rule));
}

ValidationResult BasicValidationEngine::validateRecord(const std::unordered_map<std::string, std::any>& record) {
    ValidationResult result;
    for (const auto& rule : rules_) {
        if (!enabled_types_[rule->getType()]) {
            continue;
        }
        const auto& field_name = rule->getFieldName();
        auto it = record.find(field_name);
        std::any value;
        if (it != record.end()) {
            value = it->second;
        }
        if (!rule->validate(value, record)) {
            result.add_error(field_name, rule->getErrorMessage(), "validation_rule");
            logger_->debug("Validation failed for field '{}': {}", field_name, rule->getErrorMessage());
        }
    }
    return result;
}

ValidationResult BasicValidationEngine::validateBatch(
    const std::vector<std::unordered_map<std::string, std::any>>& records,
    bool stop_on_error) {
    ValidationResult total_result;
    for (size_t i = 0; i < records.size(); ++i) {
        auto result = validateRecord(records[i]);
        if (!result.is_valid()) {
            // Add record index to error messages
            for (const auto& error : result.errors()) {
                // error is a ValidationError struct
                omop::common::ValidationResult::ValidationError indexed_error = error;
                indexed_error.error_message = "Record " + std::to_string(i) + ": " + error.error_message;
                total_result.add_error(indexed_error);
            }
            // Also merge warnings from failed validations
            for (const auto& warning : result.warnings()) {
                total_result.add_warning(warning);
            }
            if (stop_on_error) {
                break;
            }
        } else {
            // For valid results, still merge warnings
            for (const auto& warning : result.warnings()) {
                total_result.add_warning(warning);
            }
        }
        // Break early if we've exceeded a reasonable error threshold
        if (total_result.errors().size() > 1000) {
            total_result.add_error({"SYSTEM", "Error limit exceeded, validation stopped", "system"});
            break;
        }
    }
    return total_result;
}

void BasicValidationEngine::clearRules() {
    rules_.clear();
}

void BasicValidationEngine::setValidationTypeEnabled(ValidationType type, bool enabled) {
    enabled_types_[type] = enabled;
}

// ValidationRuleFactory implementation
std::unique_ptr<ValidationRule> ValidationRuleFactory::createRule(
    const std::unordered_map<std::string, std::any>& config) {

    auto field_it = config.find("field");
    auto type_it = config.find("type");

    if (field_it == config.end() || type_it == config.end()) {
        throw ConfigurationException("Validation rule missing required 'field' or 'type'");
    }

    std::string field_name = std::any_cast<std::string>(field_it->second);
    std::string type_str = std::any_cast<std::string>(type_it->second);

    // Get custom error message if provided
    std::string error_message;
    auto error_it = config.find("error_message");
    if (error_it != config.end()) {
        error_message = std::any_cast<std::string>(error_it->second);
    }

    if (type_str == "not_null") {
        return std::make_unique<NotNullRule>(field_name);
    }
    else if (type_str == "in_list") {
        auto values_it = config.find("values");
        if (values_it == config.end()) {
            throw ConfigurationException("in_list validation requires 'values' parameter");
        }

        // Handle different value types
        try {
            auto int_values = std::any_cast<std::vector<int>>(values_it->second);
            return std::make_unique<InListRule<int>>(field_name, int_values);
        } catch (const std::bad_any_cast&) {
            try {
                auto str_values = std::any_cast<std::vector<std::string>>(values_it->second);
                return std::make_unique<InListRule<std::string>>(field_name, str_values);
            } catch (const std::bad_any_cast&) {
                try {
                    auto double_values = std::any_cast<std::vector<double>>(values_it->second);
                    return std::make_unique<InListRule<double>>(field_name, double_values);
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid values type for in_list validation");
                }
            }
        }
    }
    else if (type_str == "regex") {
        auto pattern_it = config.find("pattern");
        if (pattern_it == config.end()) {
            throw ConfigurationException("regex validation requires 'pattern' parameter");
        }

        std::string pattern = std::any_cast<std::string>(pattern_it->second);
        return std::make_unique<RegexRule>(field_name, pattern);
    }
    else if (type_str == "numeric_range") {
        std::optional<double> min_value, max_value;

        auto min_it = config.find("min");
        if (min_it != config.end()) {
            // Handle both int and double types
            try {
                min_value = std::any_cast<double>(min_it->second);
            } catch (const std::bad_any_cast&) {
                try {
                    min_value = static_cast<double>(std::any_cast<int>(min_it->second));
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid min value type for numeric_range validation");
                }
            }
        }

        auto max_it = config.find("max");
        if (max_it != config.end()) {
            // Handle both int and double types
            try {
                max_value = std::any_cast<double>(max_it->second);
            } catch (const std::bad_any_cast&) {
                try {
                    max_value = static_cast<double>(std::any_cast<int>(max_it->second));
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid max value type for numeric_range validation");
                }
            }
        }

        return std::make_unique<NumericRangeRule<double>>(field_name, min_value, max_value);
    }
    else if (type_str == "date_range") {
        std::optional<std::chrono::system_clock::time_point> min_date, max_date;

        auto min_it = config.find("min_date");
        if (min_it != config.end()) {
            // Parse date string
            std::string min_date_str = std::any_cast<std::string>(min_it->second);
            std::tm tm = {};
            std::istringstream ss(min_date_str);
            ss >> std::get_time(&tm, "%Y-%m-%d");
            if (!ss.fail()) {
                min_date = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
        }

        auto max_it = config.find("max_date");
        if (max_it != config.end()) {
            // Parse date string
            std::string max_date_str = std::any_cast<std::string>(max_it->second);
            std::tm tm = {};
            std::istringstream ss(max_date_str);
            ss >> std::get_time(&tm, "%Y-%m-%d");
            if (!ss.fail()) {
                max_date = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
        }

        return std::make_unique<DateRangeRule>(field_name, min_date, max_date);
    }
    else if (type_str == "custom") {
        // For custom rules, check if a validator function is provided in config
        auto validator_it = config.find("validator");
        if (validator_it != config.end()) {
            try {
                auto validator_func = std::any_cast<CustomRule::ValidationFunction>(validator_it->second);
                return std::make_unique<CustomRule>(field_name, validator_func, error_message);
            } catch (const std::bad_any_cast&) {
                throw ConfigurationException("Invalid validator function for custom validation rule");
            }
        }
        
        // If no validator provided, throw exception
        throw ConfigurationException("Custom validation rules must be registered programmatically");
    }

    throw ConfigurationException("Unknown validation type: " + type_str);
}

std::unique_ptr<BasicValidationEngine> ValidationRuleFactory::createEngineFromYaml(const std::string& yaml_config) {
    auto engine = std::make_unique<BasicValidationEngine>();
    YAML::Node config;

    try {
        config = YAML::Load(yaml_config);

        if (config["validation_rules"]) {
            for (const auto& rule_node : config["validation_rules"]) {
                std::unordered_map<std::string, std::any> rule_config;

                for (const auto& item : rule_node) {
                    std::string key = item.first.as<std::string>();

                    // Convert YAML node to appropriate type
                    if (item.second.IsScalar()) {
                        // Try to determine the type
                        std::string scalar_value = item.second.as<std::string>();

                        // Try to parse as number first
                        try {
                            if (scalar_value.find('.') != std::string::npos) {
                                double d = std::stod(scalar_value);
                                rule_config[key] = d;
                            } else {
                                int i = std::stoi(scalar_value);
                                rule_config[key] = i;
                            }
                        } catch (...) {
                            // Not a number, store as string
                            rule_config[key] = scalar_value;
                        }
                    } else if (item.second.IsSequence()) {
                        // Check if it's a list of strings or numbers
                        if (item.second.size() > 0) {
                            if (item.second[0].IsScalar()) {
                                std::string first_val = item.second[0].as<std::string>();

                                try {
                                    // Try integer list
                                    std::stoi(first_val);
                                    std::vector<int> values;
                                    for (const auto& val : item.second) {
                                        values.push_back(val.as<int>());
                                    }
                                    rule_config[key] = values;
                                } catch (...) {
                                    try {
                                        // Try double list
                                        std::stod(first_val);
                                        std::vector<double> values;
                                        for (const auto& val : item.second) {
                                            values.push_back(val.as<double>());
                                        }
                                        rule_config[key] = values;
                                    } catch (...) {
                                        // String list
                                        std::vector<std::string> values;
                                        for (const auto& val : item.second) {
                                            values.push_back(val.as<std::string>());
                                        }
                                        rule_config[key] = values;
                                    }
                                }
                            }
                        }
                    }
                }

                auto rule = createRule(rule_config);
                engine->addRule(std::move(rule));
            }
        }

        // Set up default validation types if specified
        if (config["validation_settings"]) {
            if (config["validation_settings"]["enabled_types"]) {
                for (const auto& type_node : config["validation_settings"]["enabled_types"]) {
                    // Implementation for enabling/disabling specific validation types
                    [[maybe_unused]] auto _ = type_node;
                }
            }
        }
    } catch (const YAML::Exception& e) {
        throw ConfigurationException("Failed to parse validation YAML: " + std::string(e.what()));
    }

    return engine;
}

} // namespace omop::common