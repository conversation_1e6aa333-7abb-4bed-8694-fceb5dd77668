/**
 * @file utilities.cpp
 * @brief Implementation of utility functions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "utilities.h"
#include "logging.h"
#include <any>
#include <limits>

#include <fstream>
#include <random>
#include <cctype>
#include <ctime>
#include <iomanip>
#include <mutex>
#include <iostream>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <uuid/uuid.h>
#include <nlohmann/json.hpp>
#include <cstdlib>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <thread>
#include <array>
#include <memory>
#include <stdexcept>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#elif defined(__linux__)
#include <sys/resource.h>
#include <sys/sysinfo.h>
#elif defined(__APPLE__)
#include <sys/resource.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#include <mach/mach.h>
#endif

namespace omop::common {

// Thread-local random generator for better performance and thread safety
thread_local std::mt19937 tl_random_gen{std::random_device{}()};

std::string any_to_string(const std::any& value) {
    if (!value.has_value()) {
        return "";
    }

    const std::type_info& type = value.type();

    try {
        if (type == typeid(std::string)) {
            return std::any_cast<std::string>(value);
        } else if (type == typeid(int)) {
            return std::to_string(std::any_cast<int>(value));
        } else if (type == typeid(long)) {
            return std::to_string(std::any_cast<long>(value));
        } else if (type == typeid(long long)) {
            return std::to_string(std::any_cast<long long>(value));
        } else if (type == typeid(unsigned int)) {
            return std::to_string(std::any_cast<unsigned int>(value));
        } else if (type == typeid(unsigned long)) {
            return std::to_string(std::any_cast<unsigned long>(value));
        } else if (type == typeid(unsigned long long)) {
            return std::to_string(std::any_cast<unsigned long long>(value));
        } else if (type == typeid(float)) {
            return std::to_string(std::any_cast<float>(value));
        } else if (type == typeid(double)) {
            return std::to_string(std::any_cast<double>(value));
        } else if (type == typeid(bool)) {
            return std::any_cast<bool>(value) ? "true" : "false";
        } else if (type == typeid(char)) {
            return std::string(1, std::any_cast<char>(value));
        } else {
            // For unknown types, return type name
            return std::string("<") + type.name() + ">";
        }
    } catch (const std::bad_any_cast& e) {
        return std::string("<bad_cast: ") + type.name() + ">";
    }
}

// StringUtils implementation
std::string StringUtils::trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r\f\v");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(first, (last - first + 1));
}

std::string StringUtils::to_lower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::tolower(c); });
    return result;
}

std::string StringUtils::to_upper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::toupper(c); });
    return result;
}

std::vector<std::string> StringUtils::split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;

    while (std::getline(ss, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }

    return tokens;
}

std::vector<std::string> StringUtils::split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);

    while (end != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }

    tokens.push_back(str.substr(start));
    return tokens;
}

std::string StringUtils::join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }

    std::stringstream ss;
    for (size_t i = 0; i < strings.size(); ++i) {
        if (i > 0) {
            ss << delimiter;
        }
        ss << strings[i];
    }

    return ss.str();
}

std::string StringUtils::replace_all(const std::string& str,
                                    const std::string& from,
                                    const std::string& to) {
    if (from.empty()) {
        return str;
    }

    std::string result = str;
    size_t start_pos = 0;

    while ((start_pos = result.find(from, start_pos)) != std::string::npos) {
        result.replace(start_pos, from.length(), to);
        start_pos += to.length();
    }

    return result;
}

bool StringUtils::starts_with(const std::string& str, const std::string& prefix) {
    return str.size() >= prefix.size() &&
           str.compare(0, prefix.size(), prefix) == 0;
}

bool StringUtils::ends_with(const std::string& str, const std::string& suffix) {
    return str.size() >= suffix.size() &&
           str.compare(str.size() - suffix.size(), suffix.size(), suffix) == 0;
}

bool StringUtils::contains(const std::string& str, const std::string& substr) {
    return str.find(substr) != std::string::npos;
}

std::string StringUtils::to_snake_case(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (i > 0 && std::isupper(str[i]) && std::islower(str[i-1])) {
            result += '_';
        }
        result += std::tolower(str[i]);
    }
    return result;
}

std::string StringUtils::to_camel_case(const std::string& str) {
    std::string result;
    bool capitalize_next = false;

    for (char c : str) {
        if (c == '_' || c == ' ' || c == '-') {
            capitalize_next = true;
        } else {
            if (capitalize_next) {
                result += std::toupper(c);
                capitalize_next = false;
            } else {
                result += std::tolower(c);
            }
        }
    }

    return result;
}

std::string StringUtils::escape_sql(const std::string& str) {
    std::string result;
    result.reserve(str.length() * 2);

    for (char c : str) {
        switch (c) {
            case '\'': result += "''"; break;
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            case '\0': result += "\\0"; break;
            default: result += c; break;
        }
    }

    return result;
}

std::string StringUtils::random_string(size_t length) {
    static const char alphanum[] =
        "0123456789"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz";

    std::uniform_int_distribution<> dis(0, sizeof(alphanum) - 2);

    std::string result;
    result.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        result += alphanum[dis(tl_random_gen)];
    }

    return result;
}

// DateTimeUtils implementation
// Thread-safe time conversion using mutex
static std::mutex time_conversion_mutex;

std::optional<DateTimeUtils::time_point> DateTimeUtils::parse_date(
    const std::string& date_str, const std::string& format) {

    std::tm tm = {};
    std::istringstream ss(date_str);
    
    // Lock for thread-safe time parsing
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        ss >> std::get_time(&tm, format.c_str());
    }

    if (ss.fail()) {
        return std::nullopt;
    }

    // Use mutex for mktime which is not thread-safe
    time_t time_value;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        time_value = std::mktime(&tm);
    }
    
    if (time_value == -1) {
        return std::nullopt;
    }
    
    return std::chrono::system_clock::from_time_t(time_value);
}

std::string DateTimeUtils::format_date(const time_point& tp, const std::string& format) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::stringstream ss;
    
    // Thread-safe localtime
    std::tm tm_result;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm_result, &time_t);
#else
        localtime_r(&time_t, &tm_result);
#endif
    }
    
    ss << std::put_time(&tm_result, format.c_str());
    return ss.str();
}

std::string DateTimeUtils::current_timestamp(const std::string& format) {
    return format_date(std::chrono::system_clock::now(), format);
}

DateTimeUtils::time_point DateTimeUtils::convert_timezone(
    const time_point& tp, const std::string& from_tz, const std::string& to_tz) {
        // Fallback: basic offset conversion for common UK timezones
        if (from_tz == "UTC" && to_tz == "Europe/London") {
            // UTC to BST/GMT conversion
            auto local_time = std::chrono::system_clock::to_time_t(tp);
            std::tm tm_utc{};
            
            {
                std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
                gmtime_s(&tm_utc, &local_time);
#else
                gmtime_r(&local_time, &tm_utc);
#endif
            }
            
            // Simple BST check (last Sunday in March to last Sunday in October)
            bool is_bst = false;
            if (tm_utc.tm_mon >= 2 && tm_utc.tm_mon <= 9) { // March to October
                if (tm_utc.tm_mon > 2 && tm_utc.tm_mon < 9) {
                    is_bst = true; // Definitely BST
                } else {
                    // Check for last Sunday rule (simplified)
                    is_bst = (tm_utc.tm_mon == 2) ? (tm_utc.tm_mday >= 25) : (tm_utc.tm_mday < 25);
                }
            }
            
            if (is_bst) {
                return tp + std::chrono::hours(1); // BST = UTC + 1
            }
        } else if (from_tz == "Europe/London" && to_tz == "UTC") {
            // Similar reverse conversion
            auto local_time = std::chrono::system_clock::to_time_t(tp);
            std::tm tm_local{};
            
            {
                std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
                localtime_s(&tm_local, &local_time);
#else
                localtime_r(&local_time, &tm_local);
#endif
            }
            
            // Simplified BST detection
            bool is_bst = false;
            if (tm_local.tm_mon >= 2 && tm_local.tm_mon <= 9) {
                if (tm_local.tm_mon > 2 && tm_local.tm_mon < 9) {
                    is_bst = true;
                } else {
                    is_bst = (tm_local.tm_mon == 2) ? (tm_local.tm_mday >= 25) : (tm_local.tm_mday < 25);
                }
            }
            
            if (is_bst) {
                return tp - std::chrono::hours(1); // UTC = BST - 1
            }
        }
        
        return tp; // Return original time if conversion fails
}

DateTimeUtils::time_point DateTimeUtils::add_duration(
    const time_point& tp, int days, int hours, int minutes) {

    auto duration = std::chrono::hours(days * 24) +
                   std::chrono::hours(hours) +
                   std::chrono::minutes(minutes);
    return tp + duration;
}

int DateTimeUtils::calculate_age(const time_point& birth_date, const time_point& as_of_date) {
    auto birth_time_t = std::chrono::system_clock::to_time_t(birth_date);
    auto as_of_time_t = std::chrono::system_clock::to_time_t(as_of_date);

    std::tm birth_tm, as_of_tm;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&birth_tm, &birth_time_t);
        localtime_s(&as_of_tm, &as_of_time_t);
#else
        localtime_r(&birth_time_t, &birth_tm);
        localtime_r(&as_of_time_t, &as_of_tm);
#endif
    }

    int age = as_of_tm.tm_year - birth_tm.tm_year;

    if (as_of_tm.tm_mon < birth_tm.tm_mon ||
        (as_of_tm.tm_mon == birth_tm.tm_mon && as_of_tm.tm_mday < birth_tm.tm_mday)) {
        age--;
    }

    return age;
}

bool DateTimeUtils::is_valid_date(int year, int month, int day) {
    if (month < 1 || month > 12) return false;
    if (day < 1) return false;

    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    int max_day = days_in_month[month - 1];

    // Check for leap year
    if (month == 2 && ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0)) {
        max_day = 29;
    }

    return day <= max_day;
}

int DateTimeUtils::get_iso_week(const time_point& tp) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::tm tm, jan1;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm, &time_t);
#else
        localtime_r(&time_t, &tm);
#endif
    }

    // Simplified ISO week calculation
    // In production, use a proper date library
    jan1 = tm;
    jan1.tm_mon = 0;
    jan1.tm_mday = 1;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        std::mktime(&jan1);
    }

    int days_since_jan1 = tm.tm_yday;
    int jan1_weekday = jan1.tm_wday;

    // Adjust for ISO week (Monday = 1, Sunday = 7)
    if (jan1_weekday == 0) jan1_weekday = 7;

    int iso_week = (days_since_jan1 + jan1_weekday - 1) / 7 + 1;

    return iso_week;
}

// FileUtils implementation
std::optional<std::string> FileUtils::read_file(const std::string& filepath) {
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return std::nullopt;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    return content;
}

bool FileUtils::write_file(const std::string& filepath,
                          const std::string& content,
                          bool append) {
    std::ios_base::openmode mode = std::ios::out;
    if (append) {
        mode |= std::ios::app;
    }

    std::ofstream file(filepath, mode);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

bool FileUtils::file_exists(const std::string& filepath) {
    return std::filesystem::exists(filepath) &&
           std::filesystem::is_regular_file(filepath);
}

std::optional<size_t> FileUtils::file_size(const std::string& filepath) {
    try {
        return std::filesystem::file_size(filepath);
    } catch (...) {
        return std::nullopt;
    }
}

std::string FileUtils::get_extension(const std::string& filepath) {
    return std::filesystem::path(filepath).extension().string();
}

std::string FileUtils::get_basename(const std::string& filepath) {
    return std::filesystem::path(filepath).stem().string();
}

std::string FileUtils::get_directory(const std::string& filepath) {
    return std::filesystem::path(filepath).parent_path().string();
}

bool FileUtils::create_directory(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (...) {
        return false;
    }
}

std::vector<std::string> FileUtils::list_files(const std::string& directory,
                                              const std::string& pattern,
                                              bool recursive) {
    std::vector<std::string> files;
    std::regex pattern_regex(pattern);

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (...) {
        // Return empty vector on error
    }

    return files;
}

bool FileUtils::copy_file(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::copy_file(source, destination,
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (...) {
        return false;
    }
}

bool FileUtils::move_file(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::rename(source, destination);
        return true;
    } catch (...) {
        return false;
    }
}

bool FileUtils::delete_file(const std::string& filepath) {
    try {
        return std::filesystem::remove(filepath);
    } catch (...) {
        return false;
    }
}

std::string FileUtils::get_temp_directory() {
    return std::filesystem::temp_directory_path().string();
}

std::string FileUtils::create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();
    std::string filename = prefix + "_" + StringUtils::random_string(8) + extension;
    return std::filesystem::path(temp_dir) / filename;
}

// SystemUtils implementation
std::optional<std::string> SystemUtils::get_env(const std::string& name) {
    const char* value = std::getenv(name.c_str());
    if (value) {
        return std::string(value);
    }
    return std::nullopt;
}

bool SystemUtils::set_env(const std::string& name, const std::string& value) {
#ifdef _WIN32
    return _putenv_s(name.c_str(), value.c_str()) == 0;
#else
    return setenv(name.c_str(), value.c_str(), 1) == 0;
#endif
}

std::string SystemUtils::get_current_directory() {
    return std::filesystem::current_path().string();
}

std::string SystemUtils::get_home_directory() {
#ifdef _WIN32
    const char* home = std::getenv("USERPROFILE");
    if (home) {
        return std::string(home);
    }
#else
    const char* home = std::getenv("HOME");
    if (home) {
        return std::string(home);
    }

    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_dir);
    }
#endif
    return "";
}

unsigned int SystemUtils::get_cpu_count() {
    return std::thread::hardware_concurrency();
}

size_t SystemUtils::get_available_memory() {
#ifdef _WIN32
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return status.ullAvailPhys;
    }
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
#elif defined(__APPLE__)
    vm_statistics64_data_t vm_stat;
    mach_msg_type_number_t count = HOST_VM_INFO64_COUNT;
    if (host_statistics64(mach_host_self(), HOST_VM_INFO64, (host_info64_t)&vm_stat, &count) == KERN_SUCCESS) {
        return vm_stat.free_count * vm_page_size;
    }
#endif
    return 0;
}

int SystemUtils::get_process_id() {
#ifdef _WIN32
    return GetCurrentProcessId();
#else
    return getpid();
#endif
}

std::optional<std::string> SystemUtils::execute_command(const std::string& command) {
    // Custom deleter for FILE*
    auto file_deleter = [](FILE* f) {
        if (f) {
            int result = pclose(f);
            if (result == -1) {
                // Log error but don't throw in destructor
            }
        }
    };
    
    std::unique_ptr<FILE, decltype(file_deleter)> pipe(
        popen(command.c_str(), "r"), 
        file_deleter
    );
    
    if (!pipe) {
        return std::nullopt;
    }

    std::array<char, 128> buffer;
    std::string result;
    
    while (fgets(buffer.data(), static_cast<int>(buffer.size()), pipe.get()) != nullptr) {
        result += buffer.data();
    }

    return result;
}

std::string SystemUtils::get_hostname() {
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        return std::string(hostname);
    }
    return "unknown";
}

std::string SystemUtils::get_username() {
#ifdef _WIN32
    char username[256];
    DWORD size = sizeof(username);
    if (GetUserNameA(username, &size)) {
        return std::string(username);
    }
#else
    const char* user = std::getenv("USER");
    if (user) {
        return std::string(user);
    }

    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_name);
    }
#endif
    return "unknown";
}

// CryptoUtils implementation
std::string CryptoUtils::md5(const std::string& data) {
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        throw std::runtime_error("Failed to create EVP_MD_CTX");
    }
    
    if (EVP_DigestInit_ex(mdctx, EVP_md5(), nullptr) != 1 ||
        EVP_DigestUpdate(mdctx, data.c_str(), data.length()) != 1 ||
        EVP_DigestFinal_ex(mdctx, digest, &digest_len) != 1) {
        EVP_MD_CTX_free(mdctx);
        throw std::runtime_error("MD5 computation failed");
    }
    
    EVP_MD_CTX_free(mdctx);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (unsigned int i = 0; i < digest_len; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::sha256(const std::string& data) {
    unsigned char digest[SHA256_DIGEST_LENGTH];
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        throw std::runtime_error("Failed to create EVP_MD_CTX");
    }
    
    if (EVP_DigestInit_ex(mdctx, EVP_sha256(), nullptr) != 1 ||
        EVP_DigestUpdate(mdctx, data.c_str(), data.length()) != 1 ||
        EVP_DigestFinal_ex(mdctx, digest, nullptr) != 1) {
        EVP_MD_CTX_free(mdctx);
        throw std::runtime_error("SHA256 computation failed");
    }
    
    EVP_MD_CTX_free(mdctx);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::base64_encode(const std::vector<uint8_t>& data) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);

    BUF_MEM* buffer_ptr;
    BIO_get_mem_ptr(bio, &buffer_ptr);

    std::string result(buffer_ptr->data, buffer_ptr->length);
    BIO_free_all(bio);

    return result;
}

std::vector<uint8_t> CryptoUtils::base64_decode(const std::string& encoded) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new_mem_buf(encoded.c_str(), encoded.length());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);

    std::vector<uint8_t> result(encoded.length());
    int decoded_length = BIO_read(bio, result.data(), encoded.length());
    BIO_free_all(bio);

    result.resize(decoded_length);
    return result;
}

std::string CryptoUtils::generate_uuid() {
    uuid_t uuid;
    uuid_generate(uuid);

    char uuid_str[37];
    uuid_unparse_lower(uuid, uuid_str);

    return std::string(uuid_str);
}

std::vector<uint8_t> CryptoUtils::random_bytes(size_t length) {
    std::vector<uint8_t> result(length);
    
    if (RAND_bytes(result.data(), length) != 1) {
        // Fall back to C++ random if OpenSSL fails
        std::uniform_int_distribution<> dis(0, 255);
        
        // Use thread-local generator
        std::lock_guard<std::mutex> lock(random_mutex);
        
        // Log warning about fallback
        std::cerr << "Warning: OpenSSL RAND_bytes failed, using C++ random fallback\n";

        for (size_t i = 0; i < length; ++i) {
            result[i] = static_cast<uint8_t>(dis(tl_random_gen));
        }
    }

    return result;
}

// Static mutex for thread safety
std::mutex CryptoUtils::random_mutex;

// PerformanceUtils::MemoryTracker implementation
std::atomic<size_t> PerformanceUtils::MemoryTracker::global_peak_usage_{0};

// ValidationUtils implementation
bool ValidationUtils::is_valid_email(const std::string& email) {
    const std::regex pattern(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)");
    return std::regex_match(email, pattern);
}

bool ValidationUtils::is_valid_url(const std::string& url) {
    // Using simpler regex pattern from isValidURL for consistency
    const std::regex pattern(R"((https?|ftp)://[^\s/$.?#].[^\s]*)");
    return std::regex_match(url, pattern);
}

bool ValidationUtils::is_valid_ip(const std::string& ip) {
    // IPv4 pattern
    const std::regex ipv4_pattern(
        R"(^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");

    // Simplified IPv6 pattern
    const std::regex ipv6_pattern(
        R"(^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::1|::)$)");

    return std::regex_match(ip, ipv4_pattern) || std::regex_match(ip, ipv6_pattern);
}

bool ValidationUtils::is_valid_phone(const std::string& phone, const std::string& country_code) {
    if (country_code == "US") {
        // US phone number validation with cleaning (synced from isValidPhoneNumber)
        std::string cleaned = StringUtils::replace_all(phone, " ", "");
        cleaned = StringUtils::replace_all(cleaned, "-", "");
        cleaned = StringUtils::replace_all(cleaned, "(", "");
        cleaned = StringUtils::replace_all(cleaned, ")", "");
        cleaned = StringUtils::replace_all(cleaned, "+1", "");

        // Check if it's a 10-digit number
        if (cleaned.length() == 10 && std::all_of(cleaned.begin(), cleaned.end(), ::isdigit)) {
            return true;
        }

        // Check if it's a 11-digit number starting with 1
        if (cleaned.length() == 11 && cleaned[0] == '1' &&
            std::all_of(cleaned.begin() + 1, cleaned.end(), ::isdigit)) {
            return true;
        }
        
        // Also support regex pattern for flexibility
        const std::regex pattern(R"(^\+?1?\s*\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$)");
        return std::regex_match(phone, pattern);
    }
    else if (country_code == "GB" || country_code == "UK") {
        return is_valid_uk_phone(phone);
    }
    
    // For unsupported countries, return false or implement additional patterns
    return false;
}

bool ValidationUtils::is_valid_postal_code(const std::string& postal_code,
                                          const std::string& country_code) {
    if (country_code == "US") {
        const std::regex pattern(R"(^\d{5}(-\d{4})?$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "CA") {
        const std::regex pattern(R"(^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "GB" || country_code == "UK") {
        return is_valid_uk_postcode(postal_code);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool ValidationUtils::is_valid_uk_postcode(const std::string& postal_code) {
    // UK postcode format validation
    // Format: Area (1-2 letters) + District (1-2 digits + optional letter) + Space + Sector (1 digit) + Unit (2 letters)
    // Examples: M1 1AA, M60 1NW, B33 8TH, W1A 0AX, GIR 0AA (special case)
    if (postal_code.empty() || postal_code.length() < 6 || postal_code.length() > 8) {
        return false;
    }
    
    // Special case for GIR 0AA (Girobank)
    if (postal_code == "GIR 0AA") {
        return true;
    }
    
    // Standard UK postcode pattern
    static const std::regex uk_postcode_pattern(
        R"(^[A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2}$)",
        std::regex_constants::icase
    );
    
    return std::regex_match(postal_code, uk_postcode_pattern);
}

bool ValidationUtils::is_valid_nhs_number(const std::string& nhs_number) {
    // NHS number validation (10 digits with checksum)
    // Remove spaces and validate format
    std::string cleaned = nhs_number;
    cleaned.erase(std::remove_if(cleaned.begin(), cleaned.end(), ::isspace), cleaned.end());
    
    if (cleaned.length() != 10) {
        return false;
    }
    
    // Check all characters are digits
    if (!std::all_of(cleaned.begin(), cleaned.end(), ::isdigit)) {
        return false;
    }
    
    // NHS number checksum validation (Modulus 11)
    int sum = 0;
    for (int i = 0; i < 9; ++i) {
        sum += (cleaned[i] - '0') * (10 - i);
    }
    
    int remainder = sum % 11;
    int check_digit = 11 - remainder;
    
    if (check_digit == 11) {
        check_digit = 0;
    }
    
    // If check digit is 10, NHS number is invalid
    if (check_digit == 10) {
        return false;
    }
    
    return (cleaned[9] - '0') == check_digit;
}

bool ValidationUtils::is_valid_uk_ni_number(const std::string& ni_number) {
    // UK National Insurance number format: 2 letters, 6 digits, 1 letter
    // Example: *********
    if (ni_number.length() != 9) {
        return false;
    }
    
    // Check first two characters are letters
    if (!std::isalpha(ni_number[0]) || !std::isalpha(ni_number[1])) {
        return false;
    }
    
    // Check next 6 characters are digits
    for (int i = 2; i < 8; ++i) {
        if (!std::isdigit(ni_number[i])) {
            return false;
        }
    }
    
    // Check last character is letter
    if (!std::isalpha(ni_number[8])) {
        return false;
    }
    
    // Check for invalid prefixes
    static const std::vector<std::string> invalid_prefixes = {
        "BG", "GB", "NK", "KN", "TN", "NT", "ZZ"
    };
    
    std::string prefix = ni_number.substr(0, 2);
    std::transform(prefix.begin(), prefix.end(), prefix.begin(), ::toupper);
    
    return std::find(invalid_prefixes.begin(), invalid_prefixes.end(), prefix) == invalid_prefixes.end();
}

bool ValidationUtils::is_valid_snomed_code(const std::string& code) {
    // SNOMED CT identifier validation
    // Must be 6-18 digits, and pass Luhn algorithm-like check
    if (code.empty() || code.length() < 6 || code.length() > 18) {
        return false;
    }
    
    // Check all characters are digits
    if (!std::all_of(code.begin(), code.end(), ::isdigit)) {
        return false;
    }
    
    // SNOMED CT uses a simple checksum: last digit is check digit
    // For simplicity, we'll accept any properly formatted numeric code
    // Full SNOMED validation would require access to the terminology server
    return true;
}

bool ValidationUtils::is_valid_read_code(const std::string& code) {
    // Read code format validation (UK legacy clinical coding system)
    // Read codes are typically 5 characters: letters and numbers
    // Examples: G20.., 14A6., C10z.
    if (code.empty() || code.length() < 3 || code.length() > 7) {
        return false;
    }
    
    // Read codes contain alphanumeric characters and dots/dashes
    static const std::regex read_code_pattern(R"(^[A-Za-z0-9][A-Za-z0-9\.]*[A-Za-z0-9\.]$)");
    return std::regex_match(code, read_code_pattern);
}

bool ValidationUtils::is_valid_uk_phone(const std::string& phone) {
    // UK phone number validation
    // Accepts various formats: +44, 0044, or just starting with 0
    std::string cleaned = phone;
    
    // Remove spaces, hyphens, and parentheses
    cleaned.erase(std::remove_if(cleaned.begin(), cleaned.end(),
                                [](char c) { return c == ' ' || c == '-' || c == '(' || c == ')'; }),
                 cleaned.end());
    
    // Handle international formats
    if (cleaned.substr(0, 3) == "+44") {
        cleaned = "0" + cleaned.substr(3);
    } else if (cleaned.substr(0, 4) == "0044") {
        cleaned = "0" + cleaned.substr(4);
    }
    
    // UK numbers should start with 0 and be 11 digits total
    if (cleaned.length() != 11 || cleaned[0] != '0') {
        return false;
    }
    
    // Check all characters are digits
    if (!std::all_of(cleaned.begin(), cleaned.end(), ::isdigit)) {
        return false;
    }
    
    // Validate UK area codes (basic validation)
    // Check prefix patterns instead of exhaustive list
    static const std::vector<std::string> valid_prefixes = {
        "0113", "0114", "0115", "0116", "0117", "0118", "0121", "0131", "0141", "0151",
        "0161", "0191", "0203", "0207", "0208", "020", "01", "02", "03", "07", "08", "09"
    };
    
    // Simple area code validation
    return cleaned.substr(0, 2) == "01" || cleaned.substr(0, 2) == "02" ||
           cleaned.substr(0, 2) == "03" || cleaned.substr(0, 2) == "07" ||
           cleaned.substr(0, 2) == "08" || cleaned.substr(0, 2) == "09";
}

bool ValidationUtils::is_valid_date_format(const std::string& date_str,
                                          const std::string& format) {
    std::tm tm = {};
    std::istringstream ss(date_str);
    ss >> std::get_time(&tm, format.c_str());
    
    // Check if parsing failed
    if (ss.fail()) {
        return false;
    }
    
    // Verify that the parsed date is actually valid
    // tm_year is years since 1900, tm_mon is 0-11
    int year = tm.tm_year + 1900;
    int month = tm.tm_mon + 1;
    int day = tm.tm_mday;
    
    return DateTimeUtils::is_valid_date(year, month, day);
}

bool ValidationUtils::is_valid_json(const std::string& json) {
    try {
        auto parsed = nlohmann::json::parse(json);
        return true;
    } catch (...) {
        return false;
    }
}

bool ValidationUtils::is_valid_sql_identifier(const std::string& identifier) {
    if (identifier.empty()) {
        return false;
    }

    // Check if starts with letter or underscore
    if (!std::isalpha(identifier[0]) && identifier[0] != '_') {
        return false;
    }

    // Check each character
    for (char c : identifier) {
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }

    // Check for reserved words (basic check)
    std::string upper_identifier = StringUtils::to_upper(identifier);
    static const std::vector<std::string> reserved_words = {
        "SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "TABLE", "INDEX"
    };

    for (const auto& word : reserved_words) {
        if (upper_identifier == word) {
            return false;
        }
    }

    return true;
}

bool ValidationUtils::is_valid_uuid(const std::string& uuid) {
    // UUID validation regex (8-4-4-4-12 format) with anchors for exact match
    static const std::regex uuid_regex(R"(^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$)");
    return std::regex_match(uuid, uuid_regex);
}

std::string ValidationUtils::sanitize_string(const std::string& input) {
    std::string result = input;

    // Remove or escape potentially dangerous characters (HTML escaping)
    result = StringUtils::replace_all(result, "&", "&amp;");  // Must be first
    result = StringUtils::replace_all(result, "<", "&lt;");
    result = StringUtils::replace_all(result, ">", "&gt;");
    result = StringUtils::replace_all(result, "\"", "&quot;");
    result = StringUtils::replace_all(result, "'", "&#39;");

    // Remove control characters except newline and tab
    result.erase(std::remove_if(result.begin(), result.end(),
                               [](char c) { return std::iscntrl(c) && c != '\n' && c != '\t'; }),
                result.end());

    return result;
}



// PerformanceUtils implementation
PerformanceUtils::MemoryTracker::MemoryTracker() {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024; // Convert KB to bytes
        peak_usage_ = initial_usage_;
    }
#endif
}

size_t PerformanceUtils::MemoryTracker::current_usage() const {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        size_t current = pmc.WorkingSetSize;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        // Update global peak if necessary
        size_t expected = global_peak_usage_.load();
        while (expected < current && !global_peak_usage_.compare_exchange_weak(expected, current)) {}
        
        return current - initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        size_t current = usage.ru_maxrss * 1024;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        // Update global peak if necessary
        size_t expected = global_peak_usage_.load();
        while (expected < current && !global_peak_usage_.compare_exchange_weak(expected, current)) {}
        
        return current - initial_usage_;
    }
#endif
    return 0;
}

size_t PerformanceUtils::MemoryTracker::peak_usage() const {
    return peak_usage_ - initial_usage_;
}

void PerformanceUtils::MemoryTracker::reset() {
    initial_usage_ = 0;
    peak_usage_ = 0;

#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024;
        peak_usage_ = initial_usage_;
    }
#endif
}

std::string PerformanceUtils::format_bytes(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return ss.str();
}

std::string PerformanceUtils::format_duration(double seconds) {
    if (seconds < 1.0) {
        return std::to_string(static_cast<int>(seconds * 1000)) + "ms";
    }

    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds - hours * 3600 - minutes * 60);

    std::stringstream ss;
    if (hours > 0) {
        ss << hours << "h ";
    }
    if (minutes > 0 || hours > 0) {
        ss << minutes << "m ";
    }
    ss << secs << "s";

    return ss.str();
}

double PerformanceUtils::calculate_throughput(size_t items, double seconds) {
    if (seconds <= 0) {
        return 0.0;
    }
    return static_cast<double>(items) / seconds;
}

std::string ProcessingUtils::stage_name(int stage) {
    switch (stage) {
        case 0: // Extract
            return "Extract";
        case 1: // Transform
            return "Transform";
        case 2: // Load
            return "Load";
        default:
            return "Unknown";
    }
}

// Additional utility functions for UK localization
namespace UKLocalization {
    
    std::string format_uk_currency(double amount) {
        std::stringstream ss;
        ss << "£" << std::fixed << std::setprecision(2) << amount;
        return ss.str();
    }
    
    std::string format_uk_date(const std::chrono::system_clock::time_point& tp) {
        return DateTimeUtils::format_date(tp, "%d/%m/%Y");
    }
    
    std::string format_uk_datetime(const std::chrono::system_clock::time_point& tp) {
        return DateTimeUtils::format_date(tp, "%d/%m/%Y %H:%M:%S");
    }
    
    double celsius_to_fahrenheit(double celsius) {
        return (celsius * 9.0 / 5.0) + 32.0;
    }
    
    double fahrenheit_to_celsius(double fahrenheit) {
        return (fahrenheit - 32.0) * 5.0 / 9.0;
    }
    
    std::string format_temperature_celsius(double celsius) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(1) << celsius << "°C";
        return ss.str();
    }
    
    std::string format_uk_number(double number, int decimal_places) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(decimal_places) << number;
        return ss.str();
    }
    
    std::string format_uk_address(const std::string& line1, 
                                 const std::string& line2,
                                 const std::string& city,
                                 const std::string& county,
                                 const std::string& postcode) {
        std::stringstream ss;
        ss << line1;
        if (!line2.empty()) {
            ss << "\n" << line2;
        }
        ss << "\n" << city;
        if (!county.empty()) {
            ss << "\n" << county;
        }
        ss << "\n" << postcode;
        return ss.str();
    }
}

} // namespace omop::common