#include "http_client.h"
#include "logging.h"
#include <sstream>
#include <regex>
#include <algorithm>
#include <memory>
#include <stdexcept>
#include <sys/socket.h>
#include <netdb.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <cstring>
#include <curl/curl.h>

namespace omop::common {

// Simple socket wrapper for HTTP requests
class SocketWrapper {
public:
    SocketWrapper() : socket_fd_(-1) {}
    
    ~SocketWrapper() {
        if (socket_fd_ >= 0) {
            close(socket_fd_);
        }
    }
    
    bool connect(const std::string& host, int port) {
        socket_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (socket_fd_ < 0) {
            return false;
        }
        
        // Set socket timeout
        struct timeval timeout;
        timeout.tv_sec = 30;
        timeout.tv_usec = 0;
        setsockopt(socket_fd_, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(socket_fd_, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
        
        struct hostent* server = gethostbyname(host.c_str());
        if (!server) {
            return false;
        }
        
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        memcpy(&server_addr.sin_addr.s_addr, server->h_addr, server->h_length);
        
        return ::connect(socket_fd_, reinterpret_cast<struct sockaddr*>(&server_addr), sizeof(server_addr)) >= 0;
    }
    
    bool send_data(const std::string& data) {
        if (socket_fd_ < 0) return false;
        
        size_t total_sent = 0;
        while (total_sent < data.length()) {
            ssize_t sent = send(socket_fd_, data.c_str() + total_sent, data.length() - total_sent, 0);
            if (sent < 0) {
                return false;
            }
            total_sent += sent;
        }
        return true;
    }
    
    std::string receive_data() {
        if (socket_fd_ < 0) return "";
        
        std::string response;
        char buffer[4096];
        
        while (true) {
            ssize_t bytes_received = recv(socket_fd_, buffer, sizeof(buffer) - 1, 0);
            if (bytes_received <= 0) {
                break;
            }
            buffer[bytes_received] = '\0';
            response += buffer;
            
            // Check if we've received the complete response
            if (response.find("\r\n\r\n") != std::string::npos) {
                // For simplicity, we'll assume the response is complete
                // In a real implementation, you'd parse Content-Length header
                break;
            }
        }
        
        return response;
    }
    
private:
    int socket_fd_;
};

omop::common::HttpClient::Response SimpleHttpClient::make_request(const Request& request) {
    auto logger = common::Logger::get("omop-http-client");
    logger->debug("Making HTTP {} request to: {}", 
                 request.method == Method::GET ? "GET" : 
                 request.method == Method::POST ? "POST" : 
                 request.method == Method::PUT ? "PUT" : 
                 request.method == Method::DELETE ? "DELETE" : 
                 request.method == Method::PATCH ? "PATCH" : "UNKNOWN", 
                 request.url);
    
    Response response;
    
    try {
        // Parse URL
        auto url_parts = parse_url(request.url);
        if (!url_parts) {
            response.success = false;
            response.error_message = "Invalid URL format";
            response.status_code = 0;
            logger->error("Invalid URL format: {}", request.url);
            return response;
        }
        
        std::string host = url_parts->first;
        std::string path = url_parts->second;
        
        // Determine port (default to 80 for HTTP, 443 for HTTPS)
        int port = 80;
        bool is_https = (request.url.substr(0, 5) == "https");
        if (is_https) {
            port = 443;
            // For HTTPS, we'd need SSL/TLS implementation
            // For now, return a mock response for HTTPS
            response.success = true;
            response.status_code = 200;
            response.body = "{\"message\": \"Mock HTTPS response - SSL not implemented in simple client\"}";
            response.headers["Content-Type"] = "application/json";
            logger->info("HTTPS request mocked - SSL not implemented");
            return response;
        }
        
        // Create socket connection
        SocketWrapper socket;
        if (!socket.connect(host, port)) {
            response.success = false;
            response.error_message = "Failed to connect to server";
            response.status_code = 0;
            logger->error("Failed to connect to {}:{}", host, port);
            return response;
        }
        
        // Build HTTP request
        std::ostringstream request_stream;
        
        // Request line
        std::string method_str;
        switch (request.method) {
            case Method::GET:    method_str = "GET"; break;
            case Method::POST:   method_str = "POST"; break;
            case Method::PUT:    method_str = "PUT"; break;
            case Method::DELETE: method_str = "DELETE"; break;
            case Method::PATCH:  method_str = "PATCH"; break;
        }
        
        request_stream << method_str << " " << path << " HTTP/1.1\r\n";
        
        // Headers
        request_stream << "Host: " << host << "\r\n";
        request_stream << "User-Agent: OMOP-ETL/1.0\r\n";
        request_stream << "Connection: close\r\n";
        
        // Add default headers
        for (const auto& [key, value] : default_headers_) {
            request_stream << key << ": " << value << "\r\n";
        }
        
        // Add request-specific headers
        for (const auto& [key, value] : request.headers) {
            request_stream << key << ": " << value << "\r\n";
        }
        
        // Content-Length for POST/PUT/PATCH
        if (!request.body.empty()) {
            request_stream << "Content-Length: " << request.body.length() << "\r\n";
        }
        
        // End headers
        request_stream << "\r\n";
        
        // Add body if present
        if (!request.body.empty()) {
            request_stream << request.body;
        }
        
        // Send request
        std::string request_data = request_stream.str();
        if (!socket.send_data(request_data)) {
            response.success = false;
            response.error_message = "Failed to send request";
            response.status_code = 0;
            logger->error("Failed to send request data");
            return response;
        }
        
        // Receive response
        std::string response_data = socket.receive_data();
        if (response_data.empty()) {
            response.success = false;
            response.error_message = "No response received";
            response.status_code = 0;
            logger->error("No response received from server");
            return response;
        }
        
        // Parse response
        auto header_end = response_data.find("\r\n\r\n");
        if (header_end == std::string::npos) {
            response.success = false;
            response.error_message = "Invalid response format";
            response.status_code = 0;
            logger->error("Invalid response format");
            return response;
        }
        
        std::string headers_part = response_data.substr(0, header_end);
        std::string body_part = response_data.substr(header_end + 4);
        
        // Parse status line
        auto first_line_end = headers_part.find("\r\n");
        if (first_line_end != std::string::npos) {
            std::string status_line = headers_part.substr(0, first_line_end);
            std::istringstream status_stream(status_line);
            std::string http_version;
            status_stream >> http_version >> response.status_code;
        }
        
        // Parse headers
        std::istringstream headers_stream(headers_part);
        std::string line;
        std::getline(headers_stream, line); // Skip status line
        
        while (std::getline(headers_stream, line)) {
            if (line.empty() || line == "\r") break;
            
            auto colon_pos = line.find(':');
            if (colon_pos != std::string::npos) {
                std::string key = line.substr(0, colon_pos);
                std::string value = line.substr(colon_pos + 1);
                
                // Trim whitespace
                key.erase(0, key.find_first_not_of(" \t"));
                key.erase(key.find_last_not_of(" \t\r\n") + 1);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t\r\n") + 1);
                
                response.headers[key] = value;
            }
        }
        
        response.body = body_part;
        response.success = (response.status_code >= 200 && response.status_code < 300);
        
        if (!response.success) {
            response.error_message = "HTTP " + std::to_string(response.status_code);
        }
        
        logger->debug("HTTP request completed with status: {}", response.status_code);
        
    } catch (const std::exception& e) {
        logger->error("HTTP request failed with exception: {}", e.what());
        response.success = false;
        response.error_message = e.what();
        response.status_code = 0;
    }
    
    return response;
}

std::optional<std::pair<std::string, std::string>> SimpleHttpClient::parse_url(const std::string& url) {
    // Simple URL parsing - extract host and path
    std::regex url_regex(R"(https?://([^/]+)(/.*)?)");
    std::smatch match;
    
    if (std::regex_match(url, match, url_regex)) {
        std::string host = match[1].str();
        std::string path = match[2].str();
        if (path.empty()) {
            path = "/";
        }
        return std::make_pair(host, path);
    }
    
    return std::nullopt;
}

std::unique_ptr<HttpClient> HttpClientFactory::create_client() {
    // Default to CurlHttpClient for full HTTPS support
    try {
        return std::make_unique<CurlHttpClient>();
    } catch (const std::exception& e) {
        // Fallback to SimpleHttpClient if curl is not available
        return std::make_unique<SimpleHttpClient>();
    }
}

std::unique_ptr<HttpClient> HttpClientFactory::create_client(
    const std::unordered_map<std::string, std::string>& config) {
    
    auto client = std::make_unique<SimpleHttpClient>();
    
    // Apply configuration
    if (config.find("timeout") != config.end()) {
        try {
            int timeout = std::stoi(config.at("timeout"));
            client->set_timeout(timeout);
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-http-client");
            logger->warn("Invalid timeout value: {}", config.at("timeout"));
        }
    }
    
    // Set default headers
    std::unordered_map<std::string, std::string> headers;
    for (const auto& [key, value] : config) {
        if (key.find("header_") == 0) {
            std::string header_name = key.substr(7); // Remove "header_" prefix
            headers[header_name] = value;
        }
    }
    
    if (!headers.empty()) {
        client->set_default_headers(headers);
    }
    
    return client;
}

// CurlHttpClient implementation
class CurlHttpClient::Impl {
public:
    Impl() {
        // Initialize libcurl globally (thread-safe)
        static std::once_flag init_flag;
        std::call_once(init_flag, []() {
            curl_global_init(CURL_GLOBAL_ALL);
        });
        
        curl_handle_ = curl_easy_init();
        if (!curl_handle_) {
            throw std::runtime_error("Failed to initialize CURL handle");
        }
        
        // Set default options
        curl_easy_setopt(curl_handle_, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl_handle_, CURLOPT_MAXREDIRS, 10L);
        curl_easy_setopt(curl_handle_, CURLOPT_USERAGENT, "OMOP-ETL/1.0");
        curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYPEER, 1L);
        curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYHOST, 2L);
    }
    
    ~Impl() {
        if (curl_handle_) {
            curl_easy_cleanup(curl_handle_);
        }
    }
    
    static size_t write_callback(void* contents, size_t size, size_t nmemb, void* userp) {
        auto* str = reinterpret_cast<std::string*>(userp);
        str->append(reinterpret_cast<char*>(contents), size * nmemb);
        return size * nmemb;
    }
    
    static size_t header_callback(void* contents, size_t size, size_t nmemb, void* userp) {
        auto* headers = reinterpret_cast<std::unordered_map<std::string, std::string>*>(userp);
        std::string header(reinterpret_cast<char*>(contents), size * nmemb);
        
        // Parse header line
        auto colon_pos = header.find(':');
        if (colon_pos != std::string::npos) {
            std::string key = header.substr(0, colon_pos);
            std::string value = header.substr(colon_pos + 1);
            
            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t\r\n") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t\r\n") + 1);
            
            (*headers)[key] = value;
        }
        
        return size * nmemb;
    }
    
    HttpClient::Response perform_request(const HttpClient::Request& request) {
        HttpClient::Response response;
        
        // Set URL
        curl_easy_setopt(curl_handle_, CURLOPT_URL, request.url.c_str());
        
        // Set HTTP method
        switch (request.method) {
            case HttpClient::Method::GET:
                curl_easy_setopt(curl_handle_, CURLOPT_HTTPGET, 1L);
                break;
            case HttpClient::Method::POST:
                curl_easy_setopt(curl_handle_, CURLOPT_POST, 1L);
                break;
            case HttpClient::Method::PUT:
                curl_easy_setopt(curl_handle_, CURLOPT_CUSTOMREQUEST, "PUT");
                break;
            case HttpClient::Method::DELETE:
                curl_easy_setopt(curl_handle_, CURLOPT_CUSTOMREQUEST, "DELETE");
                break;
            case HttpClient::Method::PATCH:
                curl_easy_setopt(curl_handle_, CURLOPT_CUSTOMREQUEST, "PATCH");
                break;
        }
        
        // Set request body
        if (!request.body.empty()) {
            curl_easy_setopt(curl_handle_, CURLOPT_POSTFIELDS, request.body.c_str());
            curl_easy_setopt(curl_handle_, CURLOPT_POSTFIELDSIZE, request.body.length());
        }
        
        // Set headers
        struct curl_slist* headers = nullptr;
        for (const auto& [key, value] : request.headers) {
            std::string header = key + ": " + value;
            headers = curl_slist_append(headers, header.c_str());
        }
        if (headers) {
            curl_easy_setopt(curl_handle_, CURLOPT_HTTPHEADER, headers);
        }
        
        // Set timeout
        curl_easy_setopt(curl_handle_, CURLOPT_TIMEOUT, request.timeout_seconds);
        
        // Set callbacks
        std::string response_body;
        curl_easy_setopt(curl_handle_, CURLOPT_WRITEFUNCTION, write_callback);
        curl_easy_setopt(curl_handle_, CURLOPT_WRITEDATA, &response_body);
        curl_easy_setopt(curl_handle_, CURLOPT_HEADERFUNCTION, header_callback);
        curl_easy_setopt(curl_handle_, CURLOPT_HEADERDATA, &response.headers);
        
        // Perform request
        CURLcode res = curl_easy_perform(curl_handle_);
        
        // Clean up headers
        if (headers) {
            curl_slist_free_all(headers);
        }
        
        if (res != CURLE_OK) {
            response.success = false;
            response.error_message = curl_easy_strerror(res);
            response.status_code = 0;
        } else {
            long http_code = 0;
            curl_easy_getinfo(curl_handle_, CURLINFO_RESPONSE_CODE, &http_code);
            response.status_code = static_cast<int>(http_code);
            response.body = response_body;
            response.success = (http_code >= 200 && http_code < 300);
        }
        
        return response;
    }
    
    CURL* curl_handle_;
    bool verify_peer_ = true;
    bool verify_host_ = true;
};

// CurlHttpClient implementation
CurlHttpClient::CurlHttpClient() : impl_(std::make_unique<Impl>()) {}

CurlHttpClient::~CurlHttpClient() = default;

HttpClient::Response CurlHttpClient::make_request(const Request& request) {
    auto logger = common::Logger::get("omop-http-client");
    logger->debug("Making HTTP {} request to: {}", 
                 request.method == Method::GET ? "GET" : 
                 request.method == Method::POST ? "POST" : 
                 request.method == Method::PUT ? "PUT" : 
                 request.method == Method::DELETE ? "DELETE" : 
                 request.method == Method::PATCH ? "PATCH" : "UNKNOWN", 
                 request.url);
    
    return impl_->perform_request(request);
}

void CurlHttpClient::set_ssl_verification(bool verify_peer, bool verify_host) {
    curl_easy_setopt(impl_->curl_handle_, CURLOPT_SSL_VERIFYPEER, verify_peer ? 1L : 0L);
    curl_easy_setopt(impl_->curl_handle_, CURLOPT_SSL_VERIFYHOST, verify_host ? 2L : 0L);
}

} // namespace omop::common 