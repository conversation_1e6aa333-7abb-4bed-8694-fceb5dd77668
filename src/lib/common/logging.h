/**
 * @file logging.h
 * @brief Logging utilities for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <vector>
#include <functional>
#include <mutex>
#include <optional>
#include <any>
#include <format>
#include <fstream> // Required for FileSink and RotatingFileSink
#include <atomic> // Required for Logger min_level_
#include <spdlog/spdlog.h>
#include <spdlog/sinks/sink.h>
#include <spdlog/fmt/ostr.h>

namespace omop::common {

/**
 * @brief Log level enumeration
 */
enum class LogLevel {
    Trace,
    Debug,
    Info,
    Warning,
    Error,
    Critical
};

/**
 * @brief Structured log entry
 *
 * Represents a single log entry with structured data for better analysis.
 */
struct LogEntry {
    std::chrono::system_clock::time_point timestamp;
    LogLevel level;
    std::string logger_name;
    std::string message;
    std::string thread_id;
    std::string job_id;
    std::string component;
    std::string operation;
    std::unordered_map<std::string, std::any> context;
    std::optional<std::string> error_code;
    std::optional<std::string> stack_trace;
};

/**
 * @brief Log formatter interface
 */
class ILogFormatter {
public:
    virtual ~ILogFormatter() = default;

    /**
     * @brief Format log entry
     * @param entry Log entry
     * @return std::string Formatted log message
     */
    virtual std::string format(const LogEntry& entry) = 0;
};

/**
 * @brief JSON log formatter
 *
 * Formats log entries as JSON for structured logging.
 */
class JsonLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Format log entry as JSON
     * @param entry Log entry
     * @return std::string JSON formatted log
     */
    std::string format(const LogEntry& entry) override;

    /**
     * @brief Set whether to pretty print JSON
     * @param pretty Whether to pretty print
     */
    void set_pretty_print(bool pretty) { pretty_print_ = pretty; }

private:
    bool pretty_print_{false};
};

/**
 * @brief Plain text log formatter
 *
 * Formats log entries as human-readable text.
 */
class TextLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Constructor
     * @param pattern Log pattern (default: "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v")
     */
    explicit TextLogFormatter(const std::string& pattern =
        "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v");

    /**
     * @brief Format log entry as text
     * @param entry Log entry
     * @return std::string Text formatted log
     */
    std::string format(const LogEntry& entry) override;

private:
    std::string pattern_;
};

/**
 * @brief Log sink interface
 */
class ILogSink {
public:
    virtual ~ILogSink() = default;

    /**
     * @brief Write log entry
     * @param entry Log entry
     */
    virtual void write(const LogEntry& entry) = 0;

    /**
     * @brief Flush buffered logs
     */
    virtual void flush() = 0;

    /**
     * @brief Set formatter
     * @param formatter Log formatter
     */
    void set_formatter(std::unique_ptr<ILogFormatter> formatter) {
        formatter_ = std::move(formatter);
    }

protected:
    std::unique_ptr<ILogFormatter> formatter_;
};

/**
 * @brief Abstract database log sink interface
 *
 * Interface for writing log entries to a database. Concrete implementations
 * should be provided by modules that have database access.
 */
class IDatabaseLogSink : public ILogSink {
public:
    virtual ~IDatabaseLogSink() = default;

    /**
     * @brief Create log table if not exists
     */
    virtual void create_table_if_not_exists() = 0;

protected:
    // Concrete implementations will handle database-specific details
    std::string table_name_;
    std::vector<LogEntry> buffer_;
    std::mutex buffer_mutex_;
};

/**
 * @brief Generic database log sink implementation
 *
 * This is a concrete implementation that can be used with any database
 * connection that implements the IDatabaseConnection interface.
 */
class GenericDatabaseLogSink : public IDatabaseLogSink {
public:
    GenericDatabaseLogSink(std::shared_ptr<void> connection, const std::string& table_name);
    ~GenericDatabaseLogSink() override;

    void create_table_if_not_exists() override;
    void write(const LogEntry& entry) override;
    void flush() override;

private:
    std::shared_ptr<void> connection_;
    static constexpr size_t BUFFER_SIZE = 100;
    void execute_sql(const std::string& sql);
};

/**
 * @brief File log sink implementation
 *
 * Writes log entries to a file with support for rotation.
 */
class FileSink : public ILogSink {
public:
    /**
     * @brief Constructor
     * @param filename Output file path
     * @param max_size Maximum file size before rotation (default: 10MB)
     * @param max_files Maximum number of backup files (default: 5)
     */
    explicit FileSink(const std::string& filename, 
                     size_t max_size = 10 * 1024 * 1024,
                     size_t max_files = 5);

    /**
     * @brief Destructor
     */
    ~FileSink() override;

    /**
     * @brief Write log entry to file
     * @param entry Log entry
     */
    void write(const LogEntry& entry) override;

    /**
     * @brief Flush buffered logs
     */
    void flush() override;

private:
    void rotate_if_needed();
    void open_file();
    void close_file();

    std::string filename_;
    size_t max_size_;
    size_t max_files_;
    std::ofstream file_stream_;
    size_t current_size_;
    std::mutex file_mutex_;
};

/**
 * @brief Console log sink implementation
 *
 * Writes log entries to stdout/stderr based on log level.
 */
class ConsoleSink : public ILogSink {
public:
    ConsoleSink(bool use_color = true);
    ~ConsoleSink() override = default;

    void write(const LogEntry& entry) override;
    void flush() override;

    void set_use_stderr_for_errors(bool use_stderr) {
        use_stderr_for_errors_ = use_stderr;
    }

private:
    bool use_color_;
    bool use_stderr_for_errors_;
};

/**
 * @brief Rotating file sink implementation
 *
 * Writes log entries to rotating files with automatic size-based rotation.
 */
class RotatingFileSink : public ILogSink {
public:
    /**
     * @brief Constructor
     * @param base_filename Base filename for rotation
     * @param max_size Maximum file size before rotation
     * @param max_files Maximum number of backup files
     */
    explicit RotatingFileSink(const std::string& base_filename,
                             size_t max_size,
                             size_t max_files);

    /**
     * @brief Destructor
     */
    ~RotatingFileSink() override;

    /**
     * @brief Write log entry to rotating file
     * @param entry Log entry
     */
    void write(const LogEntry& entry) override;

    /**
     * @brief Flush buffered logs
     */
    void flush() override;

private:
    void rotate();
    std::string get_current_filename() const;
    void cleanup_old_files();
    void open_file();

    std::string base_filename_;
    size_t max_size_;
    size_t max_files_;
    std::ofstream current_file_;
    size_t current_size_;
    std::mutex file_mutex_;
};

/**
 * @brief Database log sink implementation
 *
 * Writes log entries to a database table.
 */
class DatabaseLogSink : public IDatabaseLogSink {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param table_name Log table name
     */
    DatabaseLogSink(std::shared_ptr<void> connection, const std::string& table_name);

    /**
     * @brief Destructor
     */
    ~DatabaseLogSink() override;

    /**
     * @brief Create log table if not exists
     */
    void create_table_if_not_exists() override;

    /**
     * @brief Write log entry to database
     * @param entry Log entry
     */
    void write(const LogEntry& entry) override;

    /**
     * @brief Flush buffered logs
     */
    void flush() override;

private:
    void insert_log_entry(const LogEntry& entry);
    std::string format_timestamp(const std::chrono::system_clock::time_point& timestamp) const;

    std::shared_ptr<void> connection_;
    std::string table_name_;
    std::vector<LogEntry> buffer_;
    std::mutex buffer_mutex_;
    static constexpr size_t BUFFER_SIZE = 100;
};

/**
 * @brief ETL-specific logger
 *
 * Provides logging functionality tailored for ETL operations with
 * support for job tracking, component identification, and metrics.
 */
class Logger {
    friend class LoggingConfig;
public:
    /**
     * @brief Get logger instance
     * @param name Logger name
     * @return std::shared_ptr<Logger> Logger instance
     */
    static std::shared_ptr<Logger> get(const std::string& name);

    /**
     * @brief Constructor
     * @param name Logger name
     */
    explicit Logger(const std::string& name);

    /**
     * @brief Set log level
     * @param level Log level
     */
    void set_level(LogLevel level);

    /**
     * @brief Add log sink
     * @param sink Log sink
     */
    void add_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Clear all sinks
     */
    void clear_sinks() { 
        std::lock_guard<std::mutex> lock(sinks_mutex_);
        sinks_.clear(); 
    }

    /**
     * @brief Set job context
     * @param job_id Job identifier
     */
    void set_job_id(const std::string& job_id) { 
        std::lock_guard<std::mutex> lock(context_mutex_);
        job_id_ = job_id; 
    }

    /**
     * @brief Set component context
     * @param component Component name
     */
    void set_component(const std::string& component) { 
        std::lock_guard<std::mutex> lock(context_mutex_);
        component_ = component; 
    }

    /**
     * @brief Get logger name
     * @return const std::string& Logger name
     */
    [[nodiscard]] const std::string& get_name() const { return name_; }

    // Logging methods
    template<typename... Args>
    void trace(const std::string& msg, Args&&... args) {
        log(LogLevel::Trace, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void debug(const std::string& msg, Args&&... args) {
        log(LogLevel::Debug, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void info(const std::string& msg, Args&&... args) {
        log(LogLevel::Info, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void warn(const std::string& msg, Args&&... args) {
        log(LogLevel::Warning, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void error(const std::string& msg, Args&&... args) {
        log(LogLevel::Error, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void critical(const std::string& msg, Args&&... args) {
        log(LogLevel::Critical, msg, std::forward<Args>(args)...);
    }

    /**
     * @brief Log with structured context
     * @param level Log level
     * @param msg Message
     * @param context Additional context
     */
    void log_structured(LogLevel level, const std::string& msg,
                       const std::unordered_map<std::string, std::any>& context);

    /**
     * @brief Log ETL operation
     * @param operation Operation name
     * @param status Operation status
     * @param details Operation details
     */
    void log_operation(const std::string& operation,
                      const std::string& status,
                      const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Log ETL metrics
     * @param metrics Metrics map
     */
    void log_metrics(const std::unordered_map<std::string, double>& metrics);

    /**
     * @brief Log exception
     * @param e Exception
     * @param context Additional context
     */
    void log_exception(const std::exception& e,
                      const std::unordered_map<std::string, std::any>& context = {});

private:
    template<typename... Args>
    void log(LogLevel level, const std::string& format, Args&&... args) {
        if (level < min_level_) return;

        auto formatted = std::vformat(format, std::make_format_args(args...));
        LogEntry entry{
            .timestamp = std::chrono::system_clock::now(),
            .level = level,
            .logger_name = name_,
            .message = formatted,
            .thread_id = get_thread_id(),
            .job_id = job_id_,
            .component = component_,
            .operation = "",
            .context = {},
            .error_code = std::nullopt,
            .stack_trace = std::nullopt
        };

        write_entry(entry);
    }

    void write_entry(const LogEntry& entry);
    std::string get_thread_id() const;
    void initialize_logger();

    std::string name_;
    mutable std::mutex context_mutex_;
    std::string job_id_;               // Protected by context_mutex_
    std::string component_;             // Protected by context_mutex_
    std::atomic<LogLevel> min_level_{LogLevel::Info};
    
    mutable std::mutex sinks_mutex_;
    std::vector<std::shared_ptr<ILogSink>> sinks_;
    std::shared_ptr<spdlog::logger> spdlog_logger_;

    static std::unordered_map<std::string, std::shared_ptr<Logger>> loggers_;
    static std::mutex loggers_mutex_;
};

/**
 * @brief Performance logger
 *
 * Specialized logger for tracking performance metrics and timings.
 */
class PerformanceLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     */
    explicit PerformanceLogger(std::shared_ptr<Logger> logger)
        : logger_(logger) {}

    /**
     * @brief Start timing an operation
     * @param operation_id Operation identifier
     */
    void start_timing(const std::string& operation_id);

    /**
     * @brief End timing an operation
     * @param operation_id Operation identifier
     * @param record_count Records processed (optional)
     */
    void end_timing(const std::string& operation_id,
                   std::optional<size_t> record_count = std::nullopt);

    /**
     * @brief Log throughput
     * @param operation Operation name
     * @param records_per_second Records per second
     */
    void log_throughput(const std::string& operation, double records_per_second);

    /**
     * @brief Log resource usage
     * @param cpu_percent CPU usage percentage
     * @param memory_mb Memory usage in MB
     * @param disk_io_mb Disk I/O in MB
     */
    void log_resource_usage(double cpu_percent, double memory_mb, double disk_io_mb);

    /**
     * @brief Log operation with timing
     * @param operation_id Operation identifier
     * @param start_time Start time
     * @param end_time End time
     */
    void log_operation(const std::string& operation_id,
                      const std::chrono::steady_clock::time_point& start_time,
                      const std::chrono::steady_clock::time_point& end_time);

    /**
     * @brief Export performance metrics
     * @return std::unordered_map<std::string, double> Performance metrics
     */
    std::unordered_map<std::string, double> export_metrics() const;

    /**
     * @brief RAII timer for automatic timing
     */
    class ScopedTimer {
    public:
        ScopedTimer(PerformanceLogger& logger, const std::string& operation_id)
            : logger_(logger), operation_id_(operation_id) {
            logger_.start_timing(operation_id_);
        }

        ~ScopedTimer() {
            if (record_count_.has_value()) {
                logger_.end_timing(operation_id_, record_count_);
            } else {
                logger_.end_timing(operation_id_);
            }
        }

        void set_record_count(size_t count) { record_count_ = count; }

    private:
        PerformanceLogger& logger_;
        std::string operation_id_;
        std::optional<size_t> record_count_;
    };

    /**
     * @brief Create scoped timer
     * @param operation_id Operation identifier
     * @return ScopedTimer Timer object
     */
    [[nodiscard]] ScopedTimer scoped_timer(const std::string& operation_id) {
        return ScopedTimer(*this, operation_id);
    }

private:
    std::shared_ptr<Logger> logger_;
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> timings_;
    mutable std::mutex timings_mutex_;
};

/**
 * @brief Audit logger
 *
 * Specialized logger for audit trail and compliance logging.
 */
class AuditLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     */
    explicit AuditLogger(std::shared_ptr<Logger> logger)
        : logger_(logger) {}

    /**
     * @brief Log data access
     * @param table_name Table accessed
     * @param operation Operation type (read/write)
     * @param record_count Number of records
     * @param user User identifier
     */
    void log_data_access(const std::string& table_name,
                        const std::string& operation,
                        size_t record_count,
                        const std::string& user);

    /**
     * @brief Log configuration change
     * @param config_item Configuration item
     * @param old_value Old value
     * @param new_value New value
     * @param user User making the change
     */
    void log_config_change(const std::string& config_item,
                          const std::string& old_value,
                          const std::string& new_value,
                          const std::string& user);

    /**
     * @brief Log security event
     * @param event_type Event type
     * @param details Event details
     */
    void log_security_event(const std::string& event_type,
                           const std::unordered_map<std::string, std::string>& details);

    /**
     * @brief Log data modification
     * @param user User identifier
     * @param table_name Table name
     * @param operation Operation type
     * @param record_count Number of records
     */
    void log_data_modification(const std::string& user,
                              const std::string& table_name,
                              const std::string& operation,
                              size_t record_count);

    /**
     * @brief Log data export
     * @param user User identifier
     * @param table_name Table name
     * @param operation Operation type
     * @param record_count Number of records
     */
    void log_data_export(const std::string& user,
                        const std::string& table_name,
                        const std::string& operation,
                        size_t record_count);

    /**
     * @brief Log access denied
     * @param user User identifier
     * @param table_name Table name
     * @param operation Operation type
     * @param reason Reason for denial
     */
    void log_access_denied(const std::string& user,
                          const std::string& table_name,
                          const std::string& operation,
                          const std::string& reason);

    /**
     * @brief Get audit trail
     * @return std::vector<std::string> Audit trail entries
     */
    std::vector<std::string> get_audit_trail() const;

private:
    std::shared_ptr<Logger> logger_;
    mutable std::vector<std::string> audit_trail_;
    mutable std::mutex audit_trail_mutex_;
};

/**
 * @brief Global logging configuration
 */
class LoggingConfig {
public:
    /**
     * @brief Initialize logging system
     * @param config_file Configuration file path
     */
    static void initialize(const std::string& config_file);

    /**
     * @brief Initialize with default configuration
     */
    static void initialize_default();

    /**
     * @brief Set global log level
     * @param level Log level
     */
    static void set_global_level(LogLevel level);

    /**
     * @brief Add global sink
     * @param sink Log sink
     */
    static void add_global_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Flush all loggers
     */
    static void flush_all();

    /**
     * @brief Shutdown logging system
     */
    static void shutdown();
};

// Convenience macros
#define LOG_TRACE(logger, ...) (logger)->trace(__VA_ARGS__)
#define LOG_DEBUG(logger, ...) (logger)->debug(__VA_ARGS__)
#define LOG_INFO(logger, ...) (logger)->info(__VA_ARGS__)
#define LOG_WARN(logger, ...) (logger)->warn(__VA_ARGS__)
#define LOG_ERROR(logger, ...) (logger)->error(__VA_ARGS__)
#define LOG_CRITICAL(logger, ...) (logger)->critical(__VA_ARGS__)

} // namespace omop::common