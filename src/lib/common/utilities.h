/**
 * @file utilities.h
 * @brief Common utility functions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides a comprehensive collection of utility functions for the OMOP ETL pipeline,
 * including string manipulation, date/time handling, file operations, and validation utilities.
 */

#pragma once

#include <string>
#include <vector>
#include <optional>
#include <chrono>
#include <filesystem>
#include <unordered_map>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <uuid/uuid.h>
#include <any>
#include <typeinfo>
#include <mutex>
#include <atomic>
#include <ctime>

namespace omop::common {

/**
 * @brief Convert std::any to string representation
 * @param value Any value to convert
 * @return String representation
 */
std::string any_to_string(const std::any& value);

/**
 * @brief String utility functions
 */
class StringUtils {
public:
    /**
     * @brief Trim whitespace from both ends of a string
     * @param str String to trim
     * @return Trimmed string
     */
    static std::string trim(const std::string& str);

    /**
     * @brief Convert string to lowercase
     * @param str Input string
     * @return Lowercase string
     */
    static std::string to_lower(const std::string& str);

    /**
     * @brief Convert string to uppercase
     * @param str Input string
     * @return Uppercase string
     */
    static std::string to_upper(const std::string& str);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return Vector of substrings
     */
    static std::vector<std::string> split(const std::string& str, char delimiter);

    /**
     * @brief Split string by string delimiter
     * @param str String to split
     * @param delimiter Delimiter string
     * @return Vector of substrings
     */
    static std::vector<std::string> split(const std::string& str, const std::string& delimiter);

    /**
     * @brief Join strings with delimiter
     * @param strings Strings to join
     * @param delimiter Delimiter to use
     * @return Joined string
     */
    static std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief Replace all occurrences of a substring
     * @param str Input string
     * @param from Substring to replace
     * @param to Replacement string
     * @return Modified string
     */
    static std::string replace_all(const std::string& str,
                                  const std::string& from,
                                  const std::string& to);

    /**
     * @brief Check if string starts with prefix
     * @param str String to check
     * @param prefix Prefix to look for
     * @return True if starts with prefix
     */
    static bool starts_with(const std::string& str, const std::string& prefix);

    /**
     * @brief Check if string ends with suffix
     * @param str String to check
     * @param suffix Suffix to look for
     * @return True if ends with suffix
     */
    static bool ends_with(const std::string& str, const std::string& suffix);

    /**
     * @brief Check if string contains substring
     * @param str String to search in
     * @param substr Substring to find
     * @return True if contains substring
     */
    static bool contains(const std::string& str, const std::string& substr);

    /**
     * @brief Convert string to snake_case
     * @param str Input string
     * @return Snake case string
     */
    static std::string to_snake_case(const std::string& str);

    /**
     * @brief Convert string to camelCase
     * @param str Input string
     * @return Camel case string
     */
    static std::string to_camel_case(const std::string& str);

    /**
     * @brief Escape SQL string
     * @param str String to escape
     * @return Escaped string
     */
    static std::string escape_sql(const std::string& str);

    /**
     * @brief Generate random string
     * @param length String length
     * @return Random string
     */
    static std::string random_string(size_t length);
};

/**
 * @brief Date and time utility functions
 */
class DateTimeUtils {
public:
    using time_point = std::chrono::system_clock::time_point;

    /**
     * @brief Parse date string to time_point
     * @param date_str Date string
     * @param format Date format
     * @return Parsed time_point
     */
    static std::optional<time_point> parse_date(const std::string& date_str,
                                               const std::string& format = "%Y-%m-%d");

    /**
     * @brief Format time_point to string
     * @param tp Time point
     * @param format Output format
     * @return Formatted string
     */
    static std::string format_date(const time_point& tp,
                                  const std::string& format = "%Y-%m-%d");

    /**
     * @brief Get current timestamp string
     * @param format Timestamp format
     * @return Current timestamp
     */
    static std::string current_timestamp(const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief Convert between timezones
     * @param tp Time point
     * @param from_tz Source timezone
     * @param to_tz Target timezone
     * @return Converted time point
     */
    static time_point convert_timezone(const time_point& tp,
                                      const std::string& from_tz,
                                      const std::string& to_tz);

    /**
     * @brief Add duration to time_point
     * @param tp Time point
     * @param days Days to add
     * @param hours Hours to add
     * @param minutes Minutes to add
     * @return New time point
     */
    static time_point add_duration(const time_point& tp,
                                  int days = 0,
                                  int hours = 0,
                                  int minutes = 0);

    /**
     * @brief Calculate age from birth date
     * @param birth_date Birth date
     * @param as_of_date Reference date (default: today)
     * @return Age in years
     */
    static int calculate_age(const time_point& birth_date,
                           const time_point& as_of_date = std::chrono::system_clock::now());

    /**
     * @brief Check if date is valid
     * @param year Year
     * @param month Month
     * @param day Day
     * @return True if valid date
     */
    static bool is_valid_date(int year, int month, int day);

    /**
     * @brief Get ISO week number
     * @param tp Time point
     * @return Week number (1-53)
     */
    static int get_iso_week(const time_point& tp);
};

/**
 * @brief File system utility functions
 */
class FileUtils {
public:
    /**
     * @brief Read entire file to string
     * @param filepath File path
     * @return File contents
     */
    static std::optional<std::string> read_file(const std::string& filepath);

    /**
     * @brief Write string to file
     * @param filepath File path
     * @param content Content to write
     * @param append Whether to append
     * @return True if successful
     */
    static bool write_file(const std::string& filepath,
                          const std::string& content,
                          bool append = false);

    /**
     * @brief Check if file exists
     * @param filepath File path
     * @return True if exists
     */
    static bool file_exists(const std::string& filepath);

    /**
     * @brief Get file size
     * @param filepath File path
     * @return File size in bytes
     */
    static std::optional<size_t> file_size(const std::string& filepath);

    /**
     * @brief Get file extension
     * @param filepath File path
     * @return Extension (including dot)
     */
    static std::string get_extension(const std::string& filepath);

    /**
     * @brief Get file name without extension
     * @param filepath File path
     * @return Base filename
     */
    static std::string get_basename(const std::string& filepath);

    /**
     * @brief Get directory path
     * @param filepath File path
     * @return Directory path
     */
    static std::string get_directory(const std::string& filepath);

    /**
     * @brief Create directory (recursive)
     * @param path Directory path
     * @return True if successful
     */
    static bool create_directory(const std::string& path);

    /**
     * @brief List files in directory
     * @param directory Directory path
     * @param pattern Regex pattern filter
     * @param recursive Whether to search recursively
     * @return List of file paths
     */
    static std::vector<std::string> list_files(const std::string& directory,
                                              const std::string& pattern = ".*",
                                              bool recursive = false);

    /**
     * @brief Copy file
     * @param source Source path
     * @param destination Destination path
     * @return True if successful
     */
    static bool copy_file(const std::string& source, const std::string& destination);

    /**
     * @brief Move/rename file
     * @param source Source path
     * @param destination Destination path
     * @return True if successful
     */
    static bool move_file(const std::string& source, const std::string& destination);

    /**
     * @brief Delete file
     * @param filepath File path
     * @return True if successful
     */
    static bool delete_file(const std::string& filepath);

    /**
     * @brief Get temporary directory
     * @return Temporary directory path
     */
    static std::string get_temp_directory();

    /**
     * @brief Create temporary file
     * @param prefix Filename prefix
     * @param extension File extension
     * @return Temporary file path
     */
    static std::string create_temp_file(const std::string& prefix = "tmp",
                                       const std::string& extension = ".tmp");
};

/**
 * @brief System utility functions
 */
class SystemUtils {
public:
    /**
     * @brief Get environment variable
     * @param name Variable name
     * @return Variable value if exists
     */
    static std::optional<std::string> get_env(const std::string& name);

    /**
     * @brief Set environment variable
     * @param name Variable name
     * @param value Variable value
     * @return True if successful
     */
    static bool set_env(const std::string& name, const std::string& value);

    /**
     * @brief Get current working directory
     * @return Current directory path
     */
    static std::string get_current_directory();

    /**
     * @brief Get home directory
     * @return Home directory path
     */
    static std::string get_home_directory();

    /**
     * @brief Get CPU count
     * @return Number of CPU cores
     */
    static unsigned int get_cpu_count();

    /**
     * @brief Get available memory
     * @return Available memory in bytes
     */
    static size_t get_available_memory();

    /**
     * @brief Get process ID
     * @return Current process ID
     */
    static int get_process_id();

    /**
     * @brief Execute command
     * @param command Command to execute
     * @return Command output
     */
    static std::optional<std::string> execute_command(const std::string& command);

    /**
     * @brief Get hostname
     * @return System hostname
     */
    static std::string get_hostname();

    /**
     * @brief Get username
     * @return Current username
     */
    static std::string get_username();
};

/**
 * @brief Cryptographic utility functions
 */
class CryptoUtils {
public:
    /**
     * @brief Calculate MD5 hash
     * @param data Input data
     * @return MD5 hash (hex string)
     */
    static std::string md5(const std::string& data);

    /**
     * @brief Calculate SHA256 hash
     * @param data Input data
     * @return SHA256 hash (hex string)
     */
    static std::string sha256(const std::string& data);

    /**
     * @brief Base64 encode
     * @param data Binary data
     * @return Base64 encoded string
     */
    static std::string base64_encode(const std::vector<uint8_t>& data);

    /**
     * @brief Base64 decode
     * @param encoded Encoded string
     * @return Decoded binary data
     */
    static std::vector<uint8_t> base64_decode(const std::string& encoded);

    /**
     * @brief Generate UUID
     * @return UUID string
     */
    static std::string generate_uuid();

    /**
     * @brief Generate random bytes
     * @param length Number of bytes
     * @return Random bytes
     */
    static std::vector<uint8_t> random_bytes(size_t length);

private:
    static std::mutex random_mutex;
};

/**
 * @brief Validation utility functions
 */
class ValidationUtils {
public:
    /**
     * @brief Validate email address
     * @param email Email address
     * @return True if valid
     */
    static bool is_valid_email(const std::string& email);

    /**
     * @brief Validate URL
     * @param url URL string
     * @return True if valid
     */
    static bool is_valid_url(const std::string& url);

    /**
     * @brief Validate IP address (v4 or v6)
     * @param ip IP address
     * @return True if valid
     */
    static bool is_valid_ip(const std::string& ip);

    /**
     * @brief Validate phone number
     * @param phone Phone number
     * @param country_code Country code
     * @return True if valid
     */
    static bool is_valid_phone(const std::string& phone,
                              const std::string& country_code = "US");

    /**
     * @brief Validate postal code
     * @param postal_code Postal code
     * @param country_code Country code
     * @return True if valid
     */
    static bool is_valid_postal_code(const std::string& postal_code,
                                    const std::string& country_code = "GB");
    
    /**
     * @brief Validate UK postal code specifically
     * @param postal_code UK postal code
     * @return True if valid UK postcode
     */
    static bool is_valid_uk_postcode(const std::string& postal_code);
    
    /**
     * @brief Validate NHS number
     * @param nhs_number NHS number string
     * @return True if valid NHS number
     */
    static bool is_valid_nhs_number(const std::string& nhs_number);
    
    /**
     * @brief Validate UK National Insurance number
     * @param ni_number National Insurance number
     * @return True if valid NI number
     */
    static bool is_valid_uk_ni_number(const std::string& ni_number);
    
    /**
     * @brief Validate SNOMED CT code format
     * @param code SNOMED CT code
     * @return True if valid format
     */
    static bool is_valid_snomed_code(const std::string& code);
    
    /**
     * @brief Validate Read code format (UK legacy)
     * @param code Read code
     * @return True if valid format
     */
    static bool is_valid_read_code(const std::string& code);
    
    /**
     * @brief Validate UK phone number
     * @param phone UK phone number
     * @return True if valid UK phone number
     */
    static bool is_valid_uk_phone(const std::string& phone);

    /**
     * @brief Validate date format
     * @param date_str Date string
     * @param format Expected format
     * @return True if valid
     */
    static bool is_valid_date_format(const std::string& date_str,
                                    const std::string& format);

    /**
     * @brief Validate JSON string
     * @param json JSON string
     * @return True if valid JSON
     */
    static bool is_valid_json(const std::string& json);

    /**
     * @brief Validate SQL identifier
     * @param identifier SQL identifier
     * @return True if valid
     */
    static bool is_valid_sql_identifier(const std::string& identifier);

    /**
     * @brief Validate UUID format
     * @param uuid UUID string
     * @return True if valid UUID
     */
    static bool is_valid_uuid(const std::string& uuid);

    /**
     * @brief Sanitize string for safe usage
     * @param input Input string
     * @return Sanitized string
     */
    static std::string sanitize_string(const std::string& input);
};

/**
 * @brief Performance monitoring utilities
 */
class PerformanceUtils {
public:
    /**
     * @brief Simple timer class
     */
    class Timer {
    public:
        Timer() : start_(std::chrono::high_resolution_clock::now()) {}

        void reset() {
            start_ = std::chrono::high_resolution_clock::now();
        }

        double elapsed_seconds() const {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(end - start_).count();
        }

        double elapsed_milliseconds() const {
            return elapsed_seconds() * 1000.0;
        }

    private:
        std::chrono::high_resolution_clock::time_point start_;
    };

    /**
     * @brief Memory usage tracker
     */
    class MemoryTracker {
    public:
        MemoryTracker();

        size_t current_usage() const;
        size_t peak_usage() const;
        void reset();
        static size_t global_peak_usage() { return global_peak_usage_.load(); }

    private:
        size_t initial_usage_;
        mutable size_t peak_usage_;
        static std::atomic<size_t> global_peak_usage_;
    };

    /**
     * @brief Format bytes to human readable string
     * @param bytes Number of bytes
     * @return Formatted string (e.g., "1.5 GB")
     */
    static std::string format_bytes(size_t bytes);

    /**
     * @brief Format duration to human readable string
     * @param seconds Duration in seconds
     * @return Formatted string (e.g., "2h 15m 30s")
     */
    static std::string format_duration(double seconds);

    /**
     * @brief Calculate throughput
     * @param items Number of items processed
     * @param seconds Time taken
     * @return Items per second
     */
    static double calculate_throughput(size_t items, double seconds);
};

/**
 * @brief UK-specific localization utilities
 */
namespace UKLocalization {
    std::string format_uk_currency(double amount);
    std::string format_uk_date(const std::chrono::system_clock::time_point& tp);
    std::string format_uk_datetime(const std::chrono::system_clock::time_point& tp);
    double celsius_to_fahrenheit(double celsius);
    double fahrenheit_to_celsius(double fahrenheit);
    std::string format_temperature_celsius(double celsius);
    
    // UK-specific number formatting
    std::string format_uk_number(double number, int decimal_places = 2);
    
    // UK-specific address formatting
    std::string format_uk_address(const std::string& line1, 
                                 const std::string& line2,
                                 const std::string& city,
                                 const std::string& county,
                                 const std::string& postcode);
}

/**
 * @brief Processing-related utility functions
 */
class ProcessingUtils {
public:
    /**
     * @brief Convert processing stage enum to string
     * @param stage Processing stage enum value
     * @return String representation of the stage
     */
    static std::string stage_name(int stage);
};

} // namespace omop::common