#include "configuration.h"

#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <ranges>
#include <format>

namespace omop::common {

// TransformationRule implementation
TransformationRule::TransformationRule(const YAML::Node& node) {
    if (node["source_column"]) {
        source_column_ = node["source_column"].as<std::string>();
    } else if (node["source_columns"]) {
        source_columns_ = node["source_columns"].as<std::vector<std::string>>();
    } else {
        throw ConfigurationException("Transformation rule must have either 'source_column' or 'source_columns'");
    }

    // Validate source columns are not empty
    if (!source_column_.empty() && source_column_.find_first_not_of(" \t\n\r") == std::string::npos) {
        throw ConfigurationException("Source column cannot be empty or whitespace");
    }
    
    for (const auto& col : source_columns_) {
        if (col.find_first_not_of(" \t\n\r") == std::string::npos) {
            throw ConfigurationException("Source column cannot be empty or whitespace");
        }
    }

    if (!node["target_column"]) {
        throw ConfigurationException("Transformation rule must have 'target_column'");
    }
    target_column_ = node["target_column"].as<std::string>();

    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"direct", Type::Direct},
            {"date_transform", Type::DateTransform},
            {"datetransform", Type::DateTransform},
            {"vocabulary_mapping", Type::VocabularyMapping},
            {"vocabularymapping", Type::VocabularyMapping},
            {"date_calculation", Type::DateCalculation},
            {"datecalculation", Type::DateCalculation},
            {"numeric_transform", Type::NumericTransform},
            {"numerictransform", Type::NumericTransform},
            {"string_concatenation", Type::StringConcatenation},
            {"stringconcatenation", Type::StringConcatenation},
            {"string_manipulation", Type::Custom},
            {"stringmanipulation", Type::Custom},
            {"conditional", Type::Conditional},
            {"custom", Type::Custom}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);
        std::replace(type_str.begin(), type_str.end(), '-', '_');
        std::replace(type_str.begin(), type_str.end(), ' ', '_');
        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown transformation type: '{}'", type_str), "type");
        }
    }

    // Store all other parameters
    // If there is a 'parameters' subnode, merge its contents first
    if (node["parameters"]) {
        for (const auto& param : node["parameters"]) {
            parameters_[param.first.as<std::string>()] = param.second;
        }
    }
    for (const auto& item : node) {
        const std::string& key = item.first.as<std::string>();
        if (key != "source_column" && key != "source_columns" &&
            key != "target_column" && key != "type" && key != "parameters") {
            parameters_[key] = item.second;
        }
    }
}

// TableMapping implementation
TableMapping::TableMapping(const YAML::Node& node) {
    if (!node["source_table"]) {
        throw ConfigurationException("Table mapping must have 'source_table'");
    }
    source_table_ = node["source_table"].as<std::string>();

    if (!node["target_table"]) {
        throw ConfigurationException("Table mapping must have 'target_table'");
    }
    target_table_ = node["target_table"].as<std::string>();

    if (node["transformations"]) {
        for (const auto& trans_node : node["transformations"]) {
            transformations_.emplace_back(trans_node);
        }
    }

    if (node["pre_process_sql"]) {
        pre_process_sql_ = node["pre_process_sql"].as<std::string>();
    }

    if (node["post_process_sql"]) {
        post_process_sql_ = node["post_process_sql"].as<std::string>();
    }

    if (node["filters"]) {
        filters_ = node["filters"];
    }

    if (node["validations"]) {
        validations_ = node["validations"];
    }
}

// DatabaseConfig implementation
DatabaseConfig::DatabaseConfig(const YAML::Node& node) {
    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"postgresql", Type::PostgreSQL},
            {"mysql", Type::MySQL},
            {"mssql", Type::MSSQL},
            {"oracle", Type::Oracle}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);

        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown database type: '{}'", type_str), "type");
        }
    }

    if (node["connection_string"]) {
        connection_string_ = node["connection_string"].as<std::string>();
    } else {
        // Build connection from individual parameters
        if (!node["host"]) {
            throw ConfigurationException("Database configuration must have 'host' or 'connection_string'");
        }
        host_ = node["host"].as<std::string>();

        if (node["port"]) {
            port_ = node["port"].as<int>();
        } else {
            // Set default ports based on database type
            switch (type_) {
                case Type::PostgreSQL: port_ = 5432; break;
                case Type::MySQL: port_ = 3306; break;
                case Type::MSSQL: port_ = 1433; break;
                case Type::Oracle: port_ = 1521; break;
            }
        }

        if (!node["database"]) {
            throw ConfigurationException("Database configuration must have 'database'");
        }
        database_ = node["database"].as<std::string>();

        if (!node["username"]) {
            throw ConfigurationException("Database configuration must have 'username'");
        }
        username_ = node["username"].as<std::string>();

        if (node["password"]) {
            password_ = node["password"].as<std::string>();
        }
    }

    // Parse additional parameters
    if (node["parameters"]) {
        const auto& params_node = node["parameters"];
        for (const auto& param : params_node) {
            parameters_[param.first.as<std::string>()] = param.second.as<std::string>();
        }
    }
}

// ConfigurationManager implementation
void ConfigurationManager::load_config(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw ConfigurationException(
            std::format("Failed to open configuration file: '{}'", filepath));
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    
    // Store the file path and load time
    config_file_path_ = filepath;
    load_time_ = std::chrono::system_clock::now();
    
    load_config_from_string(buffer.str());
}

void ConfigurationManager::load_config_from_string(const std::string& yaml_content) {
    try {
        root_config_ = YAML::Load(yaml_content);
        // When loading from string, clear the file path but set load time
        if (config_file_path_.empty()) {
            load_time_ = std::chrono::system_clock::now();
        }
    } catch (const YAML::Exception& e) {
        throw ConfigurationException(
            std::format("Failed to parse YAML configuration: {}", e.what()));
    }

    // Store the original tables node for later access
    if (root_config_["tables"]) {
        tables_node_ = root_config_["tables"];
    } else if (root_config_["table_mappings"]) {
        tables_node_ = root_config_["table_mappings"];
    } else {
        tables_node_ = YAML::Node();
    }

    // Parse source database configuration (support both 'source' and 'source_database')
    YAML::Node source_node;
    if (root_config_["source_database"]) {
        source_node = root_config_["source_database"];
    } else if (root_config_["source"]) {
        source_node = root_config_["source"];
    } else {
        throw ConfigurationException("Configuration must have 'source_database' or 'source' section");
    }
    source_db_ = parse_database_config(source_node);

    // Parse target database configuration (support both 'target' and 'target_database')
    YAML::Node target_node;
    if (root_config_["target_database"]) {
        target_node = root_config_["target_database"];
    } else if (root_config_["target"]) {
        target_node = root_config_["target"];
    } else {
        throw ConfigurationException("Configuration must have 'target_database' or 'target' section");
    }
    target_db_ = parse_database_config(target_node);

    // Parse table mappings (support both 'table_mappings' and 'tables')
    if (root_config_["table_mappings"]) {
        parse_table_mappings(root_config_["table_mappings"]);
    } else if (root_config_["tables"]) {
        parse_table_mappings(root_config_["tables"]);
    }

    // Parse vocabulary mappings (support both 'vocabulary_mappings' and 'vocabulary')
    if (root_config_["vocabulary_mappings"]) {
        vocabulary_mappings_ = root_config_["vocabulary_mappings"];
    } else if (root_config_["vocabulary"]) {
        vocabulary_mappings_ = root_config_["vocabulary"];
    }

    // Parse ETL settings (support both 'etl_settings' and 'etl')
    if (root_config_["etl_settings"]) {
        etl_settings_ = root_config_["etl_settings"];
    } else if (root_config_["etl"]) {
        etl_settings_ = root_config_["etl"];
    } else {
        // Set default ETL settings
        etl_settings_["batch_size"] = 1000;
        etl_settings_["parallel_workers"] = 4;
        etl_settings_["validation_mode"] = "strict";
        etl_settings_["error_threshold"] = 0.01;
    }

    config_loaded_ = true;
    validate_config();
}

std::optional<TableMapping> ConfigurationManager::get_table_mapping(
    const std::string& table_name) const {
    auto it = table_mappings_.find(table_name);
    if (it != table_mappings_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::optional<YAML::Node> ConfigurationManager::get_value(const std::string& key) const {
    if (!config_loaded_) {
        return std::nullopt;
    }

    // Split key by dots to navigate nested structure, handling array indexing
    std::vector<std::string> parts;
    std::string current_part;
    bool in_brackets = false;
    std::string bracket_content;
    
    for (size_t i = 0; i < key.length(); ++i) {
        char c = key[i];
        if (c == '[') {
            in_brackets = true;
            if (!current_part.empty()) {
                parts.push_back(current_part);
                current_part.clear();
            }
        } else if (c == ']') {
            in_brackets = false;
            if (!bracket_content.empty()) {
                parts.push_back("[" + bracket_content + "]");
                bracket_content.clear();
            }
        } else if (c == '.' && !in_brackets) {
            if (!current_part.empty()) {
                parts.push_back(current_part);
                current_part.clear();
            }
        } else if (in_brackets) {
            bracket_content += c;
        } else {
            current_part += c;
        }
    }
    
    if (!current_part.empty()) {
        parts.push_back(current_part);
    }

    // Special handling for etl_settings and etl keys
    if (!parts.empty() && (parts[0] == "etl_settings" || parts[0] == "etl")) {
        if (parts.size() == 1) {
            return etl_settings_;
        } else if (parts.size() == 2) {
            if (etl_settings_[parts[1]]) {
                return etl_settings_[parts[1]];
            }
        }
        return std::nullopt;
    }

    // Special handling for top-level sequence keys like 'tables'
    if (!parts.empty() && (parts[0] == "tables" || parts[0] == "table_mappings")) {
        if (parts.size() == 1) {
            if (tables_node_ && (tables_node_.IsSequence() || tables_node_.IsMap())) {
                return tables_node_;
            }
        } else if (tables_node_ && (tables_node_.IsSequence() || tables_node_.IsMap())) {
            // Start navigation from tables_node_ for further parts
            YAML::Node current = tables_node_;
            for (size_t i = 1; i < parts.size(); ++i) {
                const auto& p = parts[i];
                if (p[0] == '[' && p[p.length()-1] == ']') {
                    std::string index_str = p.substr(1, p.length()-2);
                    try {
                        size_t index = std::stoul(index_str);
                        if (!current.IsSequence() || index >= current.size()) {
                            return std::nullopt;
                        }
                        current = current[index];
                    } catch (const std::exception&) {
                        return std::nullopt;
                    }
                } else {
                    if (!current[p]) {
                        return std::nullopt;
                    }
                    current = current[p];
                }
            }
            return current;
        }
    }

    // Default behavior for other keys
    YAML::Node current = root_config_;
    for (const auto& p : parts) {
        if (p[0] == '[' && p[p.length()-1] == ']') {
            // Handle array indexing
            std::string index_str = p.substr(1, p.length()-2);
            try {
                size_t index = std::stoul(index_str);
                if (!current.IsSequence() || index >= current.size()) {
                    return std::nullopt;
                }
                current = current[index];
            } catch (const std::exception&) {
                return std::nullopt;
            }
        } else {
            // Handle regular key access
            if (!current[p]) {
                return std::nullopt;
            }
            current = current[p];
        }
    }

    return current;
}

void ConfigurationManager::validate_config() const {
    if (!config_loaded_) {
        throw ConfigurationException("Configuration not loaded");
    }

    // Validate that we have at least one table mapping
    if (table_mappings_.empty()) {
        throw ConfigurationException("No table mappings defined in configuration");
    }

    // Validate each table mapping
    for (const auto& [name, mapping] : table_mappings_) {
        if (mapping.transformations().empty()) {
            throw ConfigurationException(
                std::format("Table mapping '{}' has no transformations defined", name));
        }

        // Validate that all transformation rules are properly configured
        for (const auto& rule : mapping.transformations()) {
            if (rule.target_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty target column in table mapping '{}'", name));
            }

            if (!rule.is_multi_column() && rule.source_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty source column in table mapping '{}'", name));
            }

            // Validate vocabulary mappings reference existing vocabularies
            if (rule.type() == TransformationRule::Type::VocabularyMapping) {
                auto vocab_param = rule.parameters()["vocabulary"];
                if (!vocab_param || !vocab_param.IsDefined()) {
                    throw ConfigurationException(
                        std::format("Vocabulary mapping in table '{}' missing vocabulary parameter", name));
                }

                std::string vocab_name = vocab_param.as<std::string>();
                if (!vocabulary_mappings_[vocab_name]) {
                    throw ConfigurationException(
                        std::format("Referenced vocabulary '{}' not found in vocabulary_mappings", vocab_name),
                        vocab_name);
                }
            }
        }
    }

    // Validate ETL settings
    int batch_size = get_value_or<int>("etl_settings.batch_size", 1000);
    if (batch_size <= 0) {
        throw ConfigurationException("Invalid batch_size: must be greater than 0", "batch_size");
    }

    int workers = get_value_or<int>("etl_settings.parallel_workers", 1);
    if (workers <= 0) {
        throw ConfigurationException("Invalid parallel_workers: must be greater than 0", "parallel_workers");
    }

    double error_threshold = get_value_or<double>("etl_settings.error_threshold", 0.01);
    if (error_threshold < 0.0 || error_threshold > 1.0) {
        throw ConfigurationException("Invalid error_threshold: must be between 0.0 and 1.0", "error_threshold");
    }
}

void ConfigurationManager::parse_table_mappings(const YAML::Node& mappings_node) {
    if (mappings_node.IsMap()) {
        // Handle map format: table_name -> mapping
        for (const auto& item : mappings_node) {
            std::string table_name = item.first.as<std::string>();
            try {
                table_mappings_[table_name] = TableMapping(item.second);
            } catch (const ConfigurationException& e) {
                throw ConfigurationException(
                    std::format("Error parsing table mapping '{}': {}", table_name, e.message()));
            }
        }
    } else if (mappings_node.IsSequence()) {
        // Handle sequence format: list of mappings with target_table as key
        for (const auto& item : mappings_node) {
            try {
                TableMapping mapping(item);
                std::string target_table(mapping.target_table());
                if (target_table.empty()) {
                    throw ConfigurationException("Table mapping must have 'target_table'");
                }
                table_mappings_[target_table] = mapping;
            } catch (const ConfigurationException& e) {
                throw ConfigurationException(
                    std::format("Error parsing table mapping: {}", e.message()));
            }
        }
    } else {
        throw ConfigurationException("Table mappings must be either a map or sequence");
    }
}

DatabaseConfig ConfigurationManager::parse_database_config(const YAML::Node& db_node) {
    try {
        return DatabaseConfig(db_node);
    } catch (const ConfigurationException& e) {
        throw ConfigurationException(
            std::format("Error parsing database configuration: {}", e.message()));
    }
}

void ConfigurationManager::clear() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_loaded_ = false;
    root_config_ = YAML::Node();
    table_mappings_.clear();
    source_db_ = DatabaseConfig();
    target_db_ = DatabaseConfig();
    vocabulary_mappings_ = YAML::Node();
    etl_settings_ = YAML::Node();
    config_file_path_.clear();
    load_time_ = std::chrono::system_clock::time_point();
}

std::string ConfigurationManager::get_config_file_path() const noexcept {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_file_path_;
}

std::chrono::system_clock::time_point ConfigurationManager::get_load_time() const noexcept {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return load_time_;
}

void ConfigurationManager::reload() {
    std::string saved_path;
    
    // First check if we have a file path under a brief lock
    {
        std::lock_guard<std::mutex> lock(config_mutex_);
        if (config_file_path_.empty()) {
            throw ConfigurationException("Cannot reload: no configuration file was loaded");
        }
        saved_path = config_file_path_;
    }
    
    // Now reload from file - load_config will handle its own locking
    // This avoids potential deadlock from recursive mutex locking
    load_config(saved_path);
}

// Static member definitions
std::unique_ptr<ConfigurationManager> Config::instance_;
std::once_flag Config::init_flag_;
std::mutex Config::instance_mutex_;

} // namespace omop::common