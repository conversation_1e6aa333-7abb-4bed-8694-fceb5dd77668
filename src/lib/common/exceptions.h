/**
 * @file exceptions.h
 * @brief Exception hierarchy for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file defines the complete exception hierarchy for the OMOP ETL pipeline,
 * providing specialized exception types for different error scenarios with
 * enhanced error information including source location tracking.
 */

#pragma once

#include <exception>
#include <string>
#include <string_view>
#include <format>
#include <cstdint>

// Simple source_location fallback for compatibility
namespace std {
    struct source_location {
        static constexpr source_location current() noexcept { return {}; }
        constexpr const char* file_name() const noexcept { return "unknown"; }
        constexpr const char* function_name() const noexcept { return "unknown"; }
        constexpr std::uint_least32_t line() const noexcept { return 0; }
        constexpr std::uint_least32_t column() const noexcept { return 0; }
    };
}

namespace omop::common {

/**
 * @brief Base exception class for the OMOP ETL pipeline
 *
 * This class serves as the root of the exception hierarchy for the entire
 * OMOP ETL pipeline project. It provides enhanced error information including
 * source location tracking and formatted error messages.
 */
class OmopException : public std::exception {
public:
    /**
     * @brief Construct a new OmopException
     * @param message Error message describing the exception
     * @param location Source location where the exception occurred
     */
    explicit OmopException(std::string_view message,
                           const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the exception message
     * @return const char* Pointer to the error message
     */
    [[nodiscard]] const char* what() const noexcept override;

    /**
     * @brief Get the raw error message without location information
     * @return std::string_view The original error message
     */
    [[nodiscard]] std::string_view message() const noexcept;

    /**
     * @brief Get the source location where the exception occurred
     * @return const std::source_location& Reference to the source location
     */
    [[nodiscard]] const std::source_location& location() const noexcept;

protected:
    std::string message_;           ///< Original error message
    std::string formatted_message_; ///< Formatted message with location
    std::source_location location_; ///< Source location of the exception
};

/**
 * @brief Exception thrown during configuration operations
 */
class ConfigurationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a configuration exception with a specific config key
     * @param message Error message
     * @param config_key The configuration key that caused the error
     * @param location Source location
     */
    ConfigurationException(std::string_view message,
                           std::string_view config_key,
                           const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the configuration key that caused the error
     * @return std::string_view The configuration key
     */
    [[nodiscard]] std::string_view config_key() const noexcept;

private:
    std::string config_key_;
};

/**
 * @brief Exception thrown during data extraction operations
 */
class ExtractionException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct an extraction exception with source details
     * @param message Error message
     * @param source_name Name of the data source
     * @param location Source location
     */
    ExtractionException(std::string_view message,
                        std::string_view source_name,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the name of the data source
     * @return std::string_view The source name
     */
    [[nodiscard]] std::string_view source_name() const noexcept;

private:
    std::string source_name_;
};

/**
 * @brief Exception thrown during data transformation operations
 */
class TransformationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a transformation exception with field details
     * @param message Error message
     * @param field_name Name of the field being transformed
     * @param transformation_type Type of transformation attempted
     * @param location Source location
     */
    TransformationException(std::string_view message,
                            std::string_view field_name,
                            std::string_view transformation_type,
                            const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the field name
     * @return std::string_view The field name
     */
    [[nodiscard]] std::string_view field_name() const noexcept;

    /**
     * @brief Get the transformation type
     * @return std::string_view The transformation type
     */
    [[nodiscard]] std::string_view transformation_type() const noexcept;

private:
    std::string field_name_;
    std::string transformation_type_;
};

/**
 * @brief Exception thrown during data loading operations
 */
class LoadException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a load exception with target details
     * @param message Error message
     * @param target_table Target table name
     * @param location Source location
     */
    LoadException(std::string_view message,
                  std::string_view target_table,
                  const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the target table name
     * @return std::string_view The target table name
     */
    [[nodiscard]] std::string_view target_table() const noexcept;

private:
    std::string target_table_;
};

/**
 * @brief Exception thrown during database operations
 */
class DatabaseException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a database exception with connection details
     * @param message Error message
     * @param database_type Type of database (PostgreSQL, MySQL, etc.)
     * @param error_code Database-specific error code
     * @param location Source location
     */
    DatabaseException(std::string_view message,
                      std::string_view database_type,
                      int error_code,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the database type
     * @return std::string_view The database type
     */
    [[nodiscard]] std::string_view database_type() const noexcept;

    /**
     * @brief Get the error code
     * @return int The database-specific error code
     */
    [[nodiscard]] int error_code() const noexcept;

private:
    std::string database_type_;
    int error_code_;
};

/**
 * @brief Exception thrown during security-related operations
 */
class SecurityException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a security exception with security context
     * @param message Error message
     * @param security_context The security context (e.g., "database", "api", "file")
     * @param location Source location
     */
    SecurityException(std::string_view message,
                      std::string_view security_context,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the security context
     * @return std::string_view The security context
     */
    [[nodiscard]] std::string_view security_context() const noexcept;

private:
    std::string security_context_;
};

/**
 * @brief Exception thrown during validation operations
 */
class ValidationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a validation exception with rule details
     * @param message Error message
     * @param rule_name Name of the validation rule
     * @param field_value The value that failed validation
     * @param location Source location
     */
    ValidationException(std::string_view message,
                        std::string_view rule_name,
                        std::string_view field_value,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the validation rule name
     * @return std::string_view The rule name
     */
    [[nodiscard]] std::string_view rule_name() const noexcept;

    /**
     * @brief Get the field value that failed validation
     * @return std::string_view The field value
     */
    [[nodiscard]] std::string_view field_value() const noexcept;

private:
    std::string rule_name_;
    std::string field_value_;
};

/**
 * @brief Exception thrown during vocabulary mapping operations
 */
class VocabularyException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a vocabulary exception with mapping details
     * @param message Error message
     * @param vocabulary_name Name of the vocabulary
     * @param source_value The value that couldn't be mapped
     * @param location Source location
     */
    VocabularyException(std::string_view message,
                        std::string_view vocabulary_name,
                        std::string_view source_value,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the vocabulary name
     * @return std::string_view The vocabulary name
     */
    [[nodiscard]] std::string_view vocabulary_name() const noexcept;

    /**
     * @brief Get the source value
     * @return std::string_view The source value
     */
    [[nodiscard]] std::string_view source_value() const noexcept;

private:
    std::string vocabulary_name_;
    std::string source_value_;
};

/**
 * @brief Exception thrown during API operations
 */
class ApiException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct an API exception with HTTP details
     * @param message Error message
     * @param http_status HTTP status code
     * @param endpoint The API endpoint
     * @param location Source location
     */
    ApiException(std::string_view message,
                 int http_status,
                 std::string_view endpoint,
                 const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the HTTP status code
     * @return int The HTTP status code
     */
    [[nodiscard]] int http_status() const noexcept;

    /**
     * @brief Get the API endpoint
     * @return std::string_view The endpoint
     */
    [[nodiscard]] std::string_view endpoint() const noexcept;

private:
    int http_status_;
    std::string endpoint_;
};

/**
 * @brief Exception thrown during platform-specific operations
 */
class PlatformException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a platform exception with operation details
     * @param message Error message
     * @param operation_name Name of the platform operation
     * @param platform_type Type of platform (Windows, Unix, etc.)
     * @param location Source location
     */
    PlatformException(std::string_view message,
                      std::string_view operation_name,
                      std::string_view platform_type,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the operation name
     * @return std::string_view The operation name
     */
    [[nodiscard]] std::string_view operation_name() const noexcept;

    /**
     * @brief Get the platform type
     * @return std::string_view The platform type
     */
    [[nodiscard]] std::string_view platform_type() const noexcept;

private:
    std::string operation_name_;
    std::string platform_type_;
};

/**
 * @brief Exception thrown during OMOP CDM operations
 */
class CdmException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a CDM exception with table and operation details
     * @param message Error message
     * @param table_name Name of the OMOP table
     * @param operation Type of CDM operation (validation, generation, etc.)
     * @param location Source location
     */
    CdmException(std::string_view message,
                 std::string_view table_name,
                 std::string_view operation,
                 const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the table name
     * @return std::string_view The OMOP table name
     */
    [[nodiscard]] std::string_view table_name() const noexcept;

    /**
     * @brief Get the operation type
     * @return std::string_view The CDM operation type
     */
    [[nodiscard]] std::string_view operation() const noexcept;

private:
    std::string table_name_;
    std::string operation_;
};

} // namespace omop::common