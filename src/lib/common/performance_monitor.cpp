/**
 * @file performance_monitor.cpp
 * @brief Implementation of performance monitoring classes
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "performance_monitor.h"

#include <algorithm>
#include <sstream>
#include <fstream>
#include <filesystem>
#include <iomanip>
#include <cmath>
#include <numeric>
#include <random>
#include <thread>

#ifdef __linux__
#include <sys/resource.h>
#include <unistd.h>
#include <fstream>
#endif

#ifdef __APPLE__
#include <mach/mach.h>
#include <sys/resource.h>
#endif

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#endif

namespace omop::common {

// PerformanceMonitor implementation

PerformanceMonitor::PerformanceMonitor() : monitoring_active_(false) {}

PerformanceMonitor::~PerformanceMonitor() = default;

std::string PerformanceMonitor::generate_timer_id() {
    static std::atomic<uint64_t> counter{0};
    return "timer_" + std::to_string(counter.fetch_add(1));
}

bool PerformanceMonitor::initialize(const BenchmarkConfig& config) {
    config_ = config;
    return true;
}

bool PerformanceMonitor::start_monitoring() {
    monitoring_active_ = true;
    return true;
}

bool PerformanceMonitor::stop_monitoring() {
    monitoring_active_ = false;
    return true;
}

void PerformanceMonitor::record_measurement(const PerformanceMeasurement& measurement) {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    measurements_.push_back(measurement);
    
    // Record the measurement timestamp
    last_measurement_time_ = measurement.timestamp;
}

std::string PerformanceMonitor::start_timer(
    const std::string& metric_name,
    const std::unordered_map<std::string, std::string>& labels) {
    
    std::string timer_id = generate_timer_id();
    active_timers_[timer_id] = std::chrono::high_resolution_clock::now();
    
    return timer_id;
}

std::optional<double> PerformanceMonitor::stop_timer(const std::string& timer_id) {
    auto it = active_timers_.find(timer_id);
    if (it == active_timers_.end()) {
        return std::nullopt;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - it->second).count() / 1e6; // Convert to seconds
    
    active_timers_.erase(it);
    
    return duration;
}

bool PerformanceMonitor::start(const std::string& operation_name) {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    // Start a timer for this operation
    auto timer_id = generate_timer_id();
    active_timers_[timer_id] = std::chrono::high_resolution_clock::now();
    
    // Store the timer ID for this operation
    operation_timers_[operation_name] = timer_id;
    
    return true;
}

std::unordered_map<std::string, double> PerformanceMonitor::stop(const std::string& operation_name) {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    std::unordered_map<std::string, double> stats;
    
    auto timer_it = operation_timers_.find(operation_name);
    if (timer_it == operation_timers_.end()) {
        stats["error"] = 1.0;
        stats["error_code"] = static_cast<double>(std::hash<std::string>{}("operation_not_found"));
        return stats;
    }
    
    auto timer_id = timer_it->second;
    auto timer_start_it = active_timers_.find(timer_id);
    
    if (timer_start_it == active_timers_.end()) {
        stats["error"] = 1.0;
        stats["error_code"] = static_cast<double>(std::hash<std::string>{}("timer_not_found"));
        return stats;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration_ms = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - timer_start_it->second).count() / 1000.0; // Convert to milliseconds
    
    // Calculate statistics
    stats["duration_ms"] = duration_ms;
    stats["duration_seconds"] = duration_ms / 1000.0;
    stats["operation_name"] = static_cast<double>(std::hash<std::string>{}(operation_name));
    
    // Record the measurement
    PerformanceMeasurement measurement;
    measurement.metric_name = operation_name;
    measurement.type = PerformanceMetricType::Duration;
    measurement.value = duration_ms;
    measurement.unit = "ms";
    measurement.timestamp = std::chrono::system_clock::now();
    measurements_.push_back(measurement);
    
    // Clean up
    active_timers_.erase(timer_start_it);
    operation_timers_.erase(timer_it);
    
    return stats;
}

BenchmarkResult PerformanceMonitor::run_benchmark(
    const std::string& benchmark_name,
    std::function<void()> test_func,
    const BenchmarkConfig& config) {
    
    BenchmarkResult result;
    result.benchmark_name = benchmark_name;
    
    [[maybe_unused]] auto overall_start = std::chrono::high_resolution_clock::now();
    
    // Warmup iterations
    for (size_t i = 0; i < config.warmup_iterations; ++i) {
        try {
            test_func();
        } catch (const std::exception& e) {
            // Ignore warmup failures
        }
    }
    
    // Actual benchmark iterations
    for (size_t i = 0; i < config.iterations; ++i) {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            test_func();
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            
            if (result.min_duration > duration) {
                result.min_duration = duration;
            }
            if (result.max_duration < duration) {
                result.max_duration = duration;
            }
            
            result.total_duration += duration;
            result.completed_iterations++;
            
        } catch (const std::exception& e) {
            result.failed_iterations++;
            if (result.failure_reason.empty()) {
                result.failure_reason = e.what();
            }
        }
    }
    
    [[maybe_unused]] auto overall_end = std::chrono::high_resolution_clock::now();
    
    result.success = (result.failed_iterations == 0) || 
                    ((static_cast<double>(result.failed_iterations) / config.iterations) * 100.0 <= config.failure_threshold_pct);
    
    if (result.completed_iterations > 0) {
        result.average_duration = result.total_duration / result.completed_iterations;
    }
    
    return result;
}

SystemMetrics PerformanceMonitor::get_system_metrics() {
    SystemMetrics metrics;
    metrics.timestamp = std::chrono::system_clock::now();

#ifdef __linux__
    // Linux-specific implementation
    std::ifstream meminfo("/proc/meminfo");
    std::string line;
    while (std::getline(meminfo, line)) {
        if (line.find("MemTotal:") == 0) {
            size_t value;
            std::sscanf(line.c_str(), "MemTotal: %zu kB", &value);
            metrics.memory_available_bytes = value * 1024;
        } else if (line.find("MemAvailable:") == 0) {
            size_t value;
            std::sscanf(line.c_str(), "MemAvailable: %zu kB", &value);
            size_t available = value * 1024;
            metrics.memory_used_bytes = metrics.memory_available_bytes - available;
            break;
        }
    }
    
    if (metrics.memory_available_bytes > 0) {
        metrics.memory_usage_percent = 
            (static_cast<double>(metrics.memory_used_bytes) / metrics.memory_available_bytes) * 100.0;
    }

    // CPU usage would require more complex implementation
    metrics.cpu_usage_percent = 0.0;
    
#elif defined(__APPLE__)
    // macOS-specific implementation
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        metrics.memory_used_bytes = usage.ru_maxrss; // Note: in bytes on macOS
    }
    
    // Get system memory info
    mach_port_t host_port = mach_host_self();
    vm_size_t page_size;
    vm_statistics64_data_t vm_stat;
    mach_msg_type_number_t host_size = sizeof(vm_statistics64_data_t) / sizeof(natural_t);
    
    if (host_page_size(host_port, &page_size) == KERN_SUCCESS &&
        host_statistics64(host_port, HOST_VM_INFO64, (host_info64_t)&vm_stat, &host_size) == KERN_SUCCESS) {
        
        metrics.memory_available_bytes = (vm_stat.free_count + vm_stat.inactive_count) * page_size;
        size_t total_memory = (vm_stat.free_count + vm_stat.active_count + 
                             vm_stat.inactive_count + vm_stat.wire_count) * page_size;
        
        if (total_memory > 0) {
            metrics.memory_usage_percent = 
                (static_cast<double>(total_memory - metrics.memory_available_bytes) / total_memory) * 100.0;
        }
    }
    
#elif defined(_WIN32)
    // Windows-specific implementation
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memInfo)) {
        metrics.memory_available_bytes = memInfo.ullAvailPhys;
        metrics.memory_used_bytes = memInfo.ullTotalPhys - memInfo.ullAvailPhys;
        metrics.memory_usage_percent = memInfo.dwMemoryLoad;
    }
#endif

    return metrics;
}

std::vector<PerformanceMeasurement> PerformanceMonitor::get_measurements(
    const std::string& metric_name) {
    
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    if (metric_name.empty()) {
        return measurements_;
    }
    
    std::vector<PerformanceMeasurement> filtered;
    std::copy_if(measurements_.begin(), measurements_.end(),
                 std::back_inserter(filtered),
                 [&metric_name](const PerformanceMeasurement& m) {
                     return m.metric_name == metric_name;
                 });
    
    return filtered;
}

std::unordered_map<std::string, double> PerformanceMonitor::calculate_statistics(
    const std::string& metric_name) {
    
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    std::vector<double> values;
    for (const auto& measurement : measurements_) {
        if (metric_name.empty() || measurement.metric_name == metric_name) {
            values.push_back(measurement.value);
        }
    }
    
    std::unordered_map<std::string, double> stats;
    
    if (values.empty()) {
        return stats;
    }
    
    std::sort(values.begin(), values.end());
    
    stats["count"] = static_cast<double>(values.size());
    stats["min"] = values.front();
    stats["max"] = values.back();
    
    double sum = std::accumulate(values.begin(), values.end(), 0.0);
    stats["sum"] = sum;
    stats["average"] = sum / values.size();
    
    // Calculate median
    if (values.size() % 2 == 0) {
        stats["median"] = (values[values.size()/2 - 1] + values[values.size()/2]) / 2.0;
    } else {
        stats["median"] = values[values.size()/2];
    }
    
    // Calculate standard deviation
    double mean = stats["average"];
    double variance = 0.0;
    for (double value : values) {
        variance += std::pow(value - mean, 2);
    }
    variance /= values.size();
    stats["std_dev"] = std::sqrt(variance);
    
    // Percentiles
    auto percentile = [&values](double p) {
        double index = p * (values.size() - 1);
        size_t lower = static_cast<size_t>(std::floor(index));
        size_t upper = static_cast<size_t>(std::ceil(index));
        
        if (lower == upper) {
            return values[lower];
        }
        
        double weight = index - lower;
        return values[lower] * (1.0 - weight) + values[upper] * weight;
    };
    
    stats["p50"] = percentile(0.50);
    stats["p90"] = percentile(0.90);
    stats["p95"] = percentile(0.95);
    stats["p99"] = percentile(0.99);
    
    return stats;
}

std::string PerformanceMonitor::generate_report(const std::string& format) {
    std::ostringstream report;
    
    if (format == "json") {
        report << "{\n";
        report << "  \"measurements\": [\n";
        
        std::lock_guard<std::mutex> lock(measurements_mutex_);
        for (size_t i = 0; i < measurements_.size(); ++i) {
            const auto& m = measurements_[i];
            report << "    {\n";
            report << "      \"metric_name\": \"" << m.metric_name << "\",\n";
            report << "      \"value\": " << m.value << ",\n";
            report << "      \"unit\": \"" << m.unit << "\"\n";
            report << "    }";
            if (i < measurements_.size() - 1) report << ",";
            report << "\n";
        }
        
        report << "  ]\n";
        report << "}\n";
    } else {
        // Text format
        report << "Performance Report\n";
        report << "==================\n\n";
        
        std::unordered_map<std::string, std::vector<double>> metrics_by_name;
        
        {
            std::lock_guard<std::mutex> lock(measurements_mutex_);
            for (const auto& m : measurements_) {
                metrics_by_name[m.metric_name].push_back(m.value);
            }
        }
        
        for (const auto& [name, values] : metrics_by_name) {
            auto stats = calculate_statistics(name);
            
            report << "Metric: " << name << "\n";
            report << "  Count: " << stats.at("count") << "\n";
            report << "  Average: " << std::fixed << std::setprecision(3) << stats.at("average") << "\n";
            report << "  Min: " << stats.at("min") << "\n";
            report << "  Max: " << stats.at("max") << "\n";
            report << "  Std Dev: " << stats.at("std_dev") << "\n";
            report << "  P95: " << stats.at("p95") << "\n";
            report << "\n";
        }
    }
    
    return report.str();
}

bool PerformanceMonitor::export_measurements(
    const std::string& filename,
    const std::string& format) {
    
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    if (format == "csv") {
        file << "metric_name,value,unit,timestamp\n";
        for (const auto& m : measurements_) {
            auto time_t = std::chrono::system_clock::to_time_t(m.timestamp);
            file << m.metric_name << "," << m.value << "," << m.unit << "," << time_t << "\n";
        }
    } else if (format == "json") {
        file << generate_report("json");
    }
    
    return true;
}

void PerformanceMonitor::clear_measurements() {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    measurements_.clear();
}

void PerformanceMonitor::set_thresholds(
    const std::unordered_map<std::string, double>& thresholds) {
    
    thresholds_ = thresholds;
}

std::vector<std::string> PerformanceMonitor::check_thresholds() {
    std::vector<std::string> failures;
    
    for (const auto& [metric_name, threshold] : thresholds_) {
        auto stats = calculate_statistics(metric_name);
        if (stats.count("average") && stats.at("average") > threshold) {
            failures.push_back(metric_name + " exceeded threshold: " + 
                             std::to_string(stats.at("average")) + " > " + std::to_string(threshold));
        }
    }
    
    return failures;
}

// ScopedPerformanceTimer implementation

ScopedPerformanceTimer::ScopedPerformanceTimer(
    IPerformanceMonitor& monitor,
    const std::string& metric_name,
    const std::unordered_map<std::string, std::string>& labels)
    : timer_type_(TimerType::MONITOR_BASED), monitor_(&monitor) {
    timer_id_ = monitor_->start_timer(metric_name, labels);
}

ScopedPerformanceTimer::ScopedPerformanceTimer(
    const std::string& operation_name,
    std::unordered_map<std::string, double>& operation_timings,
    std::unordered_map<std::string, size_t>& operation_counts)
    : timer_type_(TimerType::DIRECT_STATS),
      operation_name_(operation_name),
      operation_timings_(&operation_timings),
      operation_counts_(&operation_counts),
      start_time_(std::chrono::high_resolution_clock::now()) {
}

ScopedPerformanceTimer::~ScopedPerformanceTimer() {
    if (!stopped_) {
        stop();
    }
}

std::optional<double> ScopedPerformanceTimer::stop() {
    if (stopped_) {
        return std::nullopt;
    }
    
    stopped_ = true;
    
    if (timer_type_ == TimerType::MONITOR_BASED && monitor_) {
        return monitor_->stop_timer(timer_id_);
    } else if (timer_type_ == TimerType::DIRECT_STATS && operation_timings_ && operation_counts_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration_ms = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time_).count() / 1000.0; // Convert to milliseconds
        
        (*operation_timings_)[operation_name_] += duration_ms;
        (*operation_counts_)[operation_name_]++;
        
        return duration_ms / 1000.0; // Return seconds
    }
    
    return std::nullopt;
}

// Factory functions

std::unique_ptr<IPerformanceMonitor> create_performance_monitor() {
    return std::make_unique<PerformanceMonitor>();
}

BenchmarkConfig get_default_benchmark_config() {
    BenchmarkConfig config;
    config.benchmark_name = "default_benchmark";
    config.iterations = 10;
    config.warmup_iterations = 2;
    config.timeout = std::chrono::seconds(300);
    config.collect_system_metrics = true;
    config.collect_memory_metrics = true;
    config.collect_cpu_metrics = true;
    config.sampling_interval = std::chrono::milliseconds(100);
    config.failure_threshold_pct = 10.0;
    
    return config;
}

} // namespace omop::common