#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <chrono>
#include <atomic>
#include <optional>
#include <mutex>
#include <thread>
#include <condition_variable>

namespace omop::monitoring {

/**
 * @brief Metric type enumeration
 */
enum class MetricType {
    Counter,
    Gauge,
    Histogram,
    Summary,
    Timer
};

/**
 * @brief Timer information structure
 */
struct TimerInfo {
    std::string metric_name;
    std::unordered_map<std::string, std::string> labels;
    std::chrono::high_resolution_clock::time_point start_time;
};

/**
 * @brief Metric value structure
 */
struct MetricValue {
    double value;
    std::chrono::system_clock::time_point timestamp;
    std::unordered_map<std::string, std::string> labels;
};

/**
 * @brief Histogram bucket structure
 */
struct HistogramBucket {
    double upper_bound;
    uint64_t count;
};

/**
 * @brief Histogram data structure
 */
struct HistogramData {
    std::vector<HistogramBucket> buckets;
    uint64_t total_count;
    double sum;
};

/**
 * @brief Summary quantile structure
 */
struct SummaryQuantile {
    double quantile;
    double value;
};

/**
 * @brief Summary data structure
 */
struct SummaryData {
    std::vector<SummaryQuantile> quantiles;
    uint64_t count;
    double sum;
};

/**
 * @brief Metric definition structure
 */
struct MetricDefinition {
    std::string name;
    std::string description;
    MetricType type;
    std::vector<std::string> label_names;
    std::vector<double> histogram_buckets;
    std::vector<double> summary_quantiles;
    std::unordered_map<std::string, std::any> metadata;
};

/**
 * @brief Metric data structure
 */
struct Metric {
    MetricDefinition definition;
    std::vector<MetricValue> values;
    std::optional<HistogramData> histogram_data;
    std::optional<SummaryData> summary_data;
    std::chrono::system_clock::time_point last_updated;
};

/**
 * @brief Metrics query structure
 */
struct MetricsQuery {
    std::vector<std::string> metric_names;
    std::vector<MetricType> metric_types;
    std::string name_pattern; // Regex pattern for metric names
    MetricType type{MetricType::Counter}; // Single type filter
    std::optional<std::chrono::system_clock::time_point> start_time;
    std::optional<std::chrono::system_clock::time_point> end_time;
    std::unordered_map<std::string, std::string> label_filters;
    size_t limit{100};
    size_t offset{0};
};

/**
 * @brief Metrics collection configuration
 */
struct MetricsConfig {
    bool enabled{true};
    std::chrono::seconds collection_interval{30};
    std::chrono::seconds retention_period{86400}; // 24 hours
    size_t max_metrics{10000};
    size_t max_values_per_metric{1000};
    std::string export_format{"prometheus"};
    std::string export_endpoint{"/metrics"};
    std::string storage_backend{"memory"};
    std::string storage_path;
    bool enable_compression{false};
    std::vector<std::string> default_labels;
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Metrics collector interface
 * 
 * This interface defines the contract for metrics collectors that capture
 * performance and operational metrics from the ETL system.
 */
class IMetricsCollector {
public:
    virtual ~IMetricsCollector() = default;

    /**
     * @brief Initialize metrics collector
     * @param config Metrics configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const MetricsConfig& config) = 0;

    /**
     * @brief Register metric definition
     * @param definition Metric definition
     * @return bool True if metric registered successfully
     */
    virtual bool register_metric(const MetricDefinition& definition) = 0;

    /**
     * @brief Increment counter metric
     * @param name Metric name
     * @param value Increment value
     * @param labels Metric labels
     * @return bool True if counter incremented successfully
     */
    virtual bool increment_counter(
        const std::string& name,
        double value = 1.0,
        const std::unordered_map<std::string, std::string>& labels = {}) = 0;

    /**
     * @brief Set gauge metric value
     * @param name Metric name
     * @param value Gauge value
     * @param labels Metric labels
     * @return bool True if gauge set successfully
     */
    virtual bool set_gauge(
        const std::string& name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {}) = 0;

    /**
     * @brief Record histogram observation
     * @param name Metric name
     * @param value Observed value
     * @param labels Metric labels
     * @return bool True if observation recorded successfully
     */
    virtual bool observe_histogram(
        const std::string& name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {}) = 0;

    /**
     * @brief Record summary observation
     * @param name Metric name
     * @param value Observed value
     * @param labels Metric labels
     * @return bool True if observation recorded successfully
     */
    virtual bool observe_summary(
        const std::string& name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {}) = 0;

    /**
     * @brief Start timer measurement
     * @param name Metric name
     * @param labels Metric labels
     * @return std::string Timer ID
     */
    virtual std::string start_timer(
        const std::string& name,
        const std::unordered_map<std::string, std::string>& labels = {}) = 0;

    /**
     * @brief Stop timer measurement
     * @param timer_id Timer ID
     * @return std::optional<double> Elapsed time in seconds
     */
    virtual std::optional<double> stop_timer(const std::string& timer_id) = 0;

    /**
     * @brief Get metric by name
     * @param name Metric name
     * @return std::optional<Metric> Metric if exists
     */
    virtual std::optional<Metric> get_metric(const std::string& name) = 0;

    /**
     * @brief Query metrics
     * @param query Query parameters
     * @return std::vector<Metric> List of matching metrics
     */
    virtual std::vector<Metric> query_metrics(const MetricsQuery& query) = 0;

    /**
     * @brief Get all registered metrics
     * @return std::vector<std::string> List of metric names
     */
    virtual std::vector<std::string> get_metric_names() = 0;

    /**
     * @brief Export metrics in specified format
     * @param format Export format (prometheus, json, etc.)
     * @return std::string Exported metrics data
     */
    virtual std::string export_metrics(const std::string& format = "prometheus") = 0;

    /**
     * @brief Clear metric data
     * @param name Metric name (empty to clear all)
     * @return bool True if data cleared successfully
     */
    virtual bool clear_metrics(const std::string& name = "") = 0;

    /**
     * @brief Delete old metric data
     * @param older_than Delete data older than this timestamp
     * @return size_t Number of data points deleted
     */
    virtual size_t delete_old_data(const std::chrono::system_clock::time_point& older_than) = 0;

    /**
     * @brief Get collector statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Get metrics configuration
     * @return MetricsConfig Current configuration
     */
    virtual MetricsConfig get_config() const = 0;

    /**
     * @brief Update metrics configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const MetricsConfig& config) = 0;
};

/**
 * @brief Default metrics collector implementation
 */
class MetricsCollector : public IMetricsCollector {
public:
    MetricsCollector() = default;
    ~MetricsCollector() override = default;

    bool initialize(const MetricsConfig& config) override;
    bool register_metric(const MetricDefinition& definition) override;

    bool increment_counter(
        const std::string& name,
        double value = 1.0,
        const std::unordered_map<std::string, std::string>& labels = {}) override;

    bool set_gauge(
        const std::string& name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {}) override;

    bool observe_histogram(
        const std::string& name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {}) override;

    bool observe_summary(
        const std::string& name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {}) override;

    std::string start_timer(
        const std::string& name,
        const std::unordered_map<std::string, std::string>& labels = {}) override;

    std::optional<double> stop_timer(const std::string& timer_id) override;

    std::optional<Metric> get_metric(const std::string& name) override;
    std::vector<Metric> query_metrics(const MetricsQuery& query) override;
    std::vector<std::string> get_metric_names() override;

    std::string export_metrics(const std::string& format = "prometheus") override;
    bool clear_metrics(const std::string& name = "") override;
    size_t delete_old_data(const std::chrono::system_clock::time_point& older_than) override;

    std::unordered_map<std::string, std::any> get_statistics() override;
    MetricsConfig get_config() const override;
    bool update_config(const MetricsConfig& config) override;

private:
    struct TimerInfo {
        std::string metric_name;
        std::unordered_map<std::string, std::string> labels;
        std::chrono::high_resolution_clock::time_point start_time;
    };

    mutable std::mutex mutex_;
    MetricsConfig config_;
    std::unordered_map<std::string, Metric> metrics_;
    std::unordered_map<std::string, TimerInfo> active_timers_;
    std::unordered_map<std::string, std::vector<double>> summary_values_;
    std::atomic<uint64_t> next_timer_id_;
    
    std::thread collection_thread_;
    std::atomic<bool> collection_thread_running_;
    std::condition_variable collection_cv_;
    
    // Private helper methods
    void cleanup_old_values(Metric& metric);
    void calculate_quantiles(const std::string& name, SummaryData& summary_data);
    void start_collection_thread();
    void stop_collection_thread();
    std::string export_prometheus_format();
    std::string export_json_format();
};

/**
 * @brief Timer helper class for automatic timing
 */
class Timer {
public:
    Timer(IMetricsCollector& collector, const std::string& metric_name,
          const std::unordered_map<std::string, std::string>& labels = {});
    ~Timer();

    Timer(const Timer&) = delete;
    Timer& operator=(const Timer&) = delete;
    Timer(Timer&&) = default;
    Timer& operator=(Timer&&) = default;

    /**
     * @brief Stop timer manually
     * @return std::optional<double> Elapsed time in seconds
     */
    std::optional<double> stop();

private:
    IMetricsCollector& collector_;
    std::string timer_id_;
    bool stopped_{false};
};

/**
 * @brief Create metrics collector instance
 * @return std::unique_ptr<IMetricsCollector> Metrics collector instance
 */
std::unique_ptr<IMetricsCollector> create_metrics_collector();

/**
 * @brief Get default metrics configuration
 * @return MetricsConfig Default configuration
 */
MetricsConfig get_default_metrics_config();

/**
 * @brief Convert metric type to string
 * @param type Metric type enum
 * @return std::string Metric type string
 */
std::string metric_type_to_string(MetricType type);

/**
 * @brief Convert string to metric type
 * @param type_str Metric type string
 * @return MetricType Metric type enum
 */
MetricType string_to_metric_type(const std::string& type_str);

/**
 * @brief Create standard ETL metrics
 * @param collector Metrics collector
 * @return bool True if metrics created successfully
 */
bool create_standard_etl_metrics(IMetricsCollector& collector);

/**
 * @brief Macro for automatic timing
 */
#define TIMED_SCOPE(collector, metric_name, ...) \
    auto timer_##__LINE__ = omop::monitoring::Timer(collector, metric_name, ##__VA_ARGS__)

} // namespace omop::monitoring