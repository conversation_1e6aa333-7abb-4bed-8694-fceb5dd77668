#pragma once

#include <string>
#include <string_view>
#include <memory>
#include <unordered_map>
#include <vector>
#include <optional>
#include <variant>
#include <mutex>
#include <chrono>
#include <yaml-cpp/yaml.h>

#include "exceptions.h"

namespace omop::common {

/**
 * @brief Represents a single transformation rule in the ETL pipeline
 *
 * This class encapsulates the mapping rules from source columns to target columns,
 * including the transformation type and any additional parameters needed for the
 * transformation process.
 */
class TransformationRule {
public:
    /**
     * @brief Enumeration of supported transformation types
     */
    enum class Type {
        Direct,              ///< Direct column mapping without transformation
        DateTransform,       ///< Date format transformation
        VocabularyMapping,   ///< Map using vocabulary lookup
        DateCalculation,     ///< Calculate date based on multiple fields
        NumericTransform,    ///< Numeric value transformation
        StringConcatenation, ///< Concatenate multiple string fields
        Conditional,         ///< Conditional transformation based on rules
        Custom              ///< Custom transformation logic
    };

    /**
     * @brief Default constructor
     */
    TransformationRule() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing transformation configuration
     */
    explicit TransformationRule(const YAML::Node& node);

    /**
     * @brief Get source column name
     * @return std::string_view Source column name
     */
    [[nodiscard]] std::string_view source_column() const noexcept { return source_column_; }

    /**
     * @brief Get source columns for multi-column transformations
     * @return const std::vector<std::string>& Vector of source column names
     */
    [[nodiscard]] const std::vector<std::string>& source_columns() const noexcept {
        return source_columns_;
    }

    /**
     * @brief Get target column name
     * @return std::string_view Target column name
     */
    [[nodiscard]] std::string_view target_column() const noexcept { return target_column_; }

    /**
     * @brief Get transformation type
     * @return Type The transformation type
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get transformation parameters
     * @return const YAML::Node& Additional parameters for the transformation
     */
    [[nodiscard]] const YAML::Node& parameters() const noexcept { return parameters_; }

    /**
     * @brief Check if this is a multi-column transformation
     * @return bool True if multiple source columns are involved
     */
    [[nodiscard]] bool is_multi_column() const noexcept { return !source_columns_.empty(); }

private:
    std::string source_column_;              ///< Single source column name
    std::vector<std::string> source_columns_; ///< Multiple source columns
    std::string target_column_;              ///< Target column name
    Type type_{Type::Direct};                ///< Transformation type
    YAML::Node parameters_;                  ///< Additional parameters
};

/**
 * @brief Configuration for a single table mapping
 *
 * This class represents the complete mapping configuration for transforming
 * data from a source table to an OMOP CDM target table.
 */
class TableMapping {
public:
    /**
     * @brief Default constructor
     */
    TableMapping() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing table mapping configuration
     */
    explicit TableMapping(const YAML::Node& node);

    /**
     * @brief Get source table name
     * @return std::string_view Source table name
     */
    [[nodiscard]] std::string_view source_table() const noexcept { return source_table_; }

    /**
     * @brief Get target table name
     * @return std::string_view Target table name
     */
    [[nodiscard]] std::string_view target_table() const noexcept { return target_table_; }

    /**
     * @brief Get transformation rules
     * @return const std::vector<TransformationRule>& Vector of transformation rules
     */
    [[nodiscard]] const std::vector<TransformationRule>& transformations() const noexcept {
        return transformations_;
    }

    /**
     * @brief Get pre-processing SQL query
     * @return std::optional<std::string> Optional pre-processing SQL
     */
    [[nodiscard]] const std::optional<std::string>& pre_process_sql() const noexcept {
        return pre_process_sql_;
    }

    /**
     * @brief Get post-processing SQL query
     * @return std::optional<std::string> Optional post-processing SQL
     */
    [[nodiscard]] const std::optional<std::string>& post_process_sql() const noexcept {
        return post_process_sql_;
    }

    /**
     * @brief Get filter conditions
     * @return const YAML::Node& Filter conditions as YAML node
     */
    [[nodiscard]] const YAML::Node& filters() const noexcept { return filters_; }

    /**
     * @brief Get validation rules
     * @return const YAML::Node& Validation rules as YAML node
     */
    [[nodiscard]] const YAML::Node& validations() const noexcept { return validations_; }

private:
    std::string source_table_;                    ///< Source table name
    std::string target_table_;                    ///< Target table name
    std::vector<TransformationRule> transformations_; ///< Transformation rules
    std::optional<std::string> pre_process_sql_;  ///< Pre-processing SQL
    std::optional<std::string> post_process_sql_; ///< Post-processing SQL
    YAML::Node filters_;                          ///< Filter conditions
    YAML::Node validations_;                      ///< Validation rules
};

/**
 * @brief Database connection configuration
 */
class DatabaseConfig {
public:
    /**
     * @brief Database types supported
     */
    enum class Type {
        PostgreSQL,
        MySQL,
        MSSQL,
        Oracle
    };

    /**
     * @brief Default constructor
     */
    DatabaseConfig() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing database configuration
     */
    explicit DatabaseConfig(const YAML::Node& node);

    /**
     * @brief Get database type
     * @return Type Database type
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get connection string
     * @return std::string_view Connection string
     */
    [[nodiscard]] std::string_view connection_string() const noexcept {
        return connection_string_;
    }

    /**
     * @brief Get host name
     * @return std::string_view Host name
     */
    [[nodiscard]] std::string_view host() const noexcept { return host_; }

    /**
     * @brief Get port number
     * @return int Port number
     */
    [[nodiscard]] int port() const noexcept { return port_; }

    /**
     * @brief Get database name
     * @return std::string_view Database name
     */
    [[nodiscard]] std::string_view database() const noexcept { return database_; }

    /**
     * @brief Get username
     * @return std::string_view Username
     */
    [[nodiscard]] std::string_view username() const noexcept { return username_; }

    /**
     * @brief Get password
     * @return std::string_view Password
     */
    [[nodiscard]] std::string_view password() const noexcept { return password_; }

    /**
     * @brief Get additional connection parameters
     * @return const std::unordered_map<std::string, std::string>& Additional parameters
     */
    [[nodiscard]] const std::unordered_map<std::string, std::string>& parameters() const noexcept {
        return parameters_;
    }

private:
    Type type_{Type::PostgreSQL};
    std::string connection_string_;
    std::string host_;
    int port_{5432};
    std::string database_;
    std::string username_;
    std::string password_;
    std::unordered_map<std::string, std::string> parameters_;
};

/**
 * @brief Main configuration manager for the ETL pipeline
 *
 * This class manages all configuration aspects of the OMOP ETL pipeline,
 * including loading configuration files, managing table mappings, and
 * providing access to various configuration parameters.
 */
class ConfigurationManager {
public:
    /**
     * @brief Construct a new ConfigurationManager
     */
    ConfigurationManager() = default;

    /**
     * @brief Load configuration from a YAML file
     * @param filepath Path to the YAML configuration file
     * @throws ConfigurationException if the file cannot be loaded or parsed
     */
    void load_config(const std::string& filepath);

    /**
     * @brief Load configuration from a YAML string
     * @param yaml_content YAML content as string
     * @throws ConfigurationException if the content cannot be parsed
     */
    void load_config_from_string(const std::string& yaml_content);

    /**
     * @brief Get table mapping by name
     * @param table_name Name of the table
     * @return std::optional<TableMapping> Table mapping if found
     */
    [[nodiscard]] std::optional<TableMapping> get_table_mapping(
        const std::string& table_name) const;

    /**
     * @brief Get all table mappings
     * @return const std::unordered_map<std::string, TableMapping>& All table mappings
     */
    [[nodiscard]] const std::unordered_map<std::string, TableMapping>&
        get_all_mappings() const noexcept {
        return table_mappings_;
    }

    /**
     * @brief Get source database configuration
     * @return const DatabaseConfig& Source database configuration
     */
    [[nodiscard]] const DatabaseConfig& get_source_db() const noexcept {
        return source_db_;
    }

    /**
     * @brief Get target database configuration
     * @return const DatabaseConfig& Target database configuration
     */
    [[nodiscard]] const DatabaseConfig& get_target_db() const noexcept {
        return target_db_;
    }

    /**
     * @brief Get configuration value by key
     * @param key Configuration key in dot notation (e.g., "etl.batch_size")
     * @return std::optional<YAML::Node> Configuration value if found
     */
    [[nodiscard]] std::optional<YAML::Node> get_value(const std::string& key) const;

    /**
     * @brief Get configuration value with default
     * @tparam T Type of the value
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value or default
     */
    template<typename T>
    [[nodiscard]] T get_value_or(const std::string& key, const T& default_value) const {
        auto value = get_value(key);
        if (value && value->IsDefined()) {
            try {
                return value->as<T>();
            } catch (const YAML::Exception&) {
                return default_value;
            }
        }
        return default_value;
    }

    /**
     * @brief Validate configuration
     * @throws ConfigurationException if configuration is invalid
     */
    void validate_config() const;

    /**
     * @brief Check if configuration is loaded
     * @return bool True if configuration is loaded
     */
    [[nodiscard]] bool is_loaded() const noexcept { return config_loaded_; }

    /**
     * @brief Get vocabulary mappings
     * @return const YAML::Node& Vocabulary mappings configuration
     */
    [[nodiscard]] const YAML::Node& get_vocabulary_mappings() const noexcept {
        return vocabulary_mappings_;
    }

    /**
     * @brief Get ETL settings
     * @return const YAML::Node& ETL settings configuration
     */
    [[nodiscard]] const YAML::Node& get_etl_settings() const noexcept {
        return etl_settings_;
    }
    
    /**
     * @brief Clear all configuration data
     */
    void clear();
    
    /**
     * @brief Get the path of the loaded configuration file
     * @return std::string Path to configuration file (empty if loaded from string)
     */
    [[nodiscard]] std::string get_config_file_path() const noexcept;
    
    /**
     * @brief Get the time when configuration was loaded
     * @return std::chrono::system_clock::time_point Load timestamp
     */
    [[nodiscard]] std::chrono::system_clock::time_point get_load_time() const noexcept;
    
    /**
     * @brief Reload configuration from file
     * @throws ConfigurationException if no file was previously loaded or reload fails
     */
    void reload();

private:
    /**
     * @brief Parse table mappings from configuration
     * @param mappings_node YAML node containing table mappings
     */
    void parse_table_mappings(const YAML::Node& mappings_node);

    /**
     * @brief Parse database configuration
     * @param db_node YAML node containing database configuration
     * @return DatabaseConfig Parsed database configuration
     */
    DatabaseConfig parse_database_config(const YAML::Node& db_node);

    mutable std::mutex config_mutex_;
    bool config_loaded_{false};
    YAML::Node root_config_;
    std::unordered_map<std::string, TableMapping> table_mappings_;
    DatabaseConfig source_db_;
    DatabaseConfig target_db_;
    YAML::Node vocabulary_mappings_;
    YAML::Node etl_settings_;
    YAML::Node tables_node_;
    std::string config_file_path_;
    std::chrono::system_clock::time_point load_time_;
};

/**
 * @brief Singleton accessor for global configuration
 *
 * Provides global access to the configuration manager instance.
 * This follows the singleton pattern to ensure consistent configuration
 * across the entire application.
 */
class Config {
public:
    /**
     * @brief Get the singleton instance
     * @return ConfigurationManager& Reference to the configuration manager
     */
    [[nodiscard]] static ConfigurationManager& instance() {
        std::call_once(init_flag_, []() {
            instance_.reset(new ConfigurationManager());
        });
        return *instance_;
    }

    Config() = delete;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;

private:
    static std::unique_ptr<ConfigurationManager> instance_;
    static std::once_flag init_flag_;
    static std::mutex instance_mutex_;
};

} // namespace omop::common