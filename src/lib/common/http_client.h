#pragma once

#include <string>
#include <unordered_map>
#include <optional>
#include <memory>
#include <functional>
#include <utility>

namespace omop::common {

/**
 * @brief HTTP client for making HTTP requests
 *
 * This class provides basic HTTP functionality for making requests
 * to external vocabulary services and APIs.
 */
class HttpClient {
public:
    /**
     * @brief HTTP method types
     */
    enum class Method {
        GET,
        POST,
        PUT,
        DELETE,
        PATCH
    };

    /**
     * @brief HTTP response structure
     */
    struct Response {
        int status_code{0};
        std::string body;
        std::unordered_map<std::string, std::string> headers;
        std::string error_message;
        bool success{false};
    };

    /**
     * @brief HTTP request structure
     */
    struct Request {
        Method method{Method::GET};
        std::string url;
        std::unordered_map<std::string, std::string> headers;
        std::string body;
        int timeout_seconds{30};
    };

    /**
     * @brief Default constructor
     */
    HttpClient() = default;

    /**
     * @brief Virtual destructor
     */
    virtual ~HttpClient() = default;

    /**
     * @brief Make an HTTP request
     * @param request Request configuration
     * @return Response HTTP response
     */
    virtual Response make_request(const Request& request) = 0;

    /**
     * @brief Make a GET request
     * @param url URL to request
     * @param headers Optional headers
     * @return Response HTTP response
     */
    virtual Response get(const std::string& url, 
                        const std::unordered_map<std::string, std::string>& headers = {}) {
        Request request;
        request.method = Method::GET;
        request.url = url;
        request.headers = headers;
        return make_request(request);
    }

    /**
     * @brief Make a POST request
     * @param url URL to request
     * @param body Request body
     * @param headers Optional headers
     * @return Response HTTP response
     */
    virtual Response post(const std::string& url, 
                         const std::string& body = "",
                         const std::unordered_map<std::string, std::string>& headers = {}) {
        Request request;
        request.method = Method::POST;
        request.url = url;
        request.body = body;
        request.headers = headers;
        return make_request(request);
    }

    /**
     * @brief Set default timeout
     * @param timeout_seconds Timeout in seconds
     */
    virtual void set_timeout(int timeout_seconds) {
        default_timeout_ = timeout_seconds;
    }

    /**
     * @brief Set default headers
     * @param headers Default headers to include in all requests
     */
    virtual void set_default_headers(const std::unordered_map<std::string, std::string>& headers) {
        default_headers_ = headers;
    }

protected:
    int default_timeout_{30};
    std::unordered_map<std::string, std::string> default_headers_;
};

/**
 * @brief Simple HTTP client implementation
 *
 * This is a basic implementation that can be extended with actual HTTP library
 * like libcurl or httplib.
 */
class SimpleHttpClient : public HttpClient {
public:
    /**
     * @brief Constructor
     */
    SimpleHttpClient() = default;

    /**
     * @brief Destructor
     */
    ~SimpleHttpClient() override = default;

    /**
     * @brief Make an HTTP request
     * @param request Request configuration
     * @return Response HTTP response
     */
    Response make_request(const Request& request) override;

private:
    /**
     * @brief Parse URL into components
     * @param url URL to parse
     * @return std::optional<std::pair<std::string, std::string>> Host and path
     */
    std::optional<std::pair<std::string, std::string>> parse_url(const std::string& url);
};

/**
 * @brief HTTPS-capable HTTP client implementation using libcurl
 */
class CurlHttpClient : public HttpClient {
public:
    /**
     * @brief Constructor
     */
    CurlHttpClient();

    /**
     * @brief Destructor
     */
    ~CurlHttpClient() override;

    /**
     * @brief Make an HTTP request with full SSL/TLS support
     * @param request Request configuration
     * @return Response HTTP response
     */
    Response make_request(const Request& request) override;

    /**
     * @brief Set SSL verification options
     * @param verify_peer Verify SSL peer certificate
     * @param verify_host Verify SSL hostname
     */
    void set_ssl_verification(bool verify_peer, bool verify_host);

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief HTTP client factory
 */
class HttpClientFactory {
public:
    /**
     * @brief Create HTTP client instance
     * @return std::unique_ptr<HttpClient> HTTP client
     */
    static std::unique_ptr<HttpClient> create_client();

    /**
     * @brief Create HTTP client with custom configuration
     * @param config Configuration parameters
     * @return std::unique_ptr<HttpClient> HTTP client
     */
    static std::unique_ptr<HttpClient> create_client(const std::unordered_map<std::string, std::string>& config);
};

} // namespace omop::common 