/**
 * @file validation.h
 * @brief Data validation framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains the validation framework for ensuring data quality
 * and integrity throughout the ETL process.
 */

#pragma once

#include "exceptions.h"
#include "logging.h"

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <variant>
#include <optional>
#include <chrono>
#include <regex>
#include <any>
#include <format>

namespace omop::common {

/**
 * @brief Enumeration of validation rule types
 */
enum class ValidationType {
    NOT_NULL,           ///< Field must not be null
    NOT_EMPTY,          ///< Field must not be empty
    UNIQUE,             ///< Field must be unique
    IN_LIST,            ///< Field must be in specified list
    REGEX,              ///< Field must match regex pattern
    DATE_RANGE,         ///< Date must be within range
    NUMERIC_RANGE,      ///< Number must be within range
    GREATER_THAN,       ///< Number must be greater than value
    LESS_THAN,          ///< Number must be less than value
    BETWEEN,            ///< Value must be between two values
    BEFORE,             ///< Date must be before another date
    AFTER,              ///< Date must be after another date
    LENGTH,             ///< String length validation
    CUSTOM,             ///< Custom validation function
    NOT_ZERO,           ///< Number must not be zero
    NOT_FUTURE_DATE,    ///< Date must not be in the future
    FOREIGN_KEY,        ///< Foreign key constraint
    COMPOSITE_KEY,      ///< Composite key validation
    CONDITIONAL         ///< Conditional validation based on other fields
};

class ValidationResult {
public:
    struct ValidationError {
        std::string field_name;
        std::string error_message;
        std::string rule_name;
    };

    struct ValidationWarning {
        std::string field_name;
        std::string warning_message;
        std::string rule_name;
    };

    ValidationResult() = default;

    void add_error(ValidationError error) {
        errors_.push_back(std::move(error));
        is_valid_ = false;
    }

    void add_error(const std::string& field_name,
                  const std::string& error_message,
                  const std::string& rule_name) {
        errors_.push_back({field_name, error_message, rule_name});
        is_valid_ = false;
    }

    void add_warning(ValidationWarning warning) {
        warnings_.push_back(std::move(warning));
    }

    void add_warning(const std::string& field_name,
                    const std::string& warning_message,
                    const std::string& rule_name) {
        warnings_.push_back({field_name, warning_message, rule_name});
    }

    [[nodiscard]] bool is_valid() const noexcept { return is_valid_; }

    [[nodiscard]] const std::vector<ValidationError>& errors() const noexcept {
        return errors_;
    }

    [[nodiscard]] const std::vector<ValidationWarning>& warnings() const noexcept {
        return warnings_;
    }

    [[nodiscard]] size_t error_count() const noexcept { return errors_.size(); }

    [[nodiscard]] std::string error_messages() const {
        if (errors_.empty()) {
            return "";
        }
        std::string result;
        for (const auto& error : errors_) {
            result += std::format("Field '{}': {} (rule: {})\n",
                                 error.field_name,
                                 error.error_message,
                                 error.rule_name);
        }
        return result;
    }

    void merge(const ValidationResult& other) {
        if (!other.is_valid()) {
            is_valid_ = false;
            errors_.insert(errors_.end(), other.errors().begin(), other.errors().end());
        }
        warnings_.insert(warnings_.end(), other.warnings().begin(), other.warnings().end());
    }

private:
    bool is_valid_{true};
    std::vector<ValidationError> errors_;
    std::vector<ValidationWarning> warnings_;
};

/**
 * @brief Base class for validation rules
 */
class ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param type Type of validation
     * @param error_message Custom error message
     */
    ValidationRule(const std::string& field_name,
                  ValidationType type,
                  const std::string& error_message = "");

    /**
     * @brief Virtual destructor
     */
    virtual ~ValidationRule() = default;

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if validation passes, false otherwise
     */
    virtual bool validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const = 0;

    /**
     * @brief Get the error message for validation failure
     * @return Error message string
     */
    virtual std::string getErrorMessage() const;

    /**
     * @brief Get the field name
     * @return Field name string
     */
    const std::string& getFieldName() const { return field_name_; }

    /**
     * @brief Get the validation type
     * @return Validation type enum
     */
    ValidationType getType() const { return type_; }

protected:
    std::string field_name_;      ///< Name of the field to validate
    ValidationType type_;         ///< Type of validation
    std::string error_message_;   ///< Custom error message
};

/**
 * @brief Not null validation rule
 */
class NotNullRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     */
    explicit NotNullRule(const std::string& field_name);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;
};

/**
 * @brief In list validation rule
 */
template<typename T>
class InListRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param allowed_values List of allowed values
     */
    InListRule(const std::string& field_name, const std::vector<T>& allowed_values);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::vector<T> allowed_values_;  ///< List of allowed values
};

/**
 * @brief Date range validation rule
 */
class DateRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_date Minimum allowed date (optional)
     * @param max_date Maximum allowed date (optional)
     */
    DateRangeRule(const std::string& field_name,
                  const std::optional<std::chrono::system_clock::time_point>& min_date,
                  const std::optional<std::chrono::system_clock::time_point>& max_date);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<std::chrono::system_clock::time_point> min_date_;  ///< Minimum date
    std::optional<std::chrono::system_clock::time_point> max_date_;  ///< Maximum date
};

/**
 * @brief Numeric range validation rule
 */
template<typename T>
class NumericRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_value Minimum allowed value (optional)
     * @param max_value Maximum allowed value (optional)
     */
    NumericRangeRule(const std::string& field_name,
                     const std::optional<T>& min_value,
                     const std::optional<T>& max_value);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<T> min_value_;  ///< Minimum value
    std::optional<T> max_value_;  ///< Maximum value
};

/**
 * @brief Regular expression validation rule
 */
class RegexRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param pattern Regular expression pattern
     */
    RegexRule(const std::string& field_name, const std::string& pattern);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::regex pattern_;  ///< Regular expression pattern
};

/**
 * @brief Custom validation rule with user-defined function
 */
class CustomRule : public ValidationRule {
public:
    using ValidationFunction = std::function<bool(const std::any&, const std::unordered_map<std::string, std::any>&)>;

    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param validator Custom validation function
     * @param error_message Custom error message
     */
    CustomRule(const std::string& field_name,
               ValidationFunction validator,
               const std::string& error_message = "");

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    ValidationFunction validator_;  ///< Custom validation function
};

/**
 * @brief Basic validation engine for applying validation rules
 */
class BasicValidationEngine {
public:
    /**
     * @brief Constructor
     * @param logger Logger instance
     */
    explicit BasicValidationEngine(std::shared_ptr<Logger> logger = nullptr);

    /**
     * @brief Add a validation rule
     * @param rule Validation rule to add
     */
    void addRule(std::unique_ptr<ValidationRule> rule);

    /**
     * @brief Validate a single record
     * @param record Record to validate
     * @return Validation result
     */
    ValidationResult validateRecord(const std::unordered_map<std::string, std::any>& record);

    /**
     * @brief Validate a batch of records
     * @param records Records to validate
     * @param stop_on_error Stop validation on first error
     * @return Validation result
     */
    ValidationResult validateBatch(const std::vector<std::unordered_map<std::string, std::any>>& records,
                                  bool stop_on_error = false);

    /**
     * @brief Clear all validation rules
     */
    void clearRules();

    /**
     * @brief Get the number of rules
     * @return Number of validation rules
     */
    size_t getRuleCount() const { return rules_.size(); }

    /**
     * @brief Enable or disable specific validation types
     * @param type Validation type to enable/disable
     * @param enabled true to enable, false to disable
     */
    void setValidationTypeEnabled(ValidationType type, bool enabled);

private:
    std::vector<std::unique_ptr<ValidationRule>> rules_;  ///< List of validation rules
    std::unordered_map<ValidationType, bool> enabled_types_;  ///< Enabled validation types
    std::shared_ptr<Logger> logger_;  ///< Logger instance
};

/**
 * @brief Factory class for creating validation rules from configuration
 */
class ValidationRuleFactory {
public:
    /**
     * @brief Create a validation rule from configuration
     * @param config Configuration map
     * @return Unique pointer to validation rule
     */
    static std::unique_ptr<ValidationRule> createRule(const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create validation engine from YAML configuration
     * @param yaml_config YAML configuration string
     * @return Unique pointer to validation engine
     */
    static std::unique_ptr<BasicValidationEngine> createEngineFromYaml(const std::string& yaml_config);
};

} // namespace omop::common
