#ifndef OMOP_CONFIG_H
#define OMOP_CONFIG_H

// Project version
#define OMOP_VERSION "@PROJECT_VERSION@"
#define OMOP_VERSION_MAJOR @PROJECT_VERSION_CURRENT@
#define OMOP_VERSION_MINOR @PROJECT_VERSION_AGE@
#define OMOP_VERSION_PATCH @PROJECT_VERSION_REVISION@

// Build configuration
#define OMOP_BUILD_TYPE "@CMAKE_BUILD_TYPE@"
#define OMOP_SHARED_LIBS @BUILD_SHARED_LIBS@

// Feature flags
#define OMOP_ENABLE_TESTS @BUILD_TESTS@
#define OMOP_ENABLE_DOCS @BUILD_DOCS@
#define OMOP_ENABLE_COVERAGE @ENABLE_COVERAGE@

// Database configuration
#define OMOP_HAVE_ODBC @HAVE_ODBC@
#define OMOP_POSTGRESQL_VERSION "@PostgreSQL_VERSION@"

// Compression support
#define OMOP_HAV<PERSON>_LIBARCHIVE @HAVE_LIBARCHIVE_NUM@

#endif // OMOP_CONFIG_H