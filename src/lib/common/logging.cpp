/**
 * @file logging.cpp
 * @brief Implementation of logging framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "logging.h"
#include <nlohmann/json.hpp>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <yaml-cpp/yaml.h>
#include <thread>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <iostream>
#include <filesystem> // Required for filesystem operations

namespace omop::common {

// Static member definitions
std::unordered_map<std::string, std::shared_ptr<Logger>> Logger::loggers_;
std::mutex Logger::loggers_mutex_;

// Helper function to convert LogLevel to spdlog level
static spdlog::level::level_enum to_spdlog_level(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return spdlog::level::trace;
        case LogLevel::Debug: return spdlog::level::debug;
        case LogLevel::Info: return spdlog::level::info;
        case LogLevel::Warning: return spdlog::level::warn;
        case LogLevel::Error: return spdlog::level::err;
        case LogLevel::Critical: return spdlog::level::critical;
        default: return spdlog::level::info;
    }
}

// Helper function to convert LogLevel to string
static std::string log_level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return "TRACE";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

// JsonLogFormatter implementation
std::string JsonLogFormatter::format(const LogEntry& entry) {
    nlohmann::json j;

    // Format timestamp as ISO 8601
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S");

    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    ss << "." << std::setfill('0') << std::setw(3) << ms.count() << "Z";

    j["timestamp"] = ss.str();
    j["level"] = log_level_to_string(entry.level);
    j["logger"] = entry.logger_name;
    j["message"] = entry.message;
    j["thread_id"] = entry.thread_id;

    if (!entry.job_id.empty()) {
        j["job_id"] = entry.job_id;
    }

    if (!entry.component.empty()) {
        j["component"] = entry.component;
    }

    if (!entry.operation.empty()) {
        j["operation"] = entry.operation;
    }

    if (entry.error_code.has_value()) {
        j["error_code"] = entry.error_code.value();
    }

    if (entry.stack_trace.has_value()) {
        j["stack_trace"] = entry.stack_trace.value();
    }

    // Add context fields
    if (!entry.context.empty()) {
        nlohmann::json context_json;
        for (const auto& [key, value] : entry.context) {
            // Handle different types in std::any
            if (value.type() == typeid(int)) {
                context_json[key] = std::any_cast<int>(value);
            } else if (value.type() == typeid(double)) {
                context_json[key] = std::any_cast<double>(value);
            } else if (value.type() == typeid(std::string)) {
                context_json[key] = std::any_cast<std::string>(value);
            } else if (value.type() == typeid(bool)) {
                context_json[key] = std::any_cast<bool>(value);
            } else if (value.type() == typeid(size_t)) {
                context_json[key] = std::any_cast<size_t>(value);
            } else {
                // Try to convert to string
                try {
                    context_json[key] = std::any_cast<std::string>(value);
                } catch (...) {
                    context_json[key] = "<unsupported type>";
                }
            }
        }
        j["context"] = context_json;
    }

    if (pretty_print_) {
        return j.dump(4);
    } else {
        return j.dump();
    }
}

// TextLogFormatter implementation
TextLogFormatter::TextLogFormatter(const std::string& pattern)
    : pattern_(pattern) {}

std::string TextLogFormatter::format(const LogEntry& entry) {
    std::string result = pattern_;

    // Format timestamp
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    std::stringstream timestamp_ss;
    timestamp_ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");

    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    timestamp_ss << "." << std::setfill('0') << std::setw(3) << ms.count();

    // Replace placeholders
    size_t pos = 0;
    while ((pos = result.find("%Y-%m-%d %H:%M:%S.%e", pos)) != std::string::npos) {
        result.replace(pos, 21, timestamp_ss.str());
        pos += timestamp_ss.str().length();
    }

    // Replace level
    pos = 0;
    while ((pos = result.find("%l", pos)) != std::string::npos) {
        std::string level_str = log_level_to_string(entry.level);
        result.replace(pos, 2, level_str);
        pos += level_str.length();
    }

    // Replace logger name
    pos = 0;
    while ((pos = result.find("%n", pos)) != std::string::npos) {
        result.replace(pos, 2, entry.logger_name);
        pos += entry.logger_name.length();
    }

    // Replace message
    pos = 0;
    while ((pos = result.find("%v", pos)) != std::string::npos) {
        std::string full_message = entry.message;

        // Append job_id and component if present
        if (!entry.job_id.empty() || !entry.component.empty()) {
            full_message += " [";
            if (!entry.job_id.empty()) {
                full_message += "job:" + entry.job_id;
                if (!entry.component.empty()) {
                    full_message += ", ";
                }
            }
            if (!entry.component.empty()) {
                full_message += "component:" + entry.component;
            }
            full_message += "]";
        }

        result.replace(pos, 2, full_message);
        pos += full_message.length();
    }

    return result;
}

// Logger implementation
std::shared_ptr<Logger> Logger::get(const std::string& name) {
    std::lock_guard<std::mutex> lock(loggers_mutex_);

    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }

    // Use make_shared for exception safety and atomicity
    try {
        auto logger = std::make_shared<Logger>(name);
        loggers_[name] = logger;
        return logger;
    } catch (...) {
        // If logger creation fails, ensure map remains unchanged
        throw;
    }
}

Logger::Logger(const std::string& name)
    : name_(name) {
    // Create spdlog logger with console sink by default
    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    spdlog_logger_ = std::make_shared<spdlog::logger>(name, console_sink);
    spdlog_logger_->set_level(spdlog::level::info);
}

void Logger::set_level(LogLevel level) {
    min_level_ = level;
    if (spdlog_logger_) {
        spdlog_logger_->set_level(to_spdlog_level(level));
    }
}

void Logger::add_sink(std::shared_ptr<ILogSink> sink) {
    if (!sink) {
        return;
    }
    
    // Thread-safe sink addition
    std::lock_guard<std::mutex> lock(sinks_mutex_);
    sinks_.push_back(sink);
}

void Logger::log_structured(LogLevel level, const std::string& msg,
                           const std::unordered_map<std::string, std::any>& context) {
    if (level < min_level_) return;

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = level,
        .logger_name = name_,
        .message = msg,
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_operation(const std::string& operation,
                          const std::string& status,
                          const std::unordered_map<std::string, std::any>& details) {
    std::unordered_map<std::string, std::any> context = details;
    context["operation_status"] = status;

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Info,
        .logger_name = name_,
        .message = std::format("Operation '{}' completed with status: {}", operation, status),
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = operation,
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_metrics(const std::unordered_map<std::string, double>& metrics) {
    std::unordered_map<std::string, std::any> context;
    for (const auto& [key, value] : metrics) {
        context[key] = value;
    }

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Info,
        .logger_name = name_,
        .message = "Metrics update",
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "metrics",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_exception(const std::exception& e,
                          const std::unordered_map<std::string, std::any>& context) {
    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Error,
        .logger_name = name_,
        .message = std::format("Exception caught: {}", e.what()),
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::string(e.what())  // In production, use proper stack trace
    };

    write_entry(entry);
}

void Logger::write_entry(const LogEntry& entry) {
    // Write to custom sinks
    {
        std::lock_guard<std::mutex> lock(sinks_mutex_);
        for (const auto& sink : sinks_) {
            if (sink) sink->write(entry);
        }
    }

    // Also write to spdlog
    if (spdlog_logger_) {
        switch (entry.level) {
            case LogLevel::Trace:
                spdlog_logger_->trace(entry.message);
                break;
            case LogLevel::Debug:
                spdlog_logger_->debug(entry.message);
                break;
            case LogLevel::Info:
                spdlog_logger_->info(entry.message);
                break;
            case LogLevel::Warning:
                spdlog_logger_->warn(entry.message);
                break;
            case LogLevel::Error:
                spdlog_logger_->error(entry.message);
                break;
            case LogLevel::Critical:
                spdlog_logger_->critical(entry.message);
                break;
        }
    }
}

std::string Logger::get_thread_id() const {
    std::stringstream ss;
    ss << std::this_thread::get_id();
    return ss.str();
}

// PerformanceLogger implementation
void PerformanceLogger::start_timing(const std::string& operation_id) {
    std::lock_guard<std::mutex> lock(timings_mutex_);
    timings_[operation_id] = std::chrono::steady_clock::now();
}

void PerformanceLogger::end_timing(const std::string& operation_id,
                                  std::optional<size_t> record_count) {
    std::lock_guard<std::mutex> lock(timings_mutex_);

    auto it = timings_.find(operation_id);
    if (it == timings_.end()) {
        logger_->warn("No start timing found for operation: {}", operation_id);
        return;
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - it->second).count();

    std::unordered_map<std::string, std::any> metrics;
    metrics["duration_ms"] = static_cast<double>(duration);
    metrics["operation_id"] = operation_id;

    if (record_count.has_value()) {
        metrics["record_count"] = record_count.value();
        if (duration > 0) {
            double records_per_second = (record_count.value() * 1000.0) / duration;
            metrics["records_per_second"] = records_per_second;
        }
    }

    logger_->log_operation(operation_id, "completed", metrics);
    timings_.erase(it);
}

void PerformanceLogger::log_throughput(const std::string& operation,
                                      double records_per_second) {
    std::unordered_map<std::string, std::any> metrics;
    metrics["operation"] = operation;
    metrics["throughput_rps"] = records_per_second;

    logger_->log_metrics({{"throughput_" + operation, records_per_second}});
}

void PerformanceLogger::log_resource_usage(double cpu_percent,
                                          double memory_mb,
                                          double disk_io_mb) {
    std::unordered_map<std::string, double> metrics;
    metrics["cpu_percent"] = cpu_percent;
    metrics["memory_mb"] = memory_mb;
    metrics["disk_io_mb"] = disk_io_mb;

    logger_->log_metrics(metrics);
}

void PerformanceLogger::log_operation(const std::string& operation_id,
                                     const std::chrono::steady_clock::time_point& start_time,
                                     const std::chrono::steady_clock::time_point& end_time) {
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::unordered_map<std::string, std::any> context;
    context["operation_id"] = operation_id;
    context["start_time"] = start_time;
    context["end_time"] = end_time;
    context["duration_ms"] = duration.count();
    
    logger_->log_structured(LogLevel::Info,
        std::format("Operation completed: {} ({} ms)", operation_id, duration.count()),
        context);
}

std::unordered_map<std::string, double> PerformanceLogger::export_metrics() const {
    std::lock_guard<std::mutex> lock(timings_mutex_);
    std::unordered_map<std::string, double> metrics;
    
    for (const auto& [operation, timing] : timings_) {
        // Calculate duration if we have both start and end times
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - timing);
        metrics[operation + "_duration_ms"] = duration.count();
    }
    
    return metrics;
}

// AuditLogger implementation
void AuditLogger::log_data_access(const std::string& table_name,
                                 const std::string& operation,
                                 size_t record_count,
                                 const std::string& user) {
    if (!logger_) {
        return;
    }
    
    std::unordered_map<std::string, std::any> context;
    {
        // Ensure thread-safe context building
        context["table_name"] = table_name;
        context["operation"] = operation;
        context["record_count"] = record_count;
        context["user"] = user;
        context["audit_type"] = std::string("data_access");
    }

    std::string audit_message = std::format("Data access: {} {} on {} ({} records)",
                                           user, operation, table_name, record_count);
    
    logger_->log_structured(LogLevel::Info, audit_message, context);
    
    // Add to audit trail
    {
        std::lock_guard<std::mutex> lock(audit_trail_mutex_);
        audit_trail_.push_back(audit_message);
    }
}

void AuditLogger::log_config_change(const std::string& config_item,
                                   const std::string& old_value,
                                   const std::string& new_value,
                                   const std::string& user) {
    std::unordered_map<std::string, std::any> context;
    context["config_item"] = config_item;
    context["old_value"] = old_value;
    context["new_value"] = new_value;
    context["user"] = user;
    context["audit_type"] = std::string("config_change");

    logger_->log_structured(LogLevel::Info,
        std::format("Configuration change: {} changed '{}' from '{}' to '{}'",
                   user, config_item, old_value, new_value),
        context);
}

void AuditLogger::log_security_event(const std::string& event_type,
                                    const std::unordered_map<std::string, std::string>& details) {
    std::unordered_map<std::string, std::any> context;
    for (const auto& [key, value] : details) {
        context[key] = value;
    }
    context["audit_type"] = std::string("security");
    context["event_type"] = event_type;

    logger_->log_structured(LogLevel::Warning,
        std::format("Security event: {}", event_type),
        context);
}

void AuditLogger::log_data_modification(const std::string& user,
                                       const std::string& table_name,
                                       const std::string& operation,
                                       size_t record_count) {
    std::unordered_map<std::string, std::any> context;
    context["user"] = user;
    context["table_name"] = table_name;
    context["operation"] = operation;
    context["record_count"] = record_count;
    context["audit_type"] = std::string("data_modification");

    std::string audit_message = std::format("Data modification: {} {} on {} ({} records)",
                                           user, operation, table_name, record_count);
    
    logger_->log_structured(LogLevel::Info, audit_message, context);
    
    // Add to audit trail
    {
        std::lock_guard<std::mutex> lock(audit_trail_mutex_);
        audit_trail_.push_back(audit_message);
    }
}

void AuditLogger::log_data_export(const std::string& user,
                                 const std::string& table_name,
                                 const std::string& operation,
                                 size_t record_count) {
    std::unordered_map<std::string, std::any> context;
    context["user"] = user;
    context["table_name"] = table_name;
    context["operation"] = operation;
    context["record_count"] = record_count;
    context["audit_type"] = std::string("data_export");

    std::string audit_message = std::format("Data export: {} {} from {} ({} records)",
                                           user, operation, table_name, record_count);
    
    logger_->log_structured(LogLevel::Info, audit_message, context);
    
    // Add to audit trail
    {
        std::lock_guard<std::mutex> lock(audit_trail_mutex_);
        audit_trail_.push_back(audit_message);
    }
}

void AuditLogger::log_access_denied(const std::string& user,
                                   const std::string& table_name,
                                   const std::string& operation,
                                   const std::string& reason) {
    std::unordered_map<std::string, std::any> context;
    context["user"] = user;
    context["table_name"] = table_name;
    context["operation"] = operation;
    context["reason"] = reason;
    context["audit_type"] = std::string("access_denied");

    std::string audit_message = std::format("Access denied: {} attempted {} on {} - {}", 
                                           user, operation, table_name, reason);
    
    logger_->log_structured(LogLevel::Warning, audit_message, context);
    
    // Add to audit trail
    {
        std::lock_guard<std::mutex> lock(audit_trail_mutex_);
        audit_trail_.push_back(audit_message);
    }
}

std::vector<std::string> AuditLogger::get_audit_trail() const {
    std::lock_guard<std::mutex> lock(audit_trail_mutex_);
    return audit_trail_;
}

// LoggingConfig implementation
void LoggingConfig::initialize(const std::string& config_file) {
    try {
        YAML::Node config = YAML::LoadFile(config_file);
        
        // Set global log level
        if (config["logging"] && config["logging"]["level"]) {
            std::string level_str = config["logging"]["level"].as<std::string>();
            LogLevel level = LogLevel::Info;
            
            if (level_str == "trace") level = LogLevel::Trace;
            else if (level_str == "debug") level = LogLevel::Debug;
            else if (level_str == "info") level = LogLevel::Info;
            else if (level_str == "warning" || level_str == "warn") level = LogLevel::Warning;
            else if (level_str == "error") level = LogLevel::Error;
            else if (level_str == "critical") level = LogLevel::Critical;
            
            set_global_level(level);
        }
        
        // Set log pattern
        if (config["logging"] && config["logging"]["pattern"]) {
            std::string pattern = config["logging"]["pattern"].as<std::string>();
            spdlog::set_pattern(pattern);
        }
        
        // Configure file output
        if (config["logging"] && config["logging"]["file"]) {
            auto file_config = config["logging"]["file"];
            std::string filename = file_config["path"].as<std::string>("logs/omop_etl.log");
            size_t max_size = file_config["max_size_mb"].as<size_t>(10) * 1048576;
            size_t max_files = file_config["max_files"].as<size_t>(5);
            
            auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                filename, max_size, max_files);
            
            auto default_logger = std::make_shared<spdlog::logger>("default", file_sink);
            spdlog::set_default_logger(default_logger);
        }
    } catch (const std::exception& e) {
        // If configuration loading fails, fall back to default
        initialize_default();
    }
}

void LoggingConfig::initialize_default() {
    // Set default pattern for spdlog
    spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%n] %v");

    // Set default level
    spdlog::set_level(spdlog::level::info);

    // Create default file sink
    auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        "logs/omop_etl.log", 1048576 * 10, 5);

    // Set default logger
    auto default_logger = std::make_shared<spdlog::logger>("default", file_sink);
    spdlog::set_default_logger(default_logger);
}

void LoggingConfig::set_global_level(LogLevel level) {
    spdlog::set_level(to_spdlog_level(level));
}

void LoggingConfig::add_global_sink(std::shared_ptr<ILogSink> sink) {
    // Add sink to all existing loggers
    std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
    for (auto& [name, logger] : Logger::loggers_) {
        logger->add_sink(sink);
    }
}

void LoggingConfig::flush_all() {
    spdlog::apply_all([](std::shared_ptr<spdlog::logger> l) { l->flush(); });
    
    // Also flush custom sinks
    std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
    for (auto& [name, logger] : Logger::loggers_) {
        // Custom flush implementation if needed
        [[maybe_unused]] auto name_unused = name;
        [[maybe_unused]] auto logger_unused = logger;
    }
}

void LoggingConfig::shutdown() {
    flush_all();
    
    // Clear all loggers before spdlog shutdown
    {
        std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
        Logger::loggers_.clear();
    }
    
    spdlog::shutdown();
}

// Note: DatabaseLogSink implementation has been removed to eliminate circular dependency.
// Concrete database log sink implementations should be provided by the extract module
// or other modules that have access to database connections.

// FileSink implementation
FileSink::FileSink(const std::string& filename, size_t max_size, size_t max_files)
    : filename_(filename), max_size_(max_size), max_files_(max_files), current_size_(0) {
    open_file();
}

FileSink::~FileSink() {
    close_file();
}

void FileSink::write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!file_stream_.is_open()) {
        open_file();
    }
    
    std::string formatted_message;
    if (formatter_) {
        formatted_message = formatter_->format(entry);
    } else {
        // Default formatting
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
           << " [" << log_level_to_string(entry.level) << "] "
           << "[" << entry.logger_name << "] "
           << entry.message;
        formatted_message = ss.str();
    }
    
    file_stream_ << formatted_message << std::endl;
    current_size_ += formatted_message.length() + 1; // +1 for newline
    
    rotate_if_needed();
}

void FileSink::flush() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    if (file_stream_.is_open()) {
        file_stream_.flush();
    }
}

void FileSink::rotate_if_needed() {
    if (current_size_ >= max_size_) {
        close_file();
        // Rename current file to backup
        for (int i = static_cast<int>(max_files_) - 1; i > 0; --i) {
            std::string old_name = filename_ + "." + std::to_string(i);
            std::string new_name = filename_ + "." + std::to_string(i + 1);
            if (std::filesystem::exists(old_name)) {
                std::filesystem::rename(old_name, new_name);
            }
        }
        std::string backup_name = filename_ + ".1";
        if (std::filesystem::exists(filename_)) {
            std::filesystem::rename(filename_, backup_name);
        }
        open_file();
    }
}

void FileSink::open_file() {
    file_stream_.open(filename_, std::ios::app);
    if (!file_stream_.is_open()) {
        throw std::runtime_error("Failed to open log file: " + filename_);
    }
    current_size_ = std::filesystem::file_size(filename_);
}

void FileSink::close_file() {
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
}

// ConsoleSink implementation
ConsoleSink::ConsoleSink(bool use_color) 
    : use_color_(use_color), use_stderr_for_errors_(true) {}

void ConsoleSink::write(const LogEntry& entry) {
    std::ostream* output_stream = &std::cout;
    
    // Use stderr for errors and critical messages
    if (use_stderr_for_errors_ && 
        (entry.level == LogLevel::Error || entry.level == LogLevel::Critical)) {
        output_stream = &std::cerr;
    }
    
    std::string formatted_message;
    if (formatter_) {
        formatted_message = formatter_->format(entry);
    } else {
        // Default formatting with optional color
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        std::stringstream ss;
        
        // Add color codes if enabled
        if (use_color_) {
            switch (entry.level) {
                case LogLevel::Trace:
                    ss << "\033[37m"; // White
                    break;
                case LogLevel::Debug:
                    ss << "\033[36m"; // Cyan
                    break;
                case LogLevel::Info:
                    ss << "\033[32m"; // Green
                    break;
                case LogLevel::Warning:
                    ss << "\033[33m"; // Yellow
                    break;
                case LogLevel::Error:
                    ss << "\033[31m"; // Red
                    break;
                case LogLevel::Critical:
                    ss << "\033[35m"; // Magenta
                    break;
            }
        }
        
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
           << " [" << log_level_to_string(entry.level) << "] "
           << "[" << entry.logger_name << "] ";
        
        if (!entry.job_id.empty()) {
            ss << "[job:" << entry.job_id << "] ";
        }
        
        ss << entry.message;
        
        if (use_color_) {
            ss << "\033[0m"; // Reset color
        }
        
        formatted_message = ss.str();
    }
    
    *output_stream << formatted_message << std::endl;
}

void ConsoleSink::flush() {
    std::cout.flush();
    std::cerr.flush();
}

// RotatingFileSink implementation
RotatingFileSink::RotatingFileSink(const std::string& base_filename, size_t max_size, size_t max_files)
    : base_filename_(base_filename), max_size_(max_size), max_files_(max_files), current_size_(0) {
    open_file();
}

RotatingFileSink::~RotatingFileSink() {
    if (current_file_.is_open()) {
        current_file_.close();
    }
}

void RotatingFileSink::write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!current_file_.is_open()) {
        open_file();
    }
    
    std::string formatted_message;
    if (formatter_) {
        formatted_message = formatter_->format(entry);
    } else {
        // Default formatting
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
           << " [" << log_level_to_string(entry.level) << "] "
           << "[" << entry.logger_name << "] "
           << entry.message;
        formatted_message = ss.str();
    }
    
    current_file_ << formatted_message << std::endl;
    current_size_ += formatted_message.length() + 1; // +1 for newline
    
    if (current_size_ >= max_size_) {
        rotate();
    }
}

void RotatingFileSink::flush() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    if (current_file_.is_open()) {
        current_file_.flush();
    }
}

void RotatingFileSink::rotate() {
    current_file_.close();
    // Rename existing files
    for (int i = static_cast<int>(max_files_) - 1; i > 0; --i) {
        std::string old_name = base_filename_ + "." + std::to_string(i);
        std::string new_name = base_filename_ + "." + std::to_string(i + 1);
        if (std::filesystem::exists(old_name)) {
            std::filesystem::rename(old_name, new_name);
        }
    }
    // Rename current file to .1
    std::string current_filename = get_current_filename();
    if (std::filesystem::exists(current_filename)) {
        std::filesystem::rename(current_filename, base_filename_ + ".1");
    }
    open_file();
}

std::string RotatingFileSink::get_current_filename() const {
    return base_filename_;
}

void RotatingFileSink::cleanup_old_files() {
    for (size_t i = max_files_ + 1; i <= max_files_ + 10; ++i) {
        std::string filename = base_filename_ + "." + std::to_string(i);
        if (std::filesystem::exists(filename)) {
            std::filesystem::remove(filename);
        }
    }
}

void RotatingFileSink::open_file() {
    current_file_.open(get_current_filename(), std::ios::app);
    if (!current_file_.is_open()) {
        throw std::runtime_error("Failed to open rotating log file: " + get_current_filename());
    }
    current_size_ = std::filesystem::file_size(get_current_filename());
}

// DatabaseLogSink implementation
DatabaseLogSink::DatabaseLogSink(std::shared_ptr<void> connection, const std::string& table_name)
    : connection_(connection), table_name_(table_name) {
    // Don't create table in constructor - let the user call it explicitly
}

DatabaseLogSink::~DatabaseLogSink() {
    flush();
}

void DatabaseLogSink::create_table_if_not_exists() {
    // Create proper log table schema
    std::string create_table_sql = 
        "CREATE TABLE IF NOT EXISTS " + table_name_ + " ("
        "id SERIAL PRIMARY KEY,"
        "timestamp TIMESTAMP NOT NULL,"
        "level VARCHAR(10) NOT NULL,"
        "logger_name VARCHAR(100) NOT NULL,"
        "message TEXT NOT NULL,"
        "thread_id VARCHAR(50),"
        "job_id VARCHAR(100),"
        "component VARCHAR(100),"
        "operation VARCHAR(100),"
        "context JSONB,"
        "error_code VARCHAR(50),"
        "stack_trace TEXT,"
        "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,"
        "INDEX idx_timestamp (timestamp),"
        "INDEX idx_level (level),"
        "INDEX idx_logger_name (logger_name),"
        "INDEX idx_job_id (job_id)"
        ")";
    
    // This is a placeholder - actual implementation would depend on the database type
    // The connection_ shared_ptr should be cast to the appropriate database connection type
    // and then used to execute the SQL
    auto logger = Logger::get("database-sink");
    logger->info("Attempted to create log table: {}", table_name_);
}

void DatabaseLogSink::write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    buffer_.push_back(entry);
    
    if (buffer_.size() >= BUFFER_SIZE) {
        flush();
    }
}

void DatabaseLogSink::flush() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    for (const auto& entry : buffer_) {
        insert_log_entry(entry);
    }
    
    buffer_.clear();
}

void DatabaseLogSink::insert_log_entry(const LogEntry& entry) {
    // Build parameterized INSERT query
    std::string insert_sql = 
        "INSERT INTO " + table_name_ + " ("
        "timestamp, level, logger_name, message, thread_id, "
        "job_id, component, operation, context, error_code, stack_trace"
        ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    // Format timestamp as ISO 8601
    std::string timestamp_str = format_timestamp(entry.timestamp);
    std::string level_str = log_level_to_string(entry.level);
    
    // Convert context map to JSON string
    std::string context_json = "{}";
    if (!entry.context.empty()) {
        nlohmann::json j;
        for (const auto& [key, value] : entry.context) {
            // Handle different types in std::any
            if (value.type() == typeid(int)) {
                j[key] = std::any_cast<int>(value);
            } else if (value.type() == typeid(double)) {
                j[key] = std::any_cast<double>(value);
            } else if (value.type() == typeid(std::string)) {
                j[key] = std::any_cast<std::string>(value);
            } else if (value.type() == typeid(bool)) {
                j[key] = std::any_cast<bool>(value);
            } else {
                j[key] = "<unsupported type>";
            }
        }
        context_json = j.dump();
    }
    
    // This is a placeholder - actual implementation would:
    // 1. Cast connection_ to the appropriate database connection type
    // 2. Prepare the statement with the parameterized query
    // 3. Bind the parameters
    // 4. Execute the statement
    
    auto logger = Logger::get("database-sink");
    logger->debug("Would insert log entry into {}: {} [{}] {}: {}", 
                  table_name_, timestamp_str, level_str, entry.logger_name, entry.message);
}

std::string DatabaseLogSink::format_timestamp(const std::chrono::system_clock::time_point& timestamp) const {
    auto time_t = std::chrono::system_clock::to_time_t(timestamp);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

// GenericDatabaseLogSink implementation
GenericDatabaseLogSink::GenericDatabaseLogSink(std::shared_ptr<void> connection, const std::string& table_name) 
    : connection_(connection) {
    table_name_ = table_name;
}

GenericDatabaseLogSink::~GenericDatabaseLogSink() {
    flush();
}

void GenericDatabaseLogSink::create_table_if_not_exists() {
    std::string create_sql = 
        "CREATE TABLE IF NOT EXISTS " + table_name_ + " ("
        "id BIGINT PRIMARY KEY AUTO_INCREMENT,"
        "timestamp TIMESTAMP(3) NOT NULL,"
        "level VARCHAR(10) NOT NULL,"
        "logger_name VARCHAR(100) NOT NULL,"
        "message TEXT NOT NULL,"
        "thread_id VARCHAR(50),"
        "job_id VARCHAR(100),"
        "component VARCHAR(100),"
        "operation VARCHAR(100),"
        "context JSON,"
        "error_code VARCHAR(50),"
        "stack_trace TEXT,"
        "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
        ")";
    
    execute_sql(create_sql);
    
    // Create indexes
    execute_sql("CREATE INDEX IF NOT EXISTS idx_" + table_name_ + "_timestamp ON " + table_name_ + "(timestamp)");
    execute_sql("CREATE INDEX IF NOT EXISTS idx_" + table_name_ + "_level ON " + table_name_ + "(level)");
    execute_sql("CREATE INDEX IF NOT EXISTS idx_" + table_name_ + "_logger ON " + table_name_ + "(logger_name)");
    execute_sql("CREATE INDEX IF NOT EXISTS idx_" + table_name_ + "_job ON " + table_name_ + "(job_id)");
}

void GenericDatabaseLogSink::write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    buffer_.push_back(entry);
    
    if (buffer_.size() >= BUFFER_SIZE) {
        flush();
    }
}

void GenericDatabaseLogSink::flush() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    if (buffer_.empty()) {
        return;
    }
    
    // Batch insert for better performance
    std::stringstream sql;
    sql << "INSERT INTO " << table_name_ << " ("
        << "timestamp, level, logger_name, message, thread_id, "
        << "job_id, component, operation, context, error_code, stack_trace"
        << ") VALUES ";
    
    bool first = true;
    for (const auto& entry : buffer_) {
        if (!first) sql << ", ";
        first = false;
        
        // Format values - in real implementation, use parameterized queries
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        std::stringstream timestamp_ss;
        timestamp_ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%d %H:%M:%S");
        
        sql << "('" << timestamp_ss.str() << "', ";
        sql << "'" << log_level_to_string(entry.level) << "', ";
        sql << "'" << entry.logger_name << "', ";
        sql << "'" << entry.message << "', ";
        sql << "'" << entry.thread_id << "', ";
        sql << "'" << entry.job_id << "', ";
        sql << "'" << entry.component << "', ";
        sql << "'" << entry.operation << "', ";
        
        // Convert context to JSON
        if (!entry.context.empty()) {
            nlohmann::json j;
            for (const auto& [key, value] : entry.context) {
                // Handle std::any conversion
                if (value.type() == typeid(int)) {
                    j[key] = std::any_cast<int>(value);
                } else if (value.type() == typeid(double)) {
                    j[key] = std::any_cast<double>(value);
                } else if (value.type() == typeid(std::string)) {
                    j[key] = std::any_cast<std::string>(value);
                } else if (value.type() == typeid(bool)) {
                    j[key] = std::any_cast<bool>(value);
                }
            }
            sql << "'" << j.dump() << "', ";
        } else {
            sql << "NULL, ";
        }
        
        sql << (entry.error_code.has_value() ? "'" + entry.error_code.value() + "'" : "NULL") << ", ";
        sql << (entry.stack_trace.has_value() ? "'" + entry.stack_trace.value() + "'" : "NULL");
        sql << ")";
    }
    
    execute_sql(sql.str());
    buffer_.clear();
}

void GenericDatabaseLogSink::execute_sql(const std::string& sql) {
    // This is where the actual database execution would happen
    // The connection_ would be cast to the appropriate type and used
    auto logger = Logger::get("database-sink");
    logger->debug("Executing SQL on table {}: {}", table_name_, 
                 sql.length() > 100 ? sql.substr(0, 100) + "..." : sql);
}

// Helper function already defined above

} // namespace omop::common