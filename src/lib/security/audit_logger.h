#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <chrono>
#include <optional>
#include <deque>
#include <fstream>
#include <mutex>
#include <thread>
#include <condition_variable>
#include "common/logging.h"

namespace omop::security {

/**
 * @brief Audit event type enumeration
 */
enum class AuditEventType {
    Authentication,
    Authorization,
    DataAccess,
    DataModification,
    SystemAccess,
    ConfigChange,
    UserManagement,
    SecurityViolation,
    ServiceStart,
    ServiceStop,
    JobStart,
    JobComplete,
    JobFailed,
    Error,
    Warning,
    Information
};

/**
 * @brief Audit event severity enumeration
 */
enum class AuditSeverity {
    Low,
    Medium,
    High,
    Critical
};

/**
 * @brief Audit event outcome enumeration
 */
enum class AuditOutcome {
    Success,
    Failure,
    Unknown
};

/**
 * @brief Audit event structure
 */
struct AuditEvent {
    std::string id;
    std::chrono::system_clock::time_point timestamp;
    AuditEventType event_type;
    AuditSeverity severity;
    AuditOutcome outcome;
    std::string subject;
    std::string resource;
    std::string action;
    std::string description;
    std::string source_ip;
    std::string user_agent;
    std::string session_id;
    std::string request_id;
    std::unordered_map<std::string, std::any> context;
    std::unordered_map<std::string, std::any> additional_data;
};

/**
 * @brief Audit query structure
 */
struct AuditQuery {
    std::optional<std::chrono::system_clock::time_point> start_time;
    std::optional<std::chrono::system_clock::time_point> end_time;
    std::optional<AuditEventType> event_type;
    std::optional<AuditSeverity> severity;
    std::optional<AuditOutcome> outcome;
    std::optional<std::string> subject;
    std::optional<std::string> resource;
    std::optional<std::string> action;
    std::optional<std::string> session_id;
    std::optional<std::string> request_id;
    size_t limit{100};
    size_t offset{0};
    std::string sort_by{"timestamp"};
    bool sort_descending{true};
};

/**
 * @brief Audit configuration structure
 */
struct AuditConfig {
    bool enabled{true};
    std::vector<AuditEventType> logged_events;
    AuditSeverity min_severity{AuditSeverity::Low};
    std::string log_format{"json"};
    std::string log_destination{"file"};
    std::string log_file_path{"audit.log"};
    size_t max_file_size{100 * 1024 * 1024}; // 100MB
    size_t max_backup_files{10};
    std::chrono::seconds flush_interval{30};
    size_t buffer_size{1000};
    bool compress_backups{true};
    std::string encryption_key;
    std::string database_connection;
    std::string table_name{"audit_events"};
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Audit logger interface
 * 
 * This interface defines the contract for audit loggers that capture
 * security-related events and provide querying capabilities.
 */
class IAuditLogger {
public:
    virtual ~IAuditLogger() = default;

    /**
     * @brief Initialize audit logger
     * @param config Audit configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const AuditConfig& config) = 0;

    /**
     * @brief Log audit event
     * @param event Audit event to log
     * @return bool True if event logged successfully
     */
    virtual bool log_event(const AuditEvent& event) = 0;

    /**
     * @brief Log authentication event
     * @param subject Subject identifier
     * @param outcome Event outcome
     * @param source_ip Source IP address
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_authentication(
        const std::string& subject,
        AuditOutcome outcome,
        const std::string& source_ip = "",
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Log authorization event
     * @param subject Subject identifier
     * @param resource Resource identifier
     * @param action Action performed
     * @param outcome Event outcome
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_authorization(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Log data access event
     * @param subject Subject identifier
     * @param resource Resource identifier
     * @param action Action performed
     * @param outcome Event outcome
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_data_access(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Log data modification event
     * @param subject Subject identifier
     * @param resource Resource identifier
     * @param action Action performed
     * @param outcome Event outcome
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_data_modification(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Log system access event
     * @param subject Subject identifier
     * @param action Action performed
     * @param outcome Event outcome
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_system_access(
        const std::string& subject,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Log configuration change event
     * @param subject Subject identifier
     * @param resource Resource identifier
     * @param action Action performed
     * @param outcome Event outcome
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_config_change(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Log security violation event
     * @param subject Subject identifier
     * @param violation_type Type of violation
     * @param description Violation description
     * @param severity Violation severity
     * @param additional_data Additional event data
     * @return bool True if event logged successfully
     */
    virtual bool log_security_violation(
        const std::string& subject,
        const std::string& violation_type,
        const std::string& description,
        AuditSeverity severity = AuditSeverity::High,
        const std::unordered_map<std::string, std::any>& additional_data = {}) = 0;

    /**
     * @brief Query audit events
     * @param query Query parameters
     * @return std::vector<AuditEvent> List of matching events
     */
    virtual std::vector<AuditEvent> query_events(const AuditQuery& query) = 0;

    /**
     * @brief Get event count
     * @param query Query parameters
     * @return size_t Number of matching events
     */
    virtual size_t get_event_count(const AuditQuery& query) = 0;

    /**
     * @brief Get event by ID
     * @param event_id Event ID
     * @return std::optional<AuditEvent> Event if exists
     */
    virtual std::optional<AuditEvent> get_event(const std::string& event_id) = 0;

    /**
     * @brief Delete old events
     * @param older_than Delete events older than this timestamp
     * @return size_t Number of deleted events
     */
    virtual size_t delete_old_events(const std::chrono::system_clock::time_point& older_than) = 0;

    /**
     * @brief Archive old events
     * @param older_than Archive events older than this timestamp
     * @param archive_path Archive file path
     * @return size_t Number of archived events
     */
    virtual size_t archive_old_events(
        const std::chrono::system_clock::time_point& older_than,
        const std::string& archive_path) = 0;

    /**
     * @brief Flush buffered events
     * @return bool True if flush successful
     */
    virtual bool flush() = 0;

    /**
     * @brief Get audit statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Get audit configuration
     * @return AuditConfig Current configuration
     */
    virtual AuditConfig get_config() const = 0;

    /**
     * @brief Update audit configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const AuditConfig& config) = 0;
};

/**
 * @brief Default audit logger implementation
 */
class AuditLogger : public IAuditLogger {
public:
    AuditLogger();
    ~AuditLogger() override;

    bool initialize(const AuditConfig& config) override;
    bool log_event(const AuditEvent& event) override;

    bool log_authentication(
        const std::string& subject,
        AuditOutcome outcome,
        const std::string& source_ip = "",
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    bool log_authorization(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    bool log_data_access(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    bool log_data_modification(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    bool log_system_access(
        const std::string& subject,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    bool log_config_change(
        const std::string& subject,
        const std::string& resource,
        const std::string& action,
        AuditOutcome outcome,
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    bool log_security_violation(
        const std::string& subject,
        const std::string& violation_type,
        const std::string& description,
        AuditSeverity severity = AuditSeverity::High,
        const std::unordered_map<std::string, std::any>& additional_data = {}) override;

    std::vector<AuditEvent> query_events(const AuditQuery& query) override;
    size_t get_event_count(const AuditQuery& query) override;
    std::optional<AuditEvent> get_event(const std::string& event_id) override;

    size_t delete_old_events(const std::chrono::system_clock::time_point& older_than) override;
    size_t archive_old_events(
        const std::chrono::system_clock::time_point& older_than,
        const std::string& archive_path) override;

    bool flush() override;
    std::unordered_map<std::string, std::any> get_statistics() override;
    AuditConfig get_config() const override;
    bool update_config(const AuditConfig& config) override;

private:
    AuditConfig config_;
    bool initialized_ = false;
    std::shared_ptr<omop::common::Logger> logger_;
    std::ofstream log_file_;
    std::mutex log_mutex_;
    std::deque<AuditEvent> event_buffer_;
    std::thread background_thread_;
    std::condition_variable cv_;
    bool stop_thread_ = false;
    
    void flush_buffer();
    void start_background_thread();
    void stop_background_thread();
};

/**
 * @brief Create audit logger instance
 * @return std::unique_ptr<IAuditLogger> Audit logger instance
 */
std::unique_ptr<IAuditLogger> create_audit_logger();

/**
 * @brief Get default audit configuration
 * @return AuditConfig Default configuration
 */
AuditConfig get_default_audit_config();

/**
 * @brief Convert audit event type to string
 * @param event_type Event type enum
 * @return std::string Event type string
 */
std::string event_type_to_string(AuditEventType event_type);

/**
 * @brief Convert string to audit event type
 * @param event_type_str Event type string
 * @return AuditEventType Event type enum
 */
AuditEventType string_to_event_type(const std::string& event_type_str);

/**
 * @brief Convert audit severity to string
 * @param severity Severity enum
 * @return std::string Severity string
 */
std::string severity_to_string(AuditSeverity severity);

/**
 * @brief Convert string to audit severity
 * @param severity_str Severity string
 * @return AuditSeverity Severity enum
 */
AuditSeverity string_to_severity(const std::string& severity_str);

/**
 * @brief Convert audit outcome to string
 * @param outcome Outcome enum
 * @return std::string Outcome string
 */
std::string outcome_to_string(AuditOutcome outcome);

/**
 * @brief Convert string to audit outcome
 * @param outcome_str Outcome string
 * @return AuditOutcome Outcome enum
 */
AuditOutcome string_to_outcome(const std::string& outcome_str);

/**
 * @brief Generate unique event ID
 * @return std::string Unique event ID
 */
std::string generate_event_id();

} // namespace omop::security