#pragma once

#include <any>
#include <optional>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <functional>
#include <chrono>
#include "common/logging.h"

namespace omop::security {

// Add UserStatus enum before UserInfo
enum class UserStatus {
    Active,
    Locked,
    Expired
};

/**
 * @brief Authentication result enumeration
 */
enum class AuthResult {
    Success,
    InvalidCredentials,
    AccountLocked,
    AccountExpired,
    TokenExpired,
    TokenInvalid,
    MfaRequired,
    InternalError,
    SystemError,
    TokenRevoked,
    InvalidToken,
    UserNotFound,
    UserAlreadyExists,
    UserNotActive,
    UserNotAuthorized,
    UserNotAuthenticated
};

/**
 * @brief Authentication method enumeration
 */
enum class AuthMethod {
    Password,
    Token,
    ApiKey,
    Certificate,
    OAuth2,
    SAML,
    LDAP,
    Kerberos
};

/**
 * @brief User information structure
 */
struct UserInfo {
    std::string user_id;
    std::string username;
    std::string email;
    std::string display_name;
    std::vector<std::string> roles;
    std::vector<std::string> permissions;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point updated_at;
    std::chrono::system_clock::time_point last_login;
    std::chrono::system_clock::time_point expires_at;
    bool is_active{true};
    bool is_locked{false};
    std::unordered_map<std::string, std::any> metadata;
    UserStatus status{UserStatus::Active};
};

/**
 * @brief Authentication token structure
 */
struct AuthToken {
    std::string token;
    std::string token_type;
    std::chrono::system_clock::time_point issued_at;
    std::chrono::system_clock::time_point expires_at;
    std::string issuer;
    std::string subject;
    std::vector<std::string> scopes;
    std::vector<std::string> audience;
    std::unordered_map<std::string, std::any> claims;
};

/**
 * @brief Authentication credentials structure
 */
struct AuthCredentials {
    std::string username;
    std::string password;
    std::string token;
    std::string api_key;
    std::string certificate;
    AuthMethod method{AuthMethod::Password};
    std::unordered_map<std::string, std::any> additional_data;
};

/**
 * @brief Authentication configuration
 */
struct AuthConfig {
    std::chrono::seconds token_lifetime{3600};
    std::chrono::seconds refresh_token_lifetime{86400};
    size_t max_login_attempts{5};
    std::chrono::seconds lockout_duration{900};
    bool enable_mfa{false};
    bool enable_password_policy{true};
    std::string password_policy_pattern;
    std::vector<AuthMethod> enabled_methods;
    std::string ldap_server;
    std::string ldap_base_dn;
    std::string oauth2_client_id;
    std::string oauth2_client_secret;
    std::string oauth2_redirect_uri;
    std::unordered_map<std::string, std::any> additional_config;
    bool enabled{true};
    bool create_default_admin{true};
};

/**
 * @brief Authentication manager interface
 * 
 * This interface defines the contract for authentication managers that handle
 * user authentication, token management, and session control.
 */
class IAuthManager {
public:
    virtual ~IAuthManager() = default;

    /**
     * @brief Initialize authentication manager
     * @param config Authentication configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const AuthConfig& config) = 0;

    /**
     * @brief Authenticate user with credentials
     * @param credentials User credentials
     * @return std::pair<AuthResult, std::optional<AuthToken>> Result and token
     */
    virtual std::pair<AuthResult, std::optional<AuthToken>> authenticate(
        const AuthCredentials& credentials) = 0;

    /**
     * @brief Validate authentication token
     * @param token Authentication token
     * @return std::pair<AuthResult, std::optional<UserInfo>> Result and user info
     */
    virtual std::pair<AuthResult, std::optional<UserInfo>> validate_token(
        const std::string& token) = 0;

    /**
     * @brief Refresh authentication token
     * @param refresh_token Refresh token
     * @return std::pair<AuthResult, std::optional<AuthToken>> Result and new token
     */
    virtual std::pair<AuthResult, std::optional<AuthToken>> refresh_token(
        const std::string& refresh_token) = 0;

    /**
     * @brief Revoke authentication token
     * @param token Token to revoke
     * @return bool True if token revoked successfully
     */
    virtual bool revoke_token(const std::string& token) = 0;

    /**
     * @brief Get user information
     * @param user_id User ID
     * @return std::optional<UserInfo> User information if exists
     */
    virtual std::optional<UserInfo> get_user_info(const std::string& user_id) = 0;

    /**
     * @brief Create new user
     * @param user_info User information
     * @param password Initial password
     * @return bool True if user created successfully
     */
    virtual bool create_user(const UserInfo& user_info, const std::string& password) = 0;

    /**
     * @brief Update user information
     * @param user_info Updated user information
     * @return bool True if user updated successfully
     */
    virtual bool update_user(const UserInfo& user_info) = 0;

    /**
     * @brief Delete user
     * @param user_id User ID to delete
     * @return bool True if user deleted successfully
     */
    virtual bool delete_user(const std::string& user_id) = 0;

    /**
     * @brief Lock user account
     * @param user_id User ID to lock
     * @return bool True if account locked successfully
     */
    virtual bool lock_user(const std::string& user_id) = 0;

    /**
     * @brief Unlock user account
     * @param user_id User ID to unlock
     * @return bool True if account unlocked successfully
     */
    virtual bool unlock_user(const std::string& user_id) = 0;

    /**
     * @brief Change user password
     * @param user_id User ID
     * @param old_password Current password
     * @param new_password New password
     * @return bool True if password changed successfully
     */
    virtual bool change_password(
        const std::string& user_id,
        const std::string& old_password,
        const std::string& new_password) = 0;

    /**
     * @brief Reset user password
     * @param user_id User ID
     * @param new_password New password
     * @return bool True if password reset successfully
     */
    virtual bool reset_password(const std::string& user_id, const std::string& new_password) = 0;

    /**
     * @brief Get active sessions for user
     * @param user_id User ID
     * @return std::vector<std::string> List of active session tokens
     */
    virtual std::vector<std::string> get_active_sessions(const std::string& user_id) = 0;

    /**
     * @brief Terminate user session
     * @param user_id User ID
     * @param session_token Session token to terminate
     * @return bool True if session terminated successfully
     */
    virtual bool terminate_session(const std::string& user_id, const std::string& session_token) = 0;

    /**
     * @brief Terminate all user sessions
     * @param user_id User ID
     * @return bool True if all sessions terminated successfully
     */
    virtual bool terminate_all_sessions(const std::string& user_id) = 0;

    /**
     * @brief Get authentication statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Get authentication configuration
     * @return AuthConfig Current configuration
     */
    virtual AuthConfig get_config() const = 0;

    /**
     * @brief Update authentication configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const AuthConfig& config) = 0;
};

/**
 * @brief Default authentication manager implementation
 */
class AuthManager : public IAuthManager {
public:
    AuthManager();
    ~AuthManager() override;

    bool initialize(const AuthConfig& config) override;
    std::pair<AuthResult, std::optional<AuthToken>> authenticate(
        const AuthCredentials& credentials) override;
    std::pair<AuthResult, std::optional<UserInfo>> validate_token(
        const std::string& token) override;
    std::pair<AuthResult, std::optional<AuthToken>> refresh_token(
        const std::string& refresh_token) override;
    bool revoke_token(const std::string& token) override;
    std::optional<UserInfo> get_user_info(const std::string& user_id) override;
    bool create_user(const UserInfo& user_info, const std::string& password) override;
    bool update_user(const UserInfo& user_info) override;
    bool delete_user(const std::string& user_id) override;
    bool lock_user(const std::string& user_id) override;
    bool unlock_user(const std::string& user_id) override;
    bool change_password(
        const std::string& user_id,
        const std::string& old_password,
        const std::string& new_password) override;
    bool reset_password(const std::string& user_id, const std::string& new_password) override;
    std::vector<std::string> get_active_sessions(const std::string& user_id) override;
    bool terminate_session(const std::string& user_id, const std::string& session_token) override;
    bool terminate_all_sessions(const std::string& user_id) override;
    std::unordered_map<std::string, std::any> get_statistics() override;
    AuthConfig get_config() const override;
    bool update_config(const AuthConfig& config) override;

private:
    AuthConfig config_;
    bool initialized_ = false;
    std::shared_ptr<omop::common::Logger> logger_;
    std::mutex auth_mutex_;
    // User management
    std::unordered_map<std::string, UserInfo> users_;
    std::unordered_map<std::string, std::string> password_hashes_;
    std::unordered_map<std::string, std::string> password_salts_;
    std::unordered_map<std::string, AuthToken> active_tokens_;
    std::unordered_map<std::string, std::vector<std::string>> user_sessions_;
    std::unordered_set<std::string> revoked_tokens_;
    void create_default_admin_user();
};

/**
 * @brief Create authentication manager instance
 * @return std::unique_ptr<IAuthManager> Authentication manager instance
 */
std::unique_ptr<IAuthManager> create_auth_manager();

/**
 * @brief Get default authentication configuration
 * @return AuthConfig Default configuration
 */
AuthConfig get_default_auth_config();

/**
 * @brief Hash password using secure algorithm
 * @param password Plain text password
 * @param salt Salt value
 * @return std::string Hashed password
 */
std::string hash_password(const std::string& password, const std::string& salt);

/**
 * @brief Verify password against hash
 * @param password Plain text password
 * @param hash Stored password hash
 * @param salt Salt value
 * @return bool True if password matches hash
 */
bool verify_password(const std::string& password, const std::string& hash, const std::string& salt);

/**
 * @brief Generate secure random salt
 * @return std::string Random salt
 */
std::string generate_salt();

/**
 * @brief Generate secure random token
 * @param length Token length
 * @return std::string Random token
 */
std::string generate_token(size_t length = 32);

} // namespace omop::security