#include "audit_logger.h"
#include "common/logging.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <filesystem>
#include <algorithm>
#include <random>
#include <mutex>
#include <deque>
#include <thread>
#include <condition_variable>
#include <regex>
#include <pwd.h>
#include <unistd.h>

namespace omop::security {

namespace {
    // Helper function to get current user
    std::string get_current_user() {
#ifdef _WIN32
        char username[256];
        DWORD username_len = sizeof(username);
        if (GetUserNameA(username, &username_len)) {
            return std::string(username);
        }
#else
        struct passwd* pw = getpwuid(getuid());
        if (pw) {
            return std::string(pw->pw_name);
        }
#endif
        return "unknown";
    }

    // Helper to format timestamp
    std::string format_timestamp(const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm;
#ifdef _WIN32
        localtime_s(&tm, &time_t);
#else
        localtime_r(&time_t, &tm);
#endif
        std::ostringstream oss;
        oss << std::put_time(&tm, "%d/%m/%Y %H:%M:%S");
        return oss.str();
    }

    // Helper to serialize event to JSON
    std::string serialize_event(const AuditEvent& event) {
        std::ostringstream json;
        json << "{"
             << "\"id\":\"" << event.id << "\","
             << "\"timestamp\":\"" << format_timestamp(event.timestamp) << "\","
             << "\"event_type\":\"" << event_type_to_string(event.event_type) << "\","
             << "\"severity\":\"" << severity_to_string(event.severity) << "\","
             << "\"outcome\":\"" << outcome_to_string(event.outcome) << "\","
             << "\"subject\":\"" << event.subject << "\","
             << "\"resource\":\"" << event.resource << "\","
             << "\"action\":\"" << event.action << "\","
             << "\"description\":\"" << event.description << "\","
             << "\"source_ip\":\"" << event.source_ip << "\","
             << "\"user_agent\":\"" << event.user_agent << "\","
             << "\"session_id\":\"" << event.session_id << "\","
             << "\"request_id\":\"" << event.request_id << "\"";
        
        // Add context if present
        if (!event.context.empty()) {
            json << ",\"context\":{";
            bool first = true;
            for (const auto& [key, value] : event.context) {
                if (!first) json << ",";
                json << "\"" << key << "\":\"";
                // Simple any_cast to string - in production would need proper type handling
                try {
                    json << std::any_cast<std::string>(value);
                } catch(...) {
                    json << "complex_value";
                }
                json << "\"";
                first = false;
            }
            json << "}";
        }
        
        json << "}";
        return json.str();
    }

    // Helper to escape JSON strings
    std::string escape_json_string(const std::string& str) {
        std::string result;
        result.reserve(str.length());
        
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\b': result += "\\b"; break;
                case '\f': result += "\\f"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default:
                    if (c < 32) {
                        std::ostringstream oss;
                        oss << "\\u" << std::hex << std::setw(4) << std::setfill('0') << static_cast<int>(c);
                        result += oss.str();
                    } else {
                        result += c;
                    }
                    break;
            }
        }
        return result;
    }

    // Helper to generate unique event ID
    std::string generate_unique_event_id() {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> dis(0, 999999);
        
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();
        
        std::ostringstream oss;
        oss << "audit_" << timestamp << "_" << dis(gen);
        return oss.str();
    }

    // Helper to rotate log file
    bool rotate_log_file(const std::string& log_path, size_t max_size) {
        try {
            if (!std::filesystem::exists(log_path)) {
                return true;
            }
            
            auto file_size = std::filesystem::file_size(log_path);
            if (file_size < max_size) {
                return true;
            }
            
            // Create backup filename with timestamp
            auto now = std::chrono::system_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
                now.time_since_epoch()).count();
            
            std::filesystem::path log_file(log_path);
            std::filesystem::path backup_path = log_file.parent_path() / 
                (log_file.stem().string() + "_" + std::to_string(timestamp) + 
                 log_file.extension().string());
            
            std::filesystem::rename(log_path, backup_path);
            return true;
        } catch (const std::exception& e) {
            auto logger = omop::common::Logger::get("audit_logger");
            logger->error("Failed to rotate log file: {}", e.what());
            return false;
        }
    }
}

AuditLogger::AuditLogger() = default;

AuditLogger::~AuditLogger() {
    stop_background_thread();
}

bool AuditLogger::initialize(const AuditConfig& config) {
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    config_ = config;
    
    if (!config_.enabled) {
        initialized_ = true;
        return true;
    }

    try {
        // Create log directory if it doesn't exist
        std::filesystem::path log_path(config_.log_file_path);
        std::filesystem::create_directories(log_path.parent_path());
        
        // Open log file
        log_file_.open(config_.log_file_path, std::ios::app);
        if (!log_file_.is_open()) {
            logger_ = omop::common::Logger::get("audit_logger");
            logger_->error("Failed to open audit log file: {}", config_.log_file_path);
            return false;
        }

        // Initialize logger
        logger_ = omop::common::Logger::get("audit_logger");
        logger_->info("AuditLogger initialized with file: {}", config_.log_file_path);
        
        // Start background thread for flushing
        start_background_thread();

        initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        logger_ = omop::common::Logger::get("audit_logger");
        logger_->error("Failed to initialize AuditLogger: {}", e.what());
        return false;
    }
}

bool AuditLogger::log_event(const AuditEvent& event) {
    if (!initialized_ || !config_.enabled) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(log_mutex_);

    try {
        // Add event to buffer
        event_buffer_.push_back(event);
        
        // Flush immediately if buffer is full
        if (event_buffer_.size() >= config_.buffer_size) {
            flush_buffer();
        }
        
        return true;
    } catch (const std::exception& e) {
        logger_->error("Failed to log audit event: {}", e.what());
        return false;
    }
}

void AuditLogger::flush_buffer() {
    if (event_buffer_.empty() || !log_file_.is_open()) {
        return;
    }
    
    try {
        // Check if rotation is needed
        if (log_file_.tellp() > static_cast<std::streampos>(config_.max_file_size)) {
            log_file_.close();
            rotate_log_file(config_.log_file_path, config_.max_file_size);
            log_file_.open(config_.log_file_path, std::ios::app);
        }
        
        // Write events to file
        for (const auto& event : event_buffer_) {
            std::string json_event = serialize_event(event);
            log_file_ << json_event << std::endl;
        }
        
        log_file_.flush();
        event_buffer_.clear();
        
    } catch (const std::exception& e) {
        logger_->error("Failed to flush audit buffer: {}", e.what());
    }
}

void AuditLogger::start_background_thread() {
    stop_thread_ = false;
    background_thread_ = std::thread([this]() {
        while (!stop_thread_) {
            std::unique_lock<std::mutex> lock(log_mutex_);
            if (cv_.wait_for(lock, config_.flush_interval, [this]() { return stop_thread_; })) {
                break;
            }
            
            if (!event_buffer_.empty()) {
                flush_buffer();
            }
        }
    });
}

void AuditLogger::stop_background_thread() {
    {
        std::lock_guard<std::mutex> lock(log_mutex_);
        stop_thread_ = true;
    }
    cv_.notify_all();
    
    if (background_thread_.joinable()) {
        background_thread_.join();
    }
}

bool AuditLogger::log_authentication(
    const std::string& subject,
    AuditOutcome outcome,
    const std::string& source_ip,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::Authentication;
    event.severity = (outcome == AuditOutcome::Success) ? AuditSeverity::Low : AuditSeverity::High;
    event.outcome = outcome;
    event.subject = subject;
    event.action = "authentication";
    event.description = "Authentication event";
    event.source_ip = source_ip.empty() ? "127.0.0.1" : source_ip;
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}

bool AuditLogger::log_authorization(
    const std::string& subject,
    const std::string& resource,
    const std::string& action,
    AuditOutcome outcome,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::Authorization;
    event.severity = (outcome == AuditOutcome::Success) ? AuditSeverity::Low : AuditSeverity::High;
    event.outcome = outcome;
    event.subject = subject;
    event.resource = resource;
    event.action = action;
    event.description = "Authorization event";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}

bool AuditLogger::log_data_access(
    const std::string& subject,
    const std::string& resource,
    const std::string& action,
    AuditOutcome outcome,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::DataAccess;
    event.severity = AuditSeverity::Medium;
    event.outcome = outcome;
    event.subject = subject;
    event.resource = resource;
    event.action = action;
    event.description = "Data access event";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}

bool AuditLogger::log_data_modification(
    const std::string& subject,
    const std::string& resource,
    const std::string& action,
    AuditOutcome outcome,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::DataModification;
    event.severity = AuditSeverity::High;
    event.outcome = outcome;
    event.subject = subject;
    event.resource = resource;
    event.action = action;
    event.description = "Data modification event";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}

bool AuditLogger::log_system_access(
    const std::string& subject,
    const std::string& action,
    AuditOutcome outcome,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::SystemAccess;
    event.severity = AuditSeverity::Medium;
    event.outcome = outcome;
    event.subject = subject;
    event.resource = "system";
    event.action = action;
    event.description = "System access event";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}

bool AuditLogger::log_security_violation(
    const std::string& subject,
    const std::string& violation_type,
    const std::string& description,
    AuditSeverity severity,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::SecurityViolation;
    event.severity = severity;
    event.outcome = AuditOutcome::Failure;
    event.subject = subject;
    event.resource = "security";
    event.action = violation_type;
    event.description = description;
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}

bool AuditLogger::log_config_change(
    const std::string& subject,
    const std::string& resource,
    const std::string& action,
    AuditOutcome outcome,
    const std::unordered_map<std::string, std::any>& additional_data) {
    
    AuditEvent event;
    event.id = generate_unique_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::ConfigChange;
    event.severity = AuditSeverity::Medium;
    event.outcome = outcome;
    event.subject = subject;
    event.resource = resource;
    event.action = action;
    event.description = "Configuration change event";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    // Add additional data to event
    for (const auto& [key, value] : additional_data) {
        event.additional_data[key] = value;
    }
    
    return log_event(event);
}




std::vector<AuditEvent> AuditLogger::query_events(const AuditQuery& query) {
    std::vector<AuditEvent> events;
    // Simplified implementation - would need proper query engine in production
    return events;
}

size_t AuditLogger::get_event_count(const AuditQuery& query) {
    // Simplified implementation
    return 0;
}

std::optional<AuditEvent> AuditLogger::get_event(const std::string& event_id) {
    // Simplified implementation - would need proper indexing in production
    return std::nullopt;
}

size_t AuditLogger::delete_old_events(const std::chrono::system_clock::time_point& older_than) {
    // Simplified implementation - would delete events older than specified time
    return 0;
}

size_t AuditLogger::archive_old_events(
    const std::chrono::system_clock::time_point& older_than,
    const std::string& archive_path) {
    // Simplified implementation - would archive events to specified path
    return 0;
}

bool AuditLogger::flush() {
    std::lock_guard<std::mutex> lock(log_mutex_);
    flush_buffer();
    return true;
}

std::unordered_map<std::string, std::any> AuditLogger::get_statistics() {
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    std::unordered_map<std::string, std::any> stats;
    stats["total_events"] = static_cast<int>(event_buffer_.size());
    stats["buffer_size"] = static_cast<int>(config_.buffer_size);
    stats["max_file_size"] = static_cast<int>(config_.max_file_size);
    stats["flush_interval_ms"] = static_cast<int>(
        std::chrono::duration_cast<std::chrono::milliseconds>(config_.flush_interval).count());
    
    return stats;
}

AuditConfig AuditLogger::get_config() const {
    return config_;
}

bool AuditLogger::update_config(const AuditConfig& config) {
    return initialize(config);
}

// Utility functions
std::unique_ptr<IAuditLogger> create_audit_logger() {
    return std::make_unique<AuditLogger>();
}

AuditConfig get_default_audit_config() {
    AuditConfig config;
    config.enabled = true;
    config.logged_events = {
        AuditEventType::Authentication,
        AuditEventType::Authorization,
        AuditEventType::DataAccess,
        AuditEventType::DataModification,
        AuditEventType::SecurityViolation
    };
    config.min_severity = AuditSeverity::Low;
    config.log_format = "json";
    config.log_destination = "file";
    config.log_file_path = "audit.log";
    config.max_file_size = 100 * 1024 * 1024; // 100MB
    config.max_backup_files = 10;
    config.flush_interval = std::chrono::seconds(30);
    config.buffer_size = 1000;
    config.compress_backups = true;
    return config;
}

std::string event_type_to_string(AuditEventType event_type) {
    switch (event_type) {
        case AuditEventType::Authentication: return "Authentication";
        case AuditEventType::Authorization: return "Authorization";
        case AuditEventType::DataAccess: return "DataAccess";
        case AuditEventType::DataModification: return "DataModification";
        case AuditEventType::SystemAccess: return "SystemAccess";
        case AuditEventType::ConfigChange: return "ConfigChange";
        case AuditEventType::UserManagement: return "UserManagement";
        case AuditEventType::SecurityViolation: return "SecurityViolation";
        case AuditEventType::ServiceStart: return "ServiceStart";
        case AuditEventType::ServiceStop: return "ServiceStop";
        case AuditEventType::JobStart: return "JobStart";
        case AuditEventType::JobComplete: return "JobComplete";
        case AuditEventType::JobFailed: return "JobFailed";
        case AuditEventType::Error: return "Error";
        case AuditEventType::Warning: return "Warning";
        case AuditEventType::Information: return "Information";
        default: return "Unknown";
    }
}

AuditEventType string_to_event_type(const std::string& event_type_str) {
    if (event_type_str == "Authentication") return AuditEventType::Authentication;
    if (event_type_str == "Authorization") return AuditEventType::Authorization;
    if (event_type_str == "DataAccess") return AuditEventType::DataAccess;
    if (event_type_str == "DataModification") return AuditEventType::DataModification;
    if (event_type_str == "SystemAccess") return AuditEventType::SystemAccess;
    if (event_type_str == "ConfigChange") return AuditEventType::ConfigChange;
    if (event_type_str == "UserManagement") return AuditEventType::UserManagement;
    if (event_type_str == "SecurityViolation") return AuditEventType::SecurityViolation;
    if (event_type_str == "ServiceStart") return AuditEventType::ServiceStart;
    if (event_type_str == "ServiceStop") return AuditEventType::ServiceStop;
    if (event_type_str == "JobStart") return AuditEventType::JobStart;
    if (event_type_str == "JobComplete") return AuditEventType::JobComplete;
    if (event_type_str == "JobFailed") return AuditEventType::JobFailed;
    if (event_type_str == "Error") return AuditEventType::Error;
    if (event_type_str == "Warning") return AuditEventType::Warning;
    if (event_type_str == "Information") return AuditEventType::Information;
    return AuditEventType::Information; // Default fallback
}

std::string severity_to_string(AuditSeverity severity) {
    switch (severity) {
        case AuditSeverity::Low: return "Low";
        case AuditSeverity::Medium: return "Medium";
        case AuditSeverity::High: return "High";
        case AuditSeverity::Critical: return "Critical";
        default: return "Unknown";
    }
}

AuditSeverity string_to_severity(const std::string& severity_str) {
    if (severity_str == "Low") return AuditSeverity::Low;
    if (severity_str == "Medium") return AuditSeverity::Medium;
    if (severity_str == "High") return AuditSeverity::High;
    if (severity_str == "Critical") return AuditSeverity::Critical;
    return AuditSeverity::Low; // Default fallback
}

std::string outcome_to_string(AuditOutcome outcome) {
    switch (outcome) {
        case AuditOutcome::Success: return "Success";
        case AuditOutcome::Failure: return "Failure";
        case AuditOutcome::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

AuditOutcome string_to_outcome(const std::string& outcome_str) {
    if (outcome_str == "Success") return AuditOutcome::Success;
    if (outcome_str == "Failure") return AuditOutcome::Failure;
    if (outcome_str == "Unknown") return AuditOutcome::Unknown;
    return AuditOutcome::Unknown; // Default fallback
}

std::string generate_event_id() {
    return generate_unique_event_id();
}

} // namespace omop::security