# Security library CMakeLists.txt

set(SECURITY_SOURCES
    auth_manager.cpp
    authorization.cpp
    audit_logger.cpp
)

set(SECURITY_HEADERS
    auth_manager.h
    authorization.h
    audit_logger.h
)

add_library(omop_security ${SECURITY_SOURCES})

target_include_directories(omop_security
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include/omop>
)

target_link_libraries(omop_security
    PUBLIC
        omop_common
        spdlog::spdlog
        Threads::Threads
    PRIVATE
        $<$<PLATFORM_ID:Windows>:bcrypt>
        $<$<NOT:$<PLATFORM_ID:Windows>>:OpenSSL::Crypto>
)

target_compile_features(omop_security PUBLIC cxx_std_20)

install(TARGETS omop_security
    EXPORT omop-targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)