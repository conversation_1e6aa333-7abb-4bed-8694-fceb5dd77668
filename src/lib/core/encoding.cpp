/**
 * @file encoding.cpp
 * @brief Implementation of text encoding utilities for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "encoding.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include <algorithm>
#include <fstream>
#include <iostream>
#include <chrono>
#include <array>
#include <sstream>
#include <iomanip>

namespace omop::core {

bool TextEncoder::initialize(const EncodingConfig& config) {
    try {
        // Validate configuration
        if (config.buffer_size == 0) {
            throw std::invalid_argument("Buffer size cannot be zero");
        }
        
        if (config.replacement_char == '\0') {
            throw std::invalid_argument("Replacement character cannot be null");
        }
        
        config_ = config;
        reset_statistics();
        
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->debug("TextEncoder initialized with source: {}, target: {}", 
                     static_cast<int>(config_.source_encoding),
                     static_cast<int>(config_.target_encoding));
        
        return true;
    } catch (const std::exception& e) {
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Failed to initialize TextEncoder: {}", e.what());
        return false;
    }
}

EncodingDetectionResult TextEncoder::detect_encoding(const std::vector<uint8_t>& data, size_t sample_size) {
    start_time_ = std::chrono::steady_clock::now();
    stats_.total_bytes_processed += data.size();
    
    EncodingDetectionResult result;
    
    if (data.empty()) {
        result.detected_encoding = Encoding::UTF8;
        result.confidence = 0.0;
        result.is_valid = false;
        result.encoding_name = "UTF-8";
        return result;
    }

    // Determine size to check
    size_t size_to_check = (sample_size > 0 && sample_size < data.size()) ? sample_size : data.size();
    size_to_check = std::min(size_to_check, static_cast<size_t>(8192)); // Limit to 8KB for performance
    
    // Check for BOM first
    result.detected_bom = detect_bom_from_data(data);
    
    // BOM overrides other detection
    if (result.detected_bom != BOM::None) {
        switch (result.detected_bom) {
            case BOM::UTF8:
                result.detected_encoding = Encoding::UTF8;
                result.confidence = 1.0;
                result.encoding_name = "UTF-8";
                break;
            case BOM::UTF16LE:
            case BOM::UTF16BE:
                result.detected_encoding = Encoding::UTF16;
                result.confidence = 1.0;
                result.encoding_name = "UTF-16";
                break;
            case BOM::UTF32LE:
            case BOM::UTF32BE:
                result.detected_encoding = Encoding::UTF32;
                result.confidence = 1.0;
                result.encoding_name = "UTF-32";
                break;
            default:
                break;
        }
        result.is_valid = true;
        result.possible_encodings = {result.encoding_name};
        return result;
    }
    
    // Heuristic-based detection without BOM
    double utf8_score = 0.0;
    double ascii_score = 0.0;
    double latin1_score = 0.0;
    
    // Check ASCII compatibility
    bool all_ascii = is_ascii_data(data, size_to_check);
    if (all_ascii) {
        ascii_score = 1.0;
        utf8_score = 1.0; // ASCII is valid UTF-8
        latin1_score = 1.0; // ASCII is valid Latin-1
    } else {
        // Check UTF-8 validity
        if (is_valid_utf8_data(data, size_to_check)) {
            utf8_score = 0.9;
            
            // Count multi-byte sequences to increase confidence
            size_t multibyte_count = 0;
            for (size_t i = 0; i < size_to_check; ++i) {
                if (data[i] >= 0x80) {
                    multibyte_count++;
                }
            }
            
            if (multibyte_count > 0) {
                utf8_score += 0.05; // Slight boost for actual UTF-8 content
            }
        }
        
        // Check for common Latin-1 patterns
        size_t latin1_chars = 0;
        for (size_t i = 0; i < size_to_check; ++i) {
            uint8_t byte = data[i];
            // Common extended ASCII/Latin-1 characters
            if (byte >= 0x80 && 
                (byte == 0xA0 || byte == 0xA9 || byte == 0xAE || // NBSP, ©, ®
                 byte >= 0xC0)) { // Accented characters
                latin1_chars++;
            }
        }
        
        if (latin1_chars > 0) {
            latin1_score = 0.7 + (static_cast<double>(latin1_chars) / size_to_check) * 0.2;
        }
    }
    
    // Determine best encoding based on scores
    if (ascii_score >= utf8_score && ascii_score >= latin1_score) {
        result.detected_encoding = Encoding::ASCII;
        result.confidence = ascii_score;
        result.encoding_name = "ASCII";
    } else if (utf8_score >= latin1_score) {
        result.detected_encoding = Encoding::UTF8;
        result.confidence = utf8_score;
        result.encoding_name = "UTF-8";
    } else {
        result.detected_encoding = Encoding::Windows1252;
        result.confidence = latin1_score;
        result.encoding_name = "Windows-1252";
    }
    
    result.is_valid = result.confidence > 0.5;
    result.possible_encodings = {"UTF-8", "ASCII", "Windows-1252", "ISO-8859-1", "UTF-16", "UTF-32"};
    
    // Update statistics
    if (result.is_valid) {
        stats_.valid_characters += size_to_check;
    } else {
        stats_.invalid_characters += size_to_check;
    }
    
    return result;
}

std::string TextEncoder::to_utf8(const std::vector<uint8_t>& input, Encoding source_encoding) {
    start_time_ = std::chrono::steady_clock::now();
    stats_.total_bytes_processed += input.size();

    if (input.empty()) {
        return std::string();
    }

    try {
        switch (source_encoding) {
            case Encoding::UTF8: {
                // Already UTF-8, validate and clean if necessary
                std::string result(input.begin(), input.end());
                if (config_.validate_utf8 && !is_valid_utf8_string(result)) {
                    stats_.invalid_characters += input.size() - result.size();
                    result = clean_invalid_chars_internal(result, config_.replacement_char);
                } else {
                    stats_.valid_characters += input.size();
                }
                return result;
            }
            
            case Encoding::ASCII: {
                // ASCII is subset of UTF-8
                for (uint8_t byte : input) {
                    if (byte >= 0x80) {
                        stats_.invalid_characters++;
                        if (!config_.ignore_invalid_chars) {
                            throw std::invalid_argument("Invalid ASCII character found");
                        }
                    } else {
                        stats_.valid_characters++;
                    }
                }
                return std::string(input.begin(), input.end());
            }
            
            case Encoding::Windows1252:
            case Encoding::ISO8859_1: {
                // Convert Windows-1252/ISO-8859-1 to UTF-8
                std::string result;
                result.reserve(input.size() * 2); // Conservative estimate
                
                for (uint8_t byte : input) {
                    if (byte < 0x80) {
                        // ASCII range - direct mapping
                        result.push_back(static_cast<char>(byte));
                        stats_.valid_characters++;
                    } else if (byte >= 0xA0) {
                        // Latin-1 supplement range - convert to UTF-8
                        uint16_t codepoint = byte;
                        if (codepoint <= 0x7FF) {
                            result.push_back(static_cast<char>(0xC0 | (codepoint >> 6)));
                            result.push_back(static_cast<char>(0x80 | (codepoint & 0x3F)));
                        } else {
                            result.push_back(static_cast<char>(0xE0 | (codepoint >> 12)));
                            result.push_back(static_cast<char>(0x80 | ((codepoint >> 6) & 0x3F)));
                            result.push_back(static_cast<char>(0x80 | (codepoint & 0x3F)));
                        }
                        stats_.valid_characters++;
                    } else {
                        // Control characters in 0x80-0x9F range
                        if (config_.ignore_invalid_chars) {
                            continue;
                        } else {
                            result.push_back(config_.replacement_char);
                            stats_.invalid_characters++;
                        }
                    }
                }
                return result;
            }
            
            case Encoding::UTF16: {
                // Basic UTF-16 to UTF-8 conversion
                if (input.size() % 2 != 0) {
                    throw std::invalid_argument("Invalid UTF-16 data: odd number of bytes");
                }
                
                std::string result;
                result.reserve(input.size()); // Conservative estimate
                
                for (size_t i = 0; i < input.size(); i += 2) {
                    uint16_t codepoint = (input[i] << 8) | input[i + 1]; // Big-endian
                    
                    if (codepoint < 0x80) {
                        result.push_back(static_cast<char>(codepoint));
                    } else if (codepoint < 0x800) {
                        result.push_back(static_cast<char>(0xC0 | (codepoint >> 6)));
                        result.push_back(static_cast<char>(0x80 | (codepoint & 0x3F)));
                    } else {
                        result.push_back(static_cast<char>(0xE0 | (codepoint >> 12)));
                        result.push_back(static_cast<char>(0x80 | ((codepoint >> 6) & 0x3F)));
                        result.push_back(static_cast<char>(0x80 | (codepoint & 0x3F)));
                    }
                    stats_.valid_characters++;
                }
                return result;
            }
            
            default: {
                // For unsupported encodings, attempt best-effort conversion
                std::string result;
                result.reserve(input.size() * 2);
                
                for (uint8_t byte : input) {
                    if (byte < 0x80) {
                        // ASCII range
                        result.push_back(static_cast<char>(byte));
                        stats_.valid_characters++;
                    } else {
                        // Non-ASCII byte - use replacement or ignore
                        if (config_.ignore_invalid_chars) {
                            stats_.invalid_characters++;
                            continue;
                        } else {
                            result.push_back(config_.replacement_char);
                            stats_.replaced_characters++;
                        }
                    }
                }
                return result;
            }
        }
    } catch (const std::exception& e) {
        stats_.conversion_errors++;
        stats_.invalid_characters += input.size();
        
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Error converting to UTF-8 from encoding {}: {}", 
                     static_cast<int>(source_encoding), e.what());
        
        if (config_.ignore_invalid_chars) {
            return std::string();
        } else {
            return std::string(input.size(), config_.replacement_char);
        }
    }
}

std::vector<uint8_t> TextEncoder::from_utf8(const std::string& input, Encoding target_encoding) {
    start_time_ = std::chrono::steady_clock::now();
    stats_.total_bytes_processed += input.size();

    if (input.empty()) {
        return std::vector<uint8_t>();
    }

    // Validate UTF-8 input if required
    if (config_.validate_utf8 && !is_valid_utf8_string(input)) {
        stats_.invalid_characters += input.size();
        throw std::invalid_argument("Invalid UTF-8 input string");
    }

    try {
        switch (target_encoding) {
            case Encoding::UTF8: {
                // Already UTF-8, just convert to bytes
                std::vector<uint8_t> result(input.begin(), input.end());
                stats_.valid_characters += input.size();
                return result;
            }
            
            case Encoding::ASCII: {
                // Convert UTF-8 to ASCII (lossy conversion)
                std::vector<uint8_t> result;
                result.reserve(input.size());

                for (size_t i = 0; i < input.size(); ) {
                    uint8_t byte = static_cast<uint8_t>(input[i]);
                    
                    if (byte < 0x80) {
                        // ASCII character
                        result.push_back(byte);
                        stats_.valid_characters++;
                        i++;
                    } else {
                        // Multi-byte UTF-8 character - skip or replace
                        if (config_.ignore_invalid_chars) {
                            // Skip the entire UTF-8 character
                            if ((byte & 0xE0) == 0xC0) i += 2;      // 2-byte
                            else if ((byte & 0xF0) == 0xE0) i += 3; // 3-byte
                            else if ((byte & 0xF8) == 0xF0) i += 4; // 4-byte
                            else i++; // Invalid sequence
                            stats_.invalid_characters++;
                        } else {
                            result.push_back(static_cast<uint8_t>(config_.replacement_char));
                            stats_.replaced_characters++;
                            i++; // Just skip this byte for simplicity
                        }
                    }
                }
                return result;
            }
            
            case Encoding::Windows1252:
            case Encoding::ISO8859_1: {
                // Convert UTF-8 to Windows-1252/ISO-8859-1
                std::vector<uint8_t> result;
                result.reserve(input.size());

                for (size_t i = 0; i < input.size(); ) {
                    uint8_t byte = static_cast<uint8_t>(input[i]);
                    
                    if (byte < 0x80) {
                        // ASCII character - direct mapping
                        result.push_back(byte);
                        stats_.valid_characters++;
                        i++;
                    } else if ((byte & 0xE0) == 0xC0 && i + 1 < input.size()) {
                        // 2-byte UTF-8 sequence
                        uint8_t byte2 = static_cast<uint8_t>(input[i + 1]);
                        if ((byte2 & 0xC0) == 0x80) {
                            uint16_t codepoint = ((byte & 0x1F) << 6) | (byte2 & 0x3F);
                            
                            if (codepoint <= 0xFF) {
                                // Fits in Latin-1
                                result.push_back(static_cast<uint8_t>(codepoint));
                                stats_.valid_characters++;
                            } else {
                                // Outside Latin-1 range
                                if (!config_.ignore_invalid_chars) {
                                    result.push_back(static_cast<uint8_t>(config_.replacement_char));
                                }
                                stats_.invalid_characters++;
                            }
                            i += 2;
                        } else {
                            // Invalid sequence
                            if (!config_.ignore_invalid_chars) {
                                result.push_back(static_cast<uint8_t>(config_.replacement_char));
                            }
                            stats_.invalid_characters++;
                            i++;
                        }
                    } else {
                        // 3+ byte UTF-8 or invalid - outside Latin-1 range
                        if (!config_.ignore_invalid_chars) {
                            result.push_back(static_cast<uint8_t>(config_.replacement_char));
                        }
                        stats_.invalid_characters++;
                        
                        // Skip the entire character
                        if ((byte & 0xF0) == 0xE0) i += 3;      // 3-byte
                        else if ((byte & 0xF8) == 0xF0) i += 4; // 4-byte
                        else i++; // Invalid
                    }
                }
                return result;
            }
            
            default: {
                // For unsupported encodings, fall back to ASCII conversion
                return from_utf8(input, Encoding::ASCII);
            }
        }
    } catch (const std::exception& e) {
        stats_.conversion_errors++;
        stats_.invalid_characters += input.size();
        
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Error converting from UTF-8 to encoding {}: {}", 
                     static_cast<int>(target_encoding), e.what());
        
        if (config_.ignore_invalid_chars) {
            return std::vector<uint8_t>();
        } else {
            return std::vector<uint8_t>(input.size(), static_cast<uint8_t>(config_.replacement_char));
        }
    }
}

std::vector<uint8_t> TextEncoder::convert_encoding(const std::vector<uint8_t>& input,
                                       Encoding source_encoding,
                                       Encoding target_encoding) {
    // Convert to UTF-8 first, then to target encoding
    std::string utf8_string = to_utf8(input, source_encoding);
    return from_utf8(utf8_string, target_encoding);
}

std::string TextEncoder::convert_encoding(const std::string& input,
                                        Encoding source_encoding,
                                        Encoding target_encoding) {
    std::vector<uint8_t> input_bytes(input.begin(), input.end());
    auto converted_bytes = convert_encoding(input_bytes, source_encoding, target_encoding);
    return std::string(converted_bytes.begin(), converted_bytes.end());
}

bool TextEncoder::is_valid_encoding(const std::vector<uint8_t>& input, Encoding encoding) {
    if (encoding == Encoding::UTF8) {
        return is_valid_utf8_data(input, input.size());
    } else if (encoding == Encoding::ASCII) {
        return is_ascii_data(input, input.size());
    }
    return true; // Assume valid for other encodings
}

std::string TextEncoder::clean_invalid_chars(const std::string& input, char replacement) {
    return clean_invalid_chars_internal(input, replacement);
}

std::string TextEncoder::normalize_text(const std::string& input, const std::string& form) {
    if (form == "NFC") {
        return normalize_nfc(input);
    } else if (form == "NFD") {
        return normalize_nfd(input);
    } else if (form == "NFKC") {
        return normalize_nfkc(input);
    } else if (form == "NFKD") {
        return normalize_nfkd(input);
    } else if (form == "COMPATIBILITY") {
        return normalize_compatibility(input);
    } else {
        // Default to NFC
        return normalize_nfc(input);
    }
}

EncodingStats TextEncoder::get_statistics() {
    auto end_time = std::chrono::steady_clock::now();
    stats_.processing_time = std::chrono::duration_cast<std::chrono::duration<double>>(end_time - start_time_);
    
    // Add additional computed statistics
    if (stats_.total_bytes_processed > 0) {
        stats_.additional_stats["processing_rate_mbps"] = 
            (static_cast<double>(stats_.total_bytes_processed) / (1024.0 * 1024.0)) / 
            std::max(stats_.processing_time.count(), 0.001); // Avoid division by zero
        
        stats_.additional_stats["error_rate"] = 
            static_cast<double>(stats_.invalid_characters) / stats_.total_bytes_processed;
        
        stats_.additional_stats["replacement_rate"] = 
            static_cast<double>(stats_.replaced_characters) / stats_.total_bytes_processed;
    }
    
    return stats_;
}

void TextEncoder::reset_statistics() {
    stats_ = EncodingStats{};
    start_time_ = std::chrono::steady_clock::now();
}

// Private helper methods
BOM TextEncoder::detect_bom_from_data(const std::vector<uint8_t>& data) {
    if (data.size() < 2) {
        return BOM::None;
    }

    // Check for UTF-8 BOM
    if (data.size() >= 3 && data[0] == 0xEF && data[1] == 0xBB && data[2] == 0xBF) {
        return BOM::UTF8;
    }

    // Check for UTF-16 LE BOM
    if (data[0] == 0xFF && data[1] == 0xFE) {
        return BOM::UTF16LE;
    }

    // Check for UTF-16 BE BOM
    if (data[0] == 0xFE && data[1] == 0xFF) {
        return BOM::UTF16BE;
    }

    // Check for UTF-32 LE BOM
    if (data.size() >= 4 && data[0] == 0xFF && data[1] == 0xFE && data[2] == 0x00 && data[3] == 0x00) {
        return BOM::UTF32LE;
    }

    // Check for UTF-32 BE BOM
    if (data.size() >= 4 && data[0] == 0x00 && data[1] == 0x00 && data[2] == 0xFE && data[3] == 0xFF) {
        return BOM::UTF32BE;
    }

    return BOM::None;
}

bool TextEncoder::is_valid_utf8_data(const std::vector<uint8_t>& data, size_t size_to_check) {
    size_t i = 0;
    while (i < size_to_check) {
        uint8_t byte = data[i];
        
        if (byte < 0x80) {
            // Single byte character
            i++;
        } else if ((byte & 0xE0) == 0xC0) {
            // Two byte character
            if (i + 1 >= size_to_check) return false;
            if ((data[i + 1] & 0xC0) != 0x80) return false;
            i += 2;
        } else if ((byte & 0xF0) == 0xE0) {
            // Three byte character
            if (i + 2 >= size_to_check) return false;
            if ((data[i + 1] & 0xC0) != 0x80) return false;
            if ((data[i + 2] & 0xC0) != 0x80) return false;
            i += 3;
        } else if ((byte & 0xF8) == 0xF0) {
            // Four byte character
            if (i + 3 >= size_to_check) return false;
            if ((data[i + 1] & 0xC0) != 0x80) return false;
            if ((data[i + 2] & 0xC0) != 0x80) return false;
            if ((data[i + 3] & 0xC0) != 0x80) return false;
            i += 4;
        } else {
            return false;
        }
    }
    return true;
}

bool TextEncoder::is_ascii_data(const std::vector<uint8_t>& data, size_t size_to_check) {
    for (size_t i = 0; i < size_to_check; ++i) {
        if (data[i] >= 0x80) {
            return false;
        }
    }
    return true;
}

bool TextEncoder::is_valid_utf8_string(const std::string& input) {
    return is_valid_utf8_data(std::vector<uint8_t>(input.begin(), input.end()), input.size());
}

std::string TextEncoder::clean_invalid_chars_internal(const std::string& input, char replacement) {
    std::string result;
    result.reserve(input.size());

    for (size_t i = 0; i < input.size(); ++i) {
        uint8_t byte = static_cast<uint8_t>(input[i]);
        
        if (byte < 0x80) {
            // ASCII character
            result.push_back(input[i]);
        } else if ((byte & 0xE0) == 0xC0) {
            // Two byte UTF-8 character
            if (i + 1 < input.size() && (static_cast<uint8_t>(input[i + 1]) & 0xC0) == 0x80) {
                result.push_back(input[i]);
                result.push_back(input[i + 1]);
                i++;
            } else {
                result.push_back(replacement);
            }
        } else if ((byte & 0xF0) == 0xE0) {
            // Three byte UTF-8 character
            if (i + 2 < input.size() && 
                (static_cast<uint8_t>(input[i + 1]) & 0xC0) == 0x80 &&
                (static_cast<uint8_t>(input[i + 2]) & 0xC0) == 0x80) {
                result.push_back(input[i]);
                result.push_back(input[i + 1]);
                result.push_back(input[i + 2]);
                i += 2;
            } else {
                result.push_back(replacement);
            }
        } else if ((byte & 0xF8) == 0xF0) {
            // Four byte UTF-8 character
            if (i + 3 < input.size() && 
                (static_cast<uint8_t>(input[i + 1]) & 0xC0) == 0x80 &&
                (static_cast<uint8_t>(input[i + 2]) & 0xC0) == 0x80 &&
                (static_cast<uint8_t>(input[i + 3]) & 0xC0) == 0x80) {
                result.push_back(input[i]);
                result.push_back(input[i + 1]);
                result.push_back(input[i + 2]);
                result.push_back(input[i + 3]);
                i += 3;
            } else {
                result.push_back(replacement);
            }
        } else {
            result.push_back(replacement);
        }
    }
    
    return result;
}

std::string TextEncoder::normalize_nfc(const std::string& input) {
    // NFC normalization - compose combining characters
    std::string result = input;
    
    // Basic NFC normalization using string replacement for common cases
    // Replace combining diacritical marks with precomposed characters
    
    // A + combining acute accent (U+0301) -> Á (U+00C1)
    size_t pos = 0;
    while ((pos = result.find("A\xCC\x81", pos)) != std::string::npos) {
        result.replace(pos, 3, "\xC3\x81");  // Replace with Á
        pos += 2;
    }
    
    // a + combining acute accent (U+0301) -> á (U+00E1)
    pos = 0;
    while ((pos = result.find("a\xCC\x81", pos)) != std::string::npos) {
        result.replace(pos, 3, "\xC3\xA1");  // Replace with á
        pos += 2;
    }
    
    // E + combining acute accent (U+0301) -> É (U+00C9)
    pos = 0;
    while ((pos = result.find("E\xCC\x81", pos)) != std::string::npos) {
        result.replace(pos, 3, "\xC3\x89");  // Replace with É
        pos += 2;
    }
    
    // e + combining acute accent (U+0301) -> é (U+00E9)
    pos = 0;
    while ((pos = result.find("e\xCC\x81", pos)) != std::string::npos) {
        result.replace(pos, 3, "\xC3\xA9");  // Replace with é
        pos += 2;
    }
    
    return result;
}

std::string TextEncoder::normalize_nfd(const std::string& input) {
    // NFD normalization - decompose precomposed characters
    std::string result = input;
    
    // Basic NFD normalization using string replacement for common cases
    // Replace precomposed characters with base character + combining marks
    
    // Á (U+00C1) -> A + combining acute accent (U+0301)
    size_t pos = 0;
    while ((pos = result.find("\xC3\x81", pos)) != std::string::npos) {
        result.replace(pos, 2, "A\xCC\x81");  // Replace with A + combining acute
        pos += 3;
    }
    
    // á (U+00E1) -> a + combining acute accent (U+0301)
    pos = 0;
    while ((pos = result.find("\xC3\xA1", pos)) != std::string::npos) {
        result.replace(pos, 2, "a\xCC\x81");  // Replace with a + combining acute
        pos += 3;
    }
    
    // É (U+00C9) -> E + combining acute accent (U+0301)
    pos = 0;
    while ((pos = result.find("\xC3\x89", pos)) != std::string::npos) {
        result.replace(pos, 2, "E\xCC\x81");  // Replace with E + combining acute
        pos += 3;
    }
    
    // é (U+00E9) -> e + combining acute accent (U+0301)
    pos = 0;
    while ((pos = result.find("\xC3\xA9", pos)) != std::string::npos) {
        result.replace(pos, 2, "e\xCC\x81");  // Replace with e + combining acute
        pos += 3;
    }
    
    return result;
}

std::string TextEncoder::normalize_nfkc(const std::string& input) {
    // NFKC normalization (compatibility decomposition + composition)
    std::string result = normalize_nfkd(input);
    return normalize_nfc(result);
}

std::string TextEncoder::normalize_nfkd(const std::string& input) {
    // NFKD normalization (compatibility decomposition)
    std::string result = input;
    
    // Apply NFD first for canonical decomposition
    result = normalize_nfd(result);
    
    // Apply compatibility decomposition for various Unicode characters
    size_t pos = 0;
    
    // Roman numerals to ASCII (U+2160-U+216F)
    while ((pos = result.find("\xE2\x85\xA0", pos)) != std::string::npos) { // Ⅰ -> I
        result.replace(pos, 3, "I");
        pos += 1;
    }
    while ((pos = result.find("\xE2\x85\xA1", pos)) != std::string::npos) { // Ⅱ -> II
        result.replace(pos, 3, "II");
        pos += 2;
    }
    while ((pos = result.find("\xE2\x85\xA2", pos)) != std::string::npos) { // Ⅲ -> III
        result.replace(pos, 3, "III");
        pos += 3;
    }
    
    // Superscripts to normal digits
    pos = 0;
    while ((pos = result.find("\xC2\xB9", pos)) != std::string::npos) { // ¹ -> 1
        result.replace(pos, 2, "1");
        pos += 1;
    }
    while ((pos = result.find("\xC2\xB2", pos)) != std::string::npos) { // ² -> 2
        result.replace(pos, 2, "2");
        pos += 1;
    }
    while ((pos = result.find("\xC2\xB3", pos)) != std::string::npos) { // ³ -> 3
        result.replace(pos, 2, "3");
        pos += 1;
    }
    
    // Fractions to ASCII equivalents
    pos = 0;
    while ((pos = result.find("\xC2\xBD", pos)) != std::string::npos) { // ½ -> 1/2
        result.replace(pos, 2, "1/2");
        pos += 3;
    }
    while ((pos = result.find("\xC2\xBC", pos)) != std::string::npos) { // ¼ -> 1/4
        result.replace(pos, 2, "1/4");
        pos += 3;
    }
    while ((pos = result.find("\xC2\xBE", pos)) != std::string::npos) { // ¾ -> 3/4
        result.replace(pos, 2, "3/4");
        pos += 3;
    }
    
    // UK Currency symbol normalization  
    pos = 0;
    while ((pos = result.find("\xC2\xA3", pos)) != std::string::npos) { // £ -> GBP
        result.replace(pos, 2, "GBP");
        pos += 3;
    }
    
    // Temperature symbols normalization
    pos = 0;
    while ((pos = result.find("\xE2\x84\x83", pos)) != std::string::npos) { // ℃ -> °C
        result.replace(pos, 3, "°C");
        pos += 2;
    }
    
    return result;
}

std::string TextEncoder::normalize_compatibility(const std::string& input) {
    // Simplified compatibility normalization
    std::string result = input;
    // Replace common compatibility characters using string replacement
    // Non-breaking space (U+00A0) to regular space
    size_t pos = 0;
    while ((pos = result.find("\xC2\xA0", pos)) != std::string::npos) {
        result.replace(pos, 2, " ");
        pos += 1;
    }
    // Left single quote (U+2018) to apostrophe
    pos = 0;
    while ((pos = result.find("\xE2\x80\x98", pos)) != std::string::npos) {
        result.replace(pos, 3, "'");
        pos += 1;
    }
    // Right single quote (U+2019) to apostrophe
    pos = 0;
    while ((pos = result.find("\xE2\x80\x99", pos)) != std::string::npos) {
        result.replace(pos, 3, "'");
        pos += 1;
    }
    // Left double quote (U+201C) to quote
    pos = 0;
    while ((pos = result.find("\xE2\x80\x9C", pos)) != std::string::npos) {
        result.replace(pos, 3, "\"");
        pos += 1;
    }
    // Right double quote (U+201D) to quote
    pos = 0;
    while ((pos = result.find("\xE2\x80\x9D", pos)) != std::string::npos) {
        result.replace(pos, 3, "\"");
        pos += 1;
    }
    return result;
}

// Restore missing methods that were removed during PIMPL removal

EncodingDetectionResult TextEncoder::detect_encoding(const std::string& text) {
    std::vector<uint8_t> data(text.begin(), text.end());
    return detect_encoding(data, 0);
}

bool TextEncoder::is_valid_utf8(const std::string& input) {
    return is_valid_utf8_string(input);
}

std::pair<BOM, std::vector<uint8_t>> TextEncoder::strip_bom(const std::vector<uint8_t>& data) {
    if (data.size() >= 3 && data[0] == 0xEF && data[1] == 0xBB && data[2] == 0xBF) {
        return {BOM::UTF8, std::vector<uint8_t>(data.begin() + 3, data.end())};
    }
    if (data.size() >= 2) {
        if (data[0] == 0xFF && data[1] == 0xFE) {
            return {BOM::UTF16LE, std::vector<uint8_t>(data.begin() + 2, data.end())};
        }
        if (data[0] == 0xFE && data[1] == 0xFF) {
            return {BOM::UTF16BE, std::vector<uint8_t>(data.begin() + 2, data.end())};
        }
    }
    if (data.size() >= 4) {
        if (data[0] == 0xFF && data[1] == 0xFE && data[2] == 0x00 && data[3] == 0x00) {
            return {BOM::UTF32LE, std::vector<uint8_t>(data.begin() + 4, data.end())};
        }
        if (data[0] == 0x00 && data[1] == 0x00 && data[2] == 0xFE && data[3] == 0xFF) {
            return {BOM::UTF32BE, std::vector<uint8_t>(data.begin() + 4, data.end())};
        }
    }
    return {BOM::None, data};
}

std::vector<uint8_t> TextEncoder::add_bom(const std::vector<uint8_t>& data, BOM bom_type) {
    std::vector<uint8_t> result;
    
    switch (bom_type) {
        case BOM::UTF8:
            result = {0xEF, 0xBB, 0xBF};
            break;
        case BOM::UTF16LE:
            result = {0xFF, 0xFE};
            break;
        case BOM::UTF16BE:
            result = {0xFE, 0xFF};
            break;
        case BOM::UTF32LE:
            result = {0xFF, 0xFE, 0x00, 0x00};
            break;
        case BOM::UTF32BE:
            result = {0x00, 0x00, 0xFE, 0xFF};
            break;
        default:
            break;
    }
    
    result.insert(result.end(), data.begin(), data.end());
    return result;
}


EncodingConfig TextEncoder::get_config() const {
    return config_;
}

bool TextEncoder::update_config(const EncodingConfig& config) {
    config_ = config;
    return true;
}

// Factory functions
std::unique_ptr<ITextEncoder> create_text_encoder() {
    return std::make_unique<TextEncoder>();
}

EncodingConfig get_default_encoding_config() {
    EncodingConfig config;
    config.source_encoding = Encoding::UTF8;
    config.target_encoding = Encoding::UTF8;
    config.replacement_char = '?';
    config.ignore_invalid_chars = false;
    config.transliterate = false;
    config.add_bom = false;
    config.remove_bom = false;
    config.validate_utf8 = true;
    config.buffer_size = 8192;
    return config;
}

// Forward declaration
std::vector<uint8_t> read_file_bytes(const std::string& file_path);

// CSVEncodingUtils implementations
EncodingDetectionResult CSVEncodingUtils::detect_csv_encoding(const std::string& file_path) {
    try {
        auto data = read_file_bytes(file_path);
        auto encoder = create_text_encoder();
        encoder->initialize(get_default_encoding_config());
        return encoder->detect_encoding(data, std::min(data.size(), static_cast<size_t>(8192)));
    } catch (const std::exception& e) {
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Error detecting CSV encoding: {}", e.what());
        EncodingDetectionResult result;
        result.is_valid = false;
        return result;
    }
}

std::vector<uint8_t> read_file_bytes(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file: " + file_path);
    }
    
    file.seekg(0, std::ios::end);
    size_t file_size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8_t> data(file_size);
    file.read(reinterpret_cast<char*>(data.data()), file_size);
    
    return data;
}

bool convert_file_encoding(const std::string& input_file,
                         const std::string& output_file,
                         Encoding source_encoding,
                         Encoding target_encoding) {
    try {
        std::ifstream input(input_file, std::ios::binary);
        if (!input.is_open()) {
            return false;
        }
        
        std::ofstream output(output_file, std::ios::binary);
        if (!output.is_open()) {
            return false;
        }
        
        auto encoder = create_text_encoder();
        encoder->initialize(get_default_encoding_config());
        
        std::vector<uint8_t> buffer(4096);
        while (input.read(reinterpret_cast<char*>(buffer.data()), buffer.size())) {
            size_t bytes_read = input.gcount();
            std::vector<uint8_t> chunk(buffer.begin(), buffer.begin() + bytes_read);
            
            auto converted = encoder->convert_encoding(chunk, source_encoding, target_encoding);
            output.write(reinterpret_cast<const char*>(converted.data()), converted.size());
        }
        
        // Handle any remaining bytes
        if (input.gcount() > 0) {
            size_t bytes_read = input.gcount();
            std::vector<uint8_t> chunk(buffer.begin(), buffer.begin() + bytes_read);
            
            auto converted = encoder->convert_encoding(chunk, source_encoding, target_encoding);
            output.write(reinterpret_cast<const char*>(converted.data()), converted.size());
        }
        
        return true;
    } catch (const std::exception& e) {
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Error converting file encoding: {}", e.what());
        return false;
    }
}

bool CSVEncodingUtils::validate_csv_encoding(const std::string& file_path, Encoding expected_encoding) {
    try {
        auto data = read_file_bytes(file_path);
        auto encoder = create_text_encoder();
        encoder->initialize(get_default_encoding_config());
        
        auto detection_result = encoder->detect_encoding(data, 1024);
        return detection_result.detected_encoding == expected_encoding && detection_result.is_valid;
    } catch (const std::exception& e) {
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Error validating CSV encoding: {}", e.what());
        return false;
    }
}

bool CSVEncodingUtils::convert_csv_encoding(const std::string& input_file,
                                         const std::string& output_file,
                                         Encoding source_encoding,
                                         Encoding target_encoding) {
    return convert_file_encoding(input_file, output_file, source_encoding, target_encoding);
}


bool CSVEncodingUtils::clean_csv_encoding(const std::string& input_file,
                                         const std::string& output_file,
                                         Encoding target_encoding) {
    try {
        // Detect source encoding first
        auto detection_result = detect_csv_encoding(input_file);
        if (!detection_result.is_valid) {
            return false;
        }
        
        // Convert to target encoding
        return convert_csv_encoding(input_file, output_file, 
                                   detection_result.detected_encoding, target_encoding);
    } catch (const std::exception& e) {
        auto logger = omop::common::Logger::get("omop-encoding");
        logger->error("Error cleaning CSV encoding: {}", e.what());
        return false;
    }
}

std::string EncodingUtils::encoding_to_string(Encoding encoding) {
    switch (encoding) {
        case Encoding::UTF8: return "UTF-8";
        case Encoding::UTF16: return "UTF-16";
        case Encoding::UTF32: return "UTF-32";
        case Encoding::ASCII: return "ASCII";
        case Encoding::ISO8859_1: return "ISO-8859-1";
        case Encoding::Windows1252: return "Windows-1252";
        case Encoding::CP437: return "CP437";
        case Encoding::CP850: return "CP850";
        case Encoding::CP1252: return "CP1252";
        case Encoding::MacRoman: return "MacRoman";
        case Encoding::Custom: return "Custom";
        default: return "Unknown";
    }
}

std::optional<Encoding> EncodingUtils::string_to_encoding(const std::string& encoding_str) {
    if (encoding_str == "UTF-8" || encoding_str == "utf8") return Encoding::UTF8;
    if (encoding_str == "UTF-16" || encoding_str == "utf16") return Encoding::UTF16;
    if (encoding_str == "UTF-32" || encoding_str == "utf32") return Encoding::UTF32;
    if (encoding_str == "ASCII" || encoding_str == "ascii") return Encoding::ASCII;
    if (encoding_str == "ISO-8859-1" || encoding_str == "iso8859-1") return Encoding::ISO8859_1;
    if (encoding_str == "Windows-1252" || encoding_str == "cp1252") return Encoding::Windows1252;
    if (encoding_str == "CP437" || encoding_str == "cp437") return Encoding::CP437;
    if (encoding_str == "CP850" || encoding_str == "cp850") return Encoding::CP850;
    if (encoding_str == "CP1252") return Encoding::CP1252;
    if (encoding_str == "MacRoman" || encoding_str == "macroman") return Encoding::MacRoman;
    return std::nullopt;
}

std::string EncodingUtils::bom_to_string(BOM bom) {
    switch (bom) {
        case BOM::None: return "None";
        case BOM::UTF8: return "UTF-8";
        case BOM::UTF16LE: return "UTF-16 LE";
        case BOM::UTF16BE: return "UTF-16 BE";
        case BOM::UTF32LE: return "UTF-32 LE";
        case BOM::UTF32BE: return "UTF-32 BE";
        default: return "Unknown";
    }
}

std::optional<BOM> EncodingUtils::string_to_bom(const std::string& bom_str) {
    if (bom_str == "None" || bom_str == "none") return BOM::None;
    if (bom_str == "UTF-8" || bom_str == "utf8") return BOM::UTF8;
    if (bom_str == "UTF-16 LE" || bom_str == "utf16le") return BOM::UTF16LE;
    if (bom_str == "UTF-16 BE" || bom_str == "utf16be") return BOM::UTF16BE;
    if (bom_str == "UTF-32 LE" || bom_str == "utf32le") return BOM::UTF32LE;
    if (bom_str == "UTF-32 BE" || bom_str == "utf32be") return BOM::UTF32BE;
    return std::nullopt;
}

std::vector<uint8_t> EncodingUtils::get_bom_bytes(BOM bom) {
    switch (bom) {
        case BOM::UTF8:
            return {0xEF, 0xBB, 0xBF};
        case BOM::UTF16LE:
            return {0xFF, 0xFE};
        case BOM::UTF16BE:
            return {0xFE, 0xFF};
        case BOM::UTF32LE:
            return {0xFF, 0xFE, 0x00, 0x00};
        case BOM::UTF32BE:
            return {0x00, 0x00, 0xFE, 0xFF};
        case BOM::None:
        default:
            return {};
    }
}

BOM EncodingUtils::detect_bom(const std::vector<uint8_t>& data) {
    if (data.size() >= 3 && data[0] == 0xEF && data[1] == 0xBB && data[2] == 0xBF) {
        return BOM::UTF8;
    }
    if (data.size() >= 2) {
        if (data[0] == 0xFF && data[1] == 0xFE) {
            if (data.size() >= 4 && data[2] == 0x00 && data[3] == 0x00) {
                return BOM::UTF32LE;
            }
            return BOM::UTF16LE;
        }
        if (data[0] == 0xFE && data[1] == 0xFF) {
            return BOM::UTF16BE;
        }
    }
    if (data.size() >= 4 && data[0] == 0x00 && data[1] == 0x00 && data[2] == 0xFE && data[3] == 0xFF) {
        return BOM::UTF32BE;
    }
    return BOM::None;
}

std::vector<Encoding> EncodingUtils::get_supported_encodings() {
    return {
        Encoding::UTF8,
        Encoding::UTF16,
        Encoding::UTF32,
        Encoding::ASCII,
        Encoding::ISO8859_1,
        Encoding::Windows1252,
        Encoding::CP437,
        Encoding::CP850,
        Encoding::CP1252,
        Encoding::MacRoman
    };
}

bool EncodingUtils::is_encoding_supported(Encoding encoding) {
    auto supported = get_supported_encodings();
    return std::find(supported.begin(), supported.end(), encoding) != supported.end();
}

std::string EncodingUtils::get_encoding_byte_order(Encoding encoding) {
    switch (encoding) {
        case Encoding::UTF16:
        case Encoding::UTF32:
            return "LE"; // Default to little-endian
        default:
            return "";
    }
}

size_t EncodingUtils::calculate_character_count(const std::vector<uint8_t>& data,
                                              Encoding encoding) {
    switch (encoding) {
        case Encoding::ASCII:
            return data.size();
        case Encoding::UTF8: {
            size_t count = 0;
            size_t i = 0;
            while (i < data.size()) {
                uint8_t byte = data[i];
                if (byte < 0x80) {
                    i += 1;
                } else if ((byte & 0xE0) == 0xC0) {
                    i += 2;
                } else if ((byte & 0xF0) == 0xE0) {
                    i += 3;
                } else if ((byte & 0xF8) == 0xF0) {
                    i += 4;
                } else {
                    i += 1; // Invalid byte, skip
                }
                count++;
            }
            return count;
        }
        case Encoding::UTF16:
            return data.size() / 2;
        case Encoding::UTF32:
            return data.size() / 4;
        default:
            return data.size(); // Assume single-byte encoding
    }
}

size_t EncodingUtils::estimate_conversion_buffer_size(size_t input_size,
                                                    Encoding source_encoding,
                                                    Encoding target_encoding) {
    // Conservative estimates for buffer allocation
    if (source_encoding == target_encoding) {
        return input_size;
    }
    
    // UTF-8 can expand up to 4 bytes per character
    if (target_encoding == Encoding::UTF8) {
        switch (source_encoding) {
            case Encoding::ASCII:
                return input_size; // ASCII is subset of UTF-8
            case Encoding::UTF16:
                return input_size * 2; // Conservative estimate
            case Encoding::UTF32:
                return input_size; // UTF-32 is already maximum size
            default:
                return input_size * 4; // Maximum UTF-8 expansion
        }
    }
    
    // UTF-16 is 2 bytes per character minimum
    if (target_encoding == Encoding::UTF16) {
        switch (source_encoding) {
            case Encoding::ASCII:
                return input_size * 2;
            case Encoding::UTF8:
                return input_size * 2; // Conservative estimate
            case Encoding::UTF32:
                return input_size / 2; // UTF-32 to UTF-16
            default:
                return input_size * 2;
        }
    }
    
    // UTF-32 is 4 bytes per character
    if (target_encoding == Encoding::UTF32) {
        return input_size * 4;
    }
    
    // Default conservative estimate
    return input_size * 4;
}

} // namespace omop::core