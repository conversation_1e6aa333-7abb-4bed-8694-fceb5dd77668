#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <vector>
#include <thread>
#include <atomic>
#include <future>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <string>
#include <unordered_map>
#include <any>
#include <nlohmann/json.hpp>

namespace omop::core {

/**
 * @brief ETL job status
 */
enum class JobStatus {
    Created,
    Initializing,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled
};

/**
 * @brief ETL job information
 */
class JobInfo {
public:
    std::string job_id;
    std::string job_name;
    JobStatus status{JobStatus::Created};
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> error_messages;
    std::unordered_map<std::string, std::any> metadata;

    /**
     * @brief Get job duration
     * @return std::chrono::duration<double> Duration in seconds
     */
    [[nodiscard]] std::chrono::duration<double> duration() const {
        if (status == JobStatus::Running) {
            return std::chrono::system_clock::now() - start_time;
        }
        return end_time - start_time;
    }

    /**
     * @brief Get progress percentage
     * @return double Progress (0-100)
     */
    [[nodiscard]] double progress() const {
        if (total_records == 0) return 0.0;
        return (static_cast<double>(processed_records) / total_records) * 100.0;
    }

    /**
     * @brief Get error rate
     * @return double Error rate (0-1)
     */
    [[nodiscard]] double error_rate() const {
        if (processed_records == 0) return 0.0;
        return static_cast<double>(error_records) / processed_records;
    }
};

/**
 * @brief ETL pipeline configuration
 */
struct PipelineConfig {
    size_t batch_size{1000};
    size_t max_parallel_batches{4};
    size_t queue_size{10000};
    size_t commit_interval{10000};
    double error_threshold{0.01};
    bool stop_on_error{true};
    bool validate_records{true};
    bool enable_checkpointing{false};
    std::chrono::seconds checkpoint_interval{300}; // 5 minutes
    std::string checkpoint_dir;
};

/**
 * @brief ETL pipeline orchestrator
 *
 * This class coordinates the entire ETL process, managing extractors,
 * transformers, and loaders to process data efficiently.
 */
class ETLPipeline {
public:
    /**
     * @brief Constructor
     * @param config Pipeline configuration
     */
    explicit ETLPipeline(PipelineConfig config = {});

    /**
     * @brief Destructor
     */
    ~ETLPipeline();

    /**
     * @brief Set extractor
     * @param extractor Data extractor
     */
    void set_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer
     * @param transformer Data transformer
     */
    void set_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader
     * @param loader Data loader
     */
    void set_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processing function
     */
    void add_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processing function
     */
    void add_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Start the pipeline
     * @param job_id Job identifier
     * @return std::future<JobInfo> Future containing job results
     */
    std::future<JobInfo> start(const std::string& job_id);

    /**
     * @brief Stop the pipeline gracefully
     */
    void stop();

    /**
     * @brief Pause the pipeline
     */
    void pause();

    /**
     * @brief Resume the pipeline
     */
    void resume();

    /**
     * @brief Get current job status
     * @return JobStatus Current status
     */
    [[nodiscard]] JobStatus get_status() const;

    /**
     * @brief Get job information
     * @return JobInfo Current job information
     */
    [[nodiscard]] JobInfo get_job_info() const;

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void set_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback function
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Main pipeline execution method
     */
    void run_pipeline();

    /**
     * @brief Extract data in a separate thread
     */
    void extraction_worker();

    /**
     * @brief Transform data in a separate thread
     */
    void transformation_worker();

    /**
     * @brief Load data in a separate thread
     */
    void loading_worker();

    /**
     * @brief Handle pipeline error
     * @param stage Processing stage
     * @param error Error message
     * @param exception Optional exception
     */
    void handle_error(ProcessingContext::Stage stage,
                     const std::string& error,
                     const std::exception* exception = nullptr);

    /**
     * @brief Save checkpoint
     */
    void save_checkpoint();

    /**
     * @brief Load checkpoint
     * @return bool True if checkpoint loaded
     */
    bool load_checkpoint();

    /**
     * @brief Check if extraction is complete
     */
    bool is_extraction_complete() const;

    // Job information - protected for testing
    JobInfo job_info_;
    std::atomic<bool> should_stop_{false};
    std::atomic<bool> is_paused_{false};
    std::atomic<bool> extraction_complete_{false};
    std::atomic<size_t> batches_in_flight_{0};
    std::function<void(const JobInfo&)> progress_callback_;

private:
    // Configuration
    PipelineConfig config_;

    // Components
    std::unique_ptr<IExtractor> extractor_;
    std::unique_ptr<ITransformer> transformer_;
    std::unique_ptr<ILoader> loader_;

    // Pre/post processors
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_;

    // Processing context
    ProcessingContext context_;

    // Thread management
    std::vector<std::thread> workers_;
    std::atomic<JobStatus> status_{JobStatus::Created};
    std::condition_variable pause_cv_;

    // Queues
    std::queue<RecordBatch> extract_queue_;
    std::queue<RecordBatch> transform_queue_;
    std::mutex extract_mutex_;
    std::mutex transform_mutex_;
    std::condition_variable extract_cv_;
    std::condition_variable transform_cv_;

    // Error callback
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    mutable std::mutex stats_mutex_;
    std::chrono::steady_clock::time_point last_checkpoint_;
};

/**
 * @brief Pipeline builder for fluent API
 *
 * Provides a builder pattern for constructing ETL pipelines.
 */
class PipelineBuilder {
public:
    /**
     * @brief Constructor
     */
    PipelineBuilder() : pipeline_(std::make_unique<ETLPipeline>()) {}

    /**
     * @brief Set configuration
     * @param config Pipeline configuration
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config(const PipelineConfig& config);

    /**
     * @brief Set configuration from file
     * @param config_file Configuration file path
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config_file(const std::string& config_file);

    /**
     * @brief Set extractor by type
     * @param type Extractor type
     * @param params Extractor parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(const std::string& type,
                                   const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom extractor
     * @param extractor Extractor instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer by type
     * @param type Transformer type
     * @param params Transformer parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(const std::string& type,
                                     const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set transformer for table
     * @param table_name OMOP table name
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer_for_table(const std::string& table_name);

    /**
     * @brief Set custom transformer
     * @param transformer Transformer instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader by type
     * @param type Loader type
     * @param params Loader parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(const std::string& type,
                                const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom loader
     * @param loader Loader instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Set progress callback
     * @param callback Progress callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

    /**
     * @brief Build the pipeline
     * @return std::unique_ptr<ETLPipeline> Constructed pipeline
     */
    [[nodiscard]] std::unique_ptr<ETLPipeline> build();

private:
    std::unique_ptr<ETLPipeline> pipeline_;
    ProcessingContext context_;
};

/**
 * @brief Pipeline manager for managing multiple ETL jobs
 *
 * This class manages multiple ETL pipelines, providing job scheduling,
 * monitoring, and resource management.
 */
class PipelineManager {
public:
    /**
     * @brief Constructor
     * @param max_concurrent_jobs Maximum concurrent jobs
     */
    explicit PipelineManager(size_t max_concurrent_jobs = 4);

    /**
     * @brief Destructor
     */
    ~PipelineManager();

    /**
     * @brief Submit job
     * @param job_name Job name
     * @param pipeline Pipeline to execute
     * @return std::string Job ID
     */
    std::string submit_job(const std::string& job_name,
                          std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return std::optional<JobStatus> Job status if found
     */
    [[nodiscard]] std::optional<JobStatus> get_job_status(const std::string& job_id) const;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if found
     */
    [[nodiscard]] std::optional<JobInfo> get_job_info(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return std::vector<JobInfo> All job information
     */
    [[nodiscard]] std::vector<JobInfo> get_all_jobs() const;

    /**
     * @brief Cancel job
     * @param job_id Job ID
     * @return bool True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause job
     * @param job_id Job ID
     * @return bool True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume job
     * @param job_id Job ID
     * @return bool True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Wait for job completion
     * @param job_id Job ID
     * @param timeout_ms Timeout in milliseconds (-1 for no timeout)
     * @return bool True if completed within timeout
     */
    bool wait_for_job(const std::string& job_id, int timeout_ms = -1);

    /**
     * @brief Shutdown manager
     * @param wait_for_jobs Whether to wait for running jobs
     */
    void shutdown(bool wait_for_jobs = true);

private:
    struct JobEntry {
        std::string job_id;
        std::unique_ptr<ETLPipeline> pipeline;
        std::future<JobInfo> future;
        JobInfo info;
    };

    size_t max_concurrent_jobs_;
    std::unordered_map<std::string, std::unique_ptr<JobEntry>> jobs_;
    std::queue<std::string> job_queue_;
    std::vector<std::thread> scheduler_threads_;

    mutable std::mutex jobs_mutex_;
    std::condition_variable job_cv_;
    std::atomic<bool> shutdown_{false};

    void scheduler_worker();
    std::string generate_job_id();
};

} // namespace omop::core