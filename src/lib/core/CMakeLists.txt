# Core library CMakeLists.txt

add_library(omop_core STATIC)

set(CORE_SOURCES
    component_factory.cpp
    interfaces.cpp
    job_manager.cpp
    job_scheduler.cpp
    pipeline.cpp
    record.cpp
    encoding.cpp
    workflow_engine.cpp
)

set(CORE_HEADERS
    interfaces.h
    job_manager.h
    job_scheduler.h
    pipeline.h
    record.h
    workflow_engine.h
    encoding.h
)

target_sources(omop_core PRIVATE ${CORE_SOURCES})

omop_configure_library(omop_core
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_common
        $<$<TARGET_EXISTS:yaml-cpp::yaml-cpp>:yaml-cpp::yaml-cpp>
        $<$<TARGET_EXISTS:yaml-cpp>:yaml-cpp>
    PRIVATE_DEPS
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        Threads::Threads
    HEADERS
        ${CORE_HEADERS}
)
