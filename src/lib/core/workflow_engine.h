/**
 * @file workflow_engine.h
 * @brief Workflow engine for orchestrating complex ETL workflows
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <optional>
#include <unordered_map>
#include <unordered_set>
#include <any>
#include <mutex>
#include "job_manager.h"
#include "job_scheduler.h"
#include "common/logging.h"
#include "common/exceptions.h"

namespace omop::core {

// Forward declarations
struct WorkflowStep;
struct WorkflowDefinition;
struct WorkflowExecutionState;

/**
 * @brief Workflow execution information
 */
struct WorkflowExecution {
    std::string workflow_id;
    std::string execution_id;
    bool completed{false};
    bool successful{false};
    
    bool isComplete() const { return completed; }
    bool isSuccessful() const { return successful; }
    virtual bool stepExecuted(const std::string& step_name) const { 
        // Default implementation - always return true for now
        return true; 
    }
    
    virtual ~WorkflowExecution() = default;
};

/**
 * @brief Workflow engine for orchestrating ETL workflows
 * 
 * Supports:
 * - Workflow definition and execution
 * - Conditional steps and branching
 * - Step dependencies and parallel execution
 * - Workflow state management and persistence
 */
class WorkflowEngine {
public:
    /**
     * @brief Constructor
     * @param job_manager Job manager instance
     * @param job_scheduler Job scheduler instance
     */
    WorkflowEngine(std::shared_ptr<JobManager> job_manager,
                   std::unique_ptr<JobScheduler>& job_scheduler);

    /**
     * @brief Define a workflow from YAML configuration
     * @param workflow_name Name of the workflow
     * @param workflow_yaml YAML configuration string
     */
    void defineWorkflow(const std::string& workflow_name, const std::string& workflow_yaml);

    /**
     * @brief Execute a defined workflow
     * @param workflow_name Name of the workflow to execute
     * @return Unique execution ID
     */
    std::string execute(const std::string& workflow_name);

    /**
     * @brief Wait for workflow completion
     * @param execution_id Execution ID to wait for
     * @param timeout Maximum time to wait
     */
    void waitForCompletion(const std::string& execution_id, std::chrono::seconds timeout);

    /**
     * @brief Get workflow execution information
     * @param execution_id Execution ID
     * @return Optional execution information
     */
    std::optional<WorkflowExecution> getExecution(const std::string& execution_id);

private:
    /**
     * @brief Validate workflow definition
     * @param definition Workflow definition to validate
     */
    void validateWorkflow(const WorkflowDefinition& definition);
    
    /**
     * @brief Execute workflow steps
     * @param execution Execution state
     * @param logger Logger instance
     */
    void executeWorkflowSteps(std::shared_ptr<WorkflowExecutionState> execution, 
                             std::shared_ptr<omop::common::Logger> logger);
    
    /**
     * @brief Check if step dependencies are met
     * @param step_name Step name
     * @param definition Workflow definition
     * @param execution Execution state
     * @return True if dependencies are met
     */
    bool areStepDependenciesMet(const std::string& step_name,
                               const WorkflowDefinition& definition,
                               std::shared_ptr<WorkflowExecutionState> execution);
    
    /**
     * @brief Execute a single step
     * @param step_name Step name
     * @param definition Workflow definition
     * @param execution Execution state
     * @param logger Logger instance
     * @return True if step succeeded
     */
    bool executeStep(const std::string& step_name,
                    const WorkflowDefinition& definition,
                    std::shared_ptr<WorkflowExecutionState> execution,
                    std::shared_ptr<omop::common::Logger> logger);
    
    /**
     * @brief Update step execution state
     * @param step_name Step name
     * @param execution Execution state
     * @param success Whether step succeeded
     */
    void updateStepState(const std::string& step_name,
                        std::shared_ptr<WorkflowExecutionState> execution,
                        bool success);
    
    /**
     * @brief Execute ETL job step
     * @param step Step definition
     * @param execution Execution state
     * @param logger Logger instance
     * @return True if succeeded
     */
    bool executeETLJob(const WorkflowStep& step,
                      std::shared_ptr<WorkflowExecutionState> execution,
                      std::shared_ptr<omop::common::Logger> logger);
    
    /**
     * @brief Execute data validation step
     * @param step Step definition
     * @param execution Execution state
     * @param logger Logger instance
     * @return True if succeeded
     */
    bool executeDataValidation(const WorkflowStep& step,
                              std::shared_ptr<WorkflowExecutionState> execution,
                              std::shared_ptr<omop::common::Logger> logger);
    
    /**
     * @brief Execute custom script step
     * @param step Step definition
     * @param execution Execution state
     * @param logger Logger instance
     * @return True if succeeded
     */
    bool executeCustomScript(const WorkflowStep& step,
                            std::shared_ptr<WorkflowExecutionState> execution,
                            std::shared_ptr<omop::common::Logger> logger);
    
    /**
     * @brief Execute delay step
     * @param step Step definition
     * @param execution Execution state
     * @param logger Logger instance
     * @return True if succeeded
     */
    bool executeDelay(const WorkflowStep& step,
                     std::shared_ptr<WorkflowExecutionState> execution,
                     std::shared_ptr<omop::common::Logger> logger);

    std::shared_ptr<JobManager> job_manager_;
    JobScheduler* job_scheduler_;  // Non-owning pointer to scheduler
    std::unordered_map<std::string, std::string> workflow_definitions_;
    
    // Static storage for parsed workflows and executions
    static std::unordered_map<std::string, WorkflowDefinition> parsed_workflows_;
    static std::unordered_map<std::string, std::shared_ptr<WorkflowExecutionState>> executions_;
};

} // namespace omop::core