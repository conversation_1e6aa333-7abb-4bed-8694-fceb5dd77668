#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>

#include "interfaces.h"

namespace omop::core {

// Forward declarations
class IExtractor;
class ITransformer;
class ILoader;

/**
 * @brief Initialize built-in component factories
 * 
 * This function initializes the global component factories with built-in
 * component types. It should be called once at application startup.
 */
void initialize_component_factories();

/**
 * @brief Get global extractor factory
 * @return Reference to extractor factory
 */
ComponentFactory<IExtractor>& get_extractor_factory();

/**
 * @brief Get global transformer factory
 * @return Reference to transformer factory
 */
ComponentFactory<ITransformer>& get_transformer_factory();

/**
 * @brief Get global loader factory
 * @return Reference to loader factory
 */
ComponentFactory<ILoader>& get_loader_factory();

/**
 * @brief Factory method to create extractor by type
 * @param type Extractor type
 * @param config Configuration parameters
 * @return Unique pointer to created extractor
 */
std::unique_ptr<IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Factory method to create transformer by type
 * @param type Transformer type
 * @param config Configuration parameters
 * @return Unique pointer to created transformer
 */
std::unique_ptr<ITransformer> create_transformer(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Factory method to create loader by type
 * @param type Loader type
 * @param config Configuration parameters
 * @return Unique pointer to created loader
 */
std::unique_ptr<ILoader> create_loader(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Register custom extractor type
 * @param type Type name
 * @param creator Creator function
 */
void register_extractor_type(
    const std::string& type,
    std::function<std::unique_ptr<IExtractor>()> creator);

/**
 * @brief Register custom transformer type
 * @param type Type name
 * @param creator Creator function
 */
void register_transformer_type(
    const std::string& type,
    std::function<std::unique_ptr<ITransformer>()> creator);

/**
 * @brief Register custom loader type
 * @param type Type name
 * @param creator Creator function
 */
void register_loader_type(
    const std::string& type,
    std::function<std::unique_ptr<ILoader>()> creator);

/**
 * @brief Get list of registered extractor types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_extractor_types();

/**
 * @brief Get list of registered transformer types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_transformer_types();

/**
 * @brief Get list of registered loader types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_loader_types();

/**
 * @brief Set mock CSV extractor pause flag for testing
 * @param paused Whether to pause the mock CSV extractor
 */
void set_mock_csv_paused(bool paused);

} // namespace omop::core