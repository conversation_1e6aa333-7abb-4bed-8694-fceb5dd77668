/**
 * @file pipeline.cpp
 * @brief Implementation of pipeline, pipeline builder and pipeline manager for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "pipeline.h"
#include "interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <fstream>
#include <yaml-cpp/yaml.h>
#include <algorithm>
#include <filesystem>
#include <chrono>
#include <format>
#include <thread>
#include <any>
#include <optional>

namespace omop::core {

// Forward declarations for factory functions
extern std::unique_ptr<IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

extern std::unique_ptr<ITransformer> create_transformer(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

extern std::unique_ptr<ILoader> create_loader(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

// ETLPipeline implementation
ETLPipeline::ETLPipeline(PipelineConfig config)
    : config_(std::move(config)) {
    // Reserve space for queues
    // Queue sizes are managed by condition variables
}

ETLPipeline::~ETLPipeline() {
    // Set stop flag first
    should_stop_ = true;
    
    // Notify all condition variables to wake up any waiting threads
    extract_cv_.notify_all();
    transform_cv_.notify_all();
    pause_cv_.notify_all();
    
    // Wait for all workers to finish
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

void ETLPipeline::set_extractor(std::unique_ptr<IExtractor> extractor) {
    extractor_ = std::move(extractor);
}

void ETLPipeline::set_transformer(std::unique_ptr<ITransformer> transformer) {
    transformer_ = std::move(transformer);
}

void ETLPipeline::set_loader(std::unique_ptr<ILoader> loader) {
    loader_ = std::move(loader);
}

void ETLPipeline::add_pre_processor(
    std::function<void(RecordBatch&, ProcessingContext&)> processor) {
    pre_processors_.push_back(std::move(processor));
}

void ETLPipeline::add_post_processor(
    std::function<void(RecordBatch&, ProcessingContext&)> processor) {
    post_processors_.push_back(std::move(processor));
}

std::future<JobInfo> ETLPipeline::start(const std::string& job_id) {
    if (!extractor_ || !transformer_ || !loader_) {
        throw common::ConfigurationException("Pipeline components not fully configured");
    }

    // Initialize job info
    job_info_.job_id = job_id;
    job_info_.status = JobStatus::Initializing;
    job_info_.start_time = std::chrono::system_clock::now();

    // Initialize context
    context_.set_job_id(job_id);
    context_.set_error_threshold(config_.error_threshold);

    // Initialize components
    std::unordered_map<std::string, std::any> empty_config;
    extractor_->initialize(empty_config, context_);
    transformer_->initialize(empty_config, context_);
    loader_->initialize(empty_config, context_);

    // Start worker threads
    status_ = JobStatus::Running;
    job_info_.status = JobStatus::Running;

    // Create promise for result
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();

    // Start main pipeline thread
    workers_.emplace_back([this, promise]() {
        try {
            run_pipeline();
            promise->set_value(job_info_);
        } catch (...) {
            job_info_.status = JobStatus::Failed;
            promise->set_exception(std::current_exception());
        }
    });

    // Start worker threads
    workers_.emplace_back([this]() { extraction_worker(); });
    workers_.emplace_back([this]() { transformation_worker(); });
    workers_.emplace_back([this]() { loading_worker(); });

    return future;
}

void ETLPipeline::stop() {
    should_stop_ = true;

    // Wake up all workers
    extract_cv_.notify_all();
    transform_cv_.notify_all();
    pause_cv_.notify_all();

    // Update status
    status_ = JobStatus::Cancelled;
    job_info_.status = JobStatus::Cancelled;
}

void ETLPipeline::pause() {
    is_paused_ = true;
    status_ = JobStatus::Paused;
    job_info_.status = JobStatus::Paused;
}

void ETLPipeline::resume() {
    is_paused_ = false;
    pause_cv_.notify_all();
    status_ = JobStatus::Running;
    job_info_.status = JobStatus::Running;
}

JobStatus ETLPipeline::get_status() const {
    return status_.load();
}

JobInfo ETLPipeline::get_job_info() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return job_info_;
}

void ETLPipeline::set_progress_callback(
    std::function<void(const JobInfo&)> callback) {
    progress_callback_ = std::move(callback);
}

void ETLPipeline::set_error_callback(
    std::function<void(const std::string&, const std::exception&)> callback) {
    error_callback_ = std::move(callback);
}

void ETLPipeline::run_pipeline() {
    auto logger = common::Logger::get("omop-etl-pipeline");
    logger->info("Starting pipeline for job: {}", job_info_.job_id);

    try {
        // Wait for all workers to complete
        while (!should_stop_) {
            std::unique_lock<std::mutex> lock(stats_mutex_);
            
            // Use condition variable for more efficient waiting
            pause_cv_.wait_for(lock, std::chrono::seconds(1), [this] {
                return should_stop_ || 
                       (extraction_complete_.load() && 
                        batches_in_flight_.load() == 0);
            });

            // Check if extraction is complete and queues are empty
            bool extraction_done = extraction_complete_.load();
            bool queues_empty = false;

            {
                std::lock_guard<std::mutex> lock1(extract_mutex_);
                std::lock_guard<std::mutex> lock2(transform_mutex_);
                queues_empty = extract_queue_.empty() && transform_queue_.empty();
            }

            if (extraction_done && queues_empty && batches_in_flight_ == 0) {
                // Signal workers to stop
                should_stop_ = true;
                extract_cv_.notify_all();
                transform_cv_.notify_all();
                pause_cv_.notify_all();
                break;
            }

            // Check for errors
            if (context_.is_error_threshold_exceeded()) {
                logger->error("Error threshold exceeded, stopping pipeline");
                handle_error(ProcessingContext::Stage::Extract,
                           "Error threshold exceeded", nullptr);
                should_stop_ = true;
                status_ = JobStatus::Failed;
                break;
            }

            // Update progress
            if (progress_callback_) {
                progress_callback_(job_info_);
            }

            // Save checkpoint periodically
            auto now = std::chrono::steady_clock::now();
            if (now - last_checkpoint_ > std::chrono::minutes(5)) {
                save_checkpoint();
                last_checkpoint_ = now;
            }
        }

        // Finalize components
        try {
            extractor_->finalize(context_);
            loader_->finalize(context_);
        } catch (const std::exception& e) {
            logger->error("Error during component finalization: {}", e.what());
        }

        // Update status to completed (only if not already failed or cancelled)
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            if (job_info_.status != JobStatus::Failed && job_info_.status != JobStatus::Cancelled) {
                status_ = JobStatus::Completed;
                job_info_.status = JobStatus::Completed;
            }
            job_info_.end_time = std::chrono::system_clock::now();
        }

        // Log completion status
        if (job_info_.status == JobStatus::Failed) {
            logger->error("Pipeline failed for job: {} - Processed: {}, Errors: {}",
                        job_info_.job_id, job_info_.processed_records, job_info_.error_records);
        } else {
            logger->info("Pipeline completed for job: {} - Processed: {}, Errors: {}",
                        job_info_.job_id, job_info_.processed_records, job_info_.error_records);
        }

    } catch (const std::exception& e) {
        // Update status to failed
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            status_ = JobStatus::Failed;
            job_info_.status = JobStatus::Failed;
            job_info_.end_time = std::chrono::system_clock::now();
        }
        handle_error(context_.current_stage(), e.what(), &e);
        throw;
    }
}

void ETLPipeline::extraction_worker() {
    auto logger = common::Logger::get("omop-etl-extract");
    context_.set_stage(ProcessingContext::Stage::Extract);

    try {
        while (!should_stop_ && extractor_->has_more_data()) {
            // Check if paused
            if (is_paused_) {
                std::unique_lock<std::mutex> lock(stats_mutex_);
                pause_cv_.wait(lock, [this] { return !is_paused_ || should_stop_; });
                if (should_stop_) break;
            }

            // Check queue size
            {
                std::unique_lock<std::mutex> lock(extract_mutex_);
                extract_cv_.wait(lock, [this] {
                    return extract_queue_.size() < config_.queue_size || should_stop_;
                });
                if (should_stop_) break;
            }

            // Extract batch
            auto batch = extractor_->extract_batch(config_.batch_size, context_);

            if (!batch.empty()) {
                // Store batch size before processing
                size_t batch_size = batch.size();
                
                // Apply pre-processors
                try {
                    for (const auto& processor : pre_processors_) {
                        processor(batch, context_);
                    }
                } catch (const std::exception& e) {
                    logger->error("Pre-processor error: {}", e.what());
                    context_.increment_errors(batch_size);
                    continue; // Skip this batch
                }

                // Add to queue
                {
                    std::lock_guard<std::mutex> lock(extract_mutex_);
                    extract_queue_.push(std::move(batch));
                    
                    // Update job info thread-safely using stored size
                    std::lock_guard<std::mutex> stats_lock(stats_mutex_);
                    job_info_.total_records += batch_size;
                }
                extract_cv_.notify_one();
            }
        }
        
        // Mark extraction as complete
        extraction_complete_ = true;
        pause_cv_.notify_all();
        
    } catch (const std::exception& e) {
        handle_error(ProcessingContext::Stage::Extract,
                    "Extraction error: " + std::string(e.what()), &e);
        should_stop_ = true;
    }

    logger->debug("Extraction worker completed");
}

void ETLPipeline::transformation_worker() {
    auto logger = common::Logger::get("omop-etl-transform");
    context_.set_stage(ProcessingContext::Stage::Transform);

    try {
        while (!should_stop_) {
            // Get batch from extract queue
            RecordBatch batch(0);  // Initialize with 0 capacity, will be moved from queue
            {
                std::unique_lock<std::mutex> lock(extract_mutex_);
                extract_cv_.wait(lock, [this] {
                    return !extract_queue_.empty() || should_stop_;
                });

                if (should_stop_ && extract_queue_.empty()) {
                    break;
                }

                if (!extract_queue_.empty()) {
                    batch = std::move(extract_queue_.front());
                    extract_queue_.pop();
                    batches_in_flight_++;
                }
            }

            if (batch.empty()) {
                std::this_thread::yield();
                continue;
            }

            // Transform batch
            try {
                [[maybe_unused]] const size_t original_batch_size = batch.size();
                auto transformed = transformer_->transform_batch(batch, context_);

                // Apply post-processors
                for (const auto& processor : post_processors_) {
                    processor(transformed, context_);
                }

                // Note: Processed count will be incremented in loading worker for successfully loaded records

                // Check error threshold after transformation (errors may be added during transformation)
                if (context_.is_error_threshold_exceeded() && config_.stop_on_error) {
                    logger->error("Error threshold exceeded in transformer worker - Errors: {}, Processed: {}, Threshold: {}",
                                context_.error_count(), context_.processed_count(), config_.error_threshold);
                    
                    // Update job info with current stats before stopping
                    {
                        std::lock_guard<std::mutex> lock(stats_mutex_);
                        job_info_.processed_records = context_.processed_count();
                        job_info_.error_records = context_.error_count();
                    }
                    
                    handle_error(ProcessingContext::Stage::Transform,
                               "Error threshold exceeded", nullptr);
                    should_stop_ = true;
                    status_ = JobStatus::Failed;
                    pause_cv_.notify_all();
                    extract_cv_.notify_all();
                    transform_cv_.notify_all();
                    break;
                }

                // Add to transform queue
                {
                    std::lock_guard<std::mutex> lock(transform_mutex_);
                    transform_queue_.push(std::move(transformed));
                }
                transform_cv_.notify_one();

            } catch (const std::exception& e) {
                logger->error("Transformation error: {}", e.what());
                context_.increment_errors(batch.size());

                // Check error threshold
                double error_rate = static_cast<double>(context_.error_count()) /
                                  context_.processed_count();
                if (error_rate > config_.error_threshold && config_.stop_on_error) {
                    handle_error(ProcessingContext::Stage::Transform,
                               "Error threshold exceeded", &e);
                    should_stop_ = true;
                    pause_cv_.notify_all();
                    extract_cv_.notify_all();
                    transform_cv_.notify_all();
                }
            }
            
            batches_in_flight_--;
        }
    } catch (const std::exception& e) {
        handle_error(ProcessingContext::Stage::Transform,
                    "Transformation worker error: " + std::string(e.what()), &e);
        should_stop_ = true;
    }

    logger->debug("Transformation worker completed");
}

void ETLPipeline::loading_worker() {
    auto logger = common::Logger::get("omop-etl-load");
    context_.set_stage(ProcessingContext::Stage::Load);

    size_t records_since_commit = 0;

    try {
        while (!should_stop_) {
            // Get batch from transform queue
            RecordBatch batch(0);  // Initialize with 0 capacity, will be moved from queue
            {
                std::unique_lock<std::mutex> lock(transform_mutex_);
                transform_cv_.wait(lock, [this] {
                    return !transform_queue_.empty() || should_stop_ || 
                           (extraction_complete_ && batches_in_flight_ == 0);
                });

                if ((should_stop_ && transform_queue_.empty()) || 
                    (extraction_complete_ && transform_queue_.empty() && batches_in_flight_ == 0)) {
                    break;
                }

                if (!transform_queue_.empty()) {
                    batch = std::move(transform_queue_.front());
                    transform_queue_.pop();
                }
            }

            if (batch.empty()) continue;

            // Load batch
            try {
                size_t loaded = loader_->load_batch(batch, context_);
                records_since_commit += loaded;
                
                // Increment processed count for successfully loaded records
                if (loaded > 0) {
                    context_.increment_processed(loaded);
                }
                
                // Track failed records
                size_t failed = batch.size() - loaded;
                if (failed > 0) {
                    context_.increment_errors(failed);
                }

                // Update job info with processed records
                {
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    job_info_.processed_records = context_.processed_count();
                    job_info_.error_records = context_.error_count();
                }

                // Check error threshold after both processed and error counts are updated
                if (context_.is_error_threshold_exceeded() && config_.stop_on_error) {
                    logger->error("Error threshold exceeded in loading worker - Errors: {}, Processed: {}, Threshold: {}",
                                context_.error_count(), context_.processed_count(), config_.error_threshold);
                    
                    handle_error(ProcessingContext::Stage::Load,
                               "Error threshold exceeded", nullptr);
                    should_stop_ = true;
                    pause_cv_.notify_all();
                    extract_cv_.notify_all();
                    transform_cv_.notify_all();
                    break;
                }

                // Trigger progress callback
                if (progress_callback_) {
                    progress_callback_(job_info_);
                }

                // Commit if needed
                if (records_since_commit >= config_.commit_interval) {
                    loader_->commit(context_);
                    records_since_commit = 0;
                }

            } catch (const std::exception& e) {
                logger->error("Loading error: {}", e.what());
                context_.increment_errors(batch.size());

                // Update job info with error count
                {
                    std::lock_guard<std::mutex> lock(stats_mutex_);
                    job_info_.processed_records = context_.processed_count();
                    job_info_.error_records = context_.error_count();
                }

                // Rollback on error
                try {
                    loader_->rollback(context_);
                } catch (const std::exception& re) {
                    logger->error("Rollback failed: {}", re.what());
                }

                // Check error threshold
                double error_rate = static_cast<double>(context_.error_count()) /
                                  context_.processed_count();
                if (error_rate > config_.error_threshold && config_.stop_on_error) {
                    handle_error(ProcessingContext::Stage::Load,
                               "Error threshold exceeded", &e);
                    should_stop_ = true;
                    pause_cv_.notify_all();
                    extract_cv_.notify_all();
                    transform_cv_.notify_all();
                }
            }
        }

        // Final commit
        if (records_since_commit > 0) {
            loader_->commit(context_);
        }

    } catch (const std::exception& e) {
        handle_error(ProcessingContext::Stage::Load,
                    "Loading worker error: " + std::string(e.what()), &e);
        should_stop_ = true;
    }

    logger->debug("Loading worker completed");
}

void ETLPipeline::handle_error(ProcessingContext::Stage stage,
                               const std::string& error,
                               const std::exception* exception) {
    auto logger = common::Logger::get("omop-etl-pipeline");
    logger->error("Pipeline error at stage {}: {}",
                 static_cast<int>(stage), error);

    // Update job info
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        job_info_.status = JobStatus::Failed;
        job_info_.error_messages.push_back(error);
    }

    // Call error callback
    if (error_callback_ && exception) {
        error_callback_(error, *exception);
    }
}

void ETLPipeline::save_checkpoint() {
    if (config_.checkpoint_dir.empty()) return;

    auto logger = common::Logger::get("omop-etl-pipeline");
    logger->debug("Saving checkpoint for job: {}", job_info_.job_id);

    try {
        std::filesystem::path checkpoint_path =
            std::filesystem::path(config_.checkpoint_dir) /
            (job_info_.job_id + ".checkpoint");

        nlohmann::json checkpoint;
        checkpoint["job_id"] = job_info_.job_id;
        checkpoint["processed_records"] = context_.processed_count();
        checkpoint["error_records"] = context_.error_count();
        checkpoint["timestamp"] = std::chrono::system_clock::now().time_since_epoch().count();

        std::ofstream file(checkpoint_path);
        file << checkpoint.dump(2);

    } catch (const std::exception& e) {
        logger->error("Failed to save checkpoint: {}", e.what());
    }
}

bool ETLPipeline::load_checkpoint() {
    if (!config_.enable_checkpointing) {
        return false;
    }

    std::filesystem::path checkpoint_path = 
        std::filesystem::path(config_.checkpoint_dir.empty() ? 
                              "/tmp/omop-etl/checkpoints" : config_.checkpoint_dir) /
        (job_info_.job_id + ".checkpoint");
    
    if (!std::filesystem::exists(checkpoint_path)) {
        return false;
    }

    try {
        std::ifstream file(checkpoint_path);
        if (!file.is_open()) {
            return false;
        }

        nlohmann::json checkpoint;
        file >> checkpoint;
        file.close();

        // Restore pipeline state
        job_info_.processed_records = checkpoint["processed_records"];
        job_info_.error_records = checkpoint["error_records"];
        job_info_.total_records = checkpoint["total_records"];

        return true;
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-etl-pipeline");
        logger->error("Failed to load checkpoint: {}", e.what());
        return false;
    }
}

bool ETLPipeline::is_extraction_complete() const {
    return extraction_complete_.load() || 
           (extractor_ && !extractor_->has_more_data());
}

// PipelineBuilder implementation
PipelineBuilder& PipelineBuilder::with_config(const PipelineConfig& config) {
    pipeline_ = std::make_unique<ETLPipeline>(config);
    return *this;
}

PipelineBuilder& PipelineBuilder::with_config_file(const std::string& config_file) {
    if (!std::filesystem::exists(config_file)) {
        throw common::ConfigurationException("Configuration file not found: " + config_file);
    }

    try {
        auto yaml = YAML::LoadFile(config_file);

        PipelineConfig config;
        if (yaml["pipeline"]) {
            auto pipeline_node = yaml["pipeline"];

            if (pipeline_node["batch_size"]) {
                config.batch_size = pipeline_node["batch_size"].as<size_t>();
            }
            if (pipeline_node["max_parallel_batches"]) {
                config.max_parallel_batches = pipeline_node["max_parallel_batches"].as<size_t>();
            }
            if (pipeline_node["queue_size"]) {
                config.queue_size = pipeline_node["queue_size"].as<size_t>();
            }
            if (pipeline_node["commit_interval"]) {
                config.commit_interval = pipeline_node["commit_interval"].as<size_t>();
            }
            if (pipeline_node["error_threshold"]) {
                config.error_threshold = pipeline_node["error_threshold"].as<double>();
            }
            if (pipeline_node["stop_on_error"]) {
                config.stop_on_error = pipeline_node["stop_on_error"].as<bool>();
            }
            if (pipeline_node["validate_records"]) {
                config.validate_records = pipeline_node["validate_records"].as<bool>();
            }
            if (pipeline_node["checkpoint_interval"]) {
                config.checkpoint_interval = std::chrono::seconds(
                    pipeline_node["checkpoint_interval"].as<int>());
            }
            if (pipeline_node["checkpoint_dir"]) {
                config.checkpoint_dir = pipeline_node["checkpoint_dir"].as<std::string>();
            }
        }

        pipeline_ = std::make_unique<ETLPipeline>(config);

        // Load component configurations if present
        if (yaml["extractor"]) {
            auto extractor_node = yaml["extractor"];
            std::string type = extractor_node["type"].as<std::string>();

            std::unordered_map<std::string, std::any> params;
            for (auto it = extractor_node.begin(); it != extractor_node.end(); ++it) {
                if (it->first.as<std::string>() != "type") {
                    // Convert YAML values to std::any
                    if (it->second.IsScalar()) {
                        params[it->first.as<std::string>()] = it->second.as<std::string>();
                    }
                }
            }

            with_extractor(type, params);
        }

        if (yaml["transformer"]) {
            auto transformer_node = yaml["transformer"];
            std::string type = transformer_node["type"].as<std::string>();

            std::unordered_map<std::string, std::any> params;
            for (auto it = transformer_node.begin(); it != transformer_node.end(); ++it) {
                if (it->first.as<std::string>() != "type") {
                    if (it->second.IsScalar()) {
                        params[it->first.as<std::string>()] = it->second.as<std::string>();
                    }
                }
            }

            with_transformer(type, params);
        }

        if (yaml["loader"]) {
            auto loader_node = yaml["loader"];
            std::string type = loader_node["type"].as<std::string>();

            std::unordered_map<std::string, std::any> params;
            for (auto it = loader_node.begin(); it != loader_node.end(); ++it) {
                if (it->first.as<std::string>() != "type") {
                    if (it->second.IsScalar()) {
                        params[it->first.as<std::string>()] = it->second.as<std::string>();
                    }
                }
            }

            with_loader(type, params);
        }

    } catch (const YAML::Exception& e) {
        throw common::ConfigurationException("Failed to parse configuration file: " +
                                           std::string(e.what()));
    }

    return *this;
}

PipelineBuilder& PipelineBuilder::with_extractor(const std::string& type,
                                                 const std::unordered_map<std::string, std::any>& params) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (type.empty()) {
        throw common::ConfigurationException("Extractor type cannot be empty");
    }

    try {
        auto extractor = create_extractor(type, params);
        pipeline_->set_extractor(std::move(extractor));
    } catch (const std::exception& e) {
        throw common::ConfigurationException(
            std::format("Failed to create extractor of type '{}': {}", type, e.what()));
    }

    return *this;
}

PipelineBuilder& PipelineBuilder::with_extractor(std::unique_ptr<IExtractor> extractor) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!extractor) {
        throw common::ConfigurationException("Extractor cannot be null");
    }
    pipeline_->set_extractor(std::move(extractor));
    return *this;
}

PipelineBuilder& PipelineBuilder::with_transformer(const std::string& type,
                                                   const std::unordered_map<std::string, std::any>& params) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (type.empty()) {
        throw common::ConfigurationException("Transformer type cannot be empty");
    }

    try {
        auto transformer = create_transformer(type, params);
        context_.set_stage(ProcessingContext::Stage::Transform);
        transformer->initialize(params, context_);
        pipeline_->set_transformer(std::move(transformer));
    } catch (const std::exception& e) {
        throw common::ConfigurationException(
            std::format("Failed to create transformer of type '{}': {}", type, e.what()));
    }

    return *this;
}

PipelineBuilder& PipelineBuilder::with_transformer_for_table(const std::string& table_name) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }

    // Create a mapping transformer specific to the OMOP table
    std::unordered_map<std::string, std::any> params;
    params["target_table"] = table_name;
    params["type"] = "omop_mapping";

    return with_transformer("omop_mapping", params);
}

PipelineBuilder& PipelineBuilder::with_transformer(std::unique_ptr<ITransformer> transformer) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!transformer) {
        throw common::ConfigurationException("Transformer cannot be null");
    }
    pipeline_->set_transformer(std::move(transformer));
    return *this;
}

PipelineBuilder& PipelineBuilder::with_loader(const std::string& type,
                                              const std::unordered_map<std::string, std::any>& params) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (type.empty()) {
        throw common::ConfigurationException("Loader type cannot be empty");
    }

    try {
        auto loader = create_loader(type, params);
        ProcessingContext context;
        context.set_stage(ProcessingContext::Stage::Load);
        loader->initialize(params, context);
        pipeline_->set_loader(std::move(loader));
    } catch (const std::exception& e) {
        throw common::ConfigurationException(
            std::format("Failed to create loader of type '{}': {}", type, e.what()));
    }

    return *this;
}

PipelineBuilder& PipelineBuilder::with_loader(std::unique_ptr<ILoader> loader) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!loader) {
        throw common::ConfigurationException("Loader cannot be null");
    }
    pipeline_->set_loader(std::move(loader));
    return *this;
}

PipelineBuilder& PipelineBuilder::with_pre_processor(
    std::function<void(RecordBatch&, ProcessingContext&)> processor) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!processor) {
        throw common::ConfigurationException("Pre-processor cannot be null");
    }
    pipeline_->add_pre_processor(std::move(processor));
    return *this;
}

PipelineBuilder& PipelineBuilder::with_post_processor(
    std::function<void(RecordBatch&, ProcessingContext&)> processor) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!processor) {
        throw common::ConfigurationException("Post-processor cannot be null");
    }
    pipeline_->add_post_processor(std::move(processor));
    return *this;
}

PipelineBuilder& PipelineBuilder::with_progress_callback(
    std::function<void(const JobInfo&)> callback) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!callback) {
        throw common::ConfigurationException("Progress callback cannot be null");
    }
    pipeline_->set_progress_callback(std::move(callback));
    return *this;
}

PipelineBuilder& PipelineBuilder::with_error_callback(
    std::function<void(const std::string&, const std::exception&)> callback) {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not initialized");
    }
    if (!callback) {
        throw common::ConfigurationException("Error callback cannot be null");
    }
    pipeline_->set_error_callback(std::move(callback));
    return *this;
}

std::unique_ptr<ETLPipeline> PipelineBuilder::build() {
    if (!pipeline_) {
        throw common::ConfigurationException("Pipeline not configured");
    }
    
    // Validate that all required components are set
    // Note: The pipeline will validate this in start(), but checking here gives better error messages
    
    return std::move(pipeline_);
}

// PipelineManager implementation
PipelineManager::PipelineManager(size_t max_concurrent_jobs)
    : max_concurrent_jobs_(max_concurrent_jobs) {

    // Start scheduler threads
    for (size_t i = 0; i < max_concurrent_jobs_; ++i) {
        scheduler_threads_.emplace_back(&PipelineManager::scheduler_worker, this);
    }
}

PipelineManager::~PipelineManager() {
    shutdown(true);
}

std::string PipelineManager::submit_job(const std::string& job_name,
                                      std::unique_ptr<ETLPipeline> pipeline) {
    if (!pipeline) {
        throw common::ConfigurationException("Pipeline cannot be null", "job_submission");
    }

    auto job_id = generate_job_id();

    auto entry = std::make_unique<JobEntry>();
    entry->job_id = job_id;
    entry->pipeline = std::move(pipeline);
    entry->info.job_id = job_id;
    entry->info.job_name = job_name;
    entry->info.status = JobStatus::Created;

    {
        std::lock_guard<std::mutex> lock(jobs_mutex_);
        jobs_[job_id] = std::move(entry);
        job_queue_.push(job_id);
    }

    // Notify scheduler threads
    job_cv_.notify_one();

    return job_id;
}

std::optional<JobStatus> PipelineManager::get_job_status(const std::string& job_id) const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it != jobs_.end()) {
        return it->second->info.status;
    }
    return std::nullopt;
}

std::optional<JobInfo> PipelineManager::get_job_info(const std::string& job_id) const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it != jobs_.end()) {
        return it->second->info;
    }
    return std::nullopt;
}

std::vector<JobInfo> PipelineManager::get_all_jobs() const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    std::vector<JobInfo> result;
    result.reserve(jobs_.size());

    for (const auto& [id, entry] : jobs_) {
        result.push_back(entry->info);
    }

    return result;
}

bool PipelineManager::cancel_job(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it == jobs_.end()) {
        return false;
    }

    auto& entry = it->second;
    if (entry->info.status == JobStatus::Running ||
        entry->info.status == JobStatus::Paused) {
        entry->pipeline->stop();
        entry->info.status = JobStatus::Cancelled;
        return true;
    }

    return false;
}

bool PipelineManager::pause_job(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it == jobs_.end()) {
        return false;
    }

    auto& entry = it->second;
    if (entry->info.status == JobStatus::Running) {
        entry->pipeline->pause();
        entry->info.status = JobStatus::Paused;
        return true;
    }

    return false;
}

bool PipelineManager::resume_job(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    if (it == jobs_.end()) {
        return false;
    }

    auto& entry = it->second;
    if (entry->info.status == JobStatus::Paused) {
        entry->pipeline->resume();
        entry->info.status = JobStatus::Running;
        return true;
    }

    return false;
}

bool PipelineManager::wait_for_job(const std::string& job_id, int timeout_ms) {
    auto start = std::chrono::steady_clock::now();

    while (true) {
        {
            std::lock_guard<std::mutex> lock(jobs_mutex_);
            auto it = jobs_.find(job_id);
            if (it == jobs_.end()) {
                return false;
            }

            auto status = it->second->info.status;
            if (status == JobStatus::Completed ||
                status == JobStatus::Failed ||
                status == JobStatus::Cancelled) {
                return true;
            }
        }

        if (timeout_ms >= 0) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start).count();
            if (elapsed >= timeout_ms) {
                return false;
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void PipelineManager::shutdown(bool wait_for_jobs) {
    shutdown_ = true;

    if (!wait_for_jobs) {
        // Cancel all running jobs
        std::lock_guard<std::mutex> lock(jobs_mutex_);
        for (auto& [id, entry] : jobs_) {
            if (entry->info.status == JobStatus::Running ||
                entry->info.status == JobStatus::Paused) {
                entry->pipeline->stop();
                entry->info.status = JobStatus::Cancelled;
            }
        }
    }

    // Wake up all scheduler threads
    job_cv_.notify_all();

    // Wait for scheduler threads to finish
    for (auto& thread : scheduler_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
}

void PipelineManager::scheduler_worker() {
    while (!shutdown_) {
        std::string job_id;

        // Get next job from queue
        {
            std::unique_lock<std::mutex> lock(jobs_mutex_);
            job_cv_.wait(lock, [this] {
                return !job_queue_.empty() || shutdown_;
            });

            if (shutdown_) {
                break;
            }

            if (!job_queue_.empty()) {
                job_id = job_queue_.front();
                job_queue_.pop();
            }
        }

        if (job_id.empty()) {
            continue;
        }

        // Execute the job
        {
            std::lock_guard<std::mutex> lock(jobs_mutex_);
            auto it = jobs_.find(job_id);
            if (it == jobs_.end()) {
                continue;
            }

            auto& entry = it->second;
            entry->info.status = JobStatus::Running;

            // Set up progress callback
            entry->pipeline->set_progress_callback([this, job_id](const JobInfo& info) {
                std::lock_guard<std::mutex> lock(jobs_mutex_);
                auto it = jobs_.find(job_id);
                if (it != jobs_.end()) {
                    it->second->info = info;
                }
            });

            // Start the pipeline asynchronously
            entry->future = entry->pipeline->start(job_id);
        }

        // Wait for job completion without holding the lock
        std::future<JobInfo> job_future;
        {
            std::lock_guard<std::mutex> lock(jobs_mutex_);
            auto it = jobs_.find(job_id);
            if (it != jobs_.end()) {
                job_future = std::move(it->second->future);
            }
        }
        
        if (job_future.valid()) {
            try {
                auto result = job_future.get();
                
                // Update job info after getting result
                std::lock_guard<std::mutex> lock(jobs_mutex_);
                auto it = jobs_.find(job_id);
                if (it != jobs_.end()) {
                    it->second->info = result;
                }
            } catch (const std::exception& e) {
                std::lock_guard<std::mutex> lock(jobs_mutex_);
                auto it = jobs_.find(job_id);
                if (it != jobs_.end()) {
                    it->second->info.status = JobStatus::Failed;
                    it->second->info.error_messages.push_back(e.what());
                }
            }
        }
    }
}

std::string PipelineManager::generate_job_id() {
    return omop::common::CryptoUtils::generate_uuid();
}

} // namespace omop::core