#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <optional>
#include <chrono>

namespace omop::core {

/**
 * @brief Character encoding enumeration
 */
enum class Encoding {
    UTF8,
    UTF16,
    UTF32,
    ASCII,
    ISO8859_1,
    Windows1252,
    CP437,
    CP850,
    CP1252,
    MacRoman,
    Custom
};

/**
 * @brief Byte order mark (BOM) enumeration
 */
enum class BOM {
    None,
    UTF8,
    UTF16BE,
    UTF16LE,
    UTF32BE,
    UTF32LE
};

/**
 * @brief Encoding detection result
 */
struct EncodingDetectionResult {
    Encoding detected_encoding{Encoding::UTF8};
    BOM detected_bom{BOM::None};
    double confidence{0.0}; // 0.0 to 1.0
    std::string encoding_name;
    bool is_valid{false};
    std::vector<std::string> possible_encodings;
};

/**
 * @brief Encoding conversion configuration
 */
struct EncodingConfig {
    Encoding source_encoding{Encoding::UTF8};
    Encoding target_encoding{Encoding::UTF8};
    std::string custom_source_encoding;
    std::string custom_target_encoding;
    bool ignore_invalid_chars{false};
    bool transliterate{false};
    char replacement_char{'?'};
    bool add_bom{false};
    bool remove_bom{false};
    bool validate_utf8{true};
    size_t buffer_size{8192};
    Encoding default_encoding{Encoding::UTF8};
    bool detect_encoding{true};
    bool normalize_text{true};
    std::string normalization_form{"NFC"};
};

/**
 * @brief Encoding statistics
 */
struct EncodingStats {
    size_t total_bytes_processed{0};
    size_t valid_characters{0};
    size_t invalid_characters{0};
    size_t replaced_characters{0};
    size_t conversion_errors{0};
    std::chrono::duration<double> processing_time{0};
    std::unordered_map<std::string, std::any> additional_stats;
};

/**
 * @brief Text encoding interface
 * 
 * This interface defines the contract for text encoding operations
 * including detection, conversion, and validation.
 */
class ITextEncoder {
public:
    virtual ~ITextEncoder() = default;

    /**
     * @brief Initialize encoder
     * @param config Encoding configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const EncodingConfig& config) = 0;

    /**
     * @brief Detect text encoding
     * @param data Input data
     * @param sample_size Sample size for detection (0 = use all data)
     * @return EncodingDetectionResult Detection result
     */
    virtual EncodingDetectionResult detect_encoding(
        const std::vector<uint8_t>& data,
        size_t sample_size = 0) = 0;

    /**
     * @brief Detect encoding from string
     * @param text Input text
     * @return EncodingDetectionResult Detection result
     */
    virtual EncodingDetectionResult detect_encoding(const std::string& text) = 0;

    /**
     * @brief Convert text encoding
     * @param input Input data
     * @param source_encoding Source encoding
     * @param target_encoding Target encoding
     * @return std::vector<uint8_t> Converted data
     */
    virtual std::vector<uint8_t> convert_encoding(
        const std::vector<uint8_t>& input,
        Encoding source_encoding,
        Encoding target_encoding) = 0;

    /**
     * @brief Convert string encoding
     * @param input Input string
     * @param source_encoding Source encoding
     * @param target_encoding Target encoding
     * @return std::string Converted string
     */
    virtual std::string convert_encoding(
        const std::string& input,
        Encoding source_encoding,
        Encoding target_encoding) = 0;

    /**
     * @brief Convert to UTF-8
     * @param input Input data
     * @param source_encoding Source encoding
     * @return std::string UTF-8 string
     */
    virtual std::string to_utf8(
        const std::vector<uint8_t>& input,
        Encoding source_encoding = Encoding::UTF8) = 0;

    /**
     * @brief Convert from UTF-8
     * @param input UTF-8 input string
     * @param target_encoding Target encoding
     * @return std::vector<uint8_t> Converted data
     */
    virtual std::vector<uint8_t> from_utf8(
        const std::string& input,
        Encoding target_encoding) = 0;

    /**
     * @brief Validate UTF-8 string
     * @param input Input string
     * @return bool True if valid UTF-8
     */
    virtual bool is_valid_utf8(const std::string& input) = 0;

    /**
     * @brief Validate encoding
     * @param input Input data
     * @param encoding Encoding to validate against
     * @return bool True if valid
     */
    virtual bool is_valid_encoding(
        const std::vector<uint8_t>& input,
        Encoding encoding) = 0;

    /**
     * @brief Clean invalid characters
     * @param input Input string
     * @param replacement Replacement character
     * @return std::string Cleaned string
     */
    virtual std::string clean_invalid_chars(
        const std::string& input,
        char replacement = '?') = 0;

    /**
     * @brief Normalize text
     * @param input Input string
     * @param form Normalization form (NFC, NFD, NFKC, NFKD)
     * @return std::string Normalized string
     */
    virtual std::string normalize_text(
        const std::string& input,
        const std::string& form = "NFC") = 0;

    /**
     * @brief Detect and strip BOM
     * @param data Input data
     * @return std::pair<BOM, std::vector<uint8_t>> BOM type and data without BOM
     */
    virtual std::pair<BOM, std::vector<uint8_t>> strip_bom(
        const std::vector<uint8_t>& data) = 0;

    /**
     * @brief Add BOM to data
     * @param data Input data
     * @param bom_type BOM type to add
     * @return std::vector<uint8_t> Data with BOM
     */
    virtual std::vector<uint8_t> add_bom(
        const std::vector<uint8_t>& data,
        BOM bom_type) = 0;

    /**
     * @brief Get encoding statistics
     * @return EncodingStats Processing statistics
     */
    virtual EncodingStats get_statistics() = 0;

    /**
     * @brief Reset statistics
     */
    virtual void reset_statistics() = 0;

    /**
     * @brief Get configuration
     * @return EncodingConfig Current configuration
     */
    virtual EncodingConfig get_config() const = 0;

    /**
     * @brief Update configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const EncodingConfig& config) = 0;
};

/**
 * @brief Default text encoder implementation
 */
class TextEncoder : public ITextEncoder {
public:
    TextEncoder() = default;
    ~TextEncoder() override = default;

    bool initialize(const EncodingConfig& config) override;

    EncodingDetectionResult detect_encoding(
        const std::vector<uint8_t>& data,
        size_t sample_size = 0) override;

    EncodingDetectionResult detect_encoding(const std::string& text) override;

    std::vector<uint8_t> convert_encoding(
        const std::vector<uint8_t>& input,
        Encoding source_encoding,
        Encoding target_encoding) override;

    std::string convert_encoding(
        const std::string& input,
        Encoding source_encoding,
        Encoding target_encoding) override;

    std::string to_utf8(
        const std::vector<uint8_t>& input,
        Encoding source_encoding = Encoding::UTF8) override;

    std::vector<uint8_t> from_utf8(
        const std::string& input,
        Encoding target_encoding) override;

    bool is_valid_utf8(const std::string& input) override;

    bool is_valid_encoding(
        const std::vector<uint8_t>& input,
        Encoding encoding) override;

    std::string clean_invalid_chars(
        const std::string& input,
        char replacement = '?') override;

    std::string normalize_text(
        const std::string& input,
        const std::string& form = "NFC") override;

    std::pair<BOM, std::vector<uint8_t>> strip_bom(
        const std::vector<uint8_t>& data) override;

    std::vector<uint8_t> add_bom(
        const std::vector<uint8_t>& data,
        BOM bom_type) override;

    EncodingStats get_statistics() override;
    void reset_statistics() override;
    EncodingConfig get_config() const override;
    bool update_config(const EncodingConfig& config) override;

private:
    EncodingConfig config_;
    EncodingStats stats_;
    std::chrono::steady_clock::time_point start_time_;
    
    BOM detect_bom_from_data(const std::vector<uint8_t>& data);
    bool is_valid_utf8_data(const std::vector<uint8_t>& data, size_t size_to_check);
    bool is_ascii_data(const std::vector<uint8_t>& data, size_t size_to_check);
    bool is_valid_utf8_string(const std::string& input);
    std::string clean_invalid_chars_internal(const std::string& input, char replacement);
    std::string normalize_nfc(const std::string& input);
    std::string normalize_nfd(const std::string& input);
    std::string normalize_nfkc(const std::string& input);
    std::string normalize_nfkd(const std::string& input);
    std::string normalize_compatibility(const std::string& input);
};

/**
 * @brief CSV encoding utilities
 */
class CSVEncodingUtils {
public:
    /**
     * @brief Detect CSV file encoding
     * @param file_path Path to CSV file
     * @return EncodingDetectionResult Detection result
     */
    static EncodingDetectionResult detect_csv_encoding(const std::string& file_path);

    /**
     * @brief Convert CSV file encoding
     * @param input_file Input CSV file path
     * @param output_file Output CSV file path
     * @param source_encoding Source encoding
     * @param target_encoding Target encoding
     * @return bool True if conversion successful
     */
    static bool convert_csv_encoding(
        const std::string& input_file,
        const std::string& output_file,
        Encoding source_encoding,
        Encoding target_encoding);

    /**
     * @brief Validate CSV file encoding
     * @param file_path Path to CSV file
     * @param expected_encoding Expected encoding
     * @return bool True if file matches expected encoding
     */
    static bool validate_csv_encoding(
        const std::string& file_path,
        Encoding expected_encoding);

    /**
     * @brief Clean CSV file encoding issues
     * @param input_file Input CSV file path
     * @param output_file Output CSV file path
     * @param target_encoding Target encoding
     * @return bool True if cleaning successful
     */
    static bool clean_csv_encoding(
        const std::string& input_file,
        const std::string& output_file,
        Encoding target_encoding = Encoding::UTF8);
};

/**
 * @brief Encoding utilities
 */
class EncodingUtils {
public:
    /**
     * @brief Convert encoding enum to string
     * @param encoding Encoding enum
     * @return std::string Encoding name
     */
    static std::string encoding_to_string(Encoding encoding);

    /**
     * @brief Convert string to encoding enum
     * @param encoding_str Encoding name
     * @return std::optional<Encoding> Encoding enum if valid
     */
    static std::optional<Encoding> string_to_encoding(const std::string& encoding_str);

    /**
     * @brief Convert BOM enum to string
     * @param bom BOM enum
     * @return std::string BOM name
     */
    static std::string bom_to_string(BOM bom);

    /**
     * @brief Convert string to BOM enum
     * @param bom_str BOM name
     * @return std::optional<BOM> BOM enum if valid
     */
    static std::optional<BOM> string_to_bom(const std::string& bom_str);

    /**
     * @brief Get BOM bytes
     * @param bom BOM type
     * @return std::vector<uint8_t> BOM bytes
     */
    static std::vector<uint8_t> get_bom_bytes(BOM bom);

    /**
     * @brief Detect BOM from data
     * @param data Input data
     * @return BOM Detected BOM type
     */
    static BOM detect_bom(const std::vector<uint8_t>& data);

    /**
     * @brief Get supported encodings
     * @return std::vector<Encoding> List of supported encodings
     */
    static std::vector<Encoding> get_supported_encodings();

    /**
     * @brief Check if encoding is supported
     * @param encoding Encoding to check
     * @return bool True if supported
     */
    static bool is_encoding_supported(Encoding encoding);

    /**
     * @brief Get encoding byte order
     * @param encoding Encoding
     * @return std::string Byte order (BE, LE, or empty)
     */
    static std::string get_encoding_byte_order(Encoding encoding);

    /**
     * @brief Calculate character count
     * @param data Input data
     * @param encoding Data encoding
     * @return size_t Number of characters
     */
    static size_t calculate_character_count(
        const std::vector<uint8_t>& data,
        Encoding encoding);

    /**
     * @brief Estimate conversion buffer size
     * @param input_size Input size in bytes
     * @param source_encoding Source encoding
     * @param target_encoding Target encoding
     * @return size_t Estimated buffer size
     */
    static size_t estimate_conversion_buffer_size(
        size_t input_size,
        Encoding source_encoding,
        Encoding target_encoding);
};

/**
 * @brief Create text encoder instance
 * @return std::unique_ptr<ITextEncoder> Text encoder instance
 */
std::unique_ptr<ITextEncoder> create_text_encoder();

/**
 * @brief Get default encoding configuration
 * @return EncodingConfig Default configuration
 */
EncodingConfig get_default_encoding_config();

} // namespace omop::core