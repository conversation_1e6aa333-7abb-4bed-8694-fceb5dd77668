/**
 * @file job_scheduler.h
 * @brief Job scheduling system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains the job scheduling system that manages the execution
 * of ETL jobs based on various scheduling strategies.
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <queue>
#include <chrono>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

#include "interfaces.h"
#include "pipeline.h"
#include "job_manager.h"

namespace omop::core {

/**
 * @brief Job scheduling strategy
 */
enum class SchedulingStrategy {
    FIFO,           ///< First In First Out
    PRIORITY,       ///< Priority-based scheduling
    ROUND_ROBIN,    ///< Round-robin scheduling
    FAIR_SHARE,     ///< Fair share between users/groups
    DEADLINE        ///< Deadline-based scheduling
};

/**
 * @brief Scheduled job trigger type
 */
enum class TriggerType {
    MANUAL,         ///< Manual trigger
    SCHEDULED,      ///< Time-based schedule
    EVENT,          ///< Event-based trigger
    DEPENDENCY,     ///< Dependency-based trigger
    FILE_WATCH      ///< File system watch trigger
};

/**
 * @brief Job schedule definition
 */
struct JobSchedule {
    std::string schedule_id;                    ///< Schedule identifier
    std::string job_config_id;                  ///< Job configuration ID
    TriggerType trigger_type{TriggerType::MANUAL}; ///< Trigger type
    std::string cron_expression;                ///< Cron expression for scheduled jobs
    std::chrono::system_clock::time_point next_run; ///< Next scheduled run time
    std::chrono::system_clock::time_point last_run; ///< Last run time
    bool enabled{true};                         ///< Whether schedule is enabled
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::unordered_map<std::string, std::string> parameters; ///< Schedule parameters
};

/**
 * @brief Job queue entry
 */
struct QueuedJob {
    std::string job_id;                         ///< Job identifier
    JobConfig job_config;                       ///< Job configuration
    JobPriority priority;                       ///< Job priority
    std::chrono::system_clock::time_point enqueue_time; ///< Enqueue time
    std::chrono::system_clock::time_point deadline; ///< Job deadline
    std::vector<std::string> dependencies;      ///< Job dependencies
    std::function<void()> callback;             ///< Completion callback
    int retry_count{0};                         ///< Number of retry attempts
};

/**
 * @brief Job scheduler for managing ETL job execution
 *
 * This class provides advanced scheduling capabilities for ETL jobs,
 * including cron-based scheduling, dependency management, and various
 * scheduling strategies.
 */
class JobScheduler {
public:
    /**
     * @brief Constructor
     * @param job_manager Job manager instance
     * @param strategy Scheduling strategy
     */
    JobScheduler(std::shared_ptr<JobManager> job_manager,
                 SchedulingStrategy strategy = SchedulingStrategy::PRIORITY);

    /**
     * @brief Destructor
     */
    ~JobScheduler();

    /**
     * @brief Start the scheduler
     * @return true if started successfully
     */
    bool start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Add a job schedule
     * @param schedule Job schedule definition
     * @return Schedule ID
     */
    std::string addSchedule(const JobSchedule& schedule);

    /**
     * @brief Remove a job schedule
     * @param schedule_id Schedule identifier
     * @return true if removed successfully
     */
    bool removeSchedule(const std::string& schedule_id);

    /**
     * @brief Update a job schedule
     * @param schedule_id Schedule identifier
     * @param schedule Updated schedule
     * @return true if updated successfully
     */
    bool updateSchedule(const std::string& schedule_id, const JobSchedule& schedule);

    /**
     * @brief Enable/disable a schedule
     * @param schedule_id Schedule identifier
     * @param enabled Enable flag
     * @return true if updated successfully
     */
    bool setScheduleEnabled(const std::string& schedule_id, bool enabled);

    /**
     * @brief Get schedule by ID
     * @param schedule_id Schedule identifier
     * @return Schedule if found
     */
    std::optional<JobSchedule> getSchedule(const std::string& schedule_id) const;

    /**
     * @brief Get all schedules
     * @return Vector of all schedules
     */
    std::vector<JobSchedule> getAllSchedules() const;

    /**
     * @brief Submit a job for immediate execution
     * @param job_config Job configuration
     * @param priority Job priority
     * @param dependencies Job dependencies
     * @return Job ID
     */
    std::string submitJob(const JobConfig& job_config,
                         JobPriority priority = JobPriority::NORMAL,
                         const std::vector<std::string>& dependencies = {});

    /**
     * @brief Get queued jobs
     * @return Vector of queued jobs
     */
    std::vector<QueuedJob> getQueuedJobs() const;

    /**
     * @brief Get scheduler statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, std::any> getStatistics() const;

    /**
     * @brief Set scheduling strategy
     * @param strategy New scheduling strategy
     */
    void setSchedulingStrategy(SchedulingStrategy strategy);

    /**
     * @brief Register job completion callback
     * @param callback Callback function
     */
    void registerJobCompletionCallback(
        std::function<void(const std::string&, JobStatus)> callback);

    /**
     * @brief Trigger a scheduled job immediately
     * @param schedule_id Schedule identifier
     * @return Job ID if triggered
     */
    std::optional<std::string> triggerSchedule(const std::string& schedule_id);

private:
    /**
     * @brief Scheduler main loop
     */
    void schedulerLoop();

    /**
     * @brief Process scheduled jobs
     */
    void processScheduledJobs();

    /**
     * @brief Process job queue
     */
    void processJobQueue();

    /**
     * @brief Check job dependencies
     * @param job Queued job
     * @return true if all dependencies satisfied
     */
    bool checkDependencies(const QueuedJob& job) const;

    /**
     * @brief Calculate next run time from cron expression
     * @param cron_expr Cron expression
     * @param from_time Starting time
     * @return Next run time
     */
    std::chrono::system_clock::time_point calculateNextRunTime(
        const std::string& cron_expr,
        std::chrono::system_clock::time_point from_time) const;

    /**
     * @brief Get next job based on scheduling strategy
     * @return Next job to execute
     */
    std::optional<QueuedJob> getNextJob();

    /**
     * @brief Compare jobs for priority queue
     */
    struct JobComparator {
        SchedulingStrategy strategy;

        bool operator()(const QueuedJob& a, const QueuedJob& b) const;
    };

private:
    std::shared_ptr<JobManager> job_manager_;    ///< Job manager
    SchedulingStrategy strategy_;                ///< Scheduling strategy

    std::unordered_map<std::string, JobSchedule> schedules_; ///< Schedules
    mutable std::mutex schedules_mutex_;         ///< Schedules mutex

    std::priority_queue<QueuedJob, std::vector<QueuedJob>, JobComparator> job_queue_; ///< Job queue
    std::mutex queue_mutex_;                     ///< Queue mutex
    std::condition_variable queue_cv_;           ///< Queue condition variable

    std::unordered_map<std::string, JobStatus> completed_jobs_; ///< Completed jobs
    std::mutex completed_mutex_;                 ///< Completed jobs mutex

    std::thread scheduler_thread_;               ///< Scheduler thread
    std::atomic<bool> running_{false};           ///< Running flag

    std::vector<std::function<void(const std::string&, JobStatus)>> completion_callbacks_; ///< Callbacks
    std::mutex callbacks_mutex_;                 ///< Callbacks mutex

    // Statistics
    std::atomic<size_t> jobs_scheduled_{0};      ///< Jobs scheduled counter
    std::atomic<size_t> jobs_executed_{0};       ///< Jobs executed counter
    std::atomic<size_t> jobs_failed_{0};         ///< Jobs failed counter
};

} // namespace omop::core