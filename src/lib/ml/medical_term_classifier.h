#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>

namespace ml {

/**
 * @brief Machine learning classifier for medical term identification
 */
class MedicalTermClassifier {
public:
    MedicalTermClassifier();
    ~MedicalTermClassifier();

    /**
     * @brief Load pre-trained model
     * @param model_path Path to model file
     */
    void load_model(const std::string& model_path);

    /**
     * @brief Classify a term
     * @param term Term to classify
     * @return float Confidence score (0-1) that term is medical
     */
    float classify_term(const std::string& term) const;

    /**
     * @brief Batch classify terms
     * @param terms Vector of terms
     * @return Vector of confidence scores
     */
    std::vector<float> classify_batch(const std::vector<std::string>& terms) const;

    /**
     * @brief Extract features from term
     * @param term Input term
     * @return Feature vector
     */
    std::vector<float> extract_features(const std::string& term) const;

private:
    struct ModelImpl;
    std::unique_ptr<ModelImpl> impl_;

    // Feature extraction helpers
    bool has_medical_suffix(const std::string& term) const;
    bool has_medical_prefix(const std::string& term) const;
    float calculate_character_entropy(const std::string& term) const;
    bool contains_measurement_pattern(const std::string& term) const;
    int count_syllables(const std::string& term) const;
};

} // namespace ml 