#include "medical_term_classifier.h"
#include <algorithm>
#include <cctype>
#include <regex>
#include <cmath>
#include <unordered_set>

namespace ml {

struct MedicalTermClassifier::ModelImpl {
    std::unordered_set<std::string> medical_suffixes;
    std::unordered_set<std::string> medical_prefixes;
    std::unordered_set<std::string> known_medical_terms;
    std::vector<std::regex> measurement_patterns;
    
    ModelImpl() {
        // Initialize medical suffixes
        medical_suffixes = {
            "itis", "osis", "emia", "oma", "oma", "pathy", "algia", "cele",
            "ectasis", "edema", "emia", "genesis", "gram", "graphy", "iasis",
            "itis", "lysis", "malacia", "megaly", "oma", "osis", "pathy",
            "penia", "phagia", "phasia", "phobia", "plasia", "plegia",
            "pnea", "ptosis", "rrhage", "rrhagia", "rrhea", "rrhexis",
            "sclerosis", "scope", "scopy", "stasis", "stomy", "tomy", "trophy"
        };
        
        // Initialize medical prefixes
        medical_prefixes = {
            "a", "an", "anti", "auto", "bi", "brady", "cata", "circum",
            "co", "contra", "counter", "de", "des", "dis", "dys", "ecto",
            "endo", "epi", "eu", "ex", "extra", "hemi", "hyper", "hypo",
            "in", "infra", "inter", "intra", "iso", "macro", "mal", "meta",
            "micro", "mid", "mis", "mono", "multi", "neo", "non", "omni",
            "para", "peri", "poly", "post", "pre", "pro", "proto", "pseudo",
            "quad", "quasi", "re", "retro", "semi", "sub", "super", "supra",
            "sur", "sym", "syn", "tele", "tetra", "trans", "tri", "ultra",
            "un", "uni", "vice"
        };
        
        // Initialize known medical terms (including UK variants)
        known_medical_terms = {
            "paracetamol", "anaemia", "anemia", "oesophagitis", "esophagitis",
            "colour", "tumor", "tumour", "haemoglobin", "hemoglobin",
            "oestrogen", "estrogen", "diarrhoea", "diarrhea", "leucocyte",
            "leukocyte", "platelet", "erythrocyte", "lymphocyte", "neutrophil",
            "eosinophil", "basophil", "monocyte", "thrombocyte", "hypertension",
            "hypotension", "tachycardia", "bradycardia", "arrhythmia",
            "myocardial", "cerebral", "pulmonary", "hepatic", "renal",
            "nephritis", "gastritis", "dermatitis", "pneumonia", "bronchitis"
        };
        
        // Initialize measurement patterns
        measurement_patterns = {
            std::regex(R"(\d+\.?\d*\s*(mg|ml|mcg|g|kg|cm|mm|in|ft|m|km|lb|oz|%)", std::regex::icase),
            std::regex(R"(\d+\.?\d*\s*(mg/kg|ml/kg|mcg/kg|g/kg)", std::regex::icase),
            std::regex(R"(\d+\.?\d*\s*(mg/day|ml/day|mcg/day|g/day)", std::regex::icase),
            std::regex(R"(\d+\.?\d*\s*(mg/hr|ml/hr|mcg/hr|g/hr)", std::regex::icase),
            std::regex(R"(\d+/\d+)", std::regex::icase), // Blood pressure like 120/80
            std::regex(R"(\d+\.?\d*\s*[CF])", std::regex::icase) // Temperature like 37C
        };
    }
};

MedicalTermClassifier::MedicalTermClassifier() : impl_(std::make_unique<ModelImpl>()) {}

MedicalTermClassifier::~MedicalTermClassifier() = default;

void MedicalTermClassifier::load_model(const std::string& model_path) {
    // This is a placeholder implementation
    // In a real implementation, this would load a trained ML model
    // For now, we'll use rule-based classification
}

float MedicalTermClassifier::classify_term(const std::string& term) const {
    if (term.empty()) return 0.0f;
    
    std::string lower_term = term;
    std::transform(lower_term.begin(), lower_term.end(), lower_term.begin(), ::tolower);
    
    float confidence = 0.0f;
    
    // Check if it's a known medical term first
    if (impl_->known_medical_terms.find(lower_term) != impl_->known_medical_terms.end()) {
        return 1.0f; // High confidence for known medical terms
    }
    
    // Check for medical suffixes
    for (const auto& suffix : impl_->medical_suffixes) {
        if (lower_term.length() >= suffix.length() && 
            lower_term.substr(lower_term.length() - suffix.length()) == suffix) {
            confidence += 0.3f;
            break;
        }
    }
    
    // Check for medical prefixes
    for (const auto& prefix : impl_->medical_prefixes) {
        if (lower_term.length() >= prefix.length() && 
            lower_term.substr(0, prefix.length()) == prefix) {
            confidence += 0.2f;
            break;
        }
    }
    
    // Check for measurement patterns
    for (const auto& pattern : impl_->measurement_patterns) {
        if (std::regex_search(lower_term, pattern)) {
            confidence += 0.4f;
            break;
        }
    }
    
    // Check character entropy (medical terms often have higher entropy)
    float entropy = calculate_character_entropy(lower_term);
    if (entropy > 3.0f) {
        confidence += 0.1f;
    }
    
    // Check syllable count (medical terms are often longer)
    int syllables = count_syllables(lower_term);
    if (syllables >= 3) {
        confidence += 0.1f;
    }
    
    return std::min(confidence, 1.0f);
}

std::vector<float> MedicalTermClassifier::classify_batch(const std::vector<std::string>& terms) const {
    std::vector<float> results;
    results.reserve(terms.size());
    
    for (const auto& term : terms) {
        results.push_back(classify_term(term));
    }
    
    return results;
}

std::vector<float> MedicalTermClassifier::extract_features(const std::string& term) const {
    std::vector<float> features;
    
    if (term.empty()) {
        features.resize(10, 0.0f);
        return features;
    }
    
    std::string lower_term = term;
    std::transform(lower_term.begin(), lower_term.end(), lower_term.begin(), ::tolower);
    
    // Feature 1: Has medical suffix
    features.push_back(has_medical_suffix(lower_term) ? 1.0f : 0.0f);
    
    // Feature 2: Has medical prefix
    features.push_back(has_medical_prefix(lower_term) ? 1.0f : 0.0f);
    
    // Feature 3: Contains measurement pattern
    features.push_back(contains_measurement_pattern(lower_term) ? 1.0f : 0.0f);
    
    // Feature 4: Character entropy
    features.push_back(calculate_character_entropy(lower_term));
    
    // Feature 5: Syllable count (normalized)
    features.push_back(static_cast<float>(count_syllables(lower_term)) / 10.0f);
    
    // Feature 6: Length (normalized)
    features.push_back(static_cast<float>(lower_term.length()) / 50.0f);
    
    // Feature 7: Contains numbers
    features.push_back(std::any_of(lower_term.begin(), lower_term.end(), ::isdigit) ? 1.0f : 0.0f);
    
    // Feature 8: Contains special characters
    features.push_back(std::any_of(lower_term.begin(), lower_term.end(), 
        [](char c) { return !std::isalnum(c) && c != ' ' && c != '-'; }) ? 1.0f : 0.0f);
    
    // Feature 9: All uppercase (abbreviations)
    features.push_back(std::all_of(term.begin(), term.end(), ::isupper) ? 1.0f : 0.0f);
    
    // Feature 10: Contains common medical words
    std::unordered_set<std::string> medical_words = {
        "pain", "fever", "cancer", "diabetes", "hypertension", "infection",
        "inflammation", "tumor", "lesion", "syndrome", "disease", "disorder"
    };
    features.push_back(medical_words.count(lower_term) > 0 ? 1.0f : 0.0f);
    
    return features;
}

bool MedicalTermClassifier::has_medical_suffix(const std::string& term) const {
    for (const auto& suffix : impl_->medical_suffixes) {
        if (term.length() >= suffix.length() && 
            term.substr(term.length() - suffix.length()) == suffix) {
            return true;
        }
    }
    return false;
}

bool MedicalTermClassifier::has_medical_prefix(const std::string& term) const {
    for (const auto& prefix : impl_->medical_prefixes) {
        if (term.length() >= prefix.length() && 
            term.substr(0, prefix.length()) == prefix) {
            return true;
        }
    }
    return false;
}

float MedicalTermClassifier::calculate_character_entropy(const std::string& term) const {
    if (term.empty()) return 0.0f;
    
    std::unordered_map<char, int> char_counts;
    for (char c : term) {
        char_counts[c]++;
    }
    
    float entropy = 0.0f;
    float term_length = static_cast<float>(term.length());
    
    for (const auto& pair : char_counts) {
        float probability = pair.second / term_length;
        if (probability > 0.0f) {
            entropy -= probability * std::log2(probability);
        }
    }
    
    return entropy;
}

bool MedicalTermClassifier::contains_measurement_pattern(const std::string& term) const {
    for (const auto& pattern : impl_->measurement_patterns) {
        if (std::regex_search(term, pattern)) {
            return true;
        }
    }
    return false;
}

int MedicalTermClassifier::count_syllables(const std::string& term) const {
    if (term.empty()) return 0;
    
    int syllables = 0;
    bool prev_vowel = false;
    
    std::string vowels = "aeiouy";
    
    for (char c : term) {
        bool is_vowel = vowels.find(std::tolower(c)) != std::string::npos;
        
        if (is_vowel && !prev_vowel) {
            syllables++;
        }
        
        prev_vowel = is_vowel;
    }
    
    // Handle edge cases
    if (syllables == 0 && term.length() > 0) {
        syllables = 1; // At least one syllable
    }
    
    return syllables;
}

} // namespace ml 