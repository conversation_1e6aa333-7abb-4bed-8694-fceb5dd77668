# ML library CMakeLists.txt

add_library(omop_ml STATIC)

set(ML_SOURCES
    medical_term_classifier.cpp
)

set(ML_HEADERS
    medical_term_classifier.h
)

target_sources(omop_ml PRIVATE ${ML_SOURCES})

omop_configure_library(omop_ml
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_common
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        fmt::fmt
        Threads::Threads
    HEADERS
        ${ML_HEADERS}
) 