#include "load/loader_strategies.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <algorithm>
#include <sstream>
#include <thread>
#include <random>

namespace omop::load {

// SingleRecordLoadingStrategy implementation
SingleRecordLoadingStrategy::SingleRecordLoadingStrategy() = default;

SingleRecordLoadingStrategy::~SingleRecordLoadingStrategy() = default;

bool SingleRecordLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("SingleRecordLoadingStrategy initialized");
    
    return true;
}

bool SingleRecordLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->warn("Attempting to load record without active transaction");
        return false;
    }
    
    try {
        // Simulate record loading
        std::this_thread::sleep_for(std::chrono::microseconds(100));
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to load record: {}", e.what());
        
        return false;
    }
}

size_t SingleRecordLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded;
}

bool SingleRecordLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool SingleRecordLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to commit transaction: {}", e.what());
        
        return false;
    }
}

bool SingleRecordLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true;
}

bool SingleRecordLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("SingleRecordLoadingStrategy finalized. Processed {} records in {} ms", 
                metrics_.total_records, metrics_.total_processing_time.count());
    
    return true;
}

LoadingMetrics SingleRecordLoadingStrategy::get_metrics() const {
    return metrics_;
}

std::vector<LoadingMode> SingleRecordLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Insert, LoadingMode::Append};
}

std::vector<std::string> SingleRecordLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.batch_size == 0) {
        errors.push_back("Batch size must be greater than 0");
    }
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    return errors;
}

// BatchInsertLoadingStrategy implementation
BatchInsertLoadingStrategy::BatchInsertLoadingStrategy() = default;

BatchInsertLoadingStrategy::~BatchInsertLoadingStrategy() = default;

bool BatchInsertLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    current_batch_.reserve(config.batch_size);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BatchInsertLoadingStrategy initialized with batch size {}", config.batch_size);
    
    return true;
}

bool BatchInsertLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    if (!transaction_active_) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->warn("Attempting to load record without active transaction");
        return false;
    }
    
    current_batch_.push_back(record);
    
    // Process batch if it's full
    if (current_batch_.size() >= config_.batch_size) {
        return process_current_batch(context);
    }
    
    return true;
}

size_t BatchInsertLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    if (!transaction_active_) {
        return 0;
    }
    
    size_t loaded = 0;
    for (const auto& record : batch) {
        current_batch_.push_back(record);
        loaded++;
        
        // Process batch if it's full
        if (current_batch_.size() >= config_.batch_size) {
            if (!process_current_batch(context)) {
                break;
            }
        }
    }
    
    return loaded;
}

bool BatchInsertLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool BatchInsertLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    // Process any remaining records in the current batch
    if (!current_batch_.empty()) {
        if (!process_current_batch(context)) {
            return false;
        }
    }
    
    try {
        // Simulate transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to commit transaction: {}", e.what());
        
        return false;
    }
}

bool BatchInsertLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    // Clear current batch
    current_batch_.clear();
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true;
}

bool BatchInsertLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BatchInsertLoadingStrategy finalized. Processed {} records in {} batches in {} ms", 
                metrics_.total_records, metrics_.total_batches, metrics_.total_processing_time.count());
    
    return true;
}

LoadingMetrics BatchInsertLoadingStrategy::get_metrics() const {
    return metrics_;
}

std::vector<LoadingMode> BatchInsertLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Insert, LoadingMode::Append};
}

std::vector<std::string> BatchInsertLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.batch_size == 0) {
        errors.push_back("Batch size must be greater than 0");
    }
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    return errors;
}

bool BatchInsertLoadingStrategy::process_current_batch(omop::core::ProcessingContext& context) {
    if (current_batch_.empty()) {
        return true;
    }
    
    try {
        // Simulate batch processing
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        metrics_.total_records += current_batch_.size();
        metrics_.successful_records += current_batch_.size();
        metrics_.total_batches++;
        metrics_.successful_batches++;
        
        current_batch_.clear();
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records += current_batch_.size();
        metrics_.failed_records += current_batch_.size();
        metrics_.total_batches++;
        metrics_.failed_batches++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to process batch: {}", e.what());
        
        current_batch_.clear();
        
        return false;
    }
}

// BulkLoadStrategy implementation
BulkLoadStrategy::BulkLoadStrategy() = default;

BulkLoadStrategy::~BulkLoadStrategy() = default;

bool BulkLoadStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) { 
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BulkLoadStrategy initialized");
    
    return true; 
}

bool BulkLoadStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate bulk record loading
        std::this_thread::sleep_for(std::chrono::microseconds(50));
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        return false;
    }
}

size_t BulkLoadStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) { 
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded; 
}

bool BulkLoadStrategy::begin_transaction(omop::core::ProcessingContext& context) { 
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true; 
}

bool BulkLoadStrategy::commit_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate bulk transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        return false;
    }
}

bool BulkLoadStrategy::rollback_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true; 
}

bool BulkLoadStrategy::finalize(omop::core::ProcessingContext& context) { 
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BulkLoadStrategy finalized. Processed {} records in {} ms", 
                metrics_.total_records, metrics_.total_processing_time.count());
    
    return true; 
}

LoadingMetrics BulkLoadStrategy::get_metrics() const { 
    return metrics_; 
}


std::vector<LoadingMode> BulkLoadStrategy::get_supported_modes() const { 
    return {LoadingMode::Insert, LoadingMode::Replace}; 
}

std::vector<std::string> BulkLoadStrategy::validate_config(const LoadingConfig& config) const { 
    std::vector<std::string> errors;
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    return errors; 
}

// UpsertLoadingStrategy implementation
UpsertLoadingStrategy::UpsertLoadingStrategy() = default;

UpsertLoadingStrategy::~UpsertLoadingStrategy() = default;

bool UpsertLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    insert_count_ = 0;
    update_count_ = 0;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("UpsertLoadingStrategy initialized");
    
    return true;
}

bool UpsertLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->warn("Attempting to load record without active transaction");
        return false;
    }
    
    try {
        // Check if record exists
        bool record_exists = check_record_exists(record);
        
        if (record_exists) {
            // Update existing record
            std::this_thread::sleep_for(std::chrono::microseconds(200));
            update_count_++;
        } else {
            // Insert new record
            std::this_thread::sleep_for(std::chrono::microseconds(150));
            insert_count_++;
        }
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to upsert record: {}", e.what());
        
        return false;
    }
}

size_t UpsertLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded;
}

bool UpsertLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool UpsertLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(15));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to commit transaction: {}", e.what());
        
        return false;
    }
}

bool UpsertLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true;
}

bool UpsertLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("UpsertLoadingStrategy finalized. Processed {} records ({} inserts, {} updates) in {} ms", 
                metrics_.total_records, insert_count_, update_count_, metrics_.total_processing_time.count());
    
    return true;
}

LoadingMetrics UpsertLoadingStrategy::get_metrics() const {
    return metrics_;
}


std::vector<LoadingMode> UpsertLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Upsert, LoadingMode::Merge};
}

std::vector<std::string> UpsertLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    if (config.key_columns.empty()) {
        errors.push_back("Key columns must be specified for upsert operations");
    }
    
    return errors;
}

bool UpsertLoadingStrategy::check_record_exists(const omop::core::Record& record) {
    // Simulate record existence check
    std::this_thread::sleep_for(std::chrono::microseconds(50));
    
    // For demonstration, assume 30% of records already exist
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(1, 100);
    
    return dis(gen) <= 30;
}

// ParallelLoadingStrategy implementation
ParallelLoadingStrategy::ParallelLoadingStrategy() = default;

ParallelLoadingStrategy::~ParallelLoadingStrategy() = default;

bool ParallelLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) { 
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("ParallelLoadingStrategy initialized");
    
    return true; 
}

bool ParallelLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate parallel record loading
        std::this_thread::sleep_for(std::chrono::microseconds(75));
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        return false;
    }
}

size_t ParallelLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) { 
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded; 
}

bool ParallelLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) { 
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true; 
}

bool ParallelLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate parallel transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(8));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        return false;
    }
}

bool ParallelLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true; 
}

bool ParallelLoadingStrategy::finalize(omop::core::ProcessingContext& context) { 
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("ParallelLoadingStrategy finalized. Processed {} records in {} ms", 
                metrics_.total_records, metrics_.total_processing_time.count());
    
    return true; 
}

LoadingMetrics ParallelLoadingStrategy::get_metrics() const { 
    return metrics_; 
}


std::vector<LoadingMode> ParallelLoadingStrategy::get_supported_modes() const { 
    return {LoadingMode::Insert, LoadingMode::Append}; 
}

std::vector<std::string> ParallelLoadingStrategy::validate_config(const LoadingConfig& config) const { 
    std::vector<std::string> errors;
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    if (config.worker_threads == 0) {
        errors.push_back("Worker threads must be greater than 0 for parallel loading");
    }
    
    return errors; 
}

// Factory functions
std::unique_ptr<ILoadingStrategy> LoadingStrategyFactory::create_strategy(LoadingStrategy strategy) {
    switch (strategy) {
        case LoadingStrategy::SingleRecord:
            return std::make_unique<SingleRecordLoadingStrategy>();
        case LoadingStrategy::BatchInsert:
            return std::make_unique<BatchInsertLoadingStrategy>();
        case LoadingStrategy::BulkLoad:
            return std::make_unique<BulkLoadStrategy>();
        case LoadingStrategy::UpsertLoad:
            return std::make_unique<UpsertLoadingStrategy>();
        case LoadingStrategy::ParallelLoad:
            return std::make_unique<ParallelLoadingStrategy>();
        default:
            return std::make_unique<SingleRecordLoadingStrategy>();
    }
}

void LoadingStrategyFactory::register_strategy(
    LoadingStrategy strategy,
    std::function<std::unique_ptr<ILoadingStrategy>()> creator) {
    // Implementation for custom strategy registration
}

std::vector<LoadingStrategy> LoadingStrategyFactory::get_available_strategies() {
    return {
        LoadingStrategy::SingleRecord,
        LoadingStrategy::BatchInsert,
        LoadingStrategy::BulkLoad,
        LoadingStrategy::UpsertLoad,
        LoadingStrategy::ParallelLoad
    };
}

LoadingStrategy LoadingStrategyFactory::get_recommended_strategy(size_t record_count, const std::string& target_system) {
    if (record_count > 1000000) {
        return LoadingStrategy::BulkLoad;
    } else if (record_count > 100000) {
        return LoadingStrategy::ParallelLoad;
    } else if (record_count > 10000) {
        return LoadingStrategy::BatchInsert;
    } else {
        return LoadingStrategy::SingleRecord;
    }
}

std::string LoadingStrategyUtils::strategy_to_string(LoadingStrategy strategy) {
    switch (strategy) {
        case LoadingStrategy::SingleRecord:
            return "single_record";
        case LoadingStrategy::BatchInsert:
            return "batch_insert";
        case LoadingStrategy::BulkLoad:
            return "bulk_load";
        case LoadingStrategy::UpsertLoad:
            return "upsert_load";
        case LoadingStrategy::ParallelLoad:
            return "parallel_load";
        default:
            return "unknown";
    }
}

LoadingStrategy LoadingStrategyUtils::string_to_strategy(const std::string& strategy_str) {
    if (strategy_str == "single_record") return LoadingStrategy::SingleRecord;
    if (strategy_str == "batch_insert") return LoadingStrategy::BatchInsert;
    if (strategy_str == "bulk_load") return LoadingStrategy::BulkLoad;
    if (strategy_str == "upsert_load") return LoadingStrategy::UpsertLoad;
    if (strategy_str == "parallel_load") return LoadingStrategy::ParallelLoad;
    return LoadingStrategy::SingleRecord;
}

std::string LoadingStrategyUtils::mode_to_string(LoadingMode mode) {
    switch (mode) {
        case LoadingMode::Insert:
            return "insert";
        case LoadingMode::Update:
            return "update";
        case LoadingMode::Delete:
            return "delete";
        case LoadingMode::Upsert:
            return "upsert";
        case LoadingMode::Append:
            return "append";
        case LoadingMode::Replace:
            return "replace";
        case LoadingMode::Merge:
            return "merge";
        default:
            return "unknown";
    }
}

LoadingMode LoadingStrategyUtils::string_to_mode(const std::string& mode_str) {
    if (mode_str == "insert") return LoadingMode::Insert;
    if (mode_str == "update") return LoadingMode::Update;
    if (mode_str == "delete") return LoadingMode::Delete;
    if (mode_str == "upsert") return LoadingMode::Upsert;
    if (mode_str == "append") return LoadingMode::Append;
    if (mode_str == "replace") return LoadingMode::Replace;
    if (mode_str == "merge") return LoadingMode::Merge;
    return LoadingMode::Insert;
}

LoadingConfig LoadingStrategyUtils::get_default_config(LoadingStrategy strategy) {
    LoadingConfig config;
    
    switch (strategy) {
        case LoadingStrategy::SingleRecord:
            config.batch_size = 1;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::BatchInsert:
            config.batch_size = 1000;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::BulkLoad:
            config.batch_size = 10000;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::UpsertLoad:
            config.batch_size = 500;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::ParallelLoad:
            config.batch_size = 1000;
            config.worker_threads = 4;
            break;
        default:
            config.batch_size = 1000;
            config.worker_threads = 1;
            break;
    }
    
    return config;
}

LoadingConfig LoadingStrategyUtils::optimize_config(
    const LoadingConfig& config,
    size_t record_count,
    const std::unordered_map<std::string, std::any>& system_info) {
    
    LoadingConfig optimized = config;
    
    // Apply optimizations based on record count and system info
    if (record_count > 1000000) {
        optimized.batch_size = std::max(optimized.batch_size, size_t(10000));
        optimized.worker_threads = std::max(optimized.worker_threads, size_t(8));
    } else if (record_count > 100000) {
        optimized.batch_size = std::max(optimized.batch_size, size_t(5000));
        optimized.worker_threads = std::max(optimized.worker_threads, size_t(4));
    }
    
    return optimized;
}

size_t LoadingStrategyUtils::calculate_optimal_batch_size(
    size_t record_size,
    size_t available_memory,
    const std::string& target_system) {
    
    // Use 5% of available memory for batch processing
    size_t optimal_size = (available_memory * 0.05) / record_size;
    
    // Apply system-specific adjustments
    if (target_system == "postgresql") {
        optimal_size = std::min(optimal_size, size_t(10000));
    } else if (target_system == "mysql") {
        optimal_size = std::min(optimal_size, size_t(5000));
    } else if (target_system == "sqlite") {
        optimal_size = std::min(optimal_size, size_t(1000));
    }
    
    return std::max(optimal_size, size_t(100));
}

} // namespace omop::load