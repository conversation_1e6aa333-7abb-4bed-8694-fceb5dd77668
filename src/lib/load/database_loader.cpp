#include "load/database_loader.h"
#include "common/logging.h"
#include "common/utilities.h"
#include "extract/database_connector.h"
#include <sstream>
#include <iomanip>
#include <format>
#include <shared_mutex>
#include <future>
#include <algorithm>

namespace omop::load {

// Helper function to safely extract typed values from Record
template<typename T>
std::optional<T> safe_any_cast(const std::any& value) {
    if (!value.has_value()) {
        return std::nullopt;
    }
    
    try {
        // Direct cast
        if (value.type() == typeid(T)) {
            return std::any_cast<T>(value);
        }
        
        // Handle numeric conversions
        if constexpr (std::is_arithmetic_v<T>) {
            if (value.type() == typeid(int)) {
                return static_cast<T>(std::any_cast<int>(value));
            } else if (value.type() == typeid(int64_t)) {
                return static_cast<T>(std::any_cast<int64_t>(value));
            } else if (value.type() == typeid(double)) {
                if constexpr (std::is_integral_v<T>) {
                    // Check for safe double to integer conversion
                    double d = std::any_cast<double>(value);
                    if (d >= std::numeric_limits<T>::min() && 
                        d <= std::numeric_limits<T>::max() &&
                        std::trunc(d) == d) {
                        return static_cast<T>(d);
                    }
                } else {
                    return static_cast<T>(std::any_cast<double>(value));
                }
            } else if (value.type() == typeid(float)) {
                return static_cast<T>(std::any_cast<float>(value));
            }
        }
        
        // Handle string to numeric conversions
        if constexpr (std::is_arithmetic_v<T>) {
            if (value.type() == typeid(std::string)) {
                const std::string& str = std::any_cast<const std::string&>(value);
                try {
                    if constexpr (std::is_same_v<T, int>) {
                        return std::stoi(str);
                    } else if constexpr (std::is_same_v<T, int64_t>) {
                        return std::stoll(str);
                    } else if constexpr (std::is_same_v<T, double>) {
                        return std::stod(str);
                    } else if constexpr (std::is_same_v<T, float>) {
                        return std::stof(str);
                    }
                } catch (...) {
                    // String conversion failed
                }
            }
        }
        
        return std::nullopt;
    } catch (...) {
        return std::nullopt;
    }
}

// Replace get_field_as with safe_any_cast usage
template<typename T>
std::optional<T> get_field_as(const core::Record& record, const std::string& field_name) {
    auto field_value = record.getField(field_name);
    if (!field_value.has_value()) {
        return std::nullopt;
    }
    
    return safe_any_cast<T>(field_value);
}

// DatabaseLoader Implementation
DatabaseLoader::DatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                             DatabaseLoaderOptions options)
    : LoaderBase("database"), connection_(std::move(connection)), options_(options) {

    if (!connection_) {
        throw common::LoadException("DatabaseLoader requires a valid connection", "");
    }

    start_time_ = std::chrono::steady_clock::now();
}

DatabaseLoader::~DatabaseLoader() {
    try {
        // Attempt to flush data in destructor as last resort
        if (!data_flushed_.load() && connection_ && connection_->is_connected()) {
            auto logger = common::Logger::get("omop-loader");
            logger->warn("DatabaseLoader destroyed without explicit flush() - attempting automatic flush");
            
            // Try to flush with a short timeout
            try {
                flush_all_buffers();
                if (connection_->in_transaction()) {
                    connection_->commit();
                }
            } catch (const std::exception& e) {
                logger->error("Failed to auto-flush in destructor: {}", e.what());
                // Attempt rollback to leave database in consistent state
                try {
                    connection_->rollback();
                } catch (...) {
                    // Suppress all exceptions in destructor
                }
            }
        }
    } catch (...) {
        // Ensure no exceptions escape destructor
    }
}

void DatabaseLoader::flush() {
    flush_all_buffers();
    data_flushed_ = true;
}

void DatabaseLoader::initialize(const std::unordered_map<std::string, std::any>& config,
                               core::ProcessingContext& context) {
    LoaderBase::initialize(config, context);
}

void DatabaseLoader::do_initialize(const std::unordered_map<std::string, std::any>& config,
                                  [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-loader");
    logger->info("Initializing database loader");

    // Get target table from config
    if (config.find("table_name") != config.end()) {
        target_table_ = std::any_cast<std::string>(config.at("table_name"));
    }

    if (config.find("schema_name") != config.end()) {
        schema_name_ = std::any_cast<std::string>(config.at("schema_name"));
    } else {
        schema_name_ = "cdm"; // Default OMOP schema
    }

    // Override options from config
    if (config.find("batch_size") != config.end()) {
        options_.batch_size = std::any_cast<size_t>(config.at("batch_size"));

        // Validate batch size
        if (options_.batch_size == 0) {
            throw common::ConfigurationException("batch_size must be greater than 0");
        }
    }

    // Validate configuration
    if (options_.commit_interval < options_.batch_size) {
        logger->warn("commit_interval ({}) is less than batch_size ({}), adjusting to batch_size",
                    options_.commit_interval, options_.batch_size);
        options_.commit_interval = options_.batch_size;
    }

    if (config.find("use_bulk_insert") != config.end()) {
        options_.use_bulk_insert = std::any_cast<bool>(config.at("use_bulk_insert"));
    }

    // Validate connection before proceeding
    if (!connection_) {
        throw common::ConfigurationException("Database connection is null");
    }
    
    if (!connection_->is_connected()) {
        throw common::ConfigurationException("Database connection is not established");
    }

    // Prepare target table
    if (!target_table_.empty()) {
        prepare_table(target_table_);
    }

    // Start transaction
    connection_->begin_transaction();

    logger->info("Database loader initialized for table: {}.{}", schema_name_, target_table_);
}

bool DatabaseLoader::load(const core::Record& record, core::ProcessingContext& context) {
    try {
        // Determine target table
        std::string table_name = target_table_;
        if (table_name.empty()) {
            // Try to get from record metadata
            auto metadata = record.metadata();
            auto it = metadata.find("target_table");
            if (it != metadata.end()) {
                table_name = it->second;
            } else {
                throw common::LoadException("No target table specified", "");
            }
        }

        if (options_.use_bulk_insert) {
            // Add to buffer
            auto& buffer = get_buffer(table_name);
            if (buffer.add(record)) {
                // Buffer is full, flush it
                size_t loaded = execute_bulk_insert(buffer);
                total_loaded_ += loaded;
                buffer.clear();
            }
        } else {
            // Direct insert
            if (execute_single_insert(table_name, record)) {
                total_loaded_++;
            } else {
                total_failed_++;
                return false;
            }
        }

        return true;

    } catch (const std::exception& e) {
        context.increment_errors();
        auto logger = common::Logger::get("omop-loader");
        
        // Provide more context in error messages
        std::string error_context = std::format(
            "Failed to load record to table '{}': {}", 
            target_table_.empty() ? "<unknown>" : target_table_, 
            e.what());
        logger->error(error_context);
        total_failed_++;
        
        // Consider connection health
        if (!connection_->is_connected()) {
            throw common::LoadException("Database connection lost", target_table_);
        }
        
        return false;
    }
}

size_t DatabaseLoader::load_batch(const core::RecordBatch& batch,
                                 core::ProcessingContext& context) {
    size_t loaded = 0;

    // Group records by target table
    std::unordered_map<std::string, core::RecordBatch> table_batches;

    for (const auto& record : batch) {
        std::string table_name = target_table_;
        if (table_name.empty()) {
            // Get metadata from record
            auto metadata = record.metadata();
            auto it = metadata.find("target_table");
            if (it != metadata.end()) {
                table_name = it->second;
            }
        }

        if (!table_name.empty()) {
            if (table_batches.find(table_name) == table_batches.end()) {
                table_batches[table_name] = core::RecordBatch();
            }
            table_batches[table_name].addRecord(record);
        }
    }

    // Load each table's batch
    for (auto& [table_name, table_batch] : table_batches) {
        if (options_.use_bulk_insert) {
            auto& buffer = get_buffer(table_name);

            for (const auto& record : table_batch) {
                if (buffer.add(record)) {
                    // Buffer full, flush
                    size_t table_loaded = execute_bulk_insert(buffer);
                    loaded += table_loaded;
                    buffer.clear();
                }
            }
        } else {
            // Load records individually
            for (const auto& record : table_batch) {
                if (load(record, context)) {
                    loaded++;
                }
            }
        }
    }

    total_loaded_ += loaded;
    return loaded;
}

void DatabaseLoader::commit([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-loader");

    try {
        // Flush all buffers before commit
        flush_all_buffers();

        // Mark data as flushed
        data_flushed_ = true;

        // Commit transaction
        connection_->commit();
        commit_count_++;

        // Start new transaction
        connection_->begin_transaction();

        logger->debug("Committed {} records", total_loaded_.load());

    } catch (const std::exception& e) {
        logger->error("Commit failed: {}", e.what());
        throw common::LoadException("Failed to commit transaction", target_table_);
    }
}

void DatabaseLoader::rollback([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-loader");

    try {
        // Clear buffers
        {
            std::lock_guard<std::mutex> lock(buffer_mutex_);
            buffers_.clear();
        }

        // Rollback transaction
        connection_->rollback();

        // Start new transaction
        connection_->begin_transaction();

        logger->warn("Rolled back transaction");

    } catch (const std::exception& e) {
        logger->error("Rollback failed: {}", e.what());
    }
}

void DatabaseLoader::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-loader");

    try {
        // Flush any remaining data
        if (!data_flushed_.load()) {
            flush();
        }

        // Final commit
        if (connection_->is_connected()) {
            connection_->commit();
        }

        // Re-enable constraints if disabled
        if (options_.disable_constraints && !target_table_.empty()) {
            enable_constraints(target_table_);
        }

        // Create indexes if deferred
        if (options_.create_indexes_after_load && !target_table_.empty()) {
            create_deferred_indexes(target_table_);
        }

        auto duration = std::chrono::steady_clock::now() - start_time_;
        logger->info("Database loader finalized - Loaded: {}, Failed: {}, Duration: {}s",
                    total_loaded_.load(), total_failed_.load(),
                    std::chrono::duration_cast<std::chrono::seconds>(duration).count());

    } catch (const std::exception& e) {
        logger->error("Finalization failed: {}", e.what());
    }
}

std::unordered_map<std::string, std::any> DatabaseLoader::get_statistics() const {
    auto duration = std::chrono::steady_clock::now() - start_time_;
    double records_per_second = total_loaded_ > 0
        ? total_loaded_ / std::chrono::duration<double>(duration).count()
        : 0.0;

    return {
        {"total_loaded", total_loaded_.load()},
        {"total_failed", total_failed_.load()},
        {"commit_count", commit_count_.load()},
        {"duration_seconds", std::chrono::duration<double>(duration).count()},
        {"records_per_second", records_per_second},
        {"target_table", target_table_},
        {"batch_size", options_.batch_size},
        {"bulk_insert", options_.use_bulk_insert}
    };
}

void DatabaseLoader::prepare_table(const std::string& table_name) {
    auto logger = common::Logger::get("omop-loader");

    // Validate connection
    if (!connection_) {
        throw common::LoadException("Database connection is null", table_name);
    }

    // Check if table exists
    if (!connection_->table_exists(table_name, schema_name_)) {
        throw common::LoadException(
            std::format("Target table '{}.{}' does not exist", schema_name_, table_name),
            table_name);
    }

    // Truncate if requested
    if (options_.truncate_before_load) {
        logger->warn("Truncating table {}.{}", schema_name_, table_name);
        std::string truncate_sql = std::format("TRUNCATE TABLE {}.{} CASCADE",
                                              schema_name_, table_name);
        connection_->execute_update(truncate_sql);
    }

    // Disable constraints if requested
    if (options_.disable_constraints) {
        logger->info("Disabling constraints for table {}.{}", schema_name_, table_name);
        disable_constraints(table_name);
    }
}

void DatabaseLoader::disable_constraints(const std::string& table_name) {
    auto logger = common::Logger::get("omop-loader");

    try {
        // Implementation depends on database type
        auto db_type = connection_->get_database_type();

        if (db_type == "postgresql") {
            // Disable triggers (includes foreign key checks)
            std::string sql = std::format("ALTER TABLE {}.{} DISABLE TRIGGER ALL",
                                        schema_name_, table_name);
            connection_->execute_update(sql);
        } else if (db_type == "mysql") {
            // Disable foreign key checks for session
            connection_->execute_update("SET FOREIGN_KEY_CHECKS = 0");
        }

        logger->debug("Constraints disabled for table {}.{}", schema_name_, table_name);
    } catch (const std::exception& e) {
        logger->warn("Failed to disable constraints: {}", e.what());
    }
}

void DatabaseLoader::enable_constraints(const std::string& table_name) {
    auto logger = common::Logger::get("omop-loader");

    try {
        auto db_type = connection_->get_database_type();

        if (db_type == "postgresql") {
            // Re-enable triggers
            std::string sql = std::format("ALTER TABLE {}.{} ENABLE TRIGGER ALL",
                                        schema_name_, table_name);
            connection_->execute_update(sql);
        } else if (db_type == "mysql") {
            // Re-enable foreign key checks
            connection_->execute_update("SET FOREIGN_KEY_CHECKS = 1");
        }

        logger->debug("Constraints re-enabled for table {}.{}", schema_name_, table_name);
    } catch (const std::exception& e) {
        logger->warn("Failed to re-enable constraints: {}", e.what());
    }
}

void DatabaseLoader::create_deferred_indexes(const std::string& table_name) {
    auto logger = common::Logger::get("omop-loader");

    try {
        // Get OMOP CDM standard indexes for the table
        auto indexes = get_omop_indexes(table_name);

        for (const auto& index : indexes) {
            std::string sql = std::format("CREATE INDEX IF NOT EXISTS {} ON {}.{} ({})",
                                        index.name, schema_name_, table_name, index.columns);

            logger->info("Creating index: {}", index.name);
            connection_->execute_update(sql);
        }

        logger->info("Created {} indexes for table {}.{}",
                    indexes.size(), schema_name_, table_name);
    } catch (const std::exception& e) {
        logger->error("Failed to create indexes: {}", e.what());
    }
}

std::vector<DatabaseLoader::IndexDefinition> DatabaseLoader::get_omop_indexes(
    const std::string& table_name) {

    std::vector<IndexDefinition> indexes;

    // Define standard OMOP CDM indexes
    if (table_name == "person") {
        indexes.push_back({"idx_person_id", "person_id"});
    } else if (table_name == "visit_occurrence") {
        indexes.push_back({"idx_visit_person_id", "person_id"});
        indexes.push_back({"idx_visit_concept_id", "visit_concept_id"});
    } else if (table_name == "condition_occurrence") {
        indexes.push_back({"idx_condition_person_id", "person_id"});
        indexes.push_back({"idx_condition_visit_id", "visit_occurrence_id"});
        indexes.push_back({"idx_condition_concept_id", "condition_concept_id"});
    } else if (table_name == "drug_exposure") {
        indexes.push_back({"idx_drug_person_id", "person_id"});
        indexes.push_back({"idx_drug_visit_id", "visit_occurrence_id"});
        indexes.push_back({"idx_drug_concept_id", "drug_concept_id"});
    } else if (table_name == "procedure_occurrence") {
        indexes.push_back({"idx_procedure_person_id", "person_id"});
        indexes.push_back({"idx_procedure_visit_id", "visit_occurrence_id"});
        indexes.push_back({"idx_procedure_concept_id", "procedure_concept_id"});
    } else if (table_name == "measurement") {
        indexes.push_back({"idx_measurement_person_id", "person_id"});
        indexes.push_back({"idx_measurement_visit_id", "visit_occurrence_id"});
        indexes.push_back({"idx_measurement_concept_id", "measurement_concept_id"});
    } else if (table_name == "observation") {
        indexes.push_back({"idx_observation_person_id", "person_id"});
        indexes.push_back({"idx_observation_visit_id", "visit_occurrence_id"});
        indexes.push_back({"idx_observation_concept_id", "observation_concept_id"});
    }

    return indexes;
}

size_t DatabaseLoader::execute_bulk_insert(const BulkInsertBuffer& buffer) {
    if (buffer.empty()) {
        return 0;
    }

    auto logger = common::Logger::get("omop-loader");

    try {
        if (!buffer.records().empty()) {
            const auto& first_record = buffer.records().front();
            auto field_names = first_record.getFieldNames();
            std::sort(field_names.begin(), field_names.end());

            // Build parameterized INSERT statement
            std::ostringstream sql;
            sql << "INSERT INTO " << schema_name_ << "." << buffer.table_name() << " (";

            bool first = true;
            for (const auto& field : field_names) {
                if (!first) sql << ", ";
                sql << field;
                first = false;
            }
            sql << ") VALUES ";

            // Build parameterized values
            std::vector<std::vector<std::any>> batch_values;
            for (const auto& record : buffer.records()) {
                std::vector<std::any> row_values;
                for (const auto& field : field_names) {
                    auto value = record.getFieldOptional(field);
                    row_values.push_back(value.value_or(std::any{}));
                }
                batch_values.push_back(std::move(row_values));
            }

            // Use prepared statement for batch insert
            auto stmt = connection_->prepare_statement(sql.str() + " (?" + 
                std::string(field_names.size() - 1, ',') + "?)");
            
            // Execute batch insert using prepared statement
            size_t total_affected = 0;
            for (const auto& row_values : batch_values) {
                stmt->clear_parameters();
                int param_index = 1;
                for (const auto& value : row_values) {
                    stmt->bind(param_index++, value);
                }
                total_affected += stmt->execute_update();
            }

            logger->debug("Bulk inserted {} records into {}", total_affected, buffer.table_name());
            return total_affected;
        }

        return 0;

    } catch (const std::exception& e) {
        logger->error("Bulk insert failed for table {}: {}", buffer.table_name(), e.what());
        throw common::LoadException(
            std::format("Bulk insert failed: {}", e.what()), buffer.table_name());
    }
}

bool DatabaseLoader::execute_single_insert(const std::string& table_name,
                                          const core::Record& record) {
    try {
        // Get or create prepared statement
        std::string stmt_key = table_name + "_insert";

        if (prepared_statements_.find(stmt_key) == prepared_statements_.end()) {
            std::string insert_sql = build_insert_statement(table_name, record);
            prepared_statements_[stmt_key] = connection_->prepare_statement(insert_sql);
        }

        auto& stmt = prepared_statements_[stmt_key];
        stmt->clear_parameters();

        // Bind parameters
        auto field_names = record.getFieldNames();
        std::sort(field_names.begin(), field_names.end()); // Ensure consistent order

        int param_index = 1;
        for (const auto& field : field_names) {
            auto value = record.getFieldOptional(field);
            if (value.has_value()) {
                stmt->bind(param_index++, value.value());
            } else {
                stmt->bind(param_index++, std::any{}); // NULL
            }
        }

        // Execute
        size_t affected = stmt->execute_update();
        return affected > 0;

    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-loader");
        logger->error("Single insert failed for table {}: {}", table_name, e.what());
        return false;
    }
}

std::string DatabaseLoader::build_insert_statement(const std::string& table_name,
                                                  const core::Record& record) {
    // SECURITY: Use parameterized queries instead of string concatenation
    // This method should only build the statement template, not include values
    
    auto field_names = record.getFieldNames();
    
    // Validate table and field names to prevent SQL injection
    auto validate_identifier = [](const std::string& name) {
        // Only allow alphanumeric, underscore
        return std::all_of(name.begin(), name.end(), [](char c) {
            return std::isalnum(c) || c == '_';
        });
    };
    
    if (!validate_identifier(table_name)) {
        throw common::LoadException(
            std::format("Invalid table name: {}", table_name), table_name);
    }
    
    // Continue with parameterized statement building...
    std::sort(field_names.begin(), field_names.end()); // Ensure consistent order

    std::ostringstream sql;
    sql << "INSERT INTO " << schema_name_ << "." << table_name << " (";

    bool first = true;
    for (const auto& field : field_names) {
        if (!first) sql << ", ";
        sql << field;
        first = false;
    }

    sql << ")";

    // For prepared statement, we'll add VALUES clause with placeholders
    if (!options_.use_bulk_insert) {
        sql << " VALUES (";
        first = true;
        for (size_t i = 0; i < field_names.size(); ++i) {
            if (!first) sql << ", ";
            sql << "?";
            first = false;
        }
        sql << ")";
    }

    return sql.str();
}

void DatabaseLoader::flush_all_buffers() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    for (auto& [table_name, buffer] : buffers_) {
        if (!buffer->empty()) {
            execute_bulk_insert(*buffer);
            buffer->clear();
        }
    }
}

BulkInsertBuffer& DatabaseLoader::get_buffer(const std::string& table_name) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    if (buffers_.find(table_name) == buffers_.end()) {
        buffers_[table_name] = std::make_unique<BulkInsertBuffer>(
            table_name, options_.batch_size);
    }

    return *buffers_[table_name];
}

// PostgreSQLLoader Implementation
size_t PostgreSQLLoader::execute_bulk_insert(const BulkInsertBuffer& buffer) {
    if (!options_.use_copy_command || buffer.empty()) {
        return DatabaseLoader::execute_bulk_insert(buffer);
    }

    auto logger = common::Logger::get("omop-loader-pg");

    // PostgreSQL COPY is not directly supported through generic database interface
    // Fall back to regular bulk insert with a warning
    logger->warn("PostgreSQL COPY command not available through generic interface, using bulk INSERT");
    
    // For now, use optimized multi-row INSERT
    return DatabaseLoader::execute_bulk_insert(buffer);
}

// MySQLLoader Implementation
size_t MySQLLoader::execute_bulk_insert(const BulkInsertBuffer& buffer) {
    auto logger = common::Logger::get("omop-loader-mysql");

    try {
        // For MySQL, we can use multi-row INSERT or LOAD DATA LOCAL INFILE
        // For now, use multi-row INSERT
        std::ostringstream sql;
        sql << "INSERT INTO " << schema_name_ << "." << buffer.table_name() << " ";

        if (!buffer.records().empty()) {
            const auto& first_record = buffer.records().front();
            auto field_names = first_record.getFieldNames();
            std::sort(field_names.begin(), field_names.end());

            sql << "(";
            bool first = true;
            for (const auto& field : field_names) {
                if (!first) sql << ", ";
                sql << field;
                first = false;
            }
            sql << ") VALUES ";

            // Build VALUES clauses
            bool first_row = true;
            for (const auto& record : buffer.records()) {
                if (!first_row) sql << ", ";
                sql << "(";

                bool first_field = true;
                for (const auto& field : field_names) {
                    if (!first_field) sql << ", ";

                    auto value = record.getFieldOptional(field);
                    if (value.has_value()) {
                        // Proper SQL escaping for MySQL bulk insert
                        if (value.value().type() == typeid(std::string)) {
                            std::string str_value = std::any_cast<std::string>(value.value());
                            // Escape single quotes and backslashes for MySQL
                            std::string escaped_value;
                            for (char c : str_value) {
                                if (c == '\'' || c == '\\') {
                                    escaped_value += '\\';
                                }
                                escaped_value += c;
                            }
                            sql << "'" << escaped_value << "'";
                        } else if (value.value().type() == typeid(int)) {
                            sql << std::any_cast<int>(value.value());
                        } else if (value.value().type() == typeid(int64_t)) {
                            sql << std::any_cast<int64_t>(value.value());
                        } else if (value.value().type() == typeid(double)) {
                            sql << std::any_cast<double>(value.value());
                        } else if (value.value().type() == typeid(float)) {
                            sql << std::any_cast<float>(value.value());
                        } else {
                            sql << "NULL";
                        }
                    } else {
                        sql << "NULL";
                    }
                    first_field = false;
                }

                sql << ")";
                first_row = false;
            }

            // Execute
            size_t affected = connection_->execute_update(sql.str());
            logger->debug("Bulk inserted {} records into {}", affected, buffer.table_name());
            return affected;
        }

        return 0;

    } catch (const std::exception& e) {
        logger->error("MySQL bulk insert failed: {}", e.what());
        // Fall back to base implementation
        return DatabaseLoader::execute_bulk_insert(buffer);
    }
}

// OmopDatabaseLoader Implementation
OmopDatabaseLoader::OmopDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                                     DatabaseLoaderOptions options)
    : DatabaseLoader(std::move(connection), options) {
}

void OmopDatabaseLoader::initialize(const std::unordered_map<std::string, std::any>& config,
                                   core::ProcessingContext& context) {
    DatabaseLoader::initialize(config, context);
}

void OmopDatabaseLoader::do_initialize(const std::unordered_map<std::string, std::any>& config,
                                   core::ProcessingContext& context) {
    DatabaseLoader::do_initialize(config, context);

    auto logger = common::Logger::get("omop-loader");

    // Get OMOP-specific settings
    if (config.find("omop_table") != config.end()) {
        current_omop_table_ = std::any_cast<std::string>(config.at("omop_table"));
    }

    if (config.find("validate_foreign_keys") != config.end()) {
        validate_foreign_keys_ = std::any_cast<bool>(config.at("validate_foreign_keys"));
    }

    if (config.find("create_missing_tables") != config.end()) {
        create_missing_tables_ = std::any_cast<bool>(config.at("create_missing_tables"));
    }

    // Pre-load foreign key caches if validation is enabled
    if (validate_foreign_keys_) {
        logger->info("Pre-loading foreign key caches for validation");

        try {
            // Load person IDs
            auto result = connection_->execute_query("SELECT person_id FROM cdm.person");
            while (result->next()) {
                person_id_cache_.insert(std::any_cast<int64_t>(result->get_value(0)));
            }
            logger->debug("Loaded {} person IDs", person_id_cache_.size());

            // Load visit IDs
            result = connection_->execute_query("SELECT visit_occurrence_id FROM cdm.visit_occurrence");
            while (result->next()) {
                visit_id_cache_.insert(std::any_cast<int64_t>(result->get_value(0)));
            }
            logger->debug("Loaded {} visit IDs", visit_id_cache_.size());

            // Load common concept IDs
            result = connection_->execute_query(
                "SELECT DISTINCT concept_id FROM cdm.concept WHERE standard_concept = 'S'");
            while (result->next()) {
                concept_id_cache_.insert(std::any_cast<int32_t>(result->get_value(0)));
            }
            logger->debug("Loaded {} concept IDs", concept_id_cache_.size());

        } catch (const std::exception& e) {
            logger->warn("Failed to pre-load foreign key caches: {}", e.what());
            validate_foreign_keys_ = false;
        }
    }
}

bool OmopDatabaseLoader::load(const core::Record& record, core::ProcessingContext& context) {
    // Convert to OMOP table format if needed
    if (!current_omop_table_.empty()) {
        auto omop_table = convert_to_omop_table(record, current_omop_table_);

        if (omop_table) {
            // Validate OMOP constraints
            if (validate_foreign_keys_ && !validate_omop_constraints(*omop_table)) {
                context.increment_errors();
                return false;
            }

            // Convert back to record for loading
            core::Record omop_record;
            auto field_names = omop_table->field_names();
            auto field_values = omop_table->field_values();

            for (size_t i = 0; i < field_names.size(); ++i) {
                omop_record.setField(field_names[i], field_values[i]);
            }

            return DatabaseLoader::load(omop_record, context);
        }
    }

    return DatabaseLoader::load(record, context);
}

std::unique_ptr<cdm::OmopTable> OmopDatabaseLoader::convert_to_omop_table(
    const core::Record& record,
    const std::string& table_name) {

    // Create appropriate OMOP table object based on table name
    if (table_name == "person") {
        auto person = std::make_unique<cdm::Person>();

        // Map fields
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            person->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "gender_concept_id")) {
            person->gender_concept_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "year_of_birth")) {
            person->year_of_birth = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "race_concept_id")) {
            person->race_concept_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "ethnicity_concept_id")) {
            person->ethnicity_concept_id = *val;
        }

        // Optional fields
        if (auto val = get_field_as<int32_t>(record, "month_of_birth")) {
            person->month_of_birth = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "day_of_birth")) {
            person->day_of_birth = *val;
        }
        if (auto val = get_field_as<std::string>(record, "person_source_value")) {
            person->person_source_value = *val;
        }

        return person;
    }
    else if (table_name == "visit_occurrence") {
        auto visit = std::make_unique<cdm::VisitOccurrence>();

        if (auto val = get_field_as<int64_t>(record, "visit_occurrence_id")) {
            visit->visit_occurrence_id = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            visit->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "visit_concept_id")) {
            visit->visit_concept_id = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "visit_start_date")) {
            visit->visit_start_date = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "visit_end_date")) {
            visit->visit_end_date = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "visit_type_concept_id")) {
            visit->visit_type_concept_id = *val;
        }

        return visit;
    }
    else if (table_name == "condition_occurrence") {
        auto condition = std::make_unique<cdm::ConditionOccurrence>();

        if (auto val = get_field_as<int64_t>(record, "condition_occurrence_id")) {
            condition->condition_occurrence_id = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            condition->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "condition_concept_id")) {
            condition->condition_concept_id = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "condition_start_date")) {
            condition->condition_start_date = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "visit_occurrence_id")) {
            condition->visit_occurrence_id = *val;
        }

        return condition;
    }
    else if (table_name == "drug_exposure") {
        auto drug = std::make_unique<cdm::DrugExposure>();

        if (auto val = get_field_as<int64_t>(record, "drug_exposure_id")) {
            drug->drug_exposure_id = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            drug->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "drug_concept_id")) {
            drug->drug_concept_id = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "drug_exposure_start_date")) {
            drug->drug_exposure_start_date = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "drug_type_concept_id")) {
            drug->drug_type_concept_id = *val;
        }

        return drug;
    }
    else if (table_name == "measurement") {
        auto measurement = std::make_unique<cdm::Measurement>();

        if (auto val = get_field_as<int64_t>(record, "measurement_id")) {
            measurement->measurement_id = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            measurement->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "measurement_concept_id")) {
            measurement->measurement_concept_id = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "measurement_date")) {
            measurement->measurement_date = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "measurement_type_concept_id")) {
            measurement->measurement_type_concept_id = *val;
        }

        return measurement;
    }
    else if (table_name == "observation") {
        auto observation = std::make_unique<cdm::Observation>();

        if (auto val = get_field_as<int64_t>(record, "observation_id")) {
            observation->observation_id = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            observation->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "observation_concept_id")) {
            observation->observation_concept_id = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "observation_date")) {
            observation->observation_date = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "observation_type_concept_id")) {
            observation->observation_type_concept_id = *val;
        }

        return observation;
    }
    else if (table_name == "procedure_occurrence") {
        auto procedure = std::make_unique<cdm::ProcedureOccurrence>();

        if (auto val = get_field_as<int64_t>(record, "procedure_occurrence_id")) {
            procedure->procedure_occurrence_id = *val;
        }
        if (auto val = get_field_as<int64_t>(record, "person_id")) {
            procedure->person_id = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "procedure_concept_id")) {
            procedure->procedure_concept_id = *val;
        }
        if (auto val = get_field_as<std::chrono::system_clock::time_point>(record, "procedure_date")) {
            procedure->procedure_date = *val;
        }
        if (auto val = get_field_as<int32_t>(record, "procedure_type_concept_id")) {
            procedure->procedure_type_concept_id = *val;
        }

        return procedure;
    }

    auto logger = common::Logger::get("omop-loader");
    logger->warn("Unsupported OMOP table type: {}", table_name);
    return nullptr;
}

bool OmopDatabaseLoader::validate_omop_constraints(const cdm::OmopTable& table) {
    auto logger = common::Logger::get("omop-loader");

    // First validate using the table's own validation
    if (!table.validate()) {
        auto errors = table.validation_errors();
        for (const auto& error : errors) {
            logger->warn("OMOP validation error: {}", error);
        }
        return false;
    }

    // Additional foreign key validation
    if (!validate_foreign_keys_) {
        return true;
    }

    // Example: Validate person table
    if (table.table_name() == "person") {
        // Person table has no foreign keys to validate
        return true;
    }

    // Validate visit_occurrence table
    else if (table.table_name() == "visit_occurrence") {
        const auto& visit = static_cast<const cdm::VisitOccurrence&>(table);

        // Check person_id exists
        std::shared_lock<std::shared_mutex> lock(cache_mutex_);
        if (person_id_cache_.find(visit.person_id) == person_id_cache_.end()) {
            logger->warn("Foreign key validation failed: person_id {} not found", visit.person_id);
            return false;
        }

        // Check concept IDs
        if (concept_id_cache_.find(visit.visit_concept_id) == concept_id_cache_.end()) {
            logger->warn("Foreign key validation failed: visit_concept_id {} not found",
                        visit.visit_concept_id);
            return false;
        }
    }

    // Validate condition_occurrence table
    else if (table.table_name() == "condition_occurrence") {
        const auto& condition = static_cast<const cdm::ConditionOccurrence&>(table);

        std::shared_lock<std::shared_mutex> lock(cache_mutex_);

        // Check person_id
        if (person_id_cache_.find(condition.person_id) == person_id_cache_.end()) {
            logger->warn("Foreign key validation failed: person_id {} not found", condition.person_id);
            return false;
        }

        // Check visit_occurrence_id if present
        if (condition.visit_occurrence_id.has_value() &&
            visit_id_cache_.find(*condition.visit_occurrence_id) == visit_id_cache_.end()) {
            logger->warn("Foreign key validation failed: visit_occurrence_id {} not found",
                        *condition.visit_occurrence_id);
            return false;
        }

        // Check concept IDs
        if (concept_id_cache_.find(condition.condition_concept_id) == concept_id_cache_.end()) {
            logger->warn("Foreign key validation failed: condition_concept_id {} not found",
                        condition.condition_concept_id);
            return false;
        }
    }

    // Validate drug_exposure table
    else if (table.table_name() == "drug_exposure") {
        const auto& drug = static_cast<const cdm::DrugExposure&>(table);

        std::shared_lock<std::shared_mutex> lock(cache_mutex_);

        if (person_id_cache_.find(drug.person_id) == person_id_cache_.end()) {
            logger->warn("Foreign key validation failed: person_id {} not found", drug.person_id);
            return false;
        }

        if (drug.visit_occurrence_id.has_value() &&
            visit_id_cache_.find(*drug.visit_occurrence_id) == visit_id_cache_.end()) {
            logger->warn("Foreign key validation failed: visit_occurrence_id {} not found",
                        *drug.visit_occurrence_id);
            return false;
        }
    }

    // Similar validation for other tables...

    return true;
}

void OmopDatabaseLoader::handle_foreign_key_constraints(const std::string& table_name, bool enable) {
    auto logger = common::Logger::get("omop-loader");

    try {
        auto db_type = connection_->get_database_type();

        if (db_type == "postgresql") {
            if (enable) {
                // Re-enable all foreign key constraints for the table
                auto result = connection_->execute_query(std::format(
                    "SELECT conname FROM pg_constraint "
                    "WHERE conrelid = '{}.{}'::regclass AND contype = 'f'",
                    schema_name_, table_name));

                while (result->next()) {
                    std::string constraint_name = std::any_cast<std::string>(result->get_value(0));
                    std::string sql = std::format("ALTER TABLE {}.{} "
                                                "VALIDATE CONSTRAINT {}",
                                                schema_name_, table_name, constraint_name);
                    connection_->execute_update(sql);
                }
            } else {
                // Disable foreign key constraints
                auto result = connection_->execute_query(std::format(
                    "SELECT conname FROM pg_constraint "
                    "WHERE conrelid = '{}.{}'::regclass AND contype = 'f'",
                    schema_name_, table_name));

                while (result->next()) {
                    std::string constraint_name = std::any_cast<std::string>(result->get_value(0));
                    std::string sql = std::format("ALTER TABLE {}.{} "
                                                "ALTER CONSTRAINT {} NOT VALID",
                                                schema_name_, table_name, constraint_name);
                    connection_->execute_update(sql);
                }
            }
        } else if (db_type == "mysql") {
            // MySQL uses global foreign key checks per session
            connection_->execute_update(enable ? "SET FOREIGN_KEY_CHECKS = 1"
                                              : "SET FOREIGN_KEY_CHECKS = 0");
        }

        logger->debug("Foreign key constraints {} for table {}.{}",
                     enable ? "enabled" : "disabled", schema_name_, table_name);

    } catch (const std::exception& e) {
        logger->error("Failed to {} foreign key constraints: {}",
                     enable ? "enable" : "disable", e.what());
        throw;
    }
}

void OmopDatabaseLoader::create_table_indexes(const std::string& table_name) {
    auto logger = common::Logger::get("omop-loader");

    try {
        // Get standard OMOP indexes for the table
        auto indexes = DatabaseLoader::get_omop_indexes(table_name);

        for (const auto& index : indexes) {
            std::string sql = std::format("CREATE INDEX IF NOT EXISTS {} ON {}.{} ({})",
                                        index.name, schema_name_, table_name, index.columns);

            logger->info("Creating index: {}", index.name);
            connection_->execute_update(sql);
        }

        // Create additional indexes based on table type
        if (table_name == "concept") {
            // Create covering index for vocabulary queries
            connection_->execute_update(std::format(
                "CREATE INDEX IF NOT EXISTS idx_concept_code ON {}.concept "
                "(concept_code, vocabulary_id, standard_concept)",
                schema_name_));
        }

        logger->info("Successfully created indexes for table {}.{}", schema_name_, table_name);

    } catch (const std::exception& e) {
        logger->error("Failed to create indexes for table {}: {}", table_name, e.what());
        throw;
    }
}

// ParallelDatabaseLoader Implementation
ParallelDatabaseLoader::ParallelDatabaseLoader(
    std::function<std::unique_ptr<extract::IDatabaseConnection>()> connection_factory,
    size_t num_workers,
    DatabaseLoaderOptions options)
    : connection_factory_(connection_factory), num_workers_(num_workers), options_(options) {
}

ParallelDatabaseLoader::~ParallelDatabaseLoader() {
    shutdown_ = true;
    queue_cv_.notify_all();

    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

void ParallelDatabaseLoader::initialize(const std::unordered_map<std::string, std::any>& config,
                                       core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-parallel-loader");
    logger->info("Initializing parallel database loader with {} workers", num_workers_);

    // Create loader instances for each worker
    for (size_t i = 0; i < num_workers_; ++i) {
        auto connection = connection_factory_();
        auto loader = std::make_unique<DatabaseLoader>(std::move(connection), options_);
        loader->initialize(config, context);
        loaders_.push_back(std::move(loader));
    }

    // Start worker threads
    for (size_t i = 0; i < num_workers_; ++i) {
        workers_.emplace_back(&ParallelDatabaseLoader::worker_thread, this, i);
    }
}

bool ParallelDatabaseLoader::load(const core::Record& record, core::ProcessingContext& context) {
    // Create a single-record batch
    core::RecordBatch batch;
    batch.addRecord(record);

    return load_batch(batch, context) > 0;
}

size_t ParallelDatabaseLoader::load_batch(const core::RecordBatch& batch,
                                         [[maybe_unused]] core::ProcessingContext& context) {
    // Distribute batch across workers
    auto sub_batches = distribute_batch(batch);

    // Submit sub-batches to queue
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        for (auto& sub_batch : sub_batches) {
            work_queue_.push(std::move(sub_batch));
        }
    }

    queue_cv_.notify_all();

    // For simplicity, assume all records will be processed
    // In real implementation, would track results
    return batch.size();
}

void ParallelDatabaseLoader::commit(core::ProcessingContext& context) {
    // Wait for queue to empty
    std::unique_lock<std::mutex> lock(queue_mutex_);
    queue_cv_.wait(lock, [this] { return work_queue_.empty(); });

    // Commit all loaders
    for (auto& loader : loaders_) {
        loader->commit(context);
    }
}

void ParallelDatabaseLoader::rollback(core::ProcessingContext& context) {
    // Clear queue
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        while (!work_queue_.empty()) {
            work_queue_.pop();
        }
    }

    // Rollback all loaders
    for (auto& loader : loaders_) {
        loader->rollback(context);
    }
}

void ParallelDatabaseLoader::finalize(core::ProcessingContext& context) {
    // Signal shutdown
    shutdown_ = true;
    queue_cv_.notify_all();

    // Wait for workers to finish
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }

    // Finalize all loaders
    for (auto& loader : loaders_) {
        loader->finalize(context);
    }
}

std::unordered_map<std::string, std::any> ParallelDatabaseLoader::get_statistics() const {
    std::unordered_map<std::string, std::any> combined_stats = {
        {"loader_type", std::string("parallel_database")},
        {"num_workers", num_workers_},
        {"total_loaded", total_loaded_.load()},
        {"total_failed", total_failed_.load()}
    };

    // Aggregate statistics from all loaders
    for (size_t i = 0; i < loaders_.size(); ++i) {
        auto loader_stats = loaders_[i]->get_statistics();
        combined_stats[std::format("worker_{}_loaded", i)] = loader_stats["total_loaded"];
    }

    return combined_stats;
}

void ParallelDatabaseLoader::worker_thread(size_t worker_id) {
    auto logger = common::Logger::get("omop-parallel-loader");
    logger->debug("Worker {} started", worker_id);

    core::ProcessingContext context;
    auto& loader = loaders_[worker_id];

    while (!shutdown_) {
        core::RecordBatch batch;

        // Get batch from queue
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] {
                return !work_queue_.empty() || shutdown_;
            });

            if (shutdown_ && work_queue_.empty()) {
                break;
            }

            if (!work_queue_.empty()) {
                batch = std::move(work_queue_.front());
                work_queue_.pop();
            }
        }

        // Process batch
        if (!batch.empty()) {
            try {
                size_t loaded = loader->load_batch(batch, context);
                total_loaded_.fetch_add(loaded);
                total_failed_.fetch_add(batch.size() - loaded);
            } catch (const std::exception& e) {
                logger->error("Worker {} failed to process batch: {}", worker_id, e.what());
                total_failed_.fetch_add(batch.size());
            }
        }
    }

    logger->debug("Worker {} stopped", worker_id);
}

std::vector<core::RecordBatch> ParallelDatabaseLoader::distribute_batch(
    const core::RecordBatch& batch) {

    std::vector<core::RecordBatch> sub_batches(num_workers_);

    // Simple round-robin distribution
    size_t worker_idx = 0;
    for (const auto& record : batch) {
        sub_batches[worker_idx].addRecord(record);
        worker_idx = (worker_idx + 1) % num_workers_;
    }

    // Remove empty batches
    sub_batches.erase(
        std::remove_if(sub_batches.begin(), sub_batches.end(),
                      [](const core::RecordBatch& b) { return b.empty(); }),
        sub_batches.end());

    return sub_batches;
}

// LoaderFactory Implementation
std::unique_ptr<core::ILoader> LoaderFactory::create(
    const std::string& type,
    std::unique_ptr<extract::IDatabaseConnection> connection,
    const DatabaseLoaderOptions& options) {

    if (type == "database") {
        return std::make_unique<DatabaseLoader>(std::move(connection), options);
    } else if (type == "postgresql") {
        return std::make_unique<PostgreSQLLoader>(std::move(connection), options);
    } else if (type == "mysql") {
        return std::make_unique<MySQLLoader>(std::move(connection), options);
    } else if (type == "omop_database") {
        return std::make_unique<OmopDatabaseLoader>(std::move(connection), options);
    }

    throw common::LoadException(std::format("Unknown loader type: {}", type), "");
}

void LoaderFactory::register_loaders() {
    // Register loader types with the main component factory
    // Create a static factory instance since ComponentFactory doesn't have instance()
    static core::ComponentFactory<core::ILoader> factory;

    factory.register_creator("database",
        []() {
            // Simplified factory - would need proper configuration handling
            DatabaseLoaderOptions options;
            std::unique_ptr<extract::IDatabaseConnection> connection = nullptr;

            return std::make_unique<DatabaseLoader>(std::move(connection), options);
        });

    factory.register_creator("postgresql",
        []() {
            DatabaseLoaderOptions options;
            options.use_copy_command = true;
            std::unique_ptr<extract::IDatabaseConnection> connection = nullptr;
            return std::make_unique<PostgreSQLLoader>(std::move(connection), options);
        });

    factory.register_creator("mysql",
        []() {
            DatabaseLoaderOptions options;
            options.use_bulk_insert = true;
            std::unique_ptr<extract::IDatabaseConnection> connection = nullptr;
            return std::make_unique<MySQLLoader>(std::move(connection), options);
        });

    factory.register_creator("omop_database",
        []() {
            DatabaseLoaderOptions options;
            // Note: validate_foreign_keys option would need to be added to DatabaseLoaderOptions
            std::unique_ptr<extract::IDatabaseConnection> connection = nullptr;
            return std::make_unique<OmopDatabaseLoader>(std::move(connection), options);
        });
}

} // namespace omop::load