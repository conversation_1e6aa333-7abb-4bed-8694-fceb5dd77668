#include "load/batch_loader.h"
#include "load/additional_loaders.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <unordered_set>
#include <algorithm>
#include <numeric>
#include <zlib.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/mman.h>
#include <fcntl.h>
#include <unistd.h>
#endif

namespace omop::load {

// BatchLoaderOptions validation
void BatchLoaderOptions::validate() const {
    if (batch_size == 0) {
        throw common::ConfigurationException("batch_size must be greater than 0");
    }
    
    if (max_batches_in_memory == 0) {
        throw common::ConfigurationException("max_batches_in_memory must be greater than 0");
    }
    
    if (parallel_processing && worker_threads == 0) {
        throw common::ConfigurationException("worker_threads must be greater than 0 when parallel_processing is enabled");
    }
    
    if (enable_compression && compression_type != "gzip") {
        throw common::ConfigurationException("Unsupported compression type: " + compression_type + ". Only 'gzip' is supported.");
    }
    
    if (flush_interval_ms > 0 && flush_interval_ms < 100) {
        throw common::ConfigurationException("flush_interval_ms must be at least 100ms to avoid excessive CPU usage");
    }
}

// EnhancedBatch Implementation
EnhancedBatch::EnhancedBatch(size_t batch_id, size_t capacity)
    : batch_id_(batch_id), capacity_(capacity) {
    records_.reserve(capacity);
    statistics_.creation_time = std::chrono::steady_clock::now();
}

bool EnhancedBatch::add_record(const core::Record& record) {
    records_.addRecord(record);
    statistics_.records_in_batch = records_.size();

    // Estimate size (simplified - real implementation would be more accurate)
    // Estimate record size (simplified)
    statistics_.batch_size_bytes += 100; // Rough estimate

    return is_full();
}

void EnhancedBatch::clear() {
    records_ = core::RecordBatch();
    statistics_ = BatchStatistics();
    statistics_.creation_time = std::chrono::steady_clock::now();
}

void EnhancedBatch::sort(std::function<std::any(const core::Record&)> key_extractor,
                        bool in_place) {
    if (in_place && records_.supportsMutableAccess()) {
        // Sort in-place if the RecordBatch supports it
        records_.sortInPlace([&key_extractor](const core::Record& a, const core::Record& b) {
            try {
                auto key_a = key_extractor(a);
                auto key_b = key_extractor(b);
                
                // Handle different types with a type-aware comparison
                if (key_a.type() == typeid(std::string) && key_b.type() == typeid(std::string)) {
                    return std::any_cast<std::string>(key_a) < std::any_cast<std::string>(key_b);
                } else if (key_a.type() == typeid(int) && key_b.type() == typeid(int)) {
                    return std::any_cast<int>(key_a) < std::any_cast<int>(key_b);
                } else if (key_a.type() == typeid(double) && key_b.type() == typeid(double)) {
                    return std::any_cast<double>(key_a) < std::any_cast<double>(key_b);
                }
                // Default: convert to string for comparison
                return common::any_to_string(key_a) < common::any_to_string(key_b);
            } catch (...) {
                return false; // Maintain order if comparison fails
            }
        });
    } else {
        // Fall back to copy-based sorting
        auto sorted_batch = records_.sorted(key_extractor);
        records_ = std::move(sorted_batch);
    }
}

size_t EnhancedBatch::deduplicate(std::function<std::string(const core::Record&)> key_extractor,
                                 bool in_place) {
    std::unordered_set<std::string> seen_keys;
    size_t original_size = records_.size();
    
    if (in_place && records_.supportsMutableAccess()) {
        // In-place deduplication
        records_.removeIf([&seen_keys, &key_extractor](const core::Record& record) {
            std::string key;
            try {
                key = key_extractor(record);
            } catch (...) {
                // If key extraction fails, keep the record
                return false;
            }
            
            return !seen_keys.insert(key).second;  // Remove if already seen
        });
    } else {
        // Create a new batch without duplicates
        core::RecordBatch deduplicated;
        for (const auto& record : records_) {
            std::string key;
            try {
                key = key_extractor(record);
            } catch (...) {
                // If key extraction fails, keep the record
                deduplicated.addRecord(record);
                continue;
            }
            
            if (seen_keys.insert(key).second) {
                deduplicated.addRecord(record);
            }
        }
        records_ = std::move(deduplicated);
    }

    size_t duplicates_removed = original_size - records_.size();
    statistics_.records_in_batch = records_.size();

    return duplicates_removed;
}

std::vector<uint8_t> EnhancedBatch::compress(const std::string& compression_type) {
    if (compression_type != "gzip") {
        throw common::LoadException("Unsupported compression type: " + compression_type, "");
    }

    // Serialize batch to string (simplified)
    std::ostringstream oss;
    for (const auto& record : records_) {
        for (const auto& field : record.getFieldNames()) {
            oss << field << ":" << omop::common::any_to_string(record.getField(field)) << ";";
        }
        oss << "\n";
    }

    std::string data = oss.str();

    // Compress using zlib
    z_stream zs = {};
    if (deflateInit2(&zs, Z_DEFAULT_COMPRESSION, Z_DEFLATED,
                     15 + 16, 8, Z_DEFAULT_STRATEGY) != Z_OK) {
        throw common::LoadException("Failed to initialize compression", "");
    }

    zs.next_in = reinterpret_cast<Bytef*>(const_cast<char*>(data.data()));
    zs.avail_in = data.size();

    std::vector<uint8_t> compressed;
    compressed.resize(deflateBound(&zs, data.size()));

    zs.next_out = compressed.data();
    zs.avail_out = compressed.size();

    if (deflate(&zs, Z_FINISH) != Z_STREAM_END) {
        deflateEnd(&zs);
        throw common::LoadException("Compression failed", "");
    }

    compressed.resize(zs.total_out);
    deflateEnd(&zs);

    statistics_.compressed_size_bytes = compressed.size();

    return compressed;
}

size_t EnhancedBatch::estimate_memory_usage() const {
    size_t total = sizeof(*this);
    // Estimate memory usage (simplified)
    total += records_.size() * 100;
    return total;
}

// BatchLoader Implementation
BatchLoader::BatchLoader(const std::string& name, BatchLoaderOptions options)
    : LoaderBase(name), options_(options) {
    // Validate options on construction
    options_.validate();
}

BatchLoader::~BatchLoader() {
    // Ensure proper shutdown sequence
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        shutdown_ = true;
    }

    // Wake up all threads
    queue_cv_.notify_all();
    
    // Give threads time to wake up and check shutdown flag
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Join worker threads
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }

    // Join flush thread
    if (flush_thread_.joinable()) {
        flush_thread_.join();
    }
}

bool BatchLoader::load(const core::Record& record, core::ProcessingContext& context) {
    try {
        // Use unique_ptr to manage batch lifecycle
        std::unique_ptr<EnhancedBatch> full_batch;
        
        {
            std::lock_guard<std::mutex> lock(current_batch_mutex_);
            auto& batch = get_current_batch();

            if (batch.add_record(record)) {
                // Batch is full, move it out of the lock
                full_batch = std::move(current_batch_);
                current_batch_ = std::make_unique<EnhancedBatch>(
                    batch_id_counter_.fetch_add(1) + 1, options_.batch_size);
            }
        }
        
        // Submit batch outside of lock to prevent deadlock
        if (full_batch) {
            submit_batch(std::move(full_batch));
        }

        return true;

    } catch (const std::exception& e) {
        record_error(std::format("Failed to add record to batch: {}", e.what()));
        context.increment_errors();
        return false;
    }
}

size_t BatchLoader::load_batch(const core::RecordBatch& batch,
                              core::ProcessingContext& context) {
    size_t loaded = 0;

    for (const auto& record : batch) {
        if (load(record, context)) {
            loaded++;
        }
    }

    return loaded;
}

void BatchLoader::commit(core::ProcessingContext& context) {
    // Flush current batch
    std::unique_ptr<EnhancedBatch> batch_to_submit;
    
    {
        std::lock_guard<std::mutex> lock(current_batch_mutex_);
        if (current_batch_ && current_batch_->size() > 0) {
            batch_to_submit = std::move(current_batch_);
            current_batch_ = std::make_unique<EnhancedBatch>(
                batch_id_counter_.fetch_add(1) + 1, options_.batch_size);
        }
    }
    
    // Submit outside of lock
    if (batch_to_submit) {
        submit_batch(std::move(batch_to_submit));
    }

    // Wait for all batches to be processed with timeout
    auto timeout = std::chrono::steady_clock::now() + std::chrono::seconds(30);
    std::unique_lock<std::mutex> lock(queue_mutex_);
    
    if (!queue_cv_.wait_until(lock, timeout, [this] {
        return batch_queue_.empty() && batches_in_flight_ == 0;
    })) {
        throw common::LoadException("Timeout waiting for batches to complete", get_name());
    }
}

void BatchLoader::rollback(core::ProcessingContext& context) {
    // Clear current batch
    {
        std::lock_guard<std::mutex> lock(current_batch_mutex_);
        current_batch_->clear();
    }

    // Clear queue
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        while (!batch_queue_.empty()) {
            batch_queue_.pop();
        }
    }
}

void BatchLoader::do_initialize(const std::unordered_map<std::string, std::any>& config,
                               core::ProcessingContext& context) {
    // Override options from config
    if (has_config_key(config, "batch_size")) {
        options_.batch_size = get_config_value<size_t>(config, "batch_size", options_.batch_size);
    }

    if (has_config_key(config, "worker_threads")) {
        options_.worker_threads = get_config_value<size_t>(config, "worker_threads", options_.worker_threads);
    }

    if (has_config_key(config, "parallel_processing")) {
        options_.parallel_processing = get_config_value<bool>(config, "parallel_processing", options_.parallel_processing);
    }

    if (has_config_key(config, "enable_compression")) {
        options_.enable_compression = get_config_value<bool>(config, "enable_compression", options_.enable_compression);
    }

    // Initialize current batch
    current_batch_ = std::make_unique<EnhancedBatch>(
        batch_id_counter_.fetch_add(1) + 1, options_.batch_size);

    // Start worker threads
    if (options_.parallel_processing) {
        for (size_t i = 0; i < options_.worker_threads; ++i) {
            workers_.emplace_back(&BatchLoader::worker_thread, this, i);
        }
    }

    // Start flush thread
    if (options_.flush_interval_ms > 0) {
        flush_thread_ = std::thread(&BatchLoader::flush_thread, this);
    }

    auto logger = common::Logger::get("omop-batch-loader");
    if (options_.parallel_processing) {
        logger->info("Batch loader initialized with {} worker threads, batch size: {}",
                    options_.worker_threads, options_.batch_size);
    } else {
        logger->info("Batch loader initialized with synchronous processing, batch size: {}",
                    options_.batch_size);
    }
}

void BatchLoader::do_finalize(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-batch-loader");
    logger->debug("do_finalize: Flushing all batches");
    // Flush any remaining data
    flush_all_batches(context);

    // Signal shutdown
    logger->debug("do_finalize: Setting shutdown_ = true");
    shutdown_ = true;
    queue_cv_.notify_all();

    // Wait for workers to finish
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            logger->debug("do_finalize: Joining worker thread");
            worker.join();
        }
    }

    if (flush_thread_.joinable()) {
        logger->debug("do_finalize: Joining flush thread");
        flush_thread_.join();
        logger->debug("do_finalize: Flush thread joined");
    }
    logger->debug("do_finalize: Complete");
}

std::unordered_map<std::string, std::any> BatchLoader::get_additional_statistics() const {
    return {
        {"batch_size", options_.batch_size},
        {"total_batches_processed", total_batches_processed_.load()},
        {"total_batches_failed", total_batches_failed_.load()},
        {"batches_in_flight", batches_in_flight_.load()},
        {"max_queue_size", max_queue_size_.load()},
        {"current_memory_usage", current_memory_usage_.load()},
        {"compression_enabled", options_.enable_compression},
        {"total_compression_savings", total_compression_savings_.load()},
        {"worker_threads", options_.worker_threads}
    };
}

void BatchLoader::worker_thread(size_t worker_id) {
    auto logger = common::Logger::get("omop-batch-loader");
    logger->debug("Worker thread {} started", worker_id);

    core::ProcessingContext context; // Thread-local context

    while (!shutdown_.load()) {
        std::unique_ptr<EnhancedBatch> batch;

        // Get batch from queue
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] {
                return !batch_queue_.empty() || shutdown_.load();
            });

            if (shutdown_.load()) {
                if (batch_queue_.empty()) {
                    break;
                }
                // Process remaining batches before shutdown
            }

            if (!batch_queue_.empty()) {
                batch = std::move(batch_queue_.front());
                batch_queue_.pop();
                batches_in_flight_++;
            }
        }

        if (batch) {
            try {
                batch->get_mutable_statistics().processing_start_time =
                    std::chrono::steady_clock::now();

                // Apply batch processing options
                if (options_.deduplicate) {
                    std::function<std::string(const core::Record&)> key_extractor =
                        [this](const core::Record& record) -> std::string {
                        if (!options_.sort_key.empty()) {
                            try {
                                auto field = record.getField(options_.sort_key);
                                return field.type().name(); // Simplified
                            } catch (...) {
                                return "";
                            }
                        }
                        return std::to_string(std::hash<std::string>{}(record.getFieldNames().empty() ? "" : record.getFieldNames()[0]));
                    };

                    // size_t duplicates = batch->deduplicate(key_extractor);
                    size_t duplicates = 0; // Simplified for now
                    if (duplicates > 0) {
                        logger->debug("Removed {} duplicates from batch {}",
                                    duplicates, batch->get_batch_id());
                    }
                }

                if (options_.sort_batch && !options_.sort_key.empty()) {
                    std::function<std::any(const core::Record&)> key_extractor =
                        [this](const core::Record& record) -> std::any {
                        try {
                            return record.getField(options_.sort_key);
                        } catch (...) {
                            return std::any{};
                        }
                    };
                    // batch->sort(key_extractor);
                    // Simplified for now - sorting disabled
                }

                // Process the batch
                size_t processed = process_batch(std::move(batch), context);

                if (processed > 0) {
                    update_progress(processed, 0);
                    total_batches_processed_++;
                } else {
                    total_batches_failed_++;
                }

            } catch (const std::exception& e) {
                logger->error("Worker {} failed to process batch: {}", worker_id, e.what());
                total_batches_failed_++;
            }

            // Ensure atomic decrement and notification
            batches_in_flight_.fetch_sub(1);
            queue_cv_.notify_all();
        }
    }

    logger->debug("Worker thread {} stopped", worker_id);
}

void BatchLoader::flush_thread() {
    auto logger = common::Logger::get("omop-batch-loader");
    logger->debug("Flush thread started");

    while (!shutdown_) {
        logger->debug("Flush thread: sleeping for {} ms", options_.flush_interval_ms);
        std::this_thread::sleep_for(std::chrono::milliseconds(options_.flush_interval_ms));

        if (shutdown_) {
            logger->debug("Flush thread: detected shutdown, breaking loop");
            break;
        }

        // Check if current batch needs flushing
        std::unique_ptr<EnhancedBatch> batch_to_submit;
        {
            std::lock_guard<std::mutex> lock(current_batch_mutex_);
            if (current_batch_ && current_batch_->size() > 0) {
                auto age = std::chrono::steady_clock::now() -
                          current_batch_->get_statistics().creation_time;

                if (age > std::chrono::milliseconds(options_.flush_interval_ms)) {
                    logger->debug("Flush thread: submitting batch for flush");
                    batch_to_submit = std::move(current_batch_);
                    current_batch_ = std::make_unique<EnhancedBatch>(
                        batch_id_counter_.fetch_add(1) + 1, options_.batch_size);
                }
            }
        }
        
        // Submit batch outside of lock to avoid deadlock
        if (batch_to_submit) {
            submit_batch(std::move(batch_to_submit));
        }
    }

    logger->debug("Flush thread stopped");
}

void BatchLoader::flush_all_batches(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-batch-loader");
    logger->debug("flush_all_batches: Flushing current batch if needed");
    // Flush current batch
    {
        std::lock_guard<std::mutex> lock(current_batch_mutex_);
        if (current_batch_ && current_batch_->size() > 0) {
            logger->debug("flush_all_batches: Submitting current batch");
            submit_batch(std::move(current_batch_));
        }
    }

    // Wait for all batches to complete
    logger->debug("flush_all_batches: Waiting for batch_queue to empty and batches_in_flight to be 0");
    std::unique_lock<std::mutex> lock(queue_mutex_);
    queue_cv_.wait(lock, [this, logger] {
        logger->debug("flush_all_batches: queue size = {}, batches_in_flight = {}", batch_queue_.size(), batches_in_flight_.load());
        return batch_queue_.empty() && batches_in_flight_ == 0;
    });
    logger->debug("flush_all_batches: Done waiting, all batches processed");
}

void BatchLoader::submit_batch(std::unique_ptr<EnhancedBatch> batch) {
    if (!batch || batch->size() == 0) {
        return;
    }

    // Update memory usage
    current_memory_usage_ += batch->estimate_memory_usage();

    // Check memory limit
    while (current_memory_usage_ > max_memory_usage_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Handle non-parallel processing: process batch immediately
    if (!options_.parallel_processing) {
        core::ProcessingContext context;
        size_t batch_memory = batch->estimate_memory_usage();
        try {
            batch->get_mutable_statistics().processing_start_time =
                std::chrono::steady_clock::now();

            size_t processed = process_batch(std::move(batch), context);
            update_progress(processed, 0);

            // Update memory usage
            current_memory_usage_ -= batch_memory;
        } catch (const std::exception& e) {
            record_error(std::format("Failed to process batch: {}", e.what()));
            current_memory_usage_ -= batch_memory;
        }
        return;
    }

    // Add to queue for parallel processing
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        batch_queue_.push(std::move(batch));

        size_t current_size = batch_queue_.size();
        if (current_size > max_queue_size_) {
            max_queue_size_ = current_size;
        }
    }

    queue_cv_.notify_one();
}

EnhancedBatch& BatchLoader::get_current_batch() {
    // Note: This method assumes the caller holds current_batch_mutex_
    if (!current_batch_) {
        current_batch_ = std::make_unique<EnhancedBatch>(
            batch_id_counter_.fetch_add(1) + 1, options_.batch_size);
    }
    return *current_batch_;
}

EnhancedBatch& BatchLoader::get_current_batch_locked() {
    std::lock_guard<std::mutex> lock(current_batch_mutex_);
    if (!current_batch_) {
        current_batch_ = std::make_unique<EnhancedBatch>(
            batch_id_counter_.fetch_add(1) + 1, options_.batch_size);
    }
    return *current_batch_;
}

// CsvBatchLoader Implementation
CsvBatchLoader::CsvBatchLoader(BatchLoaderOptions options,
                             char delimiter,
                             char quote_char)
    : BatchLoader("csv_batch", options),
      delimiter_(delimiter),
      quote_char_(quote_char) {
}

size_t CsvBatchLoader::process_batch(std::unique_ptr<EnhancedBatch> batch,
                                    core::ProcessingContext& context) {
    if (!batch || batch->size() == 0) {
        return 0;
    }

    std::lock_guard<std::mutex> lock(output_mutex_);

    try {
        // Write header if needed
        if (!header_written_ && !batch->get_records().empty()) {
            const auto& records = batch->get_records();
            if (records.empty()) {
                return 0;
            }
            const auto& first_record = records.getRecords()[0];
            auto field_names = first_record.getFieldNames();
            std::sort(field_names.begin(), field_names.end());
            column_order_ = field_names;

            // Write header
            for (size_t i = 0; i < column_order_.size(); ++i) {
                if (i > 0) output_stream_ << delimiter_;
                output_stream_ << column_order_[i];
            }
            output_stream_ << "\n";
            header_written_ = true;
        }

        // Write records
        size_t written = 0;
        for (const auto& record : batch->get_records()) {
            output_stream_ << format_csv_line(record) << "\n";
            written++;
        }

        output_stream_.flush();

        batch->get_mutable_statistics().processed = true;
        batch->get_mutable_statistics().success = true;
        batch->get_mutable_statistics().processing_end_time =
            std::chrono::steady_clock::now();

        return written;

    } catch (const std::exception& e) {
        batch->get_mutable_statistics().processed = true;
        batch->get_mutable_statistics().success = false;
        batch->get_mutable_statistics().error_message = e.what();
        record_error(std::format("Failed to write CSV batch: {}", e.what()));
        return 0;
    }
}

void CsvBatchLoader::do_initialize(const std::unordered_map<std::string, std::any>& config,
                                  core::ProcessingContext& context) {
    BatchLoader::do_initialize(config, context);

    output_file_ = get_config_value<std::string>(config, "output_file", "output.csv");

    output_stream_.open(output_file_, std::ios::out);
    if (!output_stream_.is_open()) {
        throw common::LoadException(
            std::format("Failed to open CSV output file: {}", output_file_), get_name());
    }
}

std::string CsvBatchLoader::format_csv_line(const core::Record& record) {
    std::ostringstream line;

    for (size_t i = 0; i < column_order_.size(); ++i) {
        if (i > 0) line << delimiter_;

        try {
            auto field = record.getField(column_order_[i]);
            std::string value = omop::common::any_to_string(field); // Use utility function
            line << escape_csv_value(value);
        } catch (...) {
            // Empty string for missing fields
        }
        // Empty string for null values
    }

    return line.str();
}

std::string CsvBatchLoader::escape_csv_value(const std::string& value) {
    // Check if escaping is needed
    if (value.find(delimiter_) == std::string::npos &&
        value.find(quote_char_) == std::string::npos &&
        value.find('\n') == std::string::npos &&
        value.find('\r') == std::string::npos) {
        return value;
    }

    // Escape by quoting
    std::ostringstream escaped;
    escaped << quote_char_;

    for (char c : value) {
        if (c == quote_char_) {
            escaped << quote_char_ << quote_char_; // Double quotes
        } else {
            escaped << c;
        }
    }

    escaped << quote_char_;
    return escaped.str();
}

// MmapBatchLoader implementation
MmapBatchLoader::MmapBatchLoader(BatchLoaderOptions options, size_t file_size_hint)
    : BatchLoader("mmap_batch", options), file_size_hint_(file_size_hint) {
}

MmapBatchLoader::~MmapBatchLoader() {
    try {
        unmap_file();
    } catch (...) {
        // Suppress exceptions in destructor
    }
}

size_t MmapBatchLoader::process_batch(std::unique_ptr<EnhancedBatch> batch,
                                     core::ProcessingContext& context) {
    if (!batch || batch->size() == 0) {
        return 0;
    }

    try {
        size_t written = 0;
        
        // Write each record as binary data to the file
        for (const auto& record : batch->get_records()) {
            // Simple serialization: write field count, then field name/value pairs
            auto field_names = record.getFieldNames();
            size_t field_count = field_names.size();
            
            // Write field count
            output_stream_.write(reinterpret_cast<const char*>(&field_count), sizeof(field_count));
            
            // Write each field
            for (const auto& field_name : field_names) {
                // Write field name length and name
                size_t name_len = field_name.length();
                output_stream_.write(reinterpret_cast<const char*>(&name_len), sizeof(name_len));
                output_stream_.write(field_name.c_str(), name_len);
                
                // Write field value (simplified - just convert to string)
                auto value_opt = record.getFieldOptional(field_name);
                std::string value_str = value_opt.has_value() ? 
                    common::any_to_string(value_opt.value()) : "";
                size_t value_len = value_str.length();
                output_stream_.write(reinterpret_cast<const char*>(&value_len), sizeof(value_len));
                output_stream_.write(value_str.c_str(), value_len);
            }
            
            written++;
            current_offset_ = output_stream_.tellp();
        }
        
        output_stream_.flush();
        
        batch->get_mutable_statistics().processed = true;
        batch->get_mutable_statistics().success = true;
        batch->get_mutable_statistics().processing_end_time = 
            std::chrono::steady_clock::now();

        return written;
        
    } catch (const std::exception& e) {
        batch->get_mutable_statistics().processed = true;
        batch->get_mutable_statistics().success = false;
        batch->get_mutable_statistics().error_message = e.what();
        throw common::LoadException(
            std::format("Failed to write memory-mapped batch: {}", e.what()), 
            get_name());
    }
}

void MmapBatchLoader::do_initialize(const std::unordered_map<std::string, std::any>& config,
                                   core::ProcessingContext& context) {
    BatchLoader::do_initialize(config, context);
    
    mapped_file_path_ = get_config_value<std::string>(config, "output_file", "output.mmap");
    
    // Create and open the output file
    output_stream_.open(mapped_file_path_, std::ios::out | std::ios::binary);
    if (!output_stream_.is_open()) {
        throw common::LoadException(
            std::format("Failed to open memory-mapped file: {}", mapped_file_path_), 
            get_name());
    }
    
    // Initialize with file size hint
    current_offset_ = 0;
    
    auto logger = common::Logger::get("omop-mmap-loader");
    logger->info("Memory-mapped batch loader initialized with file: {}", mapped_file_path_);
}

void MmapBatchLoader::do_finalize(core::ProcessingContext& context) {
    BatchLoader::do_finalize(context);
    unmap_file();
}

void MmapBatchLoader::map_file(const std::string& file_path, size_t size) {
    // For testing and basic functionality, we'll use a simple file-based approach
    // In production, this would use platform-specific mmap() or MapViewOfFile()
    
    // For now, just track the file path and size
    mapped_file_path_ = file_path;
    mapped_size_ = size;
    
    auto logger = common::Logger::get("omop-mmap-loader");
    logger->debug("Memory-mapped file mapping simulated for: {} (size: {} bytes)", 
                  file_path, size);
}

void MmapBatchLoader::unmap_file() {
    if (output_stream_.is_open()) {
        output_stream_.close();
    }
    if (mapped_memory_ != nullptr) {
        mapped_memory_ = nullptr;
        mapped_size_ = 0;
    }
}

void MmapBatchLoader::extend_file(size_t new_size) {
    // For testing and basic functionality, we'll use a simple file-based approach
    // In production, this would use platform-specific file extension and remapping
    
    if (new_size > mapped_size_) {
        mapped_size_ = new_size;
        
        auto logger = common::Logger::get("omop-mmap-loader");
        logger->debug("Memory-mapped file extension simulated to: {} bytes", new_size);
    }
}

// BatchLoaderFactory Implementation
std::unique_ptr<BatchLoader> BatchLoaderFactory::create(const std::string& type,
                                                       const BatchLoaderOptions& options) {
    if (type == "csv_batch") {
        return std::make_unique<CsvBatchLoader>(options);
    } else if (type == "json_batch") {
        return std::make_unique<JsonBatchLoader>(options, JsonBatchLoader::JsonOptions{});
    } else if (type == "mmap_batch") {
        return std::make_unique<MmapBatchLoader>(options);
    }

    throw common::LoadException(
        std::format("Unknown batch loader type: {}", type), "");
}

void BatchLoaderFactory::register_batch_loaders() {
    // Register with the main component factory
    // This would be called during application initialization
}

} // namespace omop::load