#include "load/batch_inserter.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <algorithm>
#include <future>
#include <memory>
#include <sstream>
#include <thread>

namespace omop::load {


BatchInserter::BatchInserter() = default;

BatchInserter::~BatchInserter() {
    stop(true);
}

bool BatchInserter::initialize(const BatchInserterConfig& config) {
    config_ = config;
    
    // Validate configuration
    auto errors = validate_config();
    if (!errors.empty()) {
        return false;
    }

    // Initialize queues and state
    running_ = false;
    batch_id_counter_ = 0;
    
    // Initialize statistics
    stats_ = BatchInserterStats{};
    
    return true;
}

bool BatchInserter::start() {
    if (running_) {
        return false;
    }

    running_ = true;
    
    // Start worker threads
    for (size_t i = 0; i < config_.worker_threads; ++i) {
        workers_.emplace_back(&BatchInserter::worker_thread, this, i);
    }

    auto logger = common::Logger::get("omop-batch-inserter");
    logger->info("BatchInserter started with {} workers", config_.worker_threads);
    
    return true;
}

bool BatchInserter::stop(bool wait_for_completion) {
    if (!running_) {
        return false;
    }

    // Signal shutdown
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        shutdown_ = true;
    }
    queue_cv_.notify_all();

    if (wait_for_completion) {
        // Wait for all workers to complete
        for (auto& worker : workers_) {
            if (worker.joinable()) {
                worker.join();
            }
        }
    }

    running_ = false;
    return true;
}

bool BatchInserter::add_record(const omop::core::Record& record) {
    if (!running_) {
        return false;
    }

    std::lock_guard<std::mutex> lock(current_batch_mutex_);
    
    // Initialize current batch if needed
    if (!current_batch_) {
        current_batch_ = std::make_unique<omop::core::RecordBatch>();
        current_batch_->reserve(config_.batch_size);
    }

    current_batch_->addRecord(record);

    // Check if batch is full
    if (current_batch_->size() >= config_.batch_size) {
        submit_batch(std::move(current_batch_));
        current_batch_ = nullptr;
    }

    return true;
}

size_t BatchInserter::add_records(const std::vector<omop::core::Record>& records) {
    size_t added = 0;
    for (const auto& record : records) {
        if (add_record(record)) {
            added++;
        }
    }
    return added;
}

bool BatchInserter::add_batch(const omop::core::RecordBatch& batch) {
    if (!running_) {
        return false;
    }

    auto record_batch = std::make_unique<omop::core::RecordBatch>(batch);
    submit_batch(std::move(record_batch));
    return true;
}

bool BatchInserter::flush(bool wait_for_completion) {
    if (!running_) {
        return false;
    }

    // Submit current batch if it exists
    {
        std::lock_guard<std::mutex> lock(current_batch_mutex_);
        if (current_batch_ && !current_batch_->empty()) {
            submit_batch(std::move(current_batch_));
            current_batch_ = nullptr;
        }
    }

    if (wait_for_completion) {
        return this->wait_for_completion(std::chrono::seconds(0));
    }

    return true;
}

bool BatchInserter::wait_for_completion(std::chrono::seconds timeout) {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    
    if (timeout.count() > 0) {
        return queue_cv_.wait_for(lock, timeout, [this]() {
            return batch_queue_.empty() && !running_;
        });
    } else {
        queue_cv_.wait(lock, [this]() {
            return batch_queue_.empty() && !running_;
        });
        return true;
    }
}

BatchInserterStats BatchInserter::get_statistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void BatchInserter::register_batch_callback(std::function<void(const BatchInsertResult&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    batch_callbacks_.push_back(std::move(callback));
}

void BatchInserter::register_error_callback(std::function<void(const std::string&, const BatchInsertResult&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callbacks_.push_back(std::move(callback));
}

bool BatchInserter::is_running() const {
    return running_;
}

BatchInserterConfig BatchInserter::get_config() const {
    return config_;
}

bool BatchInserter::update_config(const BatchInserterConfig& config) {
    if (running_) {
        return false;
    }
    
    config_ = config;
    return true;
}

std::optional<BatchInsertResult> BatchInserter::get_batch_result(size_t batch_id) {
    std::lock_guard<std::mutex> lock(results_mutex_);
    auto it = batch_results_.find(batch_id);
    if (it != batch_results_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::vector<BatchInsertResult> BatchInserter::get_all_batch_results() {
    std::lock_guard<std::mutex> lock(results_mutex_);
    std::vector<BatchInsertResult> results;
    results.reserve(batch_results_.size());
    for (const auto& [id, result] : batch_results_) {
        results.push_back(result);
    }
    return results;
}

// Private helper methods
std::vector<std::string> BatchInserter::validate_config() {
    std::vector<std::string> errors;
    
    if (config_.batch_size == 0) {
        errors.push_back("Batch size must be greater than 0");
    }
    
    if (config_.worker_threads == 0) {
        errors.push_back("Worker threads must be greater than 0");
    }
    
    if (config_.max_queue_size == 0) {
        errors.push_back("Max queue size must be greater than 0");
    }
    
    return errors;
}

void BatchInserter::submit_batch(std::unique_ptr<omop::core::RecordBatch> batch) {
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (batch_queue_.size() >= config_.max_queue_size) {
            // Queue is full, process immediately
            process_batch(std::move(batch));
            return;
        }
        batch_queue_.push(std::move(batch));
    }
    queue_cv_.notify_one();
}

void BatchInserter::worker_thread(size_t worker_id) {
    auto logger = common::Logger::get("omop-batch-inserter");
    logger->debug("Worker thread {} started", worker_id);
    
    while (true) {
        std::unique_ptr<omop::core::RecordBatch> batch;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this]() {
                return !batch_queue_.empty() || shutdown_;
            });
            
            if (shutdown_ && batch_queue_.empty()) {
                break;
            }
            
            if (!batch_queue_.empty()) {
                batch = std::move(batch_queue_.front());
                batch_queue_.pop();
            }
        }
        
        if (batch) {
            process_batch(std::move(batch));
        }
    }
    
    logger->debug("Worker thread {} stopped", worker_id);
}

void BatchInserter::process_batch(std::unique_ptr<omop::core::RecordBatch> batch) {
    auto start_time = std::chrono::steady_clock::now();
    
    BatchInsertResult result;
    result.batch_id = ++batch_id_counter_;
    result.total_records = batch->size();
    result.successful_records = 0;
    result.failed_records = 0;
    result.insert_time = std::chrono::duration<double>(0);
    
    try {
        // Simulate batch processing
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        result.successful_records = batch->size();
        result.failed_records = 0;
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.total_batches_processed++;
            stats_.successful_batches++;
            stats_.total_records_processed += batch->size();
            stats_.successful_records += batch->size();
            result.insert_time = std::chrono::duration<double>(std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time).count() / 1000.0);
            stats_.total_processing_time += result.insert_time;
        }
        
        // Store result
        {
            std::lock_guard<std::mutex> lock(results_mutex_);
            batch_results_[result.batch_id] = result;
        }
        
        notify_batch_callbacks(result);
        
    } catch (const std::exception& e) {
        result.successful_records = 0;
        result.failed_records = batch->size();
        result.error_message = e.what();
        
        // Update error statistics
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.failed_batches++;
            stats_.failed_records += batch->size();
        }
        
        notify_error_callbacks(e.what(), result);
    }
}

void BatchInserter::notify_batch_callbacks(const BatchInsertResult& result) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    for (const auto& callback : batch_callbacks_) {
        try {
            callback(result);
        } catch (...) {
            // Ignore callback errors
        }
    }
}

void BatchInserter::notify_error_callbacks(const std::string& error, const BatchInsertResult& result) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    for (const auto& callback : error_callbacks_) {
        try {
            callback(error, result);
        } catch (...) {
            // Ignore callback errors
        }
    }
}

// ParallelBatchInserter implementation
ParallelBatchInserter::ParallelBatchInserter() = default;

ParallelBatchInserter::~ParallelBatchInserter() = default;

bool ParallelBatchInserter::initialize(const BatchInserterConfig& config) {
    // Enable parallel processing by default
    auto parallel_config = config;
    parallel_config.enable_parallel_insert = true;
    parallel_config.worker_threads = std::max(parallel_config.worker_threads, size_t(8));
    return BatchInserter::initialize(parallel_config);
}

// OrderedBatchInserter implementation
OrderedBatchInserter::OrderedBatchInserter() = default;

OrderedBatchInserter::~OrderedBatchInserter() = default;

bool OrderedBatchInserter::initialize(const BatchInserterConfig& config) {
    // Enable order preservation
    auto ordered_config = config;
    ordered_config.preserve_order = true;
    ordered_config.worker_threads = 1; // Use single thread for ordering
    return BatchInserter::initialize(ordered_config);
}

void OrderedBatchInserter::process_batch(std::unique_ptr<omop::core::RecordBatch> batch) {
    // For ordered insertion, process batches sequentially
    BatchInserter::process_batch(std::move(batch));
}

// Factory functions
std::unique_ptr<IBatchInserter> create_batch_inserter(BatchInserterFactory::InserterType type) {
    return BatchInserterFactory::create_inserter(type);
}

BatchInserterConfig get_default_batch_inserter_config() {
    BatchInserterConfig config;
    config.batch_size = 1000;
    config.worker_threads = 4;
    config.max_queue_size = 100;
    return config;
}

std::unique_ptr<IBatchInserter> BatchInserterFactory::create_inserter(InserterType type) {
    switch (type) {
        case InserterType::Standard:
            return std::make_unique<BatchInserter>();
        case InserterType::Parallel:
            return std::make_unique<ParallelBatchInserter>();
        case InserterType::Ordered:
            return std::make_unique<OrderedBatchInserter>();
        case InserterType::HighThroughput:
            return std::make_unique<ParallelBatchInserter>();
        case InserterType::LowLatency:
            return std::make_unique<OrderedBatchInserter>();
        default:
            return std::make_unique<BatchInserter>();
    }
}

std::unique_ptr<IBatchInserter> BatchInserterFactory::create_for_database(
    const std::string& database_type,
    const BatchInserterConfig& config) {
    
    auto inserter = create_inserter(InserterType::Standard);
    inserter->initialize(config);
    return inserter;
}

void BatchInserterFactory::register_inserter_type(
    InserterType type,
    std::function<std::unique_ptr<IBatchInserter>()> creator) {
    // For now, store in a static map for custom inserter registration
    static std::unordered_map<InserterType, std::function<std::unique_ptr<IBatchInserter>()>> custom_creators;
    custom_creators[type] = creator;
}

BatchInserterFactory::InserterType BatchInserterFactory::get_recommended_type(
    size_t record_count,
    const std::unordered_map<std::string, std::any>& target_system) {
    
    if (record_count > 100000) {
        return InserterType::Parallel;
    } else if (record_count > 10000) {
        return InserterType::HighThroughput;
    } else {
        return InserterType::Standard;
    }
}

size_t BatchInserterUtils::calculate_optimal_batch_size(
    size_t record_size,
    size_t available_memory,
    const std::string& database_type) {
    // Use 5% of available memory for batch processing
    size_t optimal_size = (available_memory * 0.05) / record_size;
    
    // Apply database-specific adjustments
    if (database_type == "postgresql") {
        optimal_size = std::min(optimal_size, size_t(10000));
    } else if (database_type == "mysql") {
        optimal_size = std::min(optimal_size, size_t(5000));
    } else if (database_type == "sqlite") {
        optimal_size = std::min(optimal_size, size_t(1000));
    }
    
    return std::max(optimal_size, size_t(100));
}

size_t BatchInserterUtils::calculate_optimal_workers(
    size_t target_throughput,
    std::chrono::duration<double> average_batch_time,
    size_t system_cores) {
    
    // Simple calculation: use 75% of available cores
    return std::max(size_t(1), static_cast<size_t>(system_cores * 0.75));
}

BatchInserterConfig BatchInserterUtils::optimize_config(
    const BatchInserterConfig& config,
    const std::unordered_map<std::string, std::any>& performance_requirements,
    const std::unordered_map<std::string, std::any>& system_constraints) {
    
    BatchInserterConfig optimized = config;
    
    // Apply optimizations based on requirements and constraints
    if (performance_requirements.count("high_throughput")) {
        optimized.worker_threads = std::max(optimized.worker_threads, size_t(8));
        optimized.batch_size = std::max(optimized.batch_size, size_t(5000));
    }
    
    return optimized;
}

std::vector<std::string> BatchInserterUtils::validate_config(const BatchInserterConfig& config) {
    std::vector<std::string> errors;
    
    if (config.batch_size == 0) {
        errors.push_back("Batch size must be greater than 0");
    }
    
    if (config.worker_threads == 0) {
        errors.push_back("Worker threads must be greater than 0");
    }
    
    return errors;
}

std::string BatchInserterUtils::type_to_string(BatchInserterFactory::InserterType type) {
    switch (type) {
        case BatchInserterFactory::InserterType::Standard:
            return "standard";
        case BatchInserterFactory::InserterType::Parallel:
            return "parallel";
        case BatchInserterFactory::InserterType::Ordered:
            return "ordered";
        case BatchInserterFactory::InserterType::HighThroughput:
            return "high_throughput";
        case BatchInserterFactory::InserterType::LowLatency:
            return "low_latency";
        default:
            return "unknown";
    }
}

BatchInserterFactory::InserterType BatchInserterUtils::string_to_type(const std::string& type_str) {
    if (type_str == "standard") return BatchInserterFactory::InserterType::Standard;
    if (type_str == "parallel") return BatchInserterFactory::InserterType::Parallel;
    if (type_str == "ordered") return BatchInserterFactory::InserterType::Ordered;
    if (type_str == "high_throughput") return BatchInserterFactory::InserterType::HighThroughput;
    if (type_str == "low_latency") return BatchInserterFactory::InserterType::LowLatency;
    return BatchInserterFactory::InserterType::Standard;
}


} // namespace omop::load