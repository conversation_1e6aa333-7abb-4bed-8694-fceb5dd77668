#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <future>
#include <queue>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <optional>
#include <thread>
#include <map>

#include "core/interfaces.h"
#include "core/record.h"

namespace omop::load {

// Forward declarations for implementation classes
class ParallelBatchInserterImpl;
class OrderedBatchInserterImpl;

/**
 * @brief Batch insert configuration
 */
struct BatchInserterConfig {
    size_t batch_size{1000};
    size_t max_pending_batches{10};
    size_t max_queue_size{100};  // Maximum size of the batch queue
    std::chrono::seconds batch_timeout{30};
    size_t worker_threads{4};
    bool enable_parallel_insert{true};
    bool enable_retry{true};
    size_t max_retry_attempts{3};
    std::chrono::seconds retry_delay{1};
    bool preserve_order{false};
    bool enable_compression{false};
    std::string target_table;
    std::vector<std::string> columns;
    std::unordered_map<std::string, std::any> database_config;
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Batch insert result
 */
struct BatchInsertResult {
    size_t batch_id{0};
    size_t total_records{0};
    size_t successful_records{0};
    size_t failed_records{0};
    std::chrono::duration<double> insert_time{0};
    std::string error_message;
    std::vector<std::string> detailed_errors;
    std::unordered_map<std::string, std::any> metadata;
};

/**
 * @brief Batch insert statistics
 */
struct BatchInserterStats {
    size_t total_batches_processed{0};
    size_t successful_batches{0};
    size_t failed_batches{0};
    size_t total_records_processed{0};
    size_t successful_records{0};
    size_t failed_records{0};
    size_t pending_batches{0};
    std::chrono::duration<double> total_processing_time{0};
    double average_batch_time{0.0};
    double records_per_second{0.0};
    size_t memory_used_bytes{0};
    std::unordered_map<std::string, std::any> additional_stats;
};

/**
 * @brief Batch inserter interface
 * 
 * This interface defines the contract for batch inserters that efficiently
 * load large volumes of data into target systems using batching techniques.
 */
class IBatchInserter {
public:
    virtual ~IBatchInserter() = default;

    /**
     * @brief Initialize batch inserter
     * @param config Batch inserter configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const BatchInserterConfig& config) = 0;

    /**
     * @brief Start batch inserter
     * @return bool True if started successfully
     */
    virtual bool start() = 0;

    /**
     * @brief Stop batch inserter
     * @param wait_for_completion Wait for pending batches to complete
     * @return bool True if stopped successfully
     */
    virtual bool stop(bool wait_for_completion = true) = 0;

    /**
     * @brief Add record to batch
     * @param record Record to add
     * @return bool True if record added successfully
     */
    virtual bool add_record(const omop::core::Record& record) = 0;

    /**
     * @brief Add multiple records to batch
     * @param records Records to add
     * @return size_t Number of records successfully added
     */
    virtual size_t add_records(const std::vector<omop::core::Record>& records) = 0;

    /**
     * @brief Add record batch
     * @param batch Record batch to add
     * @return bool True if batch added successfully
     */
    virtual bool add_batch(const omop::core::RecordBatch& batch) = 0;

    /**
     * @brief Flush pending records
     * @param wait_for_completion Wait for flush to complete
     * @return bool True if flush initiated successfully
     */
    virtual bool flush(bool wait_for_completion = false) = 0;

    /**
     * @brief Wait for all pending batches to complete
     * @param timeout Timeout duration
     * @return bool True if all batches completed within timeout
     */
    virtual bool wait_for_completion(std::chrono::seconds timeout = std::chrono::seconds(0)) = 0;

    /**
     * @brief Get batch insert result
     * @param batch_id Batch ID
     * @return std::optional<BatchInsertResult> Result if available
     */
    virtual std::optional<BatchInsertResult> get_batch_result(size_t batch_id) = 0;

    /**
     * @brief Get all batch results
     * @return std::vector<BatchInsertResult> All available results
     */
    virtual std::vector<BatchInsertResult> get_all_batch_results() = 0;

    /**
     * @brief Get inserter statistics
     * @return BatchInserterStats Current statistics
     */
    virtual BatchInserterStats get_statistics() = 0;

    /**
     * @brief Register batch completion callback
     * @param callback Callback function
     */
    virtual void register_batch_callback(
        std::function<void(const BatchInsertResult&)> callback) = 0;

    /**
     * @brief Register error callback
     * @param callback Error callback function
     */
    virtual void register_error_callback(
        std::function<void(const std::string&, const BatchInsertResult&)> callback) = 0;

    /**
     * @brief Check if inserter is running
     * @return bool True if running
     */
    virtual bool is_running() const = 0;

    /**
     * @brief Get configuration
     * @return BatchInserterConfig Current configuration
     */
    virtual BatchInserterConfig get_config() const = 0;

    /**
     * @brief Update configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const BatchInserterConfig& config) = 0;
};

/**
 * @brief Default batch inserter implementation
 */
class BatchInserter : public IBatchInserter {
public:
    BatchInserter();
    ~BatchInserter() override;

    bool initialize(const BatchInserterConfig& config) override;
    bool start() override;
    bool stop(bool wait_for_completion = true) override;

    bool add_record(const omop::core::Record& record) override;
    size_t add_records(const std::vector<omop::core::Record>& records) override;
    bool add_batch(const omop::core::RecordBatch& batch) override;

    bool flush(bool wait_for_completion = false) override;
    bool wait_for_completion(std::chrono::seconds timeout = std::chrono::seconds(0)) override;

    std::optional<BatchInsertResult> get_batch_result(size_t batch_id) override;
    std::vector<BatchInsertResult> get_all_batch_results() override;

    BatchInserterStats get_statistics() override;

    void register_batch_callback(
        std::function<void(const BatchInsertResult&)> callback) override;

    void register_error_callback(
        std::function<void(const std::string&, const BatchInsertResult&)> callback) override;

    bool is_running() const override;
    BatchInserterConfig get_config() const override;
    bool update_config(const BatchInserterConfig& config) override;

protected:
    virtual void process_batch(std::unique_ptr<omop::core::RecordBatch> batch);
    
private:
    BatchInserterConfig config_;
    std::atomic<bool> running_{false};
    std::atomic<bool> shutdown_{false};
    std::atomic<size_t> batch_id_counter_{0};
    
    // Threading
    std::vector<std::thread> workers_;
    std::queue<std::unique_ptr<omop::core::RecordBatch>> batch_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Current batch being built
    std::unique_ptr<omop::core::RecordBatch> current_batch_;
    std::mutex current_batch_mutex_;
    
    // Statistics
    BatchInserterStats stats_;
    std::mutex stats_mutex_;
    
    // Results
    std::unordered_map<size_t, BatchInsertResult> batch_results_;
    std::mutex results_mutex_;
    
    // Callbacks
    std::vector<std::function<void(const BatchInsertResult&)>> batch_callbacks_;
    std::vector<std::function<void(const std::string&, const BatchInsertResult&)>> error_callbacks_;
    std::mutex callback_mutex_;
    
    // Private helper methods
    std::vector<std::string> validate_config();
    void submit_batch(std::unique_ptr<omop::core::RecordBatch> batch);
    void worker_thread(size_t worker_id);
    void notify_batch_callbacks(const BatchInsertResult& result);
    void notify_error_callbacks(const std::string& error, const BatchInsertResult& result);
};

/**
 * @brief Parallel batch inserter
 * 
 * High-performance batch inserter that uses multiple worker threads
 * to process batches in parallel for maximum throughput.
 */
class ParallelBatchInserter : public BatchInserter {
public:
    ParallelBatchInserter();
    ~ParallelBatchInserter() override;

    bool initialize(const BatchInserterConfig& config) override;
};

/**
 * @brief Ordered batch inserter
 * 
 * Batch inserter that preserves the order of records during insertion.
 * Useful when insertion order is important for data consistency.
 */
class OrderedBatchInserter : public BatchInserter {
public:
    OrderedBatchInserter();
    ~OrderedBatchInserter() override;

    bool initialize(const BatchInserterConfig& config) override;
    
protected:
    void process_batch(std::unique_ptr<omop::core::RecordBatch> batch) override;
};

/**
 * @brief Batch inserter factory
 */
class BatchInserterFactory {
public:
    enum class InserterType {
        Standard,
        Parallel,
        Ordered,
        HighThroughput,
        LowLatency
    };

    /**
     * @brief Create batch inserter
     * @param type Inserter type
     * @return std::unique_ptr<IBatchInserter> Batch inserter instance
     */
    static std::unique_ptr<IBatchInserter> create_inserter(InserterType type);

    /**
     * @brief Create inserter for database type
     * @param database_type Database type (postgresql, mysql, sqlite, etc.)
     * @param config Inserter configuration
     * @return std::unique_ptr<IBatchInserter> Optimized inserter
     */
    static std::unique_ptr<IBatchInserter> create_for_database(
        const std::string& database_type,
        const BatchInserterConfig& config);

    /**
     * @brief Register custom inserter type
     * @param type Inserter type
     * @param creator Creator function
     */
    static void register_inserter_type(
        InserterType type,
        std::function<std::unique_ptr<IBatchInserter>()> creator);

    /**
     * @brief Get recommended inserter type
     * @param record_count Estimated record count
     * @param target_system Target system information
     * @return InserterType Recommended inserter type
     */
    static InserterType get_recommended_type(
        size_t record_count,
        const std::unordered_map<std::string, std::any>& target_system);
};

/**
 * @brief Batch inserter utilities
 */
class BatchInserterUtils {
public:
    /**
     * @brief Calculate optimal batch size
     * @param record_size Average record size in bytes
     * @param available_memory Available memory in bytes
     * @param database_type Database type
     * @return size_t Optimal batch size
     */
    static size_t calculate_optimal_batch_size(
        size_t record_size,
        size_t available_memory,
        const std::string& database_type);

    /**
     * @brief Calculate optimal worker count
     * @param target_throughput Target records per second
     * @param average_batch_time Average batch processing time
     * @param system_cores Number of CPU cores
     * @return size_t Optimal worker count
     */
    static size_t calculate_optimal_workers(
        size_t target_throughput,
        std::chrono::duration<double> average_batch_time,
        size_t system_cores);

    /**
     * @brief Optimize configuration
     * @param config Base configuration
     * @param performance_requirements Performance requirements
     * @param system_constraints System constraints
     * @return BatchInserterConfig Optimized configuration
     */
    static BatchInserterConfig optimize_config(
        const BatchInserterConfig& config,
        const std::unordered_map<std::string, std::any>& performance_requirements,
        const std::unordered_map<std::string, std::any>& system_constraints);

    /**
     * @brief Validate configuration
     * @param config Configuration to validate
     * @return std::vector<std::string> Validation errors
     */
    static std::vector<std::string> validate_config(const BatchInserterConfig& config);

    /**
     * @brief Convert inserter type to string
     * @param type Inserter type
     * @return std::string Type name
     */
    static std::string type_to_string(BatchInserterFactory::InserterType type);

    /**
     * @brief Convert string to inserter type
     * @param type_str Type name
     * @return BatchInserterFactory::InserterType Inserter type
     */
    static BatchInserterFactory::InserterType string_to_type(const std::string& type_str);
};

/**
 * @brief RAII batch inserter manager
 * 
 * Manages the lifecycle of a batch inserter with automatic cleanup.
 */
class ScopedBatchInserter {
public:
    explicit ScopedBatchInserter(std::unique_ptr<IBatchInserter> inserter);
    ~ScopedBatchInserter();

    ScopedBatchInserter(const ScopedBatchInserter&) = delete;
    ScopedBatchInserter& operator=(const ScopedBatchInserter&) = delete;
    ScopedBatchInserter(ScopedBatchInserter&&) = default;
    ScopedBatchInserter& operator=(ScopedBatchInserter&&) = default;

    /**
     * @brief Get inserter reference
     * @return IBatchInserter& Inserter reference
     */
    IBatchInserter& get() { return *inserter_; }

    /**
     * @brief Get inserter pointer
     * @return IBatchInserter* Inserter pointer
     */
    IBatchInserter* operator->() { return inserter_.get(); }

    /**
     * @brief Release inserter without cleanup
     * @return std::unique_ptr<IBatchInserter> Released inserter
     */
    std::unique_ptr<IBatchInserter> release();

private:
    std::unique_ptr<IBatchInserter> inserter_;
};

/**
 * @brief Create batch inserter instance
 * @param type Inserter type
 * @return std::unique_ptr<IBatchInserter> Batch inserter instance
 */
std::unique_ptr<IBatchInserter> create_batch_inserter(
    BatchInserterFactory::InserterType type = BatchInserterFactory::InserterType::Standard);

/**
 * @brief Get default batch inserter configuration
 * @return BatchInserterConfig Default configuration
 */
BatchInserterConfig get_default_batch_inserter_config();

} // namespace omop::load