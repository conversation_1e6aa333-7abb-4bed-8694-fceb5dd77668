# Load library CMakeLists.txt

add_library(omop_load STATIC)

set(LOAD_SOURCES
    additional_loaders.cpp
    batch_loader.cpp
    batch_inserter.cpp
    database_loader.cpp
    loader_base.cpp
    loader_strategies.cpp
)

set(LOAD_HEADERS
    additional_loaders.h
    batch_loader.h
    database_loader.h
    loader_base.h
    batch_inserter.h
    loader_strategies.h
)

target_sources(omop_load PRIVATE ${LOAD_SOURCES})

omop_configure_library(omop_load
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_extract
        omop_core
        omop_common
        omop_cdm
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        spdlog::spdlog
        fmt::fmt
        Threads::Threads
    HEADERS
        ${LOAD_HEADERS}
)