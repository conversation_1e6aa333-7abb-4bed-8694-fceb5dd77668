#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <chrono>

#include "core/interfaces.h"
#include "core/record.h"

namespace omop::load {

// Forward declarations for implementation classes
class BulkLoadStrategyImpl;
class ParallelLoadingStrategyImpl;

/**
 * @brief Loading strategy enumeration
 */
enum class LoadingStrategy {
    SingleRecord,
    BatchInsert,
    BulkLoad,
    StreamingLoad,
    UpsertLoad,
    DeltaLoad,
    ParallelLoad
};

/**
 * @brief Loading mode enumeration
 */
enum class LoadingMode {
    Insert,
    Update,
    Upsert,
    Replace,
    Append,
    Merge,
    Delete
};

/**
 * @brief Loading performance metrics
 */
struct LoadingMetrics {
    size_t total_records{0};
    size_t successful_records{0};
    size_t failed_records{0};
    size_t duplicate_records{0};
    size_t skipped_records{0};
    size_t total_transactions{0};
    size_t successful_transactions{0};
    size_t failed_transactions{0};
    size_t total_batches{0};
    size_t successful_batches{0};
    size_t failed_batches{0};
    std::chrono::duration<double> loading_time{0};
    std::chrono::milliseconds total_processing_time{0};
    double records_per_second{0.0};
    size_t memory_used_bytes{0};
    std::unordered_map<std::string, std::any> additional_metrics;
};

/**
 * @brief Loading configuration
 */
struct LoadingConfig {
    LoadingStrategy strategy{LoadingStrategy::BatchInsert};
    LoadingMode mode{LoadingMode::Insert};
    size_t batch_size{1000};
    size_t parallel_workers{1};
    size_t worker_threads{1};
    std::chrono::seconds timeout{300};
    bool enable_transactions{true};
    bool enable_validation{true};
    bool skip_duplicates{false};
    bool continue_on_error{false};
    double error_threshold{0.1}; // 10% error threshold
    std::string target_table;
    std::vector<std::string> key_columns;
    std::vector<std::string> update_columns;
    std::unordered_map<std::string, std::any> strategy_specific_config;
};

/**
 * @brief Loading strategy interface
 * 
 * This interface defines the contract for different loading strategies
 * that can be used to load data into target systems.
 */
class ILoadingStrategy {
public:
    virtual ~ILoadingStrategy() = default;

    /**
     * @brief Initialize loading strategy
     * @param config Loading configuration
     * @param context Processing context
     * @return bool True if initialization successful
     */
    virtual bool initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Load single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if record loaded successfully
     */
    virtual bool load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Load batch of records
     * @param batch Batch of records to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    virtual size_t load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Begin loading transaction
     * @param context Processing context
     * @return bool True if transaction started successfully
     */
    virtual bool begin_transaction(omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Commit loading transaction
     * @param context Processing context
     * @return bool True if transaction committed successfully
     */
    virtual bool commit_transaction(omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Rollback loading transaction
     * @param context Processing context
     * @return bool True if transaction rolled back successfully
     */
    virtual bool rollback_transaction(omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Finalize loading process
     * @param context Processing context
     * @return bool True if finalization successful
     */
    virtual bool finalize(omop::core::ProcessingContext& context) = 0;

    /**
     * @brief Get loading metrics
     * @return LoadingMetrics Current metrics
     */
    virtual LoadingMetrics get_metrics() const = 0;

    /**
     * @brief Get strategy type
     * @return LoadingStrategy Strategy type
     */
    virtual LoadingStrategy get_strategy_type() const = 0;

    /**
     * @brief Get supported loading modes
     * @return std::vector<LoadingMode> Supported modes
     */
    virtual std::vector<LoadingMode> get_supported_modes() const = 0;

    /**
     * @brief Validate configuration
     * @param config Configuration to validate
     * @return std::vector<std::string> Validation errors (empty if valid)
     */
    virtual std::vector<std::string> validate_config(const LoadingConfig& config) const = 0;
};

/**
 * @brief Single record loading strategy
 * 
 * Loads records one at a time. Simple but slow for large datasets.
 */
class SingleRecordLoadingStrategy : public ILoadingStrategy {
public:
    SingleRecordLoadingStrategy();
    ~SingleRecordLoadingStrategy() override;

    bool initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) override;
    bool load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) override;
    size_t load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) override;
    bool begin_transaction(omop::core::ProcessingContext& context) override;
    bool commit_transaction(omop::core::ProcessingContext& context) override;
    bool rollback_transaction(omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    LoadingMetrics get_metrics() const override;
    LoadingStrategy get_strategy_type() const override { return LoadingStrategy::SingleRecord; }
    std::vector<LoadingMode> get_supported_modes() const override;
    std::vector<std::string> validate_config(const LoadingConfig& config) const override;

private:
    LoadingConfig config_;
    LoadingMetrics metrics_;
    std::chrono::steady_clock::time_point start_time_;
    bool transaction_active_{false};
};

/**
 * @brief Batch insert loading strategy
 * 
 * Loads records in batches for improved performance.
 */
class BatchInsertLoadingStrategy : public ILoadingStrategy {
public:
    BatchInsertLoadingStrategy();
    ~BatchInsertLoadingStrategy() override;

    bool initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) override;
    bool load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) override;
    size_t load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) override;
    bool begin_transaction(omop::core::ProcessingContext& context) override;
    bool commit_transaction(omop::core::ProcessingContext& context) override;
    bool rollback_transaction(omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    LoadingMetrics get_metrics() const override;
    LoadingStrategy get_strategy_type() const override { return LoadingStrategy::BatchInsert; }
    std::vector<LoadingMode> get_supported_modes() const override;
    std::vector<std::string> validate_config(const LoadingConfig& config) const override;

private:
    LoadingConfig config_;
    LoadingMetrics metrics_;
    std::chrono::steady_clock::time_point start_time_;
    bool transaction_active_{false};
    
    std::vector<omop::core::Record> current_batch_;
    std::mutex batch_mutex_;
    
    // Private helper methods
    bool process_current_batch(omop::core::ProcessingContext& context);
};

/**
 * @brief Bulk load strategy
 * 
 * Uses database-specific bulk loading mechanisms for maximum performance.
 */
class BulkLoadStrategy : public ILoadingStrategy {
public:
    BulkLoadStrategy();
    ~BulkLoadStrategy() override;

    bool initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) override;
    bool load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) override;
    size_t load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) override;
    bool begin_transaction(omop::core::ProcessingContext& context) override;
    bool commit_transaction(omop::core::ProcessingContext& context) override;
    bool rollback_transaction(omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    LoadingMetrics get_metrics() const override;
    LoadingStrategy get_strategy_type() const override { return LoadingStrategy::BulkLoad; }
    std::vector<LoadingMode> get_supported_modes() const override;
    std::vector<std::string> validate_config(const LoadingConfig& config) const override;

private:
    LoadingConfig config_;
    LoadingMetrics metrics_;
    std::chrono::steady_clock::time_point start_time_;
    bool transaction_active_{false};
};

/**
 * @brief Upsert loading strategy
 * 
 * Performs insert or update operations based on key columns.
 */
class UpsertLoadingStrategy : public ILoadingStrategy {
public:
    UpsertLoadingStrategy();
    ~UpsertLoadingStrategy() override;

    bool initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) override;
    bool load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) override;
    size_t load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) override;
    bool begin_transaction(omop::core::ProcessingContext& context) override;
    bool commit_transaction(omop::core::ProcessingContext& context) override;
    bool rollback_transaction(omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    LoadingMetrics get_metrics() const override;
    LoadingStrategy get_strategy_type() const override { return LoadingStrategy::UpsertLoad; }
    std::vector<LoadingMode> get_supported_modes() const override;
    std::vector<std::string> validate_config(const LoadingConfig& config) const override;

private:
    LoadingConfig config_;
    LoadingMetrics metrics_;
    std::chrono::steady_clock::time_point start_time_;
    bool transaction_active_{false};
    size_t insert_count_{0};
    size_t update_count_{0};
    
    // Private helper methods
    bool check_record_exists(const omop::core::Record& record);
};

/**
 * @brief Parallel loading strategy
 * 
 * Loads data using multiple parallel workers for improved performance.
 */
class ParallelLoadingStrategy : public ILoadingStrategy {
public:
    ParallelLoadingStrategy();
    ~ParallelLoadingStrategy() override;

    bool initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) override;
    bool load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) override;
    size_t load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) override;
    bool begin_transaction(omop::core::ProcessingContext& context) override;
    bool commit_transaction(omop::core::ProcessingContext& context) override;
    bool rollback_transaction(omop::core::ProcessingContext& context) override;
    bool finalize(omop::core::ProcessingContext& context) override;
    LoadingMetrics get_metrics() const override;
    LoadingStrategy get_strategy_type() const override { return LoadingStrategy::ParallelLoad; }
    std::vector<LoadingMode> get_supported_modes() const override;
    std::vector<std::string> validate_config(const LoadingConfig& config) const override;

private:
    LoadingConfig config_;
    LoadingMetrics metrics_;
    std::chrono::steady_clock::time_point start_time_;
    bool transaction_active_{false};
};

/**
 * @brief Loading strategy factory
 */
class LoadingStrategyFactory {
public:
    /**
     * @brief Create loading strategy
     * @param strategy Strategy type
     * @return std::unique_ptr<ILoadingStrategy> Loading strategy instance
     */
    static std::unique_ptr<ILoadingStrategy> create_strategy(LoadingStrategy strategy);

    /**
     * @brief Register custom strategy
     * @param strategy Strategy type
     * @param creator Creator function
     */
    static void register_strategy(
        LoadingStrategy strategy,
        std::function<std::unique_ptr<ILoadingStrategy>()> creator);

    /**
     * @brief Get available strategies
     * @return std::vector<LoadingStrategy> Available strategies
     */
    static std::vector<LoadingStrategy> get_available_strategies();

    /**
     * @brief Get recommended strategy
     * @param record_count Estimated record count
     * @param target_system Target system type
     * @return LoadingStrategy Recommended strategy
     */
    static LoadingStrategy get_recommended_strategy(
        size_t record_count,
        const std::string& target_system);
};

/**
 * @brief Loading strategy utilities
 */
class LoadingStrategyUtils {
public:
    /**
     * @brief Convert strategy to string
     * @param strategy Loading strategy
     * @return std::string Strategy name
     */
    static std::string strategy_to_string(LoadingStrategy strategy);

    /**
     * @brief Convert string to strategy
     * @param strategy_str Strategy name
     * @return LoadingStrategy Strategy enum
     */
    static LoadingStrategy string_to_strategy(const std::string& strategy_str);

    /**
     * @brief Convert mode to string
     * @param mode Loading mode
     * @return std::string Mode name
     */
    static std::string mode_to_string(LoadingMode mode);

    /**
     * @brief Convert string to mode
     * @param mode_str Mode name
     * @return LoadingMode Mode enum
     */
    static LoadingMode string_to_mode(const std::string& mode_str);

    /**
     * @brief Get default configuration
     * @param strategy Loading strategy
     * @return LoadingConfig Default configuration
     */
    static LoadingConfig get_default_config(LoadingStrategy strategy);

    /**
     * @brief Optimize configuration
     * @param config Configuration to optimize
     * @param record_count Estimated record count
     * @param system_info System information
     * @return LoadingConfig Optimized configuration
     */
    static LoadingConfig optimize_config(
        const LoadingConfig& config,
        size_t record_count,
        const std::unordered_map<std::string, std::any>& system_info);

    /**
     * @brief Calculate optimal batch size
     * @param record_size Average record size in bytes
     * @param available_memory Available memory in bytes
     * @param target_system Target system type
     * @return size_t Optimal batch size
     */
    static size_t calculate_optimal_batch_size(
        size_t record_size,
        size_t available_memory,
        const std::string& target_system);
};

} // namespace omop::load