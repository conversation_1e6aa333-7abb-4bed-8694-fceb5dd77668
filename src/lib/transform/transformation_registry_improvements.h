// transformation_registry_improvements.h
// Improvements to TransformationRegistry for better test support

#pragma once

#include "transform/transformations.h"
#include <unordered_set>

namespace omop::transform {

/**
 * Enhanced TransformationRegistry with cleanup support
 */
class EnhancedTransformationRegistry : public TransformationRegistry {
public:
    /**
     * Unregister a transformation type
     * @param type_name Transformation type to remove
     * @return bool True if successfully removed
     */
    bool unregister_transformation(const std::string& type_name) {
        std::lock_guard<std::mutex> lock(registry_mutex_);
        auto it = factories_.find(type_name);
        if (it != factories_.end()) {
            factories_.erase(it);
            return true;
        }
        return false;
    }

    /**
     * Clear all registered transformations
     */
    void clear_all() {
        std::lock_guard<std::mutex> lock(registry_mutex_);
        factories_.clear();
    }

    /**
     * Create a scoped registration that automatically unregisters on destruction
     */
    class ScopedRegistration {
    public:
        ScopedRegistration(EnhancedTransformationRegistry& registry,
                          const std::string& type_name,
                          std::function<std::unique_ptr<FieldTransformation>()> factory)
            : registry_(registry), type_name_(type_name) {
            registry_.register_transformation(type_name, std::move(factory));
        }

        ~ScopedRegistration() {
            registry_.unregister_transformation(type_name_);
        }

        // Disable copy
        ScopedRegistration(const ScopedRegistration&) = delete;
        ScopedRegistration& operator=(const ScopedRegistration&) = delete;

        // Enable move
        ScopedRegistration(ScopedRegistration&&) = default;
        ScopedRegistration& operator=(ScopedRegistration&&) = default;

    private:
        EnhancedTransformationRegistry& registry_;
        std::string type_name_;
    };

    /**
     * Get singleton instance (enhanced version)
     */
    static EnhancedTransformationRegistry& enhanced_instance() {
        static EnhancedTransformationRegistry instance;
        return instance;
    }

private:
    // Make factories_ accessible to derived class
    using TransformationRegistry::factories_;
    using TransformationRegistry::registry_mutex_;
};

} // namespace omop::transform