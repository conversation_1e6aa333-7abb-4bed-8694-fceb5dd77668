#pragma once

/**
 * @file vocabulary_transformations.h
 * @brief Vocabulary transformation implementations
 *
 * Provides vocabulary transformation types including concept hierarchy,
 * domain mapping, and concept relationship transformations.
 */

#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include <string>
#include <vector>
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

/**
 * @brief Concept hierarchy transformation
 *
 * Maps concepts to their ancestors or descendants in the concept hierarchy.
 */
class ConceptHierarchyTransformation : public ComplexTransformation {
public:
    enum class Direction {
        ToAncestor,
        ToDescendant,
        ToRoot,
        ToLeaf
    };

    ConceptHierarchyTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "concept_hierarchy"; }

    void configure(const YAML::Node& params) override;

private:
    int extract_concept_id(const std::any& input);
    void configure_direction(const std::string& dir_str);

    Direction direction_{Direction::ToAncestor};
    int ancestor_level_{1};
    int descendant_level_{1};
    std::string select_strategy_{"first"};
    int default_concept_id_{0};
    bool use_original_on_fail_{true};
};

/**
 * @brief Domain-specific concept mapping
 *
 * Maps concepts to ensure they belong to the correct domain.
 */
class DomainMappingTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "domain_mapping"; }

    void configure(const YAML::Node& params) override;

private:
    struct FieldMappingConfig {
        std::string source_field;
        std::string target_field;
        std::string type = "direct";
        std::string expression;
    };

    std::string extract_string_value(const std::any& input);
    int get_domain_default(const std::string& domain);
    TransformationResult transform_record(const core::Record& input, core::ProcessingContext& context);
    std::string evaluate_expression(const std::string& expression, const core::Record& record);

    std::string source_vocabulary_{"SOURCE"};
    std::string target_domain_{"Drug"};
    int default_concept_id_{0};
    std::unordered_map<std::string, int> domain_defaults_{
        {"Drug", 0},
        {"Condition", 0},
        {"Procedure", 0},
        {"Observation", 0},
        {"Measurement", 0},
        {"Device", 0}
    };
    std::vector<FieldMappingConfig> field_mappings_;
};

/**
 * @brief Concept relationship transformation
 *
 * Navigates concept relationships to find related concepts.
 */
class ConceptRelationshipTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "concept_relationship"; }

    void configure(const YAML::Node& params) override;

private:
    int extract_concept_id(const std::any& input);
    std::vector<int> find_related_concepts(VocabularyService& vocab_service,
                                          int source_concept_id);
    int select_concept(const std::vector<int>& concepts,
                      VocabularyService& vocab_service);

    std::string relationship_id_{"Maps to"};
    std::string selection_strategy_{"first"};
    std::string filter_vocabulary_;
    std::string filter_domain_;
    bool prefer_standard_{true};
    int default_concept_id_{0};
    bool use_source_on_no_match_{true};
};

} // namespace omop::transform