#pragma once

/**
 * @file custom_transformations.h
 * @brief Custom transformation implementations
 *
 * Provides custom transformation types including JavaScript, SQL, plugin-based,
 * and Python transformations for flexible data processing.
 */

#include "transform/transformations.h"
#include <string>
#include <vector>
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

/**
 * @brief JavaScript expression transformation
 *
 * Evaluates JavaScript expressions for custom transformations.
 * Note: In production, this would use a JavaScript engine like V8 or QuickJS.
 */
class JavaScriptTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "javascript"; }

    void configure(const YAML::Node& params) override;

private:
    std::string evaluate_expression(const std::string& expr,
                                  const std::any& input,
                                  core::ProcessingContext& context);

    std::string expression_;
    std::string output_type_{"string"};
    std::vector<std::string> imports_;
};

/**
 * @brief SQL expression transformation
 *
 * Evaluates SQL expressions for data transformation.
 */
class SQLTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "sql"; }

    void configure(const YAML::Node& params) override;

private:
    std::string evaluate_sql_expression(const std::string& expr,
                                      const std::unordered_map<std::string, std::any>& params);
    std::any convert_sql_result(const std::string& result);

    std::string sql_expression_;
    std::vector<std::string> context_params_;
    std::string result_type_{"string"};
    YAML::Node variables_;
};

/**
 * @brief Plugin-based transformation
 *
 * Loads and executes transformations from external plugins.
 */
class PluginTransformation : public ComplexTransformation {
public:
    using TransformFunc = std::any(*)(const std::any&, const YAML::Node&);

    ~PluginTransformation();

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "plugin"; }

    void configure(const YAML::Node& params) override;

private:
    void load_plugin();
    void validate_plugin_path();
    void validate_plugin_security();

    std::string plugin_name_;
    std::string plugin_path_;
    std::string function_name_{"transform"};
    YAML::Node plugin_config_;
    void* plugin_handle_{nullptr};
    TransformFunc transform_function_{nullptr};
    bool plugin_loaded_{false};
};

/**
 * @brief Python script transformation
 *
 * Executes Python scripts for custom transformations.
 * Note: In production, would embed Python interpreter.
 */
class PythonTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "python"; }

    void configure(const YAML::Node& params) override;

private:
    void load_script_from_file(const std::string& filename);

    std::string script_;
    std::vector<std::string> modules_;
};

/**
 * @brief Composite transformation that chains multiple transformations
 */
class CompositeTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "composite"; }

    void configure(const YAML::Node& params) override;

private:
    struct TransformationStep {
        std::string type;
        std::string name;
        YAML::Node params;
    };

    std::vector<TransformationStep> transformations_;
    bool stop_on_error_{false};
    TransformationRegistry* registry_{&TransformationRegistry::instance()};
};

/**
 * @brief Custom transformation factory
 *
 * Creates custom transformations based on type.
 */
class CustomTransformationFactory {
public:
    static void register_custom_transformations();
};

} // namespace omop::transform