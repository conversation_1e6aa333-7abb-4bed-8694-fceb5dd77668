/**
 * @file transformation_result.h
 * @brief Transformation result structures for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file defines the result structures used by transformation operations
 * in the OMOP ETL pipeline, including success/failure status and metadata.
 */

#pragma once
#include <any>
#include <vector>
#include <string>
#include <optional>
#include <unordered_map>

namespace omop::transform {

/**
 * @brief Result of a transformation operation
 *
 * Contains the transformed value, success status, warnings, errors,
 * and additional metadata about the transformation.
 */
struct TransformationResult {
    std::any value;                                    ///< Transformed value
    bool success{true};                                ///< Success flag
    std::vector<std::string> warnings;                 ///< Warning messages
    std::optional<std::string> error_message;          ///< Error message if failed
    std::unordered_map<std::string, std::any> metadata; ///< Additional metadata

    /**
     * @brief Check if transformation was successful
     * @return bool True if successful
     */
    bool is_success() const { return success && !error_message.has_value(); }
    
    /**
     * @brief Add a warning message
     * @param warning Warning message
     */
    void add_warning(const std::string& warning) { warnings.push_back(warning); }
    
    /**
     * @brief Set an error message
     * @param error Error message
     */
    void set_error(const std::string& error) { success = false; error_message = error; }
};

} // namespace omop::transform 