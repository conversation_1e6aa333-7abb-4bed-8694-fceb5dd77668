#include "transform/numeric_transformations.h"
#include "common/logging.h"
#include <cmath>
#include <limits>
#include <iostream>

namespace omop::transform {

TransformationResult AdvancedNumericTransformation::transform_detailed(const std::any& input,
                                                                      core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for numeric transformation");
            return result;
        }

        double value = extract_numeric_value(input);

        // Apply operation
        switch (operation_) {
            case Operation::UnitConversion: {
                if (from_unit_.empty() || to_unit_.empty()) {
                    result.set_error("Unit conversion requires from_unit and to_unit");
                    return result;
                }
                value = TransformationUtils::convert_units(value, from_unit_, to_unit_);
                result.metadata["from_unit"] = from_unit_;
                result.metadata["to_unit"] = to_unit_;
                break;
            }

            case Operation::Logarithm: {
                if (value <= 0) {
                    result.set_error("Logarithm requires positive value");
                    return result;
                }
                if (log_base_ == 0) {
                    value = std::log(value); // Natural log
                } else if (log_base_ == 10) {
                    value = std::log10(value);
                } else if (log_base_ == 2) {
                    value = std::log2(value);
                } else {
                    value = std::log(value) / std::log(log_base_);
                }
                break;
            }

            case Operation::Exponential: {
                if (exp_base_ == 0) {
                    value = std::exp(value); // e^x
                } else {
                    value = std::pow(exp_base_, value);
                }
                break;
            }

            case Operation::Power: {
                value = std::pow(value, power_exponent_);
                break;
            }

            case Operation::SquareRoot: {
                if (value < 0) {
                    result.set_error("Square root requires non-negative value");
                    return result;
                }
                value = std::sqrt(value);
                break;
            }

            case Operation::Percentage: {
                if (percentage_total_ == 0) {
                    result.set_error("Percentage calculation requires non-zero total");
                    return result;
                }
                value = (value / percentage_total_) * 100.0;
                break;
            }

            case Operation::ZScore: {
                if (std_deviation_ == 0) {
                    result.set_error("Z-score calculation requires non-zero standard deviation");
                    return result;
                }
                value = (value - mean_value_) / std_deviation_;
                break;
            }

            case Operation::MinMax: {
                if (min_value_ == max_value_) {
                    result.add_warning("Min and max values are equal, result will be 0 or 1");
                }
                value = (value - min_value_) / (max_value_ - min_value_);
                break;
            }

            case Operation::Clamp: {
                if (value < min_value_) {
                    value = min_value_;
                    result.add_warning(std::format("Value clamped to minimum: {}", min_value_));
                } else if (value > max_value_) {
                    value = max_value_;
                    result.add_warning(std::format("Value clamped to maximum: {}", max_value_));
                }
                break;
            }

            case Operation::BucketRange: {
                if (bucket_size_ <= 0) {
                    result.set_error("Bucket size must be positive");
                    return result;
                }
                double bucket_start = std::floor(value / bucket_size_) * bucket_size_;
                double bucket_end = bucket_start + bucket_size_;
                std::string bucket_range = std::to_string(static_cast<int>(bucket_start)) + 
                                         "-" + 
                                         std::to_string(static_cast<int>(bucket_end));
                result.value = bucket_range;
                result.metadata["bucket_size"] = bucket_size_;
                result.metadata["bucket_start"] = bucket_start;
                result.metadata["bucket_end"] = bucket_end;
                break;
            }
        }

        // Apply rounding if specified
        if (round_to_decimal_ >= 0) {
            double multiplier = std::pow(10, round_to_decimal_);
            value = std::round(value * multiplier) / multiplier;
        }

        // Format output - only for operations that return numeric values
        if (operation_ != Operation::BucketRange) {
            if (output_as_integer_) {
                result.value = static_cast<int64_t>(value);
            } else {
                result.value = value;
            }
        }

        // Add metadata
        result.metadata["operation"] = get_operation_name();
        result.metadata["original_value"] = extract_numeric_value(input);

    } catch (const std::exception& e) {
        result.set_error(std::format("Numeric transformation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

bool AdvancedNumericTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    if (input.type() == typeid(double) ||
        input.type() == typeid(float) ||
        input.type() == typeid(int) ||
        input.type() == typeid(int64_t)) {
        return true;
    }

    if (input.type() == typeid(std::string)) {
        std::string str = std::any_cast<std::string>(input);
        // Try to convert to double to validate
        try {
            std::stod(str);
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    return false;
}

void AdvancedNumericTransformation::configure(const YAML::Node& params) {
    if (params["operation"]) {
        std::string op_str = params["operation"].as<std::string>();
        configure_operation(op_str);
    }

    // Unit conversion parameters
    if (params["from_unit"]) {
        from_unit_ = params["from_unit"].as<std::string>();
    }
    if (params["to_unit"]) {
        to_unit_ = params["to_unit"].as<std::string>();
    }

    // Mathematical operation parameters
    if (params["base"]) {
        log_base_ = params["base"].as<double>();
        exp_base_ = log_base_;
    }
    if (params["exponent"]) {
        power_exponent_ = params["exponent"].as<double>();
    }

    // Statistical parameters
    if (params["mean"]) {
        mean_value_ = params["mean"].as<double>();
    }
    if (params["std_deviation"]) {
        std_deviation_ = params["std_deviation"].as<double>();
    }
    if (params["percentage_total"]) {
        percentage_total_ = params["percentage_total"].as<double>();
    }

    // Range parameters
    if (params["min_value"]) {
        min_value_ = params["min_value"].as<double>();
    }
    if (params["max_value"]) {
        max_value_ = params["max_value"].as<double>();
    }
    if (params["bucket_size"]) {
        bucket_size_ = params["bucket_size"].as<double>();
    }

    // Output formatting
    if (params["round_to_decimal"]) {
        round_to_decimal_ = params["round_to_decimal"].as<int>();
    }
    if (params["output_as_integer"]) {
        output_as_integer_ = params["output_as_integer"].as<bool>();
    }
}

double AdvancedNumericTransformation::extract_numeric_value(const std::any& input) const {
    if (input.type() == typeid(double)) {
        return std::any_cast<double>(input);
    } else if (input.type() == typeid(float)) {
        return static_cast<double>(std::any_cast<float>(input));
    } else if (input.type() == typeid(int)) {
        return static_cast<double>(std::any_cast<int>(input));
    } else if (input.type() == typeid(int64_t)) {
        return static_cast<double>(std::any_cast<int64_t>(input));
    } else if (input.type() == typeid(std::string)) {
        return TransformationUtils::extract_numeric(
            std::any_cast<std::string>(input), 0.0);
    }
    return 0.0;
}

void AdvancedNumericTransformation::configure_operation(const std::string& op_str) {
    static const std::unordered_map<std::string, Operation> op_map = {
        {"unit_conversion", Operation::UnitConversion},
        {"convert_units", Operation::UnitConversion},
        {"logarithm", Operation::Logarithm},
        {"log", Operation::Logarithm},
        {"exponential", Operation::Exponential},
        {"exp", Operation::Exponential},
        {"power", Operation::Power},
        {"pow", Operation::Power},
        {"square_root", Operation::SquareRoot},
        {"sqrt", Operation::SquareRoot},
        {"percentage", Operation::Percentage},
        {"percent", Operation::Percentage},
        {"zscore", Operation::ZScore},
        {"z_score", Operation::ZScore},
        {"minmax", Operation::MinMax},
        {"min_max", Operation::MinMax},
        {"clamp", Operation::Clamp},
        {"bucket_range", Operation::BucketRange},
        {"bucket", Operation::BucketRange}
    };

    auto it = op_map.find(op_str);
    if (it != op_map.end()) {
        operation_ = it->second;
    }
}

std::string AdvancedNumericTransformation::get_operation_name() const {
    switch (operation_) {
        case Operation::UnitConversion: return "unit_conversion";
        case Operation::Logarithm: return "logarithm";
        case Operation::Exponential: return "exponential";
        case Operation::Power: return "power";
        case Operation::SquareRoot: return "square_root";
        case Operation::Percentage: return "percentage";
        case Operation::ZScore: return "zscore";
        case Operation::MinMax: return "minmax";
        case Operation::Clamp: return "clamp";
        case Operation::BucketRange: return "bucket_range";
        default: return "unknown";
    }
}

double AdvancedNumericTransformation::bucket_value(double value) const {
    if (bucket_size_ <= 0) {
        return value;
    }
    return std::floor(value / bucket_size_) * bucket_size_;
}

TransformationResult NumericValidationTransformation::transform_detailed(const std::any& input,
                                                   core::ProcessingContext& context) {
    TransformationResult result;
    
    if (!validate_input(input)) {
        result.set_error("Invalid input for numeric validation");
        return result;
    }

    double value = extract_numeric_value(input);

    // Check for special values
    if (std::isnan(value)) {
        if (reject_nan_) {
            result.set_error("NaN values not allowed");
            return result;
        } else {
            value = default_value_;
        }
    }

    if (std::isinf(value)) {
        if (reject_infinity_) {
            result.set_error("Infinite values not allowed");
            return result;
        } else {
            value = value > 0 ? (max_allowed_ ? *max_allowed_ : value) : (min_allowed_ ? *min_allowed_ : value);
        }
    }

    // Range validation
    if (!TransformationUtils::validate_numeric_range(value, min_allowed_, max_allowed_)) {
        if (clamp_to_range_) {
            if (min_allowed_ && value < *min_allowed_) {
                value = *min_allowed_;
            }
            if (max_allowed_ && value > *max_allowed_) {
                value = *max_allowed_;
            }
        } else {
            result.set_error("Value outside allowed range");
            return result;
        }
    }

    // Precision validation
    if (required_precision_ > 0) {
        double multiplier = std::pow(10, required_precision_);
        double rounded = std::round(value * multiplier) / multiplier;
        if (std::abs(value - rounded) > constants::NUMERIC_EPSILON) {
            if (enforce_precision_) {
                value = rounded;
            }
        }
    }

    // Check against allowed values list
    if (!allowed_values_.empty()) {
        bool found = false;
        for (double allowed : allowed_values_) {
            if (std::abs(value - allowed) < constants::NUMERIC_EPSILON) {
                found = true;
                break;
            }
        }
        if (!found) {
            result.set_error("Value not in allowed values list");
            return result;
        }
    }

    result.value = value;
    return result;
}

bool NumericValidationTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return !reject_null_;

    return input.type() == typeid(double) ||
           input.type() == typeid(float) ||
           input.type() == typeid(int) ||
           input.type() == typeid(int64_t) ||
           input.type() == typeid(std::string);
}

void NumericValidationTransformation::configure(const YAML::Node& params) {
    if (params["min_allowed"]) {
        min_allowed_ = params["min_allowed"].as<double>();
    }
    if (params["max_allowed"]) {
        max_allowed_ = params["max_allowed"].as<double>();
    }
    if (params["clamp_to_range"]) {
        clamp_to_range_ = params["clamp_to_range"].as<bool>();
    }
    if (params["reject_nan"]) {
        reject_nan_ = params["reject_nan"].as<bool>();
    }
    if (params["reject_infinity"]) {
        reject_infinity_ = params["reject_infinity"].as<bool>();
    }
    if (params["reject_null"]) {
        reject_null_ = params["reject_null"].as<bool>();
    }
    if (params["default_value"]) {
        default_value_ = params["default_value"].as<double>();
    }
    if (params["required_precision"]) {
        required_precision_ = params["required_precision"].as<int>();
    }
    if (params["enforce_precision"]) {
        enforce_precision_ = params["enforce_precision"].as<bool>();
    }
    if (params["allowed_values"]) {
        allowed_values_ = params["allowed_values"].as<std::vector<double>>();
    }
}

double NumericValidationTransformation::extract_numeric_value(const std::any& input) const {
    if (input.type() == typeid(double)) {
        return std::any_cast<double>(input);
    } else if (input.type() == typeid(float)) {
        return static_cast<double>(std::any_cast<float>(input));
    } else if (input.type() == typeid(int)) {
        return static_cast<double>(std::any_cast<int>(input));
    } else if (input.type() == typeid(int64_t)) {
        return static_cast<double>(std::any_cast<int64_t>(input));
    } else if (input.type() == typeid(std::string)) {
        return TransformationUtils::extract_numeric(
            std::any_cast<std::string>(input), default_value_);
    }
    return default_value_;
}

// Register numeric transformations
static bool register_numeric_transformations() {
    auto& registry = TransformationRegistry::instance();

    registry.register_transformation("advanced_numeric_transform",
        []() { return std::make_unique<AdvancedNumericTransformation>(); });

    registry.register_transformation("numeric_validation",
        []() { return std::make_unique<NumericValidationTransformation>(); });

    return true;
}

static bool numeric_transformations_registered = register_numeric_transformations();

} // namespace omop::transform