#pragma once

/**
 * @file string_transformations.h
 * @brief String manipulation and pattern extraction transformations
 *
 * Provides transformations for string manipulation operations such as case conversion,
 * trimming, padding, substring extraction, and pattern matching.
 */

#include "transform/transformations.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <regex>
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

/**
 * @brief String manipulation transformation
 *
 * Provides various string manipulation operations such as case conversion,
 * trimming, padding, and substring extraction.
 */
class StringManipulationTransformation : public ComplexTransformation {
public:
    enum class Operation {
        Uppercase,
        Lowercase,
        TitleCase,
        Trim,
        LeftTrim,
        RightTrim,
        PadLeft,
        PadRight,
        Substring,
        Replace,
        RegexReplace,
        RemoveNonAlphanumeric,
        NormalizeWhitespace
    };

    StringManipulationTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "string_manipulation"; }

    void configure(const YAML::Node& params) override;

private:
    std::string extract_string_value(const std::any& input) const;
    void configure_operation(const std::string& op_str);
    std::string get_operation_name() const;
    std::string to_title_case(const std::string& str);
    std::string trim_string(const std::string& str);
    std::string left_trim(const std::string& str);
    std::string right_trim(const std::string& str);
    std::string extract_substring(const std::string& str);
    std::string replace_string(const std::string& str);
    std::string regex_replace_string(const std::string& str);
    std::string remove_non_alphanumeric(const std::string& str);
    std::string normalize_whitespace(const std::string& str);

    Operation operation_{Operation::Trim};
    size_t target_length_{0};
    char pad_char_{' '};
    int start_pos_{0};
    int end_pos_{-1};
    std::string search_text_;
    std::string replace_text_;
    std::string pattern_;
    std::string replacement_;
    int max_length_{0};
    bool preserve_spaces_{true};
};

/**
 * @brief String pattern extraction transformation
 *
 * Extracts specific patterns from strings using regular expressions.
 */
class StringPatternExtractionTransformation : public ComplexTransformation {
public:
    enum class PatternType {
        Custom,
        Email,
        Phone,
        PostalCode,
        SSN,
        CreditCard,
        Date,
        Time,
        Number,
        AlphaOnly,
        AlphanumericOnly
    };

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "string_pattern_extraction"; }

    void configure(const YAML::Node& params) override;

private:
    std::string extract_string_value(const std::any& input) const;
    void configure_pattern_type(const std::string& type_str);
    std::string get_pattern() const;
    std::string get_pattern_type_name() const;

    PatternType pattern_type_{PatternType::Custom};
    std::string custom_pattern_;
    int capture_group_{0};
    bool extract_all_{false};
    std::string separator_{", "};
    std::string default_value_;
    bool return_default_on_no_match_{true};
};

} // namespace omop::transform