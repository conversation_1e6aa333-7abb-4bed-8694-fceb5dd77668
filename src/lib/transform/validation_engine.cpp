#include "transform/validation_engine.h"
#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include "common/validation.h"
#include <regex>
#include <set>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

// ValidationEngine constructor - register built-in validators
ValidationEngine::ValidationEngine() {
    // Register built-in validators
    register_validator("required", []() { return std::make_unique<RequiredFieldValidator>(); });
    register_validator("datatype", []() { return std::make_unique<DataTypeValidator>(); });
    register_validator("data_type", []() { return std::make_unique<DataTypeValidator>(); }); // Alternative name
    register_validator("range", []() { return std::make_unique<RangeValidator>(); });
    register_validator("pattern", []() { return std::make_unique<PatternValidator>(); });
    register_validator("length", []() { return std::make_unique<LengthValidator>(); });
    register_validator("vocabulary", []() { return std::make_unique<VocabularyFieldValidator>(); });
    register_validator("crossfield", []() { return std::make_unique<CrossFieldValidator>(); });
    register_validator("custom", []() { return std::make_unique<CustomValidator>(); });
}

// RequiredFieldValidator
omop::common::ValidationResult RequiredFieldValidator::validate(const std::any& value, core::ProcessingContext&) {
    omop::common::ValidationResult result;
    
    // Check if field is present
    if (!value.has_value()) {
        result.add_error(field_name_, "Required field is missing", "required_field");
        return result;
    }
    
    // Check data type if expected_type_ is set (but don't confuse with validator type)
    if (!expected_type_.empty() && expected_type_ != "required") {
        bool type_valid = false;
        if (expected_type_ == "string") {
            type_valid = (value.type() == typeid(std::string));
        } else if (expected_type_ == "int" || expected_type_ == "integer") {
            type_valid = (value.type() == typeid(int) || value.type() == typeid(int64_t));
        } else if (expected_type_ == "double" || expected_type_ == "float") {
            type_valid = (value.type() == typeid(double) || value.type() == typeid(float));
        } else if (expected_type_ == "bool" || expected_type_ == "boolean") {
            type_valid = (value.type() == typeid(bool));
        }
        
        if (!type_valid) {
            result.add_error(field_name_, 
                std::format("Expected type '{}' but got different type", expected_type_), 
                "type_validation");
            return result;
        }
    }
    
    // Check if field is empty string
    if (value.type() == typeid(std::string)) {
        std::string str_val = std::any_cast<std::string>(value);
        if (str_val.empty() && !allow_empty_string_) {
            result.add_error(field_name_, "Required field is empty", "required_field");
            return result;
        }
        
        // Validate string length
        if (min_length_ > 0 && str_val.length() < static_cast<size_t>(min_length_)) {
            result.add_error(field_name_, 
                std::format("String too short (minimum {} characters)", min_length_), 
                "length_validation");
        }
        
        if (max_length_ > 0 && str_val.length() > static_cast<size_t>(max_length_)) {
            result.add_error(field_name_, 
                std::format("String too long (maximum {} characters)", max_length_), 
                "length_validation");
        }
        
        // Validate pattern
        if (!pattern_.empty()) {
            try {
                std::regex pattern_regex(pattern_);
                if (!std::regex_match(str_val, pattern_regex)) {
                    result.add_error(field_name_, 
                        std::format("String does not match required pattern"), 
                        "pattern_validation");
                }
            } catch (const std::regex_error& e) {
                result.add_error(field_name_, 
                    std::format("Invalid regex pattern: {}", e.what()), 
                    "pattern_validation");
            }
        }
    }
    
    return result;
}

void RequiredFieldValidator::configure(const YAML::Node& params) {
    if (params["allow_empty_string"]) {
        allow_empty_string_ = params["allow_empty_string"].as<bool>();
    }
    if (params["min_length"]) {
        min_length_ = params["min_length"].as<int>();
    }
    if (params["max_length"]) {
        max_length_ = params["max_length"].as<int>();
    }
    if (params["pattern"]) {
        pattern_ = params["pattern"].as<std::string>();
    }
    if (params["type"]) {
        expected_type_ = params["type"].as<std::string>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// DataTypeValidator
omop::common::ValidationResult DataTypeValidator::validate(const std::any& value, core::ProcessingContext&) {
    omop::common::ValidationResult result;
    if (!value.has_value()) {
        if (required_) {
            result.add_error(field_name_, "Value is required", "not_null");
        }
        return result;
    }
    bool valid = false;
    std::string actual_type = get_actual_type(value);
    switch (expected_type_) {
        case DataType::String:
            valid = value.type() == typeid(std::string) || value.type() == typeid(const char*);
            break;
        case DataType::Integer:
            valid = value.type() == typeid(int) || value.type() == typeid(int64_t);
            if (!valid && value.type() == typeid(std::string)) {
                try { std::stoi(std::any_cast<std::string>(value)); valid = true; } catch (...) {}
            }
            break;
        case DataType::Float:
            valid = value.type() == typeid(double) || value.type() == typeid(float);
            if (!valid && value.type() == typeid(std::string)) {
                try { std::stod(std::any_cast<std::string>(value)); valid = true; } catch (...) {}
            }
            break;
        case DataType::Boolean:
            valid = value.type() == typeid(bool);
            if (!valid && value.type() == typeid(std::string)) {
                std::string str_val = std::any_cast<std::string>(value);
                valid = (str_val == "true" || str_val == "false" || str_val == "1" || str_val == "0");
            }
            break;
        case DataType::Date:
        case DataType::DateTime:
        case DataType::Time:
            if (value.type() == typeid(std::string)) {
                std::string str_val = std::any_cast<std::string>(value);
                valid = validate_date_format(str_val);
            } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
                valid = true;
            }
            break;
    }
    if (!valid) {
        result.add_error(field_name_, "Type mismatch", "type_validation");
    }
    return result;
}

void DataTypeValidator::configure(const YAML::Node& params) {
    if (params["type"]) {
        configure_type(params["type"].as<std::string>());
    }
    if (params["expected_type"]) {
        configure_type(params["expected_type"].as<std::string>());
    }
    if (params["required"]) {
        required_ = params["required"].as<bool>();
    }
    if (params["date_formats"]) {
        date_formats_ = params["date_formats"].as<std::vector<std::string>>();
    }
}

void DataTypeValidator::configure_type(const std::string& type_str) {
    static const std::unordered_map<std::string, DataType> type_map = {
        {"string", DataType::String}, {"integer", DataType::Integer}, {"int", DataType::Integer},
        {"float", DataType::Float}, {"double", DataType::Float}, {"boolean", DataType::Boolean},
        {"bool", DataType::Boolean}, {"date", DataType::Date}, {"datetime", DataType::DateTime}, {"time", DataType::Time}
    };
    auto it = type_map.find(type_str);
    if (it != type_map.end()) expected_type_ = it->second;
}

std::string DataTypeValidator::get_type_name(DataType type) const {
    switch (type) {
        case DataType::String: return "string";
        case DataType::Integer: return "integer";
        case DataType::Float: return "float";
        case DataType::Boolean: return "boolean";
        case DataType::Date: return "date";
        case DataType::DateTime: return "datetime";
        case DataType::Time: return "time";
        default: return "unknown";
    }
}

std::string DataTypeValidator::get_actual_type(const std::any& value) const {
    if (value.type() == typeid(std::string)) return "string";
    if (value.type() == typeid(int)) return "integer";
    if (value.type() == typeid(int64_t)) return "integer";
    if (value.type() == typeid(double)) return "float";
    if (value.type() == typeid(float)) return "float";
    if (value.type() == typeid(bool)) return "boolean";
    return "unknown";
}

bool DataTypeValidator::validate_date_format(const std::string& date_str) {
    if (date_formats_.empty()) {
        date_formats_ = {"%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y"};
    }
    auto parsed = TransformationUtils::parse_date(date_str, date_formats_);
    return parsed.has_value();
}

// RangeValidator
omop::common::ValidationResult RangeValidator::validate(const std::any& value, core::ProcessingContext&) {
    omop::common::ValidationResult result;
    if (!value.has_value()) return result;
    double val = 0.0;
    if (value.type() == typeid(int)) val = static_cast<double>(std::any_cast<int>(value));
    else if (value.type() == typeid(int64_t)) val = static_cast<double>(std::any_cast<int64_t>(value));
    else if (value.type() == typeid(double)) val = std::any_cast<double>(value);
    else if (value.type() == typeid(float)) val = static_cast<double>(std::any_cast<float>(value));
    else return result;
    if (min_value_ && val < *min_value_) result.add_error(field_name_, "Value below minimum", "range");
    if (max_value_ && val > *max_value_) result.add_error(field_name_, "Value above maximum", "range");
    if (!allowed_values_.empty() && std::find(allowed_values_.begin(), allowed_values_.end(), val) == allowed_values_.end())
        result.add_error(field_name_, "Value not in allowed set", "range");
    return result;
}

void RangeValidator::configure(const YAML::Node& params) {
    if (params["min"]) min_value_ = params["min"].as<double>();
    if (params["max"]) max_value_ = params["max"].as<double>();
    if (params["allowed_values"]) allowed_values_ = params["allowed_values"].as<std::vector<double>>();
}

// PatternValidator
omop::common::ValidationResult PatternValidator::validate(const std::any& value, core::ProcessingContext&) {
    omop::common::ValidationResult result;
    if (!value.has_value()) return result;
    if (value.type() != typeid(std::string)) return result;
    std::string str_val = std::any_cast<std::string>(value);
    if (!std::regex_match(str_val, regex_pattern_)) result.add_error(field_name_, "Pattern does not match", "pattern");
    return result;
}

void PatternValidator::configure(const YAML::Node& params) {
    if (params["pattern"]) {
        pattern_ = params["pattern"].as<std::string>();
        regex_pattern_ = std::regex(pattern_);
    }
}

// LengthValidator
omop::common::ValidationResult LengthValidator::validate(const std::any& value, core::ProcessingContext&) {
    omop::common::ValidationResult result;
    if (!value.has_value()) return result;
    if (value.type() != typeid(std::string)) return result;
    std::string str_val = std::any_cast<std::string>(value);
    if (min_length_ && str_val.length() < *min_length_) result.add_error(field_name_, "String shorter than minimum length", "length");
    if (max_length_ && str_val.length() > *max_length_) result.add_error(field_name_, "String longer than maximum length", "length");
    if (exact_length_ && str_val.length() != *exact_length_) result.add_error(field_name_, "String does not match required length", "length");
    return result;
}

void LengthValidator::configure(const YAML::Node& params) {
    if (params["min_length"]) min_length_ = params["min_length"].as<size_t>();
    if (params["max_length"]) max_length_ = params["max_length"].as<size_t>();
    if (params["exact_length"]) exact_length_ = params["exact_length"].as<size_t>();
}

// VocabularyFieldValidator
omop::common::ValidationResult VocabularyFieldValidator::validate(const std::any& value, core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    if (!value.has_value()) return result;
    
    // Use the singleton vocabulary service manager
    if (!VocabularyServiceManager::is_initialized()) {
        result.add_error(field_name_, "Vocabulary service not available", "vocabulary");
        return result;
    }
    
    auto& vocab_service = VocabularyServiceManager::instance();
    
    if (value.type() == typeid(std::string)) {
        std::string concept_code = std::any_cast<std::string>(value);
        if (validate_concept_id_) {
            VocabularyValidator validator(vocab_service);
            int concept_id = 0;
            try { concept_id = std::stoi(concept_code); } catch (...) { concept_id = -1; }
            if (!validator.validate_concept_id(concept_id, vocabulary_name_)) {
                result.add_error(field_name_, "Invalid concept ID", "vocabulary");
            }
        } else {
            VocabularyValidator validator(vocab_service);
            // Use validate_mapping_exists instead of validate_concept_code
            if (!validator.validate_mapping_exists(concept_code, vocabulary_name_)) {
                result.add_error(field_name_, "Invalid concept code", "vocabulary");
            }
        }
    }
    return result;
}

void VocabularyFieldValidator::configure(const YAML::Node& params) {
    if (params["vocabulary_name"]) vocabulary_name_ = params["vocabulary_name"].as<std::string>();
    if (params["expected_domain"]) expected_domain_ = params["expected_domain"].as<std::string>();
    if (params["validate_concept_id"]) validate_concept_id_ = params["validate_concept_id"].as<bool>();
    if (params["require_standard"]) require_standard_ = params["require_standard"].as<bool>();
}

// CrossFieldValidator
omop::common::ValidationResult CrossFieldValidator::validate(const std::any& value, 
                                                   core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    
    // Extract the record from context to access related fields
    auto record_opt = context.get_data("current_record");
    if (!record_opt.has_value()) {
        result.add_error(field_name_, "Cannot perform cross-field validation without record context", "cross_field");
        return result;
    }
    
    try {
        const auto& record = std::any_cast<const core::Record&>(*record_opt);
        
        // Evaluate the cross-field condition
        if (!condition_.empty()) {
            // Parse and evaluate condition expression
            // Example conditions: "start_date <= end_date", "value1 + value2 < 100"
            bool condition_met = evaluate_condition(condition_, record, field_name_, value);
            
            if (!condition_met) {
                std::string error_msg = std::format("Cross-field validation failed: {}", condition_);
                result.add_error(field_name_, error_msg, "cross_field_condition");
            }
        }
        
        // Check related field dependencies
        for (const auto& related_field : related_fields_) {
            if (!record.hasField(related_field)) {
                result.add_warning(field_name_, 
                    std::format("Related field '{}' is missing", related_field), 
                    "missing_related_field");
            }
        }
        
    } catch (const std::exception& e) {
        result.add_error(field_name_, 
            std::format("Cross-field validation error: {}", e.what()), 
            "cross_field_error");
    }
    
    return result;
}

void CrossFieldValidator::configure(const YAML::Node& params) {
    if (params["related_fields"]) related_fields_ = params["related_fields"].as<std::vector<std::string>>();
    if (params["condition"]) condition_ = params["condition"].as<std::string>();
}

// ValidationEngine
void ValidationEngine::load_validation_rules(const YAML::Node& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    field_validators_.clear();
    
    // Handle both config["rules"] and direct sequence formats
    YAML::Node rules_node = config;
    if (config["rules"]) {
        rules_node = config["rules"];
    }
    
    if (!rules_node || !rules_node.IsSequence()) return;
    
    for (const auto& rule_node : rules_node) {
        std::string field_name;
        if (rule_node["field"]) field_name = rule_node["field"].as<std::string>();
        std::vector<std::unique_ptr<IFieldValidator>> validators;
        if (rule_node["validators"]) {
            for (const auto& val_node : rule_node["validators"]) {
                std::string type = val_node["type"].as<std::string>();
                auto validator = create_validator(type);
                if (validator) {
                    validator->set_field_name(field_name);
                    validator->configure(val_node);
                    validators.push_back(std::move(validator));
                }
            }
        }
        field_validators_[field_name] = std::move(validators);
    }
}

omop::common::ValidationResult ValidationEngine::validate_record(const core::Record& record, core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    std::lock_guard<std::mutex> lock(mutex_);
    for (const auto& [field_name, validators] : field_validators_) {
        auto field_value_opt = record.getFieldOptional(field_name);
        std::any field_value = field_value_opt.value_or(std::any{});
        for (const auto& validator : validators) {
            auto val_result = validator->validate(field_value, context);
            result.merge(val_result);
        }
    }
    // Cross-field validation could be added here
    return result;
}

omop::common::ValidationResult ValidationEngine::validate_field(const std::string& field_name, const std::any& value, core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = field_validators_.find(field_name);
    if (it != field_validators_.end()) {
        for (const auto& validator : it->second) {
            auto val_result = validator->validate(value, context);
            result.merge(val_result);
        }
    }
    return result;
}

void ValidationEngine::register_validator(const std::string& type, std::function<std::unique_ptr<IFieldValidator>()> factory) {
    std::lock_guard<std::mutex> lock(mutex_);
    validator_factories_[type] = std::move(factory);
}

std::vector<std::string> ValidationEngine::get_registered_types() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string> types;
    for (const auto& pair : validator_factories_) types.push_back(pair.first);
    return types;
}

void ValidationEngine::clear_rules() {
    std::lock_guard<std::mutex> lock(mutex_);
    field_validators_.clear();
}

std::unique_ptr<IFieldValidator> ValidationEngine::create_validator(const std::string& type) {
    auto it = validator_factories_.find(type);
    if (it != validator_factories_.end()) return it->second();
    auto logger = common::Logger::get("omop-transform");
    logger->warn("Unknown validator type: {}", type);
    return nullptr;
}

std::unique_ptr<IValidationEngine> create_validation_engine() {
    return std::make_unique<ValidationEngine>();
}

// CustomValidator
omop::common::ValidationResult omop::transform::CustomValidator::validate(const std::any& value, core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    if (validation_func_) {
        bool valid = validation_func_(value, context);
        if (!valid) {
            result.add_error(field_name_, error_message_.empty() ? "Custom validation failed" : error_message_, "custom");
        }
    } else {
        result.add_error(field_name_, "No custom validation function set", "custom");
    }
    return result;
}

void omop::transform::CustomValidator::configure(const YAML::Node& params) {
    if (params["error_message"]) {
        error_message_ = params["error_message"].as<std::string>();
    }
    // Additional configuration can be added here
}

// Add helper method to evaluate conditions
bool CrossFieldValidator::evaluate_condition(const std::string& condition,
                                           const core::Record& record,
                                           const std::string& current_field,
                                           const std::any& current_value) const {
    // Simple condition parser for common cases
    // Supports: field1 < field2, field1 == field2, field1 != field2, etc.
    
    std::regex pattern(R"((\w+)\s*(<=|>=|<|>|==|!=)\s*(\w+))");
    std::smatch match;
    
    if (std::regex_match(condition, match, pattern)) {
        std::string left_field = match[1].str();
        std::string op = match[2].str();
        std::string right_field = match[3].str();
        
        // Get field values
        std::any left_value = (left_field == current_field) ? current_value : record.getField(left_field);
        std::any right_value = (right_field == current_field) ? current_value : record.getField(right_field);
        
        // Compare values based on type
        return compare_field_values(left_value, right_value, op);
    }
    
    return true; // If condition cannot be parsed, assume it passes
}

bool CrossFieldValidator::compare_field_values(const std::any& left_value, 
                                             const std::any& right_value, 
                                             const std::string& op) const {
    // Simple comparison implementation
    // In a real implementation, this would handle different data types more robustly
    
    if (!left_value.has_value() || !right_value.has_value()) {
        return false;
    }
    
    // For now, convert both to strings for comparison
    std::string left_str, right_str;
    
    if (left_value.type() == typeid(std::string)) {
        left_str = std::any_cast<std::string>(left_value);
    } else if (left_value.type() == typeid(int)) {
        left_str = std::to_string(std::any_cast<int>(left_value));
    } else if (left_value.type() == typeid(double)) {
        left_str = std::to_string(std::any_cast<double>(left_value));
    } else {
        return false;
    }
    
    if (right_value.type() == typeid(std::string)) {
        right_str = std::any_cast<std::string>(right_value);
    } else if (right_value.type() == typeid(int)) {
        right_str = std::to_string(std::any_cast<int>(right_value));
    } else if (right_value.type() == typeid(double)) {
        right_str = std::to_string(std::any_cast<double>(right_value));
    } else {
        return false;
    }
    
    if (op == "==") {
        return left_str == right_str;
    } else if (op == "!=") {
        return left_str != right_str;
    } else if (op == "<") {
        return left_str < right_str;
    } else if (op == ">") {
        return left_str > right_str;
    } else if (op == "<=") {
        return left_str <= right_str;
    } else if (op == ">=") {
        return left_str >= right_str;
    }
    
    return false;
}

void ValidationEngine::load_configuration(const YAML::Node& config) {
    auto logger = common::Logger::get("omop-validation");
    logger->info("Loading validation configuration");
    
    // Load validation rules from configuration
    if (config["validation_rules"]) {
        load_validation_rules(config["validation_rules"]);
    } else {
        // Fallback to loading entire config as validation rules
        load_validation_rules(config);
    }
    
    // Configure additional validation settings
    if (config["strict_mode"]) {
        bool strict_mode = config["strict_mode"].as<bool>();
        logger->info("Validation strict mode: {}", strict_mode ? "enabled" : "disabled");
    }
    
    if (config["max_errors_per_record"]) {
        int max_errors = config["max_errors_per_record"].as<int>();
        logger->info("Maximum validation errors per record: {}", max_errors);
    }
    
    logger->info("Validation configuration loaded successfully");
}

std::vector<omop::common::ValidationResult> ValidationEngine::validate_batch(
    const std::vector<core::Record>& records,
    core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-validation");
    logger->debug("Validating batch of {} records", records.size());
    
    std::vector<omop::common::ValidationResult> results;
    results.reserve(records.size());
    
    // Track batch-level statistics
    size_t total_errors = 0;
    size_t total_warnings = 0;
    
    // Validate each record in the batch
    for (size_t i = 0; i < records.size(); ++i) {
        const auto& record = records[i];
        
        try {
            auto result = validate_record(record, context);
            
            // Count errors and warnings for batch statistics
            total_errors += result.errors().size();
            total_warnings += result.warnings().size();
            
            results.push_back(std::move(result));
            
        } catch (const std::exception& e) {
            // Handle validation failures gracefully
            omop::common::ValidationResult error_result;
            // Use the proper API to add errors
            error_result.add_error("batch_record_" + std::to_string(i), 
                                 std::format("Validation exception: {}", e.what()), 
                                 "batch_validation");
            
            results.push_back(std::move(error_result));
            total_errors++;
        }
    }
    
    logger->info("Batch validation completed: {} records, {} errors, {} warnings", 
                records.size(), total_errors, total_warnings);
    
    return results;
}

} // namespace omop::transform 