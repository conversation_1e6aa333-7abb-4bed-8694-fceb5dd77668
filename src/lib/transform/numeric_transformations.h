#pragma once

/**
 * @file numeric_transformations.h
 * @brief Numeric transformation implementations
 *
 * Provides transformations for numeric operations including unit conversions,
 * mathematical functions, statistical calculations, and numeric validation.
 */

#include "transform/transformations.h"
#include <string>
#include <vector>
#include <optional>
#include <any>
#include <cmath>
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

/**
 * @brief Advanced numeric transformation with unit conversion
 *
 * Extends basic numeric transformation with support for complex unit conversions,
 * statistical calculations, and numeric formatting.
 */
class AdvancedNumericTransformation : public ComplexTransformation {
public:
    enum class Operation {
        UnitConversion,
        Logarithm,
        Exponential,
        Power,
        SquareRoot,
        Percentage,
        ZScore,
        MinMax,
        Clamp,
        BucketRange
    };

    AdvancedNumericTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "advanced_numeric_transform"; }

    void configure(const YAML::Node& params) override;

private:
    double extract_numeric_value(const std::any& input) const;
    void configure_operation(const std::string& op_str);
    std::string get_operation_name() const;
    double bucket_value(double value) const;

    Operation operation_{Operation::UnitConversion};

    // Unit conversion parameters
    std::string from_unit_;
    std::string to_unit_;

    // Mathematical operation parameters
    double log_base_{0}; // 0 = natural log
    double exp_base_{0}; // 0 = e
    double power_exponent_{2.0};

    // Statistical parameters
    double mean_value_{0.0};
    double std_deviation_{1.0};
    double percentage_total_{100.0};

    // Range parameters
    double min_value_{0.0};
    double max_value_{1.0};
    double bucket_size_{1.0};

    // Output formatting
    int round_to_decimal_{-1}; // -1 = no rounding
    bool output_as_integer_{false};
};

/**
 * @brief Numeric validation transformation
 *
 * Validates numeric values against specified constraints and applies
 * validation rules including range checking and precision enforcement.
 */
class NumericValidationTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "numeric_validation"; }

    void configure(const YAML::Node& params) override;

private:
    double extract_numeric_value(const std::any& input) const;

    std::optional<double> min_allowed_;
    std::optional<double> max_allowed_;
    bool clamp_to_range_{false};
    bool reject_nan_{true};
    bool reject_infinity_{true};
    bool reject_null_{false};
    double default_value_{0.0};
    int required_precision_{-1};
    bool enforce_precision_{false};
    std::vector<double> allowed_values_;
};

} // namespace omop::transform