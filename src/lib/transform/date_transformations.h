#pragma once

/**
 * @file date_transformations.h
 * @brief Date and time transformation implementations
 *
 * Provides date and time transformation types including date calculations,
 * formatting, and validation transformations.
 */

#include "transform/transformations.h"
#include <string>
#include <vector>
#include <chrono>
#include <memory>
#include <yaml-cpp/yaml.h>
#include <optional>
#include <any>

namespace omop::transform {

/**
 * @brief Date calculation transformation
 *
 * Performs calculations on dates such as age calculation, date differences,
 * and date arithmetic operations.
 */
class DateCalculationTransformation : public ComplexTransformation {
public:
    enum class Operation {
        Age,
        DateDiff,
        AddDays,
        AddMonths,
        AddYears,
        StartOfMonth,
        EndOfMonth,
        StartOfYear,
        EndOfYear
    };

    DateCalculationTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "date_calculation"; }

    void configure(const YAML::Node& params) override;

private:
    void configure_operation(const std::string& op_str);
    std::string get_operation_name() const;
    std::any format_output(const std::chrono::system_clock::time_point& date);

    Operation operation_{Operation::Age};
    std::string input_format_{constants::DEFAULT_DATE_FORMAT};
    std::string output_format_{constants::DEFAULT_DATE_FORMAT};
    std::optional<std::chrono::system_clock::time_point> reference_date_;
    double offset_value_{0.0};
    std::string diff_unit_{"days"};
};

/**
 * @brief Date range validation transformation
 *
 * Validates dates against specified ranges and applies validation rules.
 */
class DateRangeValidationTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "date_range_validation"; }

    void configure(const YAML::Node& params) override;

private:
    std::optional<std::chrono::system_clock::time_point> min_date_;
    std::optional<std::chrono::system_clock::time_point> max_date_;
    bool clamp_to_range_{false};
    bool reject_future_dates_{true};
    bool set_future_to_today_{false};
    std::string input_format_{constants::DEFAULT_DATE_FORMAT};
    std::string output_format_{constants::DEFAULT_DATE_FORMAT};
};

} // namespace omop::transform