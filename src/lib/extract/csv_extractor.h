#pragma once

#include "core/interfaces.h"
#include "extractor_base.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>
#include <string>
#include <optional>
#include <vector>
#include <unordered_map>
#include <any>
#include <chrono>
#include "common/validation.h"

namespace omop::extract {

/**
 * @brief CSV parsing options
 */
struct CsvOptions {
    char delimiter{','};
    char quote_char{'"'};
    char escape_char{'\\'};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    std::vector<std::string> column_names;
    std::vector<std::string> column_types;
    bool skip_empty_lines{true};
    bool trim_fields{true};
    size_t skip_lines{0};
    std::optional<size_t> max_lines;
    std::string null_string{"NULL"};
    std::string true_string{"TRUE"};
    std::string false_string{"FALSE"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
};

/**
 * @brief CSV field parser
 *
 * Handles parsing of individual CSV fields with proper quote and escape handling.
 */
class CsvFieldParser {
public:
    /**
     * @brief Constructor
     * @param options CSV parsing options
     */
    explicit CsvFieldParser(const CsvOptions& options) : options_(options) {}

    /**
     * @brief Parse a single field
     * @param input Input string
     * @param pos Current position (updated)
     * @return std::string Parsed field value
     */
    std::string parse_field(const std::string& input, size_t& pos);

    /**
     * @brief Parse a complete line
     * @param line Input line
     * @return std::vector<std::string> Parsed fields
     */
    std::vector<std::string> parse_line(const std::string& line);

    /**
     * @brief Convert field to typed value
     * @param field Field value
     * @param type_hint Type hint (optional)
     * @return std::any Typed value
     */
    std::any convert_field(const std::string& field,
                          const std::string& type_hint = "");

    /**
     * @brief Parse date/time value
     * @param value String value
     * @param format Format string
     * @return std::chrono::system_clock::time_point Parsed time
     */
    std::chrono::system_clock::time_point parse_datetime(
        const std::string& value, const std::string& format) const;

    /**
     * @brief Trim whitespace from string
     * @param str String to trim
     * @return std::string Trimmed string
     */
    std::string trim(const std::string& str) const;

    /**
     * @brief Check if a string is valid UTF-8
     * @param str String to check
     * @return bool True if valid UTF-8, false otherwise
     */
    bool is_valid_utf8(const std::string& str) const;

private:
    CsvOptions options_;
};

/**
 * @brief CSV file extractor
 *
 * Extends ExtractorBase for CSV file sources,
 * providing efficient streaming extraction from CSV files with
 * comprehensive error handling, statistics, and progress tracking.
 */
class CsvExtractor : public ExtractorBase {
public:
    /**
     * @brief Constructor
     */
    CsvExtractor();

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Check if a file is a compressed CSV file by extension
     * @param filepath File path
     * @return bool True if file is compressed
     */
    static bool is_compressed_file(const std::string& filepath);

    /**
     * @brief Get the schema of the CSV data source
     * @return SourceSchema CSV schema information
     */
    virtual SourceSchema getSchema() const override;

    /**
     * @brief Validate the source CSV file
     * @return omop::common::ValidationResult Validation result
     */
    virtual omop::common::ValidationResult validateSource() override;

protected:
    // ExtractorBase pure virtual method implementations
    
    /**
     * @brief Connect to the CSV data source
     * @return bool True if connection successful
     */
    bool connect() override;
    
    /**
     * @brief Disconnect from the CSV data source
     */
    void disconnect() override;
    
    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return std::vector<core::Record> Vector of records
     */
    std::vector<core::Record> extractBatchImpl(size_t batch_size) override;
    
    /**
     * @brief Convert source data to Record format
     * @param source_data Source data (vector<string> of CSV fields)
     * @return core::Record Converted record
     */
    core::Record convertToRecord(const std::any& source_data) override;

    // CSV-specific methods
    
    /**
     * @brief Open CSV file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read and parse header
     */
    void read_header();

    /**
     * @brief Infer column types from data
     * @param sample_size Number of rows to sample
     */
    void infer_column_types(size_t sample_size = 100);

    /**
     * @brief Create record from parsed fields
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_record(const std::vector<std::string>& fields);

    /**
     * @brief Read a complete CSV record (handling multi-line quoted fields)
     * @param line Output line containing complete record
     * @return bool True if record was read successfully
     */
    bool read_complete_record(std::string& line);

protected:
    // CSV-specific members (protected so derived classes can access)
    std::ifstream file_stream_;                    ///< CSV file stream
    std::string filepath_;                         ///< Path to CSV file
    CsvOptions options_;                           ///< CSV parsing options
    CsvFieldParser parser_;                        ///< CSV field parser
    std::vector<std::string> column_names_;        ///< Column names from header
    std::vector<std::string> column_types_;        ///< Inferred column types
    size_t current_line_{0};                       ///< Current line number in file
    size_t total_lines_{0};                        ///< Total lines in file (if known)
    
    // Note: The following are now handled by ExtractorBase:
    // - extracted_count_ -> use stats_.successful_records
    // - error_count_ -> use stats_.failed_records  
    // - max_records_ -> use options_.max_records
    // - has_more_ -> use has_more_data_
    // - start_time_ -> use stats_.start_time
};

/**
 * @brief Multi-file CSV extractor
 *
 * Extends CsvExtractor to handle multiple CSV files as a single data source.
 */
class MultiFileCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    MultiFileCsvExtractor() = default;

    /**
     * @brief Initialize with multiple files
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract a batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Batch of extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "multi_csv"; }

protected:
    /**
     * @brief Move to next file
     * @return bool True if next file opened successfully
     */
    bool next_file();

    std::vector<std::string> file_paths_;
    size_t current_file_index_{0};
    bool skip_headers_after_first_{true};
};

/**
 * @brief CSV directory extractor
 *
 * Extracts data from all CSV files in a directory with pattern matching.
 */
class CsvDirectoryExtractor : public MultiFileCsvExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvDirectoryExtractor() = default;

    /**
     * @brief Initialize with directory
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv_directory"; }

protected:
    /**
     * @brief Find CSV files in directory
     * @param directory Directory path
     * @param pattern File name pattern (regex)
     * @param recursive Whether to search recursively
     * @return std::vector<std::string> File paths
     */
    std::vector<std::string> find_csv_files(const std::string& directory,
                                            const std::string& pattern = ".*\\.csv$",
                                            bool recursive = false);

private:
    std::string directory_path_;
    std::regex file_pattern_;
    bool recursive_search_{false};
};

/**
 * @brief Compressed CSV extractor
 *
 * Handles extraction from compressed CSV files (gzip, zip, etc.).
 */
class CompressedCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Compression format
     */
    enum class CompressionFormat {
        None,
        Gzip,
        Zip,
        Bzip2,
        Xz
    };

    /**
     * @brief Constructor
     */
    CompressedCsvExtractor() = default;

    /**
     * @brief Initialize with compressed file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "compressed_csv"; }

    /**
     * @brief Finalize extraction and cleanup
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Detect compression format
     * @param filepath File path
     * @return CompressionFormat Detected format
     */
    static CompressionFormat detect_compression(const std::string& filepath);

protected:

    /**
     * @brief Decompress file
     * @param filepath Compressed file path
     * @param format Compression format
     * @return std::string Path to decompressed file
     */
    std::string decompress_file(const std::string& filepath,
                               CompressionFormat format);

    /**
     * @brief Convert compression format to string
     * @param format Compression format
     * @return std::string String representation
     */
    std::string format_to_string(CompressionFormat format) const;

    /**
     * @brief Convert string to compression format
     * @param format_str String representation
     * @return CompressionFormat Compression format
     */
    CompressionFormat string_to_format(const std::string& format_str);

private:
    /**
     * @brief Decompress gzip file
     * @param src Source file path
     * @param dst Destination file path
     */
    void decompress_gzip(const std::string& src, const std::string& dst);
    
    /**
     * @brief Decompress zip file
     * @param src Source file path
     * @param dst Destination file path
     */
    void decompress_zip(const std::string& src, const std::string& dst);
    
    /**
     * @brief Decompress bzip2 file
     * @param src Source file path
     * @param dst Destination file path
     */
    void decompress_bzip2(const std::string& src, const std::string& dst);
    
    /**
     * @brief Decompress xz file
     * @param src Source file path
     * @param dst Destination file path
     */
    void decompress_xz(const std::string& src, const std::string& dst);

    CompressionFormat compression_format_{CompressionFormat::None};
    std::string temp_file_path_;
    bool cleanup_temp_file_{true};
};

/**
 * @brief CSV extractor factory
 */
class CsvExtractorFactory {
public:
    /**
     * @brief Create CSV extractor
     * @param type Extractor type (csv, multi_csv, csv_directory, compressed_csv)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register CSV extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline std::string CsvFieldParser::trim(const std::string& str) const {
    if (!options_.trim_fields) return str;

    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

inline std::string CsvFieldParser::parse_field(const std::string& input, size_t& pos) {
    std::string field;
    bool in_quotes = false;
    bool was_quoted = false;  // Track if field was originally quoted
    bool escape_next = false;

    // Check if we have leading whitespace before potential quote
    [[maybe_unused]] size_t leading_spaces = 0;
    size_t check_pos = pos;
    while (check_pos < input.length() && std::isspace(input[check_pos]) && input[check_pos] != '\n') {
        leading_spaces++;
        check_pos++;
    }

    // Check if field starts with quote (after any leading whitespace)
    if (check_pos < input.length() && input[check_pos] == options_.quote_char) {
        // This is a quoted field - skip leading whitespace and start after quote
        pos = check_pos + 1;
        in_quotes = true;
        was_quoted = true;
    }
    // If not quoted, start from original position (including any leading whitespace)

    while (pos < input.length()) {
        char c = input[pos];

        if (escape_next) {
            // Handle escape sequences
            switch (c) {
                case 't': field += '\t'; break;
                case 'n': field += '\n'; break;
                case 'r': field += '\r'; break;
                case '\\': field += '\\'; break;
                case '"': field += '"'; break;
                default: field += c; break;
            }
            escape_next = false;
            ++pos;
            continue;
        }

        if (c == options_.escape_char) {
            escape_next = true;
            ++pos;
            continue;
        }

        if (in_quotes) {
            if (c == options_.quote_char) {
                // Check for escaped quote (double quote)
                if (pos + 1 < input.length() && input[pos + 1] == options_.quote_char) {
                    field += c;
                    pos += 2;
                    continue;
                }
                in_quotes = false;
                ++pos;
                // Skip to delimiter or end of line
                while (pos < input.length() &&
                       input[pos] != options_.delimiter &&
                       input[pos] != '\n') {
                    ++pos;
                }
                break;
            }
            field += c;
            ++pos;
        } else {
            if (c == options_.delimiter || c == '\n') {
                break;
            }
            field += c;
            ++pos;
        }
    }

    // Skip delimiter if present
    if (pos < input.length() && input[pos] == options_.delimiter) {
        ++pos;
    }
    
    // Skip newline if present (to handle lines ending with \n)
    if (pos < input.length() && input[pos] == '\n') {
        ++pos;
    }

    // Validate UTF-8 encoding if specified
    if (options_.encoding == "UTF-8" && !field.empty() && !is_valid_utf8(field)) {
        auto logger = common::Logger::get("omop-csv-parser");
        logger->warn("Invalid UTF-8 sequence detected in field, attempting to sanitize");

        // Basic sanitization - replace invalid bytes with ?
        for (size_t i = 0; i < field.length(); ++i) {
            if (static_cast<unsigned char>(field[i]) > 0x7F && !is_valid_utf8(field.substr(i, 1))) {
                field[i] = '?';
            }
        }
    }

    // Only trim unquoted fields if trim_fields is enabled
    // Use was_quoted to preserve whitespace in originally quoted fields
    if (was_quoted) {
        // Preserve whitespace in quoted fields
        return field;
    } else if (options_.trim_fields) {
        return trim(field);
    }
    
    return field;
}

inline bool CsvFieldParser::is_valid_utf8(const std::string& str) const {
    const unsigned char* bytes = reinterpret_cast<const unsigned char*>(str.c_str());
    const unsigned char* end = bytes + str.length();

    while (bytes < end) {
        if (*bytes <= 0x7F) {
            // ASCII
            bytes++;
        } else if ((*bytes & 0xE0) == 0xC0) {
            // 2-byte sequence
            if (bytes + 1 >= end || (bytes[1] & 0xC0) != 0x80) return false;
            bytes += 2;
        } else if ((*bytes & 0xF0) == 0xE0) {
            // 3-byte sequence
            if (bytes + 2 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80) return false;
            bytes += 3;
        } else if ((*bytes & 0xF8) == 0xF0) {
            // 4-byte sequence
            if (bytes + 3 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80 || (bytes[3] & 0xC0) != 0x80) return false;
            bytes += 4;
        } else {
            return false;
        }
    }
    return true;
}

} // namespace omop::extract