/**
 * @file windows_utils.cpp
 * @brief Windows-specific utility functions implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifdef _WIN32

#include "extract/platform/windows_utils.h"
#include "common/exceptions.h"
#include <format>
#include <vector>
#include <random>

namespace omop::extract::platform {

std::wstring utf8_to_wide(const std::string& utf8_str) {
    if (utf8_str.empty()) {
        return std::wstring();
    }

    int required_size = MultiByteToWideChar(CP_UTF8, 0,
                                           utf8_str.c_str(),
                                           static_cast<int>(utf8_str.length()),
                                           nullptr, 0);

    if (required_size == 0) {
        throw common::PlatformException(
            std::format("Failed to convert UTF-8 to wide string: {}",
                       get_windows_error_message()));
    }

    std::wstring wide_str(required_size, L'\0');
    int result = MultiByteToWideChar(CP_UTF8, 0,
                                    utf8_str.c_str(),
                                    static_cast<int>(utf8_str.length()),
                                    wide_str.data(),
                                    required_size);

    if (result == 0) {
        throw common::PlatformException(
            std::format("Failed to convert UTF-8 to wide string: {}",
                       get_windows_error_message()));
    }

    return wide_str;
}

std::string wide_to_utf8(const std::wstring& wide_str) {
    if (wide_str.empty()) {
        return std::string();
    }

    int required_size = WideCharToMultiByte(CP_UTF8, 0,
                                           wide_str.c_str(),
                                           static_cast<int>(wide_str.length()),
                                           nullptr, 0, nullptr, nullptr);

    if (required_size == 0) {
        throw common::PlatformException(
            std::format("Failed to convert wide string to UTF-8: {}",
                       get_windows_error_message()));
    }

    std::string utf8_str(required_size, '\0');
    int result = WideCharToMultiByte(CP_UTF8, 0,
                                    wide_str.c_str(),
                                    static_cast<int>(wide_str.length()),
                                    utf8_str.data(),
                                    required_size,
                                    nullptr, nullptr);

    if (result == 0) {
        throw common::PlatformException(
            std::format("Failed to convert wide string to UTF-8: {}",
                       get_windows_error_message()));
    }

    return utf8_str;
}

std::string get_windows_error_message(DWORD error_code) {
    if (error_code == 0) {
        error_code = GetLastError();
    }

    LPWSTR message_buffer = nullptr;
    DWORD size = FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER |
        FORMAT_MESSAGE_FROM_SYSTEM |
        FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr,
        error_code,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        reinterpret_cast<LPWSTR>(&message_buffer),
        0,
        nullptr);

    if (size == 0) {
        return std::format("Unknown error code: {}", error_code);
    }

    std::wstring message(message_buffer, size);
    LocalFree(message_buffer);

    // Remove trailing newline characters
    while (!message.empty() &&
           (message.back() == L'\n' || message.back() == L'\r')) {
        message.pop_back();
    }

    return wide_to_utf8(message);
}

std::pair<WindowsFileHandle, WindowsFileHandle> create_file_mapping(const std::string& filepath) {
    std::wstring wide_path = utf8_to_wide(filepath);

    // Open file for reading
    WindowsFileHandle file_handle(CreateFileW(
        wide_path.c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL | FILE_FLAG_SEQUENTIAL_SCAN,
        nullptr));

    if (!file_handle.is_valid()) {
        throw common::PlatformException(
            std::format("Failed to open file '{}': {}",
                       filepath, get_windows_error_message()));
    }

    // Get file size
    LARGE_INTEGER file_size;
    if (!GetFileSizeEx(file_handle.get(), &file_size)) {
        throw common::PlatformException(
            std::format("Failed to get file size '{}': {}",
                       filepath, get_windows_error_message()));
    }

    // Create file mapping
    WindowsFileHandle mapping_handle(CreateFileMappingW(
        file_handle.get(),
        nullptr,
        PAGE_READONLY,
        file_size.HighPart,
        file_size.LowPart,
        nullptr));

    if (!mapping_handle.is_valid()) {
        throw common::PlatformException(
            std::format("Failed to create file mapping '{}': {}",
                       filepath, get_windows_error_message()));
    }

    return {std::move(file_handle), std::move(mapping_handle)};
}

void* map_view_of_file(HANDLE mapping_handle, size_t offset, size_t size) {
    LARGE_INTEGER offset_li;
    offset_li.QuadPart = offset;

    void* view = MapViewOfFile(
        mapping_handle,
        FILE_MAP_READ,
        offset_li.HighPart,
        offset_li.LowPart,
        size);

    if (!view) {
        throw common::PlatformException(
            std::format("Failed to map view of file: {}",
                       get_windows_error_message()));
    }

    return view;
}

bool unmap_view_of_file(void* view) {
    return UnmapViewOfFile(view) != 0;
}

size_t get_file_size(const std::string& filepath) {
    std::wstring wide_path = utf8_to_wide(filepath);

    WIN32_FILE_ATTRIBUTE_DATA file_info;
    if (!GetFileAttributesExW(wide_path.c_str(),
                             GetFileExInfoStandard,
                             &file_info)) {
        throw common::PlatformException(
            std::format("Failed to get file attributes '{}': {}",
                       filepath, get_windows_error_message()));
    }

    LARGE_INTEGER size;
    size.HighPart = file_info.nFileSizeHigh;
    size.LowPart = file_info.nFileSizeLow;

    return static_cast<size_t>(size.QuadPart);
}

bool is_network_path(const std::string& path) {
    // UNC paths start with \\
    if (path.length() >= 2 && path[0] == '\\' && path[1] == '\\') {
        return true;
    }

    // Check if drive is network mapped
    if (path.length() >= 2 && path[1] == ':') {
        std::string drive = path.substr(0, 2) + "\\";
        UINT drive_type = GetDriveTypeA(drive.c_str());
        return drive_type == DRIVE_REMOTE;
    }

    return false;
}

std::vector<char> get_available_drives() {
    std::vector<char> drives;
    DWORD drive_mask = GetLogicalDrives();

    for (char drive = 'A'; drive <= 'Z'; ++drive) {
        if (drive_mask & 1) {
            drives.push_back(drive);
        }
        drive_mask >>= 1;
    }

    return drives;
}

std::string get_temp_directory() {
    wchar_t temp_path[MAX_PATH + 1];
    DWORD result = GetTempPathW(MAX_PATH, temp_path);

    if (result == 0 || result > MAX_PATH) {
        throw common::PlatformException(
            std::format("Failed to get temp directory: {}",
                       get_windows_error_message()));
    }

    return wide_to_utf8(temp_path);
}

std::string create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();

    // Generate random suffix
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(100000, 999999);

    std::string filename = std::format("{}{}{}", prefix, dis(gen), extension);
    std::string full_path = temp_dir + filename;

    // Create the file
    std::wstring wide_path = utf8_to_wide(full_path);
    WindowsFileHandle file(CreateFileW(
        wide_path.c_str(),
        GENERIC_WRITE,
        0,
        nullptr,
        CREATE_NEW,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));

    if (!file.is_valid()) {
        // Try again with different random number
        filename = std::format("{}{}{}", prefix, dis(gen), extension);
        full_path = temp_dir + filename;
        wide_path = utf8_to_wide(full_path);

        file = WindowsFileHandle(CreateFileW(
            wide_path.c_str(),
            GENERIC_WRITE,
            0,
            nullptr,
            CREATE_NEW,
            FILE_ATTRIBUTE_NORMAL,
            nullptr));

        if (!file.is_valid()) {
            throw common::PlatformException(
                std::format("Failed to create temp file: {}",
                           get_windows_error_message()));
        }
    }

    return full_path;
}

bool set_file_attributes(const std::string& filepath, DWORD attributes) {
    std::wstring wide_path = utf8_to_wide(filepath);
    return SetFileAttributesW(wide_path.c_str(), attributes) != 0;
}

DWORD get_file_attributes(const std::string& filepath) {
    std::wstring wide_path = utf8_to_wide(filepath);
    return GetFileAttributesW(wide_path.c_str());
}

// WindowsHighResTimer implementation

WindowsHighResTimer::WindowsHighResTimer() {
    QueryPerformanceFrequency(&frequency_);
    reset();
}

void WindowsHighResTimer::reset() {
    QueryPerformanceCounter(&start_time_);
}

double WindowsHighResTimer::elapsed_seconds() const {
    LARGE_INTEGER current_time;
    QueryPerformanceCounter(&current_time);

    LONGLONG elapsed = current_time.QuadPart - start_time_.QuadPart;
    return static_cast<double>(elapsed) / frequency_.QuadPart;
}

double WindowsHighResTimer::elapsed_milliseconds() const {
    return elapsed_seconds() * 1000.0;
}

// Memory and process functions

MemoryInfo get_memory_info() {
    MEMORYSTATUSEX mem_status;
    mem_status.dwLength = sizeof(mem_status);

    if (!GlobalMemoryStatusEx(&mem_status)) {
        throw common::PlatformException(
            std::format("Failed to get memory info: {}",
                       get_windows_error_message()));
    }

    MemoryInfo info;
    info.total_physical = mem_status.ullTotalPhys;
    info.available_physical = mem_status.ullAvailPhys;
    info.total_virtual = mem_status.ullTotalVirtual;
    info.available_virtual = mem_status.ullAvailVirtual;

    return info;
}

bool set_process_priority(DWORD priority) {
    HANDLE process = GetCurrentProcess();
    return SetPriorityClass(process, priority) != 0;
}

bool enable_large_pages() {
    // Check if we have the required privilege
    HANDLE token;
    if (!OpenProcessToken(GetCurrentProcess(),
                         TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
                         &token)) {
        return false;
    }

    TOKEN_PRIVILEGES tp;
    tp.PrivilegeCount = 1;
    tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

    if (!LookupPrivilegeValueW(nullptr, SE_LOCK_MEMORY_NAME,
                              &tp.Privileges[0].Luid)) {
        CloseHandle(token);
        return false;
    }

    BOOL result = AdjustTokenPrivileges(token, FALSE, &tp, 0, nullptr, nullptr);
    DWORD error = GetLastError();
    CloseHandle(token);

    return result && error == ERROR_SUCCESS;
}

} // namespace omop::extract::platform

#endif // _WIN32