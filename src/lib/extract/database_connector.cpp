/**
 * @file database_connector.cpp
 * @brief Implementation of database connection interfaces
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "database_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <chrono>
#include <thread>
#include <queue>
#include <condition_variable>
#include <sstream>
#include <any>
#include <regex>
#include <future>
#include <iostream>

namespace omop::extract {

// DatabaseExtractor Implementation

void DatabaseExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                 [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");
    logger->info("Initializing database extractor");

    start_time_ = std::chrono::steady_clock::now();

    // Extract configuration parameters
    if (config.find("table") == config.end()) {
        throw common::ConfigurationException("Database extractor requires 'table' parameter");
    }
    table_name_ = std::any_cast<std::string>(config.at("table"));

    // Optional parameters
    if (config.find("schema") != config.end()) {
        schema_name_ = std::any_cast<std::string>(config.at("schema"));
    }

    if (config.find("columns") != config.end()) {
        columns_ = std::any_cast<std::vector<std::string>>(config.at("columns"));
    }

    if (config.find("filter") != config.end()) {
        filter_condition_ = std::any_cast<std::string>(config.at("filter"));

        // Validate filter condition for common injection patterns
        std::string filter_upper = filter_condition_;
        std::transform(filter_upper.begin(), filter_upper.end(), filter_upper.begin(), ::toupper);
        if (filter_upper.find("EXEC") != std::string::npos ||
            filter_upper.find("DROP") != std::string::npos ||
            filter_upper.find("CREATE") != std::string::npos ||
            filter_upper.find("ALTER") != std::string::npos) {
            throw common::SecurityException(
                "Filter condition contains potentially dangerous SQL keywords", "database");
        }
    }

    if (config.find("order_by") != config.end()) {
        order_by_ = std::any_cast<std::string>(config.at("order_by"));

        // Validate ORDER BY clause - should only contain column names and ASC/DESC
        std::regex order_by_pattern("^[a-zA-Z0-9_,\\s]+(\\s+(ASC|DESC))?$", std::regex::icase);
        if (!std::regex_match(order_by_, order_by_pattern)) {
            throw common::SecurityException("Invalid ORDER BY clause format", "database");
        }
    }

    // Verify connection
    if (!connection_->is_connected()) {
        throw common::DatabaseException("Database connection not established",
                                      connection_->get_database_type(), 0);
    }

    // Verify table exists
    if (!connection_->table_exists(table_name_, schema_name_)) {
        std::string schema_display = schema_name_.empty() ? "default" : schema_name_;
        throw common::DatabaseException(
            "Table '" + table_name_ + "' does not exist in schema '" + schema_display + "'",
            connection_->get_database_type(), 0);
    }

    // Execute initial query to get result set
    std::string query = build_query();
    logger->info("Executing extraction query: {}", query);

    try {
        current_result_set_ = connection_->execute_query(query);
        has_more_data_ = current_result_set_ != nullptr;
    } catch (const std::exception& e) {
        throw common::ExtractionException(
            "Failed to execute extraction query: " + std::string(e.what()),
            connection_->get_database_type());
    }
}

core::RecordBatch DatabaseExtractor::extract_batch(size_t batch_size,
                                                [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");
    
    if (!has_more_data_ || current_result_set_ == nullptr) {
        logger->debug("No more data to extract");
        return core::RecordBatch{};
    }

    core::RecordBatch batch(batch_size);
    size_t records_extracted = 0;

    try {
        while (records_extracted < batch_size && current_result_set_->next()) {
            core::Record record;
            
            // Extract all columns from the result set
            for (size_t i = 0; i < current_result_set_->column_count(); ++i) {
                std::string column_name = current_result_set_->column_name(i);
                std::any value = current_result_set_->get_value(column_name);
                record.setField(column_name, value);
            }

            batch.addRecord(std::move(record));
            records_extracted++;
        }

        // Check if we've reached the end of the result set
        if (records_extracted < batch_size) {
            has_more_data_ = false;
            current_result_set_.reset();
            logger->info("Extraction completed. Total records extracted: {}", total_extracted_);
        }

        total_extracted_ += records_extracted;
        logger->debug("Extracted batch of {} records", records_extracted);

    } catch (const std::exception& e) {
        logger->error("Error during batch extraction: {}", e.what());
        throw common::ExtractionException(
            "Failed to extract batch: " + std::string(e.what()),
            connection_->get_database_type());
    }

    return batch;
}

void DatabaseExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");

    // Clean up result set
    current_result_set_.reset();

    // Log final statistics
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("Database extraction completed: {} records in {} seconds ({:.2f} records/sec)",
                total_extracted_, duration,
                duration > 0 ? static_cast<double>(total_extracted_) / duration : 0.0);
}

std::unordered_map<std::string, std::any> DatabaseExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["total_records"] = total_extracted_;
    stats["records_extracted"] = total_extracted_;  // Add test-expected key
    stats["batch_count"] = batch_count_;
    stats["table_name"] = table_name_;
    stats["schema_name"] = schema_name_;
    stats["database_type"] = connection_->get_database_type();

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;
    stats["extraction_time"] = duration;  // Add test-expected key

    return stats;
}

std::string DatabaseExtractor::apply_filters(const std::string& base_query) const {
    if (filter_condition_.empty()) {
        return base_query;
    }

    // Check if query already has WHERE clause
    std::string upper_query = base_query;
    std::transform(upper_query.begin(), upper_query.end(), upper_query.begin(), ::toupper);

    size_t where_pos = upper_query.find(" WHERE ");
    if (where_pos != std::string::npos) {
        // Append to existing WHERE clause
        return base_query + " AND " + filter_condition_;
    } else {
        // Add new WHERE clause
        return base_query + " WHERE " + filter_condition_;
    }
}

std::chrono::milliseconds DatabaseExtractor::get_processing_time() const {
    auto end_time = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
}

std::string DatabaseExtractor::build_query() const {
    std::stringstream query;

    // SELECT clause
    query << "SELECT ";
    if (columns_.empty()) {
        query << "*";
    } else {
        for (size_t i = 0; i < columns_.size(); ++i) {
            if (i > 0) query << ", ";
            query << columns_[i];
        }
    }

    // FROM clause
    query << " FROM ";
    if (!schema_name_.empty()) {
        query << schema_name_ << ".";
    }
    query << table_name_;

    // WHERE clause
    if (!filter_condition_.empty()) {
        query << " WHERE " << filter_condition_;
    }

    // ORDER BY clause
    if (!order_by_.empty()) {
        query << " ORDER BY " << order_by_;
    }

    // LIMIT clause
    if (limit_ > 0) {
        query << " LIMIT " << limit_;
    }

    return query.str();
}

// ConnectionPool Implementation

ConnectionPool::ConnectionPool(size_t min_connections,
                             size_t max_connections,
                             std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory)
    : min_connections_(min_connections),
      max_connections_(max_connections),
      connection_factory_(connection_factory),
      shutdown_(false),
      total_acquisitions_(0),
      total_releases_(0),
      wait_count_(0),
      active_connections_(0),
      total_wait_time_(0) {
    
    auto logger = common::Logger::get("omop-connection-pool");
    logger->info("Initializing connection pool: min={}, max={}", min_connections, max_connections);
    
    // Pre-populate with minimum connections
    for (size_t i = 0; i < min_connections; ++i) {
        try {
            auto connection = connection_factory_();
            if (connection) {
                // Check if connection is already connected
                if (!connection->is_connected()) {
                    // Connection needs parameters to connect, skip pre-population
                    logger->debug("Skipping pre-population of connection {} - requires parameters", i);
                    break;
                } else {
                    idle_connections_.push(std::move(connection));
                }
            } else {
                logger->warn("Failed to create initial connection {}", i);
            }
        } catch (const std::exception& e) {
            logger->error("Error creating initial connection {}: {}", i, e.what());
        }
    }
    
    logger->info("Connection pool initialized with {} idle connections", idle_connections_.size());
}

ConnectionPool::~ConnectionPool() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    logger->info("Shutting down connection pool");
    
    shutdown_ = true;
    
    // Clear idle connections
    while (!idle_connections_.empty()) {
        idle_connections_.pop();
    }
    
    logger->info("Connection pool shutdown complete");
}

std::unique_ptr<IDatabaseConnection> ConnectionPool::acquire(int timeout_ms) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto start_time = std::chrono::steady_clock::now();
    total_acquisitions_++;
    
    auto logger = common::Logger::get("omop-connection-pool");
    
    while (!shutdown_) {
        // Try to get an idle connection
        if (!idle_connections_.empty()) {
            auto connection = std::move(idle_connections_.front());
            idle_connections_.pop();
            active_connections_++;
            
            logger->debug("Acquired connection from pool. Active: {}, Idle: {}", 
                         active_connections_.load(), idle_connections_.size());
            
            return connection;
        }
        
        // Try to create a new connection if under max limit
        if (active_connections_ + idle_connections_.size() < max_connections_) {
            try {
                auto connection = connection_factory_();
                if (connection) {
                    // Check if connection is already connected
                    if (!connection->is_connected()) {
                        // Can't create new connection without parameters
                        logger->debug("Cannot create new connection - requires parameters");
                    } else {
                        active_connections_++;

                        logger->debug("Created new connection. Active: {}, Idle: {}",
                                     active_connections_.load(), idle_connections_.size());

                        return connection;
                    }
                } else {
                    logger->warn("Failed to create new connection");
                }
            } catch (const std::exception& e) {
                logger->error("Error creating new connection: {}", e.what());
            }
        }
        
        // Wait for a connection to become available
        if (timeout_ms > 0) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time);
            
            if (elapsed.count() >= timeout_ms) {
                wait_count_++;
                total_wait_time_ += elapsed.count();
                
                logger->warn("Connection acquisition timeout after {}ms", timeout_ms);
                throw common::DatabaseException(
                    "Connection acquisition timeout after " + std::to_string(timeout_ms) + "ms",
                    "connection_pool", 0);
            }
            
            auto remaining_time = std::chrono::milliseconds(timeout_ms) - elapsed;
            cv_.wait_for(lock, remaining_time);
        } else {
            wait_count_++;
            cv_.wait(lock);
        }
    }
    
    logger->warn("Connection pool is shutting down");
    return nullptr;
}

void ConnectionPool::release(std::unique_ptr<IDatabaseConnection> connection) {
    if (!connection) {
        return;
    }
    
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    
    if (shutdown_) {
        logger->debug("Pool is shutting down, discarding connection");
        return;
    }
    
    // Check if connection is still valid
    if (!connection->is_connected()) {
        logger->debug("Connection is not connected, discarding");
        active_connections_--;
        return;
    }
    
    // Return to idle pool if under max connections
    if (idle_connections_.size() < max_connections_) {
        idle_connections_.push(std::move(connection));
        active_connections_--;
        total_releases_++;
        
        logger->debug("Released connection to pool. Active: {}, Idle: {}", 
                     active_connections_.load(), idle_connections_.size());
        
        cv_.notify_one();
    } else {
        logger->debug("Pool is full, discarding connection");
        active_connections_--;
    }
}

ConnectionPool::PoolStats ConnectionPool::get_statistics() const {
    std::unique_lock<std::mutex> lock(mutex_);
    
    PoolStats stats;
    auto active_count = active_connections_.load();
    auto total_acq = total_acquisitions_.load();
    auto total_rel = total_releases_.load();
    auto wait_cnt = wait_count_.load();
    auto total_wait = total_wait_time_.load();
    
    stats.total_connections = active_count + idle_connections_.size();
    stats.active_connections = active_count;
    stats.idle_connections = idle_connections_.size();
    stats.total_acquisitions = total_acq;
    stats.total_releases = total_rel;
    stats.wait_count = wait_cnt;
    stats.total_wait_time_ms = total_wait;
    stats.avg_wait_time = wait_cnt > 0 ? std::chrono::milliseconds(total_wait / wait_cnt) : std::chrono::milliseconds(0);

    return stats;
}

void ConnectionPool::clear_idle_connections() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    size_t cleared = idle_connections_.size();
    
    while (!idle_connections_.empty()) {
        idle_connections_.pop();
    }
    
    logger->info("Cleared {} idle connections", cleared);
}

size_t ConnectionPool::validate_connections() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    size_t valid_count = 0;
    size_t invalid_count = 0;
    
    std::queue<std::unique_ptr<IDatabaseConnection>> valid_connections;
    
    while (!idle_connections_.empty()) {
        auto connection = std::move(idle_connections_.front());
        idle_connections_.pop();
        
        if (connection && connection->is_connected()) {
            valid_connections.push(std::move(connection));
            valid_count++;
        } else {
            invalid_count++;
        }
    }
    
    idle_connections_ = std::move(valid_connections);
    
    logger->info("Connection validation complete: {} valid, {} invalid", valid_count, invalid_count);
    
    return valid_count;
}

size_t ConnectionPool::get_connection_pool_size() const {
    std::unique_lock<std::mutex> lock(mutex_);
    return idle_connections_.size() + active_connections_.load();
}

// DatabaseConnectionFactory implementation

std::unique_ptr<IDatabaseConnection> DatabaseConnectionFactory::create_from_config(
    const std::unordered_map<std::string, std::any>& config) {

    // Extract type from config
    auto type_it = config.find("type");
    if (type_it == config.end()) {
        throw std::runtime_error("Database type not specified in config");
    }
    std::string type = std::any_cast<std::string>(type_it->second);

    IDatabaseConnection::ConnectionParams params;

    // Extract connection parameters from config
    auto host_it = config.find("host");
    if (host_it != config.end()) {
        params.host = std::any_cast<std::string>(host_it->second);
    }

    auto port_it = config.find("port");
    if (port_it != config.end()) {
        params.port = std::any_cast<int>(port_it->second);
    }

    auto database_it = config.find("database");
    if (database_it != config.end()) {
        params.database = std::any_cast<std::string>(database_it->second);
    }

    auto username_it = config.find("username");
    if (username_it != config.end()) {
        params.username = std::any_cast<std::string>(username_it->second);
    }

    auto password_it = config.find("password");
    if (password_it != config.end()) {
        params.password = std::any_cast<std::string>(password_it->second);
    }

    auto options_it = config.find("options");
    if (options_it != config.end()) {
        params.options = std::any_cast<std::unordered_map<std::string, std::string>>(options_it->second);
    }

    auto connection = create(type, params);

    return connection;
}

} // namespace omop::extract