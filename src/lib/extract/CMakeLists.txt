# Extract library CMakeLists.txt

add_library(omop_extract STATIC)

set(EXTRACT_SOURCES
    connection_pool.cpp
    csv_extractor.cpp
    extractor_base.cpp
    extract_utils.cpp
    database_connector.cpp
    extractor_factory.cpp
    json_extractor.cpp
)

set(EXTRACT_HEADERS
    csv_extractor.h
    database_connector.h
    extract.h
    extractor_base.h
    extract_utils.h
    extractor_factory.h
    json_extractor.h
)

target_sources(omop_extract PRIVATE ${EXTRACT_SOURCES})

omop_configure_library(omop_extract
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_core
        omop_common
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        spdlog::spdlog
        ZLIB::ZLIB
        Threads::Threads
        $<$<BOOL:${HAVE_LIBARCHIVE}>:LibArchive::LibArchive>
        $<$<PLATFORM_ID:Linux>:bz2>
        $<$<PLATFORM_ID:Darwin>:bz2>
        $<$<PLATFORM_ID:Linux>:lzma>
        $<$<PLATFORM_ID:Darwin>:lzma>
    HEADERS
        ${EXTRACT_HEADERS}
)

# Platform-specific sources
if(WIN32)
    target_sources(omop_extract PRIVATE platform/windows_utils.cpp)
elseif(UNIX)
    target_sources(omop_extract PRIVATE platform/unix_utils.cpp)
endif()

# PostgreSQL support
find_package(PostgreSQL)
if(PostgreSQL_FOUND)
    target_sources(omop_extract PRIVATE postgresql_connector.cpp)
    list(APPEND EXTRACT_HEADERS postgresql_connector.h)
    set(POSTGRESQL_SUPPORT_ENABLED TRUE)
endif()

# MySQL support
find_package(MySQL)
if(MySQL_FOUND OR TARGET MySQL::MySQL)
    target_sources(omop_extract PRIVATE mysql_connector.cpp)
    list(APPEND EXTRACT_HEADERS mysql_connector.h)
    set(MYSQL_SUPPORT_ENABLED TRUE)
endif()

# ODBC support
find_package(ODBC)
if(ODBC_FOUND)
    target_sources(omop_extract PRIVATE odbc_connector.cpp)
    list(APPEND EXTRACT_HEADERS odbc_connector.h)
    set(ODBC_SUPPORT_ENABLED TRUE)
endif()

# Configure PostgreSQL if found
if(POSTGRESQL_SUPPORT_ENABLED)
    if(PostgreSQL_INCLUDE_DIRS)
        target_include_directories(omop_extract PRIVATE ${PostgreSQL_INCLUDE_DIRS})
    endif()
    if(PostgreSQL_LIBRARIES)
        target_link_libraries(omop_extract PRIVATE ${PostgreSQL_LIBRARIES})
    endif()
    target_compile_definitions(omop_extract PUBLIC OMOP_HAS_POSTGRESQL)
endif()

# Configure MySQL if found and libraries exist
if(MYSQL_SUPPORT_ENABLED)
    if(MySQL_INCLUDE_DIRS)
        target_include_directories(omop_extract PRIVATE ${MySQL_INCLUDE_DIRS})
    endif()
    if(MySQL_LIBRARIES AND EXISTS "${MySQL_LIBRARIES}")
        target_link_libraries(omop_extract PRIVATE ${MySQL_LIBRARIES})
    endif()
    target_compile_definitions(omop_extract PUBLIC OMOP_HAS_MYSQL)
endif()

# Configure ODBC if found
if(ODBC_SUPPORT_ENABLED)
    target_include_directories(omop_extract PRIVATE ${ODBC_INCLUDE_DIRS})
    target_link_libraries(omop_extract PRIVATE ${ODBC_LIBRARIES})
    target_compile_definitions(omop_extract PUBLIC OMOP_HAS_ODBC)
endif()

# Install platform-specific headers
install(DIRECTORY platform/
    DESTINATION include/omop/extract/platform
    FILES_MATCHING PATTERN "*.h"
)

# Generate export header
include(GenerateExportHeader)
generate_export_header(omop_extract
    EXPORT_FILE_NAME ${CMAKE_CURRENT_BINARY_DIR}/extract_export.h
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/extract_export.h
    DESTINATION include/omop/extract
)
