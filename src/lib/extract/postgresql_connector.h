#pragma once

#ifdef OMOP_HAS_POSTGRESQL
#include "extract/database_connector.h"
#include <libpq-fe.h>
#include <memory>
#include <mutex>

namespace omop::extract {

/**
 * @brief PostgreSQL result set implementation
 *
 * This class provides access to PostgreSQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class PostgreSQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param result PostgreSQL result handle
     */
    explicit PostgreSQLResultSet(PGresult* result);

    /**
     * @brief Destructor
     */
    ~PostgreSQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert PostgreSQL value to appropriate type
     * @param value String value from PostgreSQL
     * @param oid PostgreSQL type OID
     * @return std::any Converted value
     */
    std::any convert_value(const char* value, Oid oid) const;

    PGresult* result_;
    int row_count_;
    int current_row_;
    int column_count_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
};

/**
 * @brief PostgreSQL prepared statement implementation
 *
 * This class implements prepared statements for PostgreSQL,
 * providing parameterized query execution.
 */
class PostgreSQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param statement_name Prepared statement name
     * @param sql SQL query
     */
    PostgreSQLPreparedStatement(PGconn* connection,
                              const std::string& statement_name,
                              const std::string& sql);

    /**
     * @brief Destructor
     */
    ~PostgreSQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Convert parameter value to string
     * @param value Parameter value
     * @return std::string String representation
     */
    std::string convert_parameter(const std::any& value) const;
    
    /**
     * @brief Convert integer array to PostgreSQL array format
     * @param array Vector of integers
     * @return std::string PostgreSQL array representation
     */
    std::string convert_int_array(const std::vector<int>& array) const;
    
    /**
     * @brief Convert string array to PostgreSQL array format
     * @param array Vector of strings
     * @return std::string PostgreSQL array representation
     */
    std::string convert_string_array(const std::vector<std::string>& array) const;
    
    /**
     * @brief Convert double array to PostgreSQL array format
     * @param array Vector of doubles
     * @return std::string PostgreSQL array representation
     */
    std::string convert_double_array(const std::vector<double>& array) const;
    
    /**
     * @brief Escape string value for PostgreSQL array
     * @param value String value to escape
     * @return std::string Escaped string
     */
    std::string escape_array_element(const std::string& value) const;

    PGconn* connection_;
    std::string statement_name_;
    std::string sql_;
    std::vector<std::string> parameters_;
    std::vector<const char*> param_values_;
    std::vector<int> param_lengths_;
    std::vector<int> param_formats_;
};

/**
 * @brief PostgreSQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for PostgreSQL databases, using libpq for database operations.
 */
class PostgreSQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    PostgreSQLConnection();

    /**
     * @brief Destructor
     */
    ~PostgreSQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "PostgreSQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw PostgreSQL connection handle
     * @return PGconn* Connection handle (for internal use)
     */
    PGconn* get_raw_connection() { return connection_; }

    bool in_transaction() const override { return in_transaction_; }

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string PostgreSQL connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Check for PostgreSQL errors
     * @param result Query result
     * @param operation Operation description
     */
    void check_error(PGresult* result, const std::string& operation) const;

    /**
     * @brief Generate unique statement name
     * @return std::string Unique statement name
     */
    std::string generate_statement_name();

    PGconn* connection_;
    bool in_transaction_;
    int query_timeout_;
    mutable std::mutex connection_mutex_;
    std::atomic<int> statement_counter_;
};

/**
 * @brief PostgreSQL-specific database extractor
 *
 * This class extends DatabaseExtractor with PostgreSQL-specific optimizations
 * such as cursor-based extraction for large result sets.
 */
class PostgreSQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     */
    explicit PostgreSQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Constructor with type
     * @param connection PostgreSQL connection
     * @param type The type string used to create this extractor
     */
    PostgreSQLExtractor(std::unique_ptr<IDatabaseConnection> connection, const std::string& type)
        : DatabaseExtractor(std::move(connection)), type_(type) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return type_.empty() ? "postgresql" : type_; }

protected:
    /**
     * @brief Build extraction query with PostgreSQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;

    /**
     * @brief Apply PostgreSQL-specific query hints
     * @param query Base query
     * @return std::string Query with hints
     */
    std::string apply_query_hints(const std::string& query) const;

private:
    std::string type_{"postgresql"};  // Default type
};

/**
 * @brief Registration helper for PostgreSQL components
 *
 * This class handles the registration of PostgreSQL components
 * with the appropriate factories during application startup.
 */
class PostgreSQLRegistrar {
public:
    /**
     * @brief Register all PostgreSQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "postgresql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "postgres",  // Alias
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    PostgreSQLRegistrar() = default;
};

} // namespace omop::extract

#endif // OMOP_HAS_POSTGRESQL