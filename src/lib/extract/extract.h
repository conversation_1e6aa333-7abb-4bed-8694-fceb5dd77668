/**
 * @file extract.h
 * @brief Comprehensive header for the OMOP ETL extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This header provides convenient access to all extraction functionality
 * within the OMOP ETL pipeline, including various data source extractors
 * and supporting utilities.
 */

#pragma once

// Core interfaces
#include "core/interfaces.h"

// Base extractor class (includes schema definitions)
#include "extract/extractor_base.h"

// Extractor factory functionality
#include "extract/extractor_factory.h"

// File-based extractors
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"

// Database extractors
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif

// Extraction utilities
#include "extract/extract_utils.h"

// Common utilities
#include "common/exceptions.h"
#include "common/logging.h"
#include <mutex>

/**
 * @namespace omop::extract
 * @brief Data extraction functionality for the OMOP ETL pipeline
 *
 * The extract namespace contains all components responsible for extracting
 * data from various sources including files (CSV, JSON) and databases
 * (PostgreSQL, MySQL, ODBC). The module provides a unified interface for
 * data extraction with support for batch processing, error handling, and
 * progress monitoring.
 */
namespace omop::extract {

// All utility functions and classes are now defined in extract_utils.h

} // namespace omop::extract