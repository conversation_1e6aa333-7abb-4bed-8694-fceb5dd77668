/**
 * @file csv_extractor.cpp
 * @brief Implementation of CSV data extractor
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "csv_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "extractor_factory.h"
#include <algorithm>
#include <cctype>
#include <iomanip>
#include <sstream>
#include <any>
#include <string>
#include <array>
#include <cstdlib>
#include "common/validation.h"
#include <zlib.h>
#ifdef HAVE_BZLIB
#include <bzlib.h>
#endif
#ifdef HAVE_LZMA
#include <lzma.h>
#endif
#ifdef HAVE_LIBARCHIVE
#include <archive.h>
#include <archive_entry.h>
#endif

namespace omop::extract {

// UTF-8 validation helper
[[maybe_unused]] static bool is_valid_utf8(const std::string& str) {
    const unsigned char* bytes = reinterpret_cast<const unsigned char*>(str.c_str());
    const unsigned char* end = bytes + str.length();

    while (bytes < end) {
        if (*bytes <= 0x7F) {
            // ASCII
            bytes++;
        } else if ((*bytes & 0xE0) == 0xC0) {
            // 2-byte sequence
            if (bytes + 1 >= end || (bytes[1] & 0xC0) != 0x80) return false;
            bytes += 2;
        } else if ((*bytes & 0xF0) == 0xE0) {
            // 3-byte sequence
            if (bytes + 2 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80) return false;
            bytes += 3;
        } else if ((*bytes & 0xF8) == 0xF0) {
            // 4-byte sequence
            if (bytes + 3 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80 || (bytes[3] & 0xC0) != 0x80) return false;
            bytes += 4;
        } else {
            return false;
        }
    }
    return true;
}

// CsvExtractor implementation

CsvExtractor::CsvExtractor() 
    : ExtractorBase("csv", nullptr, common::Logger::get("omop-csv-extractor")), 
      parser_(options_) {
}

// CsvFieldParser implementation

std::any CsvFieldParser::convert_field(const std::string& field, const std::string& type_hint) {
    // Handle null values - check for empty, null string, and case-insensitive "null"
    if (field.empty() || field == options_.null_string) {
        return std::any{};
    }
    
    // Check for case-insensitive "null"
    std::string lower_field = field;
    std::transform(lower_field.begin(), lower_field.end(), lower_field.begin(), ::tolower);
    if (lower_field == "null") {
        return std::any{};
    }

    // If no type hint, try to infer type
    if (type_hint.empty()) {
        // Try integer first (prioritize numbers over booleans)
        try {
            size_t pos;
            long long int_val = std::stoll(field, &pos);
            if (pos == field.length()) {
                return int_val;
            }
        } catch (const std::exception&) {
            // Fall through to try double
        }

        // Try double
        try {
            size_t pos;
            double double_val = std::stod(field, &pos);
            if (pos == field.length()) {
                return double_val;
            }
        } catch (const std::exception&) {
            // Fall through to try boolean
        }

        // Try boolean (only if it's not a number)
        std::string upper_field = field;
        std::transform(upper_field.begin(), upper_field.end(), upper_field.begin(), ::toupper);

        if (upper_field == options_.true_string || upper_field == "TRUE" ||
            upper_field == "YES" || upper_field == "Y" || upper_field == "T" ||
            upper_field == "ON" || upper_field == "1") {
            return true;
        }
        if (upper_field == options_.false_string || upper_field == "FALSE" ||
            upper_field == "NO" || upper_field == "N" || upper_field == "F" ||
            upper_field == "OFF" || upper_field == "0") {
            return false;
        }

        // Try date/datetime
        auto datetime = parse_datetime(field, options_.datetime_format);
        if (datetime.time_since_epoch().count() > 0) {
            return datetime;
        }

        auto date = parse_datetime(field, options_.date_format);
        if (date.time_since_epoch().count() > 0) {
            return date;
        }

        // Default to string
        return field;
    }

    // Convert based on type hint
    if (type_hint == "boolean" || type_hint == "bool") {
        std::string upper_field = field;
        std::transform(upper_field.begin(), upper_field.end(), upper_field.begin(), ::toupper);
        return upper_field == options_.true_string || upper_field == "1" ||
               upper_field == "TRUE" || upper_field == "YES" || upper_field == "Y" ||
               upper_field == "T" || upper_field == "ON";
    }
    else if (type_hint == "integer" || type_hint == "int" || type_hint == "bigint") {
        try {
            size_t pos;
            long long int_val = std::stoll(field, &pos);
            // Check if the entire string was consumed
            if (pos == field.length()) {
                return int_val;
            } else {
                // Not all characters were consumed, fall back to string
                return field;
            }
        } catch (const std::exception&) {
            // Fall back to string on conversion error
            return field;
        }
    }
    else if (type_hint == "double" || type_hint == "float" || type_hint == "decimal") {
        try {
            size_t pos;
            double double_val = std::stod(field, &pos);
            // Check if the entire string was consumed
            if (pos == field.length()) {
                return double_val;
            } else {
                // Not all characters were consumed, fall back to string
                return field;
            }
        } catch (const std::exception&) {
            // Fall back to string on conversion error
            return field;
        }
    }
    else if (type_hint == "date") {
        return parse_datetime(field, options_.date_format);
    }
    else if (type_hint == "datetime" || type_hint == "timestamp") {
        return parse_datetime(field, options_.datetime_format);
    }
    else {
        return field;
    }
}

std::chrono::system_clock::time_point CsvFieldParser::parse_datetime(
    const std::string& value, const std::string& format) const {

    std::tm tm = {};
    std::stringstream ss(value);
    ss >> std::get_time(&tm, format.c_str());

    if (ss.fail()) {
        return std::chrono::system_clock::time_point{};
    }

    return std::chrono::system_clock::from_time_t(std::mktime(&tm));
}

std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
    std::vector<std::string> fields;
    // Pre-allocate based on expected number of fields
    fields.reserve(32); // Common CSV files have < 32 columns

    size_t pos = 0;
    // Use string_view to avoid copies during parsing
    [[maybe_unused]] std::string_view line_view(line);

    while (pos < line.length()) {
        size_t old_pos = pos;
        fields.push_back(parse_field(line, pos));
        
        // Safeguard against infinite loops - ensure pos always advances
        if (pos == old_pos) {
            // If parse_field didn't advance position, force advance by 1
            ++pos;
        }
    }

    return fields;
}

// CsvExtractor implementation

void CsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                            [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Initializing CSV extractor");
    
    // Debug all config keys
    for (const auto& [key, value] : config) {
        logger->info("CSV Config key: '{}' type: {}", key, value.type().name());
    }
    logger->info("CSV extractor about to process config parameters");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ConfigurationException("CSV extractor requires 'filepath' parameter");
    }
    try {
        filepath_ = std::any_cast<std::string>(config.at("filepath"));
        logger->debug("Filepath: {}", filepath_);
    } catch (const std::bad_any_cast& e) {
        logger->error("Failed to cast filepath: {}", e.what());
        throw;
    }

    // Configure options
    if (config.find("delimiter") != config.end()) {
        try {
            // Try char first, then string
            try {
                options_.delimiter = std::any_cast<char>(config.at("delimiter"));
            } catch (const std::bad_any_cast&) {
                // Try as string and take first character
                std::string delim_str = std::any_cast<std::string>(config.at("delimiter"));
                if (!delim_str.empty()) {
                    options_.delimiter = delim_str[0];
                } else {
                    throw std::invalid_argument("Delimiter string cannot be empty");
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast delimiter (should be char or string): {}", e.what());
            throw;
        }
    }
    if (config.find("quote_char") != config.end()) {
        try {
            // Try char first, then string
            try {
                options_.quote_char = std::any_cast<char>(config.at("quote_char"));
            } catch (const std::bad_any_cast&) {
                // Try as string and take first character
                std::string quote_str = std::any_cast<std::string>(config.at("quote_char"));
                if (!quote_str.empty()) {
                    options_.quote_char = quote_str[0];
                } else {
                    throw std::invalid_argument("Quote char string cannot be empty");
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast quote_char (should be char or string): {}", e.what());
            throw;
        }
    }
    if (config.find("escape_char") != config.end()) {
        try {
            // Try char first, then string
            try {
                options_.escape_char = std::any_cast<char>(config.at("escape_char"));
            } catch (const std::bad_any_cast&) {
                // Try as string and take first character
                std::string escape_str = std::any_cast<std::string>(config.at("escape_char"));
                if (!escape_str.empty()) {
                    options_.escape_char = escape_str[0];
                } else {
                    throw std::invalid_argument("Escape char string cannot be empty");
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast escape_char (should be char or string): {}", e.what());
            throw;
        }
    }
    if (config.find("has_header") != config.end()) {
        try {
            options_.has_header = std::any_cast<bool>(config.at("has_header"));
            logger->debug("Has header: {}", options_.has_header);
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast has_header: {}", e.what());
            throw;
        }
    }
    if (config.find("encoding") != config.end()) {
        try {
            options_.encoding = std::any_cast<std::string>(config.at("encoding"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast encoding: {}", e.what());
            throw;
        }
    }
    if (config.find("skip_lines") != config.end()) {
        try {
            // Try size_t first, then int
            try {
                options_.skip_lines = std::any_cast<size_t>(config.at("skip_lines"));
            } catch (const std::bad_any_cast&) {
                // Try as int and convert to size_t
                int skip_lines_int = std::any_cast<int>(config.at("skip_lines"));
                if (skip_lines_int < 0) {
                    throw std::invalid_argument("skip_lines cannot be negative");
                }
                options_.skip_lines = static_cast<size_t>(skip_lines_int);
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast skip_lines (should be size_t or int): {}", e.what());
            throw;
        }
    }
    if (config.find("max_lines") != config.end()) {
        try {
            // Try size_t first, then int
            try {
                options_.max_lines = std::any_cast<size_t>(config.at("max_lines"));
            } catch (const std::bad_any_cast&) {
                // Try as int and convert to size_t
                int max_lines_int = std::any_cast<int>(config.at("max_lines"));
                if (max_lines_int < 0) {
                    throw std::invalid_argument("max_lines cannot be negative");
                }
                options_.max_lines = static_cast<size_t>(max_lines_int);
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast max_lines (should be size_t or int): {}", e.what());
            throw;
        }
    }
    if (config.find("max_records") != config.end()) {
        try {
            // Try size_t first since that's what the tests use
            // Use ExtractorBase options_ for max_records
            ExtractorBase::options_.max_records = std::any_cast<size_t>(config.at("max_records"));
            logger->debug("Max records set to (size_t): {}", ExtractorBase::options_.max_records);
        } catch (const std::bad_any_cast& e) {
            try {
                // On this platform, size_t might be unsigned long, so try that
                ExtractorBase::options_.max_records = std::any_cast<unsigned long>(config.at("max_records"));
                logger->debug("Max records set to (unsigned long): {}", ExtractorBase::options_.max_records);
            } catch (const std::bad_any_cast& e2) {
                try {
                    ExtractorBase::options_.max_records = std::any_cast<unsigned int>(config.at("max_records"));
                    logger->debug("Max records set to (unsigned int): {}", ExtractorBase::options_.max_records);
                } catch (const std::bad_any_cast& e3) {
                    logger->error("Failed to cast max_records (tried size_t, unsigned long, unsigned int): original error: {}", e.what());
                    throw;
                }
            }
        }
    }
    
    // Handle column selection
    if (config.find("columns") != config.end()) {
        try {
            ExtractorBase::options_.columns = std::any_cast<std::vector<std::string>>(config.at("columns"));
            logger->debug("Column selection enabled: {} columns", ExtractorBase::options_.columns.size());
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast columns parameter: {}", e.what());
            throw;
        }
    }

    // Initialize parser
    parser_ = CsvFieldParser(options_);

    // Open file
    open_file(filepath_);

    // Skip initial lines if requested
    for (size_t i = 0; i < options_.skip_lines && file_stream_.good(); ++i) {
        std::string line;
        std::getline(file_stream_, line);
        current_line_++;
    }

    // Read header if present
    if (options_.has_header) {
        read_header();
    } else if (config.find("column_names") != config.end()) {
        try {
            column_names_ = std::any_cast<std::vector<std::string>>(config.at("column_names"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast column_names: {}", e.what());
            throw;
        }
    }

    // Get column types if provided
    if (config.find("column_types") != config.end()) {
        try {
            // Try vector first (indexed by position)
            try {
                column_types_ = std::any_cast<std::vector<std::string>>(config.at("column_types"));
            } catch (const std::bad_any_cast&) {
                // Try map (column name to type)
                auto column_type_map = std::any_cast<std::unordered_map<std::string, std::string>>(config.at("column_types"));
                
                // Convert map to vector based on column names
                column_types_.resize(column_names_.size());
                for (size_t i = 0; i < column_names_.size(); ++i) {
                    auto it = column_type_map.find(column_names_[i]);
                    if (it != column_type_map.end()) {
                        column_types_[i] = it->second;
                    } else {
                        column_types_[i] = "string"; // Default type
                    }
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast column_types (should be vector<string> or map<string,string>): {}", e.what());
            throw;
        }
    } else {
        // Infer types from data
        infer_column_types();
    }

    // ExtractorBase handles timing automatically
    logger->info("CSV extractor initialized for file: {}", filepath_);
}

core::RecordBatch CsvExtractor::extract_batch(size_t batch_size,
                                            core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    // Pre-allocate line buffer for better performance
    std::string line;
    line.reserve(4096); // Common line length

    auto logger = common::Logger::get("omop-csv-extractor");
    size_t count = 0;
    
    logger->debug("Extract batch: batch_size={}, max_records={}, extracted_count={}", 
                  batch_size, ExtractorBase::options_.max_records, stats_.successful_records);

    while (file_stream_.good() && count < batch_size && has_more_data_) {
        // Check max_records before processing
        if (ExtractorBase::options_.max_records > 0 && stats_.successful_records >= ExtractorBase::options_.max_records) {
            logger->debug("Reached max_records limit: {} >= {}", stats_.successful_records, ExtractorBase::options_.max_records);
            has_more_data_ = false;
            break;
        }

        line.clear();
        
        // First try reading a single line - most CSV records are single-line
        if (!std::getline(file_stream_, line)) {
            // End of file
            has_more_data_ = false;
            break;
        }
        current_line_++;
        
        // max_lines limits total lines including header
        if (options_.max_lines > 0 && current_line_ > options_.max_lines) {
            has_more_data_ = false;
            break;
        }
        
        // Check if this line has unmatched quotes (multi-line record)
        bool has_unmatched_quotes = false;
        bool in_quotes = false;
        bool escape_next = false;
        
        for (char c : line) {
            if (escape_next) {
                escape_next = false;
                continue;
            }
            if (c == options_.escape_char) {
                escape_next = true;
                continue;
            }
            if (c == options_.quote_char) {
                in_quotes = !in_quotes;
            }
        }
        has_unmatched_quotes = in_quotes;
        
        // If we have unmatched quotes, we need to read additional lines
        if (has_unmatched_quotes) {
            std::string continuation_line;
            while (file_stream_.good() && in_quotes) {
                if (!std::getline(file_stream_, continuation_line)) {
                    break;
                }
                current_line_++;
                line += '\n' + continuation_line;
                
                // Check quotes in continuation line
                escape_next = false;
                for (char c : continuation_line) {
                    if (escape_next) {
                        escape_next = false;
                        continue;
                    }
                    if (c == options_.escape_char) {
                        escape_next = true;
                        continue;
                    }
                    if (c == options_.quote_char) {
                        in_quotes = !in_quotes;
                    }
                }
            }
        }

        if (line.empty() && options_.skip_empty_lines) {
            continue;
        }


        try {
            auto fields = parser_.parse_line(line);

            if (!fields.empty()) {
                auto record = create_record(fields);
                batch.addRecord(std::move(record));
                count++;
                stats_.successful_records++;
                stats_.total_records++;
            }

        } catch (const std::exception& e) {
            stats_.failed_records++;
            stats_.total_records++;
            context.increment_errors();

            logger->warn("Error parsing line {}: {}", current_line_, e.what());

            // Continue processing on error by default
        }
    }

    // Check if we've reached end of file
    if (!file_stream_.good() ||
        (options_.max_lines > 0 && current_line_ >= options_.max_lines) ||
        file_stream_.peek() == EOF) {
        has_more_data_ = false;
    }

    return batch;
}

void CsvExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-extractor");

    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    auto end_time = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - stats_.start_time).count();

    logger->info("CSV extraction completed: {} records extracted, {} errors in {} seconds",
                stats_.successful_records, stats_.failed_records, duration);
}

bool CsvExtractor::has_more_data() const {
    return has_more_data_ && file_stream_.good();
}

std::unordered_map<std::string, std::any> CsvExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["total_lines"] = current_line_;
    stats["extracted_count"] = stats_.successful_records;
    stats["records_extracted"] = stats_.successful_records;  // Add expected key
    stats["total_records"] = stats_.total_records;  // Expected by tests
    stats["successful_records"] = stats_.successful_records;  // Expected by tests
    stats["failed_records"] = stats_.failed_records;  // Expected by tests
    stats["error_count"] = stats_.failed_records;
    stats["column_count"] = column_names_.size();

    auto current_time = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - stats_.start_time).count();
    stats["extraction_time_seconds"] = duration;
    stats["extraction_time"] = duration;  // Add expected key

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(stats_.successful_records) / duration;
    }

    return stats;
}

void CsvExtractor::open_file(const std::string& filepath) {
    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            "CSV file not found: '" + filepath + "'", "csv");
    }

    file_stream_.open(filepath, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            "Failed to open CSV file: '" + filepath + "'", "csv");
    }

    // Count total lines if needed
    if (options_.max_lines == 0) {
        std::ifstream count_stream(filepath);
        total_lines_ = std::count(std::istreambuf_iterator<char>(count_stream),
                                 std::istreambuf_iterator<char>(), '\n');
        count_stream.close();
    }
}

void CsvExtractor::read_header() {
    std::string header_line;
    std::getline(file_stream_, header_line);
    current_line_++;

    if (header_line.empty()) {
        throw common::ExtractionException("Empty header line in CSV file", "csv");
    }

    column_names_ = parser_.parse_line(header_line);

    // Trim column names
    for (auto& name : column_names_) {
        name.erase(0, name.find_first_not_of(" \t\r\n"));
        name.erase(name.find_last_not_of(" \t\r\n") + 1);
    }
}

void CsvExtractor::infer_column_types(size_t sample_size) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Inferring column types from {} sample rows", sample_size);

    // Save current position
    auto current_pos = file_stream_.tellg();

    // Read sample rows
    std::vector<std::vector<std::string>> sample_rows;
    size_t rows_read = 0;

    while (file_stream_.good() && rows_read < sample_size) {
        std::string line;
        std::getline(file_stream_, line);

        if (line.empty() && options_.skip_empty_lines) {
            continue;
        }

        auto fields = parser_.parse_line(line);
        if (!fields.empty()) {
            sample_rows.push_back(fields);
            rows_read++;
        }
    }

    // Infer types for each column
    column_types_.resize(column_names_.size(), "string");

    for (size_t col = 0; col < column_names_.size(); ++col) {
        bool all_integer = true;
        bool all_double = true;
        bool all_boolean = true;
        bool all_date = true;
        bool all_datetime = true;

        for (const auto& row : sample_rows) {
            if (col >= row.size() || row[col].empty() ||
                row[col] == options_.null_string) {
                continue;
            }

            const std::string& value = row[col];

            // Check integer
            if (all_integer) {
                try {
                    size_t pos;
                    std::stoll(value, &pos);
                    // Must consume entire string to be considered integer
                    if (pos != value.length()) {
                        all_integer = false;
                    }
                } catch (...) {
                    all_integer = false;
                }
            }

            // Check double (if not integer, or if integer parsing didn't consume entire string)
            if (all_double) {
                try {
                    size_t pos;
                    std::stod(value, &pos);
                    // Must consume entire string to be considered double
                    if (pos != value.length()) {
                        all_double = false;
                    }
                } catch (...) {
                    all_double = false;
                }
            }

            // Check boolean
            if (all_boolean) {
                std::string upper = value;
                std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);
                if (upper != options_.true_string && upper != options_.false_string &&
                    upper != "1" && upper != "0" && upper != "TRUE" && upper != "FALSE" &&
                    upper != "YES" && upper != "NO" && upper != "Y" && upper != "N") {
                    all_boolean = false;
                }
            }

            // Skip date/datetime inference by default - CSV extractor keeps dates as strings
            // This can be enabled later if needed via configuration
            all_datetime = false;
            all_date = false;
        }

        // Assign inferred type
        if (all_integer) {
            column_types_[col] = "integer";
        } else if (all_double) {
            column_types_[col] = "double";
        } else if (all_boolean) {
            column_types_[col] = "boolean";
        } else if (all_datetime) {
            column_types_[col] = "datetime";
        } else if (all_date) {
            column_types_[col] = "date";
        } else {
            column_types_[col] = "string";
        }
    }

    // Restore file position
    file_stream_.clear();
    file_stream_.seekg(current_pos);

    logger->info("Column types inferred successfully");
}

core::Record CsvExtractor::create_record(const std::vector<std::string>& fields) {
    core::Record record;

    // Bounds check
    if (fields.size() > column_names_.size()) {
        auto logger = common::Logger::get("omop-csv-extractor");
        logger->warn("Row has {} fields but only {} columns defined. Extra fields will be ignored.",
                    fields.size(), column_names_.size());
    }

    for (size_t i = 0; i < fields.size() && i < column_names_.size(); ++i) {
        const std::string& field_name = column_names_[i];
        const std::string& field_value = fields[i];

        // Get type hint
        std::string type_hint;
        if (i < column_types_.size()) {
            type_hint = column_types_[i];
        }

        // Convert and set field
        try {
            auto converted_value = parser_.convert_field(field_value, type_hint);
            record.setField(field_name, converted_value);
        } catch (const std::exception& e) {
            // Log conversion error but continue
            auto logger = common::Logger::get("omop-csv-extractor");
            logger->debug("Failed to convert field '{}' value '{}': {}",
                        field_name, field_value, e.what());

            // Store as string
            record.setField(field_name, field_value);
        }
    }

    // Add metadata
    // Set metadata using the Record metadata structure
    core::Record::RecordMetadata metadata;
    metadata.custom["source_file"] = filepath_;
    metadata.custom["line_number"] = current_line_;
    metadata.extraction_time = std::chrono::system_clock::now();
    record.setMetadata(metadata);

    // Apply column filtering if specified
    if (!ExtractorBase::options_.columns.empty()) {
        return ExtractorBase::selectColumns(record);
    }

    return record;
}

bool CsvExtractor::read_complete_record(std::string& record) {
    record.clear();
    std::string current_line;
    bool in_quotes = false;
    bool first_line = true;

    while (file_stream_.good()) {
        if (!std::getline(file_stream_, current_line)) {
            // End of file
            break;
        }

        // Always increment line number for each line read
        current_line_++;

        // Check if this line contains quotes
        bool has_quotes = false;
        for (char c : current_line) {
            if (c == options_.quote_char) {
                has_quotes = true;
                break;
            }
        }

        // If we're not in quotes and this line has no quotes, it's a simple single-line record
        if (!in_quotes && !has_quotes) {
            record = current_line;
            return !record.empty() || !options_.skip_empty_lines;
        }

        // Add to record (with newline if this is a continuation)
        if (!first_line) {
            record += '\n';
        }
        record += current_line;
        first_line = false;

        // Count quotes to determine if we're inside a quoted field
        bool escape_next = false;
        for (char c : current_line) {
            if (escape_next) {
                escape_next = false;
                continue;
            }

            if (c == options_.escape_char) {
                escape_next = true;
                continue;
            }

            if (c == options_.quote_char) {
                in_quotes = !in_quotes;
            }
        }

        // If we're not in quotes anymore, the record is complete
        if (!in_quotes) {
            return true;
        }
    }

    // Return whatever we have, even if incomplete
    return !record.empty();
}

bool CsvExtractor::is_compressed_file(const std::string& filepath) {
    static const std::vector<std::string> compressed_exts = {".gz", ".bz2", ".zip", ".xz"};
    for (const auto& ext : compressed_exts) {
        if (filepath.size() >= ext.size() &&
            filepath.compare(filepath.size() - ext.size(), ext.size(), ext) == 0) {
            return true;
        }
    }
    return false;
}

// ExtractorBase pure virtual method implementations

bool CsvExtractor::connect() {
    if (is_connected_) {
        return true;
    }
    
    if (filepath_.empty()) {
        handleError("No file path specified for CSV extractor");
        return false;
    }
    
    try {
        open_file(filepath_);
        is_connected_ = true;
        return true;
    } catch (const std::exception& e) {
        handleError(std::string("Failed to connect to CSV file: ") + e.what());
        return false;
    }
}

void CsvExtractor::disconnect() {
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
    is_connected_ = false;
    has_more_data_ = true;
    current_line_ = 0;
}

std::vector<core::Record> CsvExtractor::extractBatchImpl(size_t batch_size) {
    std::vector<core::Record> records;
    records.reserve(batch_size);
    
    if (!file_stream_.is_open() || file_stream_.eof()) {
        has_more_data_ = false;
        return records;
    }
    
    std::string line;
    size_t records_read = 0;
    
    while (records_read < batch_size && std::getline(file_stream_, line)) {
        current_line_++;
        
        // Skip empty lines if configured
        if (options_.skip_empty_lines && line.empty()) {
            continue;
        }
        
        try {
            auto fields = parser_.parse_line(line);
            if (fields.size() != column_names_.size()) {
                handleError(std::string("Field count mismatch at line ") + 
                           std::to_string(current_line_));
                stats_.failed_records++;
                continue;
            }
            
            // Convert fields to record using the existing method
            core::Record record = create_record(fields);
            records.push_back(std::move(record));
            records_read++;
            stats_.successful_records++;
            
        } catch (const std::exception& e) {
            handleError(std::string("Error parsing line ") + 
                       std::to_string(current_line_) + ": " + e.what());
            stats_.failed_records++;
        }
        
        // Update progress
        updateProgress(stats_.successful_records + stats_.failed_records);
        
        // Check if we've hit max records limit
        if (ExtractorBase::options_.max_records > 0 && 
            (stats_.successful_records + stats_.failed_records) >= ExtractorBase::options_.max_records) {
            has_more_data_ = false;
            break;
        }
    }
    
    if (file_stream_.eof()) {
        has_more_data_ = false;
    }
    
    return records;
}

core::Record CsvExtractor::convertToRecord(const std::any& source_data) {
    try {
        // Expect source_data to be a vector<string> of CSV fields
        auto fields = std::any_cast<std::vector<std::string>>(source_data);
        return create_record(fields);
    } catch (const std::bad_any_cast& e) {
        throw common::ExtractionException(
            std::string("Invalid source data type for CSV conversion: ") + e.what(),
            "csv_extractor");
    }
}

SourceSchema CsvExtractor::getSchema() const {
    SourceSchema schema;
    schema.source_name = filepath_;
    schema.source_type = "csv";
    
    // Build column information
    for (size_t i = 0; i < column_names_.size(); ++i) {
        SourceSchema::Column col;
        col.name = column_names_[i];
        col.data_type = (i < column_types_.size()) ? column_types_[i] : "string";
        col.nullable = true;
        col.description = "CSV column";
        schema.columns.push_back(col);
    }
    
    schema.metadata["delimiter"] = std::string(1, options_.delimiter);
    schema.metadata["quote_char"] = std::string(1, options_.quote_char);
    schema.metadata["has_header"] = options_.has_header ? "true" : "false";
    schema.metadata["encoding"] = options_.encoding;
    
    return schema;
}

omop::common::ValidationResult CsvExtractor::validateSource() {
    omop::common::ValidationResult result;
    
    // Check if file exists
    if (!std::filesystem::exists(filepath_)) {
        result.add_error("", "CSV file does not exist: " + filepath_, "csv_file_not_found");
        return result;
    }
    
    // Check if file is readable
    std::ifstream test_file(filepath_);
    if (!test_file.is_open()) {
        result.add_error("", "Cannot open CSV file: " + filepath_, "csv_file_not_readable");
        return result;
    }
    test_file.close();
    
    // Check if file is empty
    if (std::filesystem::file_size(filepath_) == 0) {
        // Note: ValidationResult doesn't have add_warning, so we'll just log this
        auto logger = common::Logger::get("omop-csv-extractor");
        logger->warn("CSV file is empty: {}", filepath_);
        return result;
    }
    
    // Basic parsing validation
    try {
        std::ifstream file(filepath_);
        std::string first_line;
        if (std::getline(file, first_line)) {
            parser_.parse_line(first_line);
        }
    } catch (const std::exception& e) {
        result.add_error("", "Failed to parse CSV header: " + std::string(e.what()), "csv_parse_error");
        return result;
    }
    
    return result;
}

// MultiFileCsvExtractor implementation

void MultiFileCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-multi-csv-extractor");

    // Get file paths - try both "files" and "filepaths"
    if (config.find("files") != config.end()) {
        try {
            file_paths_ = std::any_cast<std::vector<std::string>>(config.at("files"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'files' parameter to vector<string>: {}", e.what());
            throw;
        }
    } else if (config.find("filepaths") != config.end()) {
        try {
            file_paths_ = std::any_cast<std::vector<std::string>>(config.at("filepaths"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'filepaths' parameter to vector<string>: {}", e.what());
            throw;
        }
    } else {
        throw common::ConfigurationException("Multi-file CSV extractor requires 'files' or 'filepaths' parameter");
    }

    if (file_paths_.empty()) {
        throw common::ConfigurationException("No files provided for multi-file CSV extractor");
    }

    // Get skip headers option
    if (config.find("skip_headers_after_first") != config.end()) {
        try {
            skip_headers_after_first_ = std::any_cast<bool>(config.at("skip_headers_after_first"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'skip_headers_after_first' parameter to bool: {}", e.what());
            throw;
        }
    }

    logger->info("Initializing multi-file CSV extractor with {} files", file_paths_.size());

    // Create modified config for first file
    auto file_config = config;
    file_config["filepath"] = file_paths_[0];

    // Initialize with first file
    CsvExtractor::initialize(file_config, context);
}

core::RecordBatch MultiFileCsvExtractor::extract_batch(size_t batch_size,
                                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-multi-csv-extractor");
    
    // Try to extract from current file first
    auto batch = CsvExtractor::extract_batch(batch_size, context);
    
    // If we got a full batch, return it
    if (batch.size() >= batch_size) {
        return batch;
    }
    
    // If current file is exhausted, try to move to next file
    while (!CsvExtractor::has_more_data() && next_file()) {
        logger->debug("Switched to next file, extracting remaining records");
        
        // Extract from the new file
        auto next_batch = CsvExtractor::extract_batch(batch_size - batch.size(), context);
        
        // Add records from next batch to current batch
        auto next_records = next_batch.getRecords();
        for (auto& record : next_records) {
            batch.addRecord(std::move(record));
        }
        
        // If we got a full batch, return it
        if (batch.size() >= batch_size) {
            break;
        }
    }
    
    return batch;
}

bool MultiFileCsvExtractor::has_more_data() const {
    return CsvExtractor::has_more_data() ||
           (current_file_index_ + 1 < file_paths_.size());
}

bool MultiFileCsvExtractor::next_file() {
    auto logger = common::Logger::get("omop-multi-csv-extractor");

    current_file_index_++;
    if (current_file_index_ >= file_paths_.size()) {
        return false;
    }

    // Close current file
    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    // Open next file
    filepath_ = file_paths_[current_file_index_];
    logger->info("Switching to file {}/{}: {}",
                current_file_index_ + 1, file_paths_.size(), filepath_);

    open_file(filepath_);

    // Skip header if needed
    if (options_.has_header && skip_headers_after_first_) {
        std::string header_line;
        std::getline(file_stream_, header_line);
        current_line_++;
    }

    has_more_data_ = true;
    return true;
}

// CsvDirectoryExtractor implementation

void CsvDirectoryExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-directory-extractor");

    // Get directory path
    if (config.find("directory") == config.end()) {
        throw common::ConfigurationException("CSV directory extractor requires 'directory' parameter");
    }
    
    try {
        directory_path_ = std::any_cast<std::string>(config.at("directory"));
    } catch (const std::bad_any_cast& e) {
        logger->error("Failed to cast 'directory' parameter to string: {}", e.what());
        throw;
    }

    // Get pattern
    std::string pattern = ".*\\.csv$";  // Default pattern
    if (config.find("pattern") != config.end()) {
        try {
            // Try std::string first
            pattern = std::any_cast<std::string>(config.at("pattern"));
        } catch (const std::bad_any_cast&) {
            try {
                // Try const char* if std::string fails
                pattern = std::any_cast<const char*>(config.at("pattern"));
            } catch (const std::bad_any_cast& e) {
                logger->error("Failed to cast 'pattern' parameter to string or const char*: {}", e.what());
                logger->error("Type of 'pattern' parameter: {}", config.at("pattern").type().name());
                throw;
            }
        }
    }
    file_pattern_ = std::regex(pattern, std::regex_constants::icase);

    // Get recursive option
    if (config.find("recursive") != config.end()) {
        try {
            recursive_search_ = std::any_cast<bool>(config.at("recursive"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'recursive' parameter to bool: {}", e.what());
            throw;
        }
    }

    // Find CSV files
    file_paths_ = find_csv_files(directory_path_, pattern, recursive_search_);

    if (file_paths_.empty()) {
        throw common::ExtractionException(
            "No CSV files found in directory '" + directory_path_ + "' matching pattern '" + pattern + "'",
            "csv_directory");
    }

    logger->info("Found {} CSV files in directory '{}'",
                file_paths_.size(), directory_path_);

    // Initialize with multi-file config
    auto multi_config = config;
    multi_config["files"] = file_paths_;

    MultiFileCsvExtractor::initialize(multi_config, context);
}

std::vector<std::string> CsvDirectoryExtractor::find_csv_files(const std::string& directory,
                                                             const std::string& pattern,
                                                             bool recursive) {
    std::vector<std::string> files;
    std::regex file_regex(pattern, std::regex_constants::icase);

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        throw common::ExtractionException(
            "Error accessing directory '" + directory + "': " + e.what(),
            "csv_directory");
    }

    // Sort files for consistent ordering
    std::sort(files.begin(), files.end());

    return files;
}

// CsvExtractorFactory implementation

std::unique_ptr<core::IExtractor> CsvExtractorFactory::create(const std::string& type) {
    if (type == "csv") {
        return std::make_unique<CsvExtractor>();
    } else if (type == "multi_csv") {
        return std::make_unique<MultiFileCsvExtractor>();
    } else if (type == "csv_directory") {
        return std::make_unique<CsvDirectoryExtractor>();
    } else if (type == "compressed_csv") {
        return std::make_unique<CompressedCsvExtractor>();
    } else {
        throw common::ConfigurationException(
            "Unknown CSV extractor type: '" + type + "'");
    }
}

void CsvExtractorFactory::register_extractors() {
    // Register CSV extractors with the main factory
    ExtractorFactoryRegistry::register_type("csv",
        []() { return std::make_unique<CsvExtractor>(); });

    ExtractorFactoryRegistry::register_type("multi_csv",
        []() { return std::make_unique<MultiFileCsvExtractor>(); });

    ExtractorFactoryRegistry::register_type("csv_directory",
        []() { return std::make_unique<CsvDirectoryExtractor>(); });

    ExtractorFactoryRegistry::register_type("compressed_csv",
        []() { return std::make_unique<CompressedCsvExtractor>(); });

    auto logger = common::Logger::get("omop-csv-extractor-factory");
    logger->info("Registered CSV extractor types: csv, multi_csv, csv_directory, compressed_csv");
}

// CompressedCsvExtractor implementation

void CompressedCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                       core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-compressed-csv-extractor");
    logger->info("Initializing compressed CSV extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ConfigurationException("Compressed CSV extractor requires 'filepath' parameter");
    }
    
    std::string compressed_filepath = std::any_cast<std::string>(config.at("filepath"));
    
    // Check for unsupported compression formats first
    std::string lower_filepath = compressed_filepath;
    std::transform(lower_filepath.begin(), lower_filepath.end(), lower_filepath.begin(), ::tolower);
    
    if (lower_filepath.ends_with(".rar") || lower_filepath.ends_with(".7z") || 
        lower_filepath.ends_with(".tar") || lower_filepath.ends_with(".lz4")) {
        throw common::ExtractionException(
            "Unsupported compression format: " + compressed_filepath,
            "compressed_csv");
    }
    
    // Detect compression format
    compression_format_ = detect_compression(compressed_filepath);
    if (compression_format_ == CompressionFormat::None) {
        // Allow fallback to regular CSV if not compressed
        logger->info("File does not appear to be compressed, treating as regular CSV: {}", compressed_filepath);
        CsvExtractor::initialize(config, context);
        return;
    }
    
    logger->info("Detected compression format: {}", format_to_string(compression_format_));
    
    // Decompress file
    temp_file_path_ = decompress_file(compressed_filepath, compression_format_);
    
    // Create modified config for decompressed file
    auto decompressed_config = config;
    decompressed_config["filepath"] = temp_file_path_;
    
    // Initialize base CSV extractor with decompressed file
    CsvExtractor::initialize(decompressed_config, context);
    
    logger->info("Compressed CSV extractor initialized successfully");
}

void CompressedCsvExtractor::finalize(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-compressed-csv-extractor");
    
    // Call base finalize
    CsvExtractor::finalize(context);
    
    // Clean up temporary file
    if (cleanup_temp_file_ && !temp_file_path_.empty()) {
        try {
            if (std::filesystem::exists(temp_file_path_)) {
                std::filesystem::remove(temp_file_path_);
                logger->debug("Cleaned up temporary file: {}", temp_file_path_);
            }
        } catch (const std::exception& e) {
            logger->warn("Failed to clean up temporary file {}: {}", temp_file_path_, e.what());
        }
        temp_file_path_.clear();
    }
}

std::unordered_map<std::string, std::any> CompressedCsvExtractor::get_statistics() const {
    auto stats = CsvExtractor::get_statistics();
    
    // Add compression-specific statistics
    stats["compression_format"] = format_to_string(compression_format_);
    if (!temp_file_path_.empty()) {
        stats["temp_file_path"] = temp_file_path_;
        
        try {
            auto temp_size = std::filesystem::file_size(temp_file_path_);
            stats["decompressed_size_bytes"] = static_cast<size_t>(temp_size);
            stats["original_size"] = static_cast<size_t>(temp_size);  // Expected by tests
        } catch (const std::exception&) {
            // Ignore errors getting file size
        }
    }
    
    // Add compressed size if original file exists
    if (!filepath_.empty()) {
        try {
            auto compressed_size = std::filesystem::file_size(filepath_);
            stats["compressed_size"] = static_cast<size_t>(compressed_size);  // Expected by tests
        } catch (const std::exception&) {
            // Ignore errors getting file size
        }
    }
    
    return stats;
}

CompressedCsvExtractor::CompressionFormat CompressedCsvExtractor::detect_compression(const std::string& filepath) {
    // Check file extension first
    std::string lower_filepath = filepath;
    std::transform(lower_filepath.begin(), lower_filepath.end(), lower_filepath.begin(), ::tolower);
    
    if (lower_filepath.ends_with(".gz") || lower_filepath.ends_with(".gzip")) {
        return CompressionFormat::Gzip;
    } else if (lower_filepath.ends_with(".bz2") || lower_filepath.ends_with(".bzip2")) {
        return CompressionFormat::Bzip2;
    } else if (lower_filepath.ends_with(".xz")) {
        return CompressionFormat::Xz;
    } else if (lower_filepath.ends_with(".zip")) {
        return CompressionFormat::Zip;
    }
    
    // Check magic bytes if extension is not conclusive
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return CompressionFormat::None;
    }
    
    std::array<unsigned char, 4> header{};
    file.read(reinterpret_cast<char*>(header.data()), header.size());
    
    if (!file.good()) {
        return CompressionFormat::None;
    }
    
    // Check magic bytes
    if (header[0] == 0x1f && header[1] == 0x8b) {
        return CompressionFormat::Gzip;
    } else if (header[0] == 'B' && header[1] == 'Z') {
        return CompressionFormat::Bzip2;
    } else if (header[0] == 0xfd && header[1] == '7' && header[2] == 'z' && header[3] == 'X') {
        return CompressionFormat::Xz;
    } else if (header[0] == 'P' && header[1] == 'K') {
        return CompressionFormat::Zip;
    }
    
    return CompressionFormat::None;
}

std::string CompressedCsvExtractor::decompress_file(const std::string& filepath,
                                                   CompressionFormat format) {
    auto logger = common::Logger::get("omop-compressed-csv-extractor");
    
    // Create temporary file path
    std::filesystem::path temp_dir = std::filesystem::temp_directory_path();
    std::string temp_filename = "omop_csv_" + std::to_string(std::hash<std::string>{}(filepath)) + ".csv";
    std::string temp_path = (temp_dir / temp_filename).string();
    
    logger->info("Decompressing {} to {}", filepath, temp_path);
    
#if defined(__unix__) || defined(__APPLE__)
    // Use command-line tools for decompression on Unix-like systems
    std::string command;
    switch (format) {
        case CompressionFormat::Gzip:
            command = "gunzip -c '" + filepath + "' > '" + temp_path + "'";
            break;
        case CompressionFormat::Bzip2:
            command = "bunzip2 -c '" + filepath + "' > '" + temp_path + "'";
            break;
        case CompressionFormat::Xz:
            command = "xz -dc '" + filepath + "' > '" + temp_path + "'";
            break;
        case CompressionFormat::Zip:
            // For ZIP files, we need to extract the first CSV file
            command = "unzip -p '" + filepath + "' '*.csv' | head -1 > '" + temp_path + "'";
            break;
        default:
            throw common::ExtractionException(
                "Unsupported compression format: " + format_to_string(format),
                "compressed_csv");
    }
    
    logger->debug("Executing decompression command: {}", command);
    
    int result = std::system(command.c_str());
    if (result != 0) {
        throw common::ExtractionException(
            "Failed to decompress file: " + filepath + " (exit code: " + std::to_string(result) + ")",
            "compressed_csv");
    }
    
#else
    // For Windows or other platforms, implement basic decompression manually
    switch (format) {
        case CompressionFormat::Gzip:
            decompress_gzip(filepath, temp_path);
            break;
        case CompressionFormat::Zip:
            decompress_zip(filepath, temp_path);
            break;
        case CompressionFormat::Bzip2:
            decompress_bzip2(filepath, temp_path);
            break;
        case CompressionFormat::Xz:
            decompress_xz(filepath, temp_path);
            break;
        default:
            throw common::ExtractionException(
                "Compression format not supported on this platform: " + format_to_string(format),
                "compressed_csv");
    }
#endif
    
    // Verify decompressed file exists and is not empty
    if (!std::filesystem::exists(temp_path)) {
        throw common::ExtractionException(
            "Decompression failed - temporary file not created", "compressed_csv");
    }
    
    if (std::filesystem::file_size(temp_path) == 0) {
        std::filesystem::remove(temp_path);
        throw common::ExtractionException(
            "Decompression failed - empty output file", "compressed_csv");
    }
    
    logger->info("Successfully decompressed {} bytes", std::filesystem::file_size(temp_path));
    
    return temp_path;
}

std::string CompressedCsvExtractor::format_to_string(CompressionFormat format) const {
    switch (format) {
        case CompressionFormat::None: return "none";
        case CompressionFormat::Gzip: return "gzip";
        case CompressionFormat::Zip: return "zip";
        case CompressionFormat::Bzip2: return "bzip2";
        case CompressionFormat::Xz: return "xz";
        default: return "unknown";
    }
}

CompressedCsvExtractor::CompressionFormat CompressedCsvExtractor::string_to_format(const std::string& format_str) {
    std::string lower_format = format_str;
    std::transform(lower_format.begin(), lower_format.end(), lower_format.begin(), ::tolower);
    
    if (lower_format == "gzip" || lower_format == "gz") {
        return CompressionFormat::Gzip;
    } else if (lower_format == "zip") {
        return CompressionFormat::Zip;
    } else if (lower_format == "bzip2" || lower_format == "bz2") {
        return CompressionFormat::Bzip2;
    } else if (lower_format == "xz") {
        return CompressionFormat::Xz;
    } else {
        return CompressionFormat::None;
    }
}

// Decompression implementations using native libraries
void CompressedCsvExtractor::decompress_gzip(const std::string& src, const std::string& dst) {
    constexpr size_t CHUNK_SIZE = 16384;
    
    gzFile gz_file = gzopen(src.c_str(), "rb");
    if (!gz_file) {
        throw common::ExtractionException(
            "Failed to open gzip file: " + src, "compressed_csv");
    }
    
    std::ofstream out_file(dst, std::ios::binary);
    if (!out_file) {
        gzclose(gz_file);
        throw common::ExtractionException(
            "Failed to create output file: " + dst, "compressed_csv");
    }
    
    std::vector<char> buffer(CHUNK_SIZE);
    int bytes_read = 0;
    
    while ((bytes_read = gzread(gz_file, buffer.data(), CHUNK_SIZE)) > 0) {
        out_file.write(buffer.data(), bytes_read);
        if (out_file.fail()) {
            gzclose(gz_file);
            throw common::ExtractionException(
                "Failed to write decompressed data to: " + dst, "compressed_csv");
        }
    }
    
    if (bytes_read < 0) {
        int error_num = 0;
        const char* error_msg = gzerror(gz_file, &error_num);
        gzclose(gz_file);
        throw common::ExtractionException(
            "gzip decompression error: " + std::string(error_msg), "compressed_csv");
    }
    
    gzclose(gz_file);
}

void CompressedCsvExtractor::decompress_zip(const std::string& src, const std::string& dst) {
#ifdef HAVE_LIBARCHIVE
    struct archive* archive = archive_read_new();
    struct archive_entry* entry = nullptr;
    
    if (!archive) {
        throw common::ExtractionException(
            "Failed to create archive reader for: " + src, "compressed_csv");
    }
    
    archive_read_support_filter_all(archive);
    archive_read_support_format_zip(archive);
    
    int result = archive_read_open_filename(archive, src.c_str(), 10240);
    if (result != ARCHIVE_OK) {
        archive_read_free(archive);
        throw common::ExtractionException(
            "Failed to open zip file: " + src + " - " + archive_error_string(archive),
            "compressed_csv");
    }
    
    std::ofstream out_file;
    bool found_csv = false;
    
    while (archive_read_next_header(archive, &entry) == ARCHIVE_OK) {
        const char* entry_name = archive_entry_pathname(entry);
        std::string filename(entry_name);
        
        // Look for CSV files (case-insensitive)
        std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);
        if (filename.ends_with(".csv") || filename.ends_with(".txt")) {
            found_csv = true;
            out_file.open(dst, std::ios::binary);
            if (!out_file) {
                archive_read_free(archive);
                throw common::ExtractionException(
                    "Failed to create output file: " + dst, "compressed_csv");
            }
            
            constexpr size_t BUFFER_SIZE = 8192;
            std::vector<char> buffer(BUFFER_SIZE);
            la_ssize_t bytes_read = 0;
            
            while ((bytes_read = archive_read_data(archive, buffer.data(), BUFFER_SIZE)) > 0) {
                out_file.write(buffer.data(), bytes_read);
                if (out_file.fail()) {
                    out_file.close();
                    archive_read_free(archive);
                    throw common::ExtractionException(
                        "Failed to write decompressed data to: " + dst, "compressed_csv");
                }
            }
            
            if (bytes_read < 0) {
                out_file.close();
                archive_read_free(archive);
                throw common::ExtractionException(
                    "Error reading from zip archive: " + archive_error_string(archive),
                    "compressed_csv");
            }
            
            out_file.close();
            break; // Only extract the first CSV file found
        } else {
            archive_read_data_skip(archive);
        }
    }
    
    archive_read_free(archive);
    
    if (!found_csv) {
        throw common::ExtractionException(
            "No CSV files found in zip archive: " + src, "compressed_csv");
    }
#else
    throw common::ExtractionException(
        "ZIP decompression not available - libarchive not found during build",
        "compressed_csv");
#endif
}

void CompressedCsvExtractor::decompress_bzip2(const std::string& src, const std::string& dst) {
#ifdef HAVE_BZLIB
    FILE* bz_file = fopen(src.c_str(), "rb");
    if (!bz_file) {
        throw common::ExtractionException(
            "Failed to open bzip2 file: " + src, "compressed_csv");
    }
    
    int error = BZ_OK;
    BZFILE* bz_stream = BZ2_bzReadOpen(&error, bz_file, 0, 0, nullptr, 0);
    if (error != BZ_OK) {
        fclose(bz_file);
        throw common::ExtractionException(
            "Failed to initialize bzip2 decompression for: " + src, "compressed_csv");
    }
    
    std::ofstream out_file(dst, std::ios::binary);
    if (!out_file) {
        BZ2_bzReadClose(&error, bz_stream);
        fclose(bz_file);
        throw common::ExtractionException(
            "Failed to create output file: " + dst, "compressed_csv");
    }
    
    constexpr size_t BUFFER_SIZE = 8192;
    std::vector<char> buffer(BUFFER_SIZE);
    int bytes_read = 0;
    
    while ((bytes_read = BZ2_bzRead(&error, bz_stream, buffer.data(), BUFFER_SIZE)) > 0 && error == BZ_OK) {
        out_file.write(buffer.data(), bytes_read);
        if (out_file.fail()) {
            BZ2_bzReadClose(&error, bz_stream);
            fclose(bz_file);
            throw common::ExtractionException(
                "Failed to write decompressed data to: " + dst, "compressed_csv");
        }
    }
    
    if (error != BZ_OK && error != BZ_STREAM_END) {
        BZ2_bzReadClose(&error, bz_stream);
        fclose(bz_file);
        throw common::ExtractionException(
            "bzip2 decompression error for: " + src, "compressed_csv");
    }
    
    BZ2_bzReadClose(&error, bz_stream);
    fclose(bz_file);
#else
    throw common::ExtractionException(
        "bzip2 decompression not available - libbz2 not found during build", "compressed_csv");
#endif
}

void CompressedCsvExtractor::decompress_xz(const std::string& src, const std::string& dst) {
#ifdef HAVE_LZMA
    FILE* xz_file = fopen(src.c_str(), "rb");
    if (!xz_file) {
        throw common::ExtractionException(
            "Failed to open xz file: " + src, "compressed_csv");
    }
    
    std::ofstream out_file(dst, std::ios::binary);
    if (!out_file) {
        fclose(xz_file);
        throw common::ExtractionException(
            "Failed to create output file: " + dst, "compressed_csv");
    }
    
    lzma_stream strm = LZMA_STREAM_INIT;
    lzma_ret ret = lzma_stream_decoder(&strm, UINT64_MAX, LZMA_CONCATENATED);
    
    if (ret != LZMA_OK) {
        fclose(xz_file);
        throw common::ExtractionException(
            "Failed to initialize xz decoder for: " + src, "compressed_csv");
    }
    
    constexpr size_t BUFFER_SIZE = 8192;
    std::vector<uint8_t> in_buffer(BUFFER_SIZE);
    std::vector<uint8_t> out_buffer(BUFFER_SIZE);
    
    strm.avail_in = 0;
    strm.next_out = out_buffer.data();
    strm.avail_out = BUFFER_SIZE;
    
    lzma_action action = LZMA_RUN;
    
    while (true) {
        // Read more input if needed
        if (strm.avail_in == 0 && !feof(xz_file)) {
            strm.next_in = in_buffer.data();
            strm.avail_in = fread(in_buffer.data(), 1, BUFFER_SIZE, xz_file);
            
            if (ferror(xz_file)) {
                lzma_end(&strm);
                fclose(xz_file);
                throw common::ExtractionException(
                    "Error reading from xz file: " + src, "compressed_csv");
            }
            
            if (feof(xz_file)) {
                action = LZMA_FINISH;
            }
        }
        
        ret = lzma_code(&strm, action);
        
        // Write output if buffer is full or we're done
        if (strm.avail_out == 0 || ret == LZMA_STREAM_END) {
            size_t write_size = BUFFER_SIZE - strm.avail_out;
            out_file.write(reinterpret_cast<const char*>(out_buffer.data()), write_size);
            
            if (out_file.fail()) {
                lzma_end(&strm);
                fclose(xz_file);
                throw common::ExtractionException(
                    "Failed to write decompressed data to: " + dst, "compressed_csv");
            }
            
            strm.next_out = out_buffer.data();
            strm.avail_out = BUFFER_SIZE;
        }
        
        if (ret == LZMA_STREAM_END) {
            break;
        }
        
        if (ret != LZMA_OK) {
            lzma_end(&strm);
            fclose(xz_file);
            throw common::ExtractionException(
                "xz decompression error for: " + src, "compressed_csv");
        }
    }
    
    lzma_end(&strm);
    fclose(xz_file);
#else
    throw common::ExtractionException(
        "xz decompression not available - liblzma not found during build", "compressed_csv");
#endif
}

} // namespace omop::extract