/**
 * @file json_extractor.cpp
 * @brief Implementation of JSON data extractors
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "json_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "extractor_factory.h"
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <stack>

namespace omop::extract {

// Helper functions

json::const_iterator JsonExtractor::navigate_to_data(const json& root, const std::string& path) {
    std::vector<std::string> path_parts;
    std::stringstream ss(path);
    std::string part;

    while (std::getline(ss, part, '.')) {
        path_parts.push_back(part);
    }

    const json* current = &root;

    for (const auto& p : path_parts) {
        if (current->contains(p)) {
            current = &(*current)[p];
        } else {
            throw common::ExtractionException(
                "Path '" + path + "' not found in JSON structure", "json");
        }
    }

    if (!current->is_array()) {
        throw common::ExtractionException(
            "Path '" + path + "' does not point to an array", "json");
    }

    return current->begin();
}

std::optional<std::chrono::system_clock::time_point> JsonExtractor::parse_date(const std::string& date_str) {
    for (const auto& format : options_.date_formats) {
        std::tm tm = {};
        std::stringstream ss(date_str);
        ss >> std::get_time(&tm, format.c_str());

        if (!ss.fail()) {
            return std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }
    }

    return std::nullopt;
}

void JsonExtractor::finalize(core::ProcessingContext& context) {
    // Delegate to JsonLinesExtractor if using JSON Lines format
    if (jsonl_extractor_) {
        jsonl_extractor_->finalize(context);
        return;
    }

    auto logger = common::Logger::get("omop-json-extractor");

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("JSON extraction completed: {} records extracted in {} seconds",
                extracted_count_, duration);
}

std::unordered_map<std::string, std::any> JsonExtractor::get_statistics() const {
    // Delegate to JsonLinesExtractor if using JSON Lines format
    if (jsonl_extractor_) {
        auto stats = jsonl_extractor_->get_statistics();
        // Add aliases for test compatibility
        if (stats.find("extracted_count") != stats.end()) {
            stats["successful_records"] = stats["extracted_count"];
        }
        if (stats.find("error_count") != stats.end()) {
            stats["failed_records"] = stats["error_count"];
        } else {
            stats["failed_records"] = size_t{0};
            stats["error_count"] = size_t{0};
        }
        return stats;
    }

    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["total_records"] = static_cast<size_t>(total_records_);
    stats["extracted_count"] = static_cast<size_t>(extracted_count_);
    stats["successful_records"] = static_cast<size_t>(extracted_count_);  // Alias for test compatibility
    stats["failed_records"] = size_t{0};             // JSON extractor doesn't track failures separately
    stats["error_count"] = size_t{0};                // Add error_count for consistency
    stats["data_loaded"] = data_loaded_;

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }

    return stats;
}

// JsonLinesExtractor implementation

void JsonLinesExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                   [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-jsonl-extractor");
    logger->info("Initializing JSON Lines extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException("JSONL extractor requires 'filepath' parameter", "jsonl");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }
    if (config.find("array_delimiter") != config.end()) {
        options_.array_delimiter = std::any_cast<std::string>(config.at("array_delimiter"));
    }
    if (config.find("parse_dates") != config.end()) {
        options_.parse_dates = std::any_cast<bool>(config.at("parse_dates"));
    }
    if (config.find("date_formats") != config.end()) {
        options_.date_formats = std::any_cast<std::vector<std::string>>(config.at("date_formats"));
    }
    if (config.find("ignore_null") != config.end()) {
        options_.ignore_null = std::any_cast<bool>(config.at("ignore_null"));
    }
    if (config.find("max_depth") != config.end()) {
        try {
            options_.max_depth = std::any_cast<size_t>(config.at("max_depth"));
        } catch (const std::bad_any_cast& e) {
            throw common::ExtractionException(
                "Failed to cast max_depth parameter to size_t in JsonLinesExtractor: " + std::string(e.what()), "jsonl");
        }
    }

    start_time_ = std::chrono::steady_clock::now();

    // Open file
    open_file(filepath_);
}

void JsonLinesExtractor::open_file(const std::string& filepath) {
    auto logger = common::Logger::get("omop-jsonl-extractor");

    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            "JSONL file not found: '" + filepath + "'", "jsonl");
    }

    file_stream_.open(filepath, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            "Failed to open JSONL file: '" + filepath + "'", "jsonl");
    }

    logger->info("Opened JSONL file: {}", filepath);
}

std::optional<json> JsonLinesExtractor::read_next_line() {
    if (!file_stream_.good()) {
        return std::nullopt;
    }

    std::string line;
    std::getline(file_stream_, line);
    current_line_++;

    if (line.empty()) {
        return std::nullopt;
    }

    try {
        return json::parse(line);
    } catch (const json::parse_error& e) {
        auto logger = common::Logger::get("omop-jsonl-extractor");
        logger->warn("Failed to parse JSON at line {}: {}", current_line_, e.what());
        error_count_++;
        return std::nullopt;
    }
}

core::Record JsonLinesExtractor::json_to_record(const json& obj) {
    core::Record record;

    // Use JsonExtractor's flattening logic
    JsonExtractor temp_extractor;
    temp_extractor.options_ = options_;

    return temp_extractor.flatten_json_object(obj);
}

core::RecordBatch JsonLinesExtractor::extract_batch(size_t batch_size,
                                                   core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    size_t count = 0;

    while (has_more_ && count < batch_size) {
        auto json_obj = read_next_line();

        if (!json_obj) {
            if (!file_stream_.good()) {
                has_more_ = false;
            }
            continue;
        }

        try {
            auto record = json_to_record(*json_obj);

            // Add metadata
            // Set metadata using the Record metadata structure
            core::Record::RecordMetadata metadata;
            metadata.custom["source_file"] = filepath_;
            metadata.custom["line_number"] = current_line_;
            metadata.extraction_time = std::chrono::system_clock::now();
            record.setMetadata(metadata);

            batch.addRecord(std::move(record));
            count++;
            extracted_count_++;

        } catch (const std::exception& e) {
            context.log("warning",
                "Failed to extract record at line " + std::to_string(current_line_) + ": " + std::string(e.what()));
            context.increment_errors();
            error_count_++;

            // Continue processing on error by default
        }
    }

    return batch;
}

bool JsonLinesExtractor::has_more_data() const {
    return has_more_ && file_stream_.good();
}

void JsonLinesExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-jsonl-extractor");

    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("JSONL extraction completed: {} records extracted, {} errors in {} seconds",
                extracted_count_, error_count_, duration);
}

std::unordered_map<std::string, std::any> JsonLinesExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["lines_processed"] = static_cast<size_t>(current_line_);
    stats["extracted_count"] = static_cast<size_t>(extracted_count_);
    stats["error_count"] = static_cast<size_t>(error_count_);

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }

    return stats;
}

// StreamingJsonExtractor::JsonHandler implementation

bool StreamingJsonExtractor::JsonHandler::null() {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = nullptr;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::boolean(bool val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::number_integer(number_integer_t val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::number_unsigned(number_unsigned_t val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::number_float(number_float_t val, [[maybe_unused]] const string_t& s) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::string(string_t& val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::binary([[maybe_unused]] binary_t& val) {
    // Binary data not typically used in JSON extraction
    return true;
}

bool StreamingJsonExtractor::JsonHandler::start_object([[maybe_unused]] std::size_t elements) {
    if (in_data_array_ ||
        (!options_.root_path.empty() &&
         !options_.root_path.empty())) {
        object_stack_.push(json::object());
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::end_object() {
    if (!object_stack_.empty()) {
        json obj = object_stack_.top();
        object_stack_.pop();

        if (object_stack_.empty() && in_data_array_) {
            // This is a complete record object
            JsonExtractor temp_extractor;
            temp_extractor.options_ = options_;

            core::Record record = temp_extractor.flatten_json_object(obj);
            record_queue_.push(std::move(record));
        } else if (!object_stack_.empty() && !current_key_.empty()) {
            // Nested object
            object_stack_.top()[current_key_] = obj;
        }
    }

    if (!path_stack_.empty()) {
        path_stack_.pop();
    }

    return true;
}

bool StreamingJsonExtractor::JsonHandler::start_array([[maybe_unused]] std::size_t elements) {
    if (!options_.root_path.empty()) {
        std::string current_path;
        // Convert stack to vector for iteration
        std::vector<std::string> path_vector;
        std::stack<std::string> temp_stack = path_stack_;
        while (!temp_stack.empty()) {
            path_vector.push_back(temp_stack.top());
            temp_stack.pop();
        }
        std::reverse(path_vector.begin(), path_vector.end());

        for (const auto& p : path_vector) {
            if (!current_path.empty()) current_path += ".";
            current_path += p;
        }

        if (current_path == options_.root_path) {
            in_data_array_ = true;
        }
    } else if (path_stack_.empty()) {
        // Root level array
        in_data_array_ = true;
    }

    return true;
}

bool StreamingJsonExtractor::JsonHandler::end_array() {
    if (in_data_array_ && path_stack_.size() <= 1) {
        in_data_array_ = false;
    }

    if (!path_stack_.empty()) {
        path_stack_.pop();
    }

    return true;
}

bool StreamingJsonExtractor::JsonHandler::key(string_t& val) {
    current_key_ = val;
    path_stack_.push(val);
    return true;
}

bool StreamingJsonExtractor::JsonHandler::parse_error(std::size_t position,
                                                      const std::string& last_token,
                                                      const nlohmann::detail::exception& ex) {
    auto logger = common::Logger::get("omop-streaming-json");
    logger->error("JSON parse error at position {}: {} (last token: '{}')",
                 position, ex.what(), last_token);
    return false;
}

// StreamingJsonExtractor implementation

void StreamingJsonExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                       [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-streaming-json");
    logger->info("Initializing streaming JSON extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException(
            "Streaming JSON extractor requires 'filepath' parameter", "streaming_json");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("root_path") != config.end()) {
        options_.root_path = std::any_cast<std::string>(config.at("root_path"));
    }
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Open file
    if (!std::filesystem::exists(filepath_)) {
        throw common::ExtractionException(
            "JSON file not found: '" + filepath_ + "'", "streaming_json");
    }

    file_stream_.open(filepath_, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            "Failed to open JSON file: '" + filepath_ + "'", "streaming_json");
    }

    // Start async parsing thread
    parser_thread_ = std::make_unique<std::thread>(&StreamingJsonExtractor::parse_file_async, this);

    logger->info("Started streaming extraction for file: {}", filepath_);
}

void StreamingJsonExtractor::parse_file_async() {
    auto logger = common::Logger::get("omop-streaming-json");

    try {
        JsonHandler handler(record_queue_, options_);
        json::sax_parse(file_stream_, &handler);

        parsing_complete_ = true;
        queue_cv_.notify_all();

    } catch (const std::exception& e) {
        logger->error("Error during JSON parsing: {}", e.what());
        error_message_ = e.what();
        has_error_ = true;
        parsing_complete_ = true;
        queue_cv_.notify_all();
    }
}

core::RecordBatch StreamingJsonExtractor::extract_batch(size_t batch_size,
                                                       [[maybe_unused]] core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    size_t count = 0;

    while (count < batch_size) {
        std::unique_lock<std::mutex> lock(queue_mutex_);

        // Wait for records or completion
        queue_cv_.wait(lock, [this] {
            return !record_queue_.empty() || parsing_complete_;
        });

        // Check for errors
        if (has_error_) {
            throw common::ExtractionException(
                "Streaming parse error: " + error_message_, "streaming_json");
        }

        // Extract records from queue
        while (!record_queue_.empty() && count < batch_size) {
            batch.addRecord(std::move(record_queue_.front()));
            record_queue_.pop();
            count++;
            extracted_count_++;
        }

        // Check if parsing is complete and queue is empty
        if (parsing_complete_ && record_queue_.empty()) {
            break;
        }
    }

    return batch;
}

bool StreamingJsonExtractor::has_more_data() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return !parsing_complete_ || !record_queue_.empty();
}

void StreamingJsonExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-streaming-json");

    // Wait for parser thread to complete
    if (parser_thread_ && parser_thread_->joinable()) {
        parser_thread_->join();
    }

    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("Streaming JSON extraction completed: {} records extracted in {} seconds",
                extracted_count_, duration);
}

std::unordered_map<std::string, std::any> StreamingJsonExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["extracted_count"] = static_cast<size_t>(extracted_count_);
    stats["parsing_complete"] = parsing_complete_.load();
    stats["has_error"] = has_error_.load();

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }

    return stats;
}

// JsonExtractorFactory implementation

std::unique_ptr<core::IExtractor> JsonExtractorFactory::create(const std::string& type) {
    if (type == "json") {
        return std::make_unique<JsonExtractor>();
    } else if (type == "jsonl" || type == "json_lines") {
        return std::make_unique<JsonLinesExtractor>();
    } else if (type == "streaming_json") {
        return std::make_unique<StreamingJsonExtractor>();
    } else {
        throw common::ConfigurationException(
            "Unknown JSON extractor type: '" + type + "'");
    }
}

void JsonExtractorFactory::register_extractors() {
    // Register JSON extractors with the main factory
    ExtractorFactoryRegistry::register_type("json",
        []() { return std::make_unique<JsonExtractor>(); });

    ExtractorFactoryRegistry::register_type("jsonl",
        []() { return std::make_unique<JsonLinesExtractor>("jsonl"); });

    ExtractorFactoryRegistry::register_type("json_lines",
        []() { return std::make_unique<JsonLinesExtractor>("json_lines"); });

    ExtractorFactoryRegistry::register_type("streaming_json",
        []() { return std::make_unique<StreamingJsonExtractor>(); });

    auto logger = common::Logger::get("omop-json-extractor-factory");
    logger->info("Registered JSON extractor types: json, jsonl, json_lines, streaming_json");
}

} // namespace omop::extract