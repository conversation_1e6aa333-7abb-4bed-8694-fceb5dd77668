# Source directory CMake configuration

# Add library subdirectory
add_subdirectory(lib)

# Add application subdirectory if enabled
if(BUILD_APPLICATIONS)
    add_subdirectory(app)
endif()

# Set common include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/lib
    ${CMAKE_CURRENT_BINARY_DIR}
)

# Export include directories for parent scope
set(OMOP_INCLUDE_DIRS
    ${CMAKE_CURRENT_SOURCE_DIR}/lib
    ${CMAKE_CURRENT_BINARY_DIR}
    PARENT_SCOPE
)

# Create convenience targets for building different components
add_custom_target(libraries
    DEPENDS 
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        # omop_service  # Temporarily disabled due to gRPC dependency issues
    COMMENT "Build all OMOP ETL libraries"
)

if(BUILD_APPLICATIONS)
    add_custom_target(applications
        DEPENDS 
            omop_etl_cli
            omop_etl_api
        COMMENT "Build all OMOP ETL applications"
    )
endif()

# Create a comprehensive build target
add_custom_target(all-components
    DEPENDS libraries
    COMMENT "Build all OMOP ETL components"
)

if(BUILD_APPLICATIONS)
    add_dependencies(all-components applications)
endif()