# CLI application - Standardized CMakeLists.txt

# Validate executable naming
omop_validate_target_name("omop_etl_cli" "omop_")

# Create CLI executable
add_executable(omop_etl_cli
    cli_application.cpp
)

# Define dependencies
set(CLI_DEPENDENCIES
    omop_common
    omop_core
    omop_cdm
    omop_extract
    omop_transform
    omop_load
    # omop_service  # TODO: Re-enable when gRPC issues are resolved
)

# Configure executable using standardized function
omop_configure_executable(omop_etl_cli
    DEPENDENCIES ${CLI_DEPENDENCIES}
    CONFIG_FILES ${CMAKE_SOURCE_DIR}/config/etl/cli_config.yaml
)

# Set component-specific include directories
target_include_directories(omop_etl_cli
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib
)

# Add compile definitions
target_compile_definitions(omop_etl_cli 
    PRIVATE
        OMOP_CLI_VERSION="${PROJECT_VERSION}"
        OMOP_BUILD_TYPE="${CMAKE_BUILD_TYPE}"
)

# Platform-specific configurations
if(WIN32)
    # Windows-specific settings
    target_compile_definitions(omop_etl_cli PRIVATE OMOP_PLATFORM_WINDOWS)
    set_target_properties(omop_etl_cli PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
elseif(APPLE)
    # macOS-specific settings
    target_compile_definitions(omop_etl_cli PRIVATE OMOP_PLATFORM_MACOS)
else()
    # Linux-specific settings
    target_compile_definitions(omop_etl_cli PRIVATE OMOP_PLATFORM_LINUX)
endif()

# Add debugging information in debug builds
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(omop_etl_cli PRIVATE OMOP_DEBUG_BUILD)
endif()

# Export component information
omop_print_component_summary("CLI Application" "omop_etl_cli")

# Add alias for consistent usage
add_executable(omop::cli ALIAS omop_etl_cli)