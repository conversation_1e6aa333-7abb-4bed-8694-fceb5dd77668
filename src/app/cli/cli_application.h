/**
 * @file cli_application.h
 * @brief Command-line interface for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides a comprehensive command-line interface for the OMOP ETL pipeline,
 * including argument parsing, command execution, and user interaction utilities.
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <optional>
#include <iostream>
#include <iomanip>
#include <thread>
#include <chrono>
#include "core/interfaces.h"
#include "common/configuration.h"

namespace omop::cli {

/**
 * @brief Command line argument
 */
struct Argument {
    std::string name;
    std::string description;
    bool required{false};
    std::string default_value;
    std::vector<std::string> allowed_values;
};

/**
 * @brief Command line option
 */
struct Option {
    std::string short_name;
    std::string long_name;
    std::string description;
    bool has_value{true};
    std::string default_value;
    std::vector<std::string> allowed_values;
    std::function<bool(const std::string&)> validator;
};

/**
 * @brief Command line command
 */
class Command {
public:
    using Handler = std::function<int(const std::unordered_map<std::string, std::string>&)>;

    /**
     * @brief Constructor
     * @param name Command name
     * @param description Command description
     */
    Command(const std::string& name, const std::string& description)
        : name_(name), description_(description) {}

    /**
     * @brief Add argument
     * @param arg Argument definition
     * @return Command& Reference for chaining
     */
    Command& add_argument(const Argument& arg) {
        arguments_.push_back(arg);
        return *this;
    }

    /**
     * @brief Add option
     * @param opt Option definition
     * @return Command& Reference for chaining
     */
    Command& add_option(const Option& opt) {
        options_.push_back(opt);
        if (!opt.short_name.empty()) {
            short_options_[opt.short_name] = options_.size() - 1;
        }
        if (!opt.long_name.empty()) {
            long_options_[opt.long_name] = options_.size() - 1;
        }
        return *this;
    }

    /**
     * @brief Set handler
     * @param handler Command handler function
     * @return Command& Reference for chaining
     */
    Command& set_handler(Handler handler) {
        handler_ = handler;
        return *this;
    }

    /**
     * @brief Execute command
     * @param args Parsed arguments
     * @return int Exit code
     */
    int execute(const std::unordered_map<std::string, std::string>& args) {
        if (handler_) {
            return handler_(args);
        }
        return 0;
    }

    /**
     * @brief Print help
     */
    void print_help() const;

    // Getters
    [[nodiscard]] const std::string& name() const { return name_; }
    [[nodiscard]] const std::string& description() const { return description_; }
    [[nodiscard]] const std::vector<Argument>& arguments() const { return arguments_; }
    [[nodiscard]] const std::vector<Option>& options() const { return options_; }

    /**
     * @brief Find option by name
     * @param name Short or long option name
     * @return std::optional<size_t> Option index
     */
    [[nodiscard]] std::optional<size_t> find_option(const std::string& name) const {
        if (name.length() == 1) {
            auto it = short_options_.find(name);
            if (it != short_options_.end()) return it->second;
        } else {
            auto it = long_options_.find(name);
            if (it != long_options_.end()) return it->second;
        }
        return std::nullopt;
    }

private:
    std::string name_;
    std::string description_;
    std::vector<Argument> arguments_;
    std::vector<Option> options_;
    std::unordered_map<std::string, size_t> short_options_;
    std::unordered_map<std::string, size_t> long_options_;
    Handler handler_;
};

/**
 * @brief Command line interface application
 *
 * Main CLI application class that handles command parsing and execution.
 */
class CliApplication {
public:
    /**
     * @brief Constructor
     * @param name Application name
     * @param version Application version
     * @param description Application description
     */
    CliApplication(const std::string& name,
                   const std::string& version,
                   const std::string& description)
        : name_(name), version_(version), description_(description) {}

    /**
     * @brief Add command
     * @param command Command to add
     * @return CliApplication& Reference for chaining
     */
    CliApplication& add_command(const Command& command) {
        commands_[command.name()] = command;
        return *this;
    }

    /**
     * @brief Add global option
     * @param opt Option definition
     * @return CliApplication& Reference for chaining
     */
    CliApplication& add_global_option(const Option& opt) {
        global_options_.push_back(opt);
        return *this;
    }

    /**
     * @brief Run application
     * @param argc Argument count
     * @param argv Argument values
     * @return int Exit code
     */
    int run(int argc, char* argv[]);

    /**
     * @brief Print help
     */
    void print_help() const;

    /**
     * @brief Print version
     */
    void print_version() const {
        std::cout << name_ << " version " << version_ << std::endl;
    }

private:
    /**
     * @brief Parse command line arguments
     * @param argc Argument count
     * @param argv Argument values
     * @return Parsed arguments and command
     */
    struct ParseResult {
        std::string command;
        std::unordered_map<std::string, std::string> args;
        std::vector<std::string> errors;
    };

    ParseResult parse_args(int argc, char* argv[]) const;

    std::string name_;
    std::string version_;
    std::string description_;
    std::unordered_map<std::string, Command> commands_;
    std::vector<Option> global_options_;
};

/**
 * @brief OMOP ETL CLI Application
 *
 * Specific CLI implementation for the OMOP ETL pipeline.
 */
class OmopEtlCli {
public:
    /**
     * @brief Constructor
     */
    OmopEtlCli();

    /**
     * @brief Run the CLI application
     * @param argc Argument count
     * @param argv Argument values
     * @return int Exit code
     */
    int run(int argc, char* argv[]);

private:
    // Command handlers
    int handle_extract(const std::unordered_map<std::string, std::string>& args);
    int handle_transform(const std::unordered_map<std::string, std::string>& args);
    int handle_load(const std::unordered_map<std::string, std::string>& args);
    int handle_run(const std::unordered_map<std::string, std::string>& args);
    int handle_validate(const std::unordered_map<std::string, std::string>& args);
    int handle_list_tables(const std::unordered_map<std::string, std::string>& args);
    int handle_list_mappings(const std::unordered_map<std::string, std::string>& args);
    int handle_server(const std::unordered_map<std::string, std::string>& args);
    int handle_test_connection(const std::unordered_map<std::string, std::string>& args);
    int handle_create_schema(const std::unordered_map<std::string, std::string>& args);

    // Helper methods
    /**
     * @brief Load configuration
     * @param config_file Configuration file path
     * @return bool True if successful
     */
    bool load_configuration(const std::string& config_file);

    /**
     * @brief Build ETL pipeline
     * @param table_name OMOP table name
     * @param options Pipeline options
     * @return std::unique_ptr<core::ETLPipeline> Pipeline instance
     */
    std::unique_ptr<core::ETLPipeline> build_pipeline(
        const std::string& table_name,
        const std::unordered_map<std::string, std::string>& options);

    /**
     * @brief Print progress
     * @param job_info Job information
     */
    void print_progress(const core::JobInfo& job_info);

    /**
     * @brief Print table format
     * @param headers Table headers
     * @param rows Table rows
     */
    void print_table(const std::vector<std::string>& headers,
                    const std::vector<std::vector<std::string>>& rows);

    std::unique_ptr<CliApplication> app_;
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<common::Logger> logger_;
};

/**
 * @brief Progress bar for CLI output
 */
class ProgressBar {
public:
    /**
     * @brief Constructor
     * @param total Total items
     * @param width Bar width
     */
    ProgressBar(size_t total, size_t width = 50)
        : total_(total), width_(width) {}

    /**
     * @brief Update progress
     * @param current Current progress
     */
    void update(size_t current) {
        current_ = current;
        print();
    }

    /**
     * @brief Increment progress
     * @param amount Amount to increment
     */
    void increment(size_t amount = 1) {
        current_ += amount;
        print();
    }

    /**
     * @brief Set status message
     * @param status Status message
     */
    void set_status(const std::string& status) {
        status_ = status;
        print();
    }

    /**
     * @brief Complete progress
     */
    void complete() {
        current_ = total_;
        status_ = "Complete";
        print();
        std::cout << std::endl;
    }

private:
    void print() {
        double progress = total_ > 0 ? static_cast<double>(current_) / total_ : 0.0;
        size_t filled = static_cast<size_t>(progress * width_);

        std::cout << "\r[";
        for (size_t i = 0; i < width_; ++i) {
            if (i < filled) {
                std::cout << "=";
            } else if (i == filled) {
                std::cout << ">";
            } else {
                std::cout << " ";
            }
        }

        std::cout << "] " << std::fixed << std::setprecision(1)
                  << (progress * 100.0) << "% ";

        if (!status_.empty()) {
            std::cout << " " << status_;
        }

        std::cout << std::flush;
    }

    size_t total_;
    size_t current_{0};
    size_t width_;
    std::string status_;
};

/**
 * @brief Spinner for indeterminate progress
 */
class Spinner {
public:
    /**
     * @brief Constructor
     * @param message Message to display
     */
    explicit Spinner(const std::string& message = "")
        : message_(message) {}

    /**
     * @brief Start spinner
     */
    void start() {
        if (running_) return;
        running_ = true;
        thread_ = std::thread([this]() { spin(); });
    }

    /**
     * @brief Stop spinner
     */
    void stop() {
        if (!running_) return;
        running_ = false;
        if (thread_.joinable()) {
            thread_.join();
        }
        std::cout << "\r" << std::string(message_.length() + 10, ' ') << "\r" << std::flush;
    }

    /**
     * @brief Update message
     * @param message New message
     */
    void set_message(const std::string& message) {
        message_ = message;
    }

private:
    void spin() {
        const std::vector<std::string> frames = {"|", "/", "-", "\\"};
        size_t frame = 0;

        while (running_) {
            std::cout << "\r" << frames[frame] << " " << message_ << std::flush;
            frame = (frame + 1) % frames.size();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    std::string message_;
    std::atomic<bool> running_{false};
    std::thread thread_;
};

/**
 * @brief Interactive prompt
 */
class Prompt {
public:
    /**
     * @brief Get string input
     * @param prompt Prompt message
     * @param default_value Default value
     * @return std::string User input
     */
    static std::string get_string(const std::string& prompt,
                                 const std::string& default_value = "");

    /**
     * @brief Get password input (hidden)
     * @param prompt Prompt message
     * @return std::string Password
     */
    static std::string get_password(const std::string& prompt);

    /**
     * @brief Get yes/no confirmation
     * @param prompt Prompt message
     * @param default_yes Default to yes
     * @return bool True if yes
     */
    static bool get_confirmation(const std::string& prompt, bool default_yes = false);

    /**
     * @brief Get choice from list
     * @param prompt Prompt message
     * @param choices Available choices
     * @param default_index Default choice index
     * @return size_t Selected index
     */
    static size_t get_choice(const std::string& prompt,
                           const std::vector<std::string>& choices,
                           size_t default_index = 0);
};

} // namespace omop::cli