/**
 * @file microservice_main.cpp
 * @brief Main entry point for microservice-based OMOP ETL API
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "api/api_service.h"
#include "service/service_orchestrator.h"
#include "service/extract_service.h"
#include "service/transform_service.h"
#include "service/load_service.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <thread>
#include <signal.h>
#include <iostream>

namespace {
    std::atomic<bool> shutdown_requested{false};
    
    void signal_handler(int signal) {
        if (signal == SIGINT || signal == SIGTERM) {
            shutdown_requested = true;
        }
    }
}

int main(int argc, char* argv[]) {
    try {
        // Set up signal handlers
        signal(SIGINT, signal_handler);
        signal(SIGTERM, signal_handler);
        
        // Initialize logging
        auto logger = omop::common::Logger::get("omop-microservice-main");
        logger->info("Starting OMOP ETL Microservice Application");
        
        // Load configuration
        auto config = std::make_shared<omop::common::ConfigurationManager>();
        std::string config_file = (argc > 1) ? argv[1] : "config/etl/config.yaml";
        config->load_config(config_file);
        
        // Determine run mode from configuration
        std::string run_mode = config->get_value_or<std::string>("run_mode", "monolithic");
        
        if (run_mode == "microservices") {
            // Microservices mode - start individual services in separate threads
            logger->info("Starting in microservices mode");
            
            // Start Extract Service
            std::thread extract_thread([&config]() {
                auto logger = omop::common::Logger::get("extract-service-thread");
                logger->info("Starting Extract Service");
                
                auto extract_service = omop::service::ExtractServiceFactory::create(config);
                
                // In production, this would start a gRPC server
                // For now, keep service alive
                while (!shutdown_requested) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
            });
            
            // Start Transform Service
            std::thread transform_thread([&config]() {
                auto logger = omop::common::Logger::get("transform-service-thread");
                logger->info("Starting Transform Service");
                
                auto transform_service = omop::service::TransformServiceFactory::create(config);
                
                // In production, this would start a gRPC server
                while (!shutdown_requested) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
            });
            
            // Start Load Service
            std::thread load_thread([&config]() {
                auto logger = omop::common::Logger::get("load-service-thread");
                logger->info("Starting Load Service");
                
                auto load_service = omop::service::LoadServiceFactory::create(config);
                
                // In production, this would start a gRPC server
                while (!shutdown_requested) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
            });
            
            // Wait for all services to complete
            extract_thread.join();
            transform_thread.join();
            load_thread.join();
            
        } else {
            // Orchestrator mode - single process with internal services
            logger->info("Starting in orchestrator mode");
            
            // Create orchestrator configuration
            omop::service::OrchestratorConfig orchestrator_config;
            orchestrator_config.extract_service_endpoint = 
                config->get_value_or<std::string>("services.extract.endpoint", "localhost:50051");
            orchestrator_config.transform_service_endpoint = 
                config->get_value_or<std::string>("services.transform.endpoint", "localhost:50052");
            orchestrator_config.load_service_endpoint = 
                config->get_value_or<std::string>("services.load.endpoint", "localhost:50053");
            orchestrator_config.max_concurrent_jobs = 
                config->get_value_or<size_t>("orchestrator.max_concurrent_jobs", 10);
            orchestrator_config.batch_size = 
                config->get_value_or<size_t>("orchestrator.batch_size", 1000);
            
            // Create service orchestrator
            auto orchestrator = std::make_shared<omop::service::ServiceOrchestrator>(
                orchestrator_config, config);
            orchestrator->start();
            
            // Create API service
            auto api_service = std::make_unique<omop::api::ETLApiService>(orchestrator, config);
            api_service->register_routes();
            
            // Create API server configuration
            omop::api::ApiServerConfig server_config;
            server_config.host = config->get_value_or<std::string>("api.host", "0.0.0.0");
            server_config.port = config->get_value_or<int>("api.port", 8080);
            server_config.thread_pool_size = config->get_value_or<size_t>("api.threads", 4);
            
            // Create and start API server
            auto api_server = std::make_unique<omop::api::ApiServer>(
                server_config, orchestrator, config);
            api_server->start();
            
            logger->info("API server started on {}:{}", server_config.host, server_config.port);
            
            // Wait for shutdown signal
            while (!shutdown_requested) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            
            // Graceful shutdown
            logger->info("Shutting down services...");
            api_server->stop();
            orchestrator->stop();
        }
        
        logger->info("Application shutdown complete");
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
} 