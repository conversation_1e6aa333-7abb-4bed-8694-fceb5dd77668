# API application - Standardized CMakeLists.txt

# Validate target naming
omop_validate_target_name("omop_api" "omop_")
omop_validate_target_name("omop_api_server" "omop_")

# Define API library sources
set(API_SOURCES
    api_service.cpp
    # etl_service.cpp # Deprecated - replaced by microservice architecture
)

# Create API library
add_library(omop_api ${API_SOURCES})

# Define API library dependencies
set(API_LIB_DEPENDENCIES
    omop_core
    omop_common
    # omop_service  # TODO: Re-enable when gRPC issues are resolved
)

# Configure API library
omop_configure_library(omop_api
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS ${API_LIB_DEPENDENCIES}
    PRIVATE_DEPS
        spdlog::spdlog
        nlohmann_json::nlohmann_json
)

# Set API library include directories
target_include_directories(omop_api
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${HTTPLIB_INCLUDE_DIR}
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
)

# Add compile definitions for API library
target_compile_definitions(omop_api 
    PUBLIC
        OMOP_API_VERSION="${PROJECT_VERSION}"
        $<$<BOOL:${HAVE_GRPC}>:OMOP_HAS_GRPC>
    PRIVATE
        OMOP_API_INTERNAL_BUILD
)

# Platform-specific API configurations
if(WIN32)
    target_compile_definitions(omop_api PRIVATE OMOP_API_PLATFORM_WINDOWS)
elseif(APPLE)
    target_compile_definitions(omop_api PRIVATE OMOP_API_PLATFORM_MACOS)
else()
    target_compile_definitions(omop_api PRIVATE OMOP_API_PLATFORM_LINUX)
endif()

# Create API server executable
add_executable(omop_api_server
    microservice_main.cpp
)

# Define API server dependencies
set(API_SERVER_DEPENDENCIES
    omop_api
    # omop_service  # TODO: Re-enable when gRPC issues are resolved
    omop_core
    omop_common
    omop_extract
    omop_transform
    omop_load
    Threads::Threads
)

# Configure API server executable
omop_configure_executable(omop_api_server
    DEPENDENCIES ${API_SERVER_DEPENDENCIES}
    CONFIG_FILES ${CMAKE_SOURCE_DIR}/config/etl/microservices_config.yaml
)

# Add external dependencies as PRIVATE to avoid export issues
target_link_libraries(omop_api_server PRIVATE
    spdlog::spdlog
    nlohmann_json::nlohmann_json
)

# Set API server include directories
target_include_directories(omop_api_server
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib
        ${HTTPLIB_INCLUDE_DIR}
)

# Add compile definitions for API server
target_compile_definitions(omop_api_server 
    PRIVATE
        OMOP_API_SERVER_VERSION="${PROJECT_VERSION}"
        OMOP_BUILD_TYPE="${CMAKE_BUILD_TYPE}"
        $<$<BOOL:${HAVE_GRPC}>:OMOP_HAS_GRPC>
)

# Add debugging information in debug builds
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(omop_api_server PRIVATE OMOP_DEBUG_BUILD)
endif()

# gRPC-specific configurations
if(HAVE_GRPC)
    target_link_libraries(omop_api_server PRIVATE
        ${_GRPC_GRPCPP}
        ${_PROTOBUF_LIBPROTOBUF}
    )
    target_compile_definitions(omop_api_server PRIVATE OMOP_GRPC_ENABLED)
endif()

# Export component information
omop_print_component_summary("API Application" "omop_api;omop_api_server")

# Add aliases for consistent usage
add_library(omop::api ALIAS omop_api)
add_executable(omop::api_server ALIAS omop_api_server)