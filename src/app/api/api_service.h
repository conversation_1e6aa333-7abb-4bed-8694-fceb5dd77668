/**
 * @file api_service.h
 * @brief REST API service for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides a comprehensive REST API for the OMOP ETL pipeline,
 * including job management, configuration, and monitoring endpoints.
 */

#pragma once

#include "service/service_orchestrator.h"
#include "service/etl_service.h"
#include "common/configuration.h"
#include <string>
#include <memory>
#include <functional>
#include <unordered_map>
#include <nlohmann/json.hpp>

namespace omop::api {

using json = nlohmann::json;

/**
 * @brief HTTP request method
 */
enum class HttpMethod {
    GET,
    POST,
    PUT,
    DELETE,
    PATCH,
    HEAD,
    OPTIONS
};

/**
 * @brief HTTP request
 */
struct HttpRequest {
    HttpMethod method;
    std::string path;
    std::string query_string;
    std::unordered_map<std::string, std::string> headers;
    std::unordered_map<std::string, std::string> params;
    std::unordered_map<std::string, std::string> query_params;
    std::string body;
    json json_body;
};

/**
 * @brief HTTP response
 */
struct HttpResponse {
    int status_code{200};
    std::unordered_map<std::string, std::string> headers;
    std::string body;

    /**
     * @brief Set JSON response
     * @param data JSON data
     */
    void set_json(const json& data) {
        headers["Content-Type"] = "application/json";
        body = data.dump();
    }

    /**
     * @brief Set error response
     * @param status HTTP status code
     * @param message Error message
     * @param details Additional details
     */
    void set_error(int status, const std::string& message,
                  const json& details = json::object()) {
        status_code = status;
        json error_response = {
            {"error", message},
            {"status", status},
            {"timestamp", std::chrono::system_clock::now().time_since_epoch().count()}
        };
        if (!details.empty()) {
            error_response["details"] = details;
        }
        set_json(error_response);
    }
};

/**
 * @brief Route handler function type
 */
using RouteHandler = std::function<HttpResponse(const HttpRequest&)>;

/**
 * @brief Middleware function type
 */
using Middleware = std::function<bool(HttpRequest&, HttpResponse&)>;

/**
 * @brief ETL API endpoints
 *
 * This class implements the REST API for the OMOP ETL pipeline,
 * providing endpoints for job management, configuration, and monitoring.
 */
class ETLApiService {
public:
    /**
     * @brief Constructor
     * @param orchestrator Service orchestrator instance
     * @param config Configuration manager
     */
    ETLApiService(std::shared_ptr<service::ServiceOrchestrator> orchestrator,
                  std::shared_ptr<common::ConfigurationManager> config);

    /**
     * @brief Register API routes
     */
    void register_routes();

    /**
     * @brief Get route handler for path and method
     * @param method HTTP method
     * @param path Request path
     * @return RouteHandler Handler function
     */
    RouteHandler get_handler(HttpMethod method, const std::string& path) const;

    /**
     * @brief Add middleware
     * @param middleware Middleware function
     */
    void add_middleware(Middleware middleware);

    /**
     * @brief Process request through middleware chain
     * @param request HTTP request
     * @param response HTTP response
     * @return bool True if request should continue
     */
    bool process_middleware(HttpRequest& request, HttpResponse& response);

protected:
    // Job management endpoints
    HttpResponse create_job(const HttpRequest& request);
    HttpResponse get_job(const HttpRequest& request);
    HttpResponse list_jobs(const HttpRequest& request);
    HttpResponse cancel_job(const HttpRequest& request);
    HttpResponse pause_job(const HttpRequest& request);
    HttpResponse resume_job(const HttpRequest& request);
    HttpResponse get_job_logs(const HttpRequest& request);

    // Configuration endpoints
    HttpResponse get_config(const HttpRequest& request);
    HttpResponse update_config(const HttpRequest& request);
    HttpResponse validate_config(const HttpRequest& request);
    HttpResponse get_table_mappings(const HttpRequest& request);

    // Vocabulary endpoints
    HttpResponse get_vocabularies(const HttpRequest& request);
    HttpResponse search_concepts(const HttpRequest& request);
    HttpResponse get_concept(const HttpRequest& request);
    HttpResponse create_mapping(const HttpRequest& request);

    // System endpoints
    HttpResponse get_health(const HttpRequest& request);
    HttpResponse get_metrics(const HttpRequest& request);
    HttpResponse get_version(const HttpRequest& request);
    HttpResponse get_supported_tables(const HttpRequest& request);
    HttpResponse get_supported_extractors(const HttpRequest& request);
    HttpResponse get_supported_loaders(const HttpRequest& request);

    // OpenAPI/Swagger
    HttpResponse get_openapi_spec(const HttpRequest& request);

    // Helper methods
    /**
     * @brief Validate request body against schema
     * @param request HTTP request
     * @param schema JSON schema
     * @return std::optional<json> Validation errors if any
     */
    std::optional<json> validate_request_body(const HttpRequest& request,
                                            const json& schema);

    /**
     * @brief Parse job configuration from request
     * @param request HTTP request
     * @return Job configuration
     */
    struct JobConfig {
        std::string name;
        std::string source_table;
        std::string target_table;
        std::string extractor_type;
        std::string loader_type;
        json extractor_params;
        json loader_params;
        json pipeline_config;
    };

    JobConfig parse_job_config(const HttpRequest& request);



private:
    std::shared_ptr<service::ServiceOrchestrator> orchestrator_;
    std::shared_ptr<common::ConfigurationManager> config_;

    // Route registry
    struct Route {
        std::string path_pattern;
        RouteHandler handler;
        std::vector<std::string> path_params;
    };

    std::unordered_map<HttpMethod, std::vector<Route>> routes_;
    std::vector<Middleware> middleware_;

    // Job logs storage (in production, use proper logging system)
    std::unordered_map<std::string, std::vector<std::string>> job_logs_;
    mutable std::mutex logs_mutex_;

    /**
     * @brief Register a route
     * @param method HTTP method
     * @param path Path pattern (e.g., "/api/v1/jobs/{job_id}")
     * @param handler Route handler
     */
    void route(HttpMethod method, const std::string& path, RouteHandler handler);

    /**
     * @brief Match path against route pattern
     * @param path Request path
     * @param pattern Route pattern
     * @param params Extracted parameters
     * @return bool True if matches
     */
    bool match_route(const std::string& path, const std::string& pattern,
                    std::unordered_map<std::string, std::string>& params) const;
};

/**
 * @brief API server configuration
 */
struct ApiServerConfig {
    std::string host{"0.0.0.0"};
    int port{8080};
    size_t thread_pool_size{4};
    size_t max_request_size{10 * 1024 * 1024};  // 10MB
    std::chrono::seconds request_timeout{30};
    bool enable_cors{true};
    bool enable_compression{true};
    std::string ssl_cert_file;
    std::string ssl_key_file;
};

/**
 * @brief HTTP server interface
 *
 * Abstract interface for HTTP server implementations.
 */
class IHttpServer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IHttpServer() = default;

    /**
     * @brief Start the server
     * @param config Server configuration
     */
    virtual void start(const ApiServerConfig& config) = 0;

    /**
     * @brief Stop the server
     */
    virtual void stop() = 0;

    /**
     * @brief Check if server is running
     * @return bool True if running
     */
    virtual bool is_running() const = 0;

    /**
     * @brief Set request handler
     * @param handler Request handler function
     */
    virtual void set_handler(
        std::function<HttpResponse(const HttpRequest&)> handler) = 0;
};

/**
 * @brief API server
 *
 * Main API server class that combines the HTTP server with the ETL API service.
 */
class ApiServer {
public:
    /**
     * @brief Constructor
     * @param config Server configuration
     * @param orchestrator Service orchestrator
     * @param config_manager Configuration manager
     */
    ApiServer(const ApiServerConfig& config,
              std::shared_ptr<service::ServiceOrchestrator> orchestrator,
              std::shared_ptr<common::ConfigurationManager> config_manager);

    /**
     * @brief Start the server
     */
    void start();

    /**
     * @brief Stop the server
     */
    void stop();

    /**
     * @brief Check if server is running
     * @return bool True if running
     */
    [[nodiscard]] bool is_running() const;

    /**
     * @brief Get server URL
     * @return std::string Server URL
     */
    [[nodiscard]] std::string get_url() const;

    /**
     * @brief Add custom middleware
     * @param middleware Middleware function
     */
    void add_middleware(Middleware middleware);

private:
    ApiServerConfig config_;
    std::unique_ptr<IHttpServer> http_server_;
    std::unique_ptr<ETLApiService> api_service_;

    /**
     * @brief Main request handler
     * @param request HTTP request
     * @return HttpResponse HTTP response
     */
    HttpResponse handle_request(const HttpRequest& request);

    /**
     * @brief Apply CORS headers
     * @param request Request
     * @param response Response
     */
    void apply_cors_headers(const HttpRequest& request, HttpResponse& response);
};

/**
 * @brief OpenAPI specification generator
 *
 * Generates OpenAPI/Swagger documentation for the API.
 */
class OpenApiGenerator {
public:
    /**
     * @brief Generate OpenAPI specification
     * @param base_url API base URL
     * @return json OpenAPI specification
     */
    static json generate_spec(const std::string& base_url);

private:
    static json generate_info();
    static json generate_paths();
    static json generate_schemas();
    static json generate_security_schemes();
    static json generate_tags();
};

} // namespace omop::api