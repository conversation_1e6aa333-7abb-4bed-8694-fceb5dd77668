// This file is deprecated and replaced by the microservice architecture
// See src/lib/service/service_orchestrator.cpp for the new implementation

// The ETL functionality has been moved to:
// - src/lib/service/extract_service.cpp (Extract microservice)
// - src/lib/service/transform_service.cpp (Transform microservice)
// - src/lib/service/load_service.cpp (Load microservice)
// - src/lib/service/service_orchestrator.cpp (Orchestration)