#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <future>

#include "etl/etl_service.h"
#include "security/auth_manager.h"
#include "security/authorization.h"
#include "security/audit_logger.h"
#include "monitoring/metrics_collector.h"

namespace omop::api {

/**
 * @brief gRPC server configuration
 */
struct GrpcServerConfig {
    std::string host{"0.0.0.0"};
    uint16_t port{9090};
    size_t thread_pool_size{4};
    bool enable_ssl{false};
    std::string ssl_cert_path;
    std::string ssl_key_path;
    std::string ssl_ca_path;
    bool enable_compression{true};
    std::chrono::seconds keep_alive_timeout{30};
    std::chrono::seconds keep_alive_interval{30};
    size_t max_message_size{100 * 1024 * 1024}; // 100MB
    bool enable_reflection{true};
    bool enable_health_service{true};
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief gRPC service context
 */
struct GrpcContext {
    std::string client_address;
    std::string user_agent;
    std::unordered_map<std::string, std::string> metadata;
    std::string auth_token;
    std::string user_id;
    std::vector<std::string> user_roles;
    std::chrono::system_clock::time_point request_time;
};

/**
 * @brief gRPC server interface
 * 
 * This interface defines the contract for gRPC servers that provide
 * high-performance RPC endpoints for ETL operations.
 */
class IGrpcServer {
public:
    virtual ~IGrpcServer() = default;

    /**
     * @brief Initialize gRPC server
     * @param config Server configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const GrpcServerConfig& config) = 0;

    /**
     * @brief Start gRPC server
     * @return bool True if server started successfully
     */
    virtual bool start() = 0;

    /**
     * @brief Stop gRPC server
     * @return bool True if server stopped successfully
     */
    virtual bool stop() = 0;

    /**
     * @brief Check if server is running
     * @return bool True if server is running
     */
    virtual bool is_running() const = 0;

    /**
     * @brief Set ETL service
     * @param service ETL service instance
     */
    virtual void set_etl_service(std::shared_ptr<omop::etl::IETLService> service) = 0;

    /**
     * @brief Set authentication manager
     * @param auth_manager Authentication manager instance
     */
    virtual void set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager) = 0;

    /**
     * @brief Set authorization manager
     * @param authz_manager Authorization manager instance
     */
    virtual void set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) = 0;

    /**
     * @brief Set audit logger
     * @param audit_logger Audit logger instance
     */
    virtual void set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger) = 0;

    /**
     * @brief Set metrics collector
     * @param metrics_collector Metrics collector instance
     */
    virtual void set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) = 0;

    /**
     * @brief Get server statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Get server configuration
     * @return GrpcServerConfig Current configuration
     */
    virtual GrpcServerConfig get_config() const = 0;

    /**
     * @brief Update server configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const GrpcServerConfig& config) = 0;
};

/**
 * @brief Default gRPC server implementation
 */
class GrpcServer : public IGrpcServer {
public:
    GrpcServer() = default;
    ~GrpcServer() override = default;

    bool initialize(const GrpcServerConfig& config) override;
    bool start() override;
    bool stop() override;
    bool is_running() const override;

    void set_etl_service(std::shared_ptr<omop::etl::IETLService> service) override;
    void set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager) override;
    void set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) override;
    void set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger) override;
    void set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) override;

    std::unordered_map<std::string, std::any> get_statistics() override;
    GrpcServerConfig get_config() const override;
    bool update_config(const GrpcServerConfig& config) override;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief gRPC service implementation base class
 * 
 * This class provides common functionality for gRPC service implementations,
 * including authentication, authorization, and audit logging.
 */
class GrpcServiceBase {
public:
    GrpcServiceBase() = default;
    virtual ~GrpcServiceBase() = default;

    /**
     * @brief Set ETL service
     * @param service ETL service instance
     */
    void set_etl_service(std::shared_ptr<omop::etl::IETLService> service);

    /**
     * @brief Set authentication manager
     * @param auth_manager Authentication manager instance
     */
    void set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager);

    /**
     * @brief Set authorization manager
     * @param authz_manager Authorization manager instance
     */
    void set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager);

    /**
     * @brief Set audit logger
     * @param audit_logger Audit logger instance
     */
    void set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger);

    /**
     * @brief Set metrics collector
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector);

protected:
    /**
     * @brief Authenticate request
     * @param context gRPC context
     * @return bool True if authentication successful
     */
    bool authenticate_request(const GrpcContext& context);

    /**
     * @brief Authorize request
     * @param context gRPC context
     * @param resource Resource being accessed
     * @param action Action being performed
     * @return bool True if authorization successful
     */
    bool authorize_request(
        const GrpcContext& context,
        const std::string& resource,
        const std::string& action);

    /**
     * @brief Log audit event
     * @param context gRPC context
     * @param event_type Event type
     * @param resource Resource
     * @param action Action
     * @param outcome Outcome
     */
    void log_audit_event(
        const GrpcContext& context,
        const std::string& event_type,
        const std::string& resource,
        const std::string& action,
        bool outcome);

    /**
     * @brief Record metric
     * @param metric_name Metric name
     * @param value Metric value
     * @param labels Metric labels
     */
    void record_metric(
        const std::string& metric_name,
        double value,
        const std::unordered_map<std::string, std::string>& labels = {});

    std::shared_ptr<omop::etl::IETLService> etl_service_;
    std::shared_ptr<omop::security::IAuthManager> auth_manager_;
    std::shared_ptr<omop::security::IAuthorizationManager> authz_manager_;
    std::shared_ptr<omop::security::IAuditLogger> audit_logger_;
    std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector_;
};

/**
 * @brief gRPC interceptors for cross-cutting concerns
 */
class GrpcInterceptors {
public:
    /**
     * @brief Authentication interceptor
     * @param auth_manager Authentication manager
     * @return Interceptor function
     */
    static std::function<bool(GrpcContext&)> authentication_interceptor(
        std::shared_ptr<omop::security::IAuthManager> auth_manager);

    /**
     * @brief Authorization interceptor
     * @param authz_manager Authorization manager
     * @return Interceptor function
     */
    static std::function<bool(const GrpcContext&, const std::string&, const std::string&)> authorization_interceptor(
        std::shared_ptr<omop::security::IAuthorizationManager> authz_manager);

    /**
     * @brief Logging interceptor
     * @param audit_logger Audit logger
     * @return Interceptor function
     */
    static std::function<void(const GrpcContext&, const std::string&, const std::string&, bool)> logging_interceptor(
        std::shared_ptr<omop::security::IAuditLogger> audit_logger);

    /**
     * @brief Metrics interceptor
     * @param metrics_collector Metrics collector
     * @return Interceptor function
     */
    static std::function<void(const std::string&, double, const std::unordered_map<std::string, std::string>&)> metrics_interceptor(
        std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector);

    /**
     * @brief Rate limiting interceptor
     * @param requests_per_minute Rate limit
     * @return Interceptor function
     */
    static std::function<bool(const GrpcContext&)> rate_limiting_interceptor(size_t requests_per_minute);
};

/**
 * @brief gRPC utilities
 */
class GrpcUtils {
public:
    /**
     * @brief Extract metadata from gRPC context
     * @param grpc_context Native gRPC context
     * @return GrpcContext Extracted context
     */
    static GrpcContext extract_context(void* grpc_context);

    /**
     * @brief Create gRPC status
     * @param code Status code
     * @param message Status message
     * @return void* gRPC status
     */
    static void* create_status(int code, const std::string& message);

    /**
     * @brief Convert error to gRPC status
     * @param error Error message
     * @return void* gRPC status
     */
    static void* error_to_status(const std::string& error);

    /**
     * @brief Validate request message
     * @param message Request message
     * @return std::vector<std::string> Validation errors
     */
    static std::vector<std::string> validate_request(const std::any& message);

    /**
     * @brief Serialize response message
     * @param message Response message
     * @return std::string Serialized message
     */
    static std::string serialize_response(const std::any& message);

    /**
     * @brief Deserialize request message
     * @param data Serialized data
     * @return std::any Deserialized message
     */
    static std::any deserialize_request(const std::string& data);
};

/**
 * @brief gRPC health service
 */
class GrpcHealthService : public GrpcServiceBase {
public:
    /**
     * @brief Check health status
     * @param context gRPC context
     * @param service_name Service name to check
     * @return Health status
     */
    std::string check_health(const GrpcContext& context, const std::string& service_name = "");

    /**
     * @brief Watch health status
     * @param context gRPC context
     * @param service_name Service name to watch
     * @param callback Status change callback
     */
    void watch_health(
        const GrpcContext& context,
        const std::string& service_name,
        std::function<void(const std::string&)> callback);
};

/**
 * @brief gRPC reflection service
 */
class GrpcReflectionService : public GrpcServiceBase {
public:
    /**
     * @brief Get server reflection info
     * @param context gRPC context
     * @return std::vector<std::string> Service descriptors
     */
    std::vector<std::string> get_server_reflection_info(const GrpcContext& context);

    /**
     * @brief List services
     * @param context gRPC context
     * @return std::vector<std::string> Service names
     */
    std::vector<std::string> list_services(const GrpcContext& context);

    /**
     * @brief Get service descriptor
     * @param context gRPC context
     * @param service_name Service name
     * @return std::string Service descriptor
     */
    std::string get_service_descriptor(const GrpcContext& context, const std::string& service_name);
};

/**
 * @brief Create gRPC server instance
 * @return std::unique_ptr<IGrpcServer> gRPC server instance
 */
std::unique_ptr<IGrpcServer> create_grpc_server();

/**
 * @brief Get default gRPC server configuration
 * @return GrpcServerConfig Default configuration
 */
GrpcServerConfig get_default_grpc_server_config();

} // namespace omop::api