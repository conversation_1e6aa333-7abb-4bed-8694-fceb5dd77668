#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <future>

#include "etl/etl_service.h"
#include "security/auth_manager.h"
#include "security/authorization.h"
#include "security/audit_logger.h"
#include "monitoring/metrics_collector.h"

namespace omop::api {

/**
 * @brief HTTP request structure
 */
struct HttpRequest {
    std::string method;
    std::string path;
    std::string query_string;
    std::unordered_map<std::string, std::string> headers;
    std::string body;
    std::string remote_addr;
    std::string user_agent;
    std::unordered_map<std::string, std::any> context;
};

/**
 * @brief HTTP response structure
 */
struct HttpResponse {
    int status_code{200};
    std::string status_message{"OK"};
    std::unordered_map<std::string, std::string> headers;
    std::string body;
    std::string content_type{"application/json"};
};

/**
 * @brief REST endpoint configuration
 */
struct EndpointConfig {
    std::string path;
    std::string method;
    std::function<HttpResponse(const HttpRequest&)> handler;
    std::vector<std::string> required_permissions;
    bool require_auth{true};
    bool enable_cors{true};
    std::chrono::seconds timeout{30};
    size_t max_body_size{10 * 1024 * 1024}; // 10MB
    std::unordered_map<std::string, std::any> metadata;
};

/**
 * @brief REST server configuration
 */
struct RestServerConfig {
    std::string host{"0.0.0.0"};
    uint16_t port{8080};
    size_t thread_pool_size{4};
    std::string base_path{"/api/v1"};
    bool enable_ssl{false};
    std::string ssl_cert_path;
    std::string ssl_key_path;
    std::string ssl_ca_path;
    bool enable_compression{true};
    std::chrono::seconds keep_alive_timeout{30};
    size_t max_request_size{50 * 1024 * 1024}; // 50MB
    bool enable_cors{true};
    std::vector<std::string> allowed_origins{"*"};
    std::vector<std::string> allowed_methods{"GET", "POST", "PUT", "DELETE", "OPTIONS"};
    std::vector<std::string> allowed_headers{"*"};
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief REST server interface
 * 
 * This interface defines the contract for REST servers that provide
 * HTTP/HTTPS API endpoints for ETL operations.
 */
class IRestServer {
public:
    virtual ~IRestServer() = default;

    /**
     * @brief Initialize REST server
     * @param config Server configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const RestServerConfig& config) = 0;

    /**
     * @brief Start REST server
     * @return bool True if server started successfully
     */
    virtual bool start() = 0;

    /**
     * @brief Stop REST server
     * @return bool True if server stopped successfully
     */
    virtual bool stop() = 0;

    /**
     * @brief Check if server is running
     * @return bool True if server is running
     */
    virtual bool is_running() const = 0;

    /**
     * @brief Register endpoint
     * @param config Endpoint configuration
     * @return bool True if endpoint registered successfully
     */
    virtual bool register_endpoint(const EndpointConfig& config) = 0;

    /**
     * @brief Unregister endpoint
     * @param method HTTP method
     * @param path Endpoint path
     * @return bool True if endpoint unregistered successfully
     */
    virtual bool unregister_endpoint(const std::string& method, const std::string& path) = 0;

    /**
     * @brief Set ETL service
     * @param service ETL service instance
     */
    virtual void set_etl_service(std::shared_ptr<omop::etl::IETLService> service) = 0;

    /**
     * @brief Set authentication manager
     * @param auth_manager Authentication manager instance
     */
    virtual void set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager) = 0;

    /**
     * @brief Set authorization manager
     * @param authz_manager Authorization manager instance
     */
    virtual void set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) = 0;

    /**
     * @brief Set audit logger
     * @param audit_logger Audit logger instance
     */
    virtual void set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger) = 0;

    /**
     * @brief Set metrics collector
     * @param metrics_collector Metrics collector instance
     */
    virtual void set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) = 0;

    /**
     * @brief Add middleware
     * @param middleware Middleware function
     */
    virtual void add_middleware(std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> middleware) = 0;

    /**
     * @brief Get server statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Get server configuration
     * @return RestServerConfig Current configuration
     */
    virtual RestServerConfig get_config() const = 0;

    /**
     * @brief Update server configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const RestServerConfig& config) = 0;
};

/**
 * @brief Default REST server implementation
 */
class RestServer : public IRestServer {
public:
    RestServer() = default;
    ~RestServer() override = default;

    bool initialize(const RestServerConfig& config) override;
    bool start() override;
    bool stop() override;
    bool is_running() const override;

    bool register_endpoint(const EndpointConfig& config) override;
    bool unregister_endpoint(const std::string& method, const std::string& path) override;

    void set_etl_service(std::shared_ptr<omop::etl::IETLService> service) override;
    void set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager) override;
    void set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) override;
    void set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger) override;
    void set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) override;

    void add_middleware(std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> middleware) override;

    std::unordered_map<std::string, std::any> get_statistics() override;
    RestServerConfig get_config() const override;
    bool update_config(const RestServerConfig& config) override;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief REST API endpoints for ETL service
 */
class ETLRestEndpoints {
public:
    /**
     * @brief Register standard ETL endpoints
     * @param server REST server instance
     * @return bool True if endpoints registered successfully
     */
    static bool register_endpoints(IRestServer& server);

    // ETL Service Endpoints
    static HttpResponse get_service_status(const HttpRequest& request);
    static HttpResponse get_service_config(const HttpRequest& request);
    static HttpResponse update_service_config(const HttpRequest& request);
    static HttpResponse get_service_statistics(const HttpRequest& request);

    // Job Management Endpoints
    static HttpResponse submit_job(const HttpRequest& request);
    static HttpResponse get_job_info(const HttpRequest& request);
    static HttpResponse get_all_jobs(const HttpRequest& request);
    static HttpResponse get_running_jobs(const HttpRequest& request);
    static HttpResponse cancel_job(const HttpRequest& request);
    static HttpResponse wait_for_job(const HttpRequest& request);

    // Authentication Endpoints
    static HttpResponse authenticate(const HttpRequest& request);
    static HttpResponse refresh_token(const HttpRequest& request);
    static HttpResponse revoke_token(const HttpRequest& request);
    static HttpResponse get_user_info(const HttpRequest& request);

    // Health and Monitoring Endpoints
    static HttpResponse health_check(const HttpRequest& request);
    static HttpResponse get_metrics(const HttpRequest& request);
    static HttpResponse get_audit_events(const HttpRequest& request);

    // Utility Endpoints
    static HttpResponse get_api_info(const HttpRequest& request);
    static HttpResponse get_openapi_spec(const HttpRequest& request);
};

/**
 * @brief REST middleware utilities
 */
class RestMiddleware {
public:
    /**
     * @brief Authentication middleware
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> authentication();

    /**
     * @brief Authorization middleware
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> authorization();

    /**
     * @brief CORS middleware
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> cors();

    /**
     * @brief Logging middleware
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> logging();

    /**
     * @brief Metrics middleware
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> metrics();

    /**
     * @brief Rate limiting middleware
     * @param requests_per_minute Rate limit
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> rate_limiting(size_t requests_per_minute);

    /**
     * @brief Request validation middleware
     * @return Middleware function
     */
    static std::function<HttpResponse(const HttpRequest&, std::function<HttpResponse(const HttpRequest&)>)> validation();
};

/**
 * @brief REST utilities
 */
class RestUtils {
public:
    /**
     * @brief Parse JSON request body
     * @param request HTTP request
     * @return std::unordered_map<std::string, std::any> Parsed JSON
     */
    static std::unordered_map<std::string, std::any> parse_json_body(const HttpRequest& request);

    /**
     * @brief Create JSON response
     * @param data Response data
     * @param status_code HTTP status code
     * @return HttpResponse JSON response
     */
    static HttpResponse create_json_response(
        const std::unordered_map<std::string, std::any>& data,
        int status_code = 200);

    /**
     * @brief Create error response
     * @param error_message Error message
     * @param status_code HTTP status code
     * @return HttpResponse Error response
     */
    static HttpResponse create_error_response(
        const std::string& error_message,
        int status_code = 400);

    /**
     * @brief Extract bearer token from request
     * @param request HTTP request
     * @return std::optional<std::string> Bearer token if present
     */
    static std::optional<std::string> extract_bearer_token(const HttpRequest& request);

    /**
     * @brief Parse query parameters
     * @param query_string Query string
     * @return std::unordered_map<std::string, std::string> Query parameters
     */
    static std::unordered_map<std::string, std::string> parse_query_params(const std::string& query_string);

    /**
     * @brief URL encode string
     * @param str String to encode
     * @return std::string URL-encoded string
     */
    static std::string url_encode(const std::string& str);

    /**
     * @brief URL decode string
     * @param str String to decode
     * @return std::string URL-decoded string
     */
    static std::string url_decode(const std::string& str);
};

/**
 * @brief Create REST server instance
 * @return std::unique_ptr<IRestServer> REST server instance
 */
std::unique_ptr<IRestServer> create_rest_server();

/**
 * @brief Get default REST server configuration
 * @return RestServerConfig Default configuration
 */
RestServerConfig get_default_rest_server_config();

} // namespace omop::api