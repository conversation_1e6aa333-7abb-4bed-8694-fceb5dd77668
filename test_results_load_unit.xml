<?xml version="1.0" encoding="UTF-8"?>
<testsuites tests="138" failures="0" disabled="24" errors="0" time="8.407" timestamp="2025-08-01T22:27:58.459" name="AllTests">
  <testsuite name="JsonBatchLoaderTest" tests="8" failures="0" disabled="0" skipped="0" errors="0" time="2.411" timestamp="2025-08-01T22:27:58.459">
    <testcase name="BasicFunctionality" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="65" status="run" result="completed" time="0.21" timestamp="2025-08-01T22:27:58.459" classname="JsonBatchLoaderTest" />
    <testcase name="DifferentOptions" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="87" status="run" result="completed" time="0.311" timestamp="2025-08-01T22:27:58.670" classname="JsonBatchLoaderTest" />
    <testcase name="ArrayOutputFormat" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="79" status="run" result="completed" time="0.213" timestamp="2025-08-01T22:27:58.981" classname="JsonBatchLoaderTest" />
    <testcase name="NdjsonOutputFormat" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="97" status="run" result="completed" time="0.31" timestamp="2025-08-01T22:27:59.192" classname="JsonBatchLoaderTest" />
    <testcase name="ComplexDataTypes" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="124" status="run" result="completed" time="0.228" timestamp="2025-08-01T22:27:59.503" classname="JsonBatchLoaderTest" />
    <testcase name="MetadataInclusion" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="163" status="run" result="completed" time="0.336" timestamp="2025-08-01T22:27:59.731" classname="JsonBatchLoaderTest" />
    <testcase name="ConcurrentWriting" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="210" status="run" result="completed" time="0.322" timestamp="2025-08-01T22:28:00.068" classname="JsonBatchLoaderTest" />
    <testcase name="PerformanceTest" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="254" status="run" result="completed" time="0.478" timestamp="2025-08-01T22:28:00.391" classname="JsonBatchLoaderTest" />
  </testsuite>
  <testsuite name="HttpLoaderTest" tests="4" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-01T22:28:00.869">
    <testcase name="BasicFunctionality" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="128" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.869" classname="HttpLoaderTest" />
    <testcase name="BasicConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="312" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.869" classname="HttpLoaderTest" />
    <testcase name="AuthenticationConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="323" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.869" classname="HttpLoaderTest" />
    <testcase name="RetryMechanism" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="338" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.869" classname="HttpLoaderTest" />
  </testsuite>
  <testsuite name="S3LoaderTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="0.001" timestamp="2025-08-01T22:28:00.869">
    <testcase name="BasicFunctionality" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="158" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.869" classname="S3LoaderTest" />
    <testcase name="BasicConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="420" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.870" classname="S3LoaderTest" />
    <testcase name="MultipartUploadConfiguration" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="430" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.870" classname="S3LoaderTest" />
    <testcase name="MetadataAndTags" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="446" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.870" classname="S3LoaderTest" />
    <testcase name="ServerSideEncryption" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="471" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:00.871" classname="S3LoaderTest" />
  </testsuite>
  <testsuite name="AdditionalLoadersErrorTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.212" timestamp="2025-08-01T22:28:00.871">
    <testcase name="InvalidConfigurations" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="171" status="run" result="completed" time="0.108" timestamp="2025-08-01T22:28:00.871" classname="AdditionalLoadersErrorTest" />
    <testcase name="InvalidConfigurationsComplex" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="487" status="run" result="completed" time="0.104" timestamp="2025-08-01T22:28:00.979" classname="AdditionalLoadersErrorTest" />
  </testsuite>
  <testsuite name="AdditionalLoadersTest" tests="7" failures="0" disabled="0" skipped="0" errors="0" time="0.421" timestamp="2025-08-01T22:28:01.084">
    <testcase name="CompilationTest" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="185" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.084" classname="AdditionalLoadersTest" />
    <testcase name="JsonBatchLoaderInstantiation" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="193" status="run" result="completed" time="0.101" timestamp="2025-08-01T22:28:01.084" classname="AdditionalLoadersTest" />
    <testcase name="JsonBatchLoaderUKLocalization" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="205" status="run" result="completed" time="0.219" timestamp="2025-08-01T22:28:01.185" classname="AdditionalLoadersTest" />
    <testcase name="HttpLoaderUKRegion" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="250" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.404" classname="AdditionalLoadersTest" />
    <testcase name="DatabaseLoaderUKNHSData" file="/workspace/tests/unit/load/additional_loaders_test.cpp" line="268" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.405" classname="AdditionalLoadersTest" />
    <testcase name="CompilationTestComplex" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="741" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.405" classname="AdditionalLoadersTest" />
    <testcase name="JsonBatchLoaderInstantiationComplex" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="750" status="run" result="completed" time="0.1" timestamp="2025-08-01T22:28:01.405" classname="AdditionalLoadersTest" />
  </testsuite>
  <testsuite name="JsonBatchLoaderUKTest" tests="2" failures="0" disabled="0" skipped="2" errors="0" time="0.203" timestamp="2025-08-01T22:28:01.505">
    <testcase name="UKDateFormatting" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="616" status="run" result="skipped" time="0.102" timestamp="2025-08-01T22:28:01.505" classname="JsonBatchLoaderUKTest">
      <skipped message="/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535&#x0A;UK locale not available on this system&#x0A;"><![CDATA[/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535
UK locale not available on this system
]]></skipped>
    </testcase>
    <testcase name="UKMeasurementAndCurrency" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="641" status="run" result="skipped" time="0.1" timestamp="2025-08-01T22:28:01.608" classname="JsonBatchLoaderUKTest">
      <skipped message="/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535&#x0A;UK locale not available on this system&#x0A;"><![CDATA[/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535
UK locale not available on this system
]]></skipped>
    </testcase>
  </testsuite>
  <testsuite name="UKLocalizationTest" tests="3" failures="0" disabled="0" skipped="3" errors="0" time="0." timestamp="2025-08-01T22:28:01.709">
    <testcase name="UKDataValidation" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="662" status="run" result="skipped" time="0." timestamp="2025-08-01T22:28:01.709" classname="UKLocalizationTest">
      <skipped message="/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535&#x0A;UK locale not available on this system&#x0A;"><![CDATA[/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535
UK locale not available on this system
]]></skipped>
    </testcase>
    <testcase name="LargeUKDatasetMemoryManagement" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="674" status="run" result="skipped" time="0." timestamp="2025-08-01T22:28:01.709" classname="UKLocalizationTest">
      <skipped message="/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535&#x0A;UK locale not available on this system&#x0A;"><![CDATA[/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535
UK locale not available on this system
]]></skipped>
    </testcase>
    <testcase name="UKDataPerformanceBenchmark" file="/workspace/tests/unit/load/additional_loaders_test_complex.cpp" line="703" status="run" result="skipped" time="0." timestamp="2025-08-01T22:28:01.709" classname="UKLocalizationTest">
      <skipped message="/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535&#x0A;UK locale not available on this system&#x0A;"><![CDATA[/workspace/tests/unit/load/additional_loaders_test_complex.cpp:535
UK locale not available on this system
]]></skipped>
    </testcase>
  </testsuite>
  <testsuite name="EnhancedBatchTest" tests="9" failures="0" disabled="0" skipped="0" errors="0" time="0.003" timestamp="2025-08-01T22:28:01.709">
    <testcase name="ConstructorInitialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="47" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.709" classname="EnhancedBatchTest" />
    <testcase name="AddRecord" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="55" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.709" classname="EnhancedBatchTest" />
    <testcase name="BatchBecomesFull" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="69" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.709" classname="EnhancedBatchTest" />
    <testcase name="ClearBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="84" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.710" classname="EnhancedBatchTest" />
    <testcase name="SortBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="99" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.710" classname="EnhancedBatchTest" />
    <testcase name="DeduplicateBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="127" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.711" classname="EnhancedBatchTest" />
    <testcase name="CompressBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="152" status="run" result="completed" time="0.001" timestamp="2025-08-01T22:28:01.711" classname="EnhancedBatchTest" />
    <testcase name="UnsupportedCompressionThrows" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="169" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.712" classname="EnhancedBatchTest" />
    <testcase name="EstimateMemoryUsage" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="178" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:01.713" classname="EnhancedBatchTest" />
  </testsuite>
  <testsuite name="BatchLoaderTest" tests="15" failures="0" disabled="0" skipped="0" errors="0" time="2.926" timestamp="2025-08-01T22:28:01.713">
    <testcase name="Initialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="216" status="run" result="completed" time="0.102" timestamp="2025-08-01T22:28:01.713" classname="BatchLoaderTest" />
    <testcase name="LoadSingleRecord" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="230" status="run" result="completed" time="0.101" timestamp="2025-08-01T22:28:01.816" classname="BatchLoaderTest" />
    <testcase name="BatchFillingAndProcessing" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="243" status="run" result="completed" time="0.304" timestamp="2025-08-01T22:28:01.918" classname="BatchLoaderTest" />
    <testcase name="LoadBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="266" status="run" result="completed" time="0.304" timestamp="2025-08-01T22:28:02.222" classname="BatchLoaderTest" />
    <testcase name="CommitFlushesCurrentBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="292" status="run" result="completed" time="0.104" timestamp="2025-08-01T22:28:02.527" classname="BatchLoaderTest" />
    <testcase name="RollbackClearsCurrentBatch" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="313" status="run" result="completed" time="0.102" timestamp="2025-08-01T22:28:02.631" classname="BatchLoaderTest" />
    <testcase name="LoadErrorHandling" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="331" status="run" result="completed" time="0.101" timestamp="2025-08-01T22:28:02.734" classname="BatchLoaderTest" />
    <testcase name="GetStatistics" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="345" status="run" result="completed" time="0.102" timestamp="2025-08-01T22:28:02.835" classname="BatchLoaderTest" />
    <testcase name="BatchLoaderWithDeduplication" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="468" status="run" result="completed" time="0.205" timestamp="2025-08-01T22:28:02.938" classname="BatchLoaderTest" />
    <testcase name="BatchLoaderWithSorting" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="501" status="run" result="completed" time="0.204" timestamp="2025-08-01T22:28:03.144" classname="BatchLoaderTest" />
    <testcase name="FlushIntervalFunctionality" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="531" status="run" result="completed" time="0.404" timestamp="2025-08-01T22:28:03.348" classname="BatchLoaderTest" />
    <testcase name="DestructorHandlesCleanup" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="567" status="run" result="completed" time="0.202" timestamp="2025-08-01T22:28:03.753" classname="BatchLoaderTest" />
    <testcase name="ConcurrentBatchAccess" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="698" status="run" result="completed" time="0.116" timestamp="2025-08-01T22:28:03.956" classname="BatchLoaderTest" />
    <testcase name="MemoryManagement" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="742" status="run" result="completed" time="0.261" timestamp="2025-08-01T22:28:04.072" classname="BatchLoaderTest" />
    <testcase name="BatchCompression" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="801" status="run" result="completed" time="0.304" timestamp="2025-08-01T22:28:04.334" classname="BatchLoaderTest" />
  </testsuite>
  <testsuite name="CsvBatchLoaderTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="1.085" timestamp="2025-08-01T22:28:04.639">
    <testcase name="Initialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="383" status="run" result="completed" time="0.112" timestamp="2025-08-01T22:28:04.639" classname="CsvBatchLoaderTest" />
    <testcase name="CsvOutputWithHeader" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="394" status="run" result="completed" time="0.225" timestamp="2025-08-01T22:28:04.752" classname="CsvBatchLoaderTest" />
    <testcase name="CsvEscaping" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="424" status="run" result="completed" time="0.218" timestamp="2025-08-01T22:28:04.977" classname="CsvBatchLoaderTest" />
    <testcase name="CustomDelimiter" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="589" status="run" result="completed" time="0.318" timestamp="2025-08-01T22:28:05.196" classname="CsvBatchLoaderTest" />
    <testcase name="ErrorScenarios" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="779" status="run" result="completed" time="0.211" timestamp="2025-08-01T22:28:05.514" classname="CsvBatchLoaderTest" />
  </testsuite>
  <testsuite name="BatchLoaderFactoryTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.101" timestamp="2025-08-01T22:28:05.725">
    <testcase name="CreateCsvBatchLoader" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="450" status="run" result="completed" time="0.101" timestamp="2025-08-01T22:28:05.725" classname="BatchLoaderFactoryTest" />
    <testcase name="UnknownLoaderTypeThrows" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="461" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:05.827" classname="BatchLoaderFactoryTest" />
  </testsuite>
  <testsuite name="MmapBatchLoaderTest" tests="15" failures="0" disabled="12" skipped="0" errors="0" time="0.646" timestamp="2025-08-01T22:28:05.827">
    <testcase name="Initialization" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="641" status="run" result="completed" time="0.111" timestamp="2025-08-01T22:28:05.827" classname="MmapBatchLoaderTest" />
    <testcase name="MemoryMappedFileOperations" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="651" status="run" result="completed" time="0.225" timestamp="2025-08-01T22:28:05.938" classname="MmapBatchLoaderTest" />
    <testcase name="FileExtension" file="/workspace/tests/unit/load/batch_loader_test.cpp" line="673" status="run" result="completed" time="0.309" timestamp="2025-08-01T22:28:06.164" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_BasicInitialization" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="62" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_SmallInitialSize" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="103" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_AutomaticFileExtension" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="127" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_ConcurrentAccess" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="155" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_WriteFailureHandling" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="210" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_PlatformSpecificBehavior" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="220" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_MappingFailureRecovery" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="250" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_BatchProcessing" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="282" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_MemoryUsageTracking" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="302" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_FileSynchronization" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="328" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_DiskFullHandling" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="363" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
    <testcase name="DISABLED_PerformanceComparison" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="397" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MmapBatchLoaderTest" />
  </testsuite>
  <testsuite name="BulkInsertBufferTest" tests="3" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-01T22:28:06.474">
    <testcase name="ConstructionAndBasicOperations" file="/workspace/tests/unit/load/database_loader_test.cpp" line="86" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.474" classname="BulkInsertBufferTest" />
    <testcase name="AddRecords" file="/workspace/tests/unit/load/database_loader_test.cpp" line="93" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.474" classname="BulkInsertBufferTest" />
    <testcase name="ClearBuffer" file="/workspace/tests/unit/load/database_loader_test.cpp" line="119" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.474" classname="BulkInsertBufferTest" />
  </testsuite>
  <testsuite name="DatabaseLoaderTest" tests="15" failures="0" disabled="8" skipped="0" errors="0" time="0.005" timestamp="2025-08-01T22:28:06.474">
    <testcase name="Initialization" file="/workspace/tests/unit/load/database_loader_test.cpp" line="158" status="run" result="completed" time="0.001" timestamp="2025-08-01T22:28:06.474" classname="DatabaseLoaderTest" />
    <testcase name="InitializationWithMissingTableThrows" file="/workspace/tests/unit/load/database_loader_test.cpp" line="176" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.476" classname="DatabaseLoaderTest" />
    <testcase name="LoadSingleRecord" file="/workspace/tests/unit/load/database_loader_test.cpp" line="187" status="run" result="completed" time="0.001" timestamp="2025-08-01T22:28:06.477" classname="DatabaseLoaderTest" />
    <testcase name="BulkInsert" file="/workspace/tests/unit/load/database_loader_test.cpp" line="218" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.478" classname="DatabaseLoaderTest" />
    <testcase name="LoadWithMetadataTable" file="/workspace/tests/unit/load/database_loader_test.cpp" line="262" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.479" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_LoadBatch" file="/workspace/tests/unit/load/database_loader_test.cpp" line="289" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_CommitFlushesBuffers" file="/workspace/tests/unit/load/database_loader_test.cpp" line="317" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="RollbackClearsBuffers" file="/workspace/tests/unit/load/database_loader_test.cpp" line="342" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.479" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_Finalization" file="/workspace/tests/unit/load/database_loader_test.cpp" line="366" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="TruncateBeforeLoad" file="/workspace/tests/unit/load/database_loader_test.cpp" line="718" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.480" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_BulkInsertErrorHandling" file="/workspace/tests/unit/load/database_loader_test.cpp" line="743" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_DestructorFlushesBuffers" file="/workspace/tests/unit/load/database_loader_test.cpp" line="770" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_ConcurrentBatchLoading" file="/workspace/tests/unit/load/database_loader_test.cpp" line="804" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_ConnectionLossDuringBatch" file="/workspace/tests/unit/load/database_loader_test.cpp" line="852" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
    <testcase name="DISABLED_PartialBatchFailure" file="/workspace/tests/unit/load/database_loader_test.cpp" line="883" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="DatabaseLoaderTest" />
  </testsuite>
  <testsuite name="PostgreSQLLoaderTest" tests="1" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="1970-01-01T00:00:00.000">
    <testcase name="DISABLED_UseCopyCommand" file="/workspace/tests/unit/load/database_loader_test.cpp" line="431" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="PostgreSQLLoaderTest" />
  </testsuite>
  <testsuite name="MySQLLoaderTest" tests="1" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="1970-01-01T00:00:00.000">
    <testcase name="DISABLED_MultiRowInsert" file="/workspace/tests/unit/load/database_loader_test.cpp" line="478" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="MySQLLoaderTest" />
  </testsuite>
  <testsuite name="OmopDatabaseLoaderTest" tests="1" failures="0" disabled="1" skipped="0" errors="0" time="0." timestamp="1970-01-01T00:00:00.000">
    <testcase name="DISABLED_RecordConversionAndValidation" file="/workspace/tests/unit/load/database_loader_test.cpp" line="573" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="OmopDatabaseLoaderTest" />
  </testsuite>
  <testsuite name="ParallelDatabaseLoaderTest" tests="2" failures="0" disabled="1" skipped="0" errors="0" time="0.001" timestamp="2025-08-01T22:28:06.480">
    <testcase name="Initialization" file="/workspace/tests/unit/load/database_loader_test.cpp" line="639" status="run" result="completed" time="0.001" timestamp="2025-08-01T22:28:06.480" classname="ParallelDatabaseLoaderTest" />
    <testcase name="DISABLED_ParallelBatchLoading" file="/workspace/tests/unit/load/database_loader_test.cpp" line="651" status="notrun" result="suppressed" time="0." timestamp="1970-01-01T00:00:00.000" classname="ParallelDatabaseLoaderTest" />
  </testsuite>
  <testsuite name="LoaderFactoryTest" tests="5" failures="0" disabled="0" skipped="0" errors="0" time="0." timestamp="2025-08-01T22:28:06.482">
    <testcase name="CreateDatabaseLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="674" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderFactoryTest" />
    <testcase name="CreatePostgreSQLLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="683" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderFactoryTest" />
    <testcase name="CreateMySQLLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="692" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderFactoryTest" />
    <testcase name="CreateOmopDatabaseLoader" file="/workspace/tests/unit/load/database_loader_test.cpp" line="701" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderFactoryTest" />
    <testcase name="UnknownLoaderTypeThrows" file="/workspace/tests/unit/load/database_loader_test.cpp" line="710" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderFactoryTest" />
  </testsuite>
  <testsuite name="LoaderBaseTest" tests="13" failures="0" disabled="0" skipped="0" errors="0" time="0.118" timestamp="2025-08-01T22:28:06.482">
    <testcase name="ConstructorInitializesName" file="/workspace/tests/unit/load/loader_base_test.cpp" line="159" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderBaseTest" />
    <testcase name="InitializeSuccess" file="/workspace/tests/unit/load/loader_base_test.cpp" line="165" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderBaseTest" />
    <testcase name="InitializeTwiceThrows" file="/workspace/tests/unit/load/loader_base_test.cpp" line="177" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.482" classname="LoaderBaseTest" />
    <testcase name="InitializeFailure" file="/workspace/tests/unit/load/loader_base_test.cpp" line="188" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.483" classname="LoaderBaseTest" />
    <testcase name="ProgressTracking" file="/workspace/tests/unit/load/loader_base_test.cpp" line="199" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.483" classname="LoaderBaseTest" />
    <testcase name="ErrorRecording" file="/workspace/tests/unit/load/loader_base_test.cpp" line="214" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.483" classname="LoaderBaseTest" />
    <testcase name="ElapsedTimeCalculation" file="/workspace/tests/unit/load/loader_base_test.cpp" line="243" status="run" result="completed" time="0.102" timestamp="2025-08-01T22:28:06.484" classname="LoaderBaseTest" />
    <testcase name="ConfigurationHelpers" file="/workspace/tests/unit/load/loader_base_test.cpp" line="255" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.586" classname="LoaderBaseTest" />
    <testcase name="GetStatisticsWithAdditional" file="/workspace/tests/unit/load/loader_base_test.cpp" line="277" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.586" classname="LoaderBaseTest" />
    <testcase name="Finalization" file="/workspace/tests/unit/load/loader_base_test.cpp" line="294" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.586" classname="LoaderBaseTest" />
    <testcase name="ErrorLimitEnforced" file="/workspace/tests/unit/load/loader_base_test.cpp" line="551" status="run" result="completed" time="0.003" timestamp="2025-08-01T22:28:06.586" classname="LoaderBaseTest" />
    <testcase name="ConcurrentAccess" file="/workspace/tests/unit/load/loader_base_test.cpp" line="641" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.590" classname="LoaderBaseTest" />
    <testcase name="ConcurrentErrorRecording" file="/workspace/tests/unit/load/loader_base_test.cpp" line="670" status="run" result="completed" time="0.009" timestamp="2025-08-01T22:28:06.591" classname="LoaderBaseTest" />
  </testsuite>
  <testsuite name="FileLoaderBaseTest" tests="9" failures="0" disabled="0" skipped="0" errors="0" time="0.047" timestamp="2025-08-01T22:28:06.601">
    <testcase name="InitializeWithExplicitPath" file="/workspace/tests/unit/load/loader_base_test.cpp" line="327" status="run" result="completed" time="0.007" timestamp="2025-08-01T22:28:06.601" classname="FileLoaderBaseTest" />
    <testcase name="InitializeWithAutoPath" file="/workspace/tests/unit/load/loader_base_test.cpp" line="338" status="run" result="completed" time="0.003" timestamp="2025-08-01T22:28:06.608" classname="FileLoaderBaseTest" />
    <testcase name="AppendMode" file="/workspace/tests/unit/load/loader_base_test.cpp" line="352" status="run" result="completed" time="0.009" timestamp="2025-08-01T22:28:06.612" classname="FileLoaderBaseTest" />
    <testcase name="FileOperations" file="/workspace/tests/unit/load/loader_base_test.cpp" line="379" status="run" result="completed" time="0.004" timestamp="2025-08-01T22:28:06.621" classname="FileLoaderBaseTest" />
    <testcase name="OperationsOnClosedFileThrow" file="/workspace/tests/unit/load/loader_base_test.cpp" line="402" status="run" result="completed" time="0.003" timestamp="2025-08-01T22:28:06.626" classname="FileLoaderBaseTest" />
    <testcase name="OpenAlreadyOpenFileThrows" file="/workspace/tests/unit/load/loader_base_test.cpp" line="411" status="run" result="completed" time="0.002" timestamp="2025-08-01T22:28:06.630" classname="FileLoaderBaseTest" />
    <testcase name="DestructorHandlesExceptions" file="/workspace/tests/unit/load/loader_base_test.cpp" line="588" status="run" result="completed" time="0.002" timestamp="2025-08-01T22:28:06.633" classname="FileLoaderBaseTest" />
    <testcase name="AdditionalStatistics" file="/workspace/tests/unit/load/loader_base_test.cpp" line="601" status="run" result="completed" time="0.002" timestamp="2025-08-01T22:28:06.635" classname="FileLoaderBaseTest" />
    <testcase name="ConcurrentWrites" file="/workspace/tests/unit/load/loader_base_test.cpp" line="722" status="run" result="completed" time="0.01" timestamp="2025-08-01T22:28:06.637" classname="FileLoaderBaseTest" />
  </testsuite>
  <testsuite name="NetworkLoaderBaseTest" tests="8" failures="0" disabled="0" skipped="0" errors="0" time="0.011" timestamp="2025-08-01T22:28:06.648">
    <testcase name="InitializeSuccess" file="/workspace/tests/unit/load/loader_base_test.cpp" line="437" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.648" classname="NetworkLoaderBaseTest" />
    <testcase name="InitializeWithoutEndpointThrows" file="/workspace/tests/unit/load/loader_base_test.cpp" line="451" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.649" classname="NetworkLoaderBaseTest" />
    <testcase name="InitializeWithCustomTimeout" file="/workspace/tests/unit/load/loader_base_test.cpp" line="458" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.649" classname="NetworkLoaderBaseTest" />
    <testcase name="NetworkStatisticsTracking" file="/workspace/tests/unit/load/loader_base_test.cpp" line="470" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.649" classname="NetworkLoaderBaseTest" />
    <testcase name="LoadOperation" file="/workspace/tests/unit/load/loader_base_test.cpp" line="492" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.649" classname="NetworkLoaderBaseTest" />
    <testcase name="BatchLoadOperation" file="/workspace/tests/unit/load/loader_base_test.cpp" line="512" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.650" classname="NetworkLoaderBaseTest" />
    <testcase name="FinalizationDisconnects" file="/workspace/tests/unit/load/loader_base_test.cpp" line="539" status="run" result="completed" time="0." timestamp="2025-08-01T22:28:06.650" classname="NetworkLoaderBaseTest" />
    <testcase name="AdditionalStatisticsWithConnectionDuration" file="/workspace/tests/unit/load/loader_base_test.cpp" line="616" status="run" result="completed" time="0.01" timestamp="2025-08-01T22:28:06.650" classname="NetworkLoaderBaseTest" />
  </testsuite>
  <testsuite name="MmapBatchLoaderSimpleTest" tests="2" failures="0" disabled="0" skipped="0" errors="0" time="0.102" timestamp="2025-08-01T22:28:06.660">
    <testcase name="ConstructorTest" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="75" status="run" result="completed" time="0.1" timestamp="2025-08-01T22:28:06.660" classname="MmapBatchLoaderSimpleTest" />
    <testcase name="TempDirectoryTest" file="/workspace/tests/unit/load/mmap_batch_loader_tests.cpp" line="91" status="run" result="completed" time="0.001" timestamp="2025-08-01T22:28:06.761" classname="MmapBatchLoaderSimpleTest" />
  </testsuite>
  <testsuite name="MmapSimpleTest" tests="1" failures="0" disabled="0" skipped="0" errors="0" time="0.101" timestamp="2025-08-01T22:28:06.763">
    <testcase name="BasicCreation" file="/workspace/tests/unit/load/mmap_simple_test.cpp" line="9" status="run" result="completed" time="0.101" timestamp="2025-08-01T22:28:06.763" classname="MmapSimpleTest" />
  </testsuite>
</testsuites>
