<?xml version="1.0" encoding="UTF-8"?>
<testsuites tests="11" failures="3" disabled="0" errors="0" time="2.174" timestamp="2025-08-02T00:25:34.730" name="AllTests">
  <testsuite name="BatchInserterIntegrationTest" tests="11" failures="3" disabled="0" skipped="0" errors="0" time="2.174" timestamp="2025-08-02T00:25:34.730">
    <testcase name="BasicBatchInsertion" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="399" status="run" result="completed" time="0.074" timestamp="2025-08-02T00:25:34.730" classname="BatchInserterIntegrationTest" />
    <testcase name="BulkCopyMode" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="447" status="run" result="completed" time="0.13" timestamp="2025-08-02T00:25:34.804" classname="BatchInserterIntegrationTest" />
    <testcase name="PreparedStatementCaching" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="496" status="run" result="completed" time="0.057" timestamp="2025-08-02T00:25:34.935" classname="BatchInserterIntegrationTest" />
    <testcase name="ErrorHandlingAndRecovery" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="551" status="run" result="completed" time="0.053" timestamp="2025-08-02T00:25:34.992" classname="BatchInserterIntegrationTest" />
    <testcase name="LargeBatchPerformance" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="618" status="run" result="completed" time="1.002" timestamp="2025-08-02T00:25:35.045" classname="BatchInserterIntegrationTest">
      <failure message="unknown file&#x0A;C++ exception with description &quot;format error: failed to parse format-spec&quot; thrown in the test body.&#x0A;" type=""><![CDATA[unknown file
C++ exception with description "format error: failed to parse format-spec" thrown in the test body.
]]></failure>
    </testcase>
    <testcase name="DataTypeHandling" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="678" status="run" result="completed" time="0.042" timestamp="2025-08-02T00:25:36.048" classname="BatchInserterIntegrationTest">
      <failure message="/workspace/tests/integration/load/test_batch_inserter_integration.cpp:749&#x0A;Expected equality of these values:&#x0A;  safe_numeric_cast(result-&gt;get_value(0))&#x0A;    Which is: 0&#x0A;  1&#x0A;" type=""><![CDATA[/workspace/tests/integration/load/test_batch_inserter_integration.cpp:749
Expected equality of these values:
  safe_numeric_cast(result->get_value(0))
    Which is: 0
  1
]]></failure>
    </testcase>
    <testcase name="ConcurrentBatchInsertion" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="757" status="run" result="completed" time="0.462" timestamp="2025-08-02T00:25:36.090" classname="BatchInserterIntegrationTest" />
    <testcase name="CsvGenerationForBulkCopy" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="815" status="run" result="completed" time="0.045" timestamp="2025-08-02T00:25:36.553" classname="BatchInserterIntegrationTest">
      <failure message="/workspace/tests/integration/load/test_batch_inserter_integration.cpp:870&#x0A;Expected: (csv_files.size()) &gt; (0), actual: 0 vs 0&#x0A;No CSV files were generated&#x0A;" type=""><![CDATA[/workspace/tests/integration/load/test_batch_inserter_integration.cpp:870
Expected: (csv_files.size()) > (0), actual: 0 vs 0
No CSV files were generated
]]></failure>
      <failure message="/workspace/tests/integration/load/test_batch_inserter_integration.cpp:876&#x0A;Expected equality of these values:&#x0A;  safe_numeric_cast(result-&gt;get_value(0))&#x0A;    Which is: 0&#x0A;  test_strings.size()&#x0A;    Which is: 6&#x0A;" type=""><![CDATA[/workspace/tests/integration/load/test_batch_inserter_integration.cpp:876
Expected equality of these values:
  safe_numeric_cast(result->get_value(0))
    Which is: 0
  test_strings.size()
    Which is: 6
]]></failure>
    </testcase>
    <testcase name="UKPostcodeValidation" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="888" status="run" result="completed" time="0.044" timestamp="2025-08-02T00:25:36.598" classname="BatchInserterIntegrationTest" />
    <testcase name="UKCurrencyHandling" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="933" status="run" result="completed" time="0.045" timestamp="2025-08-02T00:25:36.643" classname="BatchInserterIntegrationTest" />
    <testcase name="PerformanceTest" file="/workspace/tests/integration/load/test_batch_inserter_integration.cpp" line="996" status="run" result="completed" time="0.214" timestamp="2025-08-02T00:25:36.689" classname="BatchInserterIntegrationTest" />
  </testsuite>
</testsuites>
