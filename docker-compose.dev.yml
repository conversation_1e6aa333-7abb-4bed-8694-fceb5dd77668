# Docker Compose Development Environment for OMOP ETL Pipeline
# This file provides a complete development environment with all build targets

version: '3.8'

services:
  # Development Environment Container
  omop-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        TARGETPLATFORM: ${DOCKER_PLATFORM:-linux/amd64}
        TARGETARCH: ${DOCKER_ARCH:-amd64}
    container_name: omop-etl-dev
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:cached
      - dev_build_cache:/workspace/build
      - dev_conan_cache:/home/<USER>/.conan2
      - dev_cmake_cache:/home/<USER>/.cmake
    working_dir: /workspace
    environment:
      - POSTGRES_HOST=postgres-dev
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db-dev
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - LOG_LEVEL=DEBUG
      - CMAKE_BUILD_TYPE=Debug
      - BUILD_TESTS=ON
      - BUILD_INTEGRATION_TESTS=ON
      - ENABLE_COVERAGE=ON
      - ENABLE_SANITIZERS=ON
    depends_on:
      postgres-dev:
        condition: service_healthy
      omop-db-dev:
        condition: service_healthy
    ports:
      - "8080:8080"  # API server
      - "9090:9090"  # Debug port
    networks:
      - omop-dev-network
    # Keep container running for interactive development
    command: tail -f /dev/null
    # Resource limits for development
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 16G
        reservations:
          cpus: '4.0'
          memory: 8G
    shm_size: 4g
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  # PostgreSQL Source Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: omop-postgres-dev
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
    volumes:
      - clinical-data:/var/lib/postgresql/data
      - ./scripts/init-source-clinical-postgres.sql:/docker-entrypoint-initdb.d/init.sql
      - ./tests/integration/test_data/sql:/docker-entrypoint-initdb.d/test_data:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U clinical_user -d clinical_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-dev-network

  # OMOP CDM Database for Development
  omop-db-dev:
    image: postgres:15-alpine
    container_name: omop-cdm-dev
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=omop_cdm
      - POSTGRES_USER=omop_user
      - POSTGRES_PASSWORD=omop_pass
    volumes:
      - omop_dev_data:/var/lib/postgresql/data
      - ./scripts/init-target-omop-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omop_user -d omop_cdm"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-dev-network

  # Test Database for Unit Tests
  test-db:
    image: postgres:15-alpine
    container_name: omop-test-db
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=omop_test
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_pass
    volumes:
      - test_db_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d omop_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-dev-network

  # Redis for caching during development
  redis-dev:
    image: redis:7-alpine
    container_name: omop-redis-dev
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    networks:
      - omop-dev-network

  # Development tools and utilities
  dev-tools:
    image: ubuntu:22.04
    container_name: omop-dev-tools
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    volumes:
      - .:/workspace:ro
      - dev_tools_cache:/cache
    working_dir: /workspace
    command: tail -f /dev/null
    networks:
      - omop-dev-network
    profiles:
      - tools

volumes:
  clinical-data:
    driver: local
  omop_dev_data:
    driver: local
  test_db_data:
    driver: local
  redis_dev_data:
    driver: local
  dev_build_cache:
    driver: local
  dev_conan_cache:
    driver: local
  dev_cmake_cache:
    driver: local
  dev_tools_cache:
    driver: local

networks:
  omop-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16