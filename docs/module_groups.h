/**
 * @file module_groups.h
 * @brief Module group definitions for OMOP ETL Pipeline documentation
 * 
 * This file defines module groups that organize the OMOP ETL Pipeline
 * documentation by library namespaces for better navigation and understanding.
 */

/**
 * @defgroup omop OMOP ETL Pipeline
 * @brief Main namespace for the OMOP ETL Pipeline project
 * 
 * The OMOP ETL Pipeline is a comprehensive data processing system designed
 * to transform healthcare data into the Observational Medical Outcomes Partnership
 * (OMOP) Common Data Model format.
 * 
 * @{
 */

/**
 * @defgroup omop_common omop::common - Common Library
 * @ingroup omop
 * @brief Common utilities, configuration, and shared components
 * 
 * The Common library provides shared utilities, configuration management,
 * logging, exception handling, and other foundational components used
 * throughout the OMOP ETL Pipeline.
 * 
 * Key components:
 * - Configuration management
 * - Logging and error handling
 * - Utility functions
 * - Common data structures
 * - Exception handling
 * - Validation utilities
 * 
 * @{
 */

/**
 * @defgroup omop_common_config Configuration Management
 * @ingroup omop_common
 * @brief Configuration loading, parsing, and management utilities
 */

/**
 * @defgroup omop_common_logging Logging System
 * @ingroup omop_common
 * @brief Logging utilities and structured logging support
 */

/**
 * @defgroup omop_common_exceptions Exception Handling
 * @ingroup omop_common
 * @brief Exception classes and error handling utilities
 */

/**
 * @defgroup omop_common_utils Utility Functions
 * @ingroup omop_common
 * @brief Common utility functions for string manipulation, date handling, etc.
 */

/**
 * @defgroup omop_common_validation Validation Utilities
 * @ingroup omop_common
 * @brief Data validation and verification utilities
 */

/**
 * @} // omop_common
 */

/**
 * @defgroup omop_core omop::core - Core Library
 * @ingroup omop
 * @brief Core ETL pipeline components and interfaces
 * 
 * The Core library defines the fundamental interfaces and components
 * that form the backbone of the ETL pipeline architecture.
 * 
 * Key components:
 * - Pipeline interfaces
 * - Job management
 * - Processing contexts
 * - Component factories
 * - Core data structures
 * - Record handling
 * 
 * @{
 */

/**
 * @defgroup omop_core_interfaces Core Interfaces
 * @ingroup omop_core
 * @brief Fundamental interfaces for ETL components
 */

/**
 * @defgroup omop_core_record Record Management
 * @ingroup omop_core
 * @brief Data record representation and handling
 */

/**
 * @defgroup omop_core_context Processing Context
 * @ingroup omop_core
 * @brief Processing context and job management
 */

/**
 * @defgroup omop_core_factory Component Factory
 * @ingroup omop_core
 * @brief Factory pattern for component creation
 */

/**
 * @} // omop_core
 */

/**
 * @defgroup omop_cdm omop::cdm - CDM Library
 * @ingroup omop
 * @brief Common Data Model schema and table definitions
 * 
 * The CDM library contains the schema definitions, table structures,
 * and data model components for the OMOP Common Data Model.
 * 
 * Key components:
 * - Table definitions
 * - Schema management
 * - Constraint definitions
 * - Index specifications
 * - OMOP table classes
 * 
 * @{
 */

/**
 * @defgroup omop_cdm_tables OMOP Table Classes
 * @ingroup omop_cdm
 * @brief Concrete OMOP CDM table implementations
 */

/**
 * @defgroup omop_cdm_schema Schema Definitions
 * @ingroup omop_cdm
 * @brief Database schema and constraint definitions
 */

/**
 * @defgroup omop_cdm_factory Table Factory
 * @ingroup omop_cdm
 * @brief Factory for creating OMOP table instances
 */

/**
 * @} // omop_cdm
 */

/**
 * @defgroup omop_extract omop::extract - Extract Library
 * @ingroup omop
 * @brief Data extraction components and connectors
 * 
 * The Extract library provides components for extracting data from
 * various source systems including databases, files, and APIs.
 * 
 * Key components:
 * - Database connectors (PostgreSQL, MySQL, ODBC)
 * - File extractors (CSV, JSON, XML)
 * - Connection pooling
 * - Query builders
 * - Result set handling
 * 
 * @{
 */

/**
 * @defgroup omop_extract_connectors Database Connectors
 * @ingroup omop_extract
 * @brief Database connection and query components
 */

/**
 * @defgroup omop_extract_files File Extractors
 * @ingroup omop_extract
 * @brief File-based data extraction components
 */

/**
 * @defgroup omop_extract_utils Extraction Utilities
 * @ingroup omop_extract
 * @brief Utility functions for data extraction
 */

/**
 * @} // omop_extract
 */

/**
 * @defgroup omop_transform omop::transform - Transform Library
 * @ingroup omop
 * @brief Data transformation and validation components
 * 
 * The Transform library handles data transformation, validation,
 * and mapping operations to convert source data into OMOP CDM format.
 * 
 * Key components:
 * - Field transformations
 * - Data validation
 * - Vocabulary mapping
 * - Transformation engines
 * - Validation rules
 * 
 * @{
 */

/**
 * @defgroup omop_transform_engine Transformation Engine
 * @ingroup omop_transform
 * @brief Core transformation engine and pipeline
 */

/**
 * @defgroup omop_transform_rules Transformation Rules
 * @ingroup omop_transform
 * @brief Data transformation rules and mappings
 */

/**
 * @defgroup omop_transform_vocabulary Vocabulary Mapping
 * @ingroup omop_transform
 * @brief Vocabulary and concept mapping utilities
 */

/**
 * @defgroup omop_transform_validation Data Validation
 * @ingroup omop_transform
 * @brief Data validation and quality checks
 */

/**
 * @} // omop_transform
 */

/**
 * @defgroup omop_load omop::load - Load Library
 * @ingroup omop
 * @brief Data loading and persistence components
 * 
 * The Load library manages the loading and persistence of transformed
 * data into target systems and databases.
 * 
 * Key components:
 * - Database loaders
 * - Batch processing
 * - Loading strategies
 * - Transaction management
 * - Performance optimization
 * 
 * @{
 */

/**
 * @defgroup omop_load_database Database Loaders
 * @ingroup omop_load
 * @brief Database-specific loading components
 */

/**
 * @defgroup omop_load_batch Batch Processing
 * @ingroup omop_load
 * @brief Batch loading and optimization utilities
 */

/**
 * @defgroup omop_load_strategies Loading Strategies
 * @ingroup omop_load
 * @brief Different loading strategies and patterns
 */

/**
 * @} // omop_load
 */

/**
 * @defgroup omop_service omop::service - Service Library
 * @ingroup omop
 * @brief Microservice and API components
 * 
 * The Service library provides microservice infrastructure and
 * inter-service communication components.
 * 
 * Key components:
 * - Service interfaces
 * - gRPC communication
 * - HTTP/REST fallbacks
 * - Service discovery
 * - Load balancing
 * 
 * @{
 */

/**
 * @defgroup omop_service_grpc gRPC Services
 * @ingroup omop_service
 * @brief gRPC-based service implementations
 */

/**
 * @defgroup omop_service_http HTTP Services
 * @ingroup omop_service
 * @brief HTTP/REST service implementations
 */

/**
 * @defgroup omop_service_discovery Service Discovery
 * @ingroup omop_service
 * @brief Service discovery and registration
 */

/**
 * @} // omop_service
 */

/**
 * @defgroup omop_api omop::api - API Library
 * @ingroup omop
 * @brief REST API and client components
 * 
 * The API library provides REST API endpoints and client libraries
 * for external integration with the OMOP ETL Pipeline.
 * 
 * Key components:
 * - REST API endpoints
 * - API documentation
 * - Client libraries
 * - Authentication
 * - Rate limiting
 * 
 * @{
 */

/**
 * @defgroup omop_api_endpoints API Endpoints
 * @ingroup omop_api
 * @brief REST API endpoint implementations
 */

/**
 * @defgroup omop_api_clients API Clients
 * @ingroup omop_api
 * @brief Client libraries for API integration
 */

/**
 * @defgroup omop_api_auth Authentication
 * @ingroup omop_api
 * @brief API authentication and authorization
 */

/**
 * @} // omop_api
 */

/**
 * @defgroup omop_monitoring omop::monitoring - Monitoring Library
 * @ingroup omop
 * @brief Monitoring, metrics, and observability components
 * 
 * The Monitoring library provides components for tracking pipeline
 * performance, collecting metrics, and monitoring system health.
 * 
 * Key components:
 * - Metrics collection
 * - Performance monitoring
 * - Health checks
 * - Alerting
 * 
 * @{
 */

/**
 * @defgroup omop_monitoring_metrics Metrics Collection
 * @ingroup omop_monitoring
 * @brief Metrics collection and reporting
 */

/**
 * @defgroup omop_monitoring_health Health Monitoring
 * @ingroup omop_monitoring
 * @brief Health checks and system monitoring
 */

/**
 * @} // omop_monitoring
 */

/**
 * @defgroup omop_security omop::security - Security Library
 * @ingroup omop
 * @brief Security and authentication components
 * 
 * The Security library provides components for authentication,
 * authorization, and audit logging.
 * 
 * Key components:
 * - Authentication
 * - Authorization
 * - Audit logging
 * - Security utilities
 * 
 * @{
 */

/**
 * @defgroup omop_security_auth Authentication
 * @ingroup omop_security
 * @brief User authentication and session management
 */

/**
 * @defgroup omop_security_audit Audit Logging
 * @ingroup omop_security
 * @brief Security audit logging and compliance
 */

/**
 * @} // omop_security
 */

/**
 * @} // omop
 */ 