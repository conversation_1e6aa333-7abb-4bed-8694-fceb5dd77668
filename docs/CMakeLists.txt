# Documentation CMakeLists.txt
# This file configures the build for the OMOP ETL Pipeline documentation

cmake_minimum_required(VERSION 3.20)

# Documentation support
if(BUILD_DOCS AND HAVE_DOXYGEN)
    # Find Doxygen
    find_package(Doxygen REQUIRED)
    
    # Ensure docs/sources directory exists
    file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/docs/sources)
    
    # Configure custom Doxygen file
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in
        ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        @ONLY
    )
    
    # Define module groups for better organization
    set(DOXYGEN_GROUP_DEFINITIONS
        "\\defgroup omop OMOP ETL Pipeline
         Main namespace for the OMOP ETL Pipeline project.
         
         \\defgroup omop_common omop::common - Common Library
         \\ingroup omop
         Common utilities, configuration, and shared components.
         
         \\defgroup omop_core omop::core - Core Library  
         \\ingroup omop
         Core ETL pipeline components and interfaces.
         
         \\defgroup omop_cdm omop::cdm - CDM Library
         \\ingroup omop  
         Common Data Model schema and table definitions.
         
         \\defgroup omop_extract omop::extract - Extract Library
         \\ingroup omop
         Data extraction components and connectors.
         
         \\defgroup omop_transform omop::transform - Transform Library
         \\ingroup omop
         Data transformation and validation components.
         
         \\defgroup omop_load omop::load - Load Library
         \\ingroup omop
         Data loading and persistence components.
         
         \\defgroup omop_service omop::service - Service Library
         \\ingroup omop
         Microservice and API components.
         
         \\defgroup omop_api omop::api - API Library
         \\ingroup omop
         REST API and client components.
         
         \\defgroup omop_monitoring omop::monitoring - Monitoring Library
         \\ingroup omop
         Monitoring, metrics, and observability components.
         
         \\defgroup omop_security omop::security - Security Library
         \\ingroup omop
         Security and authentication components."
    )
    
    # Create the main documentation target
    doxygen_add_docs(docs
        ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        COMMENT "Generating OMOP ETL Pipeline API documentation"
    )
    
    # Add custom target to open documentation
    find_program(OPEN_COMMAND NAMES open xdg-open start)
    if(OPEN_COMMAND)
        add_custom_target(open-docs
            COMMAND ${OPEN_COMMAND} ${CMAKE_SOURCE_DIR}/docs/sources/html/index.html
            DEPENDS docs
            COMMENT "Opening documentation in browser"
        )
    endif()
    
    # Install documentation
    install(DIRECTORY ${CMAKE_SOURCE_DIR}/docs/sources/html/
        DESTINATION ${CMAKE_INSTALL_DATADIR}/doc/omop-etl
        OPTIONAL
    )
    
    message(STATUS "Documentation target 'docs' created successfully")
    message(STATUS "Documentation will be generated in: ${CMAKE_SOURCE_DIR}/docs/sources/")
    message(STATUS "Build with: cmake --build build --target docs")
    
elseif(BUILD_DOCS AND NOT HAVE_DOXYGEN)
    message(FATAL_ERROR "BUILD_DOCS is enabled but Doxygen was not found. Please install Doxygen first.")
endif() 