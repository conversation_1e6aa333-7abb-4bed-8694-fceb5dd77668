# Doxygen configuration file for OMOP ETL Pipeline
# This file is configured by CMake

# Project information
PROJECT_NAME           = "OMOP ETL Pipeline"
PROJECT_NUMBER         = @PROJECT_VERSION@
PROJECT_BRIEF          = "Observational Medical Outcomes Partnership ETL Pipeline"
OUTPUT_DIRECTORY       = @DOXYGEN_OUTPUT_DIRECTORY@

# Input settings
INPUT                  = @CMAKE_SOURCE_DIR@/src/lib @CMAKE_SOURCE_DIR@/src/app @CMAKE_SOURCE_DIR@/README.md @CMAKE_CURRENT_SOURCE_DIR@/module_groups.h @CMAKE_CURRENT_SOURCE_DIR@/namespace_hierarchies.md
RECURSIVE              = YES
FILE_PATTERNS          = *.h *.hpp *.cpp *.cc *.cxx *.md
EXCLUDE_PATTERNS       = */build/* */tests/* */CMakeFiles/* */_deps/* */Testing/* */unit/* */integration/*
EXCLUDE_SYMLINKS       = YES

# Output settings
GENERATE_HTML          = YES
HTML_OUTPUT            = html
HTML_FILE_EXTENSION    = .html
HTML_HEADER            = 
HTML_FOOTER            = 
HTML_STYLESHEET        = 
HTML_EXTRA_STYLESHEET  = 
HTML_EXTRA_FILES       = 
HTML_COLORSTYLE_HUE    = 220
HTML_COLORSTYLE_SAT    = 100
HTML_COLORSTYLE_GAMMA  = 80
HTML_TIMESTAMP         = YES
HTML_DYNAMIC_MENUS     = YES
HTML_DYNAMIC_SECTIONS  = NO

# Enable XML output for external diagram generation
GENERATE_XML           = YES
XML_OUTPUT             = xml
XML_PROGRAMLISTING     = YES

# Navigation settings
GENERATE_TREEVIEW      = YES
TREEVIEW_WIDTH         = 250
FULL_SIDEBAR           = YES
SIDEBAR_WIDTH          = 300

# Search settings
SEARCHENGINE           = YES
SERVER_BASED_SEARCH    = YES
EXTERNAL_SEARCH        = YES
SEARCHDATA_FILE        = searchdata.xml

# Namespace and class organization - Enhanced for namespace hierarchy
SHOW_NAMESPACES        = YES
SHOW_GROUPED_MEMB_INC  = YES
SHOW_USED_FILES        = YES
SHOW_FILES             = YES
GROUP_NESTED_COMPOUNDS = YES
SUBGROUPING            = YES
INLINE_GROUPED_CLASSES = YES
INLINE_SIMPLE_STRUCTS  = YES

# Class hierarchy and inheritance settings - Enhanced for comprehensive visualization
CLASS_DIAGRAMS         = YES
HIDE_UNDOC_RELATIONS   = NO
CLASS_GRAPH            = YES
COLLABORATION_GRAPH    = YES
UML_LOOK               = YES
TEMPLATE_RELATIONS     = YES

# Enhanced visualization settings for namespace-based class display
HAVE_DOT               = YES
CLASS_GRAPH            = YES
COLLABORATION_GRAPH    = YES
UML_LOOK               = YES
TEMPLATE_RELATIONS     = YES
INCLUDE_GRAPH          = YES
INCLUDED_BY_GRAPH      = YES
CALL_GRAPH             = YES
CALLER_GRAPH           = YES
GRAPHICAL_HIERARCHY    = YES
DIRECTORY_GRAPH        = YES
DOT_IMAGE_FORMAT       = svg
INTERACTIVE_SVG        = YES
DOT_MULTI_TARGETS      = YES
GENERATE_LEGEND        = YES
DOT_CLEANUP            = NO
DOT_NUM_THREADS        = 0

# Settings for namespace-specific class hierarchy pages
SHOW_NAMESPACES        = YES
SHOW_GROUPED_MEMB_INC  = YES
SHOW_USED_FILES        = YES
SHOW_FILES             = YES
GROUP_NESTED_COMPOUNDS = YES
SUBGROUPING            = YES
INLINE_GROUPED_CLASSES = YES
INLINE_SIMPLE_STRUCTS  = YES

# Generate dedicated class hierarchy pages
GENERATE_CLASS_HIERARCHY = YES
CLASS_HIERARCHY_GRAPH  = YES
CLASS_HIERARCHY_GRAPH_DEPTH = 10
CLASS_HIERARCHY_GRAPH_COLOR = YES
CLASS_HIERARCHY_GRAPH_LEGEND = YES

# Enable group-based diagrams for namespace-like organization
GROUP_GRAPHS = YES
COLLABORATION_GRAPH = YES
INCLUDE_GRAPH = YES
INCLUDED_BY_GRAPH = YES

# Generate namespace-specific pages
GENERATE_NAMESPACE_PAGES = YES
NAMESPACE_PAGE_TITLE   = "Namespace %s"
NAMESPACE_PAGE_HEADER  = "Namespace %s"
NAMESPACE_PAGE_FOOTER  = ""

# Enhanced class documentation
SHOW_INCLUDE_FILES     = YES
SHOW_GROUPED_MEMB_INC  = YES
SHOW_USED_FILES        = YES
SHOW_FILES             = YES
SHOW_NAMESPACES        = YES
GROUP_NESTED_COMPOUNDS = YES
SUBGROUPING            = YES
INLINE_GROUPED_CLASSES = YES
INLINE_SIMPLE_STRUCTS  = YES

# Namespace-specific visualization
SHOW_NAMESPACES        = YES
SHOW_GROUPED_MEMB_INC  = YES
SHOW_USED_FILES        = YES
SHOW_FILES             = YES
GROUP_NESTED_COMPOUNDS = YES
SUBGROUPING            = YES
INLINE_GROUPED_CLASSES = YES
INLINE_SIMPLE_STRUCTS  = YES

# Documentation quality
EXTRACT_ALL            = YES
EXTRACT_LOCAL_CLASSES  = YES
EXTRACT_LOCAL_METHODS  = YES
EXTRACT_ANON_NSPACES   = YES
HIDE_SCOPE_NAMES       = NO
SHOW_INCLUDE_FILES     = YES
INLINE_INFO            = YES
SORT_BRIEF_DOCS        = YES
SORT_MEMBER_DOCS       = YES
SORT_GROUP_NAMES       = YES
SORT_LEXICALLY         = YES

# Warnings and errors
QUIET                  = YES
WARN_IF_UNDOCUMENTED   = YES
WARN_IF_DOC_ERROR      = YES
WARN_NO_PARAMDOC       = YES

# Main page
USE_MDFILE_AS_MAINPAGE = @CMAKE_SOURCE_DIR@/README.md

# Custom layout file for better organization
LAYOUT_FILE = @CMAKE_CURRENT_SOURCE_DIR@/DoxygenLayout.xml

# Module grouping for better organization
GROUP_NAMES            = "omop" "omop_common" "omop_core" "omop_cdm" "omop_extract" "omop_transform" "omop_load" "omop_service" "omop_api" "omop_monitoring" "omop_security"

# Custom group definitions
@DOXYGEN_GROUP_DEFINITIONS@ 