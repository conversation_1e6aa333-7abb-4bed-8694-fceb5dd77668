# High Level Design: OMOP ETL Pipeline Sequence Diagram

```mermaid
sequenceDiagram
    title High-Level OMOP ETL Sequence Diagram

    actor User
    participant ApiService as omop::api::ETLApiService
    participant ETLService as omop::service::ETLService
    participant PipelineMgr as omop::core::PipelineManager
    participant Pipeline as omop::core::ETLPipeline
    participant Context as omop::core::ProcessingContext
    participant Extractor as omop::extract::IExtractor
    participant Transformer as omop::transform::TransformationEngine
    participant Loader as omop::load::ILoader
    participant Config as omop::common::ConfigurationManager
    participant CDM as omop::cdm::OmopTable
    participant DB as Target Database

    User->>ApiService: POST /api/v1/etl/jobs
    activate ApiService

    ApiService->>Config: get_table_mapping(table_name)
    activate Config
    Config-->>ApiService: TableMapping
    deactivate Config

    ApiService->>ETLService: create_job(ETLJobRequest)
    activate ETLService

    ETLService->>PipelineMgr: submit_job(pipeline)
    activate PipelineMgr

    PipelineMgr->>Pipeline: start(job_id)
    activate Pipeline

    Pipeline->>Context: set_job_id(job_id)
    activate Context
    Context-->>Pipeline: void
    deactivate Context

    Pipeline->>Extractor: initialize(config, context)
    activate Extractor
    Extractor->>Config: get_source_db()
    activate Config
    Config-->>Extractor: DatabaseConfig
    deactivate Config
    Extractor-->>Pipeline: void
    deactivate Extractor

    Pipeline->>Transformer: initialize(config, context)
    activate Transformer
    Transformer->>Config: get_table_mapping(table_name)
    activate Config
    Config-->>Transformer: TableMapping
    deactivate Config
    Transformer-->>Pipeline: void
    deactivate Transformer

    Pipeline->>Loader: initialize(config, context)
    activate Loader
    Loader->>Config: get_target_db()
    activate Config
    Config-->>Loader: DatabaseConfig
    deactivate Config
    Loader->>DB: connect()
    activate DB
    DB-->>Loader: connection
    deactivate DB
    Loader-->>Pipeline: void
    deactivate Loader

    note over Pipeline: Start parallel processing threads

    par Extraction Thread
        loop while has_more_data()
            Pipeline->>Extractor: extract_batch(batch_size, context)
            activate Extractor
            Extractor->>Context: increment_processed(count)
            activate Context
            Context-->>Extractor: void
            deactivate Context
            Extractor-->>Pipeline: RecordBatch
            deactivate Extractor
            
            Pipeline->>Context: set_stage(Transform)
            activate Context
            Context-->>Pipeline: void
            deactivate Context
            
            Pipeline->>Transformer: transform_batch(batch, context)
            activate Transformer
            Transformer->>Transformer: apply_transformations(record)
            Transformer-->>Pipeline: RecordBatch
            deactivate Transformer
            
            Pipeline->>Context: set_stage(Load)
            activate Context
            Context-->>Pipeline: void
            deactivate Context
            
            Pipeline->>Loader: load_batch(batch, context)
            activate Loader
            Loader->>CDM: convert_to_omop_table(record)
            activate CDM
            CDM-->>Loader: OmopTable
            deactivate CDM
            Loader->>DB: insert_batch(omop_table)
            activate DB
            DB-->>Loader: result
            deactivate DB
            Loader->>Context: increment_processed(count)
            activate Context
            Context-->>Loader: void
            deactivate Context
            Loader-->>Pipeline: size_t
            deactivate Loader
        end
    end

    Pipeline->>Context: set_stage(Complete)
    activate Context
    Context-->>Pipeline: void
    deactivate Context

    Pipeline->>Loader: finalize(context)
    activate Loader
    Loader->>DB: commit()
    activate DB
    DB-->>Loader: success
    deactivate DB
    Loader-->>Pipeline: void
    deactivate Loader

    Pipeline-->>PipelineMgr: JobInfo
    deactivate Pipeline

    PipelineMgr-->>ETLService: JobInfo
    deactivate PipelineMgr

    ETLService->>ETLService: update_job_result(job_id, result)
    ETLService-->>ApiService: job_id
    deactivate ETLService

    ApiService-->>User: HTTP 201 Created {job_id, status}
    deactivate ApiService

    note over User: Job is now running asynchronously

    User->>ApiService: GET /api/v1/etl/jobs/{job_id}
    activate ApiService

    ApiService->>ETLService: get_job_result(job_id)
    activate ETLService
    ETLService-->>ApiService: ETLJobResult
    deactivate ETLService

    ApiService-->>User: HTTP 200 OK {job_status, progress, metrics}
    deactivate ApiService
```
