```mermaid
flowchart TD
    DataAdmin[Data Administrator]
    SysAdmin[System Administrator]
    ComplianceOfficer[Compliance Officer]
    Analyst[Healthcare Analyst]

    Pipeline[ETL Pipeline]
    Orchestrator[Pipeline Orchestrator]
    Scheduler[Job Scheduler]

    Extract[Extraction Service]
    Transform[Transformation Service]
    Validate[Validation Service]
    Load[Loading Service]

    Security[Security Service]
    Monitoring[Monitoring Service]
    Documentation[Documentation Service]
    Metadata[Metadata Service]

    Staging[(Staging Database)]
    MetadataRepo[(Metadata Repository)]
    OMOPCDM[(OMOP CDM Database)]

    Databases[(Healthcare Databases)]
    Files[(File Systems)]
    HL7[(HL7 Messages)]

    Terminology[(Terminology Services)]
    OHDSI[(OHDSI Tools)]

    %% User relationships
    DataAdmin --> Scheduler
    SysAdmin --> Monitoring
    ComplianceOfficer --> Documentation
    Analyst --> OHDSI

    %% Core flow
    Scheduler --> Orchestrator
    Orchestrator --> Pipeline
    Pipeline --> Extract
    Pipeline --> Transform
    Pipeline --> Validate
    Pipeline --> Load

    %% Component relationships
    Extract --> Staging
    Transform --> Staging
    Validate --> Staging
    Load --> OMOPCDM

    %% Cross-cutting concerns
    Pipeline --> Security
    Pipeline --> Monitoring
    Pipeline --> Documentation
    Pipeline --> Metadata

    %% Data flow
    Metadata --> MetadataRepo

    %% Source systems
    Extract --> Databases
    Extract --> Files
    Extract --> HL7

    %% Integration
    Transform --> Terminology
    Load --> OHDSI
```
