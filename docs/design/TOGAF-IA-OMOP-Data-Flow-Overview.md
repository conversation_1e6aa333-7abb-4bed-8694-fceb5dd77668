```mermaid
flowchart LR
    %% Source Systems
    subgraph Sources["Source Healthcare Data Systems"]
        ehr[(EHR Systems)]
        lab[(Laboratory Results)]
        rx[(Prescription Data)]
        hl7[HL7 Messages]
        files[Flat Files]
    end

    %% Extraction & Staging
    subgraph Staging["Extraction & Staging"]
        raw_staging[(Raw Data\nStaging Area)]
        profiler[Data Profiler]
        change_tracker[Change Tracker]
    end

    %% Transformation
    subgraph Transform["Transformation Layer"]
        concept_map[Concept Mapping]
        data_enrichment[Data Enrichment]
        relationship_mgmt[Relationship Management]
        temporal_integrity[Temporal Integrity]
    end

    %% Terminology Services
    subgraph Terminology["Terminology Services"]
        snomed[(SNOMED CT)]
        readcodes[(Read Codes)]
        dmplus[(dm+d)]
        vocab_service[Vocabulary Service]
    end

    %% Validation
    subgraph Validation["Validation Layer"]
        cdm_validator[CDM Validator]
        quality_check[Quality Checker]
        value_set_validator[Value Set Validator]
        relationship_validator[Relationship Validator]
    end

    %% Target OMOP CDM
    subgraph OMOP["OMOP CDM Target"]
        persons[(Person)]
        conditions[(Condition_Occurrence)]
        drugs[(Drug_Exposure)]
        procedures[(Procedure_Occurrence)]
        measurements[(Measurement)]
        observations[(Observation)]
        visits[(Visit_Occurrence)]
        metadata[(Metadata Tables)]
    end

    %% Analytics
    subgraph Analytics["OHDSI Analytics"]
        atlas[Atlas]
        achilles[Achilles]
        webapi[WebAPI]
    end

    %% Flow Connections
    Sources --> Staging
    ehr --> raw_staging
    claims --> raw_staging
    lab --> raw_staging
    rx --> raw_staging
    hl7 --> raw_staging
    files --> raw_staging

    raw_staging --> profiler
    raw_staging --> change_tracker

    profiler --> Transform
    change_tracker --> Transform

    Transform --> Validation
    concept_map <--> vocab_service
    vocab_service <--> Terminology

    Validation --> OMOP
    cdm_validator --> persons
    cdm_validator --> conditions
    cdm_validator --> drugs
    cdm_validator --> procedures
    cdm_validator --> measurements
    cdm_validator --> observations
    cdm_validator --> visits

    OMOP --> Analytics
    persons --> atlas
    conditions --> atlas
    drugs --> atlas
    procedures --> atlas
    measurements --> atlas
    observations --> atlas
    visits --> atlas
    metadata --> atlas
```
