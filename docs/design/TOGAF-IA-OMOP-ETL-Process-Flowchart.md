```mermaid
flowchart TD
    %% ETL Process Start
    start([Start ETL Process]) --> load_config

    %% Configuration Phase
    load_config[Load Configuration] --> validate_config{Validate Config?}
    validate_config -- Valid --> check_incremental{Incremental Mode?}
    validate_config -- Invalid --> config_error[Log Configuration Errors]
    config_error --> notify_admin[Notify Administrator]
    notify_admin --> end_error([End with Error])

    %% Extraction Phase
    check_incremental -- Full Load --> init_extract[Initialize Extraction]
    check_incremental -- Incremental --> get_last_run[Get Last Run Timestamp]
    get_last_run --> init_extract

    init_extract --> connect_source{Connect to Source?}
    connect_source -- Success --> extract_data[Extract Data]
    connect_source -- Failure --> connect_error[Log Connection Error]
    connect_error --> retry_connect{Retry?}
    retry_connect -- Yes --> connect_source
    retry_connect -- No --> end_error

    extract_data --> data_profile[Profile Raw Data]
    data_profile --> create_staging[Create/Truncate Staging Tables]
    create_staging --> load_staging[Load Data to Staging]
    load_staging --> extraction_complete{Extraction Complete?}
    extraction_complete -- Yes --> create_checkpoint1[Create Extraction Checkpoint]
    extraction_complete -- No --> extract_error[Log Extraction Error]
    extract_error --> retry_extract{Retry Extract?}
    retry_extract -- Yes --> extract_data
    retry_extract -- No --> end_error

    %% Transformation Phase
    create_checkpoint1 --> init_transform[Initialize Transformation]
    init_transform --> load_mappings[Load Concept Mappings]
    load_mappings --> vocab_lookup{Vocabulary Lookup}

    vocab_lookup -- Success --> apply_transforms[Apply Transformations]
    vocab_lookup -- Failure --> vocab_error[Log Vocabulary Errors]
    vocab_error --> end_error

    apply_transforms --> enrich_data[Enrich Data]
    enrich_data --> check_relationships[Verify Relationships]
    check_relationships --> transform_complete{Transformation Complete?}
    transform_complete -- Yes --> create_checkpoint2[Create Transformation Checkpoint]
    transform_complete -- No --> transform_error[Log Transformation Error]
    transform_error --> retry_transform{Retry Transform?}
    retry_transform -- Yes --> apply_transforms
    retry_transform -- No --> end_error

    %% Validation Phase
    create_checkpoint2 --> init_validation[Initialize Validation]
    init_validation --> cdm_validation[Validate CDM Compliance]
    cdm_validation --> data_quality[Check Data Quality]
    data_quality --> value_set_validation[Validate Value Sets]
    value_set_validation --> relationship_validation[Validate Relationships]
    relationship_validation --> validation_complete{Validation Passed?}
    validation_complete -- Yes --> create_checkpoint3[Create Validation Checkpoint]
    validation_complete -- No --> log_validation_issues[Log Validation Issues]
    log_validation_issues --> manual_review{Manual Review?}
    manual_review -- Yes --> notify_review[Notify for Review]
    notify_review --> wait_approval[Wait for Approval]
    wait_approval --> review_complete{Approved?}
    review_complete -- Yes --> create_checkpoint3
    review_complete -- No --> end_error
    manual_review -- No --> end_error

    %% Loading Phase
    create_checkpoint3 --> init_load[Initialize Loading]
    init_load --> prepare_target[Prepare Target Schema]
    prepare_target --> begin_transaction[Begin Transaction]
    begin_transaction --> bulk_load[Bulk Load Data]
    bulk_load --> verify_load[Verify Loaded Data]
    verify_load --> load_complete{Load Successful?}
    load_complete -- Yes --> commit_transaction[Commit Transaction]
    load_complete -- No --> rollback_transaction[Rollback Transaction]
    rollback_transaction --> retry_load{Retry Load?}
    retry_load -- Yes --> init_load
    retry_load -- No --> end_error

    %% Finalization Phase
    commit_transaction --> post_load_optimize[Post-Load Optimization]
    post_load_optimize --> generate_metadata[Generate Metadata]
    generate_metadata --> generate_docs[Generate Documentation]
    generate_docs --> collect_metrics[Collect Performance Metrics]
    collect_metrics --> notify_completion[Notify Completion]
    notify_completion --> end_success([End Successfully])
```
