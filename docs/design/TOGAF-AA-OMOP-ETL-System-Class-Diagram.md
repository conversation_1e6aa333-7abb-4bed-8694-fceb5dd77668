# High Level Design: OMOP ETL System Class Diagram

```mermaid
classDiagram
    title High-Level OMOP ETL Class Diagram

    %% Core Package
    class omop_core_ETLPipeline {
        -PipelineConfig config_
        -std::unique_ptr<IExtractor> extractor_
        -std::unique_ptr<ITransformer> transformer_
        -std::unique_ptr<ILoader> loader_
        -ProcessingContext context_
        -std::atomic<JobStatus> status_
        +set_extractor(extractor: IExtractor): void
        +set_transformer(transformer: ITransformer): void
        +set_loader(loader: ILoader): void
        +start(job_id: string): future<JobInfo>
        +stop(): void
        +pause(): void
        +resume(): void
        +get_status(): JobStatus
    }

    class omop_core_PipelineManager {
        -std::unordered_map<string, JobEntry> jobs_
        -std::mutex jobs_mutex_
        -std::atomic<bool> should_stop_
        +submit_job(pipeline: ETLPipeline): string
        +get_job_info(job_id: string): optional<JobInfo>
        +cancel_job(job_id: string): bool
        +pause_job(job_id: string): bool
        +resume_job(job_id: string): bool
        +shutdown(wait_for_jobs: bool): void
    }

    class omop_core_ProcessingContext {
        -Stage current_stage_
        -string job_id_
        -atomic<size_t> processed_count_
        -atomic<size_t> error_count_
        -chrono::steady_clock::time_point start_time_
        +set_stage(stage: Stage): void
        +increment_processed(count: size_t): void
        +increment_errors(count: size_t): void
        +set_data(key: string, value: any): void
        +get_data(key: string): optional<any>
    }

    class omop_core_Record {
        -unordered_map<string, any> fields_
        -RecordMetadata metadata_
        +setField(name: string, value: any): void
        +getField(name: string): any
        +getFieldNames(): vector<string>
        +getFieldCount(): size_t
        +isEmpty(): bool
        +toJson(): string
        +fromJson(json: string): Record
    }

    class omop_core_RecordBatch {
        -vector<Record> records_
        -size_t capacity_
        +addRecord(record: Record): void
        +getRecord(index: size_t): Record
        +size(): size_t
        +isEmpty(): bool
        +clear(): void
    }

    class omop_core_IExtractor {
        <<interface>>
        +initialize(config: map, context: ProcessingContext): void
        +extract_batch(batch_size: size_t, context: ProcessingContext): RecordBatch
        +has_more_data(): bool
        +get_type(): string
        +finalize(context: ProcessingContext): void
    }

    class omop_core_ITransformer {
        <<interface>>
        +initialize(config: map, context: ProcessingContext): void
        +transform(record: Record, context: ProcessingContext): optional<Record>
        +transform_batch(batch: RecordBatch, context: ProcessingContext): RecordBatch
        +validate(record: Record): ValidationResult
        +get_type(): string
    }

    class omop_core_ILoader {
        <<interface>>
        +initialize(config: map, context: ProcessingContext): void
        +load(record: Record, context: ProcessingContext): bool
        +load_batch(batch: RecordBatch, context: ProcessingContext): size_t
        +finalize(context: ProcessingContext): void
        +get_type(): string
    }

    %% Service Package
    class omop_service_ETLService {
        -shared_ptr<ConfigurationManager> config_
        -shared_ptr<PipelineManager> pipeline_manager_
        -shared_ptr<Logger> logger_
        -unordered_map<string, ETLJobResult> job_results_
        +create_job(request: ETLJobRequest): string
        +get_job_result(job_id: string): optional<ETLJobResult>
        +cancel_job(job_id: string): bool
        +pause_job(job_id: string): bool
        +resume_job(job_id: string): bool
        +run_all_tables(parallel: bool): map<string, string>
    }

    class omop_service_ServiceOrchestrator {
        -OrchestratorConfig config_
        -shared_ptr<ConfigurationManager> config_manager_
        -unique_ptr<IExtractService> extract_service_
        -unique_ptr<ITransformService> transform_service_
        -unique_ptr<ILoadService> load_service_
        +orchestrate_job(request: ETLJobRequest): future<ETLJobResult>
        +get_service_health(): vector<ServiceHealth>
        +get_orchestrator_stats(): OrchestratorStats
    }

    %% API Package
    class omop_api_ETLApiService {
        -shared_ptr<ServiceOrchestrator> orchestrator_
        -shared_ptr<ConfigurationManager> config_
        -unordered_map<string, Route> routes_
        +register_routes(): void
        +create_job(request: HttpRequest): HttpResponse
        +get_job(request: HttpRequest): HttpResponse
        +list_jobs(request: HttpRequest): HttpResponse
        +cancel_job(request: HttpRequest): HttpResponse
        +get_health(request: HttpRequest): HttpResponse
        +get_metrics(request: HttpRequest): HttpResponse
    }

    class omop_api_ApiServer {
        -ApiServerConfig config_
        -unique_ptr<IHttpServer> http_server_
        -unique_ptr<ETLApiService> api_service_
        +start(): void
        +stop(): void
        +is_running(): bool
        +get_url(): string
    }

    %% Extract Package
    class omop_extract_ExtractorBase {
        <<abstract>>
        -string name_
        -shared_ptr<ConfigurationManager> config_
        -shared_ptr<Logger> logger_
        +initialize(config: map, context: ProcessingContext): void
        +extract_batch(batch_size: size_t, context: ProcessingContext): RecordBatch
        +has_more_data(): bool
        +get_type(): string
    }

    class omop_extract_CSVExtractor {
        -string file_path_
        -ifstream file_stream_
        -vector<string> headers_
        -char delimiter_
        +initialize(config: map, context: ProcessingContext): void
        +extractBatchImpl(batch_size: size_t): vector<Record>
        +getSchema(): SourceSchema
    }

    class omop_extract_DatabaseExtractor {
        -unique_ptr<DatabaseConnector> connector_
        -string query_
        -vector<string> columns_
        +initialize(config: map, context: ProcessingContext): void
        +extractBatchImpl(batch_size: size_t): vector<Record>
        +getSchema(): SourceSchema
    }

    %% Transform Package
    class omop_transform_TransformationEngine {
        -unordered_map<string, unique_ptr<FieldTransformation>> transformations_
        -vector<string> field_order_
        -shared_ptr<VocabularyService> vocabulary_service_
        +initialize(config: map, context: ProcessingContext): void
        +transform(record: Record, context: ProcessingContext): optional<Record>
        +transform_batch(batch: RecordBatch, context: ProcessingContext): RecordBatch
        +validate(record: Record): ValidationResult
        +register_transformation(type: string, factory: function): void
    }

    class omop_transform_VocabularyService {
        -unique_ptr<IDatabaseConnection> connection_
        -unordered_map<string, Concept> concept_cache_
        +lookup_concept(source_value: string, vocabulary: string): optional<int>
        +search_concepts(query: string, vocabulary: string): vector<Concept>
        +get_concept(concept_id: int): optional<Concept>
    }

    class omop_transform_FieldTransformation {
        <<abstract>>
        +transform(input: any, context: ProcessingContext): any
        +validate_input(input: any): bool
        +get_type(): string
        +configure(params: YAML::Node): void
    }

    class omop_transform_DateTransformation {
        -string input_format_
        -string output_format_
        -string timezone_
        +transform(input: any, context: ProcessingContext): any
        +configure(params: YAML::Node): void
    }

    class omop_transform_VocabularyTransformation {
        -VocabularyService& vocabulary_service_
        -string vocabulary_name_
        -string source_vocabulary_
        -string target_vocabulary_
        +transform(input: any, context: ProcessingContext): any
        +configure(params: YAML::Node): void
    }

    %% Load Package
    class omop_load_LoaderBase {
        <<abstract>>
        -string name_
        -bool initialized_
        -LoadStats stats_
        +initialize(config: map, context: ProcessingContext): void
        +load(record: Record, context: ProcessingContext): bool
        +load_batch(batch: RecordBatch, context: ProcessingContext): size_t
        +finalize(context: ProcessingContext): void
        +get_statistics(): map<string, any>
    }

    class omop_load_DatabaseLoader {
        -unique_ptr<IDatabaseConnection> connection_
        -string table_name_
        -string schema_name_
        -bool enable_transactions_
        +initialize(config: map, context: ProcessingContext): void
        +load(record: Record, context: ProcessingContext): bool
        +load_batch(batch: RecordBatch, context: ProcessingContext): size_t
        +commit(context: ProcessingContext): void
        +rollback(context: ProcessingContext): void
    }

    class omop_load_OmopDatabaseLoader {
        -string current_omop_table_
        -bool validate_foreign_keys_
        +initialize(config: map, context: ProcessingContext): void
        +load(record: Record, context: ProcessingContext): bool
        +convert_to_omop_table(record: Record, table_name: string): unique_ptr<OmopTable>
        +validate_omop_constraints(table: OmopTable): bool
    }

    %% CDM Package
    class omop_cdm_OmopTable {
        <<abstract>>
        +table_name(): string
        +schema_name(): string
        +to_insert_sql(escape_values: bool): string
        +field_names(): vector<string>
        +field_values(): vector<any>
        +visit_fields(visitor: FieldVisitor): void
        +validate(): bool
        +validation_errors(): vector<string>
    }

    class omop_cdm_Person {
        -int64_t person_id_
        -int32_t gender_concept_id_
        -int32_t year_of_birth_
        -optional<chrono::system_clock::time_point> birth_datetime_
        +table_name(): string
        +to_insert_sql(escape_values: bool): string
        +validate(): bool
    }

    class omop_cdm_ConditionOccurrence {
        -int64_t condition_occurrence_id_
        -int64_t person_id_
        -int32_t condition_concept_id_
        -chrono::system_clock::time_point condition_start_date_
        +table_name(): string
        +to_insert_sql(escape_values: bool): string
        +validate(): bool
    }

    class omop_cdm_OmopTableFactory {
        +create(table_name: string): unique_ptr<OmopTable>
        +get_supported_tables(): vector<string>
        +is_supported(table_name: string): bool
        +register_table(table_name: string, creator: function): void
    }

    %% Common Package
    class omop_common_ConfigurationManager {
        -mutex config_mutex_
        -YAML::Node root_config_
        -unordered_map<string, TableMapping> table_mappings_
        -DatabaseConfig source_db_
        -DatabaseConfig target_db_
        +load_config(filepath: string): void
        +get_table_mapping(table_name: string): TableMapping
        +get_source_db(): DatabaseConfig
        +get_target_db(): DatabaseConfig
        +get_value(key: string): optional<any>
        +validate_config(): void
    }

    class omop_common_Logger {
        -string name_
        -LogLevel level_
        +info(message: string): void
        +warn(message: string): void
        +error(message: string): void
        +debug(message: string): void
        +set_level(level: LogLevel): void
    }

    class omop_common_OmopException {
        -string message_
        -source_location location_
        +what(): const char*
        +message(): string_view
        +location(): const source_location&
    }

    %% Relationships
    omop_core_IExtractor <|-- omop_extract_ExtractorBase
    omop_extract_ExtractorBase <|-- omop_extract_CSVExtractor
    omop_extract_ExtractorBase <|-- omop_extract_DatabaseExtractor

    omop_core_ITransformer <|-- omop_transform_TransformationEngine
    omop_transform_FieldTransformation <|-- omop_transform_DateTransformation
    omop_transform_FieldTransformation <|-- omop_transform_VocabularyTransformation

    omop_core_ILoader <|-- omop_load_LoaderBase
    omop_load_LoaderBase <|-- omop_load_DatabaseLoader
    omop_load_DatabaseLoader <|-- omop_load_OmopDatabaseLoader

    omop_cdm_OmopTable <|-- omop_cdm_Person
    omop_cdm_OmopTable <|-- omop_cdm_ConditionOccurrence

    omop_core_ETLPipeline --> omop_core_IExtractor
    omop_core_ETLPipeline --> omop_core_ITransformer
    omop_core_ETLPipeline --> omop_core_ILoader
    omop_core_ETLPipeline --> omop_core_ProcessingContext
    omop_core_ETLPipeline --> omop_core_RecordBatch

    omop_core_PipelineManager --> omop_core_ETLPipeline
    omop_core_PipelineManager --> omop_core_JobInfo

    omop_service_ETLService --> omop_core_PipelineManager
    omop_service_ETLService --> omop_common_ConfigurationManager
    omop_service_ETLService --> omop_service_ETLJobRequest
    omop_service_ETLService --> omop_service_ETLJobResult

    omop_service_ServiceOrchestrator --> omop_service_ETLService
    omop_service_ServiceOrchestrator --> omop_common_ConfigurationManager

    omop_api_ETLApiService --> omop_service_ServiceOrchestrator
    omop_api_ETLApiService --> omop_common_ConfigurationManager
    omop_api_ApiServer --> omop_api_ETLApiService

    omop_transform_TransformationEngine --> omop_transform_FieldTransformation
    omop_transform_TransformationEngine --> omop_transform_VocabularyService

    omop_core_RecordBatch --> omop_core_Record
    omop_core_Record --> omop_core_ProcessingContext

    omop_common_ConfigurationManager --> omop_common_TableMapping
    omop_common_ConfigurationManager --> omop_common_DatabaseConfig

    omop_load_OmopDatabaseLoader --> omop_cdm_OmopTable
    omop_load_OmopDatabaseLoader --> omop_cdm_OmopTableFactory

    omop_transform_VocabularyTransformation --> omop_transform_VocabularyService
