# OMOP Extract Library Architecture

## Executive Summary

The OMOP Extract Library provides a comprehensive data extraction framework that supports multiple data sources including CSV files, JSON documents, and various database systems (PostgreSQL, MySQL, ODBC). The library implements a unified extraction interface with support for batch processing, parallel extraction, error handling, and progress monitoring. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)
6. [Security and Compliance](#security-and-compliance)

---

## Business Architecture

### Business Context

The Extract Library serves as the data acquisition layer in the OMOP ETL pipeline, enabling healthcare organizations to extract data from diverse sources for transformation into the OMOP CDM format. The library addresses the critical business need for standardized data extraction across multiple healthcare systems and data formats.

### Business Capabilities

#### Core Extraction Capabilities
- **Multi-Format Data Extraction**: Support for CSV, JSON, and database sources
- **Batch Processing**: Efficient handling of large datasets through batch operations
- **Parallel Extraction**: Concurrent processing from multiple sources
- **Error Resilience**: Robust error handling and recovery mechanisms
- **Progress Monitoring**: Real-time tracking of extraction progress

#### Data Source Management
- **Source Discovery**: Automatic detection and validation of data sources
- **Connection Management**: Efficient handling of database connections and file handles
- **Resource Optimization**: Memory and CPU optimization for large datasets
- **Format Validation**: Automatic detection and validation of data formats

### Business Processes

#### Data Extraction Workflow
```mermaid
graph TD
    A[Source Identification] --> B[Connection Establishment]
    B --> C[Data Validation]
    C --> D[Batch Extraction]
    D --> E[Error Handling]
    E --> F[Progress Reporting]
    F --> G[Data Delivery]
    G --> H[Resource Cleanup]
```

#### Parallel Extraction Process
```mermaid
graph TD
    A[Multiple Sources] --> B[Source Distribution]
    B --> C[Parallel Processing]
    C --> D[Result Aggregation]
    D --> E[Data Consolidation]
    E --> F[Quality Validation]
```

### Business Rules

#### Data Quality Rules
- **Format Compliance**: All extracted data must conform to expected formats
- **Encoding Standards**: UTF-8 encoding enforcement for text data
- **Null Handling**: Consistent null value representation across sources
- **Date Validation**: Proper date format validation and parsing

#### Performance Rules
- **Memory Management**: Efficient memory usage for large datasets
- **Batch Optimization**: Optimal batch sizes based on available memory
- **Connection Pooling**: Reuse of database connections for efficiency
- **Error Thresholds**: Maximum acceptable error rates per source

#### Compliance Rules
- **Data Privacy**: Secure handling of sensitive healthcare data
- **Audit Trail**: Complete logging of extraction activities
- **Access Control**: Proper authentication and authorization
- **Data Retention**: Compliance with healthcare data retention policies

---

## Information Architecture

### Data Models

#### Core Data Structures

```mermaid
classDiagram
    class IExtractor {
        <<interface>>
        +initialize(config, context)
        +extract_batch(batch_size, context)
        +has_more_data()
        +get_statistics()
        +finalize(context)
    }
    
    class ExtractorBase {
        <<abstract>>
        #config_: unordered_map~string, any~
        #context_: ProcessingContext&
        #logger_: shared_ptr~Logger~
        +initialize(config, context)
        +get_statistics()
        +finalize(context)
        #validate_config()
        #log_extraction_stats()
    }
    
    class CSVExtractor {
        -file_path_: string
        -delimiter_: char
        -has_header_: bool
        -encoding_: string
        -batch_size_: size_t
        -file_stream_: ifstream
        -current_line_: size_t
        +extract_batch(batch_size, context)
        +has_more_data()
        -parse_csv_line()
        -detect_encoding()
    }
    
    class JSONExtractor {
        -file_path_: string
        -root_path_: string
        -batch_size_: size_t
        -file_stream_: ifstream
        -current_position_: size_t
        +extract_batch(batch_size, context)
        +has_more_data()
        -parse_json_object()
        -extract_nested_fields()
    }
    
    class DatabaseExtractor {
        -connection_string_: string
        -query_: string
        -batch_size_: size_t
        -connection_: shared_ptr~Connection~
        -statement_: shared_ptr~Statement~
        -result_set_: shared_ptr~ResultSet~
        +extract_batch(batch_size, context)
        +has_more_data()
        -execute_query()
        -fetch_results()
    }
    
    class PostgreSQLConnector {
        -host_: string
        -port_: int
        -database_: string
        -username_: string
        -password_: string
        -connection_: PGconn*
        +connect()
        +disconnect()
        +execute_query()
        +fetch_results()
    }
    
    class MySQLConnector {
        -host_: string
        -port_: int
        -database_: string
        -username_: string
        -password_: string
        -connection_: MYSQL*
        +connect()
        +disconnect()
        +execute_query()
        +fetch_results()
    }
    
    class ODBCConnector {
        -dsn_: string
        -connection_string_: string
        -connection_: SQLHENV
        -stmt_: SQLHSTMT
        +connect()
        +disconnect()
        +execute_query()
        +fetch_results()
    }
    
    class ConnectionPool {
        -connections_: vector~shared_ptr~Connection~~
        -available_: queue~shared_ptr~Connection~~
        -mutex_: mutex
        -condition_: condition_variable
        +get_connection()
        +release_connection()
        +initialize_pool()
        +cleanup_pool()
    }
    
    IExtractor <|-- ExtractorBase
    ExtractorBase <|-- CSVExtractor
    ExtractorBase <|-- JSONExtractor
    ExtractorBase <|-- DatabaseExtractor
    DatabaseExtractor --> PostgreSQLConnector
    DatabaseExtractor --> MySQLConnector
    DatabaseExtractor --> ODBCConnector
    DatabaseExtractor --> ConnectionPool
```

#### Record and Batch Models

```mermaid
classDiagram
    class Record {
        -fields_: unordered_map~string, any~
        -metadata_: RecordMetadata
        +setField(name, value)
        +getField(name)
        +hasField(name)
        +getFieldNames()
        +to_json()
    }
    
    class RecordBatch {
        -records_: vector~Record~
        -batch_id_: string
        -metadata_: BatchMetadata
        +addRecord(record)
        +getRecord(index)
        +size()
        +empty()
        +clear()
        +to_json()
    }
    
    class RecordMetadata {
        +source_table: string
        +source_row_number: size_t
        +extraction_time: time_point
        +record_id: string
        +custom: unordered_map~string, string~
    }
    
    class BatchMetadata {
        +batch_number: size_t
        +source_name: string
        +extraction_start: time_point
        +extraction_end: time_point
        +record_count: size_t
        +error_count: size_t
    }
    
    Record --> RecordMetadata
    RecordBatch --> BatchMetadata
    RecordBatch --> Record
```

### Information Flow

#### Data Extraction Flow

```mermaid
graph TD
    A[Source System] --> B[Connection Establishment]
    B --> C[Schema Discovery]
    C --> D[Query Execution]
    D --> E[Result Processing]
    E --> F[Data Validation]
    F --> G[Record Creation]
    G --> H[Batch Assembly]
    H --> I[Data Delivery]
    
    J[Configuration] --> B
    J --> D
    K[Processing Context] --> E
    K --> G
    L[Error Handler] --> F
    L --> I
```

#### Batch Processing Flow

```mermaid
graph TD
    A[Source Data] --> B[Batch Size Check]
    B --> C{Sufficient Data?}
    C -->|Yes| D[Create Batch]
    C -->|No| E[Wait for More Data]
    D --> F[Validate Batch]
    F --> G{Valid?}
    G -->|Yes| H[Deliver Batch]
    G -->|No| I[Error Handling]
    H --> J[Update Statistics]
    I --> J
    E --> B
    J --> K[Continue Processing]
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Extract Library Components"
        A[Extractor Factory] --> B[CSV Extractor]
        A --> C[JSON Extractor]
        A --> D[Database Extractor]
        A --> E[Compressed CSV Extractor]
        
        D --> F[PostgreSQL Connector]
        D --> G[MySQL Connector]
        D --> H[ODBC Connector]
        
        I[Connection Pool] --> F
        I --> G
        I --> H
        
        J[Extract Utils] --> B
        J --> C
        J --> D
        J --> E
        
        K[Extractor Base] --> B
        K --> C
        K --> D
        K --> E
    end
    
    subgraph "Interface Layer"
        L[IExtractor]
        M[ExtractorBase]
        N[ProcessingContext]
    end
    
    B --> L
    C --> L
    D --> L
    E --> L
    
    B --> M
    C --> M
    D --> M
    E --> M
```

### Implemented Extractors

#### 1. CSV Extractor (`src/lib/extract/csv_extractor.h/cpp`)

**Features:**
- **Delimiter Support**: Configurable field separators (comma, tab, pipe, etc.)
- **Header Handling**: Automatic header detection and field mapping
- **Encoding Support**: UTF-8, UTF-16, and other encoding formats
- **Large File Support**: Streaming processing for files of any size
- **Error Recovery**: Graceful handling of malformed CSV data

**Configuration:**
```yaml
csv_extractor:
  file_path: "/path/to/data.csv"
  delimiter: ","
  has_header: true
  encoding: "UTF-8"
  batch_size: 1000
  skip_empty_lines: true
  trim_whitespace: true
```

**Usage Example:**
```cpp
auto extractor = std::make_unique<CSVExtractor>();
std::unordered_map<std::string, std::any> config = {
    {"file_path", "/path/to/data.csv"},
    {"delimiter", ","},
    {"has_header", true}
};
extractor->initialize(config, context);

while (extractor->has_more_data()) {
    auto batch = extractor->extract_batch(1000, context);
    // Process batch
}
```

#### 2. JSON Extractor (`src/lib/extract/json_extractor.h/cpp`)

**Features:**
- **Nested Object Support**: Handles complex JSON structures
- **Array Processing**: Processes JSON arrays efficiently
- **Path-based Extraction**: Extracts data using JSONPath expressions
- **Schema Validation**: Validates JSON structure against schemas
- **Streaming Parsing**: Memory-efficient processing of large JSON files

**Configuration:**
```yaml
json_extractor:
  file_path: "/path/to/data.json"
  root_path: "$.records[*]"
  batch_size: 1000
  flatten_objects: true
  include_metadata: true
```

**Usage Example:**
```cpp
auto extractor = std::make_unique<JSONExtractor>();
std::unordered_map<std::string, std::any> config = {
    {"file_path", "/path/to/data.json"},
    {"root_path", "$.patients[*]"},
    {"batch_size", 1000}
};
extractor->initialize(config, context);

while (extractor->has_more_data()) {
    auto batch = extractor->extract_batch(1000, context);
    // Process batch
}
```

#### 3. Database Extractors

##### PostgreSQL Connector (`src/lib/extract/postgresql_connector.h/cpp`)

**Features:**
- **Native PostgreSQL Support**: Direct libpq integration
- **Prepared Statements**: Optimized query execution
- **Connection Pooling**: Efficient connection management
- **Large Object Support**: Handles BLOB and CLOB data types
- **Transaction Management**: Proper transaction handling

**Configuration:**
```yaml
postgresql_extractor:
  host: "localhost"
  port: 5432
  database: "clinical_data"
  username: "${DB_USER}"
  password: "${DB_PASSWORD}"
  query: "SELECT * FROM patients WHERE active = true"
  batch_size: 1000
  connection_pool_size: 10
```

##### MySQL Connector (`src/lib/extract/mysql_connector.h/cpp`)

**Features:**
- **Native MySQL Support**: Direct MySQL C API integration
- **SSL/TLS Support**: Secure connection handling
- **Character Set Support**: Full Unicode and multi-byte character support
- **Replication Support**: Read from replica databases
- **Performance Optimization**: Optimized for MySQL-specific features

**Configuration:**
```yaml
mysql_extractor:
  host: "localhost"
  port: 3306
  database: "clinical_data"
  username: "${DB_USER}"
  password: "${DB_PASSWORD}"
  query: "SELECT * FROM patients WHERE active = 1"
  batch_size: 1000
  charset: "utf8mb4"
```

##### ODBC Connector (`src/lib/extract/odbc_connector.h/cpp`)

**Features:**
- **Universal Database Support**: Works with any ODBC-compliant database
- **Driver Management**: Automatic driver detection and configuration
- **Cross-Platform**: Works on Windows, Linux, and macOS
- **Legacy System Support**: Connects to older database systems
- **Configuration Flexibility**: Supports various connection string formats

**Configuration:**
```yaml
odbc_extractor:
  dsn: "ClinicalDataDSN"
  connection_string: "DSN=ClinicalDataDSN;UID=${DB_USER};PWD=${DB_PASSWORD}"
  query: "SELECT * FROM patients WHERE active = 1"
  batch_size: 1000
  timeout: 30
```

#### 4. Compressed CSV Extractor (`src/lib/extract/compressed_csv_extractor.cpp`)

**Features:**
- **Compression Support**: Handles gzip, bzip2, and other compression formats
- **Automatic Detection**: Detects compression format automatically
- **Streaming Decompression**: Memory-efficient processing of compressed files
- **Error Recovery**: Handles corrupted compressed files gracefully

**Configuration:**
```yaml
compressed_csv_extractor:
  file_path: "/path/to/data.csv.gz"
  compression_type: "auto"  # auto, gzip, bzip2
  delimiter: ","
  has_header: true
  batch_size: 1000
```

### Connection Management

#### Connection Pool (`src/lib/extract/connection_pool.cpp`)

**Features:**
- **Connection Reuse**: Efficiently manages database connections
- **Load Balancing**: Distributes load across multiple connections
- **Health Monitoring**: Monitors connection health and availability
- **Automatic Recovery**: Reconnects on connection failures
- **Configurable Pool Size**: Adjustable based on workload

**Usage Example:**
```cpp
auto pool = std::make_shared<ConnectionPool>();
pool->initialize(10, connection_config);  // 10 connections

auto connection = pool->get_connection();
// Use connection
pool->release_connection(connection);
```

### Error Handling and Recovery

#### Exception Hierarchy

```mermaid
classDiagram
    class ExtractionException {
        <<abstract>>
        +string source_name
        +string extraction_query
        +string error_details
        +what()
    }
    
    class CSVExtractionException {
        +string file_path
        +size_t line_number
        +string line_content
    }
    
    class JSONExtractionException {
        +string file_path
        +string json_path
        +string parsing_error
    }
    
    class DatabaseExtractionException {
        +string connection_string
        +string sql_query
        +int error_code
        +string database_error
    }
    
    class ConnectionException {
        +string connection_string
        +string connection_error
        +int retry_count
    }
    
    ExtractionException <|-- CSVExtractionException
    ExtractionException <|-- JSONExtractionException
    ExtractionException <|-- DatabaseExtractionException
    ExtractionException <|-- ConnectionException
```

#### Error Recovery Strategies

1. **Retry with Backoff**: Automatic retry for transient errors
2. **Connection Pooling**: Automatic failover to healthy connections
3. **Partial Data Recovery**: Continues processing after non-critical errors
4. **Checkpoint and Resume**: Resumes extraction from last successful point
5. **Error Logging**: Comprehensive error reporting for troubleshooting

### Performance Optimization

#### Memory Management

- **Streaming Processing**: Processes data in chunks to minimize memory usage
- **Object Pooling**: Reuses objects to reduce allocation overhead
- **Smart Pointers**: Ensures proper resource cleanup
- **Batch Size Optimization**: Configurable batch sizes based on available memory

#### Database Optimization

- **Prepared Statements**: Reduces parsing overhead for repeated queries
- **Connection Pooling**: Minimizes connection establishment overhead
- **Query Optimization**: Supports optimized SQL queries
- **Index-aware Queries**: Leverages database indexes for performance

#### Parallel Processing

- **Multi-threaded Extraction**: Parallel processing from multiple sources
- **Batch Parallelism**: Concurrent processing of multiple batches
- **I/O Overlap**: Overlaps computation with I/O operations
- **Load Balancing**: Distributes load across available resources

---

## Cross-Cutting Concerns

### Configuration Management

The Extract Library integrates with the Common Library's configuration system to provide:

- **YAML-based Configuration**: Human-readable configuration format
- **Environment Variable Substitution**: Secure credential management
- **Configuration Validation**: Schema and semantic validation
- **Hot Reloading**: Runtime configuration updates

### Logging and Monitoring

- **Structured Logging**: JSON-formatted logs with context information
- **Performance Metrics**: Detailed timing and throughput measurements
- **Health Checks**: Connection health monitoring and reporting
- **Audit Trail**: Comprehensive extraction activity logging

### Security

- **Credential Management**: Secure storage and transmission of credentials
- **Connection Encryption**: TLS/SSL support for database connections
- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Access Control**: Role-based permissions for data access

---

## Deployment and Operations

### Build System

The Extract Library uses CMake for build management with the following structure:

```
src/lib/extract/
├── CMakeLists.txt
├── extract.h
├── extractor_base.h/cpp
├── csv_extractor.h/cpp
├── json_extractor.h/cpp
├── database_connector.h/cpp
├── postgresql_connector.h/cpp
├── mysql_connector.h/cpp
├── odbc_connector.h/cpp
├── compressed_csv_extractor.cpp
├── connection_pool.cpp
├── extract_utils.h/cpp
├── extractor_factory.h/cpp
└── platform/
    ├── CMakeLists.txt
    ├── unix_utils.cpp
    └── windows_utils.cpp
```

### Dependencies

**Core Dependencies:**
- **libpq**: PostgreSQL client library
- **libmysqlclient**: MySQL client library
- **unixODBC**: ODBC driver manager
- **zlib**: Compression library for gzip support
- **bzip2**: Compression library for bzip2 support

**System Dependencies:**
- **C++17**: Modern C++ features and standard library
- **Threading**: Standard C++ threading support
- **Filesystem**: C++17 filesystem library

### Testing Strategy

- **Unit Tests**: Component-level testing with mock objects
- **Integration Tests**: End-to-end extraction testing
- **Performance Tests**: Throughput and scalability validation
- **Stress Tests**: Error condition and recovery testing

### Monitoring and Observability

- **Metrics Collection**: Performance and error rate monitoring
- **Health Checks**: Connection availability verification
- **Distributed Tracing**: Request flow tracking across components
- **Alerting**: Automated notification of issues and anomalies

### Deployment Models

#### Monolithic Deployment
- Single application instance with all extractors
- Simplified deployment and management
- Shared memory and resource utilization

#### Microservices Deployment
- Separate services for different extraction types
- Independent scaling and deployment
- Network-based inter-service communication

### Operational Considerations

#### Resource Management
- **Memory Limits**: Configurable memory usage limits
- **CPU Affinity**: Thread-to-core binding for performance
- **I/O Optimization**: Asynchronous I/O operations
- **Connection Pooling**: Database connection management

#### Fault Tolerance
- **Circuit Breakers**: Prevents cascade failures
- **Retry Mechanisms**: Automatic recovery from transient errors
- **Graceful Degradation**: Continues operation with reduced functionality
- **Data Consistency**: Transaction-based data integrity

#### Scalability
- **Horizontal Scaling**: Multiple extraction instances
- **Vertical Scaling**: Resource allocation optimization
- **Load Balancing**: Distribution of processing load
- **Caching**: Frequently accessed data caching

---

## Security and Compliance

### Data Protection

- **Encryption in Transit**: TLS/SSL for all database connections
- **Encryption at Rest**: Support for encrypted file systems
- **Credential Security**: Secure storage and transmission of credentials
- **Access Control**: Role-based permissions for data access

### Compliance

- **HIPAA Compliance**: Healthcare data protection standards
- **GDPR Compliance**: European data protection regulations
- **Audit Logging**: Comprehensive audit trail for compliance
- **Data Retention**: Configurable data retention policies

### Security Best Practices

- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Principle of Least Privilege**: Minimal required permissions
- **Secure Configuration**: Encrypted configuration management
- **Regular Security Updates**: Timely security patch management

## Conclusion

The OMOP Extract Library provides a robust, scalable, and extensible foundation for data extraction from diverse sources. Its modular architecture, comprehensive error handling, and performance optimizations make it suitable for both small-scale deployments and enterprise-wide implementations. The plugin-based extractor system ensures the framework can evolve to meet changing requirements while maintaining stability and performance. 