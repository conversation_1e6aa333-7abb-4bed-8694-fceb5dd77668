# OMOP Service Architecture

## Executive Summary

The OMOP Service Architecture provides a comprehensive service layer that orchestrates ETL operations, manages job lifecycle, and exposes REST API and gRPC interfaces for external integration. The architecture supports both monolithic and microservices deployment modes, with sophisticated job management, monitoring, and orchestration capabilities. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)
6. [Security and Compliance](#security-and-compliance)

---

## Business Architecture

### Business Context

The Service Architecture serves as the orchestration and integration layer in the OMOP ETL pipeline, enabling healthcare organizations to manage ETL operations through standardized interfaces. The architecture addresses the critical business need for scalable, reliable, and secure service delivery while supporting both internal and external integration requirements.

### Business Capabilities

#### Core Service Capabilities
- **Job Management**: Comprehensive job lifecycle management and orchestration
- **API Services**: REST API and gRPC interfaces for external integration
- **Service Orchestration**: Coordination of microservices and distributed operations
- **Monitoring and Alerting**: Real-time monitoring and automated alerting
- **Configuration Management**: Dynamic configuration management and updates

#### Integration Capabilities
- **Multi-Protocol Support**: REST, gRPC, and internal service communication
- **Service Discovery**: Automatic service discovery and load balancing
- **Circuit Breaker**: Fault tolerance and resilience patterns
- **Rate Limiting**: API rate limiting and throttling
- **Authentication and Authorization**: Comprehensive security controls

### Business Processes

#### Job Management Workflow
```mermaid
graph TD
    A[Job Request] --> B[Request Validation]
    B --> C[Job Creation]
    C --> D[Resource Allocation]
    D --> E[Pipeline Execution]
    E --> F[Progress Monitoring]
    F --> G[Completion/Error]
    G --> H[Result Reporting]
    H --> I[Resource Cleanup]
```

#### Service Orchestration Flow
```mermaid
graph TD
    A[Client Request] --> B[API Gateway]
    B --> C[Authentication]
    C --> D[Service Router]
    D --> E[Service Execution]
    E --> F[Response Assembly]
    F --> G[Client Response]
    
    H[Monitoring] --> B
    H --> C
    H --> D
    H --> E
    H --> F
    H --> G
```

### Business Rules

#### Service Rules
- **Availability**: Services must meet 99.9% availability requirements
- **Performance**: API responses must complete within specified SLAs
- **Security**: All external access must be authenticated and authorized
- **Scalability**: Services must scale horizontally to meet demand

#### Job Management Rules
- **Job Isolation**: Jobs must be isolated from each other
- **Resource Limits**: Jobs must respect resource allocation limits
- **Error Handling**: Failed jobs must be handled gracefully
- **Audit Trail**: All job operations must be audited

---

## Information Architecture

### Data Models

#### Core Service Models

```mermaid
classDiagram
    class ETLService {
        -config_: shared_ptr~ConfigurationManager~
        -pipeline_manager_: shared_ptr~PipelineManager~
        -job_results_: unordered_map~string, ETLJobResult~
        -results_mutex_: mutex
        +create_job(request)
        +get_job_result(job_id)
        +cancel_job(job_id)
        +pause_job(job_id)
        +resume_job(job_id)
    }
    
    class ETLJobRequest {
        +name: string
        +description: string
        +source_table: string
        +target_table: string
        +extractor_type: string
        +loader_type: string
        +extractor_config: unordered_map~string, any~
        +loader_config: unordered_map~string, any~
        +pipeline_config: PipelineConfig
        +dry_run: bool
        +scheduled_time: optional~time_point~
    }
    
    class ETLJobResult {
        +job_id: string
        +status: JobStatus
        +start_time: time_point
        +end_time: time_point
        +total_records: size_t
        +processed_records: size_t
        +error_records: size_t
        +errors: vector~string~
        +metrics: unordered_map~string, any~
    }
    
    class ETLScheduler {
        -etl_service_: shared_ptr~ETLService~
        -scheduled_jobs_: unordered_map~string, ScheduledJob~
        -scheduler_thread_: thread
        -running_: atomic~bool~
        +start()
        +stop()
        +schedule_job(job_id, request, schedule)
        +cancel_scheduled_job(job_id)
    }
    
    class ETLMonitor {
        -etl_service_: shared_ptr~ETLService~
        -alerts_: vector~Alert~
        -monitor_thread_: thread
        -running_: atomic~bool~
        +start()
        +stop()
        +get_alerts()
        +clear_alerts()
    }
    
    class ServiceOrchestrator {
        -services_: unordered_map~string, ServiceEndpoint~
        -circuit_breakers_: unordered_map~string, CircuitBreaker~
        -load_balancers_: unordered_map~string, LoadBalancer~
        +register_service(name, endpoint)
        +call_service(name, request)
        +get_service_health(name)
        +get_all_services()
    }
    
    class ApiService {
        -etl_service_: shared_ptr~ETLService~
        -routes_: unordered_map~string, RouteHandler~
        -middleware_: vector~Middleware~
        +register_routes()
        +handle_request(request)
        +add_middleware(middleware)
    }
    
    ETLService --> ETLJobRequest
    ETLService --> ETLJobResult
    ETLService --> ETLScheduler
    ETLService --> ETLMonitor
    ServiceOrchestrator --> ApiService
```

#### API Data Models

```mermaid
classDiagram
    class HttpRequest {
        +method: HttpMethod
        +path: string
        +query_string: string
        +headers: unordered_map~string, string~
        +params: unordered_map~string, string~
        +query_params: unordered_map~string, string~
        +body: string
        +json_body: json
    }
    
    class HttpResponse {
        +status_code: int
        +headers: unordered_map~string, string~
        +body: string
        +set_json(data)
        +set_error(status, message, details)
    }
    
    class JobConfig {
        +name: string
        +source_table: string
        +target_table: string
        +extractor_type: string
        +loader_type: string
        +extractor_params: json
        +loader_params: json
        +pipeline_config: json
    }
    
    class ApiServerConfig {
        +host: string
        +port: int
        +threads: int
        +max_connections: int
        +request_timeout: chrono::seconds
        +enable_cors: bool
        +enable_compression: bool
    }
    
    HttpRequest --> HttpResponse
    JobConfig --> HttpRequest
    ApiServerConfig --> HttpRequest
```

### Information Flow

#### Service Request Flow

```mermaid
graph TD
    A[Client Request] --> B[API Gateway]
    B --> C[Authentication]
    C --> D[Rate Limiting]
    D --> E[Request Routing]
    E --> F[Service Execution]
    F --> G[Response Processing]
    G --> H[Response Delivery]
    
    I[Configuration] --> B
    I --> C
    I --> D
    I --> E
    I --> F
    I --> G
    I --> H
    
    J[Monitoring] --> B
    J --> C
    J --> D
    J --> E
    J --> F
    J --> G
    J --> H
```

#### Job Execution Flow

```mermaid
graph TD
    A[Job Request] --> B[Request Validation]
    B --> C[Job Creation]
    C --> D[Resource Allocation]
    D --> E[Pipeline Building]
    E --> F[Pipeline Execution]
    F --> G[Progress Monitoring]
    G --> H[Result Collection]
    H --> I[Job Completion]
    
    J[Error Handler] --> B
    J --> C
    J --> D
    J --> E
    J --> F
    J --> G
    J --> H
    J --> I
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Service Layer Components"
        A[ETL Service] --> B[Job Manager]
        A --> C[Pipeline Manager]
        A --> D[Scheduler]
        A --> E[Monitor]
        
        F[API Service] --> G[REST Server]
        F --> H[gRPC Server]
        F --> I[OpenAPI Generator]
        
        J[Service Orchestrator] --> K[Service Discovery]
        J --> L[Load Balancer]
        J --> M[Circuit Breaker]
        
        N[Microservice Interfaces] --> O[Extract Service]
        N --> P[Transform Service]
        N --> Q[Load Service]
    end
    
    subgraph "Interface Layer"
        R[ETLService]
        S[ApiService]
        T[ServiceOrchestrator]
        U[MicroserviceInterfaces]
    end
    
    A --> R
    F --> S
    J --> T
    N --> U
```

### Implemented Services

#### 1. ETL Service (`src/lib/service/etl_service.h/cpp`)

**Features:**
- **Job Management**: Complete job lifecycle management
- **Pipeline Orchestration**: Coordination of ETL pipeline execution
- **Progress Tracking**: Real-time job progress monitoring
- **Error Handling**: Comprehensive error handling and recovery
- **Scheduling**: Job scheduling and execution management

**Configuration:**
```yaml
etl_service:
  max_concurrent_jobs: 10
  job_timeout_seconds: 3600
  enable_scheduling: true
  enable_monitoring: true
  error_threshold: 0.01
  retry_attempts: 3
  retry_delay_seconds: 30
```

**Usage Example:**
```cpp
auto etl_service = std::make_shared<ETLService>(config, pipeline_manager);

// Create job
ETLJobRequest request;
request.name = "person_etl";
request.source_table = "patients";
request.target_table = "person";
request.extractor_type = "database";
request.loader_type = "omop_database";

std::string job_id = etl_service->create_job(request);

// Get job status
auto result = etl_service->get_job_result(job_id);
if (result) {
    std::cout << "Job status: " << static_cast<int>(result->status) << std::endl;
}
```

#### 2. API Service (`src/app/api/api_service.h/cpp`)

**Features:**
- **REST API**: Comprehensive REST API endpoints
- **gRPC Support**: High-performance gRPC interfaces
- **OpenAPI Generation**: Automatic OpenAPI specification generation
- **Middleware Support**: Extensible middleware architecture
- **Error Handling**: Standardized error responses

**REST Endpoints:**
```cpp
// Job Management
POST   /api/v1/jobs                    // Create job
GET    /api/v1/jobs                    // List jobs
GET    /api/v1/jobs/{job_id}           // Get job status
DELETE /api/v1/jobs/{job_id}           // Cancel job
POST   /api/v1/jobs/{job_id}/pause     // Pause job
POST   /api/v1/jobs/{job_id}/resume    // Resume job

// Configuration Management
GET    /api/v1/config                  // Get configuration
PUT    /api/v1/config                  // Update configuration
POST   /api/v1/config/validate         // Validate configuration

// Vocabulary Operations
GET    /api/v1/vocabularies            // List vocabularies
GET    /api/v1/vocabularies/{id}       // Get vocabulary
POST   /api/v1/vocabularies/search     // Search concepts
GET    /api/v1/concepts/{id}           // Get concept

// Health and Monitoring
GET    /api/v1/health                  // Health check
GET    /api/v1/metrics                 // Get metrics
GET    /api/v1/version                 // Get version
```

**Configuration:**
```yaml
api_service:
  host: "0.0.0.0"
  port: 8080
  threads: 4
  max_connections: 1000
  request_timeout_seconds: 300
  enable_cors: true
  enable_compression: true
  
  authentication:
    enabled: true
    jwt_secret: "${JWT_SECRET}"
    token_expiry_hours: 24
  
  rate_limiting:
    enabled: true
    requests_per_minute: 1000
    burst_size: 100
```

#### 3. Service Orchestrator (`src/lib/service/service_orchestrator.h/cpp`)

**Features:**
- **Service Discovery**: Automatic service discovery and registration
- **Load Balancing**: Intelligent load balancing across service instances
- **Circuit Breaker**: Fault tolerance and resilience patterns
- **Health Monitoring**: Service health monitoring and reporting
- **Configuration Management**: Dynamic service configuration

**Configuration:**
```yaml
service_orchestrator:
  discovery:
    enabled: true
    service_registry: "consul"
    health_check_interval: 30
  
  load_balancing:
    strategy: "round_robin"
    health_check_enabled: true
    failover_enabled: true
  
  circuit_breaker:
    enabled: true
    failure_threshold: 5
    recovery_timeout: 60
    half_open_state: true
  
  services:
    extract:
      endpoint: "localhost:50051"
      timeout_seconds: 300
      retry_attempts: 3
    
    transform:
      endpoint: "localhost:50052"
      timeout_seconds: 300
      retry_attempts: 3
    
    load:
      endpoint: "localhost:50053"
      timeout_seconds: 300
      retry_attempts: 3
```

#### 4. Microservice Interfaces (`src/lib/service/microservice_interfaces.h/cpp`)

**Features:**
- **gRPC Communication**: High-performance inter-service communication
- **Service Abstraction**: Unified interface for microservices
- **Error Handling**: Standardized error handling across services
- **Performance Optimization**: Optimized for high-throughput operations

**Service Definitions:**
```protobuf
service ExtractService {
  rpc ExtractData(ExtractRequest) returns (ExtractResponse);
  rpc GetExtractStatus(StatusRequest) returns (StatusResponse);
  rpc CancelExtract(CancelRequest) returns (CancelResponse);
}

service TransformService {
  rpc TransformData(TransformRequest) returns (TransformResponse);
  rpc GetTransformStatus(StatusRequest) returns (StatusResponse);
  rpc CancelTransform(CancelRequest) returns (CancelResponse);
}

service LoadService {
  rpc LoadData(LoadRequest) returns (LoadResponse);
  rpc GetLoadStatus(StatusRequest) returns (StatusResponse);
  rpc CancelLoad(CancelRequest) returns (CancelResponse);
}
```

**Usage Example:**
```cpp
auto orchestrator = std::make_shared<ServiceOrchestrator>();

// Register services
orchestrator->register_service("extract", "localhost:50051");
orchestrator->register_service("transform", "localhost:50052");
orchestrator->register_service("load", "localhost:50053");

// Call service
auto response = orchestrator->call_service("extract", extract_request);
```

#### 5. Individual Microservices

##### Extract Service (`src/lib/service/extract_service.h/cpp`)
```cpp
class ExtractService {
public:
    ExtractResponse extract_data(const ExtractRequest& request);
    StatusResponse get_status(const StatusRequest& request);
    CancelResponse cancel_extract(const CancelRequest& request);
    
private:
    std::shared_ptr<ExtractorFactory> extractor_factory_;
    std::shared_ptr<ConfigurationManager> config_manager_;
};
```

##### Transform Service (`src/lib/service/transform_service.h/cpp`)
```cpp
class TransformService {
public:
    TransformResponse transform_data(const TransformRequest& request);
    StatusResponse get_status(const StatusRequest& request);
    CancelResponse cancel_transform(const CancelRequest& request);
    
private:
    std::shared_ptr<TransformationEngine> transformation_engine_;
    std::shared_ptr<VocabularyService> vocabulary_service_;
};
```

##### Load Service (`src/lib/service/load_service.h/cpp`)
```cpp
class LoadService {
public:
    LoadResponse load_data(const LoadRequest& request);
    StatusResponse get_status(const StatusRequest& request);
    CancelResponse cancel_load(const CancelRequest& request);
    
private:
    std::shared_ptr<DatabaseLoader> database_loader_;
    std::shared_ptr<BatchLoader> batch_loader_;
};
```

### Job Management

#### Job Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Created
    Created --> Scheduled
    Scheduled --> Running
    Running --> Paused
    Paused --> Running
    Running --> Completed
    Running --> Failed
    Running --> Cancelled
    Failed --> [*]
    Completed --> [*]
    Cancelled --> [*]
```

#### Job Scheduling

**Scheduler Features:**
```cpp
class ETLScheduler {
public:
    // Schedule job with cron expression
    void schedule_job(const std::string& job_id,
                     const ETLJobRequest& request,
                     const std::string& cron_expression);
    
    // Schedule job with interval
    void schedule_job(const std::string& job_id,
                     const ETLJobRequest& request,
                     std::chrono::minutes interval);
    
    // Cancel scheduled job
    bool cancel_scheduled_job(const std::string& job_id);
    
    // Get scheduled jobs
    std::vector<ScheduledJob> get_scheduled_jobs() const;
};
```

**Scheduling Configuration:**
```yaml
scheduler:
  enabled: true
  max_scheduled_jobs: 100
  timezone: "UTC"
  
  default_schedule:
    retry_attempts: 3
    retry_delay_seconds: 300
    timeout_seconds: 3600
  
  cron_expressions:
    daily: "0 2 * * *"
    weekly: "0 2 * * 0"
    monthly: "0 2 1 * *"
```

### Monitoring and Alerting

#### Monitoring Features

**Monitor Implementation:**
```cpp
class ETLMonitor {
public:
    // Start monitoring
    void start();
    
    // Stop monitoring
    void stop();
    
    // Get current alerts
    std::vector<Alert> get_alerts() const;
    
    // Clear alerts
    void clear_alerts();
    
    // Set alert thresholds
    void set_thresholds(double error_rate_threshold = 0.05,
                       size_t memory_threshold_mb = 1024,
                       double cpu_threshold_percent = 80.0);
};
```

**Alert Types:**
```cpp
enum class AlertType {
    JobFailed,
    JobTimeout,
    HighErrorRate,
    HighMemoryUsage,
    HighCPUUsage,
    ServiceUnavailable,
    ConfigurationError
};

struct Alert {
    AlertType type;
    std::string job_id;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    std::unordered_map<std::string, std::any> details;
};
```

**Monitoring Configuration:**
```yaml
monitoring:
  enabled: true
  check_interval_seconds: 30
  
  thresholds:
    error_rate: 0.05
    memory_usage_mb: 1024
    cpu_usage_percent: 80.0
    job_timeout_seconds: 3600
  
  alerting:
    enabled: true
    email_notifications: true
    webhook_url: "${ALERT_WEBHOOK_URL}"
    slack_webhook: "${SLACK_WEBHOOK_URL}"
```

### Error Handling and Recovery

#### Exception Hierarchy

```mermaid
classDiagram
    class ServiceException {
        <<abstract>>
        +string service_name
        +string operation
        +string error_details
        +what()
    }
    
    class JobException {
        +string job_id
        +JobStatus status
        +string error_message
        +chrono::time_point timestamp
    }
    
    class ApiException {
        +int status_code
        +string endpoint
        +string request_id
        +string error_code
    }
    
    class OrchestrationException {
        +string service_name
        +string operation
        +string circuit_breaker_state
        +int retry_count
    }
    
    class ConfigurationException {
        +string config_key
        +string config_value
        +string validation_error
    }
    
    ServiceException <|-- JobException
    ServiceException <|-- ApiException
    ServiceException <|-- OrchestrationException
    ServiceException <|-- ConfigurationException
```

#### Error Recovery Strategies

1. **Circuit Breaker**: Automatic service isolation on failures
2. **Retry with Backoff**: Exponential backoff for transient errors
3. **Graceful Degradation**: Continue operation with reduced functionality
4. **Fallback Services**: Use alternative services when primary fails
5. **Error Logging**: Comprehensive error reporting for troubleshooting

### Performance Optimization

#### API Performance

- **Connection Pooling**: Efficient HTTP connection management
- **Response Caching**: Cache frequently requested data
- **Compression**: Gzip compression for large responses
- **Async Processing**: Asynchronous request processing

#### Service Performance

- **Load Balancing**: Distribute load across service instances
- **Connection Pooling**: Reuse database and service connections
- **Caching**: Multi-level caching for performance
- **Parallel Processing**: Concurrent job execution

---

## Cross-Cutting Concerns

### Configuration Management

The Service Architecture integrates with the Common Library's configuration system to provide:

- **YAML-based Configuration**: Human-readable service configuration
- **Environment Variable Substitution**: Secure parameter management
- **Configuration Validation**: Schema and semantic validation
- **Hot Reloading**: Runtime configuration updates

### Logging and Monitoring

- **Structured Logging**: JSON-formatted logs with context information
- **Performance Metrics**: Detailed timing and throughput measurements
- **Service Tracking**: Complete audit trail of service operations
- **Health Metrics**: Service health measurement and reporting

### Security

- **Authentication**: JWT-based authentication for API access
- **Authorization**: Role-based access control for service operations
- **Encryption**: TLS/SSL for all external communications
- **Audit Logging**: Comprehensive audit trail for compliance

---

## Deployment and Operations

### Build System

The Service Architecture uses CMake for build management with the following structure:

```
src/lib/service/
├── CMakeLists.txt
├── etl_service.h/cpp
├── extract_service.h/cpp
├── transform_service.h/cpp
├── load_service.h/cpp
├── service_orchestrator.h/cpp
└── microservice_interfaces.h/cpp

src/app/api/
├── CMakeLists.txt
├── api_service.h/cpp
├── rest_server.h
├── grpc_service.h
├── microservice_main.cpp
└── grpc/
    └── omop_etl.grpc.pb.h
```

### Dependencies

**Core Dependencies:**
- **yaml-cpp**: YAML configuration parsing
- **nlohmann/json**: JSON data handling
- **spdlog**: Structured logging framework
- **grpc**: gRPC communication framework
- **protobuf**: Protocol buffer serialization

**System Dependencies:**
- **C++17**: Modern C++ features and standard library
- **Threading**: Standard C++ threading support
- **Network**: Network communication libraries

### Testing Strategy

- **Unit Tests**: Component-level testing with mock objects
- **Integration Tests**: End-to-end service testing
- **Performance Tests**: Throughput and scalability validation
- **Stress Tests**: Error condition and recovery testing

### Monitoring and Observability

- **Metrics Collection**: Performance and error rate monitoring
- **Health Checks**: Service availability verification
- **Distributed Tracing**: Request flow tracking across services
- **Alerting**: Automated notification of issues and anomalies

### Deployment Models

#### Monolithic Deployment
- Single application instance with all services
- Simplified deployment and management
- Shared memory and resource utilization

#### Microservices Deployment
- Separate services for different ETL stages
- Independent scaling and deployment
- Network-based inter-service communication

### Operational Considerations

#### Resource Management
- **Memory Limits**: Configurable memory usage limits
- **CPU Affinity**: Thread-to-core binding for performance
- **I/O Optimization**: Asynchronous I/O operations
- **Connection Pooling**: Service connection management

#### Fault Tolerance
- **Circuit Breakers**: Prevents cascade failures
- **Retry Mechanisms**: Automatic recovery from transient errors
- **Graceful Degradation**: Continues operation with reduced functionality
- **Data Consistency**: Transaction-based data integrity

#### Scalability
- **Horizontal Scaling**: Multiple service instances
- **Vertical Scaling**: Resource allocation optimization
- **Load Balancing**: Distribution of processing load
- **Caching**: Frequently accessed data caching

---

## Security and Compliance

### Data Protection

- **Encryption in Transit**: TLS/SSL for all external communications
- **Encryption at Rest**: Support for encrypted data storage
- **Credential Security**: Secure storage and transmission of credentials
- **Access Control**: Role-based permissions for service operations

### Compliance

- **HIPAA Compliance**: Healthcare data protection standards
- **GDPR Compliance**: European data protection regulations
- **Audit Logging**: Comprehensive audit trail for compliance
- **Data Retention**: Configurable data retention policies

### Security Best Practices

- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Principle of Least Privilege**: Minimal required permissions
- **Secure Configuration**: Encrypted configuration management
- **Regular Security Updates**: Timely security patch management

## Conclusion

The OMOP Service Architecture provides a robust, scalable, and extensible foundation for ETL service orchestration. Its modular architecture, comprehensive error handling, and performance optimizations make it suitable for both small-scale deployments and enterprise-wide implementations. The flexible deployment model ensures the framework can evolve to meet changing requirements while maintaining stability and performance. 