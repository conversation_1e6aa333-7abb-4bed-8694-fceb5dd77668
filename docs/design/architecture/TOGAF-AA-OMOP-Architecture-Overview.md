# OMOP ETL Pipeline Architecture

## Overview

The OMOP ETL Pipeline is designed as a modular, extensible framework for transforming healthcare data into the OMOP Common Data Model format. The architecture emphasizes separation of concerns, plugin-based extensibility, and high performance through parallel processing capabilities. The system is implemented in C++ with support for both CLI and REST API interfaces, microservices architecture, and comprehensive monitoring.

## Design Principles

### Modularity
The system is decomposed into discrete components with well-defined interfaces. Each component handles a specific aspect of the ETL process, allowing for independent development, testing, and deployment.

### Extensibility
New data sources, transformation rules, and target formats can be added through a plugin architecture without modifying the core framework. All major components implement standard interfaces defined in the core module.

### Performance
The pipeline supports parallel processing at multiple levels, including concurrent extraction from multiple sources, parallel transformation of record batches, and bulk loading operations. Memory usage is optimized through streaming processing and configurable batch sizes.

### Reliability
Comprehensive error handling ensures graceful degradation and recovery. The system supports transaction management, automatic retry mechanisms, and detailed error reporting for troubleshooting.

### Configurability
YAML-based configuration files allow users to define complex mappings and transformations without code changes. The configuration system supports environment variable substitution and validation.

## System Architecture

### Layered Architecture

The system follows a layered architecture pattern with clear separation between different levels of abstraction:

```
┌─────────────────────────────────────────────────────────────┐
│                     Application Layer                        │
│                  (CLI and REST API Services)                 │
├─────────────────────────────────────────────────────────────┤
│                      Service Layer                           │
│              (High-level ETL Orchestration)                  │
├─────────────────────────────────────────────────────────────┤
│                       Core Layer                             │
│            (Pipeline Engine and Interfaces)                  │
├─────────────────────────────────────────────────────────────┤
│                    Component Layer                           │
│        (Extractors, Transformers, and Loaders)              │
├─────────────────────────────────────────────────────────────┤
│                     Common Layer                             │
│      (Shared Utilities, Configuration, and Logging)          │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

#### Pipeline Engine (`src/lib/core/pipeline.h/cpp`)
The Pipeline Engine orchestrates the entire ETL process. It manages component lifecycle, coordinates data flow between stages, and handles error recovery. The engine supports both sequential and parallel execution modes with configurable batch sizes and queue management.

**Key Features:**
- Multi-threaded pipeline execution with separate worker threads for extract, transform, and load stages
- Configurable batch processing with queue-based data flow
- Progress tracking and error threshold monitoring
- Checkpoint and resume capabilities
- Pipeline builder pattern for fluent configuration

#### Extractors (`src/lib/extract/`)
Extractors are responsible for reading data from various sources. Each extractor implements the `IExtractor` interface and handles source-specific connection management, schema discovery, and data retrieval.

**Implemented Extractors:**
- **CSV Extractor** (`csv_extractor.h/cpp`): Handles CSV file extraction with support for compressed files
- **JSON Extractor** (`json_extractor.h/cpp`): Processes JSON documents and arrays
- **Database Extractors**: 
  - PostgreSQL (`postgresql_connector.h/cpp`)
  - MySQL (`mysql_connector.h/cpp`)
  - ODBC (`odbc_connector.h/cpp`)
- **Compressed CSV Extractor** (`compressed_csv_extractor.cpp`): Handles gzip/bzip2 compressed CSV files

#### Transformers (`src/lib/transform/`)
Transformers apply business logic to convert source data into the target OMOP CDM format. They support field mapping, data type conversion, vocabulary lookups, custom functions, and validation rules.

**Key Components:**
- **Transformation Engine** (`transformation_engine.h/cpp`): Core transformation orchestration
- **Vocabulary Service** (`vocabulary_service.h/cpp`): Concept mapping and vocabulary lookups
- **Field Transformations** (`field_transformations.h/cpp`): Basic field-level transformations
- **String Transformations** (`string_transformations.h/cpp`): String manipulation and formatting
- **Date Transformations** (`date_transformations.h/cpp`): Date parsing and formatting
- **Numeric Transformations** (`numeric_transformations.h/cpp`): Numeric value processing
- **Custom Transformations** (`custom_transformations.h/cpp`): User-defined transformation logic
- **Conditional Transformations** (`conditional_transformations.h/cpp`): Rule-based transformations
- **Validation Engine** (`validation_engine.h/cpp`): Data validation and quality checks

#### Loaders (`src/lib/load/`)
Loaders write transformed data to target destinations. They implement the `ILoader` interface and handle connection management, transaction control, and bulk insert operations.

**Implemented Loaders:**
- **Database Loader** (`database_loader.h/cpp`): Primary database loading with support for PostgreSQL and MySQL
- **Batch Loader** (`batch_loader.h/cpp`): Optimized batch insertion with configurable commit intervals
- **Batch Inserter** (`batch_inserter.h/cpp`): Low-level batch insertion utilities
- **Additional Loaders** (`additional_loaders.h/cpp`): Specialized loaders for specific use cases

#### Vocabulary Service
The Vocabulary Service provides concept mapping functionality required for OMOP CDM compliance. It maintains a cache of vocabulary mappings and supports real-time lookups during transformation.

### Service Architecture

#### ETL Service (`src/lib/service/etl_service.h/cpp`)
High-level service that manages ETL operations, job lifecycle, and coordination between components.

**Features:**
- Job creation and management
- Pipeline orchestration
- Progress tracking and monitoring
- Error handling and recovery
- Scheduling capabilities

#### API Service (`src/app/api/api_service.h/cpp`)
REST API service providing HTTP endpoints for ETL operations, configuration management, and monitoring.

**Key Endpoints:**
- Job management (create, get, list, cancel, pause, resume)
- Configuration management (get, update, validate)
- Vocabulary operations (search, get concepts, create mappings)
- Health checks and metrics
- OpenAPI specification generation

#### Service Orchestrator (`src/lib/service/service_orchestrator.h/cpp`)
Coordinates microservices communication and manages service discovery in distributed deployments.

### Data Flow

The ETL pipeline processes data through the following stages:

1. **Extraction**: Data is read from source systems in configurable batches
2. **Transformation**: Each batch is processed through transformation rules
3. **Validation**: Transformed data is validated against OMOP CDM constraints
4. **Loading**: Valid records are loaded into the target database

### Concurrency Model

The pipeline supports three levels of concurrency:

1. **Component-level**: Multiple extractors can run simultaneously
2. **Batch-level**: Record batches can be processed in parallel
3. **Thread-pool**: Configurable worker threads handle processing tasks

Thread safety is ensured through lock-free data structures where possible and fine-grained locking where necessary.

## Configuration System

### Configuration Hierarchy

Configuration is organized in a hierarchical structure:

```yaml
# Global configuration
global:
  batch_size: 10000
  thread_count: 8
  error_handling:
    skip_on_error: true
    max_errors: 100

# Source configuration
sources:
  - name: clinical_db
    type: postgresql
    connection:
      host: ${DB_HOST}
      port: ${DB_PORT}
      database: ${DB_NAME}

# Target configuration
target:
  type: postgresql
  schema: omop_cdm

# Mapping configuration
mappings:
  - source: patients
    target: person
    transformations: [...]
```

### Environment Variable Substitution

Configuration files support environment variable substitution using the `${VAR_NAME}` syntax, allowing for secure credential management and environment-specific settings.

### Validation

Configuration is validated at multiple levels including schema validation, semantic validation, and runtime validation. Validation errors provide detailed feedback about configuration issues.

## Error Handling

### Exception Hierarchy

The system uses a comprehensive exception hierarchy rooted in `OmopException`:

- `ConfigurationException`: Configuration-related errors
- `DatabaseException`: Database connectivity and query errors
- `ExtractionException`: Source data access errors
- `TransformationException`: Data transformation errors
- `LoadException`: Target data writing errors
- `ValidationException`: Data validation failures

### Error Recovery

The pipeline implements several error recovery strategies:

1. **Retry with backoff**: Transient errors trigger automatic retries
2. **Skip and continue**: Invalid records can be skipped
3. **Checkpoint and resume**: Pipeline state can be persisted
4. **Rollback**: Database transactions ensure data consistency

### Error Reporting

Errors are reported through multiple channels including structured logging, error callbacks, and API endpoints. Each error includes context information such as record identifiers and field values.

## Performance Optimization

### Memory Management

- Streaming processing minimizes memory footprint
- Configurable batch sizes balance memory usage and performance
- Object pooling reduces allocation overhead
- Smart pointers ensure proper resource cleanup

### Database Optimization

- Prepared statements reduce parsing overhead
- Bulk insert operations minimize round trips
- Connection pooling manages database resources
- Index-aware loading improves write performance

### Parallel Processing

- Work stealing queues balance load across threads
- Lock-free data structures minimize contention
- Batch partitioning enables parallel transformation
- Asynchronous I/O overlaps computation and data transfer

## Security Considerations

### Authentication and Authorization

The REST API implements JWT-based authentication with role-based access control. API keys can be used for service-to-service communication.

**Security Components:**
- **Auth Manager** (`src/lib/security/auth_manager.h`): Authentication and session management
- **Authorization** (`src/lib/security/authorization.h`): Role-based access control
- **Audit Logger** (`src/lib/security/audit_logger.h`): Comprehensive audit trail

### Data Protection

- Credentials are never logged or stored in plain text
- TLS encryption protects data in transit
- Audit logging tracks all data access
- PII handling complies with healthcare regulations

### Input Validation

All external inputs are validated to prevent injection attacks and ensure data integrity. The validation framework supports custom rules for healthcare-specific requirements.

## Monitoring and Observability

### Metrics

The system exposes metrics through:
- Record processing rates
- Error rates by component
- Resource utilization
- Pipeline latency

### Logging

Structured logging provides detailed operational visibility:
- Debug logs for development
- Info logs for normal operation
- Warning logs for recoverable issues
- Error logs for failures

### Health Checks

Each component implements health checks that verify:
- Database connectivity
- File system access
- Memory availability
- Configuration validity

## Machine Learning Integration

### Medical Term Classification (`src/lib/ml/medical_term_classifier.h/cpp`)
The system includes machine learning capabilities for medical term classification and concept mapping, supporting automated vocabulary mapping and data quality assessment.

## Deployment Architecture

### Container Deployment

The application is designed for container deployment with:
- Multi-stage Docker builds
- Non-root user execution
- Health check endpoints
- Graceful shutdown handling

### Scalability

The system scales through:
- Horizontal scaling of API instances
- Parallel pipeline execution
- Database connection pooling
- Caching of frequently accessed data

### High Availability

High availability is achieved through:
- Stateless API design
- Database failover support
- Retry mechanisms
- Circuit breakers for external services

## Microservices Support

The system supports both monolithic and microservices deployment modes:

### Orchestrator Mode
- Single application instance manages all ETL operations
- Internal component communication
- Simplified deployment and management

### Microservices Mode
- Separate services for extract, transform, and load operations
- gRPC-based inter-service communication
- Independent scaling and deployment
- Service discovery and load balancing

## Future Enhancements

### Planned Features

1. **Real-time Processing**: Support for streaming data sources
2. **Enhanced ML Integration**: Automated data quality assessment and anomaly detection
3. **Cloud Native Features**: Kubernetes operators and auto-scaling
4. **Additional Formats**: Support for HL7 FHIR and other healthcare standards

### Extension Points

The architecture provides extension points for:
- Custom extractors for proprietary systems
- Domain-specific transformations
- Alternative storage backends
- Custom validation rules

## Conclusion

The OMOP ETL Pipeline architecture provides a robust foundation for healthcare data transformation. Its modular design, comprehensive error handling, and performance optimizations make it suitable for both small-scale deployments and enterprise-wide implementations. The plugin-based architecture ensures the system can evolve to meet changing requirements while maintaining stability and performance.