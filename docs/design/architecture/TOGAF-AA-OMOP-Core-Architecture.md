# OMOP Core Library Architecture

## Executive Summary

The OMOP Core Library provides the fundamental ETL pipeline engine and orchestration framework that drives the entire OMOP data transformation process. This library implements the core ETL patterns, pipeline management, job scheduling, and component lifecycle management. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)

---

## Business Architecture

### Business Context and Objectives

The Core Library serves as the central orchestration engine that enables:

- **ETL Pipeline Orchestration**: Coordinated execution of extract, transform, and load operations
- **Job Management**: Comprehensive job lifecycle management with scheduling and monitoring
- **Component Integration**: Seamless integration of extractors, transformers, and loaders
- **Scalability**: Parallel processing and resource optimization for large-scale data operations
- **Reliability**: Fault tolerance, error recovery, and checkpoint mechanisms

### Business Capabilities

```mermaid
graph TB
    subgraph "Core Library Business Capabilities"
        A[Pipeline Orchestration] --> B[ETL Execution]
        A --> C[Job Management]
        A --> D[Component Integration]
        A --> E[Resource Management]
        A --> F[Error Handling]
        
        B --> G[Data Processing]
        C --> H[Job Scheduling]
        D --> I[Plugin Architecture]
        E --> J[Performance Optimization]
        F --> K[Fault Tolerance]
    end
    
    subgraph "Business Stakeholders"
        L[Data Engineers]
        M[System Architects]
        N[Operations Teams]
        O[Business Analysts]
        P[Quality Assurance]
    end
    
    G --> L
    H --> M
    I --> N
    J --> O
    K --> P
```

### Business Processes

#### ETL Pipeline Execution Workflow

```mermaid
sequenceDiagram
    participant Job as Job Manager
    participant Pipeline as ETL Pipeline
    participant Extractor as Data Extractor
    participant Transformer as Data Transformer
    participant Loader as Data Loader
    participant Context as Processing Context
    
    Job->>Pipeline: Start ETL Job
    Pipeline->>Context: Initialize Context
    Pipeline->>Extractor: Extract Data
    Extractor->>Context: Update Progress
    Pipeline->>Transformer: Transform Data
    Transformer->>Context: Update Progress
    Pipeline->>Loader: Load Data
    Loader->>Context: Update Progress
    Pipeline->>Job: Complete Job
    
    Note over Context: Thread-safe progress tracking
    Note over Context: Error threshold monitoring
    Note over Context: Performance metrics collection
```

### Business Rules and Constraints

- **Pipeline Integrity**: ETL pipelines must maintain data consistency across all stages
- **Error Thresholds**: Configurable error thresholds with automatic job termination
- **Resource Limits**: Memory and CPU usage must be monitored and controlled
- **Checkpointing**: Long-running jobs must support checkpoint and resume capabilities
- **Parallel Processing**: Pipeline must support configurable parallel processing levels

---

## Information Architecture

### Data Models

#### Core Data Structures

```mermaid
classDiagram
    class IExtractor {
        <<interface>>
        +initialize(config, context)
        +extract_batch(batch_size, context)
        +has_more_data()
        +get_statistics()
        +finalize(context)
    }
    
    class ITransformer {
        <<interface>>
        +initialize(config, context)
        +transform(record, context)
        +validate(record)
        +get_statistics()
        +finalize(context)
    }
    
    class ILoader {
        <<interface>>
        +initialize(config, context)
        +load(record, context)
        +load_batch(batch, context)
        +get_statistics()
        +finalize(context)
    }
    
    class ProcessingContext {
        +Stage current_stage
        +string job_id
        +size_t processed_count
        +size_t error_count
        +set_stage(stage)
        +increment_processed(count)
        +increment_errors(count)
        +log(level, message)
    }
    
    class Record {
        +getField(name)
        +setField(name, value)
        +getFieldNames()
    }
    
    class RecordBatch {
        +vector<Record> records
        +size_t size()
        +empty()
        +push_back(record)
    }
    
    IExtractor --> ProcessingContext
    ITransformer --> ProcessingContext
    ILoader --> ProcessingContext
    IExtractor --> RecordBatch
    ITransformer --> Record
    ILoader --> Record
```

#### Pipeline Configuration Model

```mermaid
classDiagram
    class PipelineConfig {
        +size_t batch_size
        +size_t max_parallel_batches
        +size_t queue_size
        +size_t commit_interval
        +double error_threshold
        +bool stop_on_error
        +bool validate_records
        +bool enable_checkpointing
        +chrono::seconds checkpoint_interval
        +string checkpoint_dir
    }
    
    class JobInfo {
        +string job_id
        +string job_name
        +JobStatus status
        +chrono::system_clock::time_point start_time
        +chrono::system_clock::time_point end_time
        +size_t total_records
        +size_t processed_records
        +size_t error_records
        +vector<string> error_messages
        +unordered_map<string, any> metadata
        +duration()
        +progress()
        +error_rate()
    }
    
    class JobStatus {
        <<enumeration>>
        Created
        Initializing
        Running
        Paused
        Completed
        Failed
        Cancelled
    }
    
    PipelineConfig --> JobInfo
    JobInfo --> JobStatus
```

### Information Flow

#### Data Processing Flow

```mermaid
graph TD
    A[Source Data] --> B[Extractor]
    B --> C[Record Batch]
    C --> D[Transformer]
    D --> E[Transformed Record]
    E --> F[Validator]
    F --> G[Validated Record]
    G --> H[Loader]
    H --> I[Target Database]
    
    J[Processing Context] --> B
    J --> D
    J --> F
    J --> H
    
    K[Error Handler] --> B
    K --> D
    K --> F
    K --> H
```

#### Configuration Flow

```mermaid
graph TD
    A[YAML Config] --> B[Configuration Manager]
    B --> C[Pipeline Builder]
    C --> D[ETL Pipeline]
    D --> E[Component Factory]
    E --> F[Extractor]
    E --> G[Transformer]
    E --> H[Loader]
    
    I[Environment Variables] --> B
    J[Command Line Args] --> B
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Core Library Components"
        A[Pipeline Engine] --> B[Component Factory]
        A --> C[Job Manager]
        A --> D[Processing Context]
        
        B --> E[Extractor Registry]
        B --> F[Transformer Registry]
        B --> G[Loader Registry]
        
        C --> H[Job Scheduler]
        C --> I[Job Monitor]
        
        D --> J[Progress Tracker]
        D --> K[Error Handler]
        D --> L[Metrics Collector]
    end
    
    subgraph "Interface Layer"
        M[IExtractor]
        N[ITransformer]
        O[ILoader]
        P[IComponent]
    end
    
    E --> M
    F --> N
    G --> O
    B --> P
```

#### Pipeline Engine Implementation

The Pipeline Engine (`src/lib/core/pipeline.h/cpp`) implements a multi-threaded architecture with the following key components:

**Worker Threads:**
- **Extraction Worker**: Continuously extracts data from source systems
- **Transformation Worker**: Processes extracted batches through transformation rules
- **Loading Worker**: Loads transformed data into target systems
- **Main Pipeline Thread**: Orchestrates overall pipeline execution

**Queue Management:**
- **Extract Queue**: Buffers extracted batches for transformation
- **Transform Queue**: Buffers transformed batches for loading
- **Condition Variables**: Coordinate thread synchronization and flow control

**Key Features:**
- Configurable batch sizes and queue limits
- Thread-safe progress tracking and error handling
- Automatic checkpoint creation and recovery
- Graceful shutdown and pause/resume capabilities

#### Component Factory System

The Component Factory (`src/lib/core/component_factory.h/cpp`) provides a plugin architecture for dynamic component registration and instantiation:

**Factory Functions:**
```cpp
// Global factory accessors
ComponentFactory<IExtractor>& get_extractor_factory();
ComponentFactory<ITransformer>& get_transformer_factory();
ComponentFactory<ILoader>& get_loader_factory();

// Component creation
std::unique_ptr<IExtractor> create_extractor(const std::string& type, const std::unordered_map<std::string, std::any>& config);
std::unique_ptr<ITransformer> create_transformer(const std::string& type, const std::unordered_map<std::string, std::any>& config);
std::unique_ptr<ILoader> create_loader(const std::string& type, const std::unordered_map<std::string, std::any>& config);

// Custom component registration
void register_extractor_type(const std::string& type, std::function<std::unique_ptr<IExtractor>()> creator);
void register_transformer_type(const std::string& type, std::function<std::unique_ptr<ITransformer>()> creator);
void register_loader_type(const std::string& type, std::function<std::unique_ptr<ILoader>()> creator);
```

#### Pipeline Builder Pattern

The Pipeline Builder (`src/lib/core/pipeline.h`) provides a fluent API for constructing ETL pipelines:

```cpp
auto pipeline = PipelineBuilder()
    .with_config(pipeline_config)
    .with_extractor("database", extractor_params)
    .with_transformer_for_table("person")
    .with_loader("omop_database", loader_params)
    .with_progress_callback(progress_handler)
    .with_error_callback(error_handler)
    .build();
```

### Interface Definitions

#### Core Interfaces

**IExtractor Interface:**
```cpp
class IExtractor {
public:
    virtual ~IExtractor() = default;
    
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;
    
    virtual RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) = 0;
    
    virtual bool has_more_data() const = 0;
    
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
    
    virtual void finalize(ProcessingContext& context) = 0;
};
```

**ITransformer Interface:**
```cpp
class ITransformer {
public:
    virtual ~ITransformer() = default;
    
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;
    
    virtual std::optional<Record> transform(const Record& record,
                                          ProcessingContext& context) = 0;
    
    virtual omop::common::ValidationResult validate(const Record& record) const = 0;
    
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
    
    virtual void finalize(ProcessingContext& context) = 0;
};
```

**ILoader Interface:**
```cpp
class ILoader {
public:
    virtual ~ILoader() = default;
    
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;
    
    virtual bool load(const Record& record, ProcessingContext& context) = 0;
    
    virtual size_t load_batch(const RecordBatch& batch, ProcessingContext& context) = 0;
    
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
    
    virtual void finalize(ProcessingContext& context) = 0;
};
```

### Processing Context

The Processing Context (`src/lib/core/interfaces.h`) provides thread-safe state management and services for ETL components:

**Key Features:**
- **Stage Management**: Tracks current processing stage (Extract, Transform, Load)
- **Progress Tracking**: Thread-safe counters for processed and error records
- **Error Threshold Monitoring**: Automatic job termination on error threshold exceed
- **Logging Integration**: Structured logging with context information
- **Data Storage**: Thread-safe storage for component-specific data

**Usage Example:**
```cpp
void extraction_worker() {
    context_.set_stage(ProcessingContext::Stage::Extract);
    
    while (extractor_->has_more_data()) {
        auto batch = extractor_->extract_batch(batch_size, context_);
        context_.increment_processed(batch.size());
        
        if (context_.is_error_threshold_exceeded()) {
            break;
        }
    }
}
```

### Job Management

#### Job Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Created
    Created --> Initializing
    Initializing --> Running
    Running --> Paused
    Paused --> Running
    Running --> Completed
    Running --> Failed
    Running --> Cancelled
    Failed --> [*]
    Completed --> [*]
    Cancelled --> [*]
```

#### Job Information Model

The JobInfo class provides comprehensive job status and metrics:

```cpp
class JobInfo {
public:
    std::string job_id;
    std::string job_name;
    JobStatus status{JobStatus::Created};
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> error_messages;
    std::unordered_map<std::string, std::any> metadata;
    
    // Computed properties
    std::chrono::duration<double> duration() const;
    double progress() const;
    double error_rate() const;
};
```

### Error Handling and Recovery

#### Exception Hierarchy

```mermaid
classDiagram
    class OmopException {
        <<abstract>>
        +string message
        +string details
        +what()
    }
    
    class ConfigurationException {
        +string config_key
        +string config_value
    }
    
    class DatabaseException {
        +string connection_string
        +string sql_query
        +int error_code
    }
    
    class ExtractionException {
        +string source_name
        +string extraction_query
    }
    
    class TransformationException {
        +string transformation_rule
        +string field_name
    }
    
    class LoadException {
        +string target_name
        +string load_operation
    }
    
    class ValidationException {
        +string validation_rule
        +string field_name
        +string actual_value
    }
    
    OmopException <|-- ConfigurationException
    OmopException <|-- DatabaseException
    OmopException <|-- ExtractionException
    OmopException <|-- TransformationException
    OmopException <|-- LoadException
    OmopException <|-- ValidationException
```

#### Error Recovery Strategies

1. **Retry with Exponential Backoff**: Transient errors trigger automatic retries
2. **Error Threshold Monitoring**: Jobs terminate when error rate exceeds threshold
3. **Graceful Degradation**: Non-critical errors allow processing to continue
4. **Checkpoint and Resume**: Long-running jobs can resume from saved state
5. **Transaction Rollback**: Database operations ensure data consistency

### Performance Optimization

#### Memory Management

- **Streaming Processing**: Minimizes memory footprint through batch processing
- **Object Pooling**: Reduces allocation overhead for frequently created objects
- **Smart Pointers**: Ensures proper resource cleanup and exception safety
- **Configurable Batch Sizes**: Balances memory usage and performance

#### Thread Safety

- **Lock-free Data Structures**: Minimizes contention in high-performance scenarios
- **Fine-grained Locking**: Uses specific locks for different data structures
- **Condition Variables**: Efficient thread synchronization and notification
- **Atomic Operations**: Thread-safe counters and flags

#### Database Optimization

- **Connection Pooling**: Reuses database connections for efficiency
- **Prepared Statements**: Reduces parsing overhead for repeated queries
- **Bulk Operations**: Minimizes round trips through batch processing
- **Transaction Management**: Optimizes commit frequency for performance

---

## Cross-Cutting Concerns

### Configuration Management

The Core Library integrates with the Common Library's configuration system (`src/lib/common/configuration.h`) to provide:

- **YAML-based Configuration**: Human-readable configuration format
- **Environment Variable Substitution**: Secure credential management
- **Configuration Validation**: Schema and semantic validation
- **Hot Reloading**: Runtime configuration updates

### Logging and Monitoring

- **Structured Logging**: JSON-formatted logs with context information
- **Performance Metrics**: Detailed timing and throughput measurements
- **Health Checks**: Component health monitoring and reporting
- **Audit Trail**: Comprehensive operation logging for compliance

### Security

- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Access Control**: Role-based permissions for pipeline operations
- **Audit Logging**: Tracks all data access and modifications
- **Secure Configuration**: Encrypted credential storage and transmission

---

## Deployment and Operations

### Build System

The Core Library uses CMake for build management with the following structure:

```
src/lib/core/
├── CMakeLists.txt
├── pipeline.h/cpp
├── interfaces.h
├── component_factory.h/cpp
└── job_manager.h/cpp
```

### Dependencies

**Core Dependencies:**
- **spdlog**: Structured logging framework
- **yaml-cpp**: YAML configuration parsing
- **nlohmann/json**: JSON data handling
- **fmt**: String formatting library

**System Dependencies:**
- **C++17**: Modern C++ features and standard library
- **Threading**: Standard C++ threading support
- **Filesystem**: C++17 filesystem library

### Testing Strategy

- **Unit Tests**: Component-level testing with mock objects
- **Integration Tests**: End-to-end pipeline testing
- **Performance Tests**: Throughput and scalability validation
- **Stress Tests**: Error condition and recovery testing

### Monitoring and Observability

- **Metrics Collection**: Performance and error rate monitoring
- **Health Checks**: Component availability verification
- **Distributed Tracing**: Request flow tracking across components
- **Alerting**: Automated notification of issues and anomalies

### Deployment Models

#### Monolithic Deployment
- Single application instance with all components
- Simplified deployment and management
- Shared memory and resource utilization

#### Microservices Deployment
- Separate services for different pipeline stages
- Independent scaling and deployment
- Network-based inter-service communication

### Operational Considerations

#### Resource Management
- **Memory Limits**: Configurable memory usage limits
- **CPU Affinity**: Thread-to-core binding for performance
- **I/O Optimization**: Asynchronous I/O operations
- **Connection Pooling**: Database connection management

#### Fault Tolerance
- **Circuit Breakers**: Prevents cascade failures
- **Retry Mechanisms**: Automatic recovery from transient errors
- **Graceful Degradation**: Continues operation with reduced functionality
- **Data Consistency**: Transaction-based data integrity

#### Scalability
- **Horizontal Scaling**: Multiple pipeline instances
- **Vertical Scaling**: Resource allocation optimization
- **Load Balancing**: Distribution of processing load
- **Caching**: Frequently accessed data caching

## Conclusion

The OMOP Core Library provides a robust, scalable, and extensible foundation for ETL pipeline orchestration. Its modular architecture, comprehensive error handling, and performance optimizations make it suitable for both small-scale deployments and enterprise-wide implementations. The plugin-based component system ensures the framework can evolve to meet changing requirements while maintaining stability and performance. 