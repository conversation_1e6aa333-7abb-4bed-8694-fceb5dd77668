# OMOP Load Library Architecture

## Executive Summary

The OMOP Load Library provides a comprehensive data loading framework that writes transformed healthcare data into OMOP CDM-compliant databases. The library implements sophisticated batch loading, transaction management, constraint handling, and performance optimization mechanisms. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)
6. [Security and Compliance](#security-and-compliance)

---

## Business Architecture

### Business Context

The Load Library serves as the data persistence layer in the OMOP ETL pipeline, enabling healthcare organizations to efficiently load transformed data into OMOP CDM-compliant databases. The library addresses the critical business need for reliable, high-performance data loading while maintaining data integrity and compliance with healthcare data standards.

### Business Capabilities

#### Core Loading Capabilities
- **Batch Loading**: Efficient bulk insertion of large datasets
- **Transaction Management**: ACID-compliant transaction handling
- **Constraint Handling**: Proper handling of database constraints and relationships
- **Error Recovery**: Robust error handling and recovery mechanisms
- **Performance Optimization**: High-throughput data loading operations

#### Data Quality Management
- **Constraint Validation**: Validation against database constraints
- **Referential Integrity**: Maintenance of referential integrity
- **Data Consistency**: Ensuring data consistency across tables
- **Quality Reporting**: Detailed loading quality reports

### Business Processes

#### Data Loading Workflow
```mermaid
graph TD
    A[Transformed Records] --> B[Batch Assembly]
    B --> C[Constraint Validation]
    C --> D[Transaction Preparation]
    D --> E[Database Insertion]
    E --> F[Commit/Rollback]
    F --> G[Error Handling]
    G --> H[Progress Reporting]
    H --> I[Continue or Stop]
```

#### Batch Processing Flow
```mermaid
graph TD
    A[Record Stream] --> B[Batch Buffer]
    B --> C{Batch Full?}
    C -->|Yes| D[Process Batch]
    C -->|No| E[Add to Buffer]
    D --> F[Validate Batch]
    F --> G{Valid?}
    G -->|Yes| H[Insert Batch]
    G -->|No| I[Error Handling]
    H --> J[Commit Transaction]
    I --> K[Log Errors]
    J --> L[Update Statistics]
    K --> L
    E --> B
    L --> M[Continue Processing]
```

### Business Rules

#### Loading Rules
- **Data Integrity**: All loaded data must maintain referential integrity
- **Transaction Safety**: All operations must be transaction-safe
- **Performance**: Loading must meet performance requirements
- **Error Handling**: Graceful handling of loading errors

#### Quality Rules
- **Constraint Compliance**: All data must comply with database constraints
- **Referential Integrity**: Foreign key relationships must be maintained
- **Data Consistency**: Data must be consistent across related tables
- **Timeliness**: Loading must complete within time constraints

---

## Information Architecture

### Data Models

#### Core Data Structures

```mermaid
classDiagram
    class ILoader {
        <<interface>>
        +initialize(config, context)
        +load(record, context)
        +load_batch(batch, context)
        +get_statistics()
        +finalize(context)
    }
    
    class LoaderBase {
        <<abstract>>
        #config_: unordered_map~string, any~
        #context_: ProcessingContext&
        #logger_: shared_ptr~Logger~
        +initialize(config, context)
        +get_statistics()
        +finalize(context)
        #validate_config()
        #log_loading_stats()
    }
    
    class DatabaseLoader {
        -connection_pool_: shared_ptr~ConnectionPool~
        -table_name_: string
        -batch_size_: size_t
        -commit_interval_: size_t
        -constraint_handling_: ConstraintHandling
        +load(record, context)
        +load_batch(batch, context)
        -prepare_statement()
        -execute_batch()
        -handle_constraints()
    }
    
    class BatchLoader {
        -loader_: shared_ptr~ILoader~
        -batch_buffer_: vector~Record~
        -batch_size_: size_t
        -commit_interval_: size_t
        -transaction_count_: size_t
        +add_record(record)
        +flush_batch()
        +commit_transaction()
        -process_batch()
    }
    
    class BatchInserter {
        -connection_: shared_ptr~Connection~
        -table_name_: string
        -column_names_: vector~string~
        -prepared_statement_: shared_ptr~PreparedStatement~
        -batch_size_: size_t
        +insert_batch(records)
        +prepare_statement()
        +execute_batch()
        +get_affected_rows()
    }
    
    class AdditionalLoaders {
        -loaders_: unordered_map~string, shared_ptr~ILoader~~
        +register_loader(name, loader)
        +get_loader(name)
        +load_with_loader(name, record, context)
        +get_all_loaders()
    }
    
    class LoaderStrategies {
        -strategy_: LoadingStrategy
        -batch_size_: size_t
        -parallel_workers_: size_t
        +set_strategy(strategy)
        +load_batch(batch, context)
        +optimize_performance()
    }
    
    class ConnectionPool {
        -connections_: vector~shared_ptr~Connection~~
        -available_: queue~shared_ptr~Connection~~
        -mutex_: mutex
        -condition_: condition_variable
        +get_connection()
        +release_connection()
        +initialize_pool()
        +cleanup_pool()
    }
    
    ILoader <|-- LoaderBase
    LoaderBase <|-- DatabaseLoader
    LoaderBase <|-- BatchLoader
    DatabaseLoader --> BatchInserter
    DatabaseLoader --> ConnectionPool
    DatabaseLoader --> AdditionalLoaders
    DatabaseLoader --> LoaderStrategies
```

#### Database Schema Model

```mermaid
classDiagram
    class OMOPTable {
        +table_name: string
        +columns: vector~Column~
        +constraints: vector~Constraint~
        +indexes: vector~Index~
        +get_create_sql()
        +get_insert_sql()
    }
    
    class Column {
        +column_name: string
        +data_type: string
        +is_nullable: bool
        +default_value: string
        +constraints: vector~Constraint~
    }
    
    class Constraint {
        +constraint_name: string
        +constraint_type: ConstraintType
        +column_names: vector~string~
        +reference_table: string
        +reference_columns: vector~string~
    }
    
    class Index {
        +index_name: string
        +column_names: vector~string~
        +is_unique: bool
        +index_type: IndexType
    }
    
    OMOPTable --> Column
    OMOPTable --> Constraint
    OMOPTable --> Index
    Column --> Constraint
```

### Information Flow

#### Data Loading Flow

```mermaid
graph TD
    A[Transformed Records] --> B[Batch Assembly]
    B --> C[Constraint Validation]
    C --> D[Transaction Preparation]
    D --> E[Database Connection]
    E --> F[Prepared Statement]
    F --> G[Batch Execution]
    G --> H[Commit/Rollback]
    H --> I[Error Handling]
    I --> J[Progress Update]
    
    K[Configuration] --> B
    K --> C
    K --> D
    K --> E
    K --> F
    K --> G
    K --> H
    
    L[Processing Context] --> B
    L --> C
    L --> D
    L --> E
    L --> F
    L --> G
    L --> H
    L --> I
    L --> J
```

#### Constraint Handling Flow

```mermaid
graph TD
    A[Record] --> B[Primary Key Check]
    B --> C{Valid?}
    C -->|Yes| D[Foreign Key Check]
    C -->|No| E[Error Handling]
    D --> F{Valid?}
    F -->|Yes| G[Unique Constraint Check]
    F -->|No| H[Error Handling]
    G --> I{Valid?}
    I -->|Yes| J[Check Constraint]
    I -->|No| K[Error Handling]
    J --> L{Valid?}
    L -->|Yes| M[Record Valid]
    L -->|No| N[Error Handling]
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Load Library Components"
        A[Database Loader] --> B[Batch Loader]
        A --> C[Batch Inserter]
        A --> D[Additional Loaders]
        A --> E[Loader Strategies]
        
        F[Connection Pool] --> G[PostgreSQL Connection]
        F --> H[MySQL Connection]
        F --> I[ODBC Connection]
        
        J[Constraint Handler] --> K[Primary Key Handler]
        J --> L[Foreign Key Handler]
        J --> M[Unique Constraint Handler]
        J --> N[Check Constraint Handler]
        
        O[Transaction Manager] --> P[Commit Handler]
        O --> Q[Rollback Handler]
        O --> R[Savepoint Handler]
    end
    
    subgraph "Interface Layer"
        S[ILoader]
        T[LoaderBase]
        U[ProcessingContext]
    end
    
    A --> S
    B --> S
    C --> S
    D --> S
    E --> S
    
    A --> T
    B --> T
    C --> T
    D --> T
    E --> T
```

### Implemented Loaders

#### 1. Database Loader (`src/lib/load/database_loader.h/cpp`)

**Features:**
- **Multi-Database Support**: PostgreSQL, MySQL, and ODBC support
- **Batch Processing**: Efficient batch insertion with configurable batch sizes
- **Transaction Management**: ACID-compliant transaction handling
- **Constraint Handling**: Proper handling of database constraints
- **Error Recovery**: Comprehensive error handling and recovery

**Configuration:**
```yaml
database_loader:
  table_name: "person"
  batch_size: 1000
  commit_interval: 10000
  connection_pool_size: 10
  constraint_handling: "strict"
  error_threshold: 0.01
  retry_attempts: 3
  retry_delay: 1000
```

**Usage Example:**
```cpp
auto loader = std::make_unique<DatabaseLoader>();
std::unordered_map<std::string, std::any> config = {
    {"table_name", "person"},
    {"batch_size", 1000},
    {"commit_interval", 10000}
};
loader->initialize(config, context);

// Load single record
loader->load(record, context);

// Load batch
loader->load_batch(batch, context);
```

#### 2. Batch Loader (`src/lib/load/batch_loader.h/cpp`)

**Features:**
- **Batch Buffering**: Efficient batch assembly and buffering
- **Configurable Batch Sizes**: Adjustable batch sizes for optimal performance
- **Transaction Management**: Automatic transaction management
- **Memory Optimization**: Memory-efficient batch processing
- **Progress Tracking**: Real-time progress tracking and reporting

**Configuration:**
```yaml
batch_loader:
  batch_size: 1000
  commit_interval: 10000
  buffer_size: 5000
  flush_on_full: true
  auto_commit: true
  progress_reporting: true
```

**Usage Example:**
```cpp
auto batch_loader = std::make_unique<BatchLoader>(loader, config);
batch_loader->initialize(config, context);

// Add records to batch
batch_loader->add_record(record1, context);
batch_loader->add_record(record2, context);

// Flush batch when ready
batch_loader->flush_batch(context);
```

#### 3. Batch Inserter (`src/lib/load/batch_inserter.h/cpp`)

**Features:**
- **Prepared Statements**: Optimized prepared statement usage
- **Bulk Insertion**: High-performance bulk insertion operations
- **Column Mapping**: Automatic column name and type mapping
- **Error Handling**: Detailed error reporting for failed insertions
- **Performance Metrics**: Comprehensive performance measurement

**Configuration:**
```yaml
batch_inserter:
  table_name: "person"
  column_mapping:
    person_id: "person_id"
    gender_concept_id: "gender_concept_id"
    birth_datetime: "birth_datetime"
  batch_size: 1000
  use_prepared_statements: true
  optimize_inserts: true
```

**Usage Example:**
```cpp
auto inserter = std::make_unique<BatchInserter>();
inserter->initialize(config, context);

// Insert batch of records
size_t affected_rows = inserter->insert_batch(records, context);
```

#### 4. Additional Loaders (`src/lib/load/additional_loaders.h/cpp`)

**Features:**
- **Specialized Loaders**: Loaders for specific use cases and data types
- **Plugin Architecture**: Extensible loader plugin system
- **Custom Logic**: Support for custom loading logic
- **Performance Optimization**: Optimized for specific data patterns

**Available Loaders:**
- **Incremental Loader**: Handles incremental data loading
- **Upsert Loader**: Handles insert/update operations
- **Delete Loader**: Handles data deletion operations
- **Audit Loader**: Handles audit trail data

**Configuration:**
```yaml
additional_loaders:
  incremental_loader:
    enabled: true
    change_detection: "timestamp"
    change_column: "modified_date"
  
  upsert_loader:
    enabled: true
    conflict_resolution: "update"
    conflict_columns: ["person_id"]
  
  audit_loader:
    enabled: true
    audit_table: "audit_log"
    track_changes: true
```

#### 5. Loader Strategies (`src/lib/load/loader_strategies.h/cpp`)

**Features:**
- **Strategy Pattern**: Different loading strategies for different scenarios
- **Performance Optimization**: Strategy-specific performance optimizations
- **Resource Management**: Efficient resource utilization
- **Scalability**: Horizontal and vertical scaling support

**Available Strategies:**
- **Sequential Strategy**: Sequential record processing
- **Parallel Strategy**: Parallel record processing
- **Streaming Strategy**: Streaming data processing
- **Hybrid Strategy**: Combination of multiple strategies

**Configuration:**
```yaml
loader_strategies:
  default_strategy: "parallel"
  parallel_strategy:
    worker_threads: 4
    batch_size: 1000
    queue_size: 10000
  
  streaming_strategy:
    buffer_size: 5000
    flush_interval: 5000
    compression: true
```

### Constraint Handling

#### Constraint Types

**Primary Key Constraints:**
```cpp
class PrimaryKeyHandler {
public:
    bool validate_primary_key(const Record& record, const std::string& table_name);
    bool handle_primary_key_violation(const Record& record, const std::string& table_name);
    std::string generate_primary_key(const Record& record);
};
```

**Foreign Key Constraints:**
```cpp
class ForeignKeyHandler {
public:
    bool validate_foreign_key(const Record& record, const std::string& column_name);
    bool handle_foreign_key_violation(const Record& record, const std::string& column_name);
    std::optional<int> resolve_foreign_key(const std::string& value, const std::string& table_name);
};
```

**Unique Constraints:**
```cpp
class UniqueConstraintHandler {
public:
    bool validate_unique_constraint(const Record& record, const std::vector<std::string>& columns);
    bool handle_unique_violation(const Record& record, const std::vector<std::string>& columns);
    std::string generate_unique_value(const std::vector<std::string>& columns);
};
```

**Check Constraints:**
```cpp
class CheckConstraintHandler {
public:
    bool validate_check_constraint(const Record& record, const std::string& constraint_expression);
    bool handle_check_violation(const Record& record, const std::string& constraint_expression);
    std::string get_constraint_error_message(const std::string& constraint_expression);
};
```

### Transaction Management

#### Transaction Features

**ACID Compliance:**
```cpp
class TransactionManager {
public:
    // Start transaction
    bool begin_transaction();
    
    // Commit transaction
    bool commit_transaction();
    
    // Rollback transaction
    bool rollback_transaction();
    
    // Create savepoint
    std::string create_savepoint(const std::string& name);
    
    // Rollback to savepoint
    bool rollback_to_savepoint(const std::string& name);
    
    // Release savepoint
    bool release_savepoint(const std::string& name);
};
```

**Transaction Configuration:**
```yaml
transaction_management:
  auto_commit: false
  isolation_level: "READ_COMMITTED"
  timeout_seconds: 300
  max_retries: 3
  retry_delay_ms: 1000
  
  savepoints:
    enabled: true
    auto_create: true
    cleanup_on_commit: true
```

### Error Handling and Recovery

#### Exception Hierarchy

```mermaid
classDiagram
    class LoadException {
        <<abstract>>
        +string table_name
        +string operation
        +string error_details
        +what()
    }
    
    class ConstraintViolationException {
        +string constraint_name
        +string constraint_type
        +string column_name
        +string actual_value
        +string expected_value
    }
    
    class TransactionException {
        +string transaction_id
        +string operation
        +string database_error
        +int error_code
    }
    
    class ConnectionException {
        +string connection_string
        +string connection_error
        +int retry_count
    }
    
    class BatchProcessingException {
        +string batch_id
        +size_t record_count
        +size_t error_count
        +vector~string~ error_messages
    }
    
    LoadException <|-- ConstraintViolationException
    LoadException <|-- TransactionException
    LoadException <|-- ConnectionException
    LoadException <|-- BatchProcessingException
```

#### Error Recovery Strategies

1. **Retry with Backoff**: Automatic retry for transient errors
2. **Constraint Resolution**: Automatic resolution of constraint violations
3. **Partial Batch Processing**: Continue processing after non-critical errors
4. **Transaction Rollback**: Automatic rollback on critical errors
5. **Error Logging**: Comprehensive error reporting for troubleshooting

### Performance Optimization

#### Memory Management

- **Batch Buffering**: Efficient batch assembly and buffering
- **Object Pooling**: Reuse of database connection objects
- **Smart Pointers**: Ensure proper resource cleanup
- **Memory Mapping**: Memory-mapped file operations for large datasets

#### Database Optimization

- **Prepared Statements**: Reduce parsing overhead for repeated operations
- **Batch Operations**: Minimize round trips through batch processing
- **Connection Pooling**: Efficient database connection management
- **Index-aware Loading**: Optimize loading based on database indexes

#### Parallel Processing

- **Multi-threaded Loading**: Parallel processing of record batches
- **Connection Pooling**: Multiple database connections for parallel operations
- **Work Stealing**: Dynamic load balancing across worker threads
- **I/O Overlap**: Overlap computation with database I/O operations

---

## Cross-Cutting Concerns

### Configuration Management

The Load Library integrates with the Common Library's configuration system to provide:

- **YAML-based Configuration**: Human-readable loading rules
- **Environment Variable Substitution**: Secure credential management
- **Configuration Validation**: Schema and semantic validation
- **Hot Reloading**: Runtime configuration updates

### Logging and Monitoring

- **Structured Logging**: JSON-formatted logs with context information
- **Performance Metrics**: Detailed timing and throughput measurements
- **Loading Tracking**: Complete audit trail of loading operations
- **Quality Metrics**: Data quality measurement and reporting

### Security

- **Credential Management**: Secure storage and transmission of database credentials
- **Connection Encryption**: TLS/SSL support for database connections
- **Access Control**: Role-based permissions for loading operations
- **Audit Logging**: Tracks all loading activities

---

## Deployment and Operations

### Build System

The Load Library uses CMake for build management with the following structure:

```
src/lib/load/
├── CMakeLists.txt
├── database_loader.h/cpp
├── batch_loader.h/cpp
├── batch_inserter.h/cpp
├── additional_loaders.h/cpp
├── loader_strategies.h/cpp
├── loader_base.h/cpp
└── load_cmakelists.txt
```

### Dependencies

**Core Dependencies:**
- **libpq**: PostgreSQL client library
- **libmysqlclient**: MySQL client library
- **unixODBC**: ODBC driver manager
- **yaml-cpp**: YAML configuration parsing
- **spdlog**: Structured logging framework

**System Dependencies:**
- **C++17**: Modern C++ features and standard library
- **Threading**: Standard C++ threading support
- **Filesystem**: C++17 filesystem library

### Testing Strategy

- **Unit Tests**: Component-level testing with mock objects
- **Integration Tests**: End-to-end loading testing
- **Performance Tests**: Throughput and scalability validation
- **Stress Tests**: Error condition and recovery testing

### Monitoring and Observability

- **Metrics Collection**: Performance and error rate monitoring
- **Health Checks**: Database connection availability verification
- **Distributed Tracing**: Request flow tracking across components
- **Alerting**: Automated notification of issues and anomalies

### Deployment Models

#### Monolithic Deployment
- Single application instance with all loaders
- Simplified deployment and management
- Shared memory and resource utilization

#### Microservices Deployment
- Separate services for different loading types
- Independent scaling and deployment
- Network-based inter-service communication

### Operational Considerations

#### Resource Management
- **Memory Limits**: Configurable memory usage limits
- **CPU Affinity**: Thread-to-core binding for performance
- **I/O Optimization**: Asynchronous I/O operations
- **Connection Pooling**: Database connection management

#### Fault Tolerance
- **Circuit Breakers**: Prevents cascade failures
- **Retry Mechanisms**: Automatic recovery from transient errors
- **Graceful Degradation**: Continues operation with reduced functionality
- **Data Consistency**: Transaction-based data integrity

#### Scalability
- **Horizontal Scaling**: Multiple loading instances
- **Vertical Scaling**: Resource allocation optimization
- **Load Balancing**: Distribution of processing load
- **Caching**: Frequently accessed data caching

---

## Security and Compliance

### Data Protection

- **Encryption in Transit**: TLS/SSL for all database connections
- **Encryption at Rest**: Support for encrypted database storage
- **Credential Security**: Secure storage and transmission of credentials
- **Access Control**: Role-based permissions for loading operations

### Compliance

- **HIPAA Compliance**: Healthcare data protection standards
- **GDPR Compliance**: European data protection regulations
- **Audit Logging**: Comprehensive audit trail for compliance
- **Data Retention**: Configurable data retention policies

### Security Best Practices

- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Principle of Least Privilege**: Minimal required database permissions
- **Secure Configuration**: Encrypted configuration management
- **Regular Security Updates**: Timely security patch management

## Conclusion

The OMOP Load Library provides a robust, scalable, and extensible foundation for healthcare data loading. Its modular architecture, comprehensive error handling, and performance optimizations make it suitable for both small-scale deployments and enterprise-wide implementations. The plugin-based loader system ensures the framework can evolve to meet changing requirements while maintaining stability and performance. 