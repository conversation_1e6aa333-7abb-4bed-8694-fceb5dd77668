# OMOP Transform Library Architecture

## Executive Summary

The OMOP Transform Library provides a comprehensive data transformation framework that converts source healthcare data into the OMOP Common Data Model format. The library implements sophisticated transformation rules, vocabulary mapping, data validation, and quality assurance mechanisms. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)
6. [Security and Compliance](#security-and-compliance)

---

## Business Architecture

### Business Context

The Transform Library serves as the data transformation layer in the OMOP ETL pipeline, enabling healthcare organizations to convert diverse source data formats into standardized OMOP CDM structures. The library addresses the critical business need for consistent data transformation across multiple healthcare systems and data formats.

### Business Capabilities

#### Core Transformation Capabilities
- **Field Mapping**: Direct and complex field-to-field transformations
- **Vocabulary Mapping**: Standardized concept mapping using OMOP vocabularies
- **Data Type Conversion**: Automatic and configurable data type transformations
- **Validation**: Comprehensive data validation and quality checks
- **Custom Logic**: Support for complex business rule transformations

#### Data Quality Management
- **Data Profiling**: Automatic generation of data quality metrics
- **Anomaly Detection**: Identification of unusual data patterns
- **Error Handling**: Graceful handling of transformation errors
- **Quality Reporting**: Detailed quality assessment reports

### Business Processes

#### Data Transformation Workflow
```mermaid
graph TD
    A[Source Record] --> B[Field Mapping]
    B --> C[Data Type Conversion]
    C --> D[Vocabulary Mapping]
    D --> E[Custom Transformations]
    E --> F[Validation]
    F --> G{Valid?}
    G -->|Yes| H[Transformed Record]
    G -->|No| I[Error Handling]
    I --> J[Error Logging]
    J --> K[Continue or Skip]
```

#### Vocabulary Mapping Process
```mermaid
graph TD
    A[Source Concept] --> B[Vocabulary Lookup]
    B --> C[Concept Search]
    C --> D[Match Scoring]
    D --> E{Match Found?}
    E -->|Yes| F[Standard Concept]
    E -->|No| G[Fallback Logic]
    G --> H[Default Mapping]
    F --> I[OMOP Concept ID]
    H --> I
```

### Business Rules

#### Transformation Rules
- **Data Integrity**: All transformations must preserve data integrity
- **Audit Trail**: Complete tracking of transformation changes
- **Performance**: Transformations must meet performance requirements
- **Accuracy**: Vocabulary mappings must be accurate and up-to-date

#### Quality Rules
- **Completeness**: Required fields must be populated
- **Accuracy**: Data must conform to expected formats and ranges
- **Consistency**: Data must be consistent across related fields
- **Timeliness**: Transformations must complete within time constraints

---

## Information Architecture

### Data Models

#### Core Data Structures

```mermaid
classDiagram
    class ITransformer {
        <<interface>>
        +initialize(config, context)
        +transform(record, context)
        +validate(record)
        +get_statistics()
        +finalize(context)
    }
    
    class TransformationEngine {
        -config_: TransformationConfig
        -rules_: vector~TransformationRule~
        -vocabulary_service_: shared_ptr~VocabularyService~
        -validation_engine_: shared_ptr~ValidationEngine~
        +transform(record, context)
        +add_rule(rule)
        +remove_rule(rule_id)
        +get_statistics()
    }
    
    class TransformationRule {
        +rule_id: string
        +rule_type: RuleType
        +source_field: string
        +target_field: string
        +transformation_logic: string
        +parameters: unordered_map~string, any~
        +condition: string
        +priority: int
        +is_active: bool
    }
    
    class VocabularyService {
        -vocabulary_cache_: unordered_map~string, Concept~
        -mapping_cache_: unordered_map~string, string~
        -vocabulary_db_: shared_ptr~Database~
        +lookup_concept(source_code, vocabulary_id)
        +search_concepts(search_term, vocabulary_id)
        +get_concept(concept_id)
        +cache_concept(concept)
    }
    
    class ValidationEngine {
        -validation_rules_: vector~ValidationRule~
        -custom_validators_: unordered_map~string, ValidatorFunction~
        +validate(record)
        +add_validation_rule(rule)
        +register_custom_validator(name, validator)
        +get_validation_results()
    }
    
    class FieldTransformation {
        +field_name: string
        +transformation_type: TransformationType
        +source_fields: vector~string~
        +target_field: string
        +parameters: unordered_map~string, any~
        +apply(record)
    }
    
    class StringTransformation {
        +transformation_type: StringTransformType
        +source_field: string
        +target_field: string
        +parameters: unordered_map~string, any~
        +apply(input_string)
    }
    
    class DateTransformation {
        +transformation_type: DateTransformType
        +source_field: string
        +target_field: string
        +input_format: string
        +output_format: string
        +apply(input_date)
    }
    
    class NumericTransformation {
        +transformation_type: NumericTransformType
        +source_field: string
        +target_field: string
        +parameters: unordered_map~string, any~
        +apply(input_value)
    }
    
    class CustomTransformation {
        +function_name: string
        +source_fields: vector~string~
        +target_field: string
        +function_code: string
        +parameters: unordered_map~string, any~
        +execute(record, context)
    }
    
    class ConditionalTransformation {
        +condition: string
        +true_transformation: shared_ptr~TransformationRule~
        +false_transformation: shared_ptr~TransformationRule~
        +evaluate_condition(record)
        +apply(record)
    }
    
    ITransformer <|-- TransformationEngine
    TransformationEngine --> TransformationRule
    TransformationEngine --> VocabularyService
    TransformationEngine --> ValidationEngine
    TransformationRule <|-- FieldTransformation
    TransformationRule <|-- StringTransformation
    TransformationRule <|-- DateTransformation
    TransformationRule <|-- NumericTransformation
    TransformationRule <|-- CustomTransformation
    TransformationRule <|-- ConditionalTransformation
```

#### Vocabulary Data Model

```mermaid
classDiagram
    class Concept {
        +concept_id: int
        +concept_name: string
        +domain_id: string
        +vocabulary_id: string
        +concept_class_id: string
        +standard_concept: string
        +concept_code: string
        +valid_start_date: date
        +valid_end_date: date
        +invalid_reason: string
    }
    
    class ConceptRelationship {
        +concept_id_1: int
        +concept_id_2: int
        +relationship_id: string
        +valid_start_date: date
        +valid_end_date: date
        +invalid_reason: string
    }
    
    class Vocabulary {
        +vocabulary_id: string
        +vocabulary_name: string
        +vocabulary_reference: string
        +vocabulary_version: string
        +vocabulary_concept_id: int
    }
    
    class ConceptMapping {
        +source_code: string
        +source_vocabulary_id: string
        +target_concept_id: int
        +mapping_type: string
        +confidence_score: double
        +created_date: date
    }
    
    Concept --> ConceptRelationship
    Concept --> Vocabulary
    Concept --> ConceptMapping
```

### Information Flow

#### Transformation Flow

```mermaid
graph TD
    A[Source Record] --> B[Pre-processing]
    B --> C[Field Mapping]
    C --> D[Data Type Conversion]
    D --> E[Vocabulary Mapping]
    E --> F[Custom Transformations]
    F --> G[Post-processing]
    G --> H[Validation]
    H --> I{Validation Pass?}
    I -->|Yes| J[Transformed Record]
    I -->|No| K[Error Handling]
    K --> L[Error Logging]
    L --> M[Continue or Skip]
    
    N[Configuration] --> B
    N --> C
    N --> D
    N --> E
    N --> F
    N --> G
    N --> H
    
    O[Vocabulary Service] --> E
    P[Validation Engine] --> H
```

#### Vocabulary Lookup Flow

```mermaid
graph TD
    A[Source Code] --> B[Cache Check]
    B --> C{Cached?}
    C -->|Yes| D[Return Cached]
    C -->|No| E[Database Lookup]
    E --> F{Found?}
    F -->|Yes| G[Cache Result]
    F -->|No| H[Fuzzy Search]
    H --> I{Match Found?}
    I -->|Yes| J[Cache Result]
    I -->|No| K[Default Mapping]
    G --> L[Return Concept]
    J --> L
    K --> L
    D --> L
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Transform Library Components"
        A[Transformation Engine] --> B[Field Transformations]
        A --> C[String Transformations]
        A --> D[Date Transformations]
        A --> E[Numeric Transformations]
        A --> F[Custom Transformations]
        A --> G[Conditional Transformations]
        
        H[Vocabulary Service] --> I[Concept Lookup]
        H --> J[Concept Search]
        H --> K[Mapping Cache]
        
        L[Validation Engine] --> M[Field Validation]
        L --> N[Record Validation]
        L --> O[Custom Validators]
        
        P[Transformation Utils] --> Q[Data Type Utils]
        P --> R[String Utils]
        P --> S[Date Utils]
        P --> T[Numeric Utils]
    end
    
    subgraph "Interface Layer"
        U[ITransformer]
        V[TransformationEngine]
        W[VocabularyService]
        X[ValidationEngine]
    end
    
    A --> U
    H --> W
    L --> X
```

### Implemented Transformations

#### 1. Field Transformations (`src/lib/transform/field_transformations.h/cpp`)

**Features:**
- **Direct Mapping**: Simple field-to-field copying
- **Multi-field Mapping**: Combining multiple source fields
- **Default Values**: Setting default values for missing fields
- **Conditional Mapping**: Field mapping based on conditions

**Configuration:**
```yaml
field_transformations:
  - source_field: "patient_id"
    target_field: "person_id"
    transformation_type: "direct"
  
  - source_fields: ["first_name", "last_name"]
    target_field: "full_name"
    transformation_type: "concatenate"
    separator: " "
  
  - target_field: "data_source"
    transformation_type: "constant"
    value: "CLINICAL_SYSTEM"
```

#### 2. String Transformations (`src/lib/transform/string_transformations.h/cpp`)

**Features:**
- **Case Conversion**: Upper, lower, title case transformations
- **Trimming**: Whitespace removal and padding
- **Substring Operations**: Extract, replace, and split operations
- **Pattern Matching**: Regular expression-based transformations
- **Encoding Conversion**: Character encoding transformations

**Configuration:**
```yaml
string_transformations:
  - source_field: "diagnosis_code"
    target_field: "standardized_code"
    transformation_type: "uppercase"
  
  - source_field: "description"
    target_field: "clean_description"
    transformation_type: "trim"
  
  - source_field: "phone_number"
    target_field: "formatted_phone"
    transformation_type: "regex_replace"
    pattern: "\\D"
    replacement: ""
```

#### 3. Date Transformations (`src/lib/transform/date_transformations.h/cpp`)

**Features:**
- **Format Conversion**: Converting between date formats
- **Date Parsing**: Automatic date format detection
- **Date Calculations**: Age calculation, date arithmetic
- **Timezone Handling**: Timezone conversion and normalization
- **Date Validation**: Valid date range checking

**Configuration:**
```yaml
date_transformations:
  - source_field: "birth_date"
    target_field: "birth_datetime"
    transformation_type: "format_conversion"
    input_format: "MM/DD/YYYY"
    output_format: "YYYY-MM-DD"
  
  - source_fields: ["birth_date", "visit_date"]
    target_field: "age_at_visit"
    transformation_type: "age_calculation"
    unit: "years"
  
  - source_field: "admission_time"
    target_field: "admission_datetime"
    transformation_type: "timezone_conversion"
    from_timezone: "EST"
    to_timezone: "UTC"
```

#### 4. Numeric Transformations (`src/lib/transform/numeric_transformations.h/cpp`)

**Features:**
- **Type Conversion**: Converting between numeric types
- **Scaling**: Unit conversion and scaling operations
- **Rounding**: Various rounding strategies
- **Range Mapping**: Mapping values to new ranges
- **Statistical Operations**: Mean, median, percentile calculations

**Configuration:**
```yaml
numeric_transformations:
  - source_field: "weight_lbs"
    target_field: "weight_kg"
    transformation_type: "unit_conversion"
    conversion_factor: 0.453592
  
  - source_field: "temperature_f"
    target_field: "temperature_c"
    transformation_type: "formula"
    formula: "(value - 32) * 5/9"
  
  - source_field: "score"
    target_field: "normalized_score"
    transformation_type: "range_mapping"
    source_min: 0
    source_max: 100
    target_min: 0
    target_max: 1
```

#### 5. Custom Transformations (`src/lib/transform/custom_transformations.h/cpp`)

**Features:**
- **Script Execution**: Custom JavaScript/Python script execution
- **Function Registration**: C++ function registration and execution
- **Parameter Passing**: Flexible parameter passing to custom functions
- **Error Handling**: Comprehensive error handling for custom code
- **Performance Optimization**: Compiled custom functions for performance

**Configuration:**
```yaml
custom_transformations:
  - source_fields: ["systolic_bp", "diastolic_bp"]
    target_field: "blood_pressure_category"
    transformation_type: "custom_function"
    function_name: "categorize_blood_pressure"
    parameters:
      normal_systolic_max: 120
      normal_diastolic_max: 80
      elevated_systolic_max: 129
      elevated_diastolic_max: 80
```

#### 6. Conditional Transformations (`src/lib/transform/conditional_transformations.h/cpp`)

**Features:**
- **Conditional Logic**: If-then-else transformation logic
- **Complex Conditions**: Multi-field condition evaluation
- **Nested Conditions**: Hierarchical conditional logic
- **Default Actions**: Fallback transformations for unmatched conditions

**Configuration:**
```yaml
conditional_transformations:
  - condition: "gender == 'M'"
    true_transformation:
      target_field: "gender_concept_id"
      value: 8507
    false_transformation:
      target_field: "gender_concept_id"
      value: 8532
  
  - condition: "age >= 18"
    true_transformation:
      target_field: "age_group"
      value: "adult"
    false_transformation:
      target_field: "age_group"
      value: "pediatric"
```

### Vocabulary Service

#### Core Features (`src/lib/transform/vocabulary_service.h/cpp`)

**Concept Lookup:**
```cpp
class VocabularyService {
public:
    // Direct concept lookup
    std::optional<Concept> lookup_concept(const std::string& source_code, 
                                         const std::string& vocabulary_id);
    
    // Fuzzy concept search
    std::vector<Concept> search_concepts(const std::string& search_term,
                                        const std::string& vocabulary_id,
                                        size_t max_results = 10);
    
    // Get concept by ID
    std::optional<Concept> get_concept(int concept_id);
    
    // Cache management
    void cache_concept(const Concept& concept);
    void clear_cache();
    void preload_vocabulary(const std::string& vocabulary_id);
};
```

**Vocabulary Mappings:**
```yaml
vocabulary_mappings:
  icd10_cm:
    - source_code: "E11.9"
      target_concept_id: 201820
      mapping_type: "EXACT"
      confidence_score: 1.0
    
    - source_code: "I10"
      target_concept_id: 316139
      mapping_type: "EXACT"
      confidence_score: 1.0
  
  snomed_ct:
    - source_code: "73211009"
      target_concept_id: 201820
      mapping_type: "EXACT"
      confidence_score: 1.0
```

### Validation Engine

#### Core Features (`src/lib/transform/validation_engine.h/cpp`)

**Field Validation:**
```cpp
class ValidationEngine {
public:
    // Field-level validation
    ValidationResult validate_field(const std::string& field_name,
                                   const std::any& value,
                                   const ValidationRule& rule);
    
    // Record-level validation
    ValidationResult validate_record(const Record& record);
    
    // Custom validator registration
    void register_custom_validator(const std::string& name,
                                  std::function<ValidationResult(const std::any&)> validator);
    
    // Validation rule management
    void add_validation_rule(const ValidationRule& rule);
    void remove_validation_rule(const std::string& rule_id);
};
```

**Validation Rules:**
```yaml
validation_rules:
  - field_name: "person_id"
    rule_type: "required"
    error_message: "Person ID is required"
  
  - field_name: "birth_datetime"
    rule_type: "date_range"
    min_date: "1900-01-01"
    max_date: "2024-12-31"
    error_message: "Birth date must be between 1900 and 2024"
  
  - field_name: "gender_concept_id"
    rule_type: "value_set"
    allowed_values: [8507, 8532]  # Male, Female
    error_message: "Invalid gender concept ID"
  
  - field_name: "age"
    rule_type: "numeric_range"
    min_value: 0
    max_value: 150
    error_message: "Age must be between 0 and 150"
```

### Transformation Engine

#### Core Features (`src/lib/transform/transformation_engine.h/cpp`)

**Engine Configuration:**
```cpp
class TransformationEngine : public ITransformer {
public:
    // Initialize with configuration
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    ProcessingContext& context) override;
    
    // Transform single record
    std::optional<Record> transform(const Record& record,
                                   ProcessingContext& context) override;
    
    // Validate record
    ValidationResult validate(const Record& record) const override;
    
    // Rule management
    void add_transformation_rule(const TransformationRule& rule);
    void remove_transformation_rule(const std::string& rule_id);
    void clear_rules();
    
    // Statistics
    std::unordered_map<std::string, std::any> get_statistics() const override;
};
```

**Engine Configuration:**
```yaml
transformation_engine:
  batch_size: 1000
  parallel_processing: true
  max_workers: 4
  error_threshold: 0.01
  continue_on_error: false
  
  vocabulary_service:
    cache_size: 10000
    preload_vocabularies: ["ICD10CM", "SNOMED", "LOINC"]
    fuzzy_search_enabled: true
    max_fuzzy_results: 5
  
  validation:
    strict_mode: true
    log_validation_errors: true
    skip_invalid_records: false
```

### Error Handling and Recovery

#### Exception Hierarchy

```mermaid
classDiagram
    class TransformationException {
        <<abstract>>
        +string transformation_rule
        +string field_name
        +string error_details
        +what()
    }
    
    class VocabularyException {
        +string source_code
        +string vocabulary_id
        +string lookup_error
    }
    
    class ValidationException {
        +string validation_rule
        +string field_name
        +string actual_value
        +string expected_value
    }
    
    class CustomTransformationException {
        +string function_name
        +string function_error
        +string parameters
    }
    
    class ConfigurationException {
        +string config_key
        +string config_value
        +string validation_error
    }
    
    TransformationException <|-- VocabularyException
    TransformationException <|-- ValidationException
    TransformationException <|-- CustomTransformationException
    TransformationException <|-- ConfigurationException
```

#### Error Recovery Strategies

1. **Graceful Degradation**: Continue processing with partial transformations
2. **Default Values**: Use default values for failed transformations
3. **Error Logging**: Comprehensive error reporting for troubleshooting
4. **Retry Logic**: Retry failed transformations with different parameters
5. **Fallback Mappings**: Use alternative vocabulary mappings on failure

### Performance Optimization

#### Memory Management

- **Object Pooling**: Reuse transformation objects to reduce allocation overhead
- **Smart Pointers**: Ensure proper resource cleanup
- **Batch Processing**: Process records in batches for memory efficiency
- **Cache Management**: Intelligent caching of frequently accessed data

#### Parallel Processing

- **Multi-threaded Transformation**: Parallel processing of record batches
- **Lock-free Data Structures**: Minimize contention in high-performance scenarios
- **Work Stealing**: Dynamic load balancing across worker threads
- **I/O Overlap**: Overlap computation with I/O operations

#### Vocabulary Optimization

- **Caching Strategy**: Multi-level caching for vocabulary lookups
- **Preloading**: Preload frequently used vocabularies
- **Indexing**: Optimized database indexes for concept lookups
- **Compression**: Compress vocabulary data for memory efficiency

---

## Cross-Cutting Concerns

### Configuration Management

The Transform Library integrates with the Common Library's configuration system to provide:

- **YAML-based Configuration**: Human-readable transformation rules
- **Environment Variable Substitution**: Secure parameter management
- **Configuration Validation**: Schema and semantic validation
- **Hot Reloading**: Runtime configuration updates

### Logging and Monitoring

- **Structured Logging**: JSON-formatted logs with context information
- **Performance Metrics**: Detailed timing and throughput measurements
- **Transformation Tracking**: Complete audit trail of transformations
- **Quality Metrics**: Data quality measurement and reporting

### Security

- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Access Control**: Role-based permissions for transformation operations
- **Audit Logging**: Tracks all transformation activities
- **Secure Configuration**: Encrypted configuration management

---

## Deployment and Operations

### Build System

The Transform Library uses CMake for build management with the following structure:

```
src/lib/transform/
├── CMakeLists.txt
├── transformations.h
├── transformation_engine.h/cpp
├── field_transformations.h/cpp
├── string_transformations.h/cpp
├── date_transformations.h/cpp
├── numeric_transformations.h/cpp
├── custom_transformations.h/cpp
├── conditional_transformations.h/cpp
├── vocabulary_service.h/cpp
├── vocabulary_transformations.h/cpp
├── validation_engine.h/cpp
├── transformation_utils.h/cpp
└── transformation_result.h
```

### Dependencies

**Core Dependencies:**
- **yaml-cpp**: YAML configuration parsing
- **nlohmann/json**: JSON data handling
- **spdlog**: Structured logging framework
- **fmt**: String formatting library

**System Dependencies:**
- **C++17**: Modern C++ features and standard library
- **Threading**: Standard C++ threading support
- **Regex**: C++17 regex library
- **Chrono**: Time and date utilities

### Testing Strategy

- **Unit Tests**: Component-level testing with mock objects
- **Integration Tests**: End-to-end transformation testing
- **Performance Tests**: Throughput and scalability validation
- **Stress Tests**: Error condition and recovery testing

### Monitoring and Observability

- **Metrics Collection**: Performance and error rate monitoring
- **Health Checks**: Component availability verification
- **Distributed Tracing**: Request flow tracking across components
- **Alerting**: Automated notification of issues and anomalies

### Deployment Models

#### Monolithic Deployment
- Single application instance with all transformations
- Simplified deployment and management
- Shared memory and resource utilization

#### Microservices Deployment
- Separate services for different transformation types
- Independent scaling and deployment
- Network-based inter-service communication

### Operational Considerations

#### Resource Management
- **Memory Limits**: Configurable memory usage limits
- **CPU Affinity**: Thread-to-core binding for performance
- **I/O Optimization**: Asynchronous I/O operations
- **Cache Management**: Vocabulary cache size management

#### Fault Tolerance
- **Circuit Breakers**: Prevents cascade failures
- **Retry Mechanisms**: Automatic recovery from transient errors
- **Graceful Degradation**: Continues operation with reduced functionality
- **Data Consistency**: Transaction-based data integrity

#### Scalability
- **Horizontal Scaling**: Multiple transformation instances
- **Vertical Scaling**: Resource allocation optimization
- **Load Balancing**: Distribution of processing load
- **Caching**: Frequently accessed data caching

---

## Security and Compliance

### Data Protection

- **Data Encryption**: Encryption of sensitive transformation data
- **Access Control**: Role-based permissions for transformation operations
- **Audit Logging**: Comprehensive audit trail for compliance
- **Data Masking**: PII/PHI masking during transformation

### Compliance

- **HIPAA Compliance**: Healthcare data protection standards
- **GDPR Compliance**: European data protection regulations
- **Audit Logging**: Comprehensive audit trail for compliance
- **Data Retention**: Configurable data retention policies

### Security Best Practices

- **Input Validation**: Prevents injection attacks and ensures data integrity
- **Principle of Least Privilege**: Minimal required permissions
- **Secure Configuration**: Encrypted configuration management
- **Regular Security Updates**: Timely security patch management

## Conclusion

The OMOP Transform Library provides a robust, scalable, and extensible foundation for healthcare data transformation. Its modular architecture, comprehensive error handling, and performance optimizations make it suitable for both small-scale deployments and enterprise-wide implementations. The plugin-based transformation system ensures the framework can evolve to meet changing requirements while maintaining stability and performance. 