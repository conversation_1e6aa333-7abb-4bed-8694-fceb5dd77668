# OMOP ETL Component Diagram

```mermaid
graph TB
    %% Client Applications
    subgraph "Client Applications"
        web_ui[Web UI]
        cli_tool[CLI Tool]
        api_client[External API Client]
    end

    %% API Layer
    subgraph "API Layer"
        api_server[ApiServer<br/>start stop is_running handle_request]
        
        api_service[ETLApiService<br/>register_routes create_job get_job list_jobs cancel_job get_health get_metrics]
    end

    %% Service Layer
    subgraph "Service Layer"
        service_orchestrator[ServiceOrchestrator<br/>orchestrate_job get_service_health get_orchestrator_stats]
        
        etl_service[ETLService<br/>create_job get_job_result cancel_job pause_job resume_job]
        
        extract_service[ExtractService<br/>extract initialize_source finalize_source]
        
        transform_service[TransformService<br/>transform validate_config load_vocabularies]
        
        load_service[LoadService<br/>load initialize_loader finalize_loader]
    end

    %% Core Layer
    subgraph "Core Layer"
        pipeline_manager[PipelineManager<br/>submit_job get_job_info cancel_job pause_job resume_job]
        
        etl_pipeline[ETLPipeline<br/>set_extractor set_transformer set_loader start stop pause resume]
        
        processing_context[ProcessingContext<br/>set_stage increment_processed increment_errors set_data get_data]
    end

    %% Extract Layer
    subgraph "Extract Layer"
        extractor_base[ExtractorBase<br/>initialize extract_batch has_more_data get_type]
        
        csv_extractor[CSVExtractor<br/>initialize extractBatchImpl getSchema]
        
        json_extractor[JSONExtractor<br/>initialize extractBatchImpl getSchema]
        
        database_extractor[DatabaseExtractor<br/>initialize extractBatchImpl getSchema]
        
        database_connector[DatabaseConnector<br/>connect execute_query prepare_statement disconnect]
    end

    %% Transform Layer
    subgraph "Transform Layer"
        transformation_engine[TransformationEngine<br/>initialize transform transform_batch validate]
        
        field_transformation[FieldTransformation<br/>transform validate_input get_type configure]
        
        date_transformation[DateTransformation<br/>transform configure]
        
        vocabulary_transformation[VocabularyTransformation<br/>transform configure]
        
        vocabulary_service[VocabularyService<br/>lookup_concept search_concepts get_concept]
    end

    %% Load Layer
    subgraph "Load Layer"
        loader_base[LoaderBase<br/>initialize load load_batch finalize]
        
        database_loader[DatabaseLoader<br/>initialize load load_batch commit rollback finalize]
        
        omop_database_loader[OmopDatabaseLoader<br/>initialize load convert_to_omop_table validate_omop_constraints]
        
        batch_inserter[BatchInserter<br/>initialize start stop add_record add_batch flush]
    end

    %% CDM Layer
    subgraph "CDM Layer"
        omop_table[OmopTable<br/>table_name schema_name to_insert_sql field_names field_values validate]
        
        person_table[Person<br/>table_name to_insert_sql validate]
        
        condition_occurrence_table[ConditionOccurrence<br/>table_name to_insert_sql validate]
        
        omop_table_factory[OmopTableFactory<br/>create get_supported_tables is_supported]
    end

    %% Common Layer
    subgraph "Common Layer"
        configuration_manager[ConfigurationManager<br/>load_config get_table_mapping get_source_db get_target_db get_value]
        
        logger[Logger<br/>info warn error debug set_level]
        
        exceptions[OmopException<br/>what message location]
    end

    %% External Systems
    subgraph "External Systems"
        source_database[Source Database<br/>PostgreSQL MySQL ODBC]
        
        target_database[Target Database<br/>OMOP CDM Database]
        
        vocabulary_database[Vocabulary Database<br/>OMOP Vocabulary Tables]
    end

    %% Relationships
    %% Client to API
    web_ui --> api_server
    cli_tool --> api_server
    api_client --> api_server
    
    %% API Layer
    api_server --> api_service
    
    %% API to Service
    api_service --> service_orchestrator
    api_service --> etl_service
    
    %% Service Layer
    service_orchestrator --> extract_service
    service_orchestrator --> transform_service
    service_orchestrator --> load_service
    
    %% Service to Core
    etl_service --> pipeline_manager
    extract_service --> extractor_base
    transform_service --> transformation_engine
    load_service --> loader_base
    
    %% Core Layer
    pipeline_manager --> etl_pipeline
    etl_pipeline --> processing_context
    
    %% Extract Layer
    extractor_base --> csv_extractor
    extractor_base --> json_extractor
    extractor_base --> database_extractor
    database_extractor --> database_connector
    
    %% Transform Layer
    transformation_engine --> field_transformation
    field_transformation --> date_transformation
    field_transformation --> vocabulary_transformation
    vocabulary_transformation --> vocabulary_service
    
    %% Load Layer
    loader_base --> database_loader
    loader_base --> omop_database_loader
    loader_base --> batch_inserter
    
    %% CDM Layer
    omop_database_loader --> omop_table
    omop_table --> person_table
    omop_table --> condition_occurrence_table
    omop_database_loader --> omop_table_factory
    
    %% Common Layer
    configuration_manager --> logger
    configuration_manager --> exceptions
    
    %% External Connections
    database_connector --> source_database
    database_loader --> target_database
    omop_database_loader --> target_database
    vocabulary_service --> vocabulary_database
    
    %% Data Flow
    processing_context -.-> extractor_base
    processing_context -.-> transformation_engine
    processing_context -.-> loader_base
``` 