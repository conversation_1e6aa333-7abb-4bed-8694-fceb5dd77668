# UML Use Case Diagrams for OMOP ETL System

## Overview

This document provides UML use case diagrams for the OMOP ETL system, showing the various actors, use cases, and their relationships. These diagrams are based on the actual implementation and reflect the real capabilities of the system.

## 1. System Overview Use Case Diagram

The main use case diagram shows the primary actors and their interactions with the system.

```mermaid
graph TB
    subgraph "External Actors"
        A[Data Engineer]
        B[System Administrator]
        C[Healthcare Analyst]
        D[External System]
        E[Monitoring System]
    end
    
    subgraph "OMOP ETL System"
        subgraph "Job Management"
            UC1[Create ETL Job]
            UC2[Schedule ETL Job]
            UC3[Monitor Job Progress]
            UC4[Cancel ETL Job]
            UC5[View Job History]
        end
        
        subgraph "Data Processing"
            UC6[Extract Data]
            UC7[Transform Data]
            UC8[Load Data]
            UC9[Validate Data]
            UC10[Generate Reports]
        end
        
        subgraph "System Management"
            UC11[Configure System]
            UC12[Manage Users]
            UC13[Monitor System Health]
            UC14[Backup Data]
            UC15[Restore Data]
        end
        
        subgraph "Data Access"
            UC16[Query OMOP Data]
            UC17[Export Data]
            UC18[Generate Analytics]
            UC19[Access Vocabulary]
        end
    end
    
    A --> UC1
    A --> UC2
    A --> UC3
    A --> UC4
    A --> UC5
    A --> UC6
    A --> UC7
    A --> UC8
    A --> UC9
    A --> UC10
    A --> UC11
    
    B --> UC11
    B --> UC12
    B --> UC13
    B --> UC14
    B --> UC15
    
    C --> UC16
    C --> UC17
    C --> UC18
    C --> UC19
    
    D --> UC6
    D --> UC8
    
    E --> UC13
```

### Actor Descriptions

#### Data Engineer
- **Description**: Primary user responsible for creating and managing ETL jobs
- **Responsibilities**:
  - Configure data sources and targets
  - Create transformation mappings
  - Monitor job execution
  - Troubleshoot issues
- **Implementation**: `omop::api::ApiServer` handles requests from data engineers

#### System Administrator
- **Description**: Responsible for system configuration and maintenance
- **Responsibilities**:
  - Configure system parameters
  - Manage user accounts and permissions
  - Monitor system health
  - Perform backup and recovery
- **Implementation**: `omop::service::ServiceOrchestrator` provides admin functions

#### Healthcare Analyst
- **Description**: End user who consumes OMOP data for analysis
- **Responsibilities**:
  - Query OMOP CDM data
  - Generate reports and analytics
  - Export data for analysis
  - Access vocabulary mappings
- **Implementation**: `omop::api::ApiServer` provides data access endpoints

#### External System
- **Description**: Automated systems that interact with the ETL system
- **Responsibilities**:
  - Provide source data
  - Consume transformed data
  - Trigger ETL jobs
  - Receive notifications
- **Implementation**: `omop::service::ETLService` handles external system interactions

#### Monitoring System
- **Description**: System monitoring and alerting infrastructure
- **Responsibilities**:
  - Collect system metrics
  - Monitor system health
  - Generate alerts
  - Track performance
- **Implementation**: `omop::monitoring::MetricsCollector` provides monitoring data

## 2. Job Management Use Case Diagram

Detailed use case diagram for ETL job management functionality.

```mermaid
graph TB
    subgraph "Actors"
        A[Data Engineer]
        B[System Administrator]
        C[Scheduler]
    end
    
    subgraph "Job Management Use Cases"
        subgraph "Job Creation"
            UC1[Create ETL Job]
            UC2[Validate Configuration]
            UC3[Set Job Parameters]
            UC4[Define Data Mappings]
        end
        
        subgraph "Job Execution"
            UC5[Schedule Job]
            UC6[Start Job]
            UC7[Pause Job]
            UC8[Resume Job]
            UC9[Cancel Job]
        end
        
        subgraph "Job Monitoring"
            UC10[Monitor Progress]
            UC11[View Logs]
            UC12[Check Status]
            UC13[View Statistics]
        end
        
        subgraph "Job History"
            UC14[View Job History]
            UC15[Download Results]
            UC16[Generate Reports]
            UC17[Archive Jobs]
        end
    end
    
    A --> UC1
    A --> UC2
    A --> UC3
    A --> UC4
    A --> UC5
    A --> UC7
    A --> UC8
    A --> UC9
    A --> UC10
    A --> UC11
    A --> UC12
    A --> UC13
    A --> UC14
    A --> UC15
    A --> UC16
    
    B --> UC5
    B --> UC9
    B --> UC10
    B --> UC11
    B --> UC12
    B --> UC13
    B --> UC14
    B --> UC17
    
    C --> UC6
    C --> UC10
    C --> UC12
```

### Use Case Descriptions

#### Create ETL Job
- **Description**: Create a new ETL job with specified configuration
- **Preconditions**: User has permission to create jobs
- **Main Flow**:
  1. User provides job configuration
  2. System validates configuration
  3. System creates job record
  4. System returns job ID
- **Postconditions**: Job is created and ready for scheduling
- **Implementation**: `omop::service::ETLService::create_job()`

#### Validate Configuration
- **Description**: Validate ETL job configuration before execution
- **Preconditions**: Job configuration is provided
- **Main Flow**:
  1. System checks configuration syntax
  2. System validates source connections
  3. System validates target connections
  4. System validates transformation rules
  5. System returns validation results
- **Postconditions**: Configuration is validated or errors are reported
- **Implementation**: `omop::common::ConfigurationManager::validate_config()`

#### Schedule Job
- **Description**: Schedule an ETL job for execution
- **Preconditions**: Job is created and validated
- **Main Flow**:
  1. User specifies execution schedule
  2. System adds job to scheduler
  3. System confirms scheduling
- **Postconditions**: Job is scheduled for execution
- **Implementation**: `omop::core::JobScheduler::schedule_job()`

#### Monitor Progress
- **Description**: Monitor the progress of an ETL job
- **Preconditions**: Job is running
- **Main Flow**:
  1. System provides real-time progress updates
  2. System shows current stage
  3. System displays statistics
- **Postconditions**: User has current job status
- **Implementation**: `omop::core::ProcessingContext::get_progress()`

## 3. Data Processing Use Case Diagram

Detailed use case diagram for data processing functionality.

```mermaid
graph TB
    subgraph "Actors"
        A[Data Engineer]
        B[ETL Pipeline]
        C[External System]
    end
    
    subgraph "Data Processing Use Cases"
        subgraph "Extraction"
            UC1[Extract from CSV]
            UC2[Extract from Database]
            UC3[Extract from JSON]
            UC4[Extract from API]
            UC5[Validate Source Data]
        end
        
        subgraph "Transformation"
            UC6[Apply Field Mappings]
            UC7[Transform Dates]
            UC8[Map Vocabulary]
            UC9[Validate Data Quality]
            UC10[Apply Business Rules]
        end
        
        subgraph "Loading"
            UC11[Load to Database]
            UC12[Load to File]
            UC13[Load to API]
            UC14[Validate Constraints]
            UC15[Handle Errors]
        end
        
        subgraph "Quality Assurance"
            UC16[Generate Quality Report]
            UC17[Detect Anomalies]
            UC18[Track Data Lineage]
            UC19[Audit Changes]
        end
    end
    
    A --> UC1
    A --> UC2
    A --> UC3
    A --> UC4
    A --> UC6
    A --> UC7
    A --> UC8
    A --> UC9
    A --> UC10
    A --> UC11
    A --> UC12
    A --> UC13
    A --> UC16
    A --> UC17
    A --> UC18
    A --> UC19
    
    B --> UC1
    B --> UC2
    B --> UC3
    B --> UC4
    B --> UC5
    B --> UC6
    B --> UC7
    B --> UC8
    B --> UC9
    B --> UC10
    B --> UC11
    B --> UC12
    B --> UC13
    B --> UC14
    B --> UC15
    
    C --> UC1
    C --> UC2
    C --> UC3
    C --> UC4
    C --> UC11
    C --> UC12
    C --> UC13
```

### Use Case Descriptions

#### Extract from CSV
- **Description**: Extract data from CSV file sources
- **Preconditions**: CSV file is accessible and properly formatted
- **Main Flow**:
  1. System reads CSV file
  2. System parses CSV structure
  3. System converts to internal format
  4. System validates data
- **Postconditions**: Data is extracted and ready for transformation
- **Implementation**: `omop::extract::CSVExtractor::extract_batch()`

#### Apply Field Mappings
- **Description**: Apply field mappings to transform data structure
- **Preconditions**: Source data and mapping configuration are available
- **Main Flow**:
  1. System reads mapping configuration
  2. System applies direct field mappings
  3. System applies transformation rules
  4. System validates transformed data
- **Postconditions**: Data is transformed according to mappings
- **Implementation**: `omop::transform::TransformationEngine::transform()`

#### Load to Database
- **Description**: Load transformed data into target database
- **Preconditions**: Data is transformed and target database is accessible
- **Main Flow**:
  1. System connects to target database
  2. System validates database schema
  3. System inserts data in batches
  4. System handles constraints and errors
- **Postconditions**: Data is loaded into target database
- **Implementation**: `omop::load::DatabaseLoader::load_batch()`

## 4. System Administration Use Case Diagram

Detailed use case diagram for system administration functionality.

```mermaid
graph TB
    subgraph "Actors"
        A[System Administrator]
        B[Security Manager]
        C[Database Administrator]
    end
    
    subgraph "System Administration Use Cases"
        subgraph "User Management"
            UC1[Create User Account]
            UC2[Assign User Roles]
            UC3[Manage Permissions]
            UC4[Deactivate User]
            UC5[Audit User Activity]
        end
        
        subgraph "System Configuration"
            UC6[Configure System Parameters]
            UC7[Manage Database Connections]
            UC8[Configure Security Settings]
            UC9[Set Performance Parameters]
            UC10[Configure Monitoring]
        end
        
        subgraph "System Maintenance"
            UC11[Backup System Data]
            UC12[Restore System Data]
            UC13[Update System Components]
            UC14[Monitor System Health]
            UC15[Generate System Reports]
        end
        
        subgraph "Security Management"
            UC16[Configure Authentication]
            UC17[Manage Encryption Keys]
            UC18[Configure Audit Logging]
            UC19[Monitor Security Events]
        end
    end
    
    A --> UC1
    A --> UC2
    A --> UC3
    A --> UC4
    A --> UC5
    A --> UC6
    A --> UC7
    A --> UC8
    A --> UC9
    A --> UC10
    A --> UC11
    A --> UC12
    A --> UC13
    A --> UC14
    A --> UC15
    
    B --> UC16
    B --> UC17
    B --> UC18
    B --> UC19
    
    C --> UC7
    C --> UC11
    C --> UC12
```

### Use Case Descriptions

#### Create User Account
- **Description**: Create a new user account in the system
- **Preconditions**: Administrator has permission to create users
- **Main Flow**:
  1. Administrator provides user details
  2. System validates user information
  3. System creates user account
  4. System assigns default permissions
- **Postconditions**: User account is created and ready for use
- **Implementation**: `omop::security::AuthManager::create_user()`

#### Configure System Parameters
- **Description**: Configure system-wide parameters and settings
- **Preconditions**: Administrator has configuration permissions
- **Main Flow**:
  1. Administrator accesses configuration interface
  2. System loads current configuration
  3. Administrator modifies parameters
  4. System validates changes
  5. System applies configuration
- **Postconditions**: System configuration is updated
- **Implementation**: `omop::common::ConfigurationManager::update_config()`

#### Monitor System Health
- **Description**: Monitor overall system health and performance
- **Preconditions**: Monitoring is configured
- **Main Flow**:
  1. System collects health metrics
  2. System analyzes performance data
  3. System generates health report
  4. System sends alerts if needed
- **Postconditions**: System health status is available
- **Implementation**: `omop::monitoring::MetricsCollector::collect_health_metrics()`

## 5. Data Access Use Case Diagram

Detailed use case diagram for data access and analytics functionality.

```mermaid
graph TB
    subgraph "Actors"
        A[Healthcare Analyst]
        B[Data Scientist]
        C[Reporting System]
    end
    
    subgraph "Data Access Use Cases"
        subgraph "Data Querying"
            UC1[Query Patient Data]
            UC2[Query Condition Data]
            UC3[Query Medication Data]
            UC4[Query Procedure Data]
            UC5[Execute Custom Queries]
        end
        
        subgraph "Data Export"
            UC6[Export to CSV]
            UC7[Export to JSON]
            UC8[Export to Database]
            UC9[Generate Data Dumps]
            UC10[Create Data Snapshots]
        end
        
        subgraph "Analytics"
            UC11[Generate Patient Reports]
            UC12[Create Population Analytics]
            UC13[Perform Statistical Analysis]
            UC14[Generate Quality Metrics]
            UC15[Create Visualizations]
        end
        
        subgraph "Vocabulary Access"
            UC16[Search Concepts]
            UC17[Map Codes]
            UC18[Validate Terminology]
            UC19[Access Vocabulary Metadata]
        end
    end
    
    A --> UC1
    A --> UC2
    A --> UC3
    A --> UC4
    A --> UC6
    A --> UC7
    A --> UC11
    A --> UC12
    A --> UC14
    A --> UC16
    A --> UC17
    A --> UC18
    A --> UC19
    
    B --> UC5
    B --> UC8
    B --> UC9
    B --> UC10
    B --> UC13
    B --> UC15
    B --> UC16
    B --> UC17
    B --> UC18
    B --> UC19
    
    C --> UC1
    C --> UC2
    C --> UC3
    C --> UC4
    C --> UC6
    C --> UC7
    C --> UC11
    C --> UC12
    C --> UC14
```

### Use Case Descriptions

#### Query Patient Data
- **Description**: Query patient demographic and clinical data
- **Preconditions**: User has permission to access patient data
- **Main Flow**:
  1. User specifies query criteria
  2. System validates query permissions
  3. System executes query against OMOP CDM
  4. System returns results
- **Postconditions**: Patient data is retrieved according to query
- **Implementation**: `omop::api::ApiServer::query_patients()`

#### Generate Patient Reports
- **Description**: Generate standardized patient reports
- **Preconditions**: Patient data is available and user has access
- **Main Flow**:
  1. User selects report type
  2. User specifies patient criteria
  3. System generates report
  4. System formats output
- **Postconditions**: Patient report is generated and available
- **Implementation**: `omop::api::ApiServer::generate_patient_report()`

#### Search Concepts
- **Description**: Search OMOP vocabulary concepts
- **Preconditions**: Vocabulary database is accessible
- **Main Flow**:
  1. User provides search terms
  2. System searches vocabulary database
  3. System returns matching concepts
  4. System provides concept details
- **Postconditions**: Vocabulary concepts are found and displayed
- **Implementation**: `omop::transform::VocabularyService::search_concepts()`

## Implementation Notes

### Use Case Realization
- Each use case is implemented through specific API endpoints
- Use cases are mapped to service layer methods
- Business logic is encapsulated in domain services
- Data access is abstracted through repository patterns

### Security Considerations
- All use cases include permission checks
- Data access is controlled by role-based access control
- Sensitive operations are logged for audit purposes
- Input validation is performed for all user inputs

### Performance Considerations
- Use cases are optimized for performance
- Database queries are optimized and cached where appropriate
- Batch processing is used for large data operations
- Asynchronous processing is used for long-running operations

### Error Handling
- Each use case includes comprehensive error handling
- User-friendly error messages are provided
- Error logging is performed for debugging
- Graceful degradation is implemented where possible

### Testing
- Use cases are covered by unit tests
- Integration tests verify use case workflows
- Performance tests validate use case performance
- Security tests ensure proper access control 