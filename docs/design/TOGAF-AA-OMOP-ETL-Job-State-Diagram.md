# High Level Design: OMOP ETL Job State Diagram

```mermaid
stateDiagram-v2
    [*] --> Created: Job Created
    Created --> Initializing: Start Processing
    Initializing --> Running: Components Ready
    Running --> Completed: Success
    Running --> Failed: Error
    Running --> Paused: Manual Pause
    Paused --> Running: Resume
    Failed --> Running: Retry
    Failed --> Terminated: Abort
    Completed --> [*]
    Terminated --> [*]

    state Running {
        Extract --> Transform: Data Available
        Transform --> Load: Transformation Complete
        Load --> Extract: More Data
        Load --> [*]: All Data Processed
    }

    note right of Failed : Error handling via OmopException
    note right of Running : Checkpoints managed by ProcessingContext
    note right of Initializing : Components initialized via ComponentFactory
```
