# Deployment Architecture for OMOP ETL System

## Overview

This document describes the deployment architecture for the OMOP ETL system, including containerization strategies, cloud deployment options, and infrastructure considerations. The deployment architecture is designed to support scalability, high availability, and security requirements for healthcare data processing.

## 1. Container Architecture

### Docker Container Structure

The system is designed to run in Docker containers with a microservices architecture.

```mermaid
graph TB
    subgraph "Container Registry"
        A[Base Image: ubuntu:22.04]
        B[Runtime Image: c++17-runtime]
        C[Development Image: c++17-dev]
    end
    
    subgraph "Application Containers"
        D[API Server Container]
        E[ETL Service Container]
        F[Extract Service Container]
        G[Transform Service Container]
        H[Load Service Container]
        I[Orchestrator Container]
    end
    
    subgraph "Infrastructure Containers"
        J[PostgreSQL Container]
        K[Redis Container]
        L[Prometheus Container]
        M[Grafana Container]
        N[Nginx Container]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    C --> I
    
    D --> N
    E --> J
    F --> J
    G --> J
    H --> J
    I --> K
    L --> M
```

### Container Specifications

#### Base Images
- **Runtime Base**: `ubuntu:22.04` with C++17 runtime libraries
- **Development Base**: `ubuntu:22.04` with full development toolchain
- **Multi-arch Support**: x86_64, ARM64 (Apple Silicon, AWS Graviton)

#### Application Containers

##### API Server Container
```dockerfile
FROM omop-etl/cpp17-runtime:latest
COPY --from=builder /app/api-server /usr/local/bin/
COPY config/api/ /etc/omop/api/
EXPOSE 8080
CMD ["/usr/local/bin/api-server"]
```

##### ETL Service Container
```dockerfile
FROM omop-etl/cpp17-runtime:latest
COPY --from=builder /app/etl-service /usr/local/bin/
COPY config/etl/ /etc/omop/etl/
EXPOSE 9090
CMD ["/usr/local/bin/etl-service"]
```

##### Extract Service Container
```dockerfile
FROM omop-etl/cpp17-runtime:latest
COPY --from=builder /app/extract-service /usr/local/bin/
COPY config/extract/ /etc/omop/extract/
EXPOSE 9091
CMD ["/usr/local/bin/extract-service"]
```

##### Transform Service Container
```dockerfile
FROM omop-etl/cpp17-runtime:latest
COPY --from=builder /app/transform-service /usr/local/bin/
COPY config/transform/ /etc/omop/transform/
EXPOSE 9092
CMD ["/usr/local/bin/transform-service"]
```

##### Load Service Container
```dockerfile
FROM omop-etl/cpp17-runtime:latest
COPY --from=builder /app/load-service /usr/local/bin/
COPY config/load/ /etc/omop/load/
EXPOSE 9093
CMD ["/usr/local/bin/load-service"]
```

## 2. Kubernetes Deployment

### Cluster Architecture

```mermaid
graph TB
    subgraph "Kubernetes Cluster"
        subgraph "Control Plane"
            A[API Server]
            B[etcd]
            C[Scheduler]
            D[Controller Manager]
        end
        
        subgraph "Worker Nodes"
            subgraph "Node 1"
                E[API Server Pod]
                F[ETL Service Pod]
                G[PostgreSQL Pod]
            end
            
            subgraph "Node 2"
                H[Extract Service Pod]
                I[Transform Service Pod]
                J[Redis Pod]
            end
            
            subgraph "Node 3"
                K[Load Service Pod]
                L[Orchestrator Pod]
                M[Monitoring Pods]
            end
        end
        
        subgraph "Storage"
            N[Persistent Volumes]
            O[ConfigMaps]
            P[Secrets]
        end
        
        subgraph "Networking"
            Q[Load Balancer]
            R[Ingress Controller]
            S[Service Mesh]
        end
    end
    
    A --> B
    A --> C
    A --> D
    
    E --> N
    F --> N
    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
    
    Q --> R
    R --> S
    S --> E
    S --> F
    S --> H
    S --> I
    S --> K
    S --> L
```

### Deployment Manifests

#### API Server Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: omop-api-server
  namespace: omop-etl
spec:
  replicas: 3
  selector:
    matchLabels:
      app: omop-api-server
  template:
    metadata:
      labels:
        app: omop-api-server
    spec:
      containers:
      - name: api-server
        image: omop-etl/api-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### ETL Service Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: omop-etl-service
  namespace: omop-etl
spec:
  replicas: 2
  selector:
    matchLabels:
      app: omop-etl-service
  template:
    metadata:
      labels:
        app: omop-etl-service
    spec:
      containers:
      - name: etl-service
        image: omop-etl/etl-service:latest
        ports:
        - containerPort: 9090
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: EXTRACT_SERVICE_URL
          value: "http://omop-extract-service:9091"
        - name: TRANSFORM_SERVICE_URL
          value: "http://omop-transform-service:9092"
        - name: LOAD_SERVICE_URL
          value: "http://omop-load-service:9093"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: config-volume
          mountPath: /etc/omop/etl
        - name: data-volume
          mountPath: /var/omop/data
      volumes:
      - name: config-volume
        configMap:
          name: etl-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: etl-data-pvc
```

### Service Configuration

#### API Server Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: omop-api-server
  namespace: omop-etl
spec:
  selector:
    app: omop-api-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

#### Internal Services
```yaml
apiVersion: v1
kind: Service
metadata:
  name: omop-etl-service
  namespace: omop-etl
spec:
  selector:
    app: omop-etl-service
  ports:
  - protocol: TCP
    port: 9090
    targetPort: 9090
  type: ClusterIP
```

### Ingress Configuration
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: omop-etl-ingress
  namespace: omop-etl
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.omop-etl.example.com
    secretName: omop-etl-tls
  rules:
  - host: api.omop-etl.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: omop-api-server
            port:
              number: 80
```

## 3. Cloud Deployment Options

### AWS Deployment

#### EKS (Elastic Kubernetes Service) Architecture

```mermaid
graph TB
    subgraph "AWS Cloud"
        subgraph "VPC"
            subgraph "Public Subnets"
                A[Internet Gateway]
                B[NAT Gateway]
                C[Load Balancer]
            end
            
            subgraph "Private Subnets"
                subgraph "EKS Cluster"
                    D[API Server Pods]
                    E[ETL Service Pods]
                    F[Worker Pods]
                end
                
                subgraph "RDS"
                    G[PostgreSQL Primary]
                    H[PostgreSQL Read Replicas]
                end
                
                subgraph "ElastiCache"
                    I[Redis Cluster]
                end
                
                subgraph "S3"
                    J[Data Storage]
                    K[Log Storage]
                end
            end
        end
        
        subgraph "Monitoring"
            L[CloudWatch]
            M[X-Ray]
            N[Prometheus]
        end
    end
    
    A --> C
    C --> D
    C --> E
    C --> F
    
    D --> G
    E --> G
    F --> G
    G --> H
    
    D --> I
    E --> I
    F --> I
    
    D --> J
    E --> J
    F --> J
    D --> K
    E --> K
    F --> K
    
    D --> L
    E --> L
    F --> L
    D --> M
    E --> M
    F --> M
    D --> N
    E --> N
    F --> N
```

#### AWS Infrastructure as Code (Terraform)

```hcl
# EKS Cluster
resource "aws_eks_cluster" "omop_etl" {
  name     = "omop-etl-cluster"
  role_arn = aws_iam_role.eks_cluster.arn
  version  = "1.28"

  vpc_config {
    subnet_ids              = var.private_subnet_ids
    endpoint_private_access = true
    endpoint_public_access  = false
  }

  enabled_cluster_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
    aws_iam_role_policy_attachment.eks_vpc_resource_controller,
  ]
}

# RDS PostgreSQL
resource "aws_db_instance" "omop_postgresql" {
  identifier = "omop-postgresql"
  
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.r6g.xlarge"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type          = "gp3"
  storage_encrypted     = true
  
  db_name  = "omop_cdm"
  username = var.db_username
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.omop.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  multi_az = true
  
  deletion_protection = true
  
  tags = {
    Name = "OMOP PostgreSQL"
  }
}

# ElastiCache Redis
resource "aws_elasticache_cluster" "omop_redis" {
  cluster_id           = "omop-redis"
  engine               = "redis"
  node_type            = "cache.r6g.large"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  security_group_ids   = [aws_security_group.redis.id]
  subnet_group_name    = aws_elasticache_subnet_group.omop.name
}

# S3 Bucket for Data Storage
resource "aws_s3_bucket" "omop_data" {
  bucket = "omop-etl-data-${random_string.bucket_suffix.result}"
}

resource "aws_s3_bucket_versioning" "omop_data" {
  bucket = aws_s3_bucket.omop_data.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "omop_data" {
  bucket = aws_s3_bucket.omop_data.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
```

### Azure Deployment

#### AKS (Azure Kubernetes Service) Architecture

```mermaid
graph TB
    subgraph "Azure Cloud"
        subgraph "Virtual Network"
            subgraph "Public Subnets"
                A[Internet Gateway]
                B[NAT Gateway]
                C[Application Gateway]
            end
            
            subgraph "Private Subnets"
                subgraph "AKS Cluster"
                    D[API Server Pods]
                    E[ETL Service Pods]
                    F[Worker Pods]
                end
                
                subgraph "Azure Database for PostgreSQL"
                    G[PostgreSQL Flexible Server]
                end
                
                subgraph "Azure Cache for Redis"
                    H[Redis Cache]
                end
                
                subgraph "Azure Storage"
                    I[Blob Storage]
                    J[File Storage]
                end
            end
        end
        
        subgraph "Monitoring"
            K[Azure Monitor]
            L[Application Insights]
            M[Log Analytics]
        end
    end
    
    A --> C
    C --> D
    C --> E
    C --> F
    
    D --> G
    E --> G
    F --> G
    
    D --> H
    E --> H
    F --> H
    
    D --> I
    E --> I
    F --> I
    D --> J
    E --> J
    F --> J
    
    D --> K
    E --> K
    F --> K
    D --> L
    E --> L
    F --> L
    D --> M
    E --> M
    F --> M
```

### Google Cloud Deployment

#### GKE (Google Kubernetes Engine) Architecture

```mermaid
graph TB
    subgraph "Google Cloud"
        subgraph "VPC Network"
            subgraph "Public Subnets"
                A[Cloud Load Balancer]
                B[Cloud NAT]
            end
            
            subgraph "Private Subnets"
                subgraph "GKE Cluster"
                    C[API Server Pods]
                    D[ETL Service Pods]
                    E[Worker Pods]
                end
                
                subgraph "Cloud SQL"
                    F[PostgreSQL Instance]
                end
                
                subgraph "Memorystore"
                    G[Redis Instance]
                end
                
                subgraph "Cloud Storage"
                    H[Data Buckets]
                    I[Log Buckets]
                end
            end
        end
        
        subgraph "Monitoring"
            J[Cloud Monitoring]
            K[Cloud Logging]
            L[Cloud Trace]
        end
    end
    
    A --> C
    A --> D
    A --> E
    
    C --> F
    D --> F
    E --> F
    
    C --> G
    D --> G
    E --> G
    
    C --> H
    D --> H
    E --> H
    C --> I
    D --> I
    E --> I
    
    C --> J
    D --> J
    E --> J
    C --> K
    D --> K
    E --> K
    C --> L
    D --> L
    E --> L
```

## 4. On-Premises Deployment

### Traditional Infrastructure

```mermaid
graph TB
    subgraph "Data Center"
        subgraph "Network Layer"
            A[Load Balancer]
            B[Firewall]
            C[VPN Gateway]
        end
        
        subgraph "Application Layer"
            D[API Server VMs]
            E[ETL Service VMs]
            F[Worker VMs]
        end
        
        subgraph "Database Layer"
            G[PostgreSQL Primary]
            H[PostgreSQL Replica]
            I[Redis Cluster]
        end
        
        subgraph "Storage Layer"
            J[NAS Storage]
            K[Backup Storage]
        end
        
        subgraph "Monitoring"
            L[Prometheus Server]
            M[Grafana Dashboard]
            N[ELK Stack]
        end
    end
    
    A --> D
    A --> E
    A --> F
    
    D --> G
    E --> G
    F --> G
    G --> H
    
    D --> I
    E --> I
    F --> I
    
    D --> J
    E --> J
    F --> J
    D --> K
    E --> K
    F --> K
    
    D --> L
    E --> L
    F --> L
    L --> M
    D --> N
    E --> N
    F --> N
```

### Virtual Machine Specifications

#### API Server VM
- **CPU**: 4 vCPUs
- **Memory**: 8 GB RAM
- **Storage**: 100 GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Network**: 1 Gbps

#### ETL Service VM
- **CPU**: 8 vCPUs
- **Memory**: 16 GB RAM
- **Storage**: 200 GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Network**: 1 Gbps

#### Database VM
- **CPU**: 16 vCPUs
- **Memory**: 32 GB RAM
- **Storage**: 1 TB SSD (RAID 1)
- **OS**: Ubuntu 22.04 LTS
- **Network**: 10 Gbps

## 5. Security Architecture

### Network Security

```mermaid
graph TB
    subgraph "Internet"
        A[External Users]
        B[External Systems]
    end
    
    subgraph "DMZ"
        C[Load Balancer]
        D[WAF]
        E[Bastion Host]
    end
    
    subgraph "Application Network"
        F[API Servers]
        G[ETL Services]
        H[Worker Nodes]
    end
    
    subgraph "Database Network"
        I[Database Servers]
        J[Cache Servers]
    end
    
    subgraph "Management Network"
        K[Monitoring Servers]
        L[Backup Servers]
    end
    
    A --> C
    B --> C
    C --> D
    D --> F
    E --> F
    E --> G
    E --> H
    
    F --> I
    G --> I
    H --> I
    F --> J
    G --> J
    H --> J
    
    F --> K
    G --> K
    H --> K
    I --> L
```

### Security Controls

#### Network Security
- **Firewalls**: Stateful packet inspection
- **WAF**: Web Application Firewall for API protection
- **VPN**: Secure remote access
- **Network Segmentation**: Isolated network zones
- **DDoS Protection**: Distributed denial of service protection

#### Application Security
- **Authentication**: Multi-factor authentication
- **Authorization**: Role-based access control
- **Encryption**: TLS 1.3 for data in transit
- **Input Validation**: Comprehensive input sanitization
- **Audit Logging**: Complete audit trail

#### Data Security
- **Encryption at Rest**: AES-256 encryption
- **Encryption in Transit**: TLS/SSL encryption
- **Data Masking**: Sensitive data masking
- **Backup Encryption**: Encrypted backups
- **Key Management**: Hardware security modules (HSM)

## 6. Monitoring and Observability

### Monitoring Stack

```mermaid
graph TB
    subgraph "Application Layer"
        A[API Server]
        B[ETL Service]
        C[Worker Nodes]
    end
    
    subgraph "Data Collection"
        D[Prometheus]
        E[Fluentd]
        F[Jaeger]
    end
    
    subgraph "Storage"
        G[Time Series DB]
        H[Log Storage]
        I[Trace Storage]
    end
    
    subgraph "Visualization"
        J[Grafana]
        K[Kibana]
        L[Jaeger UI]
    end
    
    subgraph "Alerting"
        M[Alert Manager]
        N[PagerDuty]
        O[Email/SMS]
    end
    
    A --> D
    B --> D
    C --> D
    A --> E
    B --> E
    C --> E
    A --> F
    B --> F
    C --> F
    
    D --> G
    E --> H
    F --> I
    
    G --> J
    H --> K
    I --> L
    
    D --> M
    M --> N
    M --> O
```

### Key Metrics

#### Application Metrics
- **Request Rate**: Requests per second
- **Response Time**: Average and percentile response times
- **Error Rate**: Percentage of failed requests
- **Throughput**: Records processed per second
- **Queue Depth**: Number of pending jobs

#### Infrastructure Metrics
- **CPU Usage**: CPU utilization percentage
- **Memory Usage**: Memory utilization percentage
- **Disk I/O**: Read/write operations per second
- **Network I/O**: Network bytes per second
- **Container Metrics**: Container resource usage

#### Business Metrics
- **Job Success Rate**: Percentage of successful ETL jobs
- **Data Quality**: Data quality scores
- **Processing Time**: Time to complete ETL jobs
- **Data Volume**: Amount of data processed
- **User Activity**: Number of active users

## 7. Backup and Disaster Recovery

### Backup Strategy

```mermaid
graph TB
    subgraph "Production Environment"
        A[Application Data]
        B[Database Data]
        C[Configuration Data]
    end
    
    subgraph "Backup Storage"
        D[Local Backup]
        E[Remote Backup]
        F[Cloud Backup]
    end
    
    subgraph "Recovery Environment"
        G[Standby Database]
        H[Disaster Recovery Site]
        I[Cloud DR]
    end
    
    A --> D
    B --> D
    C --> D
    
    A --> E
    B --> E
    C --> E
    
    A --> F
    B --> F
    C --> F
    
    D --> G
    E --> H
    F --> I
```

### Recovery Procedures

#### Database Recovery
- **Point-in-Time Recovery**: Restore to specific timestamp
- **Full Backup Recovery**: Restore from full backup
- **Incremental Recovery**: Apply incremental backups
- **Cross-Region Recovery**: Restore to different region

#### Application Recovery
- **Configuration Recovery**: Restore configuration files
- **Code Recovery**: Deploy application from version control
- **Data Recovery**: Restore application data
- **Service Recovery**: Restart services in correct order

#### Disaster Recovery
- **RTO (Recovery Time Objective)**: 4 hours
- **RPO (Recovery Point Objective)**: 1 hour
- **Failover Procedures**: Automated failover to DR site
- **Testing**: Monthly disaster recovery testing

## 8. Performance Optimization

### Scaling Strategies

#### Horizontal Scaling
- **Auto-scaling**: Automatic scaling based on load
- **Load Balancing**: Distribute load across instances
- **Database Sharding**: Distribute data across multiple databases
- **Microservices**: Independent scaling of services

#### Vertical Scaling
- **Resource Allocation**: Optimize CPU and memory allocation
- **Database Tuning**: Optimize database performance
- **Caching**: Implement multi-level caching
- **Connection Pooling**: Optimize database connections

### Performance Monitoring

#### Real-time Monitoring
- **APM Tools**: Application performance monitoring
- **Database Monitoring**: Database performance metrics
- **Infrastructure Monitoring**: System resource monitoring
- **User Experience Monitoring**: End-user performance metrics

#### Performance Testing
- **Load Testing**: Test system under expected load
- **Stress Testing**: Test system under maximum load
- **Endurance Testing**: Test system over extended periods
- **Spike Testing**: Test system response to sudden load increases

## Implementation Notes

### Deployment Automation
- **CI/CD Pipeline**: Automated build and deployment
- **Infrastructure as Code**: Version-controlled infrastructure
- **Configuration Management**: Automated configuration deployment
- **Rollback Procedures**: Automated rollback capabilities

### Compliance Considerations
- **HIPAA Compliance**: Healthcare data protection
- **GDPR Compliance**: Data privacy protection
- **SOC 2 Compliance**: Security and availability
- **Audit Requirements**: Comprehensive audit trails

### Cost Optimization
- **Resource Right-sizing**: Optimize resource allocation
- **Reserved Instances**: Use reserved instances for predictable workloads
- **Spot Instances**: Use spot instances for non-critical workloads
- **Storage Optimization**: Optimize storage costs and performance 