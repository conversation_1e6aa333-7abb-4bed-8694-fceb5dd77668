# UML State Diagrams for OMOP ETL System

## Overview

This document provides UML state diagrams for the key components of the OMOP ETL system, showing the various states and transitions that occur during system operation. These diagrams are based on the actual implementation in the source code.

## 1. ETL Job State Diagram

The ETL job state diagram shows the lifecycle of an ETL job from creation to completion.

```mermaid
stateDiagram-v2
    [*] --> Created : Job Created
    
    Created --> Validating : Validate Configuration
    Validating --> Validated : Configuration Valid
    Validating --> Failed : Configuration Invalid
    
    Validated --> Queued : Add to Queue
    Queued --> Initializing : Start Job
    
    Initializing --> Initialized : Resources Allocated
    Initializing --> Failed : Resource Allocation Failed
    
    Initialized --> Extracting : Begin Extraction
    Extracting --> Extracted : Extraction Complete
    Extracting --> Failed : Extraction Failed
    Extracting --> Paused : Pause Requested
    
    Paused --> Extracting : Resume Requested
    Paused --> Cancelled : Cancel Requested
    
    Extracted --> Transforming : Begin Transformation
    Transforming --> Transformed : Transformation Complete
    Transforming --> Failed : Transformation Failed
    Transforming --> Paused : Pause Requested
    
    Transformed --> Loading : Begin Loading
    Loading --> Loaded : Loading Complete
    Loading --> Failed : Loading Failed
    Loading --> Paused : Pause Requested
    
    Loaded --> Finalizing : Begin Finalization
    Finalizing --> Completed : Finalization Complete
    Finalizing --> Failed : Finalization Failed
    
    Failed --> Retrying : Retry Requested
    Retrying --> Initializing : Retry Started
    Retrying --> Failed : Max Retries Exceeded
    
    Cancelled --> [*] : Job Cancelled
    Completed --> [*] : Job Complete
    Failed --> [*] : Job Failed
```

### State Descriptions

#### Created
- **Description**: Job has been created but not yet validated
- **Actions**: Job metadata stored, configuration loaded
- **Implementation**: `omop::service::ETLService::create_job()`

#### Validating
- **Description**: Validating job configuration and dependencies
- **Actions**: Check configuration syntax, validate source/target connections
- **Implementation**: `omop::common::ConfigurationManager::validate_config()`

#### Validated
- **Description**: Configuration is valid and job is ready for execution
- **Actions**: Configuration stored, job ready for queuing
- **Implementation**: `omop::service::ETLService::validate_job()`

#### Queued
- **Description**: Job is waiting in the execution queue
- **Actions**: Job added to scheduler queue, priority assigned
- **Implementation**: `omop::core::JobScheduler::queue_job()`

#### Initializing
- **Description**: Allocating resources and initializing components
- **Actions**: Create pipeline, allocate memory, establish connections
- **Implementation**: `omop::core::ETLPipeline::initialize()`

#### Initialized
- **Description**: All resources allocated and ready for processing
- **Actions**: Components initialized, connections established
- **Implementation**: `omop::core::ETLPipeline::is_initialized()`

#### Extracting
- **Description**: Extracting data from source systems
- **Actions**: Read data from sources, convert to internal format
- **Implementation**: `omop::extract::ExtractorBase::extract_batch()`

#### Extracted
- **Description**: Data extraction completed successfully
- **Actions**: Source data fully extracted, statistics recorded
- **Implementation**: `omop::extract::ExtractorBase::finalize()`

#### Transforming
- **Description**: Transforming data into OMOP CDM format
- **Actions**: Apply transformations, validate data quality
- **Implementation**: `omop::transform::TransformationEngine::transform()`

#### Transformed
- **Description**: Data transformation completed successfully
- **Actions**: Data converted to OMOP format, quality metrics recorded
- **Implementation**: `omop::transform::TransformationEngine::finalize()`

#### Loading
- **Description**: Loading transformed data into target systems
- **Actions**: Insert data into target database, handle constraints
- **Implementation**: `omop::load::LoaderBase::load_batch()`

#### Loaded
- **Description**: Data loading completed successfully
- **Actions**: Data committed to target, statistics recorded
- **Implementation**: `omop::load::LoaderBase::finalize()`

#### Finalizing
- **Description**: Finalizing job and cleaning up resources
- **Actions**: Commit transactions, release resources, generate reports
- **Implementation**: `omop::core::ETLPipeline::finalize()`

#### Completed
- **Description**: Job completed successfully
- **Actions**: Resources released, results stored, notifications sent
- **Implementation**: `omop::service::ETLService::complete_job()`

#### Failed
- **Description**: Job failed during execution
- **Actions**: Rollback transactions, log errors, send notifications
- **Implementation**: `omop::service::ETLService::fail_job()`

#### Cancelled
- **Description**: Job was cancelled by user or system
- **Actions**: Stop processing, rollback changes, release resources
- **Implementation**: `omop::service::ETLService::cancel_job()`

#### Paused
- **Description**: Job execution is paused
- **Actions**: Suspend processing, maintain state, wait for resume
- **Implementation**: `omop::core::ETLPipeline::pause()`

#### Retrying
- **Description**: Job is being retried after failure
- **Actions**: Reset state, increment retry count, restart processing
- **Implementation**: `omop::core::JobScheduler::retry_job()`

## 2. Pipeline State Diagram

The pipeline state diagram shows the internal state transitions of the ETL pipeline during execution.

```mermaid
stateDiagram-v2
    [*] --> Idle : Pipeline Created
    
    Idle --> Configuring : Load Configuration
    Configuring --> Configured : Configuration Loaded
    Configuring --> Error : Configuration Error
    
    Configured --> CreatingComponents : Create Components
    CreatingComponents --> ComponentsCreated : Components Ready
    CreatingComponents --> Error : Component Creation Failed
    
    ComponentsCreated --> Initializing : Initialize Components
    Initializing --> Initialized : All Components Ready
    Initializing --> Error : Initialization Failed
    
    Initialized --> Running : Start Processing
    Running --> Processing : Processing Data
    Processing --> Processing : Continue Processing
    Processing --> Paused : Pause Requested
    Processing --> Error : Processing Error
    Processing --> Completed : All Data Processed
    
    Paused --> Processing : Resume Requested
    Paused --> Stopping : Stop Requested
    
    Stopping --> Stopped : Processing Stopped
    Stopped --> CleaningUp : Begin Cleanup
    
    Error --> Recovering : Attempt Recovery
    Recovering --> Processing : Recovery Successful
    Recovering --> Error : Recovery Failed
    
    Completed --> CleaningUp : Begin Cleanup
    CleaningUp --> CleanedUp : Cleanup Complete
    CleanedUp --> [*] : Pipeline Destroyed
    
    Error --> CleaningUp : Begin Cleanup
    Stopped --> CleaningUp : Begin Cleanup
```

### State Descriptions

#### Idle
- **Description**: Pipeline created but not configured
- **Actions**: Pipeline object created, default state set
- **Implementation**: `omop::core::ETLPipeline::ETLPipeline()`

#### Configuring
- **Description**: Loading and validating pipeline configuration
- **Actions**: Parse YAML config, validate parameters
- **Implementation**: `omop::core::ETLPipeline::configure()`

#### Configured
- **Description**: Configuration loaded and validated
- **Actions**: Configuration stored, ready for component creation
- **Implementation**: `omop::core::ETLPipeline::is_configured()`

#### CreatingComponents
- **Description**: Creating extractor, transformer, and loader components
- **Actions**: Use component factory to create instances
- **Implementation**: `omop::core::ComponentFactory::create_component()`

#### ComponentsCreated
- **Description**: All components created successfully
- **Actions**: Components instantiated, ready for initialization
- **Implementation**: `omop::core::ETLPipeline::create_components()`

#### Initializing
- **Description**: Initializing all pipeline components
- **Actions**: Initialize extractor, transformer, loader
- **Implementation**: `omop::core::ETLPipeline::initialize_components()`

#### Initialized
- **Description**: All components initialized and ready
- **Actions**: Components ready for data processing
- **Implementation**: `omop::core::ETLPipeline::is_initialized()`

#### Running
- **Description**: Pipeline is actively processing data
- **Actions**: Start data flow through pipeline
- **Implementation**: `omop::core::ETLPipeline::run()`

#### Processing
- **Description**: Actively processing data batches
- **Actions**: Extract, transform, load data batches
- **Implementation**: `omop::core::ETLPipeline::process_batch()`

#### Paused
- **Description**: Processing is temporarily suspended
- **Actions**: Suspend processing, maintain state
- **Implementation**: `omop::core::ETLPipeline::pause()`

#### Stopping
- **Description**: Pipeline is being stopped
- **Actions**: Stop processing, prepare for cleanup
- **Implementation**: `omop::core::ETLPipeline::stop()`

#### Stopped
- **Description**: Processing has been stopped
- **Actions**: Processing halted, ready for cleanup
- **Implementation**: `omop::core::ETLPipeline::is_stopped()`

#### Error
- **Description**: Error occurred during processing
- **Actions**: Log error, attempt recovery if possible
- **Implementation**: `omop::core::ETLPipeline::handle_error()`

#### Recovering
- **Description**: Attempting to recover from error
- **Actions**: Reset components, retry operation
- **Implementation**: `omop::core::ETLPipeline::recover()`

#### Completed
- **Description**: All data processing completed successfully
- **Actions**: Finalize processing, prepare results
- **Implementation**: `omop::core::ETLPipeline::complete()`

#### CleaningUp
- **Description**: Cleaning up resources and finalizing
- **Actions**: Release resources, close connections, finalize components
- **Implementation**: `omop::core::ETLPipeline::cleanup()`

#### CleanedUp
- **Description**: All cleanup completed
- **Actions**: Resources released, pipeline ready for destruction
- **Implementation**: `omop::core::ETLPipeline::~ETLPipeline()`

## 3. Processing Context State Diagram

The processing context state diagram shows how the processing context tracks the current stage and state of ETL operations.

```mermaid
stateDiagram-v2
    [*] --> Created : Context Created
    
    Created --> Extract : Set Stage to Extract
    Extract --> Extracting : Begin Extraction
    Extracting --> ExtractComplete : Extraction Done
    Extracting --> ExtractError : Extraction Failed
    
    ExtractError --> Extract : Retry Extraction
    ExtractError --> Failed : Max Retries Exceeded
    
    ExtractComplete --> Transform : Set Stage to Transform
    Transform --> Transforming : Begin Transformation
    Transforming --> TransformComplete : Transformation Done
    Transforming --> TransformError : Transformation Failed
    
    TransformError --> Transform : Retry Transformation
    TransformError --> Failed : Max Retries Exceeded
    
    TransformComplete --> Load : Set Stage to Load
    Load --> Loading : Begin Loading
    Loading --> LoadComplete : Loading Done
    Loading --> LoadError : Loading Failed
    
    LoadError --> Load : Retry Loading
    LoadError --> Failed : Max Retries Exceeded
    
    LoadComplete --> Completed : All Stages Complete
    
    Failed --> [*] : Context Failed
    Completed --> [*] : Context Complete
```

### State Descriptions

#### Created
- **Description**: Processing context created
- **Actions**: Initialize context, set default values
- **Implementation**: `omop::core::ProcessingContext::ProcessingContext()`

#### Extract
- **Description**: Context set to extraction stage
- **Actions**: Set stage to Extract, initialize extraction metrics
- **Implementation**: `omop::core::ProcessingContext::set_stage(Stage::Extract)`

#### Extracting
- **Description**: Actively extracting data
- **Actions**: Track extraction progress, count records
- **Implementation**: `omop::core::ProcessingContext::increment_processed()`

#### ExtractComplete
- **Description**: Extraction stage completed
- **Actions**: Record extraction statistics, prepare for transformation
- **Implementation**: `omop::core::ProcessingContext::set_stage(Stage::Transform)`

#### ExtractError
- **Description**: Error occurred during extraction
- **Actions**: Increment error count, check error threshold
- **Implementation**: `omop::core::ProcessingContext::increment_errors()`

#### Transform
- **Description**: Context set to transformation stage
- **Actions**: Set stage to Transform, initialize transformation metrics
- **Implementation**: `omop::core::ProcessingContext::set_stage(Stage::Transform)`

#### Transforming
- **Description**: Actively transforming data
- **Actions**: Track transformation progress, count records
- **Implementation**: `omop::core::ProcessingContext::increment_processed()`

#### TransformComplete
- **Description**: Transformation stage completed
- **Actions**: Record transformation statistics, prepare for loading
- **Implementation**: `omop::core::ProcessingContext::set_stage(Stage::Load)`

#### TransformError
- **Description**: Error occurred during transformation
- **Actions**: Increment error count, check error threshold
- **Implementation**: `omop::core::ProcessingContext::increment_errors()`

#### Load
- **Description**: Context set to loading stage
- **Actions**: Set stage to Load, initialize loading metrics
- **Implementation**: `omop::core::ProcessingContext::set_stage(Stage::Load)`

#### Loading
- **Description**: Actively loading data
- **Actions**: Track loading progress, count records
- **Implementation**: `omop::core::ProcessingContext::increment_processed()`

#### LoadComplete
- **Description**: Loading stage completed
- **Actions**: Record loading statistics, prepare for completion
- **Implementation**: `omop::core::ProcessingContext::elapsed_time()`

#### LoadError
- **Description**: Error occurred during loading
- **Actions**: Increment error count, check error threshold
- **Implementation**: `omop::core::ProcessingContext::increment_errors()`

#### Completed
- **Description**: All processing stages completed successfully
- **Actions**: Record final statistics, prepare results
- **Implementation**: `omop::core::ProcessingContext::elapsed_time()`

#### Failed
- **Description**: Processing failed due to errors
- **Actions**: Record error statistics, prepare error report
- **Implementation**: `omop::core::ProcessingContext::is_error_threshold_exceeded()`

## 4. Service Health State Diagram

The service health state diagram shows the health states of microservices in the system.

```mermaid
stateDiagram-v2
    [*] --> Starting : Service Starting
    
    Starting --> Healthy : Service Started Successfully
    Starting --> Failed : Service Failed to Start
    
    Healthy --> Degraded : Performance Issues Detected
    Healthy --> Unhealthy : Health Check Failed
    Healthy --> Stopping : Stop Requested
    
    Degraded --> Healthy : Performance Recovered
    Degraded --> Unhealthy : Health Check Failed
    Degraded --> Stopping : Stop Requested
    
    Unhealthy --> Healthy : Health Check Passed
    Unhealthy --> Failed : Service Crashed
    Unhealthy --> Stopping : Stop Requested
    
    Failed --> Starting : Restart Attempted
    Failed --> Stopped : Stop Requested
    
    Stopping --> Stopped : Service Stopped
    Stopped --> [*] : Service Destroyed
```

### State Descriptions

#### Starting
- **Description**: Service is in the process of starting
- **Actions**: Initialize components, establish connections
- **Implementation**: `omop::service::ETLService::start()`

#### Healthy
- **Description**: Service is operating normally
- **Actions**: Process requests, respond to health checks
- **Implementation**: `omop::service::ETLService::health_check()`

#### Degraded
- **Description**: Service is operating but with performance issues
- **Actions**: Continue processing, monitor performance
- **Implementation**: `omop::service::ETLService::get_performance_metrics()`

#### Unhealthy
- **Description**: Service is not responding to health checks
- **Actions**: Attempt recovery, notify monitoring
- **Implementation**: `omop::service::ETLService::health_check()`

#### Failed
- **Description**: Service has crashed or failed completely
- **Actions**: Log error, attempt restart, notify monitoring
- **Implementation**: `omop::service::ETLService::handle_crash()`

#### Stopping
- **Description**: Service is in the process of stopping
- **Actions**: Stop accepting requests, cleanup resources
- **Implementation**: `omop::service::ETLService::stop()`

#### Stopped
- **Description**: Service has been stopped
- **Actions**: Resources released, service ready for destruction
- **Implementation**: `omop::service::ETLService::~ETLService()`

## 5. Database Connection State Diagram

The database connection state diagram shows the lifecycle of database connections in the connection pool.

```mermaid
stateDiagram-v2
    [*] --> Closed : Connection Created
    
    Closed --> Opening : Open Connection
    Opening --> Open : Connection Established
    Opening --> Failed : Connection Failed
    
    Open --> InUse : Connection Acquired
    InUse --> Idle : Connection Released
    Idle --> InUse : Connection Acquired
    Idle --> Closing : Close Requested
    
    InUse --> Failed : Query Failed
    InUse --> Closing : Close Requested
    
    Failed --> Opening : Retry Connection
    Failed --> Closed : Close Connection
    
    Closing --> Closed : Connection Closed
    Closed --> [*] : Connection Destroyed
```

### State Descriptions

#### Closed
- **Description**: Connection is closed and not available
- **Actions**: Connection object exists but no active connection
- **Implementation**: `omop::extract::DatabaseConnector::close()`

#### Opening
- **Description**: Attempting to establish database connection
- **Actions**: Connect to database, authenticate, establish session
- **Implementation**: `omop::extract::DatabaseConnector::connect()`

#### Open
- **Description**: Connection is established and ready for use
- **Actions**: Connection available in pool, ready for queries
- **Implementation**: `omop::extract::DatabaseConnector::is_connected()`

#### InUse
- **Description**: Connection is currently being used for queries
- **Actions**: Execute queries, process results
- **Implementation**: `omop::extract::DatabaseConnector::execute_query()`

#### Idle
- **Description**: Connection is available in the pool but not in use
- **Actions**: Connection ready for acquisition, monitor timeout
- **Implementation**: `omop::extract::ConnectionPool::get_connection()`

#### Failed
- **Description**: Connection has failed or encountered an error
- **Actions**: Log error, attempt recovery, notify pool
- **Implementation**: `omop::extract::DatabaseConnector::handle_error()`

#### Closing
- **Description**: Connection is being closed
- **Actions**: Close database connection, cleanup resources
- **Implementation**: `omop::extract::DatabaseConnector::close()`

## Implementation Notes

### State Management
- All state transitions are implemented using atomic operations where possible
- State changes are logged for debugging and monitoring purposes
- Error states include detailed error information for troubleshooting

### Thread Safety
- State transitions are protected by mutexes where necessary
- Atomic counters are used for progress tracking
- State changes are thread-safe across all components

### Error Handling
- Each state includes error handling and recovery mechanisms
- Error thresholds can be configured to prevent infinite retry loops
- Error callbacks can be registered for custom error handling

### Monitoring
- State changes are monitored and can trigger alerts
- State statistics are collected for performance analysis
- State history is maintained for debugging purposes

### Configuration
- State transition behavior can be configured via YAML files
- Timeout values and retry policies are configurable
- Error thresholds and recovery strategies are customizable 