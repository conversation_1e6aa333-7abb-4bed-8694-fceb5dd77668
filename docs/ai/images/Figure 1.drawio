<mxfile host="65bd71144e">
    <diagram name="Neuromorphic Computing Architecture" id="neuromorphic-healthcare-ai">
        <mxGraphModel dx="978" dy="646" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="bg-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3f2f1;strokeColor=#e1dfdd;strokeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="140" width="1000" height="600" as="geometry"/>
                </mxCell>
                <mxCell id="title-main" value="Neuromorphic Computing Architecture for Conversational AI" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#323130;fontFamily=Segoe UI;" parent="1" vertex="1">
                    <mxGeometry x="300" y="40" width="600" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="title-sub" value="Healthcare Application Infrastructure" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#605e5c;fontFamily=Segoe UI;" parent="1" vertex="1">
                    <mxGeometry x="400" y="100" width="400" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="layer-label" value="Infrastructure Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#0078d4;strokeColor=none;fontColor=#ffffff;fontSize=12;fontStyle=1;fontFamily=Segoe UI;" parent="1" vertex="1">
                    <mxGeometry x="140" y="170" width="140" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="neural-core" value="&lt;b&gt;Spiking Neural Core&lt;/b&gt;&lt;br&gt;Event-driven Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#0078d4;strokeWidth=3;fontSize=14;fontFamily=Segoe UI;fontColor=#323130;spacing=6;" parent="1" vertex="1">
                    <mxGeometry x="500" y="430" width="200" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="transformer-llm" value="&lt;b&gt;Transformer Model&lt;/b&gt;&lt;br&gt;Language Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#40e0d0;strokeWidth=3;fontSize=14;fontFamily=Segoe UI;fontColor=#323130;spacing=6;" parent="1" vertex="1">
                    <mxGeometry x="200" y="280" width="180" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="memory-systems" value="&lt;b&gt;Memory Systems&lt;/b&gt;&lt;br&gt;Context &amp; State" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#ff8c00;strokeWidth=3;fontSize=14;fontFamily=Segoe UI;fontColor=#323130;spacing=6;" parent="1" vertex="1">
                    <mxGeometry x="820" y="280" width="180" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="accelerators" value="&lt;b&gt;Neuromorphic Accelerators&lt;/b&gt;&lt;br&gt;Hardware Optimization" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#107c10;strokeWidth=3;fontSize=14;fontFamily=Segoe UI;fontColor=#323130;spacing=6;" parent="1" vertex="1">
                    <mxGeometry x="490" y="610" width="220" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="healthcare-context" value="&lt;b&gt;Healthcare Context&lt;/b&gt;&lt;br&gt;Domain Adaptation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#5c2d91;strokeWidth=3;fontSize=14;fontFamily=Segoe UI;fontColor=#323130;spacing=6;" parent="1" vertex="1">
                    <mxGeometry x="200" y="610" width="180" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="api-gateway" value="&lt;b&gt;API Gateway&lt;/b&gt;&lt;br&gt;Service Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#0078d4;strokeWidth=3;fontSize=14;fontFamily=Segoe UI;fontColor=#323130;spacing=6;" parent="1" vertex="1">
                    <mxGeometry x="820" y="610" width="180" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="edge1" value="Language Input" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=2;fontSize=11;fontFamily=Segoe UI;fontColor=#605e5c;edgeStyle=orthogonalEdgeStyle;curved=1;" parent="1" source="transformer-llm" target="neural-core" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="380" y="325" as="sourcePoint"/>
                        <mxPoint x="500" y="430" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="450" y="325"/>
                            <mxPoint x="450" y="480"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge2" value="Context Data" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=2;fontSize=11;fontFamily=Segoe UI;fontColor=#605e5c;edgeStyle=orthogonalEdgeStyle;curved=1;" parent="1" source="memory-systems" target="neural-core" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="820" y="325" as="sourcePoint"/>
                        <mxPoint x="700" y="430" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="750" y="325"/>
                            <mxPoint x="750" y="480"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge3" value="Spike Patterns" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=2;fontSize=11;fontFamily=Segoe UI;fontColor=#605e5c;edgeStyle=orthogonalEdgeStyle;" parent="1" source="neural-core" target="accelerators" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="600" y="530" as="sourcePoint"/>
                        <mxPoint x="600" y="610" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge4" value="Domain Rules" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=2;fontSize=11;fontFamily=Segoe UI;fontColor=#605e5c;edgeStyle=orthogonalEdgeStyle;curved=1;" parent="1" source="healthcare-context" target="neural-core" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="380" y="610" as="sourcePoint"/>
                        <mxPoint x="500" y="530" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="290" y="570"/>
                            <mxPoint x="290" y="480"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge5" value="Output Interface" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=2;fontSize=11;fontFamily=Segoe UI;fontColor=#605e5c;edgeStyle=orthogonalEdgeStyle;curved=1;" parent="1" source="neural-core" target="api-gateway" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="700" y="530" as="sourcePoint"/>
                        <mxPoint x="820" y="610" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="910" y="480"/>
                            <mxPoint x="910" y="570"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="feedback1" value="" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=1;dashed=1;dashPattern=1 4;opacity=50;edgeStyle=orthogonalEdgeStyle;curved=1;startArrow=classic;startFill=1;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="470" y="480" as="sourcePoint"/>
                        <mxPoint x="310" y="590" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="470" y="590"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="feedback2" value="" style="endArrow=classic;html=1;strokeColor=#605e5c;strokeWidth=1;dashed=1;dashPattern=1 4;opacity=50;edgeStyle=orthogonalEdgeStyle;curved=1;startArrow=classic;startFill=1;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="730" y="480" as="sourcePoint"/>
                        <mxPoint x="890" y="590" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="730" y="590"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="benefits-box" value="&lt;b&gt;Key Benefits&lt;/b&gt;&lt;br&gt;&lt;br&gt;• Low-latency responses&lt;br&gt;• Energy efficient design&lt;br&gt;• Scalable architecture&lt;br&gt;• Healthcare optimized" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3f2f1;strokeColor=#e1dfdd;strokeWidth=1;fontSize=12;fontFamily=Segoe UI;fontColor=#323130;align=left;spacingLeft=10;spacingTop=5;" parent="1" vertex="1">
                    <mxGeometry x="890" y="170" width="160" height="100" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>