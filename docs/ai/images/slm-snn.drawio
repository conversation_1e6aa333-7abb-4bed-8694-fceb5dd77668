<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" etag="slm-snn-integration" version="24.7.0" type="device">
  <diagram name="SLM-SNN Integration Framework" id="slm-snn-framework">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title-1" value="SLM-SNN Integration Framework" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontFamily=Arial;" vertex="1" parent="1">
          <mxGeometry x="350" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        
        <!-- SLM Architecture Section -->
        <mxCell id="slm-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="100" width="320" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-label" value="Small Language Model (SLM)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#3B82F6;" vertex="1" parent="1">
          <mxGeometry x="160" y="110" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-transformer" value="Transformer Architecture" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=#2563EB;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="150" width="280" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-attention" value="Multi-Head Attention" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60A5FA;strokeColor=#3B82F6;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="110" y="210" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-ffn" value="Feed-Forward Network" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60A5FA;strokeColor=#3B82F6;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="250" y="210" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-embed" value="Token Embeddings" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#93BBFD;strokeColor=#3B82F6;fontColor=#1E40AF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="110" y="260" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="slm-output" value="Language Output" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#93BBFD;strokeColor=#3B82F6;fontColor=#1E40AF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="250" y="260" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- DSL Processing Layer -->
        <mxCell id="dsl-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E8FF;strokeColor=#9333EA;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="160" width="320" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="dsl-label" value="Domain-Specific Language (DSL) Processing" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#9333EA;" vertex="1" parent="1">
          <mxGeometry x="480" y="170" width="240" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="dsl-parser" value="DSL Parser" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9333EA;strokeColor=#7C3AED;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="460" y="210" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="dsl-compiler" value="DSL Compiler" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9333EA;strokeColor=#7C3AED;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="620" y="210" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="dsl-optimizer" value="Code Optimizer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#A78BFA;strokeColor=#9333EA;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="540" y="270" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- SNN Topology Section -->
        <mxCell id="snn-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#22C55E;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="100" width="320" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-label" value="Spiking Neural Network (SNN)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#22C55E;" vertex="1" parent="1">
          <mxGeometry x="880" y="110" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-layers" value="Spiking Neuron Layers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#22C55E;strokeColor=#16A34A;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="820" y="150" width="280" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-lif" value="LIF Neurons" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#4ADE80;strokeColor=#22C55E;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="830" y="210" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-stdp" value="STDP Learning" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#4ADE80;strokeColor=#22C55E;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="970" y="210" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-spike-train" value="Spike Trains" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#86EFAC;strokeColor=#22C55E;fontColor=#166534;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="830" y="260" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="snn-temporal" value="Temporal Dynamics" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#86EFAC;strokeColor=#22C55E;fontColor=#166534;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="970" y="260" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- Spike Encoding/Decoding -->
        <mxCell id="encoder-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEF3C7;strokeColor=#F59E0B;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="200" y="420" width="200" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="encoder-label" value="Spike Encoder" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="250" y="430" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="encoder-rate" value="Rate Coding" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=#D97706;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="220" y="460" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="encoder-temporal" value="Temporal Coding" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=#D97706;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="220" y="500" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="decoder-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEF3C7;strokeColor=#F59E0B;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="800" y="420" width="200" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="decoder-label" value="Spike Decoder" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="850" y="430" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="decoder-aggregate" value="Spike Aggregation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=#D97706;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="820" y="460" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="decoder-reconstruct" value="Signal Reconstruction" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=#D97706;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="820" y="500" width="160" height="30" as="geometry" />
        </mxCell>
        
        <!-- Hybrid Learning Loop -->
        <mxCell id="hybrid-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#EF4444;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="400" width="320" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="hybrid-label" value="Hybrid Learning Mechanism" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#EF4444;" vertex="1" parent="1">
          <mxGeometry x="520" y="410" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="hybrid-feedback" value="Feedback Loop" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#EF4444;strokeColor=#DC2626;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="460" y="450" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="hybrid-adaptation" value="Adaptive Learning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#EF4444;strokeColor=#DC2626;fontColor=#FFFFFF;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="620" y="450" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="hybrid-sync" value="Temporal Synchronization" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F87171;strokeColor=#EF4444;fontColor=#FFFFFF;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="540" y="510" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- Data Flow Arrows -->
        <mxCell id="arrow-slm-dsl" value="" style="endArrow=classic;html=1;strokeColor=#3B82F6;strokeWidth=2;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="slm-transformer" target="dsl-parser">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="175" as="sourcePoint" />
            <mxPoint x="450" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-dsl-snn" value="" style="endArrow=classic;html=1;strokeColor=#9333EA;strokeWidth=2;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="dsl-compiler" target="snn-layers">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="230" as="sourcePoint" />
            <mxPoint x="810" y="175" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-slm-encoder" value="" style="endArrow=classic;html=1;strokeColor=#3B82F6;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="slm-container" target="encoder-container">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="390" as="sourcePoint" />
            <mxPoint x="300" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-snn-decoder" value="" style="endArrow=classic;html=1;strokeColor=#22C55E;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="snn-container" target="decoder-container">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="390" as="sourcePoint" />
            <mxPoint x="900" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-encoder-hybrid" value="" style="endArrow=classic;html=1;strokeColor=#F59E0B;strokeWidth=2;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="encoder-container" target="hybrid-feedback">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="480" as="sourcePoint" />
            <mxPoint x="450" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-decoder-hybrid" value="" style="endArrow=classic;html=1;strokeColor=#F59E0B;strokeWidth=2;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="decoder-container" target="hybrid-adaptation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="790" y="480" as="sourcePoint" />
            <mxPoint x="750" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-hybrid-dsl" value="" style="endArrow=classic;html=1;strokeColor=#EF4444;strokeWidth=2;curved=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="hybrid-container" target="dsl-container">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="390" as="sourcePoint" />
            <mxPoint x="600" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Annotations -->
        <mxCell id="annotation-1" value="Sequential processing of language tokens" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="100" y="320" width="150" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="annotation-2" value="Event-driven spike processing" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="950" y="320" width="150" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="annotation-3" value="Bidirectional information flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="540" y="350" width="120" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="annotation-4" value="Continuous adaptation through feedback" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="540" y="570" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F9FAFB;strokeColor=#D1D5DB;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="600" width="400" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-title" value="Legend" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="90" y="610" width="60" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-slm" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=#2563EB;" vertex="1" parent="1">
          <mxGeometry x="100" y="640" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-slm-text" value="SLM Components" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="140" y="640" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-snn" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#22C55E;strokeColor=#16A34A;" vertex="1" parent="1">
          <mxGeometry x="250" y="640" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-snn-text" value="SNN Components" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="290" y="640" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-dsl" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#9333EA;strokeColor=#7C3AED;" vertex="1" parent="1">
          <mxGeometry x="100" y="670" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-dsl-text" value="DSL Processing" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="140" y="670" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-hybrid" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EF4444;strokeColor=#DC2626;" vertex="1" parent="1">
          <mxGeometry x="250" y="670" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-hybrid-text" value="Hybrid Learning" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="290" y="670" width="100" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>