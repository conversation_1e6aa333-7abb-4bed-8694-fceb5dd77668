<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" version="24.7.0" etag="togaf-ea-overview" type="device">
  <diagram name="TOGAF Enterprise Architecture" id="togaf-architecture">
    <mxGraphModel dx="1426" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" background="#ffffff" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Main Title -->
        <mxCell id="title-1" value="TOGAF Enterprise Architecture Overview" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#1E40AF;" vertex="1" parent="1">
          <mxGeometry x="385" y="20" width="400" height="40" as="geometry" />
        </mxCell>
        
        <!-- Governance Framework (Top) -->
        <mxCell id="gov-1" value="Enterprise Governance &amp; Security Framework" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1E40AF;strokeColor=#1E40AF;fontColor=#ffffff;fontSize=14;fontStyle=1;arcSize=5;" vertex="1" parent="1">
          <mxGeometry x="40" y="80" width="1090" height="40" as="geometry" />
        </mxCell>
        
        <!-- Business Architecture Layer -->
        <mxCell id="business-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#2563EB;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="140" width="1090" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="business-title" value="Business Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2563EB;" vertex="1" parent="1">
          <mxGeometry x="50" y="145" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Business Components -->
        <mxCell id="business-1" value="Business Strategy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="70" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="business-2" value="Business Processes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="210" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="business-3" value="Organization Structure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="350" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="business-4" value="Business Services" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="490" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="business-5" value="Business Capabilities" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="630" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="business-6" value="Stakeholders" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="770" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="business-7" value="Business KPIs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563EB;strokeColor=#1D4ED8;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="910" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Data Architecture Layer -->
        <mxCell id="data-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="280" width="1090" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="data-title" value="Data Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#059669;" vertex="1" parent="1">
          <mxGeometry x="50" y="285" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Data Components -->
        <mxCell id="data-1" value="Data Entities" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="70" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-2" value="Master Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="210" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-3" value="Data Warehouse" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="350" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-4" value="Data Lakes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="490" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-5" value="Data Governance" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="630" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-6" value="Data Quality" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="770" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-7" value="Analytics &amp; BI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="910" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Application Architecture Layer -->
        <mxCell id="app-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#7C3AED;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="420" width="1090" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="app-title" value="Application Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#7C3AED;" vertex="1" parent="1">
          <mxGeometry x="50" y="425" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Application Components -->
        <mxCell id="app-1" value="Core Applications" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="70" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-2" value="ERP Systems" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="210" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-3" value="CRM Systems" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="350" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-4" value="Integration Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="490" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-5" value="APIs &amp; Services" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="630" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-6" value="Microservices" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="770" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-7" value="Mobile Apps" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7C3AED;strokeColor=#6D28D9;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="910" y="460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Technology Architecture Layer -->
        <mxCell id="tech-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="560" width="1090" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-title" value="Technology Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#DC2626;" vertex="1" parent="1">
          <mxGeometry x="50" y="565" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Technology Components -->
        <mxCell id="tech-1" value="Infrastructure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="70" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-2" value="Network" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="210" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-3" value="Cloud Platforms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="350" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-4" value="Security Systems" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="490" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-5" value="Middleware" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="630" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-6" value="DevOps Tools" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="770" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tech-7" value="Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DC2626;strokeColor=#B91C1C;fontColor=#ffffff;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="910" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Security Boundary (Bottom) -->
        <mxCell id="security-1" value="Security Perimeter &amp; Compliance Controls" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1E40AF;strokeColor=#1E40AF;fontColor=#ffffff;fontSize=14;fontStyle=1;arcSize=5;" vertex="1" parent="1">
          <mxGeometry x="40" y="700" width="1090" height="40" as="geometry" />
        </mxCell>
        
        <!-- Vertical Service Interfaces -->
        <mxCell id="service-1" value="Service Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#9CA3AF;fontSize=10;rotation=90;" vertex="1" parent="1">
          <mxGeometry x="1050" y="240" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="service-2" value="Service Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#9CA3AF;fontSize=10;rotation=90;" vertex="1" parent="1">
          <mxGeometry x="1050" y="380" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="service-3" value="Service Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#9CA3AF;fontSize=10;rotation=90;" vertex="1" parent="1">
          <mxGeometry x="1050" y="520" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Inter-layer Connections -->
        <mxCell id="conn-1" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="260" as="sourcePoint" />
            <mxPoint x="130" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-2" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="260" as="sourcePoint" />
            <mxPoint x="270" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-3" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="260" as="sourcePoint" />
            <mxPoint x="410" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-4" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="260" as="sourcePoint" />
            <mxPoint x="550" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-5" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="690" y="260" as="sourcePoint" />
            <mxPoint x="690" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-6" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="260" as="sourcePoint" />
            <mxPoint x="830" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-7" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="970" y="260" as="sourcePoint" />
            <mxPoint x="970" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Data to Application connections -->
        <mxCell id="conn-8" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="400" as="sourcePoint" />
            <mxPoint x="130" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-9" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="400" as="sourcePoint" />
            <mxPoint x="270" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-10" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="400" as="sourcePoint" />
            <mxPoint x="410" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-11" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="400" as="sourcePoint" />
            <mxPoint x="550" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-12" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="690" y="400" as="sourcePoint" />
            <mxPoint x="690" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-13" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="400" as="sourcePoint" />
            <mxPoint x="830" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-14" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="970" y="400" as="sourcePoint" />
            <mxPoint x="970" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Application to Technology connections -->
        <mxCell id="conn-15" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="540" as="sourcePoint" />
            <mxPoint x="130" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-16" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="540" as="sourcePoint" />
            <mxPoint x="270" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-17" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="540" as="sourcePoint" />
            <mxPoint x="410" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-18" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="540" as="sourcePoint" />
            <mxPoint x="550" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-19" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="690" y="540" as="sourcePoint" />
            <mxPoint x="690" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-20" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="540" as="sourcePoint" />
            <mxPoint x="830" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn-21" value="" style="endArrow=classic;startArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="970" y="540" as="sourcePoint" />
            <mxPoint x="970" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Data Flow Arrows -->
        <mxCell id="flow-1" value="Data Flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6B7280;rotation=-90;" vertex="1" parent="1">
          <mxGeometry x="100" y="270" width="60" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>