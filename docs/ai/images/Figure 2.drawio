<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36" version="27.0.9">
  <diagram name="Page-1" id="ai-pipeline-diagram">
    <mxGraphModel dx="1018" dy="703" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title-1" value="Efficient AI Pipeline for Conversational Assistants" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="285" y="30" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="arch-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;opacity=20;" parent="1" vertex="1">
          <mxGeometry x="80" y="100" width="1010" height="180" as="geometry" />
        </mxCell>
        <mxCell id="arch-layer-label" value="Architecture Layer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1976D2;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="90" y="105" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arch-conv-ai" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Conversational AI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="120" y="160" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arch-conv-ai-icon" value="💬" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="180" y="171" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arch-model-inf" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Model Inference" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="320" y="160" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arch-model-inf-icon" value="🧠" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="380" y="171" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arch-memory-eff" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Memory-Efficient&lt;br&gt;AI Pipeline" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="520" y="160" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arch-memory-eff-icon" value="⚡" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="580" y="171" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arch-neuro-acc" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Neuromorphic&lt;br&gt;Acceleration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="720" y="160" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arch-neuro-acc-icon" value="🔬" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="780" y="171" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arch-data-sources" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Data Sources" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="920" y="160" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arch-data-sources-icon" value="☁️" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="970" y="171" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E9;strokeColor=#388E3C;strokeWidth=2;opacity=20;" parent="1" vertex="1">
          <mxGeometry x="80" y="320" width="1010" height="140" as="geometry" />
        </mxCell>
        <mxCell id="comp-layer-label" value="Components Layer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#388E3C;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="90" y="325" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-conv-ai" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Conversational AI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="310" y="350" width="180" height="90" as="geometry" />
        </mxCell>
        <mxCell id="comp-conv-ai-icon" value="💬" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="380" y="364" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-dyn-scale" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Dynamic and Scalable&lt;br&gt;Model Inference" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="700" y="350" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="comp-dyn-scale-icon" value="📈" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="780" y="355" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="deploy-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#F57C00;strokeWidth=2;opacity=20;" parent="1" vertex="1">
          <mxGeometry x="80" y="500" width="1010" height="180" as="geometry" />
        </mxCell>
        <mxCell id="deploy-layer-label" value="Deployment Layer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#F57C00;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="90" y="505" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="deploy-agent" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="130" y="560" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="deploy-agent-icon" value="👨‍⚕️" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="180" y="571" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="deploy-vector-db" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Vector DB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="330" y="560" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="deploy-vector-db-icon" value="🔍" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="380" y="571" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="deploy-memory-eff" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Memory-Efficient&lt;br&gt;AI Pipeline" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="610" y="560" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="deploy-memory-eff-icon" value="⚡" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="670" y="571" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="deploy-neuro-co" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Neuromorphic&lt;br&gt;Co-Processor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;arcSize=10;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="830" y="560" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="deploy-neuro-co-icon" value="🔬" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" parent="1" vertex="1">
          <mxGeometry x="890" y="571" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arrow-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="arch-conv-ai" target="comp-conv-ai" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="300" />
              <mxPoint x="400" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="arch-model-inf" target="comp-conv-ai" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="320" />
              <mxPoint x="400" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="arch-memory-eff" target="comp-dyn-scale" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="600" y="300" />
              <mxPoint x="800" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="arch-neuro-acc" target="comp-dyn-scale" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="330" />
              <mxPoint x="800" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="arch-data-sources" target="comp-dyn-scale" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="990" y="300" />
              <mxPoint x="800" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="comp-conv-ai" target="deploy-agent" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="comp-conv-ai" target="deploy-vector-db" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="comp-dyn-scale" target="deploy-memory-eff" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="comp-dyn-scale" target="deploy-neuro-co" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#CCCCCC;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="710" width="300" height="80" as="geometry" />
        </mxCell>
        <mxCell id="legend-title" value="Legend" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="90" y="715" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-arch" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="90" y="740" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="legend-arch-text" value="Architecture Layer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="115" y="735" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-comp" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="90" y="755" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="legend-comp-text" value="Components Layer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="115" y="750" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-deploy" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="90" y="770" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="legend-deploy-text" value="Deployment Layer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontFamily=Segoe UI, Arial;" parent="1" vertex="1">
          <mxGeometry x="115" y="765" width="120" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
