<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" version="24.0.0">
  <diagram name="Hybrid Integration Architecture" id="hybrid-arch">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background for organization -->
        <mxCell id="bg-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3f4f6;strokeColor=none;opacity=50;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="1100" height="700" as="geometry" />
        </mxCell>
        
        <!-- Title -->
        <mxCell id="title-1" value="HYBRID INTEGRATION ARCHITECTURE" style="text;html=1;fontSize=20;fontStyle=1;verticalAlign=middle;align=center;fontColor=#333333;fontFamily=Arial;" vertex="1" parent="1">
          <mxGeometry x="400" y="70" width="400" height="30" as="geometry" />
        </mxCell>
        
        <!-- SLM Engine -->
        <mxCell id="slm-1" value="SLM ENGINE&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Symbolic Logic Machine&lt;br&gt;Latency: 10-50ms&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=#2563EB;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="200" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- SNN Engine -->
        <mxCell id="snn-1" value="SNN ENGINE&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Spiking Neural Network&lt;br&gt;Latency: 1-5ms&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=#059669;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="870" y="200" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- Shared Memory -->
        <mxCell id="shared-mem-1" value="SHARED MEMORY&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Unified Data Store&lt;br&gt;Access: &lt;1ms&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=#7C3AED;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="150" width="300" height="80" as="geometry" />
        </mxCell>
        
        <!-- Arbitration Logic -->
        <mxCell id="arbitration-1" value="ARBITRATION LOGIC&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Priority-based Decision Making&lt;br&gt;Decision Time: &lt;0.5ms&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=#7C3AED;fontColor=#FFFFFF;fontSize=14;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="350" width="300" height="80" as="geometry" />
        </mxCell>
        
        <!-- HU1 -->
        <mxCell id="hu1" value="HU-1&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Hybrid Unit&lt;br&gt;Type: Cognitive&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E9D5FF;strokeColor=#8B5CF6;fontColor=#4C1D95;fontSize=12;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="200" y="480" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- HU2 -->
        <mxCell id="hu2" value="HU-2&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Hybrid Unit&lt;br&gt;Type: Reflexive&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E9D5FF;strokeColor=#8B5CF6;fontColor=#4C1D95;fontSize=12;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="480" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- HU3 -->
        <mxCell id="hu3" value="HU-3&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Hybrid Unit&lt;br&gt;Type: Balanced&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E9D5FF;strokeColor=#8B5CF6;fontColor=#4C1D95;fontSize=12;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="480" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- HU4 -->
        <mxCell id="hu4" value="HU-4&lt;br&gt;&lt;font size=&quot;1&quot;&gt;Hybrid Unit&lt;br&gt;Type: Adaptive&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E9D5FF;strokeColor=#8B5CF6;fontColor=#4C1D95;fontSize=12;fontStyle=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="880" y="480" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Data flows - SLM to Shared Memory -->
        <mxCell id="flow-1" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#3B82F6;curved=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="slm-1" target="shared-mem-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="flow-1-label" value="Symbolic Data&lt;br&gt;10MB/s" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="350" y="220" width="90" height="40" as="geometry" />
        </mxCell>
        
        <!-- Data flows - SNN to Shared Memory -->
        <mxCell id="flow-2" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#10B981;curved=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="snn-1" target="shared-mem-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="300" as="sourcePoint" />
            <mxPoint x="800" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="flow-2-label" value="Spike Data&lt;br&gt;100MB/s" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="770" y="220" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Shared Memory to Arbitration -->
        <mxCell id="flow-3" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#8B5CF6;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="shared-mem-1" target="arbitration-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="300" as="sourcePoint" />
            <mxPoint x="600" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="flow-3-label" value="Unified Data Stream" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="610" y="280" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- Arbitration to HUs -->
        <mxCell id="flow-4" value="" style="endArrow=classic;html=1;strokeWidth=2;strokeColor=#8B5CF6;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="arbitration-1" target="hu1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-5" value="" style="endArrow=classic;html=1;strokeWidth=2;strokeColor=#8B5CF6;exitX=0.4;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="arbitration-1" target="hu2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-6" value="" style="endArrow=classic;html=1;strokeWidth=2;strokeColor=#8B5CF6;exitX=0.6;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="arbitration-1" target="hu3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow-7" value="" style="endArrow=classic;html=1;strokeWidth=2;strokeColor=#8B5CF6;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="arbitration-1" target="hu4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Priority Indicators -->
        <mxCell id="priority-1" value="P1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#DC2626;strokeColor=#991B1B;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="170" y="440" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="priority-2" value="P2" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#F59E0B;strokeColor=#D97706;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="370" y="440" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="priority-3" value="P3" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#3B82F6;strokeColor=#2563EB;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="440" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="priority-4" value="P4" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#10B981;strokeColor=#059669;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="440" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- Timing Diagram Box -->
        <mxCell id="timing-box" value="TIMING SEQUENCE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#9CA3AF;fontSize=12;fontStyle=1;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="150" y="600" width="400" height="120" as="geometry" />
        </mxCell>
        
        <!-- Timing lines -->
        <mxCell id="timing-1" value="" style="endArrow=none;html=1;strokeWidth=2;strokeColor=#3B82F6;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="640" as="sourcePoint" />
            <mxPoint x="520" y="640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="timing-1-label" value="SLM" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="180" y="620" width="40" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="timing-2" value="" style="endArrow=none;html=1;strokeWidth=2;strokeColor=#10B981;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="670" as="sourcePoint" />
            <mxPoint x="520" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="timing-2-label" value="SNN" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="180" y="650" width="40" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="timing-3" value="" style="endArrow=none;html=1;strokeWidth=2;strokeColor=#8B5CF6;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="700" as="sourcePoint" />
            <mxPoint x="520" y="700" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="timing-3-label" value="ARB" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="180" y="680" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- Timing pulses -->
        <mxCell id="pulse-1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="250" y="635" width="40" height="10" as="geometry" />
        </mxCell>
        <mxCell id="pulse-2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="260" y="665" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="pulse-3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="300" y="695" width="30" height="10" as="geometry" />
        </mxCell>
        
        <mxCell id="pulse-4" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="350" y="635" width="40" height="10" as="geometry" />
        </mxCell>
        <mxCell id="pulse-5" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="360" y="665" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="pulse-6" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="400" y="695" width="30" height="10" as="geometry" />
        </mxCell>
        
        <!-- Performance Metrics Box -->
        <mxCell id="perf-box" value="PERFORMANCE METRICS" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#9CA3AF;fontSize=12;fontStyle=1;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="650" y="600" width="400" height="120" as="geometry" />
        </mxCell>
        
        <!-- Performance metrics -->
        <mxCell id="perf-1" value="• System Throughput: 1.2 TOPS" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="670" y="625" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="perf-2" value="• End-to-End Latency: &lt;15ms" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="670" y="645" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="perf-3" value="• Memory Bandwidth: 25 GB/s" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="670" y="665" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="perf-4" value="• Power Efficiency: 5 TOPS/W" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="670" y="685" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-title" value="Legend" style="text;html=1;fontSize=12;fontStyle=1;verticalAlign=middle;align=left;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="50" y="125" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="legend-1-text" value="Cognitive Processing" style="text;html=1;fontSize=10;verticalAlign=middle;align=left;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="75" y="120" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="50" y="145" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="legend-2-text" value="Reflexive Processing" style="text;html=1;fontSize=10;verticalAlign=middle;align=left;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="75" y="140" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="50" y="165" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="legend-3-text" value="Integration Layer" style="text;html=1;fontSize=10;verticalAlign=middle;align=left;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="75" y="160" width="120" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>