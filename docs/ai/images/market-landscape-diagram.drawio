<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" etag="unique" version="24.7.17" type="device">
  <diagram name="Market Landscape" id="market-landscape-2025-2030">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background Panel -->
        <mxCell id="bg-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8fafc;strokeColor=#e5e7eb;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1090" height="750" as="geometry" />
        </mxCell>
        
        <!-- Title Section -->
        <mxCell id="title-1" value="Market Landscape &amp; Applications 2025-2030" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#1E3A8A;" vertex="1" parent="1">
          <mxGeometry x="340" y="60" width="490" height="40" as="geometry" />
        </mxCell>
        
        <!-- Market Growth Panel -->
        <mxCell id="panel-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d1d5db;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="120" width="500" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="panel-title-1" value="Market Growth Projection" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1E3A8A;" vertex="1" parent="1">
          <mxGeometry x="90" y="130" width="250" height="30" as="geometry" />
        </mxCell>
        
        <!-- Growth Curve -->
        <mxCell id="curve-1" value="" style="shape=flexArrow;endArrow=classic;html=1;fillColor=#059669;strokeColor=none;gradientColor=#1E3A8A;gradientDirection=east;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="440" height="60" as="geometry" />
        </mxCell>
        
        <!-- Timeline Labels -->
        <mxCell id="year-2025" value="2025&lt;br&gt;$12.5B" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="90" y="365" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="year-2026" value="2026&lt;br&gt;$18.2B" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="180" y="365" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="year-2027" value="2027&lt;br&gt;$26.7B" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="270" y="365" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="year-2028" value="2028&lt;br&gt;$38.4B" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="360" y="365" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="year-2029" value="2029&lt;br&gt;$52.1B" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="450" y="365" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="year-2030" value="2030&lt;br&gt;$71.3B" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#059669;" vertex="1" parent="1">
          <mxGeometry x="490" y="365" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- Market Segments Pie Chart -->
        <mxCell id="panel-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d1d5db;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="120" width="500" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="panel-title-2" value="Market Segments by 2030" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1E3A8A;" vertex="1" parent="1">
          <mxGeometry x="620" y="130" width="250" height="30" as="geometry" />
        </mxCell>
        
        <!-- Pie Chart Segments -->
        <mxCell id="pie-healthcare" value="" style="shape=pie;startAngle=0;endAngle=126;fillColor=#1E3A8A;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="680" y="180" width="160" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="pie-automotive" value="" style="shape=pie;startAngle=126;endAngle=216;fillColor=#2563eb;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="680" y="180" width="160" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="pie-robotics" value="" style="shape=pie;startAngle=216;endAngle=288;fillColor=#059669;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="680" y="180" width="160" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="pie-edge" value="" style="shape=pie;startAngle=288;endAngle=360;fillColor=#10b981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="680" y="180" width="160" height="160" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-healthcare" value="Healthcare 35%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1E3A8A;fontColor=#ffffff;strokeColor=none;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="880" y="190" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-automotive" value="Automotive 25%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2563eb;fontColor=#ffffff;strokeColor=none;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="880" y="230" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-robotics" value="Robotics 20%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#059669;fontColor=#ffffff;strokeColor=none;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="880" y="270" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-edge" value="Edge Computing 20%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10b981;fontColor=#ffffff;strokeColor=none;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="880" y="310" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- Application Scenarios Panel -->
        <mxCell id="panel-3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d1d5db;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="420" width="1030" height="340" as="geometry" />
        </mxCell>
        
        <mxCell id="panel-title-3" value="Key Application Scenarios &amp; Performance Metrics" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1E3A8A;" vertex="1" parent="1">
          <mxGeometry x="90" y="430" width="400" height="30" as="geometry" />
        </mxCell>
        
        <!-- Healthcare Application -->
        <mxCell id="app-healthcare" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f9ff;strokeColor=#1E3A8A;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="100" y="480" width="220" height="250" as="geometry" />
        </mxCell>
        
        <mxCell id="icon-healthcare" value="&#128657;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;" vertex="1" parent="1">
          <mxGeometry x="180" y="490" width="60" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-healthcare-title" value="Healthcare AI" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1E3A8A;" vertex="1" parent="1">
          <mxGeometry x="120" y="550" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="app-healthcare-metrics" value="&lt;b&gt;Diagnostic Accuracy:&lt;/b&gt; 97.5%&lt;br&gt;&lt;b&gt;Processing Speed:&lt;/b&gt; &lt;2s&lt;br&gt;&lt;b&gt;Cost Reduction:&lt;/b&gt; 45%&lt;br&gt;&lt;b&gt;Patient Throughput:&lt;/b&gt; +60%" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="120" y="590" width="180" height="120" as="geometry" />
        </mxCell>
        
        <!-- Automotive Application -->
        <mxCell id="app-automotive" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eff6ff;strokeColor=#2563eb;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="350" y="480" width="220" height="250" as="geometry" />
        </mxCell>
        
        <mxCell id="icon-automotive" value="&#128663;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;" vertex="1" parent="1">
          <mxGeometry x="430" y="490" width="60" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-automotive-title" value="Autonomous Vehicles" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2563eb;" vertex="1" parent="1">
          <mxGeometry x="370" y="550" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="app-automotive-metrics" value="&lt;b&gt;Safety Rating:&lt;/b&gt; Level 4&lt;br&gt;&lt;b&gt;Response Time:&lt;/b&gt; 20ms&lt;br&gt;&lt;b&gt;Fuel Efficiency:&lt;/b&gt; +35%&lt;br&gt;&lt;b&gt;Accident Reduction:&lt;/b&gt; 82%" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="370" y="590" width="180" height="120" as="geometry" />
        </mxCell>
        
        <!-- Robotics Application -->
        <mxCell id="app-robotics" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ecfdf5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="480" width="220" height="250" as="geometry" />
        </mxCell>
        
        <mxCell id="icon-robotics" value="&#129302;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;" vertex="1" parent="1">
          <mxGeometry x="680" y="490" width="60" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-robotics-title" value="Industrial Robotics" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#059669;" vertex="1" parent="1">
          <mxGeometry x="620" y="550" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="app-robotics-metrics" value="&lt;b&gt;Precision:&lt;/b&gt; ±0.1mm&lt;br&gt;&lt;b&gt;Uptime:&lt;/b&gt; 99.7%&lt;br&gt;&lt;b&gt;Productivity:&lt;/b&gt; +120%&lt;br&gt;&lt;b&gt;ROI Period:&lt;/b&gt; 14 months" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="620" y="590" width="180" height="120" as="geometry" />
        </mxCell>
        
        <!-- Edge Computing Application -->
        <mxCell id="app-edge" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0fdf4;strokeColor=#10b981;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="850" y="480" width="220" height="250" as="geometry" />
        </mxCell>
        
        <mxCell id="icon-edge" value="&#128248;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=36;" vertex="1" parent="1">
          <mxGeometry x="930" y="490" width="60" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="app-edge-title" value="Edge AI Computing" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#10b981;" vertex="1" parent="1">
          <mxGeometry x="870" y="550" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="app-edge-metrics" value="&lt;b&gt;Latency:&lt;/b&gt; &lt;5ms&lt;br&gt;&lt;b&gt;Power Efficiency:&lt;/b&gt; 3W&lt;br&gt;&lt;b&gt;Data Reduction:&lt;/b&gt; 85%&lt;br&gt;&lt;b&gt;Network Load:&lt;/b&gt; -70%" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="870" y="590" width="180" height="120" as="geometry" />
        </mxCell>
        
        <!-- Growth Indicators -->
        <mxCell id="growth-arrow-1" value="" style="shape=singleArrow;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=none;opacity=50;rotation=45;" vertex="1" parent="1">
          <mxGeometry x="260" y="190" width="40" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="growth-text-1" value="47% CAGR" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#059669;" vertex="1" parent="1">
          <mxGeometry x="290" y="170" width="80" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>