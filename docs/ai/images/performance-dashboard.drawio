<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" etag="performance-dashboard" version="20.0.0" type="device">
  <diagram name="Performance Analysis Dashboard" id="dashboard-001">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Dashboard Background -->
        <mxCell id="dashboard-bg" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#e0e0e0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1520" height="820" as="geometry" />
        </mxCell>
        
        <!-- Dashboard Title -->
        <mxCell id="title" value="PERFORMANCE ANALYSIS DASHBOARD" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#1a1a1a;" vertex="1" parent="1">
          <mxGeometry x="40" y="60" width="1520" height="40" as="geometry" />
        </mxCell>
        
        <!-- Date/Time Stamp -->
        <mxCell id="timestamp" value="Last Updated: Real-time" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1360" y="70" width="180" height="20" as="geometry" />
        </mxCell>
        
        <!-- Panel 1: Latency Histogram -->
        <mxCell id="panel1-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#e0e0e0;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="120" width="360" height="320" as="geometry" />
        </mxCell>
        
        <mxCell id="panel1-title" value="LATENCY DISTRIBUTION" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="100" y="135" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- Histogram Bars -->
        <mxCell id="hist-bar1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="40" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hist-bar2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="170" y="280" width="40" height="120" as="geometry" />
        </mxCell>
        <mxCell id="hist-bar3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="220" y="220" width="40" height="180" as="geometry" />
        </mxCell>
        <mxCell id="hist-bar4" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="270" y="300" width="40" height="100" as="geometry" />
        </mxCell>
        <mxCell id="hist-bar5" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="320" y="360" width="40" height="40" as="geometry" />
        </mxCell>
        
        <!-- X-axis labels -->
        <mxCell id="x-label1" value="0-50ms" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="120" y="405" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="x-label2" value="50-100ms" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="165" y="405" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="x-label3" value="100-200ms" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="215" y="405" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="x-label4" value="200-500ms" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="265" y="405" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="x-label5" value="&gt;500ms" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="320" y="405" width="40" height="20" as="geometry" />
        </mxCell>
        
        <!-- Current Average -->
        <mxCell id="avg-latency" value="AVG: 145ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="135" width="80" height="25" as="geometry" />
        </mxCell>
        
        <!-- Panel 2: Energy Consumption -->
        <mxCell id="panel2-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#e0e0e0;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="120" width="360" height="320" as="geometry" />
        </mxCell>
        
        <mxCell id="panel2-title" value="ENERGY CONSUMPTION" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="500" y="135" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- Energy Chart Background -->
        <mxCell id="energy-chart-bg" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f3f4f6;strokeColor=#e5e7eb;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="180" width="320" height="200" as="geometry" />
        </mxCell>
        
        <!-- Energy Trend Line -->
        <mxCell id="energy-line" value="" style="shape=flexArrow;endArrow=none;startArrow=none;html=1;startSize=0;endSize=0;strokeColor=#10B981;strokeWidth=3;fillColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="510" y="320" width="300" height="3" as="geometry" />
        </mxCell>
        
        <!-- Energy Data Points -->
        <mxCell id="energy-point1" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#10B981;strokeColor=#ffffff;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="520" y="318" width="8" height="8" as="geometry" />
        </mxCell>
        <mxCell id="energy-point2" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#10B981;strokeColor=#ffffff;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="580" y="298" width="8" height="8" as="geometry" />
        </mxCell>
        <mxCell id="energy-point3" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#10B981;strokeColor=#ffffff;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="640" y="288" width="8" height="8" as="geometry" />
        </mxCell>
        <mxCell id="energy-point4" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#10B981;strokeColor=#ffffff;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="700" y="278" width="8" height="8" as="geometry" />
        </mxCell>
        <mxCell id="energy-point5" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#10B981;strokeColor=#ffffff;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="760" y="268" width="8" height="8" as="geometry" />
        </mxCell>
        
        <!-- Current Consumption -->
        <mxCell id="current-energy" value="2.4 kWh" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="135" width="80" height="25" as="geometry" />
        </mxCell>
        
        <!-- Target Line -->
        <mxCell id="target-line" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;strokeColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="500" y="250" width="320" height="1" as="geometry" />
        </mxCell>
        
        <mxCell id="target-label" value="Target: 3.0 kWh" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="720" y="240" width="90" height="20" as="geometry" />
        </mxCell>
        
        <!-- Panel 3: Accuracy Trends -->
        <mxCell id="panel3-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#e0e0e0;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="880" y="120" width="360" height="320" as="geometry" />
        </mxCell>
        
        <mxCell id="panel3-title" value="ACCURACY TRENDS" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="900" y="135" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- Accuracy Percentage Display -->
        <mxCell id="accuracy-display" value="98.7%" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=48;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="960" y="220" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Accuracy Indicator -->
        <mxCell id="accuracy-arrow" value="▲ +0.3%" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="1010" y="300" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- Mini Trend Chart -->
        <mxCell id="mini-trend" value="" style="shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/svg+xml,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMzAiPjxwb2x5bGluZSBwb2ludHM9IjEwLDI1IDMwLDIwIDUwLDE1IDcwLDEwIDkwLDUiIHN0cm9rZT0iIzEwQjk4MSIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+PC9zdmc+;" vertex="1" parent="1">
          <mxGeometry x="1010" y="350" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- Panel 4: Scalability Curves -->
        <mxCell id="panel4-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#e0e0e0;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1280" y="120" width="240" height="320" as="geometry" />
        </mxCell>
        
        <mxCell id="panel4-title" value="SCALABILITY" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="1300" y="135" width="200" height="25" as="geometry" />
        </mxCell>
        
        <!-- Scalability Metrics -->
        <mxCell id="scale-metric1" value="THROUGHPUT" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1300" y="180" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="scale-value1" value="12.5K req/s" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="1400" y="180" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="scale-metric2" value="CONCURRENT USERS" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1300" y="220" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="scale-value2" value="5,234" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="1400" y="220" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="scale-metric3" value="CPU UTILIZATION" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1300" y="260" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="scale-value3" value="67%" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="1400" y="260" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="scale-metric4" value="MEMORY USAGE" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1300" y="300" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="scale-value4" value="82%" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="1400" y="300" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Progress Bars -->
        <mxCell id="progress-bg1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e5e7eb;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="1300" y="340" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="progress-fill1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10B981;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="1300" y="340" width="134" height="8" as="geometry" />
        </mxCell>
        
        <mxCell id="progress-bg2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e5e7eb;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="1300" y="360" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="progress-fill2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F59E0B;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="1300" y="360" width="164" height="8" as="geometry" />
        </mxCell>
        
        <!-- KPI Summary Section -->
        <mxCell id="kpi-section-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#e0e0e0;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="480" width="1440" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="kpi-title" value="KEY PERFORMANCE INDICATORS" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="100" y="495" width="300" height="30" as="geometry" />
        </mxCell>
        
        <!-- KPI Cards -->
        <mxCell id="kpi1-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0fdf4;strokeColor=#10B981;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="540" width="320" height="60" as="geometry" />
        </mxCell>
        <mxCell id="kpi1-label" value="SYSTEM UPTIME" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="135" y="550" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="kpi1-value" value="99.98%" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="320" y="555" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="kpi2-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0fdf4;strokeColor=#10B981;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="540" width="320" height="60" as="geometry" />
        </mxCell>
        <mxCell id="kpi2-label" value="RESPONSE TIME (P95)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="495" y="550" width="130" height="20" as="geometry" />
        </mxCell>
        <mxCell id="kpi2-value" value="245ms" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="680" y="555" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="kpi3-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fef3c7;strokeColor=#F59E0B;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="840" y="540" width="320" height="60" as="geometry" />
        </mxCell>
        <mxCell id="kpi3-label" value="ERROR RATE" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="855" y="550" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="kpi3-value" value="0.12%" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="1040" y="555" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="kpi4-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0fdf4;strokeColor=#10B981;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1200" y="540" width="280" height="60" as="geometry" />
        </mxCell>
        <mxCell id="kpi4-label" value="EFFICIENCY SCORE" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1215" y="550" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="kpi4-value" value="94.2" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="1360" y="555" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- Alert Section -->
        <mxCell id="alert-section-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#e0e0e0;strokeWidth=1;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="660" width="1440" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="alert-title" value="ACTIVE ALERTS" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#374151;" vertex="1" parent="1">
          <mxGeometry x="100" y="675" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Alert Items -->
        <mxCell id="alert1-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fef3c7;strokeColor=#F59E0B;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="720" width="440" height="30" as="geometry" />
        </mxCell>
        <mxCell id="alert1-icon" value="⚠" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="110" y="725" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="alert1-text" value="Memory usage approaching threshold (82%)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#92400e;" vertex="1" parent="1">
          <mxGeometry x="140" y="725" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="alert1-time" value="2 min ago" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="450" y="725" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="alert2-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0fdf4;strokeColor=#10B981;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="720" width="440" height="30" as="geometry" />
        </mxCell>
        <mxCell id="alert2-icon" value="✓" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#10B981;" vertex="1" parent="1">
          <mxGeometry x="570" y="725" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="alert2-text" value="Database optimization completed successfully" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#166534;" vertex="1" parent="1">
          <mxGeometry x="600" y="725" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="alert2-time" value="15 min ago" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="910" y="725" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="alert3-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fef3c7;strokeColor=#F59E0B;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1020" y="720" width="480" height="30" as="geometry" />
        </mxCell>
        <mxCell id="alert3-icon" value="⚠" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#F59E0B;" vertex="1" parent="1">
          <mxGeometry x="1030" y="725" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="alert3-text" value="High latency detected in Region US-WEST (>200ms)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#92400e;" vertex="1" parent="1">
          <mxGeometry x="1060" y="725" width="340" height="20" as="geometry" />
        </mxCell>
        <mxCell id="alert3-time" value="32 min ago" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1410" y="725" width="80" height="20" as="geometry" />
        </mxCell>
        
        <!-- View All Link -->
        <mxCell id="view-all" value="View All Alerts →" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2563eb;textDecoration=underline;" vertex="1" parent="1">
          <mxGeometry x="1380" y="780" width="120" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>