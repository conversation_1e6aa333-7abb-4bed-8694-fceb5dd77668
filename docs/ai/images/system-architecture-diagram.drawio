<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" etag="sys-arch-v1" version="14.0.0" type="device">
  <diagram id="system-architecture" name="System Architecture Overview">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background Layer Containers -->
        <!-- Presentation Layer -->
        <mxCell id="layer-1" value="PRESENTATION LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1e40af;strokeColor=#1e3a8a;fontColor=#ffffff;fontSize=16;fontStyle=1;verticalAlign=top;spacingTop=10;" vertex="1" parent="1">
          <mxGeometry x="100" y="80" width="960" height="150" as="geometry" />
        </mxCell>
        
        <!-- Application Layer -->
        <mxCell id="layer-2" value="APPLICATION LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#2563eb;strokeColor=#1d4ed8;fontColor=#ffffff;fontSize=16;fontStyle=1;verticalAlign=top;spacingTop=10;" vertex="1" parent="1">
          <mxGeometry x="100" y="250" width="960" height="150" as="geometry" />
        </mxCell>
        
        <!-- Business Logic Layer -->
        <mxCell id="layer-3" value="BUSINESS LOGIC LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#059669;strokeColor=#047857;fontColor=#ffffff;fontSize=16;fontStyle=1;verticalAlign=top;spacingTop=10;" vertex="1" parent="1">
          <mxGeometry x="100" y="420" width="960" height="150" as="geometry" />
        </mxCell>
        
        <!-- Data Layer -->
        <mxCell id="layer-4" value="DATA LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#10b981;strokeColor=#059669;fontColor=#ffffff;fontSize=16;fontStyle=1;verticalAlign=top;spacingTop=10;" vertex="1" parent="1">
          <mxGeometry x="100" y="590" width="960" height="150" as="geometry" />
        </mxCell>
        
        <!-- Presentation Layer Components -->
        <mxCell id="ui-gateway" value="UI Gateway&#xa;&#xa;Latency: &lt;50ms&#xa;Throughput: 10K req/s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#2563eb;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="140" y="120" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="web-portal" value="Web Portal&#xa;&#xa;Active Users: 5K&#xa;Response Time: 200ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#2563eb;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="320" y="120" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-api" value="Mobile API&#xa;&#xa;Concurrent: 2K&#xa;Cache Hit: 85%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#2563eb;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="500" y="120" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="load-balancer" value="Load Balancer&#xa;&#xa;Distribution: Round Robin&#xa;Health Check: 5s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#2563eb;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="680" y="120" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="cdn" value="CDN&#xa;&#xa;Hit Rate: 92%&#xa;Edge Locations: 15" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#2563eb;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="860" y="120" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- Application Layer Components -->
        <mxCell id="auth-service" value="Auth Service&#xa;&#xa;Token Valid: 1hr&#xa;Sessions: 50K" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bfdbfe;strokeColor=#3730a3;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="140" y="290" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="api-gateway" value="API Gateway&#xa;&#xa;Rate Limit: 1K/min&#xa;Circuit Break: 50%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bfdbfe;strokeColor=#3730a3;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="320" y="290" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="event-bus" value="Event Bus&#xa;&#xa;Msg/sec: 5K&#xa;Queue Depth: 10K" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bfdbfe;strokeColor=#3730a3;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="500" y="290" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="service-mesh" value="Service Mesh&#xa;&#xa;Services: 25&#xa;Istio Enabled" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bfdbfe;strokeColor=#3730a3;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="680" y="290" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="monitoring" value="Monitoring&#xa;&#xa;Metrics: 500/s&#xa;Alerts: P1-P3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bfdbfe;strokeColor=#3730a3;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="860" y="290" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- Business Logic Layer Components with FPGA accents -->
        <mxCell id="fpga-processor" value="FPGA Processor&#xa;&#xa;Cores: 128&#xa;Clock: 500MHz" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e9d5ff;strokeColor=#9333ea;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="140" y="460" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="ml-engine" value="ML Engine&#xa;&#xa;Models: 15&#xa;Inference: 10ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#86efac;strokeColor=#16a34a;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="320" y="460" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="rules-engine" value="Rules Engine&#xa;&#xa;Rules: 500+&#xa;Exec Time: 5ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#86efac;strokeColor=#16a34a;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="500" y="460" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="workflow-engine" value="Workflow Engine&#xa;&#xa;Active: 1K&#xa;Steps/sec: 500" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#86efac;strokeColor=#16a34a;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="680" y="460" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="batch-processor" value="Batch Processor&#xa;&#xa;Jobs: 100/hr&#xa;Parallel: 10" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e9d5ff;strokeColor=#9333ea;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="460" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- Data Layer Components -->
        <mxCell id="primary-db" value="Primary DB&#xa;&#xa;Size: 5TB&#xa;IOPS: 50K" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbf7d0;strokeColor=#15803d;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="140" y="630" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="cache-cluster" value="Cache Cluster&#xa;&#xa;Memory: 256GB&#xa;Hit Rate: 95%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbf7d0;strokeColor=#15803d;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="320" y="630" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="data-lake" value="Data Lake&#xa;&#xa;Storage: 50TB&#xa;Ingestion: 1TB/day" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbf7d0;strokeColor=#15803d;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="500" y="630" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="search-index" value="Search Index&#xa;&#xa;Docs: 10M&#xa;Query: &lt;100ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbf7d0;strokeColor=#15803d;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="680" y="630" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="backup-storage" value="Backup Storage&#xa;&#xa;Retention: 30d&#xa;RPO: 1hr" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbf7d0;strokeColor=#15803d;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="860" y="630" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- Bidirectional Communication Paths -->
        <mxCell id="comm-1-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;exitX=0.5;exitY=1;strokeColor=#1e40af;strokeWidth=3;startArrow=classic;startFill=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="web-portal" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="comm-2-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;exitX=0.5;exitY=1;strokeColor=#2563eb;strokeWidth=3;startArrow=classic;startFill=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="api-gateway" target="ml-engine">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="comm-3-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;exitX=0.5;exitY=1;strokeColor=#059669;strokeWidth=3;startArrow=classic;startFill=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="ml-engine" target="cache-cluster">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Data Flow Indicators -->
        <mxCell id="data-flow-1" value="Request Flow&#xa;10K req/s" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#1e40af;" vertex="1" parent="1">
          <mxGeometry x="380" y="230" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="data-flow-2" value="Processing Flow&#xa;5K msg/s" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2563eb;" vertex="1" parent="1">
          <mxGeometry x="380" y="400" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="data-flow-3" value="Data Flow&#xa;1TB/day" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#059669;" vertex="1" parent="1">
          <mxGeometry x="380" y="570" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Integration Points -->
        <mxCell id="integration-1" value="REST API" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fbbf24;strokeColor=#f59e0b;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="270" y="210" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-2" value="GraphQL" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fbbf24;strokeColor=#f59e0b;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="450" y="210" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-3" value="gRPC" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fbbf24;strokeColor=#f59e0b;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="630" y="380" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-4" value="Kafka" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fbbf24;strokeColor=#f59e0b;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="810" y="380" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- Performance Metrics Dashboard -->
        <mxCell id="metrics-container" value="SYSTEM METRICS" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f3f4f6;strokeColor=#9ca3af;fontSize=14;fontStyle=1;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="1100" y="80" width="200" height="660" as="geometry" />
        </mxCell>
        
        <mxCell id="metric-cpu" value="CPU Usage&#xa;68%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fef3c7;strokeColor=#f59e0b;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1120" y="120" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric-memory" value="Memory Usage&#xa;72%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fef3c7;strokeColor=#f59e0b;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1120" y="190" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric-network" value="Network I/O&#xa;850 Mbps" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fef3c7;strokeColor=#f59e0b;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1120" y="260" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric-uptime" value="Uptime&#xa;99.95%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1fae5;strokeColor=#10b981;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1120" y="330" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric-response" value="Avg Response Time&#xa;145ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#3b82f6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1120" y="400" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric-errors" value="Error Rate&#xa;0.02%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fee2e2;strokeColor=#ef4444;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1120" y="470" width="160" height="50" as="geometry" />
        </mxCell>
        
        <!-- Scalability Indicators -->
        <mxCell id="scale-horizontal" value="Horizontal Scaling&#xa;Auto-scale: 2-10 nodes" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e0e7ff;strokeColor=#6366f1;fontSize=11;dashed=1;dashPattern=5 5;" vertex="1" parent="1">
          <mxGeometry x="1120" y="540" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="scale-vertical" value="Vertical Scaling&#xa;Max: 64 vCPU, 256GB" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e0e7ff;strokeColor=#6366f1;fontSize=11;dashed=1;dashPattern=5 5;" vertex="1" parent="1">
          <mxGeometry x="1120" y="610" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="scale-regions" value="Multi-Region&#xa;US-East, EU-West, APAC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e0e7ff;strokeColor=#6366f1;fontSize=11;dashed=1;dashPattern=5 5;" vertex="1" parent="1">
          <mxGeometry x="1120" y="680" width="160" height="50" as="geometry" />
        </mxCell>
        
        <!-- Additional Communication Arrows -->
        <mxCell id="cross-comm-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9333ea;strokeWidth=2;startArrow=classic;startFill=1;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="fpga-processor" target="batch-processor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="cross-comm-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#3b82f6;strokeWidth=2;startArrow=classic;startFill=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="event-bus" target="workflow-engine">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="cross-comm-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#10b981;strokeWidth=2;startArrow=classic;startFill=1;endArrow=classic;endFill=1;" edge="1" parent="1" source="primary-db" target="backup-storage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>