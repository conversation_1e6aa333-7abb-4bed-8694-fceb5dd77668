<mxfile host="app.diagrams.net">
  <diagram name="Application Scenarios" id="showcase">
    <mxGraphModel dx="2074" dy="1156" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Main Title -->
        <mxCell id="title" value="APPLICATION SCENARIOS SHOWCASE" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontFamily=Arial;" parent="1" vertex="1">
          <mxGeometry x="400" y="20" width="800" height="50" as="geometry"/>
        </mxCell>
        
        <!-- Healthcare Monitoring Panel -->
        <mxCell id="health-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#0EA5E9;strokeColor=none;opacity=10;" parent="1" vertex="1">
          <mxGeometry x="40" y="100" width="480" height="750" as="geometry"/>
        </mxCell>
        
        <mxCell id="health-title" value="HEALTHCARE MONITORING" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#0EA5E9;fontFamily=Arial;" parent="1" vertex="1">
          <mxGeometry x="180" y="120" width="200" height="40" as="geometry"/>
        </mxCell>
        
        <!-- Patient -->
        <mxCell id="patient" value="Patient" style="shape=actor;whiteSpace=wrap;html=1;fillColor=#0EA5E9;strokeColor=#0EA5E9;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="180" width="60" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Wearable Sensors -->
        <mxCell id="heart-sensor" value="Heart Rate&#xa;Monitor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#0EA5E9;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="80" y="300" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="bp-sensor" value="Blood Pressure&#xa;Sensor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#0EA5E9;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="230" y="300" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="o2-sensor" value="O2 Saturation&#xa;Monitor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#0EA5E9;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="380" y="300" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Connections from Patient to Sensors -->
        <mxCell id="p-to-h" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;" parent="1" edge="1" source="patient" target="heart-sensor">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="p-to-b" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;" parent="1" edge="1" source="patient" target="bp-sensor">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="p-to-o" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;" parent="1" edge="1" source="patient" target="o2-sensor">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Edge Processing -->
        <mxCell id="edge-health" value="Edge Processing&#xa;Unit" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#0EA5E9;strokeColor=none;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="210" y="400" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Data Flow from Sensors to Edge -->
        <mxCell id="h-to-edge" value="Real-time Data" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;fontSize=11;" parent="1" edge="1" source="heart-sensor" target="edge-health">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="b-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;" parent="1" edge="1" source="bp-sensor" target="edge-health">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="o-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;" parent="1" edge="1" source="o2-sensor" target="edge-health">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Cloud Platform -->
        <mxCell id="cloud-health" value="Healthcare Cloud&#xa;Platform" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#0EA5E9;strokeWidth=2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="195" y="520" width="170" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="edge-to-cloud" value="Processed Data" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;fontSize=11;" parent="1" edge="1" source="edge-health" target="cloud-health">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Healthcare Professionals -->
        <mxCell id="doctor" value="Doctor" style="shape=actor;whiteSpace=wrap;html=1;fillColor=#0EA5E9;strokeColor=#0EA5E9;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="660" width="60" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="nurse" value="Nurse" style="shape=actor;whiteSpace=wrap;html=1;fillColor=#0EA5E9;strokeColor=#0EA5E9;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="660" width="60" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="alert-system" value="Alert System" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#0EA5E9;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="380" y="670" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Connections from Cloud to Healthcare Staff -->
        <mxCell id="cloud-to-doc" value="" style="endArrow=classic;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;curved=1;" parent="1" edge="1" source="cloud-health" target="doctor">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="cloud-to-nurse" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;" parent="1" edge="1" source="cloud-health" target="nurse">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="cloud-to-alert" value="" style="endArrow=classic;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#0EA5E9;strokeWidth=2;curved=1;" parent="1" edge="1" source="cloud-health" target="alert-system">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Performance Metrics -->
        <mxCell id="health-metrics" value="PERFORMANCE METRICS&#xa;• Response Time: &lt;100ms&#xa;• Accuracy: 99.7%&#xa;• Uptime: 99.99%&#xa;• Battery Life: 7 days" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#0EA5E9;strokeColor=none;fontColor=#FFFFFF;fontSize=12;align=left;spacingLeft=10;" parent="1" vertex="1">
          <mxGeometry x="80" y="770" width="200" height="70" as="geometry"/>
        </mxCell>
        
        <!-- Autonomous Vehicle Panel -->
        <mxCell id="auto-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#EF4444;strokeColor=none;opacity=10;" parent="1" vertex="1">
          <mxGeometry x="560" y="100" width="480" height="750" as="geometry"/>
        </mxCell>
        
        <mxCell id="auto-title" value="AUTONOMOUS VEHICLE NAVIGATION" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#EF4444;fontFamily=Arial;" parent="1" vertex="1">
          <mxGeometry x="640" y="120" width="320" height="40" as="geometry"/>
        </mxCell>
        
        <!-- Vehicle -->
        <mxCell id="vehicle" value="" style="shape=mxgraph.signs.transportation.car_3;html=1;pointerEvents=1;fillColor=#EF4444;strokeColor=none;verticalLabelPosition=bottom;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="750" y="200" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Sensor Array -->
        <mxCell id="lidar" value="LiDAR&#xa;360°" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="300" width="80" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="cameras" value="Camera&#xa;Array" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="710" y="300" width="80" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="radar" value="Radar&#xa;Sensors" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="820" y="300" width="80" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="gps" value="GPS/IMU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="930" y="300" width="80" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Connections from Vehicle to Sensors -->
        <mxCell id="v-to-l" value="" style="endArrow=classic;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="vehicle" target="lidar">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="v-to-c" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="vehicle" target="cameras">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="v-to-r" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="vehicle" target="radar">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="v-to-g" value="" style="endArrow=classic;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="vehicle" target="gps">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Vehicle Edge Computer -->
        <mxCell id="edge-auto" value="Vehicle Edge&#xa;Computer" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#EF4444;strokeColor=none;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="730" y="400" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Data Flow to Edge -->
        <mxCell id="l-to-edge" value="Point Cloud" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;fontSize=11;" parent="1" edge="1" source="lidar" target="edge-auto">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="c-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="cameras" target="edge-auto">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="r-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="radar" target="edge-auto">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="g-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="gps" target="edge-auto">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Decision Making Components -->
        <mxCell id="perception" value="Perception&#xa;Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="520" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="planning" value="Path&#xa;Planning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="750" y="520" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="control" value="Vehicle&#xa;Control" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="900" y="520" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Connections from Edge to Decision Components -->
        <mxCell id="edge-to-perc" value="" style="endArrow=classic;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="edge-auto" target="perception">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="edge-to-plan" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="edge-auto" target="planning">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="edge-to-ctrl" value="" style="endArrow=classic;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;" parent="1" edge="1" source="edge-auto" target="control">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Cloud Connection -->
        <mxCell id="cloud-auto" value="Fleet Management&#xa;Cloud" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#EF4444;strokeWidth=2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="715" y="620" width="170" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="plan-to-cloud" value="Telemetry" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#EF4444;strokeWidth=2;fontSize=11;" parent="1" edge="1" source="planning" target="cloud-auto">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Performance Metrics -->
        <mxCell id="auto-metrics" value="PERFORMANCE METRICS&#xa;• Processing: 10ms latency&#xa;• Detection: 99.9% accuracy&#xa;• Range: 200m visibility&#xa;• Updates: 100Hz" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#EF4444;strokeColor=none;fontColor=#FFFFFF;fontSize=12;align=left;spacingLeft=10;" parent="1" vertex="1">
          <mxGeometry x="600" y="770" width="200" height="70" as="geometry"/>
        </mxCell>
        
        <!-- Industrial Robotics Panel -->
        <mxCell id="ind-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=none;opacity=10;" parent="1" vertex="1">
          <mxGeometry x="1080" y="100" width="480" height="750" as="geometry"/>
        </mxCell>
        
        <mxCell id="ind-title" value="INDUSTRIAL ROBOTICS COLLABORATION" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#F97316;fontFamily=Arial;" parent="1" vertex="1">
          <mxGeometry x="1160" y="120" width="320" height="40" as="geometry"/>
        </mxCell>
        
        <!-- Human Worker -->
        <mxCell id="worker" value="Human&#xa;Worker" style="shape=actor;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=#F97316;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1150" y="180" width="60" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Robot Arm -->
        <mxCell id="robot" value="Collaborative&#xa;Robot" style="shape=mxgraph.signs.tech.cell_phone;html=1;pointerEvents=1;fillColor=#F97316;strokeColor=none;verticalLabelPosition=bottom;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="1380" y="180" width="60" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Safety Sensors -->
        <mxCell id="proximity" value="Proximity&#xa;Sensors" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1120" y="300" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="vision" value="Vision&#xa;System" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1250" y="300" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="force" value="Force/Torque&#xa;Sensors" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1380" y="300" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Connections -->
        <mxCell id="w-to-p" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="worker" target="proximity">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="w-to-v" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;curved=1;" parent="1" edge="1" source="worker" target="vision">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="r-to-f" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="robot" target="force">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Edge Controller -->
        <mxCell id="edge-ind" value="Safety Edge&#xa;Controller" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#F97316;strokeColor=none;fontColor=#FFFFFF;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1250" y="400" width="140" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Sensor to Edge Connections -->
        <mxCell id="p-to-edge" value="Distance Data" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;fontSize=11;" parent="1" edge="1" source="proximity" target="edge-ind">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="v-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="vision" target="edge-ind">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="f-to-edge" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="force" target="edge-ind">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Safety and Control Modules -->
        <mxCell id="safety" value="Safety&#xa;Monitor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1120" y="520" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="motion" value="Motion&#xa;Control" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1270" y="520" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="task" value="Task&#xa;Planner" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1420" y="520" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Edge to Control Connections -->
        <mxCell id="edge-to-safe" value="" style="endArrow=classic;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="edge-ind" target="safety">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="edge-to-motion" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="edge-ind" target="motion">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="edge-to-task" value="" style="endArrow=classic;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;" parent="1" edge="1" source="edge-ind" target="task">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Factory Cloud -->
        <mxCell id="cloud-ind" value="Factory Operations&#xa;Cloud" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F97316;strokeWidth=2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1235" y="620" width="170" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="motion-to-cloud" value="Production Data" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#F97316;strokeWidth=2;fontSize=11;" parent="1" edge="1" source="motion" target="cloud-ind">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
        
        <!-- Performance Metrics -->
        <mxCell id="ind-metrics" value="PERFORMANCE METRICS&#xa;• Safety Zone: 0.5m radius&#xa;• Reaction Time: 5ms&#xa;• Precision: ±0.1mm&#xa;• Productivity: +40%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=none;fontColor=#FFFFFF;fontSize=12;align=left;spacingLeft=10;" parent="1" vertex="1">
          <mxGeometry x="1120" y="770" width="200" height="70" as="geometry"/>
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-title" value="LEGEND" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontFamily=Arial;" parent="1" vertex="1">
          <mxGeometry x="1350" y="770" width="80" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend-sensor" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#666666;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="1350" y="800" width="30" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend-sensor-text" value="Sensor/Component" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="1390" y="800" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend-edge" value="" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#666666;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="1350" y="825" width="30" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend-edge-text" value="Edge Processing" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="1390" y="825" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend-cloud" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#666666;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="1345" y="850" width="40" height="25" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend-cloud-text" value="Cloud Platform" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="1390" y="852" width="120" height="20" as="geometry"/>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>