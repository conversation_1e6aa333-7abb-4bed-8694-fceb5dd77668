<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="Mozilla/5.0" etag="hardware-arch" version="21.0.0" type="device">
  <diagram name="Hardware Architecture" id="hardware-architecture">
    <mxGraphModel dx="2000" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" background="#0f172a" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background -->
        <mxCell id="bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1e293b;strokeColor=#334155;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1520" height="1120" as="geometry" />
        </mxCell>
        
        <!-- Title -->
        <mxCell id="title" value="HARDWARE ARCHITECTURE &amp; INTEGRATION" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#f1f5f9;" vertex="1" parent="1">
          <mxGeometry x="600" y="60" width="400" height="40" as="geometry" />
        </mxCell>
        
        <!-- CPU Section -->
        <mxCell id="cpu-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#3B82F6;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="140" width="400" height="300" as="geometry" />
        </mxCell>
        
        <mxCell id="cpu1" value="CPU 0&lt;br&gt;64 Cores&lt;br&gt;3.5 GHz&lt;br&gt;280W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=#1e40af;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="160" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="cpu2" value="CPU 1&lt;br&gt;64 Cores&lt;br&gt;3.5 GHz&lt;br&gt;280W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3B82F6;strokeColor=#1e40af;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="180" width="160" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="cpu-interconnect" value="UPI 3.0&lt;br&gt;20.8 GT/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#3B82F6;strokeWidth=3;fontColor=#3B82F6;fontSize=10;" edge="1" parent="1" source="cpu1" target="cpu2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Memory Section -->
        <mxCell id="memory-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#F97316;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="480" width="400" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="memory1" value="DDR5 Memory&lt;br&gt;512GB (8x64GB)&lt;br&gt;5600 MT/s&lt;br&gt;ECC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=#c2410c;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="520" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="memory2" value="DDR5 Memory&lt;br&gt;512GB (8x64GB)&lt;br&gt;5600 MT/s&lt;br&gt;ECC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F97316;strokeColor=#c2410c;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- GPU Section -->
        <mxCell id="gpu-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#22C55E;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="140" width="480" height="540" as="geometry" />
        </mxCell>
        
        <mxCell id="gpu1" value="GPU 0&lt;br&gt;H100&lt;br&gt;80GB HBM3&lt;br&gt;700W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#22C55E;strokeColor=#15803d;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="180" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="gpu2" value="GPU 1&lt;br&gt;H100&lt;br&gt;80GB HBM3&lt;br&gt;700W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#22C55E;strokeColor=#15803d;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="180" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="gpu3" value="GPU 2&lt;br&gt;H100&lt;br&gt;80GB HBM3&lt;br&gt;700W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#22C55E;strokeColor=#15803d;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="320" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="gpu4" value="GPU 3&lt;br&gt;H100&lt;br&gt;80GB HBM3&lt;br&gt;700W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#22C55E;strokeColor=#15803d;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="320" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- NVLink connections -->
        <mxCell id="nvlink1" value="NVLink 4.0&lt;br&gt;900 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#22C55E;strokeWidth=3;fontColor=#22C55E;fontSize=10;" edge="1" parent="1" source="gpu1" target="gpu2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="nvlink2" value="NVLink 4.0&lt;br&gt;900 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#22C55E;strokeWidth=3;fontColor=#22C55E;fontSize=10;" edge="1" parent="1" source="gpu1" target="gpu3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="nvlink3" value="NVLink 4.0&lt;br&gt;900 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#22C55E;strokeWidth=3;fontColor=#22C55E;fontSize=10;" edge="1" parent="1" source="gpu3" target="gpu4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="nvlink4" value="NVLink 4.0&lt;br&gt;900 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#22C55E;strokeWidth=3;fontColor=#22C55E;fontSize=10;" edge="1" parent="1" source="gpu2" target="gpu4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- FPGA Section -->
        <mxCell id="fpga-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#8B5CF6;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="1080" y="140" width="440" height="300" as="geometry" />
        </mxCell>
        
        <mxCell id="fpga1" value="FPGA 0&lt;br&gt;Xilinx VU13P&lt;br&gt;32GB HBM2&lt;br&gt;225W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=#6d28d9;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1100" y="180" width="180" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="fpga2" value="FPGA 1&lt;br&gt;Xilinx VU13P&lt;br&gt;32GB HBM2&lt;br&gt;225W TDP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#8B5CF6;strokeColor=#6d28d9;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1320" y="180" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- Storage Section -->
        <mxCell id="storage-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#64748b;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="1080" y="480" width="440" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="nvme1" value="NVMe SSD Array&lt;br&gt;8x 7.68TB&lt;br&gt;PCIe 5.0 x4&lt;br&gt;7 GB/s per drive" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#64748b;strokeColor=#475569;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1100" y="520" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="nvme2" value="NVMe SSD Array&lt;br&gt;8x 7.68TB&lt;br&gt;PCIe 5.0 x4&lt;br&gt;7 GB/s per drive" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#64748b;strokeColor=#475569;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1320" y="520" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Network Section -->
        <mxCell id="network-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#06b6d4;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="720" width="480" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="nic1" value="Network Interface&lt;br&gt;400GbE&lt;br&gt;RDMA Support&lt;br&gt;Dual Port" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#06b6d4;strokeColor=#0891b2;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="760" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="nic2" value="InfiniBand&lt;br&gt;HDR 200Gb/s&lt;br&gt;Low Latency&lt;br&gt;Dual Port" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#06b6d4;strokeColor=#0891b2;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="760" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Power Management -->
        <mxCell id="power-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#ef4444;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="940" width="400" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="psu1" value="PSU 1&lt;br&gt;2000W&lt;br&gt;80+ Titanium&lt;br&gt;Redundant" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ef4444;strokeColor=#dc2626;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="980" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="psu2" value="PSU 2&lt;br&gt;2000W&lt;br&gt;80+ Titanium&lt;br&gt;Redundant" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ef4444;strokeColor=#dc2626;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="980" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- Expansion Slots -->
        <mxCell id="expansion-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#a855f7;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="940" width="480" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="expansion" value="Expansion Slots&lt;br&gt;4x PCIe 5.0 x16&lt;br&gt;2x PCIe 5.0 x8&lt;br&gt;CXL 2.0 Support" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a855f7;strokeColor=#7c3aed;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="670" y="980" width="220" height="80" as="geometry" />
        </mxCell>
        
        <!-- Thermal Management -->
        <mxCell id="thermal-section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#334155;strokeColor=#f59e0b;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="1080" y="940" width="440" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="cooling" value="Liquid Cooling System&lt;br&gt;Direct-to-Chip&lt;br&gt;5kW Heat Dissipation&lt;br&gt;Redundant Pumps" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f59e0b;strokeColor=#d97706;fontColor=#ffffff;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1210" y="980" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Interconnections -->
        <mxCell id="cpu-gpu1" value="PCIe 5.0 x16&lt;br&gt;64 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#94a3b8;strokeWidth=2;fontColor=#94a3b8;fontSize=10;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cpu1" target="gpu1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="cpu-gpu2" value="PCIe 5.0 x16&lt;br&gt;64 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#94a3b8;strokeWidth=2;fontColor=#94a3b8;fontSize=10;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cpu2" target="gpu3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="cpu-fpga1" value="PCIe 5.0 x16&lt;br&gt;64 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#94a3b8;strokeWidth=2;fontColor=#94a3b8;fontSize=10;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cpu-section" target="fpga1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="480" y="190" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="cpu-memory1" value="DDR5&lt;br&gt;89.6 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#94a3b8;strokeWidth=2;fontColor=#94a3b8;fontSize=10;" edge="1" parent="1" source="cpu1" target="memory1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="cpu-memory2" value="DDR5&lt;br&gt;89.6 GB/s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#94a3b8;strokeWidth=2;fontColor=#94a3b8;fontSize=10;" edge="1" parent="1" source="cpu2" target="memory2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Labels -->
        <mxCell id="label-cpu" value="COMPUTE PROCESSORS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#3B82F6;" vertex="1" parent="1">
          <mxGeometry x="200" y="150" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label-gpu" value="GPU ACCELERATORS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#22C55E;" vertex="1" parent="1">
          <mxGeometry x="700" y="150" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label-fpga" value="FPGA ACCELERATORS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#8B5CF6;" vertex="1" parent="1">
          <mxGeometry x="1220" y="150" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label-memory" value="MEMORY SUBSYSTEM" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#F97316;" vertex="1" parent="1">
          <mxGeometry x="200" y="490" width="160" height="20" as="geometry" />
        </mxCell>
        
        <!-- Bandwidth Legend -->
        <mxCell id="legend" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1e293b;strokeColor=#334155;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1180" y="740" width="340" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-title" value="BANDWIDTH SPECIFICATIONS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#f1f5f9;" vertex="1" parent="1">
          <mxGeometry x="1270" y="750" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend1" value="• NVLink 4.0: 900 GB/s bidirectional" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="1200" y="780" width="300" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend2" value="• PCIe 5.0 x16: 64 GB/s bidirectional" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="1200" y="800" width="300" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend3" value="• DDR5-5600: 89.6 GB/s per channel" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="1200" y="820" width="300" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend4" value="• InfiniBand HDR: 200 Gb/s" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="1200" y="840" width="300" height="20" as="geometry" />
        </mxCell>
        
        <!-- System Specifications Box -->
        <mxCell id="specs-box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1e293b;strokeColor=#334155;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="740" width="400" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="specs-title" value="SYSTEM SPECIFICATIONS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#f1f5f9;" vertex="1" parent="1">
          <mxGeometry x="200" y="750" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="spec1" value="• Total System Memory: 1TB DDR5 ECC" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="100" y="780" width="360" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="spec2" value="• Total GPU Memory: 320GB HBM3" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="100" y="800" width="360" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="spec3" value="• Total Storage: 122.88TB NVMe" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="100" y="820" width="360" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="spec4" value="• Max Power Consumption: 5.4kW" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="100" y="840" width="360" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="spec5" value="• Cooling Requirement: 5kW Liquid" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#94a3b8;" vertex="1" parent="1">
          <mxGeometry x="100" y="860" width="360" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>