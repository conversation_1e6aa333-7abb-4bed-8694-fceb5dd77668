<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="" version="14.6.13" type="device">
  <diagram name="Technology Architecture" id="tech-arch">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background Security Zones -->
        <mxCell id="sec-zone-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#DC2626;strokeWidth=3;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1520" height="820" as="geometry" />
        </mxCell>
        
        <mxCell id="sec-label-1" value="SECURITY PERIMETER" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontColor=#DC2626;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="40" y="20" width="160" height="20" as="geometry" />
        </mxCell>
        
        <!-- DMZ Zone -->
        <mxCell id="dmz-zone" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="1440" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="dmz-label" value="DMZ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontColor=#DC2626;" vertex="1" parent="1">
          <mxGeometry x="80" y="60" width="60" height="20" as="geometry" />
        </mxCell>
        
        <!-- Presentation Layer -->
        <mxCell id="layer-1" value="PRESENTATION LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6B7280;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="1400" height="30" as="geometry" />
        </mxCell>
        
        <!-- CDN -->
        <mxCell id="cdn" value="CloudFront CDN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="150" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- WAF -->
        <mxCell id="waf" value="Web Application&#xa;Firewall (WAF)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="150" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Load Balancer -->
        <mxCell id="lb" value="Application&#xa;Load Balancer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="440" y="150" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- API Gateway -->
        <mxCell id="api-gw" value="API Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="150" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Web Servers -->
        <mxCell id="web-1" value="Nginx&#xa;Web Server" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="760" y="150" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="web-2" value="Nginx&#xa;Web Server" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="880" y="150" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="web-3" value="Nginx&#xa;Web Server" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="150" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- Scalability Indicator -->
        <mxCell id="scale-1" value="Auto Scaling Group" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2;fontColor=#059669;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="820" y="200" width="220" height="20" as="geometry" />
        </mxCell>
        
        <!-- Static Assets -->
        <mxCell id="static" value="Static Assets&#xa;S3 Bucket" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1140" y="150" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- Mobile Gateway -->
        <mxCell id="mobile-gw" value="Mobile&#xa;Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1280" y="150" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- Application Layer -->
        <mxCell id="layer-2" value="APPLICATION LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6B7280;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="250" width="1400" height="30" as="geometry" />
        </mxCell>
        
        <!-- Microservices -->
        <mxCell id="ms-1" value="User Service&#xa;(Node.js)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ms-2" value="Order Service&#xa;(Java Spring)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="240" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ms-3" value="Payment Service&#xa;(.NET Core)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ms-4" value="Inventory Service&#xa;(Python)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ms-5" value="Analytics Service&#xa;(Go)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Container Orchestration -->
        <mxCell id="k8s" value="Kubernetes Cluster&#xa;(EKS)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="740" y="300" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Service Mesh -->
        <mxCell id="mesh" value="Service Mesh&#xa;(Istio)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="900" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Message Queue -->
        <mxCell id="mq" value="Message Queue&#xa;(RabbitMQ)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1020" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Cache -->
        <mxCell id="cache" value="Redis Cache&#xa;Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1140" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Search -->
        <mxCell id="search" value="Elasticsearch&#xa;Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1260" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Serverless -->
        <mxCell id="lambda" value="Lambda&#xa;Functions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1380" y="300" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Data Layer -->
        <mxCell id="layer-3" value="DATA LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6B7280;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="400" width="1400" height="30" as="geometry" />
        </mxCell>
        
        <!-- Databases -->
        <mxCell id="db-primary" value="PostgreSQL&#xa;Primary&#xa;(RDS Multi-AZ)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="450" width="120" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="db-replica" value="PostgreSQL&#xa;Read Replicas" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="260" y="450" width="120" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="nosql" value="MongoDB&#xa;Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="400" y="450" width="100" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="warehouse" value="Data Warehouse&#xa;(Redshift)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="520" y="450" width="120" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="lake" value="Data Lake&#xa;(S3)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="660" y="450" width="100" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="stream" value="Stream Processing&#xa;(Kafka)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="780" y="450" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Backup -->
        <mxCell id="backup" value="Backup Storage&#xa;(Glacier)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="920" y="450" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- ETL -->
        <mxCell id="etl" value="ETL Pipeline&#xa;(Airflow)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1040" y="450" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- Infrastructure Layer -->
        <mxCell id="layer-4" value="INFRASTRUCTURE LAYER" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6B7280;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="570" width="1400" height="30" as="geometry" />
        </mxCell>
        
        <!-- Compute -->
        <mxCell id="compute-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#6B7280;strokeWidth=1;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="110" y="620" width="280" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="compute-label" value="COMPUTE" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="110" y="600" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="ec2" value="EC2 Instances&#xa;(Auto Scaling)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="640" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="fargate" value="Fargate&#xa;Containers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="230" y="640" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="spot" value="Spot Instances" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="690" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="gpu" value="GPU Instances&#xa;(ML Workloads)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="230" y="690" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- Network -->
        <mxCell id="network-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#6B7280;strokeWidth=1;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="410" y="620" width="380" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="network-label" value="NETWORK" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="410" y="600" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="vpc" value="VPC&#xa;10.0.0.0/16" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="420" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="subnet-pub" value="Public Subnets&#xa;Multi-AZ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="510" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="subnet-priv" value="Private Subnets&#xa;Multi-AZ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="nat" value="NAT Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="690" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="direct-connect" value="Direct Connect&#xa;10 Gbps" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="420" y="690" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="vpn" value="Site-to-Site&#xa;VPN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="530" y="690" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="route53" value="Route 53&#xa;DNS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="620" y="690" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Storage -->
        <mxCell id="storage-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3F4F6;strokeColor=#6B7280;strokeWidth=1;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="810" y="620" width="280" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="storage-label" value="STORAGE" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="810" y="600" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="ebs" value="EBS Volumes&#xa;(GP3/IO2)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="820" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="efs" value="EFS&#xa;(Shared)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="910" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="s3-storage" value="S3 Buckets" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="fsx" value="FSx for&#xa;Lustre" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="820" y="690" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="storage-gw" value="Storage&#xa;Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="910" y="690" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Security -->
        <mxCell id="security-group" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=1;dashed=1;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="1110" y="620" width="380" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="security-label" value="SECURITY" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=10;fontColor=#DC2626;" vertex="1" parent="1">
          <mxGeometry x="1110" y="600" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="iam" value="IAM Roles &amp;&#xa;Policies" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1120" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="kms" value="KMS&#xa;Encryption" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1210" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="secrets" value="Secrets&#xa;Manager" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1300" y="640" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="shield" value="AWS Shield&#xa;DDoS Protection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1390" y="640" width="90" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="guardduty" value="GuardDuty&#xa;Threat Detection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1120" y="690" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="security-hub" value="Security Hub" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1230" y="690" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="compliance" value="Compliance&#xa;Manager" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1320" y="690" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Monitoring Layer -->
        <mxCell id="layer-5" value="MONITORING &amp; MANAGEMENT" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6B7280;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="770" width="1400" height="30" as="geometry" />
        </mxCell>
        
        <!-- Monitoring Components -->
        <mxCell id="cloudwatch" value="CloudWatch&#xa;Metrics &amp; Logs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="xray" value="X-Ray&#xa;Tracing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="240" y="820" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="prometheus" value="Prometheus&#xa;Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="340" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="grafana" value="Grafana&#xa;Dashboards" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="460" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="elk" value="ELK Stack&#xa;Log Analysis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="580" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="terraform" value="Terraform&#xa;IaC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="720" y="820" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ansible" value="Ansible&#xa;Config Mgmt" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="820" y="820" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="jenkins" value="Jenkins&#xa;CI/CD" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="920" y="820" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="gitlab" value="GitLab&#xa;Source Control" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1020" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="sonarqube" value="SonarQube&#xa;Code Quality" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1140" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="pagerduty" value="PagerDuty&#xa;Alerting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1260" y="820" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="siem" value="SIEM&#xa;Security Events" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1360" y="820" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- Connections -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;" edge="1" parent="1" source="cdn" target="waf">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;" edge="1" parent="1" source="waf" target="lb">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;" edge="1" parent="1" source="lb" target="api-gw">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;" edge="1" parent="1" source="api-gw" target="web-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" value="" style="endArrow=classic;html=1;strokeColor=#059669;strokeWidth=2;dashed=1;" edge="1" parent="1" source="web-1" target="ms-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="810" y="200" as="sourcePoint" />
            <mxPoint x="180" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow6" value="" style="endArrow=classic;html=1;strokeColor=#6B7280;strokeWidth=2;" edge="1" parent="1" source="ms-1" target="db-primary">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow7" value="" style="endArrow=classic;html=1;strokeColor=#059669;strokeWidth=2;dashed=1;" edge="1" parent="1" source="ms-2" target="cache">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="360" as="sourcePoint" />
            <mxPoint x="1190" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow8" value="" style="endArrow=classic;html=1;strokeColor=#DC2626;strokeWidth=2;" edge="1" parent="1" source="k8s" target="mesh">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F9FAFB;strokeColor=#6B7280;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1540" y="670" width="140" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-title" value="LEGEND" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1540" y="675" width="140" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E7EB;strokeColor=#6B7280;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1550" y="700" width="30" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-1-text" value="Infrastructure" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1585" y="700" width="80" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#059669;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1550" y="720" width="30" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-2-text" value="Scalable" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1585" y="720" width="80" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEE2E2;strokeColor=#DC2626;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1550" y="740" width="30" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-3-text" value="Security" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1585" y="740" width="80" height="15" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>