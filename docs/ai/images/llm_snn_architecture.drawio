<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" version="21.0.0" etag="llm-snn-architecture" type="device">
  <diagram name="LLM-SNN Architecture" id="llm-snn-coupling">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Background gradient -->
        <mxCell id="bg-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=none;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1120" height="720" as="geometry" />
        </mxCell>
        
        <!-- Title -->
        <mxCell id="title-1" value="LLM-SNN HETEROGENEOUS COMPUTING ARCHITECTURE" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontFamily=Segoe UI;fontColor=#1a1a1a;" vertex="1" parent="1">
          <mxGeometry x="300" y="60" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- LLM System Container -->
        <mxCell id="llm-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#0052cc;strokeWidth=3;shadow=1;arcSize=5;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="340" height="400" as="geometry" />
        </mxCell>
        
        <!-- LLM System Box -->
        <mxCell id="llm-system" value="LLM SYSTEM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#0052cc;strokeColor=#004494;strokeWidth=2;fontColor=#ffffff;fontSize=18;fontStyle=1;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="150" y="210" width="240" height="80" as="geometry" />
        </mxCell>
        
        <!-- LLM Icon -->
        <mxCell id="llm-icon" value="🧠" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=32;" vertex="1" parent="1">
          <mxGeometry x="250" y="230" width="40" height="40" as="geometry" />
        </mxCell>
        
        <!-- SNN System Container -->
        <mxCell id="snn-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#006400;strokeWidth=3;shadow=1;arcSize=5;" vertex="1" parent="1">
          <mxGeometry x="760" y="180" width="340" height="400" as="geometry" />
        </mxCell>
        
        <!-- SNN System Box -->
        <mxCell id="snn-system" value="SNN SYSTEM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#006400;strokeColor=#004d00;strokeWidth=2;fontColor=#ffffff;fontSize=18;fontStyle=1;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="810" y="210" width="240" height="80" as="geometry" />
        </mxCell>
        
        <!-- SNN Icon -->
        <mxCell id="snn-icon" value="⚡" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=32;" vertex="1" parent="1">
          <mxGeometry x="910" y="230" width="40" height="40" as="geometry" />
        </mxCell>
        
        <!-- Arbitration Logic (Central) -->
        <mxCell id="arbitration-logic" value="ARBITRATION LOGIC" style="rhombus;whiteSpace=wrap;html=1;fillColor=#4b4b4b;strokeColor=#2e2e2e;strokeWidth=2;fontColor=#ffffff;fontSize=16;fontStyle=1;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="480" y="340" width="240" height="100" as="geometry" />
        </mxCell>
        
        <!-- Priority Queue Symbol -->
        <mxCell id="priority-queue" value="⏱️ FIFO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#666666;strokeColor=#333333;strokeWidth=1;fontColor=#ffffff;fontSize=12;fontStyle=0;shadow=0;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="560" y="375" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- Priority Arbitration Annotation -->
        <mxCell id="priority-annotation" value="Priority Arbitration" style="text;html=1;strokeColor=none;fillColor=#ffe6cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=2;fontFamily=Segoe UI;fontColor=#333333;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="460" width="160" height="30" as="geometry" />
        </mxCell>
        
        <!-- PCIe Component -->
        <mxCell id="pcie" value="PCIe Gen 5.0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9999;strokeColor=#cc0000;strokeWidth=2;fontSize=14;fontStyle=1;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="480" y="240" width="240" height="40" as="geometry" />
        </mxCell>
        
        <!-- DMA Component -->
        <mxCell id="dma" value="DMA Controller" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9999;strokeColor=#cc0000;strokeWidth=2;fontSize=14;fontStyle=1;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="480" y="500" width="240" height="40" as="geometry" />
        </mxCell>
        
        <!-- Sub-Millisecond Coupling Annotation -->
        <mxCell id="submilli-annotation" value="Sub-Millisecond Coupling" style="text;html=1;strokeColor=none;fillColor=#ccffcc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=2;fontFamily=Segoe UI;fontColor=#333333;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="510" y="190" width="180" height="30" as="geometry" />
        </mxCell>
        
        <!-- Kernel Drivers (LLM Side) -->
        <mxCell id="kernel-llm" value="Kernel Drivers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b3d9ff;strokeColor=#0066cc;strokeWidth=2;fontSize=14;fontStyle=0;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="180" y="340" width="180" height="40" as="geometry" />
        </mxCell>
        
        <!-- gRPC (LLM Side) -->
        <mxCell id="grpc-llm" value="gRPC Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b3d9ff;strokeColor=#0066cc;strokeWidth=2;fontSize=14;fontStyle=0;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="180" y="420" width="180" height="40" as="geometry" />
        </mxCell>
        
        <!-- Kernel Drivers (SNN Side) -->
        <mxCell id="kernel-snn" value="Kernel Drivers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b3ffb3;strokeColor=#009900;strokeWidth=2;fontSize=14;fontStyle=0;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="840" y="340" width="180" height="40" as="geometry" />
        </mxCell>
        
        <!-- gRPC (SNN Side) -->
        <mxCell id="grpc-snn" value="gRPC Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b3ffb3;strokeColor=#009900;strokeWidth=2;fontSize=14;fontStyle=0;shadow=1;fontFamily=Segoe UI;" vertex="1" parent="1">
          <mxGeometry x="840" y="420" width="180" height="40" as="geometry" />
        </mxCell>
        
        <!-- Bidirectional Arrows -->
        <!-- LLM to Arbitration -->
        <mxCell id="arrow-llm-arb1" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#0052cc;curved=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="kernel-llm" target="arbitration-logic">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-llm-arb2" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#0052cc;curved=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="grpc-llm" target="arbitration-logic">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- SNN to Arbitration -->
        <mxCell id="arrow-snn-arb1" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#006400;curved=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="kernel-snn" target="arbitration-logic">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="400" as="sourcePoint" />
            <mxPoint x="800" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow-snn-arb2" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#006400;curved=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="grpc-snn" target="arbitration-logic">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="450" as="sourcePoint" />
            <mxPoint x="800" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- PCIe to Arbitration -->
        <mxCell id="arrow-pcie-arb" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#cc0000;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="pcie" target="arbitration-logic">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="300" as="sourcePoint" />
            <mxPoint x="600" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- DMA to Arbitration -->
        <mxCell id="arrow-dma-arb" value="" style="endArrow=classic;startArrow=classic;html=1;strokeWidth=3;strokeColor=#cc0000;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="dma" target="arbitration-logic">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="480" as="sourcePoint" />
            <mxPoint x="600" y="430" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Performance Metrics -->
        <mxCell id="metrics-box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#999999;strokeWidth=2;shadow=1;arcSize=5;" vertex="1" parent="1">
          <mxGeometry x="440" y="620" width="320" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="metrics-title" value="PERFORMANCE METRICS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontFamily=Segoe UI;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="500" y="630" width="200" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="metrics-latency" value="• Latency: &lt; 1ms" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontFamily=Segoe UI;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="460" y="660" width="140" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="metrics-throughput" value="• Throughput: 128 GB/s" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontFamily=Segoe UI;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="460" y="680" width="140" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="metrics-bandwidth" value="• PCIe 5.0 Bandwidth" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontFamily=Segoe UI;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="600" y="660" width="140" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="metrics-qos" value="• QoS Guaranteed" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontFamily=Segoe UI;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="600" y="680" width="140" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>