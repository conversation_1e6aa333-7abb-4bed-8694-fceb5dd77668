<mxfile host="65bd71144e">
    <diagram name="Neural Processing Architecture" id="neural-loops-diagram">
        <mxGraphModel dx="641" dy="645" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="bg-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=none;gradientColor=#e6e6e6;gradientDirection=north;" parent="1" vertex="1">
                    <mxGeometry x="40" y="40" width="740" height="500" as="geometry"/>
                </mxCell>
                <mxCell id="title-1" value="&lt;b style=&quot;font-size: 24px;&quot;&gt;Neural Processing Dual-Loop Architecture&lt;/b&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="240" y="60" width="340" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="llm-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dbeafe;strokeColor=#1e40af;strokeWidth=2;shadow=1;arcSize=10;" parent="1" vertex="1">
                    <mxGeometry x="80" y="140" width="280" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="llm-label" value="&lt;b style=&quot;font-size: 18px;&quot;&gt;Cognitive Loop&lt;/b&gt;&lt;br&gt;&lt;i style=&quot;font-size: 14px;&quot;&gt;Large Language Model (LLM)&lt;/i&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontColor=#1e40af;" parent="1" vertex="1">
                    <mxGeometry x="120" y="150" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="llm-1" value="&lt;b&gt;Context Analysis&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3b82f6;strokeColor=#1e40af;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="220" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="llm-2" value="&lt;b&gt;Reasoning Engine&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3b82f6;strokeColor=#1e40af;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="280" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="llm-3" value="&lt;b&gt;Decision Making&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3b82f6;strokeColor=#1e40af;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="340" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="llm-4" value="&lt;b&gt;Response Generation&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3b82f6;strokeColor=#1e40af;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="400" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="llm-edge-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1e40af;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="llm-1" target="llm-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="llm-edge-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1e40af;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="llm-2" target="llm-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="llm-edge-3" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1e40af;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="llm-3" target="llm-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="snn-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1fae5;strokeColor=#065f46;strokeWidth=2;shadow=1;arcSize=10;" parent="1" vertex="1">
                    <mxGeometry x="460" y="140" width="280" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="snn-label" value="&lt;b style=&quot;font-size: 18px;&quot;&gt;Reflex Loop&lt;/b&gt;&lt;br&gt;&lt;i style=&quot;font-size: 14px;&quot;&gt;Spiking Neural Network (SNN)&lt;/i&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontColor=#065f46;" parent="1" vertex="1">
                    <mxGeometry x="500" y="150" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="snn-1" value="&lt;b&gt;Spike Detection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10b981;strokeColor=#065f46;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="220" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="snn-2" value="&lt;b&gt;Pattern Recognition&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10b981;strokeColor=#065f46;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="280" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="snn-3" value="&lt;b&gt;Rapid Response&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10b981;strokeColor=#065f46;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="340" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="snn-4" value="&lt;b&gt;Motor Control&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#10b981;strokeColor=#065f46;fontColor=#ffffff;fontFamily=Segoe UI;fontSize=14;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="400" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="snn-edge-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#065f46;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="snn-1" target="snn-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="snn-edge-2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#065f46;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="snn-2" target="snn-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="snn-edge-3" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#065f46;strokeWidth=2;endArrow=classic;endFill=1;" parent="1" source="snn-3" target="snn-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="arbitration" value="&lt;b style=&quot;font-size: 16px;&quot;&gt;Arbitration&lt;/b&gt;&lt;br&gt;&lt;i&gt;Central Control&lt;/i&gt;" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fbbf24;strokeColor=#d97706;strokeWidth=3;fontFamily=Segoe UI;fontSize=14;shadow=1;fontColor=#451a03;" parent="1" vertex="1">
                    <mxGeometry x="350" y="280" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="event-bus" value="&lt;b style=&quot;font-size: 16px;&quot;&gt;Shared Event Bus&lt;/b&gt;&lt;br&gt;&lt;i&gt;Message Queue&lt;/i&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a78bfa;strokeColor=#6d28d9;strokeWidth=2;fontFamily=Segoe UI;fontSize=14;shadow=1;fontColor=#ffffff;arcSize=5;" parent="1" vertex="1">
                    <mxGeometry x="300" y="480" width="220" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="arb-llm" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#d97706;strokeWidth=3;endArrow=classic;endFill=1;startArrow=classic;startFill=1;dashed=1;" parent="1" source="arbitration" target="llm-container" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="220" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="arb-snn" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#d97706;strokeWidth=3;endArrow=classic;endFill=1;startArrow=classic;startFill=1;dashed=1;" parent="1" source="arbitration" target="snn-container" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="600" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bus-llm" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#6d28d9;strokeWidth=2;endArrow=classic;endFill=1;startArrow=classic;startFill=1;" parent="1" source="event-bus" target="llm-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="bus-snn" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#6d28d9;strokeWidth=2;endArrow=classic;endFill=1;startArrow=classic;startFill=1;" parent="1" source="event-bus" target="snn-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="bus-arb" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#6d28d9;strokeWidth=2;endArrow=classic;endFill=1;startArrow=classic;startFill=1;" parent="1" source="event-bus" target="arbitration" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="priority-annotation" value="&lt;div style=&quot;background-color: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 10px;&quot;&gt;&lt;b&gt;Priority Arbitration&lt;/b&gt;&lt;br&gt;Manages conflict resolution between cognitive and reflex responses&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontSize=12;fontColor=#78350f;" parent="1" vertex="1">
                    <mxGeometry x="80" y="80" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="coupling-annotation" value="&lt;div style=&quot;background-color: #dcfce7; border: 2px solid #16a34a; border-radius: 8px; padding: 10px;&quot;&gt;&lt;b&gt;Sub-Millisecond Coupling&lt;/b&gt;&lt;br&gt;Ultra-fast communication between SNN components&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontSize=12;fontColor=#14532d;" parent="1" vertex="1">
                    <mxGeometry x="540" y="80" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="llm-perf" value="⏱ ~100-500ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#60a5fa;strokeColor=none;fontFamily=Segoe UI;fontSize=12;fontColor=#ffffff;arcSize=50;" parent="1" vertex="1">
                    <mxGeometry x="100" y="420" width="100" height="25" as="geometry"/>
                </mxCell>
                <mxCell id="snn-perf" value="⚡ &lt;1ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34d399;strokeColor=none;fontFamily=Segoe UI;fontSize=12;fontColor=#ffffff;arcSize=50;" parent="1" vertex="1">
                    <mxGeometry x="620" y="425" width="80" height="25" as="geometry"/>
                </mxCell>
                <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f9fafb;strokeColor=#d1d5db;strokeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="490" width="170" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="legend-title" value="&lt;b&gt;Processing Types:&lt;/b&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontSize=11;fontColor=#374151;" parent="1" vertex="1">
                    <mxGeometry x="610" y="495" width="100" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="legend-llm" value="■ Cognitive" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontSize=10;fontColor=#1e40af;" parent="1" vertex="1">
                    <mxGeometry x="610" y="510" width="60" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="legend-snn" value="■ Reflex" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Segoe UI;fontSize=10;fontColor=#065f46;" parent="1" vertex="1">
                    <mxGeometry x="680" y="510" width="60" height="15" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>