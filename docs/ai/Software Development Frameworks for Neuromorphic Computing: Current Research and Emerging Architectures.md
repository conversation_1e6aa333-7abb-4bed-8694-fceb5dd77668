# Software Development Frameworks for Neuromorphic Computing: Current Research and Emerging Architectures

The field of neuromorphic computing has witnessed significant advancement in software development frameworks for Spiking Neural Networks (SNNs), with multiple research initiatives addressing the unique challenges of programming brain-inspired hardware architectures. Current research spans platform-agnostic frameworks, hardware-specific toolchains, and cross-platform compatibility solutions that aim to bridge the gap between conventional computing paradigms and neuromorphic systems.

## Major Research Frameworks and Initiatives

### Intel Lava Framework: Open-Source Neuromorphic Computing Platform

Intel's Lava framework represents one of the most comprehensive open-source initiatives in neuromorphic software development. Lava is designed as a modular, composable, and extensible framework that enables developers to create applications for neuromorphic hardware architectures while maintaining compatibility with conventional processors. The framework provides abstractions and tools for developing distributed and massively parallel applications that can be deployed across heterogeneous system architectures.[1][2][3]

The Lava architecture supports flexible cross-platform execution of computational processes on novel neuromorphic architectures such as Intel Loihi as well as conventional CPU/GPU architectures. This dual compatibility addresses a critical requirement in the field: the ability to prototype and test neuromorphic algorithms without requiring access to specialized hardware. The framework employs event-based message passing for communication between processes, where messages can range from binary spikes to kilobyte-sized packets.[3][4][5]

### Intel Loihi Hardware-Software Ecosystem

Intel's second-generation Loihi 2 neuromorphic processor, introduced in 2021, incorporates learnings from three years of first-generation research and provides up to 10 times faster processing, up to 15 times greater resource density with up to one million neurons per chip, and improved energy efficiency. The accompanying software ecosystem includes NxSDK, a comprehensive software development kit that provides APIs, compilers, and debugging tools for programming Loihi systems.[5][6][7][8][9]

NxSDK allows users to specify networks at various levels of abstraction, from low-level individual neuron parameter control to high-level prepackaged parameterized modules designed for specific tasks. The framework supports both declarative programming for network structure specification and imperative programming for conventional code execution on embedded x86 processors. A specialized component called "snips" (sequential neural interfacing processes) handles real-time data format conversion and neuron interaction.[8]

### Cross-Platform Integration and Compatibility Solutions

#### Brian2Lava Interface
The Brian2Lava project represents a significant advancement in cross-platform neuromorphic computing, providing an open-source interface between the widely-used Brian simulator and Intel's Lava framework. This integration enables researchers familiar with Brian's computational neuroscience tools to deploy brain-inspired algorithms on Lava-supported hardware and emulator backends. The project addresses legal restrictions by providing CPU backend models publicly while restricting Loihi2 backend models to members of the Intel Neuromorphic Research Community.[10]

#### Nengo Framework Integration
The Neural Engineering Framework (NEF) through Nengo provides another pathway for neuromorphic application development. Nengo serves as a bridge between high-level computational goals and low-level neural dynamics, allowing users to define desired functions and optimize synaptic weights for populations of spiking neurons. The framework supports multiple backends, enabling deployment on standard CPUs/GPUs or direct deployment to neuromorphic chips.[11][12][13]

### PyTorch-Based Spiking Neural Network Frameworks

#### snnTorch Development
snnTorch represents a significant advancement in making spiking neural networks accessible to the broader deep learning community. Built as a Python package that extends PyTorch capabilities, snnTorch integrates spiking neurons into PyTorch's computational graph while treating them as recurrent activation units. The framework supports GPU acceleration and maintains compatibility with conventional deep learning workflows.[14][15][16]

The snnTorch architecture includes comprehensive components for spike generation, data conversion, visualization tools, surrogate gradient functions, and dataset utilities. The framework's lean requirements enable training of both small and large networks on CPU while leveraging GPU acceleration when models and tensors are loaded onto CUDA.[15][16]

## Current Development Maturity and Research Stage

### Production-Ready Frameworks
Several frameworks have reached significant maturity levels suitable for research and early commercial applications. Intel's Lava framework, released as open-source software, provides a stable platform for neuromorphic application development with extensive documentation and community support. The framework's compatibility with both neuromorphic hardware and conventional processors makes it suitable for practical deployment scenarios.[1][2][5]

The Loihi ecosystem has demonstrated significant progress, with applications spanning robotic arms, neuromorphic skins, and olfactory sensing. Los Alamos National Laboratory has successfully used the Loihi platform to investigate trade-offs between quantum and neuromorphic computing, demonstrating practical research applications.[6][7]

### Emerging Research Directions
Current research focuses on several key areas including asynchronous optimization frameworks compatible with neuromorphic hardware, deep learning tools for event-based networks, and improved ANN-to-SNN conversion techniques. The Lava-DL library provides specialized tools for training Deep Event-Based Networks through both direct training and ANN-to-SNN conversion approaches.[17][18][19]

### Cross-Platform Compilation and Optimization
Advanced compiler research focuses on optimizing deep convolutional SNNs for multi-core architectures. Intel's NxTF framework demonstrates near-optimal resource utilization of 80% across 16 Loihi chips for large-scale models, achieving the lowest reported error rate of 8.52% for CIFAR-10 dataset on neuromorphic hardware.[9][20]

## Architecture Requirements and Design Principles

### Platform Agnosticism and Modularity
Research consistently emphasizes the need for platform-agnostic frameworks that can operate across diverse hardware configurations. Lava's design principles include modularity through consistent computational process architecture, extensibility for supporting increasing use case breadth, and trainability for various learning paradigms.[2][4]

### Event-Based Communication Paradigms
Modern neuromorphic frameworks universally adopt event-based asynchronous communication as a unifying principle. This approach spans from nanosecond-timescale single-bit spike events between neurons to higher-level message passing over statically allocated channels using standard interfaces.[6][8]

### Hardware Abstraction and Resource Management
Effective neuromorphic software frameworks require sophisticated hardware abstraction layers that can detect and utilize available resources optimally. This includes dynamic backend selection between CPU, GPU, and specialized neuromorphic processors based on runtime conditions and performance requirements.[8]

## Future Research Directions and Challenges

Current research identifies several critical challenges requiring continued investigation: developing standardized benchmarking frameworks for cross-platform performance comparison, creating transparent hardware backend selection mechanisms, and establishing common software frameworks for neuromorphic community convergence. The field continues advancing toward commercial viability through iterative hardware improvements, cross-platform software framework development, and expanding collaborative ecosystems across industry, academia, and government organizations.[5][6][21]

The convergence of multiple research initiatives toward common software frameworks suggests the field is approaching a maturation phase where standardized development practices and interoperable tools will enable broader adoption of neuromorphic computing paradigms across diverse application domains.

[1] https://github.com/lava-nc/lava
[2] https://open-neuromorphic.org/neuromorphic-computing/software/snn-frameworks/lava/
[3] https://lava-nc.org
[4] https://lava-nc.org/lava_architecture_overview.html
[5] https://itwire.com/business-it-sp-511/business-it/intel-unveils-loihi-2-and-lava-to-further-neuromorphic-research.html
[6] https://download.intel.com/newsroom/archive/2025/en-us-2021-09-30-intel-advances-neuromorphic-with-loihi-2-new-lava-software-framework-and-new-partners.pdf
[7] https://insidehpc.com/2021/09/intel-launches-2nd-loihi-neuromorphic-chip-lanl-investigating-trade-offs-between-quantum-and-neuromorphic-computing/
[8] https://dynamicfieldtheory.org/upload/file/1631291311_c647b66b9e48f0a9baff/DavisEtAl2021.pdf
[9] https://arxiv.org/pdf/2101.04261.pdf
[10] https://pypi.org/project/brian2lava/
[11] https://blogs.ucl.ac.uk/faicdt/2025/07/18/nengo-neromorphic-ai-summer-school-2025-jianwei-liu/
[12] https://forum.nengo.ai/t/how-can-i-use-nxsdk/1715
[13] https://arxiv.org/pdf/2007.10227.pdf
[14] https://open-neuromorphic.org/neuromorphic-computing/software/snn-frameworks/snntorch/
[15] https://snntorch.readthedocs.io/en/latest/readme.html
[16] https://github.com/jeshraghian/snntorch
[17] https://arxiv.org/html/2404.17052v1
[18] https://lava-nc.org/dl.html
[19] https://github.com/lava-nc/lava-dl
[20] https://arxiv.org/abs/2101.04261
[21] https://www.youtube.com/watch?v=vXZukQ6A79k
[22] https://www.solutions-numeriques.com/intel-devoile-loihi-2-et-lava-sa-puce-neuromorphique-et-son-environnement-de-developpement-open-source/
[23] https://community.rockrms.com/lava/lava-api
[24] https://docs.lavasoftware.org/lava/data-export.html
[25] https://kandi.openweaver.com/jupyter%20notebook/lava-nc/lava-dl
[26] https://www.intel.com/content/www/us/en/research/neuromorphic-computing.html
[27] https://docs.lavanet.xyz/about/
[28] https://www.techpowerup.com/287341/intel-advances-neuromorphic-with-loihi-2-new-lava-software-framework-and-new-partners
[29] https://www.npmjs.com/package/@lavanet/lava-sdk?activeTab=dependencies
[30] https://spinnaker.io/docs/concepts/
[31] https://pypi.org/project/sPyNNaker/2015.004/
[32] https://dl.acm.org/doi/10.5555/2388996.2389070
[33] https://github.com/spinnaker/spinnaker
[34] https://pmc.ncbi.nlm.nih.gov/articles/PMC6257411/
[35] https://open-neuromorphic.org/neuromorphic-computing/hardware/truenorth-ibm/
[36] https://spinnaker.io
[37] https://softwareservices.flir.com/spinnaker/latest/_programmer_guide.html
[38] https://en.wikipedia.org/wiki/Neural_Engineering_Object
[39] https://citeseerx.ist.psu.edu/document?repid=rep1&type=pdf&doi=487687f4ccac30f4c26a788441e5c9277e73491e
[40] https://www.teledynevisionsolutions.com/en-gb/products/spinnaker-sdk?vertical=machine+vision&segment=iis
[41] https://developer.harness.io/kb/armory/general/accessing-the-spinnaker-rest-api-using-httpie-and-jq/
[42] https://pmc.ncbi.nlm.nih.gov/articles/PMC9682266/
[43] https://www.biorxiv.org/content/10.1101/2023.09.12.557460v1
[44] https://pmc.ncbi.nlm.nih.gov/articles/PMC6444189/
[45] https://open-neuromorphic.org/neuromorphic-computing/hardware/brainscales-2-universitat-heidelberg/
[46] https://github.com/NeuralEnsemble/PyNN
[47] https://www.zora.uzh.ch/id/eprint/217781/1/Temporal_Pattern_Code.pdf
[48] https://www.nature.com/articles/s41467-024-53827-9
[49] https://arxiv.org/abs/2009.04765
[50] https://spinnakermanchester.github.io/spinn_tools/3.4.1/index.html
[51] https://www.humanbrainproject.eu/silicon-brains/how-we-work/hardware/
[52] https://www.nitrc.org/projects/pynn/
[53] https://redwood.berkeley.edu/wp-content/uploads/2021/08/Davies2018.pdf
[54] https://arxiv.org/pdf/2210.13107.pdf
[55] https://dl.acm.org/doi/10.1145/3501770
[56] https://arxiv.org/abs/2201.11063
[57] https://arxiv.org/abs/2003.13750
[58] https://open-neuromorphic.org/neuromorphic-computing/hardware/tianjic-tsinghua-university/
[59] https://www.tsinghua.edu.cn/en/info/1244/3005.htm
[60] https://www.westernsydney.edu.au/icns/research_projects/open_phd_projects/a_neuromorphic_framework_for_event-based_dnns_using_minifloats
[61] https://viso.ai/deep-learning/mindspore-ai/
[62] https://github.com/thesephist/synaptic
[63] https://pmc.ncbi.nlm.nih.gov/articles/PMC7884322/
[64] https://www.scmp.com/tech/big-tech/article/3021038/chinese-researchers-develop-hybrid-chip-design-holds-promise-thinking
[65] https://pmc.ncbi.nlm.nih.gov/articles/PMC11007209/
[66] https://www.mindspore.cn/tutorials/en/r1.5/model.html
[67] https://github.com/micllynn/synappy
[68] https://www.nature.com/articles/s41586-019-1424-8
[69] https://www.nsfc.gov.cn/csc/20345/20350/articlelist/2019/PDF/1903ZW%2027.pdf
[70] https://www.prophesee.ai/2024/07/10/event-based-neural-networks-digital-neuromorphic-architecture/
[71] https://mindspore-website.obs.cn-north-4.myhuaweicloud.com/white_paper/MindSpore_white_paper_enV1.1.pdf
[72] https://pubmed.ncbi.nlm.nih.gov/39345285/
[73] https://www.science.org/doi/10.1126/scirobotics.abk2948
[74] http://scis.scichina.com/en/2023/142403.pdf
[75] https://semiengineering.com/optimizing-event-based-neural-network-processing-for-a-neuromorphic-architecture/
[76] https://www.mindspore.cn/api/en/r0.5/api/python/mindspore/mindspore.nn.html
[77] https://www.eneuro.org/content/eneuro/11/1/ENEURO.0326-23.2023.full.pdf