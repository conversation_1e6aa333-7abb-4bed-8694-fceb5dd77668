# Quick configuration for testing fast job execution
pipeline:
  batch_size: 1000
  max_parallel_batches: 2
  error_threshold: 0.01
  stop_on_error: false
  enable_checkpointing: false
  checkpoint_dir: "/tmp/omop-etl/checkpoints"
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "csv"
        file_path: "test_data/csv/patients.csv"
        delimiter: ","
        has_header: true
        processing_delay_ms: 1000
        per_record_delay_ms: 10
    - name: "transform"
      type: "transformer"
      config:
        type: "identity"
        validate_records: false
    - name: "load"
      type: "loader"
      config:
        type: "database"
        connection_string: "postgresql://test_user:test_pass@localhost:5432/test_db"
        batch_size: 1000 