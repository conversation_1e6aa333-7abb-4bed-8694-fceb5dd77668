# Test configuration for integration tests

# Pipeline configuration
pipeline:
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "csv"
        file_path: "tests/integration/test_data/csv/patients.csv"
        encoding: "utf-8"
        delimiter: ","
        has_header: true
        batch_size: 10
        processing_delay_ms: 1000
    
    - name: "transform"
      type: "transformer"
      config:
        type: "identity"
        validation_rules:
          - field: "patient_id"
            required: true
          - field: "age"
            type: "integer"
            range: [0, 150]
        cleaning_rules:
          - field: "name"
            trim: true
            lowercase: false
    
    - name: "load"
      type: "loader"
      config:
        type: "database"
        target_type: "postgresql"
        host: "localhost"
        port: 5432
        database: "test_db"
        schema: "public"
        table: "test_patients"
        batch_size: 10
        retry_attempts: 3
        retry_delay_ms: 1000

monitoring:
  metrics:
    enabled: true
    collection_interval_ms: 1000
  logging:
    level: "info"
    format: "json"

# Source database configuration
source:
  type: postgresql
  host: localhost
  port: 5432
  database: test_source
  username: postgres
  password: postgres
  schema: public

# Target database configuration
target:
  type: postgresql
  host: localhost
  port: 5432
  database: test_target
  username: postgres
  password: postgres
  schema: cdm

# ETL settings
etl:
  batch_size: 1000
  commit_interval: 5000
  error_threshold: 0.01
  validate_foreign_keys: true
  create_missing_tables: true
  log_level: debug

# Vocabulary mappings
vocabulary:
  cache_size: 10000
  case_sensitive: false
  mappings:
    gender:
      M: 8507  # Male
      F: 8532  # Female
      U: 0     # Unknown

    race:
      White: 8527
      Black: 8516
      Asian: 8515
      Other: 0

    ethnicity:
      Hispanic: 38003563
      Non-Hispanic: 38003564
      Unknown: 0

# Table mappings
tables:
  - source_table: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: gender
          default: 0

      - source_column: birth_date
        target_column: year_of_birth
        type: date_transform
        parameters:
          extract: year

      - source_column: birth_date
        target_column: month_of_birth
        type: date_transform
        parameters:
          extract: month

      - source_column: birth_date
        target_column: day_of_birth
        type: date_transform
        parameters:
          extract: day

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: race
          default: 0

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: ethnicity
          default: 0

    filters:
      - field: patient_id
        operator: is_not_null

      - field: birth_date
        operator: is_not_null

    validations:
      - field: person_id
        rule: required

      - field: year_of_birth
        rule: range
        min: 1900
        max: 2024

      - field: gender_concept_id
        rule: in_list
        values: [8507, 8532, 0]